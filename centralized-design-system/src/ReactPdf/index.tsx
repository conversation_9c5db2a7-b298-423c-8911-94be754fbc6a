import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import screenfull from 'screenfull';
import type { PDFDocumentProxy } from 'pdfjs-dist';

import Spinner from '../MUIComponents/common/Spinner';
import { INTERSECTION_THRESHOLDS, PasswordResponses, PDF_CONSTANTS, PDF_RENDER_OPTIONS } from './constants';
import { translatr } from '../Translatr';
import Tooltip from '../Tooltip';
import PasswordModal from './PasswordModal';

import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import './styles.scss';

// Configure PDF.js worker source
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

const ReactPdf = ({ fileUrl }) => {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(PDF_CONSTANTS.INITIAL_PAGE);
  const [pageInput, setPageInput] = useState(PDF_CONSTANTS.INITIAL_PAGE_INPUT);
  const [scale, setScale] = useState(PDF_CONSTANTS.INITIAL_SCALE);
  const [visiblePageRange, setVisiblePageRange] = useState(PDF_CONSTANTS.INITIAL_VISIBLE_RANGE);
  const [estimatedPageHeight, setEstimatedPageHeight] = useState(PDF_CONSTANTS.INITIAL_PAGE_HEIGHT);
  const [cancelled, setCancelled] = useState(false);
  const [passwordModalOpen, setPasswordModalOpen] = useState(false);
  const [passwordRetry, setPasswordRetry] = useState(false);
  const passwordCallbackRef = useRef(null);

  const viewerRef = useRef<HTMLDivElement>(null);
  const pageRefs = useRef<React.RefObject<HTMLDivElement>[]>([]);
  const timeoutRef = useRef(null);
  const pdfDocumentRef = useRef(null);

  // This function directly handles the password prompt using browser's native prompt
  const onPassword = (callback, reason) => {
    switch (reason) {
      case PasswordResponses.NEED_PASSWORD: {
        // First attempt
        setPasswordModalOpen(true);
        passwordCallbackRef.current = callback;
        break;
      }
      case PasswordResponses.INCORRECT_PASSWORD: {
        // Incorrect password
        setPasswordRetry(true);
        setPasswordModalOpen(true);
        passwordCallbackRef.current = callback;
        break;
      }
      default:
    }
  }

  const handlePasswordSubmit = useCallback((password: string) => {
    if (passwordCallbackRef.current) {
      passwordCallbackRef.current(password);
    }
  }, []);

  const handlePasswordCancel = useCallback(() => {
    if (passwordCallbackRef.current) {
      passwordCallbackRef.current(null);
    }
    setPasswordModalOpen(false);
    setCancelled(true);
  }, []);

  // Handle PDF document load
  const onDocumentLoadSuccess = ({ numPages, ...pdfDocument }: PDFDocumentProxy): void => {
    setPasswordModalOpen(false);

    // Store the PDF document instance for cleanup
    pdfDocumentRef.current = pdfDocument;
    
    setCancelled(false);
    
    setNumPages(numPages);
    setPageNumber(PDF_CONSTANTS.INITIAL_PAGE);
    setPageInput(PDF_CONSTANTS.INITIAL_PAGE_INPUT);
    pageRefs.current = Array(numPages)
      .fill(null)
      .map((_, i) => pageRefs.current[i] || React.createRef());

    setVisiblePageRange({ 
      start: PDF_CONSTANTS.INITIAL_VISIBLE_RANGE.start, 
      end: Math.min(PDF_CONSTANTS.INITIAL_VISIBLE_RANGE.end, numPages) 
    });
    
    setTimeout(calculateVisiblePages, PDF_CONSTANTS.RENDER_SUCCESS_TIMEOUT);
  };

  // Update estimated page height after first page renders
  const onPageRenderSuccess = (pageNum) => {
    if (pageNum === PDF_CONSTANTS.INITIAL_PAGE && pageRefs.current[0]?.current) {
      const height = pageRefs.current[0].current.getBoundingClientRect().height;
      if (height > 0) {
        setEstimatedPageHeight(height / scale);
      }
    }
  };

    // Determine which pages should be rendered based on scroll position
  const calculateVisiblePages = useCallback(() => {
    if (!viewerRef.current || !numPages) return;

    const container = viewerRef.current;
    const scrollTop = container.scrollTop;
    const viewportHeight = container.clientHeight;
    const pageHeight = estimatedPageHeight * scale;
    
    // Add more buffer pages at higher zoom levels
    const bufferPages = scale > PDF_CONSTANTS.HIGH_ZOOM_THRESHOLD 
      ? PDF_CONSTANTS.HIGH_ZOOM_BUFFER_PAGES 
      : PDF_CONSTANTS.STANDARD_BUFFER_PAGES;
      
    const startPage = Math.max(1, Math.floor(scrollTop / pageHeight) - bufferPages);
    const endPage = Math.min(numPages, Math.ceil((scrollTop + viewportHeight) / pageHeight) + bufferPages);
  
    setVisiblePageRange({ start: startPage, end: endPage });
  }, [numPages, scale, estimatedPageHeight]);  

  // Debounce scroll events for better performance
  const debouncedCalculateVisiblePages = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      calculateVisiblePages();
    }, PDF_CONSTANTS.DEBOUNCE_TIMEOUT);
  }, [calculateVisiblePages]);

  // Scroll to specific page
  const scrollToPage = (pageNum: number) => {
    const pageElement = pageRefs.current[pageNum - 1];
    const container = viewerRef.current;

    if (container) {
      if (pageElement?.current) {
        // Scroll to the page if it's already rendered
        const containerRect = container.getBoundingClientRect();
        const pageRect = pageElement.current.getBoundingClientRect();
        container.scrollTop = container.scrollTop + (pageRect.top - containerRect.top);
      } else {
        // Estimate position if page isn't rendered yet
        container.scrollTop = (pageNum - 1) * estimatedPageHeight * scale;
        setVisiblePageRange(prev => ({
          start: Math.min(prev.start, pageNum - PDF_CONSTANTS.STANDARD_BUFFER_PAGES),
          end: Math.max(prev.end, pageNum + PDF_CONSTANTS.STANDARD_BUFFER_PAGES)
        }));
      }
    }
  };

  // Navigation functions
  const goToPrevPage = () => {
    const newPage = Math.max(pageNumber - 1, PDF_CONSTANTS.INITIAL_PAGE);
    scrollToPage(newPage);
    setPageNumber(newPage);
  };

  const goToNextPage = () => {
    const newPage = Math.min(pageNumber + 1, numPages);
    scrollToPage(newPage);
    setPageNumber(newPage);
  };

  // Zoom functions that maintain center point
  const zoomIn = () => {
    if (!viewerRef.current) return;
    
    const viewer = viewerRef.current;
    const viewerRect = viewer.getBoundingClientRect();
    const viewerCenterX = viewer.scrollLeft + viewerRect.width / 2;
    const viewerCenterY = viewer.scrollTop + viewerRect.height / 2;
    
    setScale((prev) => {
      const newScale = Math.min(prev + PDF_CONSTANTS.STANDARD_ZOOM_INCREMENT, PDF_CONSTANTS.MAX_SCALE);
      
      setTimeout(() => {
        if (viewer) {
          // Maintain center point after zoom
          const scaleFactor = newScale / prev;
          const newScrollLeft = viewerCenterX * scaleFactor - viewerRect.width / 2;
          const newScrollTop = viewerCenterY * scaleFactor - viewerRect.height / 2;
          
          viewer.scrollLeft = newScrollLeft;
          viewer.scrollTop = newScrollTop;
          calculateVisiblePages();
        }
      }, PDF_CONSTANTS.ZOOM_ADJUSTMENT_TIMEOUT);
      
      return newScale;
    });
  };
  
  const zoomOut = () => {
    if (!viewerRef.current) return;
    
    const viewer = viewerRef.current;
    const viewerRect = viewer.getBoundingClientRect();
    const viewerCenterX = viewer.scrollLeft + viewerRect.width / 2;
    const viewerCenterY = viewer.scrollTop + viewerRect.height / 2;
    
    setScale((prev) => {
      const newScale = Math.max(prev - PDF_CONSTANTS.STANDARD_ZOOM_INCREMENT, PDF_CONSTANTS.MIN_SCALE);
      
      setTimeout(() => {
        if (viewer) {
          // Maintain center point after zoom
          const scaleFactor = newScale / prev;
          const newScrollLeft = viewerCenterX * scaleFactor - viewerRect.width / 2;
          const newScrollTop = viewerCenterY * scaleFactor - viewerRect.height / 2;
          
          viewer.scrollLeft = newScrollLeft;
          viewer.scrollTop = newScrollTop;
          calculateVisiblePages();
        }
      }, PDF_CONSTANTS.ZOOM_ADJUSTMENT_TIMEOUT);
      
      return newScale;
    });
  };

  const handleFullscreenChange = () => {
    // Store scale value before fullscreen
    const previousScale = scale;
    if (screenfull.isFullscreen) {
      setScale(PDF_CONSTANTS.INITIAL_SCALE); // Reset to 100% zoom in fullscreen
    } else {
      setScale(previousScale); // Restore previous zoom on exit
      setTimeout(calculateVisiblePages, PDF_CONSTANTS.RENDER_SUCCESS_TIMEOUT);
    }
  };

  // Toggle fullscreen mode
  const toggleFullScreen = () => {
    if (screenfull.isEnabled && viewerRef.current) {
      screenfull.on('change', handleFullscreenChange);
      
      screenfull.toggle(viewerRef.current).catch(err => {
        console.error('Error toggling fullscreen:', err);
        screenfull.off('change', handleFullscreenChange);
      });
    }
  };  

  // Handle page input changes
  const handleInputChange = (e) => {
    setPageInput(e.target.value);
  };

  const handlePageKeyDown = (e) => {
    if (e.key === 'Enter') {
      const parsed = parseInt(pageInput);
      if (!isNaN(parsed) && parsed >= PDF_CONSTANTS.INITIAL_PAGE && parsed <= numPages) {
        scrollToPage(parsed);
        setPageNumber(parsed);
      } else {
        scrollToPage(PDF_CONSTANTS.INITIAL_PAGE);
        setPageNumber(PDF_CONSTANTS.INITIAL_PAGE);
        setPageInput(PDF_CONSTANTS.INITIAL_PAGE_INPUT);
      }
    }
  };

  // Keep input in sync with current page
  useEffect(() => {
    setPageInput(String(pageNumber));
  }, [pageNumber]);

  // Set up scroll event listener
  useEffect(() => {
    const container = viewerRef.current;
    if (!container) return;

    container.addEventListener('scroll', debouncedCalculateVisiblePages);
    calculateVisiblePages();
    
    return () => {
      container.removeEventListener('scroll', debouncedCalculateVisiblePages);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [debouncedCalculateVisiblePages, calculateVisiblePages]);

  // Track visible pages to update current page number
  useEffect(() => {
    const container = viewerRef.current;
    if (!container || !numPages) return;

    // Create observer to detect which page is most visible
    const observer = new IntersectionObserver(
      (entries) => {
        const visibleEntries = entries.filter(entry => entry.isIntersecting);
        if (visibleEntries.length > 0) {
          let maxVisibility = 0;
          let mostVisiblePageIndex = -1;
          
          visibleEntries.forEach(entry => {
            const index = pageRefs.current.findIndex((ref) => ref.current === entry.target);
            if (index !== -1 && entry.intersectionRatio > maxVisibility) {
              maxVisibility = entry.intersectionRatio;
              mostVisiblePageIndex = index;
            }
          });
          
          if (mostVisiblePageIndex !== -1) {
            setPageNumber(mostVisiblePageIndex + 1);
          }
        }
      },
      {
        root: container,
        threshold: INTERSECTION_THRESHOLDS
      }
    );

    // Observe all page refs
    pageRefs.current.forEach((ref) => {
      if (ref && ref.current) observer.observe(ref.current);
    });

    // Re-observe when DOM changes
    const mutationObserver = new MutationObserver(() => {
      pageRefs.current.forEach((ref) => {
        if (ref && ref.current) observer.observe(ref.current);
      });
    });

    mutationObserver.observe(container, { childList: true, subtree: true });

    return () => {
      observer.disconnect();
      mutationObserver.disconnect();
    };
  }, [numPages, scale]);

  // Clean up resources
  useEffect(() => {
    return () => {
      if (screenfull.isEnabled) screenfull.off('change', handleFullscreenChange);
      
      // Clean up PDF document when component unmounts
      if (pdfDocumentRef.current) {
        try {
          pdfDocumentRef.current.destroy();
        } catch (e) {
          console.error('Error destroying PDF document:', e);
        }
        pdfDocumentRef.current = null;
      }
      
      setNumPages(null);
      pageRefs.current = [];
    };
  }, []);

  // Show blank state if cancelled
  if (cancelled) {
    return (
      <div className="pdf-viewer-container">
        <div className="pdf-cancelled">
          <p>{translatr('cds.common.main', 'ViewingCancelled')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pdf-viewer-container">
      <PasswordModal
        isOpen={passwordModalOpen}
        onSubmit={handlePasswordSubmit}
        onCancel={handlePasswordCancel}
        isRetry={passwordRetry}
      />
      <div className="pdf-controls">
        <div className="nav-controls">
          <button
            onClick={goToPrevPage}
            disabled={pageNumber <= PDF_CONSTANTS.INITIAL_PAGE}
            aria-label={translatr('web.common.main', 'PreviousPage')}
          >
            {translatr('web.common.main', 'Prev')}
          </button>
          <input
            className="page-input"
            type="number"
            value={pageInput}
            onChange={handleInputChange}
            onKeyDown={handlePageKeyDown}
            min={PDF_CONSTANTS.INITIAL_PAGE}
            max={numPages || PDF_CONSTANTS.INITIAL_PAGE}
          />
          <span>
            {translatr('web.common.main', 'Of')} {numPages || '--'}
          </span>
          <button
            onClick={goToNextPage}
            disabled={pageNumber >= numPages}
            aria-label={translatr('web.common.main', 'NextPage')}
          >
            {translatr('web.common.main', 'Next2')}
          </button>
        </div>
        <div className="zoom-controls">
          <Tooltip message={translatr('web.common.main', 'ZoomOut')}>
            <button
              onClick={zoomOut}
              disabled={scale <= PDF_CONSTANTS.MIN_SCALE}
              aria-label={translatr('web.common.main', 'ZoomOut')}
            >
              -
            </button>
          </Tooltip>

          <span className="zoom-level">{Math.round(scale * 100)}%</span>
          
          <Tooltip message={translatr('web.common.main', 'ZoomIn')}>
            <button
              onClick={zoomIn}
              disabled={scale >= PDF_CONSTANTS.MAX_SCALE}
              aria-label={translatr('web.common.main', 'ZoomIn')}
            >
              +
            </button>
          </Tooltip>
          <button
            onClick={toggleFullScreen}
            aria-label={translatr('web.common.main', 'FullScreen')}
          >
            {translatr('web.common.main', 'FullScreen')}
          </button>
        </div>
      </div>

      <div ref={viewerRef} className="pdf-viewer">
        <Document
          file={fileUrl}
          options={PDF_RENDER_OPTIONS}
          onLoadSuccess={onDocumentLoadSuccess}
          onPassword={onPassword}
          loading={<div className="image-wrapper"><Spinner /></div>}
          error={<div>{translatr('web.common.main', 'FailedToLoadPleaseRefreshThePage')}</div>}
        >
          {numPages &&
            Array.from(new Array(numPages), (_, index) => {
              const pageNum = index + 1;
              // Only render pages in the visible range
              const shouldRender =
                pageNum >= visiblePageRange.start && pageNum <= visiblePageRange.end;

              return (
                <div
                  key={index}
                  ref={pageRefs.current[index]}
                  style={{ height: shouldRender ? 'auto' : `${estimatedPageHeight * scale}px` }}
                >
                  {shouldRender && (
                    <Page
                      pageNumber={pageNum}
                      scale={scale}
                      onRenderSuccess={() => onPageRenderSuccess(pageNum)}
                      loading={
                        <div style={{ height: `${estimatedPageHeight * scale}px` }}>
                          <Spinner />
                        </div>
                      }
                    />
                  )}
                </div>
              );
            })}
        </Document>
      </div>
    </div>
  );
};

export default ReactPdf;
