import React, { useState } from 'react';
import PropTypes from 'prop-types';
import set from 'lodash/set';
import isEmpty from 'lodash/isEmpty';

import { translatr } from '../../../Translatr';
import MultiLang from './multiLang';
import TextField from '../../../Inputs/TextField';
import FileUpload from '../../../FileUpload';
import { FILESTACK_MAX_FILE_SIZE } from '../../../FileUpload/utils';
import { getLangAttrIndex, setAltImageText, updateGenAiContextOnChange } from '../Shared/utils';
import { useAsyncReference } from '../../../Utils/hooks';
import LazyloadComponent from '../../../Utils/lazy';
import ScormDescDisclaimer from '../common/ScormDescDisclaimer';
import { ENTITY } from '../../../constants/genAiConstants';
import GenAiAssistant, {
  useGenAiAssistantContext,
  withGenAiAssistantContextProvider
} from '../../../GenAiAssistant';
import { useSmartCardContext, withSmartCardContextProvider } from '../Context/SmartCardConxtext';
import {
  useGenAiTranslationContext,
  withGenAiTranslationContextProvider
} from '../../../GenAiTranslation';
import { CARDS, MEDIA, THUMBNAILS } from '../../../Utils/Uploads/constants';
import { getLXMediaHubConfigValue } from '../../../Utils';
import { ENABLE_LX_MEDIA_HUB } from '../../../Utils/constants';
import { clearTextFieldError } from '../Shared/Functions/clearTextFieldError';

const Editor = LazyloadComponent(() => import('../../../Inputs/ckeditor'))();

const MultilangScormCard = ({
  setState,
  defaultOrgLanguage,
  languageDetails,
  validateRef,
  cardData = {},
  allowedMediaMimeTypes,
  descriptionRequired
}) => {
  /**
    DevNotes: While adding new context values below in `useSmartCardContext` make sure you add required
    defaut values in `MultilangScormCardForVersionModal - exported at the bottom`
    additional info - https://github.com/edcast/centralized-design-system/pull/3599
   */
  const { displayGenAiInlineAssistantForMultiLangCards } = useSmartCardContext();
  const { updateGenAiContext } = useGenAiAssistantContext();
  const { isUpdatedDescription } = useGenAiTranslationContext();

  const [localState, setLocalState] = useAsyncReference(null);
  const [descriptionProps] = useState(
    descriptionRequired ? { required: true } : { optional: true }
  );
  const LXMediaHubConfig = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);

  const cardType = cardData?.cardType;
  const isNewCard = !cardData ? true : false;
  const isNewCardVersion = cardData?.latestCardVersion === null;
  // In edit mode card doesn't sent cardType as it assumes it is SCORM
  const isScorm = isNewCard || !cardType || cardType === 'scorm';

  const handleChange = args => {
    const {
      value,
      key,
      languageAttr,
      multiLangState,
      updateMultilangState,
      isGenAiGenerated = false
    } = args;
    let { languages_attributes } = multiLangState;
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);
    const langAttr = {
      language: languageAttr,
      [key]: value
    };

    if (langIndex >= 0) {
      languages_attributes[langIndex][key] = value;
    } else {
      languages_attributes.push(langAttr);
    }

    if (displayGenAiInlineAssistantForMultiLangCards(languageAttr) && !isGenAiGenerated) {
      // we want to capture user entered values
      updateGenAiContextOnChange({
        key,
        value,
        langId: languageAttr,
        ckEditorInstanceName: `${languageAttr}_message`,
        updateFn: updateGenAiContext
      });
    }

    const state = {
      ...multiLangState,
      languages_attributes
    };
    updateMultilangState(state);
  };

  const handleFileUpload = args => {
    const { file, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes } = multiLangState;
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);

    if (LXMediaHubConfig) {
      if (isEmpty(file)) {
        if (langIndex >= 0) {
          languages_attributes[langIndex].media_file = null;
        }
      } else {
        const { signed_id } = file?.file;
        if (langIndex >= 0) {
          languages_attributes[langIndex].media_file = signed_id;
        } else {
          languages_attributes.push({ language: languageAttr, media_file: signed_id });
        }
      }
    } else {
      const filestack = file;
      if (isEmpty(filestack)) {
        if (langIndex >= 0) {
          languages_attributes[langIndex].media = [];
        }
      } else {
        filestack.file.scorm_course = true;
        if ('id' in languages_attributes[langIndex]) {
          filestack.file.reupload_scorm = true;
        }

        if (langIndex >= 0) {
          set(languages_attributes[langIndex], 'media[0]', { ...filestack.file });
        } else {
          languages_attributes.push({ language: languageAttr, media: [{ ...filestack.file }] });
        }
      }
    }

    const state = {
      ...multiLangState,
      languages_attributes
    };
    updateMultilangState(state);
  };

  const onSetThumbnailAlt = args => {
    const { altText, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes } = multiLangState;
    const state = {
      ...multiLangState,
      languages_attributes
    };

    if (LXMediaHubConfig) {
      const isValid = setAltImageText({
        languages_attributes,
        languageAttr,
        altText,
        mediaType: 'thumbnail',
        mediaTypeAlt: 'thumbnail_alt_text'
      });
      if (!isValid) return;
    } else {
      const imageObj = (languages_attributes || []).find(item => item.language === languageAttr)
        ?.media?.[1];
      if (!imageObj) {
        return;
      }
      imageObj['alt_text'] = altText;
    }
    updateMultilangState(state);
  };

  const handleThumbnailUpload = args => {
    const { file, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes, useSameImage } = multiLangState;

    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);

    if (LXMediaHubConfig) {
      if (isEmpty(file)) {
        languages_attributes[langIndex].thumbnail = null;
      } else {
        const updateThumbnail = (index, fileData) => {
          if (index >= 0) {
            languages_attributes[index].thumbnail = fileData;
          }
        };
        if (useSameImage) {
          languages_attributes.forEach((_, index) => updateThumbnail(index, file?.file));
        } else {
          updateThumbnail(langIndex, file?.file);
        }
      }
    } else {
      const filestack = file;
      if (isEmpty(filestack)) {
        set(languages_attributes[langIndex], 'media[1]', {});
      } else {
        if (langIndex >= 0) {
          if (useSameImage) {
            for (let i = 0; i < languages_attributes.length; i++) {
              set(languages_attributes[i], 'media[1]', { ...filestack.file });
            }
          } else {
            set(languages_attributes[langIndex], 'media[1]', { ...filestack.file });
          }
        }
      }
    }

    const state = {
      ...multiLangState,
      languages_attributes
    };
    updateMultilangState(state);
  };

  const accordionContent = ({
    item,
    languageAttr,
    cardErrorsMap,
    hasMedia,
    multiLangState,
    updateMultilangState,
    enableDelete,
    validateCardFields
  }) => {
    const handleFuncArgs = { languageAttr, multiLangState, updateMultilangState };
    try {
      // There is no state here, so we have to check if the items haven't been set, then update the state using
      // the handleChange function because it updates state across 3 components
      if (isNewCardVersion && !item.title && item.title !== '') {
        // Default title will be the languges options for scorm cards first, then the card title, and if nothing is there just use a blank space
        let defaultTitle =
          multiLangState.languages?.filter?.(l => l.language === languageAttr)?.[0]?.title ||
          multiLangState.cardTitle ||
          ' ';
        handleChange({ value: defaultTitle, key: 'title', ...handleFuncArgs });

        // Include the card message if it's available
        let defaultMsg =
          multiLangState.languages?.filter?.(l => l.language === languageAttr)?.[0]?.message ||
          multiLangState.cardMessage;
        if (defaultMsg) {
          handleChange({ value: defaultMsg, key: 'message', ...handleFuncArgs });
        }
      }
    } catch (e) {}
    const scrollTooltipRelativeElement = document.querySelector('.ed-dialog-modal');
    const uploadParams = {
      objectType: CARDS,
      isUGC: true
    };

    const getPersistedFile = type => {
      const showFile = {
        thumbnail: hasMedia && (LXMediaHubConfig ? item.thumbnail : item.media[1]),
        media:
          hasMedia &&
          item.state !== 'inactive' &&
          (LXMediaHubConfig ? item.media_file : item.media[0])
      };
      return showFile[type];
    };

    return (
      <>
        <TextField
          placeholder={translatr('cds.common.main', 'EnterSmartcardTitleHere2')}
          required
          shouldCheckForMaxChar={false}
          defaultValue={item?.title}
          value={item?.title}
          title={translatr('cds.common.main', 'Title')}
          description={translatr('cds.common.main', 'EnterTitle')}
          setValue={value => handleChange({ value, key: 'title', ...handleFuncArgs })}
          name={`${languageAttr}_title`}
          error={cardErrorsMap[`${languageAttr}_title`]}
          isTranslated
          onChangeCB={() => {
            clearTextFieldError({
              cardErrorsMap,
              fieldKey: `${languageAttr}_title`,
              validateCardFields
            });
          }}
          {...(displayGenAiInlineAssistantForMultiLangCards(languageAttr) && {
            renderGenAiAssistant: () => (
              <GenAiAssistant
                entitySetter={value =>
                  handleChange({
                    value,
                    key: 'title',
                    isGenAiGenerated: true,
                    ...handleFuncArgs,
                    multiLangState: localState.current
                  })
                }
                entity={ENTITY.TITLE}
                langId={languageAttr}
              />
            )
          })}
        />
        <Editor
          key={`${languageAttr}_editor${isUpdatedDescription(languageAttr) ? '_translated' : ''}`}
          placeholder={translatr('cds.common.main', 'EnterSmartcardDescriptionHere')}
          setValue={value =>
            handleChange({
              value,
              key: 'message',
              ...handleFuncArgs,
              multiLangState: localState.current // Added ref here because it gets stale multilang state
            })
          }
          title={translatr('cds.common.main', 'Description')}
          editorType="inline"
          {...descriptionProps}
          name={`${languageAttr}_message`}
          defaultValue={item?.message}
          error={cardErrorsMap[`${languageAttr}_message`]}
          description={translatr('cds.common.main', 'PleaseProvideAnyAdditionalDetails')}
          isTranslated
          genAiAssistantProps={{
            showGenAiAssistant: displayGenAiInlineAssistantForMultiLangCards(languageAttr),
            langId: languageAttr
          }}
        />
        {isScorm && <ScormDescDisclaimer />}
        {isScorm && (
          <div className="mt-16">
            <FileUpload
              required
              label={translatr('cds.common.main', 'UploadScormFile')}
              description=""
              name={`${languageAttr}_file`}
              error={cardErrorsMap[`${languageAttr}_file`]}
              persistedFile={getPersistedFile('media')}
              setValue={file => handleFileUpload({ file, ...handleFuncArgs })}
              allowedFileType={allowedMediaMimeTypes}
              showPreviewImage={false}
              disabled={
                hasMedia &&
                !isEmpty(item.media_file || item.media?.[0]) &&
                item.state !== 'inactive'
              }
              enableDelete={enableDelete}
              recommendedSize={FILESTACK_MAX_FILE_SIZE}
              scrollTooltipRelativeElement={scrollTooltipRelativeElement}
              isTranslated
              uploadParams={{ ...uploadParams, uploadType: MEDIA }}
            />
          </div>
        )}
        {isScorm && (
          <div className="mt-16">
            <FileUpload
              persistedFile={getPersistedFile('thumbnail')}
              label={translatr('cds.common.main', 'UploadThumbnail')}
              buttonLabel={translatr('cds.common.main', 'UploadThumbnail')}
              setValue={file => handleThumbnailUpload({ file, ...handleFuncArgs })}
              setAlt={val => onSetThumbnailAlt({ altText: val, ...handleFuncArgs })}
              scrollTooltipRelativeElement={scrollTooltipRelativeElement}
              isTranslated
              uploadParams={{ ...uploadParams, uploadType: THUMBNAILS }}
            />
          </div>
        )}
        {multiLangState.state !== 'error' && item.scormUploadStatus === 'error' && (
          <span className="error-text">
            {translatr('cds.common.main', 'ErrorInUploadingPleaseUploadScormFileAgain')}
          </span>
        )}
      </>
    );
  };

  return (
    <MultiLang
      useSameImage
      setLocalState={setLocalState}
      useSameImageIndex={1}
      setState={setState}
      defaultOrgLanguage={defaultOrgLanguage}
      orgLanguages={languageDetails}
      cardData={cardData}
      validateRef={validateRef}
      accordionContent={accordionContent}
      disabledLanguageSelectionAndDefaultDropdown={!isScorm}
    />
  );
};

MultilangScormCard.propTypes = {
  setState: PropTypes.func,
  defaultOrgLanguage: PropTypes.string,
  languageDetails: PropTypes.array,
  validateRef: PropTypes.shape({ current: PropTypes.any }).isRequired,
  cardData: PropTypes.object,
  allowedMediaMimeTypes: PropTypes.array,
  descriptionRequired: PropTypes.bool
};

export const MultilangScormCardForVersionModal = withSmartCardContextProvider(
  withGenAiAssistantContextProvider(withGenAiTranslationContextProvider(MultilangScormCard)),
  {
    options: {},
    displayGenAiInlineAssistantForMultiLangCards: () => false,
    displayGenAiInlineTranslationForMultiLangCards: () => false
  }
);

export default MultilangScormCard;
