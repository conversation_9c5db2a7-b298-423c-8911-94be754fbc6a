import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import set from 'lodash/set';

import { translatr } from '../../../Translatr';
import MultiLang from './multiLang';
import TextField from '../../../Inputs/TextField';
import FileUpload from '../../../FileUpload';
import { hasPermission } from '../../../Utils/OrgConfigs';
import LazyloadComponent from '../../../Utils/lazy';
import { useAsyncReference } from '../../../Utils/hooks';
import { getLangAttrIndex, setAltImageText, updateGenAiContextOnChange } from '../Shared/utils';
import { TEXT_CARD_TITLE_LIMIT } from '../Shared/const';
import { useSmartCardContext } from '../Context/SmartCardConxtext';
import UseOnlyThumbnailCheckbox from '../common/UseOnlyThumbnailCheckbox';
import { ENTITY } from '../../../constants/genAiConstants';
import GenAiAssistant, { useGenAiAssistantContext } from '../../../GenAiAssistant';
import { useGenAiTranslationContext } from '../../../GenAiTranslation';
import { CARDS, MEDIA, RTE_MEDIA } from '../../../Utils/Uploads/constants';
import { getLXMediaHubConfigValue } from '../../../Utils';
import { ENABLE_LX_MEDIA_HUB } from '../../../Utils/constants';
import { clearTextFieldError } from '../Shared/Functions/clearTextFieldError';

const Editor = LazyloadComponent(() => import('../../../Inputs/ckeditor'))();
const UPLOAD_CONTENT_IMAGES = hasPermission('UPLOAD_CONTENT_COVER_IMAGES');

const MultilangTextCard = ({
  setState,
  defaultOrgLanguage,
  languageDetails,
  validateRef,
  cardData = {},
  descriptionRequired,
  onChangeCKEditorMode
}) => {
  const {
    scrollTooltipRelativeElement,
    displayGenAiInlineAssistantForMultiLangCards
  } = useSmartCardContext();
  const { updateGenAiContext } = useGenAiAssistantContext();
  const { isUpdatedDescription } = useGenAiTranslationContext();

  const [localState, setLocalState] = useAsyncReference(null);
  const [descriptionProps] = useState(
    descriptionRequired ? { required: true } : { optional: true }
  );
  const [descImagesFileStackArr, setFileStackForDescImage] = useState(
    cardData?.descImagesFileStackArr || {}
  );
  const LXMediaHubConfig = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);

  useEffect(() => {
    if (!!descImagesFileStackArr) {
      setState({ ...localState.current, descImagesFileStackArr });
      setLocalState({ ...localState.current, descImagesFileStackArr });
    }
  }, [descImagesFileStackArr]);

  const handleChange = args => {
    const {
      value,
      key,
      languageAttr,
      multiLangState,
      updateMultilangState,
      isGenAiGenerated = false
    } = args;

    const { languages_attributes } = multiLangState;
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);
    const langAttr = {
      language: languageAttr,
      [key]: value
    };

    if (langIndex >= 0) {
      languages_attributes[langIndex][key] = value;
    } else {
      languages_attributes.push(langAttr);
    }

    if (displayGenAiInlineAssistantForMultiLangCards(languageAttr) && !isGenAiGenerated) {
      // we want to capture user entered values
      updateGenAiContextOnChange({
        key,
        value,
        langId: languageAttr,
        ckEditorInstanceName: `${languageAttr}_message`,
        updateFn: updateGenAiContext
      });
    }

    const state = {
      ...multiLangState,
      languages_attributes
    };
    updateMultilangState(state);
  };

  const descImgUploadCB = args => {
    const { filestack, languageAttr } = args;
    setFileStackForDescImage(oldArr => {
      if (oldArr[languageAttr]?.length) {
        oldArr[languageAttr].push({ ...filestack });
      } else {
        oldArr[languageAttr] = [{ ...filestack }];
      }
      return { ...oldArr };
    });
  };

  const onSetImageAlt = args => {
    const { altText, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes } = multiLangState;
    const state = {
      ...multiLangState,
      languages_attributes
    };

    if (LXMediaHubConfig) {
      const isValid = setAltImageText({
        languages_attributes,
        languageAttr,
        altText,
        mediaType: 'media_file',
        mediaTypeAlt: 'media_file_alt_text'
      });
      if (!isValid) return;
    } else {
      const imageObj = (languages_attributes || []).find(item => item.language === languageAttr)
        ?.media?.[0];
      if (!imageObj) {
        return;
      }
      imageObj['alt_text'] = altText;
    }

    updateMultilangState(state);
  };

  const handleThumbnailUpload = args => {
    const { file, languageAttr, hasMedia, multiLangState, updateMultilangState } = args;
    const { languages_attributes, useSameImage } = multiLangState;
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);

    if (LXMediaHubConfig) {
      if (isEmpty(file)) {
        languages_attributes[langIndex].media_file = null;
      } else {
        const updateMedia = (index, fileData) => {
          if (index >= 0) {
            languages_attributes[index].media_file = fileData;
            languages_attributes[index].is_only_thumbnail_image =
              hasMedia && languages_attributes[langIndex].is_only_thumbnail_image;
          }
        };
        if (useSameImage) {
          languages_attributes.forEach((_, index) => updateMedia(index, file?.file));
        } else {
          updateMedia(langIndex, file?.file);
        }
      }
    } else {
      const filestack = file;
      if (isEmpty(filestack)) {
        set(languages_attributes[langIndex], 'media', []);
      } else {
        if (langIndex >= 0) {
          filestack.file.is_cover_image = true;
          filestack.file.is_only_thumbnail_image =
            hasMedia && languages_attributes[langIndex].media[0]?.is_only_thumbnail_image;
          if (useSameImage) {
            for (let i = 0; i < languages_attributes.length; i++) {
              languages_attributes[i].media = [{ ...filestack.file }];
            }
          } else {
            languages_attributes[langIndex].media = [{ ...filestack.file }];
          }
        }
      }
    }

    const state = {
      ...multiLangState,
      languages_attributes
    };
    updateMultilangState(state);
  };

  const setThumbnailAllLanguagues = (languages_attributes, val) => {
    languages_attributes.map(lang => {
      const fileObj = LXMediaHubConfig ? lang : lang?.media?.[0];
      fileObj.is_only_thumbnail_image = val;
    });
  };

  const setThumbnailForSelectedLang = (languageAttr, languages_attributes, value) => {
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);
    if (langIndex >= 0) {
      const fileObj = LXMediaHubConfig
        ? languages_attributes[langIndex]
        : languages_attributes[langIndex].media?.[0];
      fileObj.is_only_thumbnail_image = value;
    }
  };

  const onCheckboxChange = args => {
    const { isChecked, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes, useSameImage } = multiLangState;

    if (useSameImage) {
      setThumbnailAllLanguagues(languages_attributes, isChecked);
    } else {
      setThumbnailForSelectedLang(languageAttr, languages_attributes, isChecked);
    }

    const state = {
      ...multiLangState,
      languages_attributes: [...languages_attributes]
    };
    updateMultilangState(state);
  };

  const accordionContent = ({
    item,
    languageAttr,
    cardErrorsMap,
    hasMedia,
    multiLangState,
    updateMultilangState,
    validateCardFields
  }) => {
    const handleFuncArgs = { languageAttr, hasMedia, multiLangState, updateMultilangState };
    const getPersistedFile = () => {
      return hasMedia && (LXMediaHubConfig ? item.media_file : item.media[0]);
    };

    const showThumbnailCheckbox =
      hasMedia && (LXMediaHubConfig ? item.media_file : !!item.media[0]?.is_cover_image);

    const useOnlyThumbnailValue = () => {
      return LXMediaHubConfig
        ? item.hasOwnProperty('is_only_thumbnail_image')
          ? item.is_only_thumbnail_image
          : item.media_file?.is_only_thumbnail_image
        : item.media[0]?.is_only_thumbnail_image;
    };

    const uploadParams = {
      uploadType: MEDIA,
      objectType: CARDS,
      isUGC: true
    };

    return (
      <>
        <TextField
          placeholder={translatr('cds.common.main', 'EnterSmartcardTitleHere2')}
          required
          shouldCheckForMaxChar={true}
          maxLen={TEXT_CARD_TITLE_LIMIT}
          defaultValue={item?.title}
          title={translatr('cds.common.main', 'Title')}
          description={translatr(
            'cds.common.main',
            'TheFollowingWillBeUsedToIdentifyYourSmartcard'
          )}
          setValue={value => handleChange({ value, key: 'title', ...handleFuncArgs })}
          name={`${languageAttr}_title`}
          error={cardErrorsMap[`${languageAttr}_title`]}
          onChangeCB={() => {
            clearTextFieldError({
              cardErrorsMap,
              fieldKey: `${languageAttr}_title`,
              validateCardFields
            });
          }}
          isTranslated
          {...(displayGenAiInlineAssistantForMultiLangCards(languageAttr) && {
            renderGenAiAssistant: () => (
              <GenAiAssistant
                entitySetter={value =>
                  handleChange({
                    value,
                    key: 'title',
                    isGenAiGenerated: true,
                    ...handleFuncArgs,
                    multiLangState: localState.current
                  })
                }
                entity={ENTITY.TITLE}
                langId={languageAttr}
              />
            )
          })}
        />
        <Editor
          title={translatr('cds.common.main', 'Description')}
          key={`${languageAttr}_card${isUpdatedDescription(languageAttr) ? '_translated' : ''}`}
          defaultValue={item?.message}
          placeholder={translatr('cds.common.main', 'EnterSmartcardDescriptionHere')}
          {...descriptionProps}
          name={`${languageAttr}_message`}
          setValue={value =>
            handleChange({
              value,
              key: 'message',
              ...handleFuncArgs,
              multiLangState: localState.current // Added ref here because it gets stale multilang state
            })
          }
          descImgUploadCB={filestack => {
            descImgUploadCB({
              filestack,
              ...handleFuncArgs,
              multiLangState: localState.current // Added ref here because it gets stale multilang state
            });
          }}
          error={cardErrorsMap[`${languageAttr}_message`]}
          description={translatr('cds.common.main', 'PleaseProvideTheContentsForYourSmartcard')}
          onChangeCKEditorMode={onChangeCKEditorMode}
          isTranslated
          genAiAssistantProps={{
            showGenAiAssistant: displayGenAiInlineAssistantForMultiLangCards(languageAttr),
            langId: languageAttr
          }}
          uploadParams={{ uploadType: RTE_MEDIA, objectType: CARDS, isUGC: true }}
        />
        <p className="ed-supporting-text-color font-size-m xs-padding-top">
          {translatr('cds.common.main', 'ChangesMadeUsingTheSourcePluginWillBeSavedOnly')}
        </p>
        {UPLOAD_CONTENT_IMAGES && (
          <div className="mt-16">
            <FileUpload
              persistedFile={getPersistedFile()}
              label={translatr('cds.common.main', 'UploadSmartcardImage')}
              setValue={file => handleThumbnailUpload({ file, ...handleFuncArgs })}
              setAlt={val => onSetImageAlt({ altText: val, ...handleFuncArgs })}
              scrollTooltipRelativeElement={scrollTooltipRelativeElement}
              isTranslated
              uploadParams={uploadParams}
            />
          </div>
        )}
        {!!showThumbnailCheckbox && (
          <UseOnlyThumbnailCheckbox
            value={useOnlyThumbnailValue()}
            onCheckboxChange={e =>
              onCheckboxChange({ isChecked: e.target.checked, ...handleFuncArgs })
            }
          />
        )}
      </>
    );
  };

  return (
    <MultiLang
      useSameImage
      setLocalState={setLocalState}
      setState={setState}
      defaultOrgLanguage={defaultOrgLanguage}
      orgLanguages={languageDetails}
      cardData={cardData}
      validateRef={validateRef}
      uploadThumbnailPermission={UPLOAD_CONTENT_IMAGES}
      accordionContent={accordionContent}
    />
  );
};

MultilangTextCard.propTypes = {
  setState: PropTypes.func,
  defaultOrgLanguage: PropTypes.string,
  languageDetails: PropTypes.array,
  validateRef: PropTypes.shape({ current: PropTypes.any }).isRequired,
  cardData: PropTypes.object,
  descriptionRequired: PropTypes.bool,
  onChangeCKEditorMode: PropTypes.func
};

export default MultilangTextCard;
