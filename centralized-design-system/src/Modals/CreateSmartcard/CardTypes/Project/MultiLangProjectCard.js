import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import set from 'lodash/set';

import { translatr } from '../../../../Translatr';
import MultiLang from '../multiLang';
import TextField from '../../../../Inputs/TextField';
import FileUpload from '../../../../FileUpload';
import { Select } from '../../../../Inputs';
import { hasPermission } from '../../../../Utils/OrgConfigs';
import { FILESTACK_MAX_FILE_SIZE } from '../../../../FileUpload/utils';
import LazyloadComponent from '../../../../Utils/lazy';
import {
  getLangAttrIndex,
  generateGradingScales,
  updateGenAiContextOnChange,
  setAltImageText
} from '../../Shared/utils';
import { useAsyncReference } from '../../../../Utils/hooks';
import { useSmartCardContext } from '../../Context/SmartCardConxtext';
import { ENTITY } from '../../../../constants/genAiConstants';
import GenAiAssistant, { useGenAiAssistantContext } from '../../../../GenAiAssistant';
import { useGenAiTranslationContext } from '../../../../GenAiTranslation';
import MultiselectWithSearchableOptions from '../../Components/MultiselectWithSearchableOptions';
import { ACTIVE_TYPES } from '../../Components/MultiselectWithSearchableOptions/constants';
import { CARDS, MEDIA, PROJECT_CARDS } from '../../../../Utils/Uploads/constants';
import { getLXMediaHubConfigValue } from '../../../../Utils';
import { ENABLE_LX_MEDIA_HUB } from '../../../../Utils/constants';
import { getGradingObjects } from './const';
import { clearTextFieldError } from '../../Shared/Functions/clearTextFieldError';

const Editor = LazyloadComponent(() => import('../../../../Inputs/ckeditor'))();
const UPLOAD_CONTENT_IMAGES = hasPermission('UPLOAD_CONTENT_COVER_IMAGES');
const MultilangProjectCard = ({
  setState,
  defaultOrgLanguage,
  languageDetails,
  validateRef,
  cardData,
  descriptionRequired,
  allowedMediaMimeTypes,
  isCurrentUserAdmin,
  enableShowEMail = false
}) => {
  const {
    scrollTooltipRelativeElement,
    displayGenAiInlineAssistantForMultiLangCards,
    options,
    setOptions
  } = useSmartCardContext();

  const { updateGenAiContext } = useGenAiAssistantContext();
  const { isUpdatedDescription } = useGenAiTranslationContext();

  const [localState, setLocalState] = useAsyncReference(null);
  const [projectFileAttr, setProjectFileAttr] = useAsyncReference(cardData?.projectDetails);
  const [descriptionProps] = useState(
    descriptionRequired ? { required: true } : { optional: true }
  );
  const [passingValues, setPassingValues] = useState([]);
  const [canCompleteCheckbox, setCanCompleteCheckbox] = useState(false);
  const [showReattempts, setShowReattempts] = useState(false);
  const [showPassingGrade, setShowPassingGrade] = useState(false);

  const LXMediaHubConfig = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
  const showLxMediaFlow = LXMediaHubConfig && !cardData?.projectDetails?.filestack?.length;

  useEffect(() => {
    const scale = generateGradingScales(projectFileAttr.current?.gradingScale);
    setPassingValues(scale);
  }, [projectFileAttr.current?.gradingScale]);

  useEffect(() => {
    setState(cardData);
    if (projectFileAttr.current) {
      setCanCompleteCheckbox(
        Object.keys(projectFileAttr.current).includes('canCompleteCardOnFail') &&
          !projectFileAttr.current.canCompleteCardOnFail
      );
      setShowPassingGrade(
        Object.keys(projectFileAttr.current).includes('showPassingGrade') &&
          projectFileAttr.current.showPassingGrade
      );
      setShowReattempts(
        Object.keys(projectFileAttr.current).includes('showReattempts') &&
          projectFileAttr.current.showReattempts
      );
    }
  }, []);

  const handleChange = args => {
    const {
      value,
      key,
      languageAttr,
      multiLangState,
      updateMultilangState,
      isGenAiGenerated = false
    } = args;
    const { languages_attributes } = multiLangState;
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);
    const langAttr = {
      language: languageAttr,
      [key]: value
    };

    if (langIndex >= 0) {
      languages_attributes[langIndex][key] = value;
    } else {
      languages_attributes.push(langAttr);
    }

    if (displayGenAiInlineAssistantForMultiLangCards(languageAttr) && !isGenAiGenerated) {
      // we want to capture user entered values
      updateGenAiContextOnChange({
        key,
        value,
        langId: languageAttr,
        ckEditorInstanceName: `${languageAttr}_message`,
        updateFn: updateGenAiContext
      });
    }

    const state = {
      ...multiLangState,
      languages_attributes,
      projectDetails: { ...projectFileAttr.current }
    };

    updateMultilangState(state);
  };

  const onSetThumbnailAlt = args => {
    const { altText, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes } = multiLangState;
    const state = {
      ...multiLangState,
      languages_attributes
    };

    if (LXMediaHubConfig) {
      const isValid = setAltImageText({
        languages_attributes,
        languageAttr,
        altText,
        mediaType: 'media_file',
        mediaTypeAlt: 'media_file_alt_text'
      });
      if (!isValid) return;
    } else {
      const mediaObj = (languages_attributes || []).find(item => item.language === languageAttr)
        ?.media;
      const imageObj = mediaObj?.[1] || mediaObj?.[0];
      if (!imageObj) {
        return;
      }
      imageObj['alt_text'] = altText;
    }
    updateMultilangState(state);
  };

  const handleThumbnailUpload = args => {
    const { file, languageAttr, multiLangState, updateMultilangState } = args;
    const { languages_attributes, useSameImage } = multiLangState;
    const langIndex = getLangAttrIndex(languageAttr, languages_attributes);

    if (LXMediaHubConfig) {
      if (isEmpty(file)) {
        languages_attributes[langIndex].media_file = null;
      } else {
        const updateMedia = (index, fileData) => {
          if (index >= 0) {
            languages_attributes[index].media_file = fileData;
          }
        };
        if (useSameImage) {
          languages_attributes.forEach((_, index) => updateMedia(index, file?.file));
        } else {
          updateMedia(langIndex, file?.file);
        }
      }
    } else {
      const filestack = file;
      if (isEmpty(filestack)) {
        set(languages_attributes[langIndex], 'media', []);
      } else {
        if (langIndex >= 0) {
          if (useSameImage) {
            for (let i = 0; i < languages_attributes.length; i++) {
              languages_attributes[i].media = [{ ...filestack.file }];
            }
          } else {
            languages_attributes[langIndex].media = [{ ...filestack.file }];
          }
        }
      }
    }

    const state = {
      ...multiLangState,
      languages_attributes,
      projectDetails: { ...projectFileAttr.current }
    };
    updateMultilangState(state);
  };

  const handleProjectFileUpload = file => {
    const state = {
      ...localState.current,
      projectDetails: {
        ...projectFileAttr.current,
        ...(LXMediaHubConfig
          ? {
              media: isEmpty(file) ? null : file?.file?.signed_id
            }
          : {
              filestack: isEmpty(file) ? [] : [{ ...file.file }]
            })
      }
    };
    setProjectFileAttr(state.projectDetails);
    setState(state);
  };

  const handleProjectCardGradient = (value, key) => {
    if (key === 'maxReAttemptsAllowed' && value > 99) {
      return;
    }
    let showPassingGradeValue = projectFileAttr?.current?.showPassingGrade;
    if (key === 'gradingScale' || (key === 'passingGrade' && !value)) {
      showPassingGradeValue = false;
      setShowPassingGrade(showPassingGradeValue);
    }
    const state = {
      ...localState.current,
      projectDetails: {
        ...projectFileAttr.current,
        [key]: value,
        showPassingGrade: showPassingGradeValue
      }
    };
    if (key === 'gradingScale') {
      state.projectDetails.passingGrade = null;
    }
    setProjectFileAttr(state.projectDetails);
    setState(state);
  };

  function canCompleteToggleCheckbox(event) {
    const state = {
      ...localState.current,
      projectDetails: {
        ...projectFileAttr.current,
        canCompleteCardOnFail: !event.target.checked
      }
    };
    setCanCompleteCheckbox(event.target.checked);
    setProjectFileAttr(state.projectDetails);
    setState(state);
  }

  function showReattemptsToggleCheckbox(event) {
    const state = {
      ...localState.current,
      projectDetails: {
        ...projectFileAttr.current,
        showReattempts: event.target.checked
      }
    };
    setShowReattempts(event.target.checked);
    setProjectFileAttr(state.projectDetails);
    setState(state);
  }

  function showPassingGradeToggleCheckbox(event) {
    const state = {
      ...localState.current,
      projectDetails: {
        ...projectFileAttr.current,
        showPassingGrade: event.target.checked
      }
    };
    setShowPassingGrade(event.target.checked);
    setProjectFileAttr(state.projectDetails);
    setState(state);
  }

  const uploadParams = {
    uploadType: MEDIA,
    isUGC: true
  };

  const accordionContent = ({
    item,
    languageAttr,
    cardErrorsMap,
    hasMedia,
    multiLangState,
    updateMultilangState,
    validateCardFields
  }) => {
    const handleFuncArgs = {
      languageAttr,
      multiLangState,
      updateMultilangState
    };

    const getFileData = () => {
      if (!hasMedia) return;
      if (LXMediaHubConfig) return item?.media_file;
      else return item?.media?.[0];
    };

    return (
      <>
        <TextField
          required
          placeholder={translatr('cds.common.main', 'EnterSmartcardTitleHere2')}
          shouldCheckForMaxChar={false}
          defaultValue={item?.title}
          title={translatr('cds.common.main', 'Title')}
          description={translatr('cds.common.main', 'EnterTitle')}
          setValue={value => handleChange({ value, key: 'title', ...handleFuncArgs })}
          name={`${languageAttr}_title`}
          error={cardErrorsMap[`${languageAttr}_title`]}
          isTranslated
          onChangeCB={() => {
            clearTextFieldError({
              cardErrorsMap,
              fieldKey: `${languageAttr}_title`,
              validateCardFields
            });
          }}
          {...(displayGenAiInlineAssistantForMultiLangCards(languageAttr) && {
            renderGenAiAssistant: () => (
              <GenAiAssistant
                entitySetter={value =>
                  handleChange({
                    value,
                    key: 'title',
                    isGenAiGenerated: true,
                    ...handleFuncArgs,
                    multiLangState: localState.current
                  })
                }
                entity={ENTITY.TITLE}
                langId={languageAttr}
              />
            )
          })}
        />
        <Editor
          key={`${languageAttr}_editor${isUpdatedDescription(languageAttr) ? '_translated' : ''}`}
          placeholder={translatr('cds.common.main', 'EnterSmartcardDescriptionHere')}
          setValue={value =>
            handleChange({
              value,
              key: 'message',
              ...handleFuncArgs,
              multiLangState: localState.current // Added ref here because it gets stale multilang state
            })
          }
          title={translatr('cds.common.main', 'Description')}
          editorType="inline"
          {...descriptionProps}
          name={`${languageAttr}_message`}
          defaultValue={item?.message}
          error={cardErrorsMap[`${languageAttr}_message`]}
          description={translatr(
            'cds.common.main',
            'PleaseProvideAnyAdditionalDetailsForThisEvent'
          )}
          isTranslated
          genAiAssistantProps={{
            showGenAiAssistant: displayGenAiInlineAssistantForMultiLangCards(languageAttr),
            langId: languageAttr
          }}
        />
        {UPLOAD_CONTENT_IMAGES && (
          <div className="mt-16">
            <FileUpload
              persistedFile={getFileData()}
              label={translatr('cds.common.main', 'UploadSmartcardImage')}
              setValue={file => handleThumbnailUpload({ file, ...handleFuncArgs })}
              setAlt={val => onSetThumbnailAlt({ altText: val, ...handleFuncArgs })}
              scrollTooltipRelativeElement={scrollTooltipRelativeElement}
              isTranslated
              uploadParams={{ ...uploadParams, objectType: CARDS }}
            />
          </div>
        )}
      </>
    );
  };
  const noAccordion = ({ cardErrorsMap, setCardErrorsMap }) => {
    const persistedFile = showLxMediaFlow
      ? projectFileAttr.current?.media
      : projectFileAttr.current?.filestack?.[0] || {};

    return (
      <>
        <div className="mt-16">
          <FileUpload
            label={translatr('cds.common.main', 'UploadFile')}
            buttonLabel={translatr('cds.common.main', 'UploadFile')}
            persistedFile={persistedFile}
            setValue={handleProjectFileUpload}
            allowedFileType={allowedMediaMimeTypes}
            recommendedSize={FILESTACK_MAX_FILE_SIZE}
            scrollTooltipRelativeElement={scrollTooltipRelativeElement}
            isTranslated
            uploadParams={{ ...uploadParams, objectType: PROJECT_CARDS }}
          />
        </div>
        {getGradingObjects().map((p, index) => {
          return (
            <div className="mt-16" key={`project-select-${index}`}>
              {p.id === 'grader' ? (
                <MultiselectWithSearchableOptions
                  field={'graders'}
                  name={'grader'}
                  error={cardErrorsMap[`${p.id}`]}
                  setCardErrorsMap={setCardErrorsMap}
                  options={options.graders}
                  activeTypes={[ACTIVE_TYPES.EVALUATOR, ACTIVE_TYPES.GROUP, ACTIVE_TYPES.USER]}
                  defaultActiveType={ACTIVE_TYPES.EVALUATOR}
                  setOptions={setOptions}
                  isCurrentUserAdmin={isCurrentUserAdmin}
                  isPathwayOrJourney={false}
                  enableShowEMail={enableShowEMail}
                  required={true}
                />
              ) : (
                <Select
                  id={p.id}
                  title={p.name}
                  disabled={
                    cardData &&
                    projectFileAttr.current?.projectId &&
                    p.name !== 'Grader' &&
                    p.name !== 'Grading Scale'
                  }
                  defaultValue={projectFileAttr.current?.[p.state]}
                  items={p.options}
                  onChange={e => handleProjectCardGradient(e?.value, p.state)}
                  isTranslated
                  translateDropDownOptions={false}
                />
              )}
            </div>
          );
        })}
        <div className="mt-16 passing-score-field">
          <Select
            title={translatr('cds.common.main', 'PassingScoreGrade')}
            items={passingValues}
            defaultValue={projectFileAttr?.current?.passingGrade}
            optional="Optional"
            onChange={obj => handleProjectCardGradient(obj?.value, 'passingGrade')}
            scrollTooltipRelativeElement={scrollTooltipRelativeElement}
            tooltipMessage={translatr('cds.common.main', 'TooltipForPassingScoreGrade')}
            isTranslated
            translateDropDownOptions={false}
          />
          <div className="ed-input-container">
            <label className="checkbox" htmlFor="passingGrade">
              <input
                type="checkbox"
                id="passingGrade"
                checked={showPassingGrade}
                disabled={!projectFileAttr?.current?.passingGrade}
                onChange={event => showPassingGradeToggleCheckbox(event)}
              />
              <span className="ed-checkbox-label">
                {translatr('cds.common.main', 'ShowPassingGradeToLearners')}
              </span>
            </label>
          </div>
        </div>
        <div className="mt-16">
          <TextField
            shouldCheckForMaxChar={false}
            type={'number'}
            title={translatr('cds.common.main', 'NumberOfReAttemptsAllowed')}
            optional="Optional"
            description={translatr('cds.common.main', 'LeaveThisBlankForUnlimitedReAttempts')}
            placeholder={translatr('cds.common.main', 'Eg3')}
            setValue={value => handleProjectCardGradient(value, 'maxReAttemptsAllowed')}
            defaultValue={projectFileAttr.current?.maxReAttemptsAllowed?.toString()}
            isTranslated
          />
          <div className="ed-input-container">
            <label className="checkbox" htmlFor="reattempts">
              <input
                type="checkbox"
                id="reattempts"
                checked={showReattempts}
                onChange={event => showReattemptsToggleCheckbox(event)}
              />
              <span className="ed-checkbox-label">
                {translatr('cds.common.main', 'ShowNumberOfReattemptsToTheLearner')}
              </span>
            </label>
          </div>
        </div>
        <div className="mt-16">
          <div className="ed-input-container">
            <label className="ed-input-title">
              {translatr('cds.common.main', 'CompletionBehavior')}
              <span className="asterisk">{translatr('cds.common.main', 'Optional')}</span>
            </label>
          </div>
          <label className="checkbox" htmlFor="completion-cb">
            <input
              type="checkbox"
              id="completion-cb"
              checked={canCompleteCheckbox}
              onChange={event => canCompleteToggleCheckbox(event)}
            />
            <span className="ed-checkbox-label">
              {translatr('cds.common.main', 'KeepTheCardIncompleteIfUserReceivesAFailingGrade')}
            </span>
          </label>
        </div>
      </>
    );
  };
  return (
    <>
      <MultiLang
        useSameImage
        setLocalState={setLocalState}
        setState={setState}
        defaultOrgLanguage={defaultOrgLanguage}
        orgLanguages={languageDetails}
        cardData={cardData}
        validateRef={validateRef}
        uploadThumbnailPermission={UPLOAD_CONTENT_IMAGES}
        accordionContent={accordionContent}
        noAccordion={noAccordion}
      />
    </>
  );
};

MultilangProjectCard.propTypes = {
  setState: PropTypes.func,
  defaultOrgLanguage: PropTypes.string,
  languageDetails: PropTypes.array,
  validateRef: PropTypes.shape({ current: PropTypes.any }).isRequired,
  cardData: PropTypes.object,
  allowedMediaMimeTypes: PropTypes.array,
  descriptionRequired: PropTypes.bool,
  isCurrentUserAdmin: PropTypes.bool,
  enableShowEMail: PropTypes.bool
};

export default MultilangProjectCard;
