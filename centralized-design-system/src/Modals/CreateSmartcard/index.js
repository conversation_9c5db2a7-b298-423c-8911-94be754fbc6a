/* eslint-disable default-case */
/* ============================================= LIBRARIES ====================================================== */
import React, { useState, useEffect, useRef } from 'react';
import { array, string, number, bool, object, oneOfType } from 'prop-types';
import { connect } from 'react-redux';
import { isEmpty, set as loDashSet } from 'lodash';
import { postv2 } from 'edc-web-sdk/requests/cards';
import { generateUUID } from 'edc-web-sdk/requests/agora';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';

import { translatr } from '../../Translatr';
/* ============================================= COMPONNETS  ====================================================== */
import Modal, { ModalHeader, ModalContent, ModalFooter } from '../index';
import Loading from '../../Loading';
import SmartCardFooterActions from './Components/SmartCardFooterActions';
import SmartCardModalContent from './Components/SmartCardModalContent';
import SmartCardDataProvider from './Context/SmartCardConxtext';
/* ============================================= Utils and Constants ====================================================== */
// Shared constants
import {
  defaultOptions,
  GEN_AI_ENABLED_SMARTCARD_TYPES,
  GEN_AI_TRANSLATION_ENABLED_SMARTCARD_TYPES,
  SMARTCARDS,
  SUPPORTED_MULTILANG_TABS,
  SUPPORTED_SAME_THUMBNAIL_UPLOAD
} from './Shared/const';
// Shared Helpers
import {
  getLXMediaHubSingleLangKeyValue,
  shouldShowLanguagesOptions,
  updateBackendResourceForArticleCard,
  updateDefaultThumbNailForVideoCard
} from './Shared/helpers';
//Shared Utils
import {
  isArticleCard,
  isContentCard,
  isTextCard,
  showEmbargoErrMsg,
  showErrMsgReceivedFromResp
} from './Shared/utils';
// OTHERS
import { getDefaultImage } from '../../Utils/filestack';
import getOrgLanguages, { getOrgLanguageDetails } from '../../Utils/getOrgLanguages';
import { getAllContentTypes } from '../../Utils/getAllContentTypes';
import { ENABLE_LX_MEDIA_HUB } from '../../Utils/constants';
// Functions
import {
  createOrUpdateQuizCard,
  executeCallbackOrShowSnackbar,
  getAllRequiredBackendConfigs,
  getErrorObjAfterValidation,
  getFilteredTabsBasedOnPermissions,
  getPrivacyRadioOptions,
  getCompletableByRadioOptions,
  keyDownHandler,
  onCreateSmartCardModalOpen,
  scrollToErrorField,
  convertMultiLangTagsToTopicsArray
} from './Shared/Functions';
import getCardTypeSpecificPayLoadFields, {
  getOptionFieldsPayload
} from './Shared/Functions/ConstructPayload';
import LD from '../../Utils/LDStore';
import { createOrUpdatePollCard } from './Shared/Functions/createOrUpdatePollCard';
/* ============================================= STYLES ====================================================== */
import './styles.scss';
import { isGenAiAssistantEnabled } from '../../GenAiAssistant';
import { isGenAiTranslationEnabled } from '../../GenAiTranslation';
import { getLXMediaHubConfigValue } from '../../Utils';
import { determineLevelsInDropdown } from './Shared/Functions/onCreateSmartCardModalOpenHelper';
import { noLevelPlaceholderOption } from '../../Utils/proficiencyLevels';

const {
  defaultOrgLanguage,
  multilangCardCreation,
  enableBIA,
  uniquecodeCardCreation,
  enableSmartCardPrice
} = getAllRequiredBackendConfigs();

const CreateSmartcard = ({
  activeTabIndex,
  dispatch,
  orgLanguages,
  orgLanguageDetails,
  allowedMediaMimeTypes,
  isCurrentUserAdmin,
  isCurrentUserOrgAdmin,
  customImagesBucketName,
  customImagesRegion,
  timeZone,
  timeZoneDetails,
  isOcgEnabled,
  isEgtEnabled,
  taxonomyDomain,
  currentUserLang,
  isCurator,
  enableShowEMail,
  proficiencyLevels,
  orgId
}) => {
  const navigate = useNavigate();

  const card = useRef(); // Using a ref for performance
  const validateRef = useRef();
  const cardFieldsRef = useRef();
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState(defaultOptions);
  const [providerImage, setProviderImage] = useState(false);
  const [saving, setSaving] = useState(false);
  const [paymentOptions, setPaymentOptions] = useState([]);
  const [hideRestrictionField, setHideRestrictionField] = useState(defaultOptions.public);
  const [cardErrorsMap, setCardErrorsMap] = useState({});

  const paymentCurrencyRef = useRef();
  const [cardId, setCardId] = useState(null);
  const previousCardData = useRef();
  const [activeTab, setActiveTab] = useState(activeTabIndex ?? 0);
  const [eventDetails, setEventDetails] = useState({});
  const [loadingEdit, setLoadingEdit] = useState(false);
  const [priceInput, setPriceInput] = useState('');

  const [showPreviousVersions, setShowPreviousVersions] = useState(false);
  const [previousVersions, setPreviousVersions] = useState([]);
  const [isSubmitBtnDisbled, setIsSubmitBtnDisbled] = useState(false);

  const quizId = useRef();
  const pollId = useRef();

  const languages = getOrgLanguages(orgLanguages);
  const languageDetails = getOrgLanguageDetails(orgLanguageDetails);
  const allContentTypes = getAllContentTypes();
  const privacy = getPrivacyRadioOptions({ isPublic: options.public });
  const completableBy = getCompletableByRadioOptions();
  const [smartCardtabs, setSmartCardTabs] = useState(getFilteredTabsBasedOnPermissions());

  useEffect(() => {
    // Since we duplicate the header for card views, we need to make sure we don't attach it twice
    if (window._modal_attached_) {
      return;
    }
    window.addEventListener('openSmartBiteModal', modalListener);
    window._modal_attached_ = true;
    return () => {
      window._modal_attached_ = false;
      window.removeEventListener('openSmartBiteModal', modalListener);
    };
  }, []);

  useEffect(() => {
    if (open) {
      document.querySelector('#smart_card_header')?.focus();
    }
  }, [open]);

  const getUUID = async () => {
    return await generateUUID();
  };

  function determineProficiencyLevels(initProficiencyLevels, objectWithSkillLevel) {
    const skillLevelValue = objectWithSkillLevel
      ? objectWithSkillLevel.skillLevel || noLevelPlaceholderOption().value
      : undefined;
    return determineLevelsInDropdown(initProficiencyLevels, currentUserLang, skillLevelValue);
  }

  function modalListener(event) {
    const setStateFns = {
      setPriceInput,
      setEventDetails,
      setLoadingEdit,
      setOptions,
      setPreviousVersions,
      setPaymentOptions,
      setHideRestrictionField,
      setProviderImage,
      setActiveTab,
      setOpen,
      setCardId,
      setSmartCardTabs
    };

    // For edit flow only
    const isEditFlow = event?.detail?.card;
    // In edit we add the card type even if the card specific permission is disabled
    // If creation flow check for card specifc permission
    if (!isEditFlow) {
      setSmartCardTabs(getFilteredTabsBasedOnPermissions());
    }
    onCreateSmartCardModalOpen({
      event,
      setStateFns,
      previousCardData,
      tabs: smartCardtabs,
      multilangCardCreation,
      card,
      quizId,
      pollId,
      isEditFlow,
      proficiencyLevels: determineProficiencyLevels(proficiencyLevels, event?.detail?.card),
      currentUserLang
    });
  }

  function closeCreateSmartcard() {
    const currentSectionId = eventDetails.sectionId;
    defaultOptions.share = [];
    defaultOptions.graders = [];
    setOptions(defaultOptions);
    localStorage.removeItem('groupToShareContent');
    setOpen(false);

    // reset everything also
    card.current = null;
    previousCardData.current = null;
    setProviderImage(false);
    setSaving(false);
    setPaymentOptions([]);
    setCardErrorsMap({});
    setCardId(null);
    setEventDetails({});
    setLoadingEdit(false);
    setPreviousVersions([]);
    setShowPreviousVersions(false);
    setHideRestrictionField(defaultOptions.public);

    if (currentSectionId || currentSectionId == 0) {
      document.querySelector(`#smart_card_button_${currentSectionId}`)?.focus();
    } else {
      document.querySelector('#createBtn')?.parentElement?.focus();
    }

    if (eventDetails?.onClose) {
      eventDetails.onClose();
    }
  }

  function setState(state) {
    card.current = typeof state === 'function' ? state(card.current) : state;
  }

  /** Inputs validations handler */
  function validateCardFields() {
    const errs = getErrorObjAfterValidation({
      cardFieldsRef,
      activeTab,
      tabs: smartCardtabs,
      options,
      multilangCardCreation,
      uniquecodeCardCreation,
      paymentOptions,
      priceInput,
      cardTabSpecificDetails: card.current,
      previousCardData: previousCardData.current
    });

    // Specific check for updating cards only
    if (LD.cardVersioning() && cardId && multilangCardCreation) {
      let { languages_attributes } = card.current;
      let langs = Object.keys(errs)
        .filter(e => e.includes('_file'))
        .map(e => e.split('_')[0]);
      // Remove errors for file uploads
      for (let language of langs) {
        // Test if the language is inactive
        let languageItem = languages_attributes.filter(item => item.language === language)[0];
        if (languageItem?.state === 'inactive') {
          delete errs[`${language}_file`]; // Remove error object
          languageItem.media[0].removeBeforeSending = true; // There are additional checks on media[1] before sending (Line 276)
        }
      }
    }

    const hasError = !!Object.keys(errs).length;

    if (hasError) {
      validateRef.current(errs);
      setCardErrorsMap(errs);
      scrollToErrorField();
      return false;
    }

    // Validation successful
    return true;
  }

  /**
   * Card creation/updation function.
   */
  async function saveOrUpdate() {
    const inputValidated = validateCardFields();

    if (!inputValidated) {
      return;
    }
    const lXMediaHubConfigValue = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
    const cardTabActive = smartCardtabs[activeTab]?.type;
    const cardRefData =
      cardTabActive === SMARTCARDS.liveCard
        ? { ...card.current, completable_by: options.completable_by }
        : card.current;
    let isHTMLContent = cardTabActive === SMARTCARDS.textCard || null;
    let cardPayload = getCardTypeSpecificPayLoadFields({
      cardRefData,
      activeCardType: cardTabActive,
      multilangCardCreation,
      previousCardData: previousCardData.current,
      cardLanguage: options.language,
      graders: options.graders,
      LXMediaHubConfig: lXMediaHubConfigValue
    });

    if (cardTabActive === SMARTCARDS.liveStreamCard) {
      const getID = await getUUID();
      let liveStreamObj = {
        start_time: moment().format('YYYY-MM-DD HH:mm:ss'),
        status: 'pending',
        uuid: getID.uuid,
        vendor: 'agora'
      };
      cardPayload['video_stream'] = liveStreamObj;
      cardPayload['title'] = cardRefData.title;
    }

    // If filestack or file available, add it
    if (
      !lXMediaHubConfigValue &&
      !isArticleCard(cardTabActive) &&
      !cardPayload.filestack &&
      ((cardRefData.filestack && cardRefData.filestack[0]?.file) || cardRefData.file)
    ) {
      // Use filestack if available, otherwise use file
      const fileToUse = cardRefData.filestack?.[0]?.file || cardRefData.file;

      if (fileToUse) {
        cardPayload['filestack'] = [fileToUse];
      }

      if (cardTabActive === SMARTCARDS.scormCard && !multilangCardCreation) {
        cardPayload.filestack[0].scorm_course = true;
        cardPayload.filestack[1] = cardRefData.filestack[1]
          ? cardRefData.filestack[1]
          : getDefaultImage(window.__ED__.id);
      }
    }

    if (
      !lXMediaHubConfigValue &&
      isContentCard(cardTabActive) &&
      cardRefData.filestack &&
      cardRefData.filestack[0]?.file &&
      cardRefData.filestack[0]?.file?.originalFile?.type
    ) {
      // Override mimetype with originalFile.type if it exists
      // This is done to fix the issue of mimetype not being set correctly for content cards
      // which happened after upgrading filestack to ^3.39.5
      // Once filestack addresses this issue, we can remove this code block
      cardRefData.filestack[0].file.mimetype = cardRefData.filestack[0].file.originalFile.type;
      cardPayload['filestack'] = [cardRefData.filestack[0].file];
    }

    // For LXMediaHubConfig, if media or thumbnail is present, then add it or add null for delete
    if (lXMediaHubConfigValue && !isArticleCard(cardTabActive)) {
      if (!cardPayload.media && getLXMediaHubSingleLangKeyValue(cardRefData.media)) {
        cardPayload.media = cardRefData.media;
      }
      if (
        !isTextCard(cardTabActive) &&
        !cardPayload.thumbnail &&
        getLXMediaHubSingleLangKeyValue(cardRefData.thumbnail)
      ) {
        cardPayload.thumbnail = cardRefData.thumbnail;
      }
      if (cardRefData['media_alt_text'] && !cardPayload['media_alt_text']) {
        cardPayload['media_alt_text'] = cardRefData['media_alt_text'];
      }
      if (
        !isTextCard(cardTabActive) &&
        cardRefData['thumbnail_alt_text'] &&
        !cardPayload['thumbnail_alt_text']
      ) {
        cardPayload['thumbnail_alt_text'] = cardRefData['thumbnail_alt_text'];
      }
    }

    /** ContentCard Thumbnail related changes */
    const isEditFlow = !!previousCardData.current;
    if (isContentCard(cardTabActive) && multilangCardCreation && isEditFlow) {
      cardPayload = updateDefaultThumbNailForVideoCard(cardPayload, previousCardData.current);
    }

    // If the user have not uploaded the thumbnail image for text/scorm/project card
    if (
      !lXMediaHubConfigValue &&
      SUPPORTED_SAME_THUMBNAIL_UPLOAD.includes(cardTabActive) &&
      multilangCardCreation
    ) {
      cardPayload.languages_attributes.map(lang => {
        if (cardTabActive === SMARTCARDS.scormCard && isEmpty(lang.media?.[1])) {
          loDashSet(lang, 'media[1]', getDefaultImage(window.__ED__.id));
        } else if (isEmpty(lang.media?.[0])) {
          loDashSet(lang, 'media[0]', getDefaultImage(window.__ED__.id));
        }
      });
    }

    // Special for Project/liveEvent card to add a default imagex
    if (
      !lXMediaHubConfigValue &&
      [SMARTCARDS.projectCard, SMARTCARDS.liveCard].includes(cardTabActive) &&
      !cardRefData.filestack
    ) {
      cardPayload.filestack = cardRefData.file
        ? [cardRefData.file]
        : [getDefaultImage(window.__ED__.id)];
    }

    // Remove non updated Scorm files (Line 276)
    cardPayload.languages_attributes &&
      cardPayload.languages_attributes.forEach(la => {
        if (la.media?.[0]?.removeBeforeSending) {
          la.media[0] = {};
        }
      });

    const optionsPayload = getOptionFieldsPayload({
      options,
      enableBIA,
      eventDetails,
      isHTMLContent,
      paymentOptions,
      providerImage,
      cardPayload,
      activeTab,
      LXMediaHubConfig: lXMediaHubConfigValue
    });

    if (multilangCardCreation) {
      // Convert multi-language tags to topics array
      convertMultiLangTagsToTopicsArray(cardPayload?.languages_attributes);
    }

    const _card = eventDetails?.card;
    let payload = {
      ...optionsPayload,
      ...cardPayload,
      ...(eventDetails.pathwayJourneyCallback && {
        hidden: _card === undefined ? true : _card.hidden
      })
    };

    let id = cardId; // null = new card
    setSaving(true);
    let resp;

    if (payload.card_type === 'quiz') {
      resp = await createOrUpdateQuizCard({
        id,
        quizId,
        payload,
        mutilangCreation:
          multilangCardCreation && SUPPORTED_MULTILANG_TABS.includes?.(cardTabActive)
      });
    } else if (payload.card_type === 'poll' && cardRefData?.supportsMultipleQuestions) {
      resp = await createOrUpdatePollCard({
        id,
        pollId,
        payload
      });
    } else {
      /** If has latestFile Object it means User has uploaded new image */
      const latestFile = lXMediaHubConfigValue
        ? cardRefData?.media
        : cardRefData?.filestack?.[0]?.file;

      if (isArticleCard(cardTabActive)) {
        /**
         * Update backend resource if user has uploaded new thumbnail
         */
        await updateBackendResourceForArticleCard({
          latestFile,
          payload
        });
      }

      // Call Create or Update card API
      const multiLangCard = {
        ...(multilangCardCreation &&
          SUPPORTED_MULTILANG_TABS.includes?.(cardTabActive) && {
            multi_lang_cards_creation: true
          })
      };

      resp = await postv2({ ...multiLangCard, ...{ card: payload } }, id).catch(err => {
        console.error(`Error in CDS CreateSmartcard.saveOrUpdate[postv2].func : ${err}`);
      });
    }

    const respHasErrorMsg = !!resp?.message;
    const respHasEmbargoErr = !!resp?.embargoErr;

    if (respHasErrorMsg) dispatch(showErrMsgReceivedFromResp(resp));

    if (resp?.slug || resp?.card?.slug) {
      executeCallbackOrShowSnackbar({ eventDetails, resp, dispatch, navigate });
      setSaving(false);
      closeCreateSmartcard();
      if (cardTabActive === SMARTCARDS.liveStreamCard) {
        navigate('/insights/' + resp?.slug || resp?.card?.slug);
      }
    } else if (respHasEmbargoErr) {
      dispatch(showEmbargoErrMsg(resp));
      setSaving(false);
    } else {
      // Maybe some type of error?
      setSaving(false);
    }
  }

  /**
   * Tab click handler
   */
  function changeTab(index) {
    card.current = null;
    setActiveTab(index);
  }

  // Handle generic change
  function handleChange(value, key) {
    if (key === 'public') {
      setHideRestrictionField(value == 'true');
    }
    setOptions({
      ...options,
      [key]: value
    });
  }

  if (!open) {
    return null;
  }

  const providerObj = {
    tabs: smartCardtabs,
    options,
    setOptions,
    shouldShowLanguagesOptions,
    activeCardType: smartCardtabs[activeTab]?.type,
    multilangCardCreation,
    handleChange,
    languages,
    languageDetails,
    cardFieldsRef,
    setCardErrorsMap,
    uniquecodeCardCreation,
    hideRestrictionField,
    cardErrorsMap,
    eventDetails,
    isCurator,
    enableBIA,
    isCurrentUserOrgAdmin,
    isCurrentUserAdmin,
    providerImage,
    setProviderImage,
    enableSmartCardPrice,
    paymentOptions,
    setPaymentOptions,
    priceInput,
    paymentCurrencyRef,
    allowedMediaMimeTypes,
    setState,
    defaultOrgLanguage,
    validateRef,
    customImagesBucketName,
    customImagesRegion,
    cardData: card.current,
    previousCardData: previousCardData.current,
    cardId,
    setIsSubmitBtnDisbled,
    timeZone,
    timeZoneDetails,
    showPreviousVersions,
    previousVersions,
    isSubmitBtnDisbled,
    saving,
    saveOrUpdate,
    allContentTypes,
    privacy,
    completableBy,
    changeTab,
    activeTab,
    setPriceInput,
    setShowPreviousVersions,
    closeCreateSmartcard,
    isOcgEnabled,
    isEgtEnabled,
    taxonomyDomain,
    currentUserLang,
    enableShowEMail,
    cardAuthorId: card.current?.author?.id,
    scrollTooltipRelativeElement: document.querySelector('.ed-dialog-modal'),
    proficiencyLevels: determineProficiencyLevels(proficiencyLevels, previousCardData?.current),
    /* genAI Flag Used for single lang cards*/
    displayGenAiInlineAssistant:
      isGenAiAssistantEnabled(options?.language) &&
      GEN_AI_ENABLED_SMARTCARD_TYPES.includes(smartCardtabs[activeTab]?.type),
    /**
     * In multialng card we pass the langId through param to check if we have support of genAI for that lang
     * because lets say user defined lang is french which is not genAI supported for now, but he selects english from the language accordion
     * in this case we need to show assistant for english accordion specific input fields
     */
    displayGenAiInlineAssistantForMultiLangCards: lang =>
      isGenAiAssistantEnabled(lang) &&
      GEN_AI_ENABLED_SMARTCARD_TYPES.includes(smartCardtabs[activeTab]?.type),
    displayGenAiInlineTranslationForMultiLangCards: (lang, multiLangState) =>
      isGenAiTranslationEnabled(lang, [
        multiLangState?.state?.questions,
        multiLangState?.languages_attributes
      ]) && GEN_AI_TRANSLATION_ENABLED_SMARTCARD_TYPES.includes(smartCardtabs[activeTab]?.type),
    orgId
  };

  return (
    <SmartCardDataProvider value={providerObj}>
      <Modal className="ed-create-smartcard visibility-visible">
        <ModalHeader
          id="smart_card_header"
          title={
            cardId || loadingEdit
              ? translatr('cds.common.main', 'UpdateSmartcard')
              : translatr('cds.common.main', 'CreateASmartcard')
          }
          onClose={closeCreateSmartcard}
          onKeyDown={keyDownHandler}
        />
        <ModalContent>{loadingEdit ? <Loading /> : <SmartCardModalContent />}</ModalContent>
        <ModalFooter>
          <SmartCardFooterActions isEditing={loadingEdit} />
        </ModalFooter>
      </Modal>
    </SmartCardDataProvider>
  );
};

function mapStateToProps({ team, currentUser }) {
  const {
    custom_images_bucket_name: customImagesBucketName,
    custom_images_region: customImagesRegion,
    taxonomy_domain: taxonomyDomain
  } = team.get('config');

  return {
    orgLanguages: team.get('languages'),
    orgLanguageDetails: team.get('languageDetails'),
    allowedMediaMimeTypes: team?.get('allowedMediaMimeTypes'),
    customImagesBucketName,
    customImagesRegion,
    isCurrentUserAdmin: currentUser?.get('isAdmin'),
    isCurrentUserOrgAdmin: currentUser?.get('isOrgAdmin'),
    timeZone: currentUser?.get('profile')?.get('timeZone'),
    timeZoneDetails: currentUser?.get('profile')?.get('timeZoneDetails'),
    isOcgEnabled: team.get('isOcgEnabled'),
    isEgtEnabled: team.get('isEgtEnabled'),
    taxonomyDomain,
    currentUserLang: currentUser.get('profile').get('language'),
    isCurator: currentUser.get('rolesDefaultNames')?.some(role => {
      return role.toLowerCase() === 'curator';
    }),
    enableShowEMail: team.get('config')?.enable_show_email,
    proficiencyLevels: team.get('proficiencyLevels'),
    orgId: team.get('orgId')
  };
}

CreateSmartcard.propTypes = {
  activeTabIndex: number,
  orgLanguages: object,
  orgLanguageDetails: array,
  allowedMediaMimeTypes: object,
  isCurrentUserAdmin: bool,
  isCurrentUserOrgAdmin: bool,
  customImagesBucketName: oneOfType([string, bool]),
  customImagesRegion: oneOfType([string, bool]),
  timeZone: oneOfType([string, object]),
  timeZoneDetails: object,
  isOcgEnabled: bool,
  isEgtEnabled: bool,
  taxonomyDomain: object,
  currentUserLang: string,
  isCurator: bool,
  enableShowEMail: bool,
  proficiencyLevels: array,
  orgId: number
};

export default connect(mapStateToProps)(CreateSmartcard);
