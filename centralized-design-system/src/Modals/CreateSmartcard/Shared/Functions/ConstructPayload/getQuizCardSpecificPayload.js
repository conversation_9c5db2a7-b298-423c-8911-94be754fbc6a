import { getLXMediaHubMultiLangValue } from '../../helpers';

const getQuizPayload = cardRefData => {
  const getOptionsAttributesForQuestion = options => {
    return options.map(option => {
      if (option.label) {
        const object = {
          option: option.label,
          is_correct: option.is_correct,
          _destroy: !!option._destroy
        };
        if (option.id) {
          object.id = option.id;
        }
        return object;
      }
    });
  };

  const getQuizQuestionsAttributes = () => {
    return cardRefData.questions.map(question => {
      const object = {
        question: question.question,
        question_options_attributes: getOptionsAttributesForQuestion(question.options),
        _destroy: !!question._destroy
      };
      if (question.id) {
        object.id = question.id;
      }
      return object;
    });
  };

  return {
    reanswerable: cardRefData.retake || false,
    passing_criteria: cardRefData.passing_criteria || null,
    mandatory: cardRefData.mandatory || false,
    quiz_questions_attributes: getQuizQuestionsAttributes()
  };
};

const getQuizForMultilangPayload = cardRefData => {
  const allLanguagesQuestions = cardRefData.state.questions;
  const createLanguagePayload = (valueKeyName, obj, key, lang) => ({
    [valueKeyName]: obj[key],
    language: lang
  });
  const findElementByNid = (elementList, nid) => elementList.find(element => element.nid === nid);

  const createQuestionLanguagesAttributes = question =>
    Object.entries(allLanguagesQuestions).map(([lang, questions]) => {
      const questionInLang = findElementByNid(questions, question.nid);
      return createLanguagePayload('question', questionInLang, 'question', lang);
    });

  const createOptionLanguagesAttributes = (question, option) =>
    Object.entries(allLanguagesQuestions).map(([lang, questions]) => {
      const questionInLang = findElementByNid(questions, question.nid);
      const optionInLang = findElementByNid(questionInLang.options, option.nid);
      return createLanguagePayload('option', optionInLang, 'label', lang);
    });

  const createOptionAttributes = (question, option) => ({
    option: option.label,
    is_correct: option.is_correct,
    _destroy: option._destroy,
    languages: createOptionLanguagesAttributes(question, option),
    ...(option.id && { id: option.id })
  });

  const createQuestionPayload = question => ({
    question: question.question,
    _destroy: question._destroy,
    languages: createQuestionLanguagesAttributes(question),
    question_options_attributes: question.options.map(option =>
      createOptionAttributes(question, option)
    ),
    ...(question.id && { id: question.id })
  });

  const getQuizQuestionsAttributes = () =>
    allLanguagesQuestions[cardRefData.language].map(createQuestionPayload);

  return {
    reanswerable: cardRefData?.state.retake || false,
    passing_criteria: cardRefData?.state.passing_criteria || null,
    mandatory: cardRefData?.state.mandatory || false,
    quiz_questions_attributes: getQuizQuestionsAttributes()
  };
};

export const getQuizCardSpecificPayload = ({
  multilangCardCreation,
  cardRefData,
  LXMediaHubConfig
}) => {
  if (multilangCardCreation) {
    return {
      quiz: getQuizForMultilangPayload(cardRefData),
      languages_attributes: LXMediaHubConfig
        ? cardRefData.languages_attributes.map(lang => ({
            ...lang,
            media_file: getLXMediaHubMultiLangValue(lang?.media_file)
          }))
        : cardRefData.languages_attributes,
      language: cardRefData.language,
      message: '',
      title: '',
      card_type: 'quiz',
      card_subtype: 'simple'
    };
  }
  return {
    quiz: getQuizPayload(cardRefData),
    message: '',
    card_type: 'quiz',
    card_subtype: 'simple',
    title: cardRefData.title
  };
};
