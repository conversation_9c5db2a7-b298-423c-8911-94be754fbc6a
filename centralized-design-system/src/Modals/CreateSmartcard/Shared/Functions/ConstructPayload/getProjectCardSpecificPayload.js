import { getLXMediaHubMultiLangValue, getLXMediaHubSingleLangKeyValue } from '../../helpers';

const _isMaxReAttemptsAllowed = projectDetails => {
  return (
    (projectDetails &&
      (Object.keys(projectDetails).includes('maxReAttemptsAllowed') &&
      projectDetails.maxReAttemptsAllowed === 0
        ? JSON.stringify(projectDetails.maxReAttemptsAllowed)
        : projectDetails.maxReAttemptsAllowed)) ||
    null
  );
};

const mapStaticGraderType = optionType => {
  if (optionType === 'member') {
    return 'User';
  } else if (optionType === 'group') {
    return 'Team';
  } else {
    console.error(`Unknown option ${optionType} for graders`);
  }
};

const mapOptionsToGradersPayload = (projectDetails, graderOptions) => {
  const staticGraders = [],
    dynamicGraders = [];
  graderOptions.forEach(graderOption => {
    if (graderOption.type === 'evaluator') {
      dynamicGraders.includes(graderOption.value) || dynamicGraders.push(graderOption.value);
    } else {
      staticGraders.push({
        _destroy: false,
        grader_id: graderOption.id,
        grader_type: mapStaticGraderType(graderOption.type)
      });
    }
  });
  //add removed graders with _destroy
  const removedGraders =
    projectDetails?.graders?.filter(
      grader =>
        !graderOptions.find(
          graderOption =>
            Number(graderOption.id) === Number(grader.grader_id) ||
            grader.grader_type === 'evaluator'
        )
    ) || [];
  removedGraders.forEach(grader => {
    staticGraders.push({
      _destroy: true,
      grader_id: grader.grader_id,
      grader_type: mapStaticGraderType(grader.grader_type)
    });
  });

  return {
    dynamic: dynamicGraders,
    static: staticGraders
  };
};

const _multiLangProjectCardPayload = (cardRefData, graders, isLXMediaHubEnabled) => {
  const { projectDetails, language, languages_attributes } = cardRefData;

  const [gradingSystem, gradingRange] = (projectDetails?.gradingScale || 'Grade (A-F)').split(' ');
  const projectId = projectDetails?.projectId || '';
  const filestack = projectDetails?.filestack;
  const media = projectDetails?.media;
  const completionBehaviour = projectDetails?.canCompleteCardOnFail;

  const passingGrade = projectDetails?.passingGrade || '';

  return {
    languages_attributes: isLXMediaHubEnabled
      ? languages_attributes.map(lang => ({
          ...lang,
          media_file: getLXMediaHubMultiLangValue(lang?.media_file)
        }))
      : languages_attributes,
    language: language,
    project_card_attributes: {
      ...(projectId !== '' ? { id: projectId } : {}),
      ...(isLXMediaHubEnabled
        ? {
            ...(getLXMediaHubSingleLangKeyValue(media) && { media: media })
          }
        : { filestack: filestack?.length ? filestack : [] }),
      grading_system: gradingSystem,
      grade_range: gradingRange,
      submitter_type: projectDetails?.submitterType || 'assignees',
      grader_type: graders ? 'mixed' : projectDetails?.graderType || 'assignor',
      can_complete_card_on_fail: completionBehaviour,
      max_reattempts_allowed: _isMaxReAttemptsAllowed(projectDetails),
      passing_grade: passingGrade,
      show_passing_grade: projectDetails?.showPassingGrade || false,
      show_reattempts: projectDetails?.showReattempts || false,
      ...(!!graders && {
        graders: mapOptionsToGradersPayload(projectDetails, graders)
      })
    }
  };
};

const _singleLangProjectCardPayload = (cardRefData, graders, LXMediaHubConfig) => {
  const { gradingScale, submitterType, graderType, projectDetails, projectFile } = cardRefData;
  const [gradingSystem, gradingRange] = (gradingScale || 'Grade (A-F)').split(' ');

  return {
    message: cardRefData.message,
    title: cardRefData.title,
    project_card_attributes: {
      id: cardRefData.projectId || null,
      grading_system: gradingSystem,
      grade_range: gradingRange,
      submitter_type: submitterType || 'assignees',
      grader_type: graders ? 'mixed' : graderType || 'assignor',
      can_complete_card_on_fail: projectDetails?.canCompleteCardOnFail,
      max_reattempts_allowed: _isMaxReAttemptsAllowed(projectDetails),
      passing_grade: projectDetails?.passingGrade || '',
      show_passing_grade: projectDetails?.showPassingGrade || false,
      show_reattempts: projectDetails?.showReattempts || false,
      ...(LXMediaHubConfig
        ? {
            ...(getLXMediaHubSingleLangKeyValue(projectFile) && { media: projectFile })
          }
        : !!projectFile && {
            filestack: projectFile[0] ? [projectFile[0].file] : [projectFile]
          }),
      ...(!!graders && {
        graders: mapOptionsToGradersPayload(projectDetails, graders)
      })
    }
  };
};

export const getProjectCardSpecificPayload = ({
  multilangCardCreation,
  cardRefData,
  graders,
  LXMediaHubConfig
}) => {
  if (multilangCardCreation) {
    return _multiLangProjectCardPayload(cardRefData, graders, LXMediaHubConfig);
  } else {
    return _singleLangProjectCardPayload(cardRefData, graders, LXMediaHubConfig);
  }
};
