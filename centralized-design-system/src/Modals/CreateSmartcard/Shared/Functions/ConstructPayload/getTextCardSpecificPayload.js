import { cardSubType, cardType } from '../../const';
import {
  getFilestackObjPayload,
  getLXMediaHubMultiLangValue,
  getLXMediaHubSingleLangKeyValue,
  getRteMedia,
  getTextCardDescription
} from '../../helpers';

export const _multiLangTextCardPayload = (cardRefData, isLXMediaHubEnabled) => {
  const { descImagesFileStackArr = {}, languages_attributes, language } = cardRefData;

  // Iterate through languages_attributes and modify each attribute
  languages_attributes.forEach(attr => {
    const languageFiles = descImagesFileStackArr?.[attr.language] || [];

    // Update message for each attribute
    attr.message = getTextCardDescription({
      message: attr.message,
      isExistingTextCard: !!cardRefData?.cardMetadatum?.isHtmlContent,
      descImagesFileArr: languageFiles
    });

    // Handle media-related changes based on LXMediaHub flag
    if (isLXMediaHubEnabled) {
      attr.rte_media = getRteMedia(languageFiles, attr.message);
    } else {
      attr.media = getFilestackObjPayload({
        filestack: attr.media?.[0],
        descImagesFileStackArr: languageFiles
      });
    }
  });

  return {
    languages_attributes: isLXMediaHubEnabled
      ? languages_attributes.map(lang => ({
          ...lang,
          media_file: getLXMediaHubMultiLangValue(lang?.media_file)
        }))
      : languages_attributes,
    language,
    card_type: cardType.media,
    card_subtype: cardSubType.text
  };
};

export const _singleLangTextCardPayload = (cardRefData, isLXMediaHubEnabled) => {
  const {
    message,
    filestack,
    title,
    media,
    is_only_thumbnail_image,
    descImagesFileStackArr,
    cardMetadatum
  } = cardRefData;

  // Get the text description for the card
  const description = getTextCardDescription({
    message,
    isExistingTextCard: !!cardMetadatum?.isHtmlContent,
    descImagesFileArr: descImagesFileStackArr || []
  });

  // If LXMediaHub is not enabled, generate filestack data
  const filestackPayload = !isLXMediaHubEnabled
    ? getFilestackObjPayload({
        filestack: filestack?.[0]?.file,
        descImagesFileStackArr: descImagesFileStackArr || []
      })
    : [];

  // Build the payload based on LXMediaHub status
  const payload = isLXMediaHubEnabled
    ? {
        // Conditionally add media if available or add null if deleted
        ...(getLXMediaHubSingleLangKeyValue(media) && { media }),
        is_only_thumbnail_image,
        rte_media: getRteMedia(descImagesFileStackArr, description)
      }
    : { filestack: filestackPayload };

  // Return the complete payload
  return {
    message: description,
    ...payload,
    title,
    card_type: cardType.media,
    card_subtype: cardSubType.text
  };
};

export const getTextCardSpecificPayload = ({
  multilangCardCreation,
  cardRefData,
  LXMediaHubConfig
}) => {
  if (multilangCardCreation) {
    return _multiLangTextCardPayload(cardRefData, LXMediaHubConfig);
  } else {
    return _singleLangTextCardPayload(cardRefData, LXMediaHubConfig);
  }
};
