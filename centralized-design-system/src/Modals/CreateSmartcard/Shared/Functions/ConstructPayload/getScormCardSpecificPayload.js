import { getLXMediaHubMultiLangValue } from '../../helpers';

export const getScormCardSpecificPayload = ({
  multilangCardCreation,
  cardRefData,
  LXMediaHubConfig
}) => {
  if (multilangCardCreation) {
    return {
      languages_attributes: LXMediaHubConfig
        ? cardRefData.languages_attributes.map(lang => ({
            ...lang,
            thumbnail: getLXMediaHubMultiLangValue(lang?.thumbnail)
          }))
        : cardRefData.languages_attributes,
      language: cardRefData.language,
      card_type: 'scorm',
      card_subtype: 'link',
      state: 'processing'
    };
  } else {
    return {
      /**
       * Title and message are swapped intentionally based on the backend changes
       */
      message: cardRefData.title,
      title: cardRefData.message,
      card_type: 'scorm',
      card_subtype: 'link',
      state: 'processing'
    };
  }
};
