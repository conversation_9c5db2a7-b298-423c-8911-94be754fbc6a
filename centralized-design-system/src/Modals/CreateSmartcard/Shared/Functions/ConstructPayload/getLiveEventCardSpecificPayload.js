import moment from 'moment';

import { DATE_FORMAT, ISO_FORMAT } from '../../const';
import getLanguageCodeForMoment from '../../../../../Utils/getLanguageCodeForMoment';
import { getLXMediaHubMultiLangValue } from '../../helpers';

const buildTrainingAttributes = cardRefData => {
  const {
    meeting_id,
    conferencing_tool,
    registration_type,
    time_zone,
    start_date,
    end_date,
    meeting_details,
    close_registration_type,
    registration_limit,
    last_registration_date,
    completable_by
  } = cardRefData;
  const languageAbbreviation = getLanguageCodeForMoment();

  return {
    id: !!meeting_id ? meeting_id : null,
    start_date: moment(start_date, [DATE_FORMAT, ISO_FORMAT], languageAbbreviation)
      .locale('en')
      .format('YYYY-MM-DD HH:mm:ss'),
    end_date: moment(end_date, [DATE_FORMAT, ISO_FORMAT], languageAbbreviation)
      .locale('en')
      .format('YYYY-MM-DD HH:mm:ss'),
    conferencing_tool: conferencing_tool || 'zoom',
    registration_type: registration_type,
    time_zone: time_zone,
    completable_by: completable_by,
    meeting_details: {
      meeting_url: meeting_details.meeting_url,
      meeting_detail_type: meeting_details.meeting_detail_type,
      ...(new Date(end_date) < new Date() && {
        recording_url: meeting_details.recording_url
      })
    },
    registration_limit:
      close_registration_type === 'viltMaxUsers' || !last_registration_date
        ? registration_limit
        : null,

    last_registration_date:
      close_registration_type === 'viltEndDate' || !registration_limit
        ? last_registration_date
        : null
  };
};

export const getLiveEventCardSpecificPayload = ({
  cardRefData,
  multilangCardCreation,
  LXMediaHubConfig
}) => {
  const { completable_by } = cardRefData;
  if (multilangCardCreation) {
    return {
      card_type: 'training',
      language: cardRefData.language,
      training_attributes: buildTrainingAttributes({ ...cardRefData.state, completable_by }),
      languages_attributes: LXMediaHubConfig
        ? cardRefData.languages_attributes.map(lang => ({
            ...lang,
            media_file: getLXMediaHubMultiLangValue(lang?.media_file)
          }))
        : cardRefData.languages_attributes
    };
  }
  return {
    card_type: 'training',
    /**
     * We had to deliberately swap the title and message order to conform to the design of the backend code.
     * */
    message: cardRefData.title,
    title: cardRefData.message,
    training_attributes: buildTrainingAttributes(cardRefData)
  };
};
