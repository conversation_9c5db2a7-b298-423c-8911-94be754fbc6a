import { updateResource } from 'edc-web-sdk/requests/cards';
import { isEmpty, set } from 'lodash';

import { getDefaultImage } from '../../../Utils/filestack';
import { getMandatoryFieldsFromOrgConfigs } from '../../../Utils/getMandatoryFieldsFromOrgConfigs';
import {
  cardSubType,
  cardType,
  SMARTCARDS,
  TRANSCRIPT_STATUS,
  VIDEO_CARD_ATTACHEMENTS,
  VIDEO_MIME_TYPE_PREFIX
} from './const';
import { getAllRequiredBackendConfigs } from './Functions';
import { getLangAttrIndex, isLanguageEnglish } from './utils';
import { getFilteredTabsBasedOnPermissions } from '../Shared/Functions';
import { translatr } from '../../../Translatr';
import { getLXMediaHubConfigValue } from '../../../Utils';
import { ENABLE_LX_MEDIA_HUB } from '../../../Utils/constants';
import { TAGS_ARE_REQUIRED } from '../../../messageConstants';

const { multilangCardCreation } = getAllRequiredBackendConfigs();

export function getFormattedTextForCKEditor(string) {
  if (!string) {
    return '';
  }
  return string.replace(/&amp;/g, '&');
}

export const getTextCardDescription = ({ message, isExistingTextCard, descImagesFileArr }) => {
  if (!message) {
    return '';
  }
  let newDesc = getFormattedTextForCKEditor(message);

  if (message?.length && isExistingTextCard) {
    newDesc = newDesc
      .replace(/<p>&nbsp;<\/p>\n\n<p>&nbsp;<\/p>/g, '<p>&nbsp;</p>')
      .replace(/<br\/>/g, '<p>&nbsp;</p>')
      .replace(/<p>&nbsp;<\/p>\n$/, ''); //Remove lastly added space
  }
  //replace securedURL with filestack url
  //This logic is required for setting conver image for text card
  //For other images uploaded inside CKEditor we should not set cover image
  //This will be call once once for saving description of existing text card

  const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);

  for (let fileObj of descImagesFileArr) {
    const securedUrl = isLXMediaHubEnabled
      ? fileObj.url
      : fileObj.securedUrl || fileObj.secured_url;
    const replacementUrl = isLXMediaHubEnabled ? fileObj.key || fileObj.payload.key : fileObj.url;

    if (newDesc.includes(securedUrl)) {
      newDesc = newDesc.replace(securedUrl, replacementUrl);
    }
  }

  return newDesc;
};

export const getFilestackObjPayload = ({ filestack, descImagesFileStackArr }) => {
  const fileStackArray = [...descImagesFileStackArr];

  // If has filestack object add it to the beginning of the fileStackArray
  if (filestack?.url) {
    fileStackArray.unshift(filestack);
  }

  return fileStackArray;
};

export const getRteMedia = (descImagesFileArr = [], message) => {
  return (
    descImagesFileArr
      .filter(file => message.includes(file.key || file?.payload?.key))
      .map(file => file?.signed_id || file?.payload?.signed_id) || [] // Always returns an array
  ); // Ensure an empty array if map results in an empty array
};

export const getIsPublicBasedOnScenario = ({ eventDetails, options }) => {
  let isPublic;

  if (eventDetails?.card?.hidden || (eventDetails.pathwayJourneyCallback && !eventDetails?.card)) {
    // Sceanrio 1: If existing hidden card or creating card from within pathwayJourney then isPublic ==> true
    isPublic = 'true';
  } else if (eventDetails.pathwayJourneyCallback && eventDetails?.card?.isPublic) {
    // Sceanrio 2: Editing external card from within pathwayJourney, isPublic will be --> card.isPublic
    isPublic = `${eventDetails.card.isPublic}`;
  } else {
    // Sceanrio 3: For normal cards isPublic will be based on chosen radio option.
    isPublic = `${options.public}`;
  }

  return isPublic;
};

export const shouldShowLanguagesOptions = ({
  isMultiLangEnabled,
  multiLangTabs,
  currentActiveTab
}) => {
  /**
   * For single lang we show language options DD purely bsaed on the multilingual-content LD.
   * For Multilang we have two cases
   *  Case 1: If currentActivetab is present in multiLangSupportedTabs then do not show the dropdown
   *  Case 2: If currentActivetab is not present in multiLangSupportedTabs and multilingual-content LD is enabled then we show the DD
   */
  return isMultiLangEnabled ? !multiLangTabs.includes(currentActiveTab) : true;
};

export const isTextCard = card => {
  return card.cardType === cardType.media && card.cardSubtype === cardSubType.text;
};

export const isLiveCard = card => {
  return card.cardType === cardType.liveCard && card.cardSubtype === cardSubType.vilt;
};

export const isUploadCard = card => {
  return (
    card.cardType === cardType.media &&
    [cardSubType.audio, cardSubType.video, cardSubType.image, cardSubType.file].includes(
      card.cardSubtype
    )
  );
};

export const getMessageFromCard = card => {
  /**
   * For scorm and project card we dont fallback to the cardTitle field for message.
   *
   */

  const cardTypeHasDescSupport =
    ['scorm', 'project'].includes(card.cardType) ||
    isTextCard(card) ||
    isUploadCard(card) ||
    isLiveCard(card);
  const messageForCardTypeWithDescSupport = card.cardMessage || '';

  const messageForCardTypeWithoutDescSupport = card.cardMessage ? card.cardMessage : card.cardTitle;

  return cardTypeHasDescSupport
    ? messageForCardTypeWithDescSupport
    : messageForCardTypeWithoutDescSupport;
};

const hasUploadNewFile = (oldFileKey, currentFileKey) => {
  return oldFileKey !== currentFileKey;
};

const hadThumbNailPreviously = fileStackArray => fileStackArray.length === 2;

const isVideoContentCard = mimetype => !!mimetype && mimetype.includes('video');

/**
 * Function returns the default image for single lang upload video content card
 * if it satisfies the mentioned conditions.
 */
export const getDefaultImageIfApplicable = (cardData, previousCardData) => {
  const oldFileKey = previousCardData?.filestack?.[0]?.key;
  const currentFileKey = cardData?.file?.key;

  return isVideoContentCard(cardData?.file?.mimetype) &&
    !hasUploadNewFile(oldFileKey, currentFileKey) &&
    hadThumbNailPreviously(previousCardData.filestack)
    ? getDefaultImage(window.__ED__.id)
    : null;
};

const getRequiredAttributes = (lang, prevLanguages) => {
  const indexOfCurrentLangInPreviousLanguages = getLangAttrIndex(lang.language, prevLanguages);
  return {
    mediaArray: lang?.media,
    indexOfCurrentLangInPreviousLanguages,
    currentLangNotPresentInPreviousCard: indexOfCurrentLangInPreviousLanguages === -1,
    currentLangHasThumbnail: lang?.media?.length === 2
  };
};

const updateThumbNailAndKeyInFirstMediaObj = (lang, defaultImg) => {
  set(lang, 'media[0]', {
    ...lang.media[0],
    thumbnail: defaultImg.url,
    thumbnail_key: defaultImg.key
  });
};

/**
 * Method updates the video media obj with the default thumbnail if user deletes the prev uploaded video thumbnail
 */
export const updateDefaultThumbNailForVideoCard = (cardPayload, previousCardData) => {
  if (cardPayload.languages_attributes?.length) {
    cardPayload.languages_attributes.map(lang => {
      const {
        mediaArray,
        indexOfCurrentLangInPreviousLanguages,
        currentLangNotPresentInPreviousCard,
        currentLangHasThumbnail
      } = getRequiredAttributes(lang, previousCardData.languages);

      if (currentLangNotPresentInPreviousCard || currentLangHasThumbnail) return; // stop current execution

      const currentLangObjFromPreviousCard =
        previousCardData.languages[indexOfCurrentLangInPreviousLanguages];
      const currentFileKey = mediaArray?.[0]?.key;
      const oldFileKey = currentLangObjFromPreviousCard?.media?.[0]?.key;

      if (
        isVideoContentCard(mediaArray?.[0]?.mimetype) &&
        hadThumbNailPreviously(currentLangObjFromPreviousCard?.media) &&
        !hasUploadNewFile(oldFileKey, currentFileKey)
      ) {
        if (!isEmpty(lang?.media?.[0])) {
          updateThumbNailAndKeyInFirstMediaObj(lang, getDefaultImage(window.__ED__.id));
        }

        set(lang, 'media[1]', getDefaultImage(window.__ED__.id));
      }
    });
  }

  return cardPayload;
};

export const updateBackendResourceForArticleCard = async ({ latestFile, payload }) => {
  const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
  if (multilangCardCreation) {
    const { languages_attributes } = payload;
    const queries = [];

    languages_attributes.map(attr => {
      const {
        id: resourceId,
        imageEncryptedUrl,
        imageKey,
        imageSize,
        mediaFile
      } = attr?.resource_attributes;
      if (isLXMediaHubEnabled) {
        if (!!mediaFile) {
          queries.push(
            updateResource(resourceId, {
              resource: {
                image: mediaFile?.signed_id
              }
            })
          );
        }
        delete attr?.resource_attributes?.embedHtml;
        delete attr?.resource_attributes?.mediaFile;
      } else {
        /**
         * we use imageEncryptedUrl to detect if update resource backend call should be made or not.
         * if this is present means image has been updated.
         */
        if (!!imageEncryptedUrl) {
          queries.push(
            updateResource(resourceId, {
              image_url: imageEncryptedUrl,
              image_key: imageKey,
              image_size: imageSize
            })
          );
        }
        // We delete this keys since its not required in backend and these creates isses while creating cards.
        delete attr?.resource_attributes?.imageKey;
        delete attr?.resource_attributes?.imageUrl;
        delete attr?.resource_attributes?.imageEncryptedUrl;
        delete attr?.resource_attributes?.embedHtml;
      }
    });

    // call resource update api
    try {
      await Promise.all(queries);
    } catch (err) {
      console.error(
        `Error in CDS CreateSmartcard.saveOrUpdate[updateResource][multilang].func : ${err}`
      );
    }
  } else if (!!latestFile) {
    // ----------- SingleLang --------------
    // Update resource in the backend based on newly uploaded image.
    try {
      if (isLXMediaHubEnabled) {
        await updateResource(payload.resource_id, {
          resource: {
            image: latestFile
          }
        });
      } else {
        await updateResource(payload.resource_id, {
          image_url: latestFile?.url,
          image_key: latestFile?.key,
          image_size: latestFile?.size
        });
      }
    } catch (err) {
      console.error(`Error in CDS CreateSmartcard.saveOrUpdate[updateResource].func : ${err}`);
    }
  }
};

export const shouldCheckForMultiLangTags = () => {
  const MANDATORY_FIELDS = getMandatoryFieldsFromOrgConfigs();

  if (MANDATORY_FIELDS.smartCardTags) {
    return multilangCardCreation;
  }

  return false;
};

export const areTagsEmpty = tags => {
  return !tags || tags.length === 0;
};

export const isMultiLangCard = activeTab => {
  if (multilangCardCreation) {
    const noMultipleLangCards = [SMARTCARDS.pollCard];
    return !noMultipleLangCards.includes(getFilteredTabsBasedOnPermissions()[activeTab]?.type);
  }

  return false;
};

export const getSubtitleButtonLabel = subtitleFileObj =>
  !isEmpty(subtitleFileObj)
    ? translatr('cds.create-smartcard.main', 'UploadNewSubtitles')
    : translatr('cds.create-smartcard.main', 'UploadSubtitles');

export const getTranscriptButtonLabel = (
  transcriptFileObjFileName,
  autoGenerateTranscriptStatus
) => {
  return [
    TRANSCRIPT_STATUS.in_progress,
    TRANSCRIPT_STATUS.failed,
    TRANSCRIPT_STATUS.completed
  ].includes(autoGenerateTranscriptStatus)
    ? translatr('cds.create-smartcard.main', 'UploadManualTranscript')
    : transcriptFileObjFileName
    ? translatr('cds.create-smartcard.main', 'UploadNewTranscript')
    : translatr('cds.create-smartcard.main', 'UploadTranscript');
};

export const isMimeTypeVideo = fileType => fileType?.startsWith(VIDEO_MIME_TYPE_PREFIX);

export const getSubtitlesAndTranscriptUploadFilePath = orgId => `/organization_${orgId}/`;

export const canAutoGenerateTranscript = ({
  languageAttr,
  autoGenerateVideoTranscriptEnabled,
  cardId,
  fileObj = {},
  showLxMediaEnabled
}) => {
  const transcriptObj = showLxMediaEnabled ? fileObj : fileObj?.transcript;
  const transcriptStatus = showLxMediaEnabled
    ? fileObj?.auto_generate_transcript_status
    : fileObj?.transcript?.auto_generate_transcript_status;
  const isAutoGenerate = fileObj?.auto_generate_transcript;

  // For existing cards
  if (cardId) {
    // Case 1: Don't show auto-generate if transcript exists and is either completed or manually uploaded
    if (
      !isEmpty(transcriptObj) &&
      (!isAutoGenerate || transcriptStatus === TRANSCRIPT_STATUS.completed)
    ) {
      return false;
    }

    // Case 2: Show auto-generate if no transcript and status is in-progress or failed
    if (
      isEmpty(transcriptObj) &&
      [
        TRANSCRIPT_STATUS.in_progress,
        TRANSCRIPT_STATUS.failed,
        TRANSCRIPT_STATUS.completed
      ].includes(transcriptStatus)
    ) {
      return true;
    }
  }

  // For new cards: Show auto-generate if no transcript, feature enabled and language is English
  return (
    isEmpty(transcriptObj) && autoGenerateVideoTranscriptEnabled && isLanguageEnglish(languageAttr)
  );
};

export const getVideoCardAttachmentUpdate = (type, filestack, isAutoGenerate) => {
  if (type === VIDEO_CARD_ATTACHEMENTS.subtitle && filestack?.file) {
    return { subtitle: filestack.file };
  }
  // According to backend requirements we add the subtitle hash directly to the primary object in the filestack array
  // When transcript object is present OR auto generate is true then we need to send transcript object
  if (type === VIDEO_CARD_ATTACHEMENTS.transcript) {
    return {
      ...(filestack?.file && { transcript: { ...filestack.file } }),
      ...(isAutoGenerate && { auto_generate_transcript: true })
    };
  }

  return {};
};

export const isAutoGenerateButtonDisabled = (isAutoGenerate, autoGenerateTranscriptStatus) =>
  autoGenerateTranscriptStatus === TRANSCRIPT_STATUS.completed
    ? false
    : isAutoGenerate || autoGenerateTranscriptStatus === TRANSCRIPT_STATUS.in_progress;

export const getVttConversionTooltip = () =>
  translatr('cds.create-smartcard.main', 'SrtToVttFileConversionInfoMessage');

/**
 * Compares file handles between previous and new media to determine if download option should be displayed.
 * Returns true if handles match, indicating the file hasn't changed.
 */
const isMediaFileUnchanged = (savedData, language, currentHandle, mediaType) => {
  const savedMedia = savedData?.languages_attributes.find(item => item.language === language);

  // For LX Media flow, compare using URL
  if (savedMedia?.[mediaType]?.url) {
    return savedMedia[mediaType].url === currentHandle?.url;
  }

  // For regular filestack flow, compare using handle
  return savedMedia?.media?.[0]?.[mediaType]?.handle === currentHandle?.handle || false;
};
/**
 * Get download status for transcript and subtitle
 */
export const getMediaDownloadStatus = ({ cardId, savedData, language, currentMedia }) => ({
  transcript:
    cardId &&
    isMediaFileUnchanged(
      savedData,
      language,
      currentMedia?.transcript,
      VIDEO_CARD_ATTACHEMENTS.transcript
    ),
  subtitle:
    cardId &&
    isMediaFileUnchanged(
      savedData,
      language,
      currentMedia?.subtitle,
      VIDEO_CARD_ATTACHEMENTS.subtitle
    )
});
export const isKeyPresent = (obj, key) => {
  return obj.hasOwnProperty(key);
};

export const getLXMediaHubMultiLangKeyValue = obj => {
  return obj === null ? null : obj?.signed_id;
};

export const getLXMediaHubMultiLangValue = obj => {
  return obj === null ? null : obj?.signed_id || obj?.key;
};

export const getLXMediaHubSingleLangKeyValue = obj => {
  return (obj && typeof obj === 'string') || obj === null;
};

export const validateMultiLangTags = (languages_attributes, errors) => {
  if (!shouldCheckForMultiLangTags()) return errors;

  languages_attributes.forEach(({ language, tags }) => {
    if (areTagsEmpty(tags)) {
      errors[`${language}_tags`] = TAGS_ARE_REQUIRED();
    }
  });

  return errors;
};
