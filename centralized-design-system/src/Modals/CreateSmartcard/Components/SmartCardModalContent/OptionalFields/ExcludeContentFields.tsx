import React from 'react';
import { connect } from 'react-redux';

import { translatr } from '../../../../../Translatr';
import Tooltip from '../../../../../Tooltip';
import Checkbox from '../../../../../Checkbox';
import remCalc from '../../../../../Utils/remCalc';
import { getNewTranslatedLabel, getProfileLanguage } from '../../../../../Utils/TranslatedLabels';

interface ExcludeContentFieldsProps {
  excludeFromSearch: boolean;
  excludeFromRecommendation: boolean;
  handleChange: (value: boolean, field: string) => void;
  todaysLearningSearchObj: object;
  allLangs: { [language: string]: string; };
  currentUserLang: string;
  currentAppLang: string;
}

interface RootState {
  currentUser: Map<string, any>;
  team: Map<string, any>;
}

const ExcludeContentFields: React.FC<ExcludeContentFieldsProps> = ({
  excludeFromSearch,
  excludeFromRecommendation,
  handleChange,
  todaysLearningSearchObj,
  allLangs,
  currentUserLang,
  currentAppLang
}) => {

  const excludeContentTooltipMsg = translatr(
    'cds.common.main',
    'ExcludeContentHint'
  );

  const profileLang = getProfileLanguage({
    langs: allLangs,
    currentUserLang: currentUserLang || currentAppLang || 'en'
  });

  const recommendationLabel = getNewTranslatedLabel({
    labelObj: todaysLearningSearchObj,
    appName: 'web.home.main',
    profileLanguage: profileLang,
    newDefaultLabel: 'TodaysInsights',
    prioritiseNewLabel: true
  });

  const excludeFromRecommendationLabel = translatr('cds.common.main', 'ExcludeFromRecommendations', {
    excludeFromRecommendationLabel: recommendationLabel
  })

  return (
    <div className="mt-16" role="group" aria-labelledby="exclude-content-label">
      <div className="ed-input-container">
        <label className="ed-input-title">
          <span id="exclude-content-label">
            {translatr('cds.common.main', 'ExcludeContent')}
            <span className="optional-text" aria-label={` ${translatr('cds.common.main', 'Optional')}`}>
              {translatr('cds.common.main', 'Optional')}
            </span>
          </span>
          <Tooltip
            message={excludeContentTooltipMsg}
            pos={'right'}
            isTranslated
            tooltipParentRole={'tooltip'}
            tabIndex="0"
            tooltipCardInlineCss={{
              'max-width': remCalc(450)
            }}
          >
            <i
              className="icon-info-circle radio-tooltip mt-5"
              aria-label={excludeContentTooltipMsg}
              role="img"
            />
          </Tooltip>
        </label>
      </div>
      <div className="exclude-content-container">
        <div className="exclude-search-checkbox">
          <Checkbox
            label={translatr('cds.common.main', 'ExcludeFromSearch')}
            checked={excludeFromSearch}
            ariaLabel={translatr('cds.common.main', 'ExcludeFromSearch')}
            onChange={() => handleChange(!excludeFromSearch, 'excludeFromSearch')}
            isTranslated
          />
        </div>
        <div className="exclude-recommendation-checkbox">
          <Checkbox
            label={excludeFromRecommendationLabel}
            checked={excludeFromRecommendation}
            ariaLabel={excludeFromRecommendationLabel}
            onChange={() => handleChange(!excludeFromRecommendation, 'excludeFromRecommendation')}
            isTranslated
          />
        </div>
      </div>
    </div>
  );
};

const mapStoreStateToProps = (state: RootState) => {
  const { currentUser, team } = state;

  return {
    todaysLearningSearchObj: team?.get('Feed')?.['feed/todaysLearningSearch'],
    allLangs: team.get('languages'),
    currentUserLang: currentUser.get('profile').get('language'),
    currentAppLang: team.get('currentAppLanguage')
  };
};

export default connect(mapStoreStateToProps)(ExcludeContentFields);
