import React, { useState, useEffect } from 'react';
import FocusLock from 'react-focus-lock';
import throttle from 'lodash/throttle';
import { string, number, func, array, object, oneOfType, bool } from 'prop-types';
import { tr } from 'edc-web-sdk/helpers/translations';
import { getUsers } from 'edc-web-sdk/requests/users.v2';
import { connect } from 'react-redux';

import { translatr } from '../../Translatr/utils';
import { SearchInput } from '../../Inputs/';
import Modal, { ModalHeader, ModalFooter, ModalContent } from '../../Modals';
import Table from '../../Table';
import Checkbox from '../../Checkbox';
import Loading from '../../Loading';
import { Tags } from '../../Tags';
import AvatarWithNameAndHandle from './AvatarWithNameAndHandle';
import { getModalConfigObj, CONFIG_TYPES_CONST } from './config';
import { useEffectAfterInitialMount } from '../../Utils/hooks';

import './AddUserTypeToObjectTypeModal.scss';

const getUpdatedCheckBoxArr = (userId, checked, data, selectedCheckBox) => {
  const checkBoxArr = [...selectedCheckBox];
  if (checked) {
    const selectedObj = data.filter(item => item.id == userId)?.[0];
    checkBoxArr.push(selectedObj);
  } else {
    const index = selectedCheckBox.findIndex(ele => ele.id == userId);
    checkBoxArr.splice(index, 1);
  }
  return checkBoxArr;
};

const isAllSelected = (updatedCheckBoxArr, data) => {
  const checkBoxIdsArr = updatedCheckBoxArr.map(ele => ele.id);
  return data.every(ele => checkBoxIdsArr.includes(ele.id));
};

const FooterErrorMsg = ({ msg }) => (
  <div className="error no-selection-error">
    <span className="icon-cross-circle" />
    {msg}
  </div>
);

FooterErrorMsg.propTypes = {
  msg: string
};

const AddUserTypeToObjectTypeModal = ({
  headerLabel,
  objType,
  userType,
  objId,
  closeHandler,
  afterAddCallback,
  excludeUsers,
  excludeAuthorId,
  maxLimitObj = { allowedLimit: 0, alreadySelected: 0 }, // Max Limit which User can select,
  contributableEntity,
  groupId,
  enableShowEMail,
  maxLen
}) => {
  const modalConfigObj = getModalConfigObj(objType, userType);
  const { search: fetchUser, resDataField, query: defaultQuery } = modalConfigObj;
  const finalQuery = contributableEntity
    ? { ...defaultQuery, contributable_entity: contributableEntity }
    : defaultQuery;
  const [dataObj, setDataObj] = useState({ loading: true, data: [], total: 0 });
  const [loadMore, setLoadMore] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [selectedCheckbox, setSelectedCheckbox] = useState([]);
  const [query, setQuery] = useState(finalQuery);

  const [showNoSelectionError, setShowNoSelectionError] = useState(false);
  const [selectionLimitExceeded, setSelectionLimitExceeded] = useState(false);

  const excludeUserIds = excludeUsers?.map(userObj => +userObj.id);

  useEffectAfterInitialMount(() => {
    // executes when user searhes
    setDataObj(obj => ({ ...obj, data: [], total: 0 }));
    showNoSelectionError && setShowNoSelectionError(false);

    fetchUserData();
  }, [query.searchTerm]);

  useEffectAfterInitialMount(() => {
    // executes when new data is received from resp
    setCheckAll(false);
  }, [dataObj.data]);

  useEffectAfterInitialMount(() => {
    // executes when want to load more
    if (query.offset > 0) {
      fetchUserData(true);
    }
  }, [query.offset]);

  useEffect(() => {
    fetchUserData();
    return () => {};
  }, []);

  useEffectAfterInitialMount(() => {
    // For handling max seelection limit error
    // Proceed only if allowed limit passed
    if (!!maxLimitObj.allowedLimit) {
      if (selectedCheckbox.length > maxLimitObj.allowedLimit - maxLimitObj.alreadySelected) {
        // Show error is selected users exceed max limit
        setSelectionLimitExceeded(true);
      } else if (selectionLimitExceeded) {
        // Hide error when selected limit is less than the maxlimit
        setSelectionLimitExceeded(false);
      }
    }
  }, [selectedCheckbox]);

  const updateUsers = data => {
    let updatedUsers = data;

    if (
      objType === CONFIG_TYPES_CONST.groupObjType ||
      objType === CONFIG_TYPES_CONST.channelObjType
    ) {
      updatedUsers = data.filter(item => !excludeUserIds?.includes(+item.id));
    }
    return updatedUsers;
  };

  const fetchUserData = (isLoadMore = false) => {
    if (isLoadMore) {
      setLoadMore(true);
    }

    setDataObj(obj => ({ ...obj, loading: true }));
    const { limit, offset, searchTerm, contributable_entity } = query;

    if (objId) {
      fetchUser(objId, { limit, offset, search_term: searchTerm })
        .then(res => {
          const data = isLoadMore
            ? [...dataObj.data, ...updateUsers(res[resDataField])]
            : updateUsers(res[resDataField]);
          setDataObj(() => ({ loading: false, total: res.total, data }));
          setLoadMore(false);
        })
        .catch(err => console.error('Error in AddUserTypeToObjectTypeModal.fetchUser', err));
    } else {
      let userFields = enableShowEMail
        ? 'id,full_name,handle,picture,email'
        : 'id,full_name,handle,picture';
      let payload = {
        limit,
        offset,
        q: searchTerm,
        fields: userFields,
        ...(excludeUsers?.length && {
          'exclude_user_ids[]': excludeAuthorId
            ? [excludeAuthorId, ...excludeUserIds]
            : excludeUserIds
        }),
        ...(contributable_entity && { contributable_entity: contributable_entity })
      };

      if (
        groupId &&
        (userType === CONFIG_TYPES_CONST.admin || userType === CONFIG_TYPES_CONST.subAdmin)
      ) {
        const { ...restPayload } = payload;
        payload = {
          ...restPayload,
          exclude_team_id: groupId,
          exclude_team_role: userType
        };
      }

      getUsers(payload)
        .then(res => {
          const data = isLoadMore
            ? [...dataObj.data, ...updateUsers(res.items)]
            : updateUsers(res.items);
          setDataObj(() => ({ loading: false, total: res.totalCount, data }));
          setLoadMore(false);
        })
        .catch(err =>
          console.error('Error in AddUserTypeToObjectTypeModal.users.v2.getUsers', err)
        );
    }
  };

  const handleSearch = value => {
    const searchTerm = value;
    setQuery(queryState => ({
      ...queryState,
      offset: 0,
      searchTerm: searchTerm
    }));
  };

  const handleSelectAllCheckBox = e => {
    const { checked } = e?.target;
    const { data } = dataObj;

    if (checked) {
      setSelectedCheckbox([...selectedCheckbox, ...data]);
      setCheckAll(true);
      setShowNoSelectionError(false);
    } else {
      const selectedCheckBoxIdsArr = data.map(ele => ele.id);
      const updatedSelectedCheckBox = selectedCheckbox.filter(
        ele => !selectedCheckBoxIdsArr.includes(ele.id)
      );
      setSelectedCheckbox(updatedSelectedCheckBox);
      setCheckAll(false);
    }
  };

  const headers = [
    {
      className: 'table-header',
      children: (
        <Checkbox
          label={translatr('cds.common.main', 'Name')}
          name="Name"
          value=""
          checked={checkAll}
          onChange={e => {
            handleSelectAllCheckBox(e);
          }}
          isTranslated
        />
      ),
      id: 'name',
      align: 'text-left'
    }
  ];

  const onCheckBoxChange = e => {
    const { checked, id: userId } = e.target;
    const { data } = dataObj;

    const updatedCheckBoxArr = getUpdatedCheckBoxArr(userId, checked, data, selectedCheckbox);

    showNoSelectionError && setShowNoSelectionError(false);

    // Toggle checkAll selectBox
    if (checked) {
      const allSelected = isAllSelected(updatedCheckBoxArr, data);
      setCheckAll(allSelected);
    } else {
      setCheckAll(false);
    }

    setSelectedCheckbox(() => [...updatedCheckBoxArr]);
  };

  const handleScroll = e => {
    e.persist();
    throttledScrolledHandler(e);
  };

  const throttledScrolledHandler = throttle(e => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;

    if (
      !loadMore &&
      dataObj.total > dataObj.data.length &&
      scrollTop + clientHeight === scrollHeight
    ) {
      setQuery(queryState => ({ ...queryState, offset: queryState.offset + queryState.limit }));
    }
  }, 500);

  const onAddBtnClicked = () => {
    if (selectedCheckbox.length) {
      if (!!maxLimitObj.allowedLimit && selectionLimitExceeded) return; // return if selectionLimitError is present.

      afterAddCallback(selectedCheckbox, userType);
      closeHandler();
    } else {
      setShowNoSelectionError(true);
    }
  };

  const onTagClicked = id => {
    const allSelectedCheckMarks = selectedCheckbox.filter(ele => ele.id !== id);
    checkAll && setCheckAll(false);
    setSelectedCheckbox(() => allSelectedCheckMarks);
  };

  const getRowsForTable = data => {
    return data.map(obj => {
      const { id, picture, fullName, handle, email } = obj;

      return [
        {
          children: (
            <>
              <Checkbox
                value={handle}
                id={`${id}`}
                checked={selectedCheckbox.some(ele => ele.id === id) || checkAll || false}
                onChange={onCheckBoxChange}
                ariaLabel={fullName}
              />
              <AvatarWithNameAndHandle
                id={id}
                img={picture}
                name={fullName}
                handle={handle}
                email={email}
              />
            </>
          )
        }
      ];
    });
  };

  const { data, loading } = dataObj;
  const { searchTerm } = query;

  const rows = getRowsForTable(data);
  const selectedRowsCount = selectedCheckbox.length || 0;

  return (
    <div className="add-user-modal-wrapper">
      <Modal size="small">
        <div className="card-stats-modal">
          <FocusLock>
            <ModalHeader title={headerLabel} onClose={closeHandler} />
            <ModalContent>
              <div className="search-container">
                <SearchInput
                  value={searchTerm}
                  placeholder={translatr('cds.common.main', 'SearchUsers')}
                  onSearch={handleSearch}
                  maxLen={maxLen}
                />
              </div>

              {!Boolean(data.length) && loading ? (
                <div className="justflex align-items-center justify-center loader-wrapper">
                  <Loading />
                </div>
              ) : !Boolean(data.length) ? (
                <p className="no-results-found loader-wrapper text-center">
                  {translatr('cds.common.main', 'NoResultsFound')}
                </p>
              ) : (
                <>
                  <div className="add-user-table-wrapper" onScroll={handleScroll}>
                    <Table className="add-user-table" headers={headers} rows={rows} />
                  </div>
                  {loading && <Loading />}
                </>
              )}

              {/* Show number of users selected */}
              <div aria-live="polite" className="selected-row-count">
                {translatr('cds.common.main', 'UserSelectedCount', {
                  selectedUserCount: selectedRowsCount
                })}
              </div>

              {Boolean(selectedCheckbox.length) &&
                selectedCheckbox.map(ele => {
                  return <Tags id={ele.id} name={ele.handle} cb={onTagClicked} isTranslated />;
                })}
            </ModalContent>
            <ModalFooter>
              {showNoSelectionError && (
                <FooterErrorMsg
                  msg={translatr(
                    'cds.common.main',
                    'NoIndividualsSelectedPleaseSelectAtLeastOneIndividualFirst'
                  )}
                />
              )}

              {selectionLimitExceeded && (
                <FooterErrorMsg
                  msg={translatr('cds.common.main', 'MaxUserTypeLimitSelectionExceed', {
                    maxLimitSelection: maxLimitObj.allowedLimit,
                    userType: userType
                  })}
                />
              )}

              <div className="justflex justify-center">
                <button className="ed-btn ed-btn-neutral" onClick={() => closeHandler()}>
                  {translatr('cds.common.main', 'Cancel')}
                </button>
                <button
                  className="ed-btn ed-btn-primary"
                  onClick={onAddBtnClicked}
                  disabled={selectionLimitExceeded}
                >
                  {translatr('cds.common.main', 'Add')}
                </button>
              </div>
            </ModalFooter>
          </FocusLock>
        </div>
      </Modal>
    </div>
  );
};

const mapStateToProps = ({ team }) => ({
  enableShowEMail: team.get('config')?.enable_show_email
});

AddUserTypeToObjectTypeModal.propTypes = {
  headerLabel: string,
  objType: string,
  userType: string,
  objId: number,
  closeHandler: func,
  afterAddCallback: func,
  contributableEntity: string,
  excludeUsers: array,
  excludeAuthorId: number,
  maxLimitObj: object,
  groupId: oneOfType([string, number]),
  enableShowEMail: bool,
  maxLen: number
};

export default connect(mapStateToProps)(AddUserTypeToObjectTypeModal);
