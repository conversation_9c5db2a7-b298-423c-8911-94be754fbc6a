import React, { useState, useEffect } from 'react';
import { string, array, func, bool, number, shape, object, arrayOf } from 'prop-types';
import { tr } from 'edc-web-sdk/helpers/translations';
import { CKEditor } from 'ckeditor4-react';
import { connect } from 'react-redux';
import classNames from 'classnames';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';

import './ckeditor.scss';

import { translatr } from '../Translatr';
import { CKEDITOR_URL } from './ckeditorUtils/loadCkeditorScript';
import { CKEditorPackageLoader } from './ckeditorUtils/ckEditorPackageLoader';
import { onBeforeLoadHandler } from './ckeditorUtils/onBeforeLoadHandler';
import { isRtl } from '../Utils/rtl';
import { EMPTY_FEILD_ERROR_MSG } from '../messageConstants';
import { isCKeditorMessageEmpty } from '../Utils';
import stripHtml from './ckeditorUtils/striphtml';
import GenAiAssistant from '../GenAiAssistant';
import {
  REMOVE_EMPTY_LINES_REGEX,
  REMOVE_NEWLINE_CHARS_REGEX,
  REMOVE_NEWLINE_P_TAG_REGEX,
  REMOVE_WHITESPACE_REGEX
} from './ckeditorUtils/regexConstants';
import { initMentions, triggerMentionsCheck } from './ckeditorUtils/mentionChecker';
import { registerTagInsertPlugin } from './ckeditorUtils/tagsInsertPlugin';

const inlineEditorToolbar = [
  { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
  { name: 'paragraph', items: ['NumberedList', 'BulletedList'] },
  { name: 'links', items: ['Link'] }
];

/**********************************Note******************************************/
/**

When you add or remove any tool from ckeditor toolbar Please ensure responsiveness of CKEditor's placeholder
If You found any irresponsive behaviour in CKeditor's placeholder postion then to fix it please refer this:
https://github.com/edcast/centralized-design-system/pull/1834/files#diff-1b72a307435d8b1d3232d3b04f1741bf2d25862ff1cada55c96cd6dbeb9b713dR65-R73

**/

const classicEditorToolbar = [
  { name: 'document', items: ['Source', '-', 'Preview', 'Print', '-', 'Templates'] },
  { name: 'clipboard', items: ['Cut', 'Copy', '-', 'Undo', 'Redo'] },
  { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll'] },
  '/',
  {
    name: 'basicstyles',
    items: [
      'Bold',
      'Italic',
      'Underline',
      'Strike',
      'Subscript',
      'Superscript',
      '-',
      'CopyFormatting',
      'RemoveFormat'
    ]
  },
  {
    name: 'paragraph',
    items: [
      'NumberedList',
      'BulletedList',
      '-',
      'Outdent',
      'Indent',
      '-',
      'Blockquote',
      'CreateDiv',
      '-',
      'JustifyLeft',
      'JustifyCenter',
      'JustifyRight',
      'JustifyBlock'
    ]
  },
  { name: 'links', items: ['Link', 'Unlink'] },
  {
    name: 'insert',
    items: [
      'Image',
      'Embed',
      'Table',
      'HorizontalRule',
      'EmojiPanel',
      'SpecialChar',
      'PageBreak',
      'Iframe'
    ]
  },
  '/',
  { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
  { name: 'colors', items: ['TextColor', 'BGColor'] },
  { name: 'tools', items: ['Maximize', 'ShowBlocks'] },
  { name: 'about', items: ['About'] }
];

const defaultEditorConfig = {
  toolbar: classicEditorToolbar,
  disableNativeSpellChecker: false, //Hide spell checker and use native browser spellChecker
  linkShowTargetTab: false, //To hide Target tab from link modal
  linkDefaultProtocol: 'https://',
  filebrowserImageBrowseUrl: 'javascript:void(0)',
  embed_provider: '//ckeditor.iframe.ly/api/oembed?url={url}&callback={callback}&consent=0',
  format_tags: 'p;h1;h2;h3', //To show tags inside Paragraph format,
  disallowedContent: 'script; *[on*]',
  extraAllowedContent: '*{*};div;iframe;*[src,title,width,height,scrolling,allowfullscreen]',
  contentsLangDirection: isRtl ? 'rtl' : 'ltr',
  image2_prefillDimensions: true,
  on: {
    instanceReady: function(evt) {
      const editor = evt.editor;

      // Register custom context for image widgets on the fly.
      editor.balloonToolbars.create({
        buttons: 'JustifyLeft,JustifyCenter,JustifyRight',
        widgets: 'image'
      });
    }
  },
  protectedSource: []
};

const removeUnwantedToolbarItems = (toolbar, toolbarItemsToRemove) =>
  toolbar.map(bar =>
    bar.name
      ? { name: bar.name, items: bar.items.filter(item => !toolbarItemsToRemove.includes(item)) }
      : bar
  );

// Add comma separate extra plugin to the config
defaultEditorConfig.extraPlugins = 'tableresizerowandcolumn,image2,balloontoolbar';
defaultEditorConfig.protectedSource.push(/\n/g);
const defaultEditorStyles = {
  borderRadius: '6px',
  overflow: 'hidden'
};

// < ********* CKEditor Main component ************* >
const Editor = ({
  error,
  defaultValue = '',
  placeholder = '',
  name = 'ck-editor',
  required,
  optional,
  title,
  description,
  editorType = 'classic',
  setValue = () => {},
  descImgUploadCB = () => {},
  fileStackSources,
  defaultAllowedImageType,
  onChangeCKEditorMode,
  checkForMaxChar = false,
  maxLen,
  isTranslated = false,
  genAiAssistantProps = null,
  allowStyleTags = false,
  mentions = null,
  dispatch,
  uploadParams,
  removeToolbarItems,
  onClick,
  tagOptions = null
}) => {
  const [inputText, setInputText] = useState(defaultValue);
  const [inputError, setError] = useState(error);
  const [ckeditorMode, setCkeditorMode] = useState('wysiwyg');
  const [placeholderTxt, setPlaceholderTxt] = useState(defaultValue ? '' : placeholder);
  const [plainText, setPlainText] = useState('');
  const [isCkEditorMaximized, setIsCkEditorMaximized] = useState(false);

  const getCharsLeftMsg = text => {
    if (checkForMaxChar) {
      const difference = maxLen - text.length;
      const remainingText = difference > 0 ? difference : 0;
      return translatr('cds.common.main', 'RemainingtextmaxlenLeft', { remainingText, maxLen });
    }
  };

  const [maxInput, setMaxInput] = useState(getCharsLeftMsg(plainText));

  useEffect(() => {
    if (checkForMaxChar) {
      setInputText(defaultValue);
      setMaxInput(getCharsLeftMsg(plainText));
      if (plainText.length > maxLen) {
        setLimitError(true);
      }
    }
    if (required && error) {
      setError(isCKeditorMessageEmpty(defaultValue) ? EMPTY_FEILD_ERROR_MSG() : '');
    }
  }, [defaultValue, plainText]);

  useEffect(() => {
    setError(error);
  }, [error]);

  useEffect(() => {
    supportErrorClassForFocus();
  }, [inputError]);

  const supportErrorClassForFocus = () => {
    const ckeditor = document.getElementById(name);
    if (ckeditor) {
      if (error) {
        ckeditor.classList.add('input-error-border');
      } else {
        ckeditor.classList.remove('input-error-border');
      }
    }
  };

  const setLimitError = (err = false) => {
    setError(err ? translatr('cds.common.main', 'MaximumCharacterLimitReached') : '');
  };

  const editorConfig = {
    ...defaultEditorConfig,
    ...(removeToolbarItems
      ? { toolbar: removeUnwantedToolbarItems(defaultEditorConfig.toolbar, removeToolbarItems) }
      : {}),
    ...(allowStyleTags && {
      extraAllowedContent: defaultEditorConfig.extraAllowedContent + ';style'
    }),
    ...(editorType === 'inline' && {
      toolbar: inlineEditorToolbar
    })
  };

  const extraPlugins = ['tableresizerowandcolumn', 'image2', 'balloontoolbar'];
  if (tagOptions && tagOptions.length > 0) {
    extraPlugins.push('taginsert');
  }
  editorConfig.extraPlugins = extraPlugins.join(',');

  if (editorType === 'classic') {
    const insertToolbarItems = [
      'Image',
      'Embed',
      'Table',
      'HorizontalRule',
      'EmojiPanel',
      'SpecialChar',
      'PageBreak',
      'Iframe'
    ];
    if (tagOptions && tagOptions.length > 0) {
      insertToolbarItems.push('TagInsert');
    }

    const insertToolbarIndex = editorConfig.toolbar.findIndex(
      item => typeof item === 'object' && item.name === 'insert'
    );

    if (insertToolbarIndex !== -1) {
      editorConfig.toolbar[insertToolbarIndex] = {
        name: 'insert',
        items: insertToolbarItems
      };
    }
  }

  const editorStyles = {
    ...defaultEditorStyles,
    ...(editorType === 'inline' && {
      minHeight: '100px',
      padding: '0.35rem .75rem',
      border: `0.063rem solid var(${inputError ? '--ed-negative-1' : '--ed-border-color'})`
    }),
    ...(editorType === 'classic' &&
      genAiAssistantProps?.showGenAiAssistant && {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0
      }),
    ...(editorType === 'inline' &&
      genAiAssistantProps?.showGenAiAssistant && {
        minHeight: '9.188rem',
        paddingBottom: '3.125rem'
      })
  };

  const removeBackSlashes = inputString => inputString?.replace(REMOVE_NEWLINE_CHARS_REGEX, '');

  const handleChange = event => {
    const data = event.editor.getData();
    changePlaceHolder(event);
    setInputText(removeBackSlashes(data));
    setValue(removeBackSlashes(data));

    if (checkForMaxChar) {
      const strippedText = stripHtml(data);
      setPlainText(strippedText);
      setMaxInput(getCharsLeftMsg(strippedText));
      setLimitError(strippedText.length > maxLen);
    }
  };

  const changePlaceHolder = event => {
    let data = event.editor.getData();
    if (!!data) {
      setPlaceholderTxt('');
    } else {
      setPlaceholderTxt(placeholder);
    }
  };

  const modalHandle = event => {
    if (isRtl) {
      const modal = event.data._.element;
      modal?.setAttribute('dir', 'rtl');
      modal?.setAttribute('lang', 'ar');
    }
  };

  const onLoad = event => {
    if (isRtl) {
      const container = event.editor.container;
      container?.setAttribute('dir', 'rtl');
      container?.setAttribute('lang', 'ar');
    }
  };

  const handleClick = event => {
    if (isRtl) {
      const panel = document.querySelector('.cke_panel');
      const panelFrame = document.querySelector('.cke_panel_frame');
      const panelContainer = panelFrame?.contentWindow.document.querySelector(
        '.cke_panel_container'
      );
      [panel, panelContainer].forEach(item => {
        item?.setAttribute('dir', 'rtl');
        item?.setAttribute('lang', 'ar');
      });
    }
    // Removing newline characters from the string when the classname includes showblocks
    // because applying the show block feature adds a wrapper element with a class name containing showblocks.
    if (event?.target.className.includes('showblocks')) {
      setInputText(removeBackSlashes(inputText));
      setValue(removeBackSlashes(inputText));
    }
    onClick();
  };

  useEffect(() => {
    // Handle edit case on mount
    if (checkForMaxChar) {
      const plainData = stripHtml(defaultValue);
      setMaxInput(getCharsLeftMsg(plainData));
    }
    return () => {
      // Destroy the instance so we can recreate it later if needed
      window.CKEDITOR?.instances?.[name]?.destroy?.();
    };
  }, []);

  useEffect(() => {
    const sourceBtn = document.querySelector('.cke_button__source_label');
    const srcContent = document.querySelector('.cke_source');
    let srcData = window.CKEDITOR?.instances?.[name]?.getData?.();

    if (srcData && srcContent !== null) {
      srcData = srcData.replace(REMOVE_NEWLINE_P_TAG_REGEX, '<p>');
      srcData = srcData.replace(REMOVE_EMPTY_LINES_REGEX, '');
      document.querySelector('.cke_source').value = srcData;
    }
    if (sourceBtn) {
      sourceBtn.innerText = ckeditorMode === 'source' ? 'Preview' : 'Source';
    }
    onChangeCKEditorMode?.(ckeditorMode);
  }, [ckeditorMode]);

  useEffect(() => {
    if (editorType === 'inline') {
      const handleScroll = () => {
        window.CKEDITOR.document.getWindow().fire('scroll');
      };
      const parentElement = document.querySelector('.ed-dialog-modal');

      if (parentElement) {
        parentElement.addEventListener('scroll', handleScroll);
      }

      return () => {
        if (parentElement) {
          parentElement.removeEventListener('scroll', handleScroll);
        }
      };
    }
  }, []);

  return (
    <div
      className={classNames('ed-input-container', {
        classic__ck__editor: editorType === 'classic',
        ck__editor_maximized: isCkEditorMaximized
      })}
      onClick={handleClick}
      role="presentation"
      onKeyDown={e => {
        if (e.key === 'Enter') {
          handleClick();
        }
      }}
    >
      {title && (
        <label
          htmlFor={title?.replace(REMOVE_WHITESPACE_REGEX, '')?.toLowerCase()}
          className="ed-input-title"
        >
          {isTranslated ? title : tr(title)}
          {required && <span className="asterisk">*</span>}
          {required && <input type="hidden" name={name} required value={inputText} />}
          {optional && <span className="asterisk">{translatr('cds.common.main', 'Optional')}</span>}
        </label>
      )}
      {description && (
        <label className="input-info supporting-text">
          {typeof description === 'object'
            ? tr(description.msg, description.obj)
            : isTranslated
            ? description
            : tr(description)}
        </label>
      )}
      {placeholderTxt && (
        <div
          className={classNames('ckeditor-placeholder', {
            inline: editorType === 'inline',
            'placeholder-postion': !!description
          })}
        >
          {typeof placeholderTxt === 'object'
            ? tr(placeholderTxt.msg, placeholderTxt.obj)
            : isTranslated
            ? placeholderTxt
            : tr(placeholderTxt)}
        </div>
      )}
      <CKEditor
        initData={defaultValue}
        name={name}
        config={editorConfig}
        style={editorStyles}
        onChange={handleChange}
        onKey={changePlaceHolder}
        onMode={e => setCkeditorMode(e.editor.mode)}
        type={editorType}
        editorUrl={CKEDITOR_URL}
        onDialogShow={modalHandle}
        onLoaded={onLoad}
        onMaximize={() => setIsCkEditorMaximized(prev => !prev)}
        onBeforeSetMode={CKEDITOR => {
          // When switching from source to wysiwyg mode, check and remove data-cke-filter attribute
          // This attribute bypasses Advanced Content Filter (ACF) and allows script execution
          // Removing it maintains security by keeping content filtering active
          // Also, remove any xss code using safeRender function
          if (CKEDITOR.editor.mode === 'source' && CKEDITOR.data === 'wysiwyg') {
            let content = CKEDITOR.editor.getData();
            if (content.includes('data-cke-filter="off"')) {
              content = content.replace(/data-cke-filter="off"/g, '');
            }
            CKEDITOR.editor.setData(safeRender(content));
          }
        }}
        onBeforeLoad={CKEDITOR => {
          registerTagInsertPlugin(CKEDITOR, tagOptions);
          CKEDITOR.on('dialogDefinition', evt => {
            onBeforeLoadHandler({
              evt,
              CKEDITOR,
              isClassicEditor: editorType === 'classic',
              fileStackSources,
              acceptFileTypes: defaultAllowedImageType,
              descImgUploadCB,
              dispatch,
              uploadParams
            });
          });
        }}
        onFocus={({ editor }) => {
          if (mentions) {
            triggerMentionsCheck(mentions, editor);
          }
        }}
        onInstanceReady={CKEDITOR => {
          if (mentions) {
            initMentions(mentions, CKEDITOR.editor);
            CKEDITOR.editor.editable().on('click', () => {
              triggerMentionsCheck(mentions, CKEDITOR.editor);
            });
          }

          // Add aria-required attribute to the editor element
          CKEDITOR.editor.element.$.setAttribute('aria-required', required);

          // Also set it on the outer editor container for better accessibility
          CKEDITOR.editor.editable().$.setAttribute('aria-required', required);
        }}
      />

      {!!genAiAssistantProps?.showGenAiAssistant && (
        <GenAiAssistant
          additionalWrapperClasses={classNames({
            'width-100 xs-padding-bottom': editorType === 'classic',
            'm-padding-bottom': isCkEditorMaximized
          })}
          entity={genAiAssistantProps.entity || 'description'}
          placeAssistantAtBottom={editorType !== 'classic'}
          entitySetter={newVal => {
            /**
             * When appending AI-generated content to CKEditor, we add the 'isAiGenerated' flag to the instance, which is removed after a short timeout.
             * This workaround is necessary because updating AI-generated responses via `window.CKEDITOR?.instances[name]?.setData(newVal)`
             * triggers onChange handler. Since the same handler detects manual user input changes,
             * AI-generated responses are incorrectly detected as manual changes.
             */
            window.CKEDITOR.instances[name].isGenAiGenerated = true;
            window.CKEDITOR?.instances[name]?.setData(newVal);

            setTimeout(() => {
              delete window.CKEDITOR.instances[name].isGenAiGenerated;
            }, 100);
          }}
          langId={genAiAssistantProps.langId}
        />
      )}

      {checkForMaxChar && (
        <label aria-live="polite" className="supporting-text characters-limit-msg">
          {maxInput}
        </label>
      )}
      {inputError && (
        <label className="input-error">{isTranslated ? inputError : tr(inputError)}</label>
      )}
    </div>
  );
};

Editor.propTypes = {
  error: string,
  defaultValue: string,
  placeholder: string,
  name: string,
  required: bool,
  optional: bool,
  title: string,
  description: string,
  editorType: string,
  setValue: func,
  descImgUploadCB: func,
  fileStackSources: array,
  defaultAllowedImageType: array,
  onChangeCKEditorMode: func,
  checkForMaxChar: bool,
  maxLen: number,
  isTranslated: bool,
  genAiAssistantProps: shape({
    showGenAiAssistant: bool,
    langId: string,
    entity: string
  }),
  allowStyleTags: bool,
  mentions: shape({
    onMatched: func,
    onUnmatched: func
  }),
  uploadParams: object,
  removeToolbarItems: arrayOf(string),
  onClick: func,
  tagOptions: array
};

const mapStateToProps = ({ team }) => ({
  defaultAllowedImageType: team?.get('allowedMediaMimeTypes')?.image,
  fileStackSources: team?.get('fileStackSources')
});

export default connect(mapStateToProps)(CKEditorPackageLoader(Editor));
