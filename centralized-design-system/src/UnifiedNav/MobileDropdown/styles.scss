@import '../../Styles/_variables';

.unified-nav__mobile-dropdown {
  margin-right: var(--ed-spacing-base);

  .dropdown-btn {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  label {
    display: flex;
    cursor: pointer;
  }

  &-label {
    margin-right: rem-calc(5);
  }

  .unified-nav__mobile-dropdown-wrapper {
    position: relative;
  }

  .unified-nav__mobile-dropdown-content {
    position: absolute;
    right: 0;
    white-space: nowrap;
    box-shadow: var(--ed-shadow-md);
    background: var(--ed-white);
    border-radius: var(--ed-border-radius-lg);
    font-size: var(--ed-font-size-supporting);
    z-index: 100;
    padding: rem-calc(20) rem-calc(24);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    gap: rem-calc(12);
    width: fit-content;

    .level-one-container,
    .level-two-container {
      width: rem-calc(298);
    }

    // Specifically for very small viewports
    @media screen and (max-width: 400px) {
      right: -40px; // Remove spacing from header
      width: calc(100vw - 20px);
      margin: 10px;

      .level-one-container,
      .level-two-container {
        max-width: 100%;
      }
    }

    .level-one-content,
    .level-two-content {
      display: grid;
      gap: rem-calc(10);
      .menu__item > a,
      button {
        color: var(--ed-text-color-primary) !important;
        font-size: 15px !important;
        font-weight: 700 !important;
        line-height: 16px !important;
        padding: 0px !important;
      }
    }

    .back-container {
      height: rem-calc(56);
      padding: rem-calc(16) 0;
      border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
      .back-btn {
        color: var(--ed-text-color-primary) !important;
        .left-arrow {
          display: inline-block;
          font-size: var(--ed-font-size-xl);
          width: rem-calc(24);
          height: rem-calc(24);
          text-align: left;
        }
        .back-text {
          font-family: var(--ed-font-family-base);
          font-size: 16px;
          font-weight: 700;
          line-height: 24px;
          text-align: left;
          vertical-align: bottom;
        }
      }
    }

    ul {
      text-indent: 0;
      margin: 0;
    }
  }

  li {
    margin: 0 !important;
    a {
      padding: 0 !important;
    }
    button {
      padding: 0 !important;
    }
  }
}
