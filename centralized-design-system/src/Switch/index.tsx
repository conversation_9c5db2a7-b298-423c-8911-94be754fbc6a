import React, { useState, useEffect, forwardRef } from 'react';
import './styles.scss';

export interface SwitchProps {
  name?: string;
  className?: string;
  disabled?: boolean;
  defaultChecked?: boolean;
  onChange?: (event: { target: { checked: boolean; }}) => void;
  isLabelHidden?: boolean;
  ariaHidden?: boolean;
  ariaLabel?: string;
  role?: string;
  ariaChecked?: boolean;
}

const Switch = forwardRef<HTMLButtonElement, SwitchProps>(({
  name,
  className = '',
  disabled,
  defaultChecked,
  onChange = () => {},
  isLabelHidden = false,
  ariaHidden = true,
  ariaLabel,
  role = "switch",
  ariaChecked
  }, ref) => {
  const [isChecked, setIsChecked] = useState(defaultChecked ? true : false);

  useEffect(() => {
    setIsChecked(defaultChecked);
  }, [defaultChecked]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled) {
      e.preventDefault();
      let event = { target: { checked: !isChecked } };
      setIsChecked(!isChecked);
      onChange(event);
    }
  };

  const handleKeyPress = e => {
    if ((e.key === 'Space' || e.key === ' ' || e.key === 'Enter') && !disabled) {
      e.preventDefault();
      let event = { target: { checked: !isChecked } };
      setIsChecked(!isChecked);
      onChange(event);
    }
  };

  return (
    <button
      className={`switch cursor-pointer ${className}`}
      ref={ref}
      onClick={handleClick}
      onKeyDown={handleKeyPress}
      aria-label={ariaLabel || name}
      aria-checked={ariaChecked || isChecked}
      role={role}
    >
      <input
        type="checkbox"
        name={name}
        id={name}
        aria-hidden={ariaHidden}
        onChange={onChange}
        checked={isChecked}
        disabled={!!disabled}
      />
      <span>{isLabelHidden ? '' : name}</span>
    </button>
  );
});

export default Switch;