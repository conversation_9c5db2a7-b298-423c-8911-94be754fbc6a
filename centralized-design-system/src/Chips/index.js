import React, { useState } from 'react';
import { func, array, bool, string } from 'prop-types';

import './style.scss';

const Chips = ({
  chips = [],
  isMulti = false,
  defaultSelectedChips = [0],
  onChipClick,
  classNames = ''
}) => {
  const [selectedChips, setSelectedChips] = useState(defaultSelectedChips);

  const handleChipClick = index => {
    let updatedSelection;
    if (isMulti) {
      const isSelected = selectedChips.includes(index);
      if (isSelected) {
        updatedSelection = selectedChips.filter(i => i !== index);
      } else {
        updatedSelection = [...selectedChips, index];
      }
    } else {
      updatedSelection = [index];
    }

    setSelectedChips(updatedSelection);
    onChipClick(updatedSelection);
  };

  return (
    <div className="chip-wrapper m-margin-ends">
      <div className="chip-bar" role="tablist" aria-busy="true">
        {chips.map(({ icon, label }, index) => (
          <button
            className={`chip nav-link pointer s-margin-right s-padding-ends m-padding-sides ${
              selectedChips.includes(index) ? 'active' : ''
            } ${classNames}`}
            onClick={() => handleChipClick(index)}
            role="tab"
            aria-controls="assign_content_panel"
            aria-selected={selectedChips.includes(index)}
          >
            {icon && <i aria-hidden className={icon} />}
            {label}
          </button>
        ))}
      </div>
    </div>
  );
};

Chips.propTypes = {
  chips: array.isRequired,
  isMulti: bool,
  defaultSelectedChips: array,
  onChipClick: func,
  classNames: string
};

export default Chips;
