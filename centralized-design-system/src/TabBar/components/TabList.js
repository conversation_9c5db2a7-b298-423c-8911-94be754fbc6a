import { useRef } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import unescape from 'lodash/unescape';
import { tr } from 'edc-web-sdk/helpers/translations';
import { NavLink } from 'react-router-dom';

import { translatr } from '../../Translatr';
import Carousel from '../../Carousel';

const Tab = ({
  tabs,
  activeTab,
  handleAriaSelected,
  tabRefs,
  useTablistAriaRoles,
  activeLink,
  activeRoutes,
  handleFocusNavigation
}) => {
  return tabs.map((tab, index) => {
    const handleClick = e => {
      if (tab?.disabled) {
        e.preventDefault();
        e.stopPropagation();
      }
    };
    return (
      <li key={`tabs-${index}`} className="tab" role="presentation">
        <NavLink
          onKeyDown={handleFocusNavigation}
          className={classNames('p-sides-10', {
            active:
              (tab.link?.includes(activeLink) && index === 0) ||
              activeRoutes?.some(
                route => location.pathname.includes(route) && tab.link?.includes(route)
              ),
            disabled: tab.disabled
          })}
          onClick={handleClick}
          role={useTablistAriaRoles ? 'tab' : undefined}
          aria-selected={
            useTablistAriaRoles
              ? handleAriaSelected
                ? handleAriaSelected(index)
                : index === activeTab
              : undefined
          }
          // The aria-controls attribute connects each tab to its corresponding panel
          // by matching the panel's ID (results-section-${activeTab}-section).
          aria-controls={`results-section-${tab?.key}-section`}
          ref={el => (tabRefs.current[index] = el)}
          disabled={!!tab.disabled}
          tabIndex={tab.disabled ? -1 : 0}
          aria-disabled={tab.disabled || undefined}
          to={tab?.link}
          end={true}
        >
          {unescape(tab.label) || tr(unescape(tab.name))}
        </NavLink>
      </li>
    );
  });
};

const MoreMenu = ({ extraTabs, menuRef, showMoreOptions, handleFocusNavigation }) => {
  return (
    <li key={`menu-button`} className="tab">
      <button
        id="dropdown-menu-button"
        className={`${
          extraTabs.some(tab => tab.link === window.location.pathname) ? 'active' : ''
        }`}
        ref={menuRef}
        onKeyDown={e =>
          e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown'
            ? showMoreOptions(e)
            : handleFocusNavigation(e)
        }
        onClick={showMoreOptions}
        tabIndex={0}
        role="tab"
      >
        {translatr('cds.common.main', 'More')} <i className="icon-angle-down-arrow" />
      </button>
    </li>
  );
};

const TabList = ({
  tabs,
  activeTab,
  handleAriaSelected,
  tabRefs,
  menuRef,
  showMoreOptions,
  handleFocusNavigation,
  useCarousel,
  extraTabs,
  useTablistAriaRoles,
  carouselAriaLabel,
  activeLink,
  activeRoutes
}) => {
  const tabRef = useRef(null);

  const classes = classNames('tabs', {
    carousel: useCarousel
  });

  if (useCarousel) {
    return (
      <ul className={classes} ref={tabRef} role={useTablistAriaRoles ? 'tablist' : undefined}>
        <Carousel addScroll={-100} ariaLabel={carouselAriaLabel}>
          <Tab
            tabs={tabs}
            activeTab={activeTab}
            handleAriaSelected={handleAriaSelected}
            tabRefs={tabRefs}
            activeLink={activeLink}
            activeRoutes={activeRoutes}
            handleFocusNavigation={handleFocusNavigation}
            useTablistAriaRoles={useTablistAriaRoles}
          />
        </Carousel>
      </ul>
    );
  }
  return (
    <ul className={classes} ref={tabRef} role={useTablistAriaRoles ? 'tablist' : undefined}>
      <Tab
        tabs={tabs}
        activeTab={activeTab}
        handleAriaSelected={handleAriaSelected}
        tabRefs={tabRefs}
        activeLink={activeLink}
        activeRoutes={activeRoutes}
        handleFocusNavigation={handleFocusNavigation}
        useTablistAriaRoles={useTablistAriaRoles}
      />
      {extraTabs?.length > 0 && (
        <MoreMenu
          extraTabs={extraTabs}
          menuRef={menuRef}
          showMoreOptions={showMoreOptions}
          handleFocusNavigation={handleFocusNavigation}
        />
      )}
    </ul>
  );
};

Tab.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      name: PropTypes.string,
      disabled: PropTypes.bool,
      link: PropTypes.string
    })
  ).isRequired,
  activeTab: PropTypes.number.isRequired,
  handleAriaSelected: PropTypes.func,
  tabRefs: PropTypes.shape({
    current: PropTypes.arrayOf(PropTypes.object)
  }).isRequired,
  useTablistAriaRoles: PropTypes.bool,
  activeLink: PropTypes.string,
  activeRoutes: PropTypes.arrayOf(PropTypes.string),
  handleFocusNavigation: PropTypes.func.isRequired
};

MoreMenu.propTypes = {
  extraTabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      name: PropTypes.string,
      disabled: PropTypes.bool,
      link: PropTypes.string
    })
  ).isRequired,
  menuRef: PropTypes.shape({
    current: PropTypes.object
  }).isRequired,
  showMoreOptions: PropTypes.func.isRequired,
  handleFocusNavigation: PropTypes.func.isRequired
};

TabList.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      name: PropTypes.string,
      disabled: PropTypes.bool,
      link: PropTypes.string
    })
  ).isRequired,
  activeTab: PropTypes.number.isRequired,
  handleAriaSelected: PropTypes.func,
  tabRefs: PropTypes.shape({
    current: PropTypes.arrayOf(PropTypes.object)
  }).isRequired,
  menuRef: PropTypes.shape({
    current: PropTypes.object
  }).isRequired,
  showMoreOptions: PropTypes.func.isRequired,
  handleFocusNavigation: PropTypes.func.isRequired,
  useCarousel: PropTypes.bool,
  extraTabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      name: PropTypes.string,
      disabled: PropTypes.bool,
      link: PropTypes.string
    })
  ),
  useTablistAriaRoles: PropTypes.bool,
  carouselAriaLabel: PropTypes.string,
  activeLink: PropTypes.string,
  activeRoutes: PropTypes.arrayOf(PropTypes.string)
};

export default TabList;
