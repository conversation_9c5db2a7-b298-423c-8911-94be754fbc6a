@import '../Styles/_variables';

.ed-ui {
  .tab-wrapper {
    position: relative;
  }

  .tab-bar.block {
    background-size: cover;
    display: flex;
    align-items: center;
    padding: 0 var(--ed-tab-margin-x);
    box-shadow: var(--ed-tab-wrapper-shadow);
    border-radius: var(--ed-tab-wrapper-border-radius);
    background-color: var(--ed-tab-wrapper-background-color);
    overflow-x: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: var(--ed-border-size-sm);
      background-color: var(--ed-tab-wrapper-border-color);
    }

    .tabs {
      display: flex;
      flex: 1;
      align-items: baseline;
      overflow: auto;
      z-index: 1;
      margin-left: var(--ed-tab-wrapper-margin-x);
      padding-top: 0.25rem;

      &.carousel {
        max-width: 100%;
        padding: 0;
        margin: 0;

        .ed-carousel-container {
          max-width: 100%;

          .ed-carousel {
            margin-top: var(--ed-spacing-4xs);
            margin-bottom: 0;
          }

          .scroll-btn {
            &.right {
              right: -6px;
            }

            &.left {
              left: -6px;
            }
          }
        }
      }

      &.hide {
        visibility: hidden;
      }

      .tab {
        margin: 0 var(--ed-tab-margin-x);
        white-space: nowrap;
        color: var(--ed-neutral-3);
        font-size: var(--ed-tab-font-size);
        cursor: pointer;

        a,
        button.nav-link {
          color: var(--ed-neutral-3);
          display: block;
          text-transform: var(--ed-tab-text-transform);
          padding: var(--ed-tab-padding-y) var(--ed-tab-padding-x);
          line-height: var(--ed-font-size-base);
          cursor: pointer;

          .icon-angle-down-arrow {
            font-size: rem-calc(23);
            display: inline-block !important;
            vertical-align: middle;
            margin-left: 0;
          }
        }

        .disabled {
          cursor: not-allowed !important;
          color: var(--ed-neutral-6) !important;
          &:hover {
            border-bottom: none !important;
          }
        }

        &.active a,
        a.active,
        &.active button,
        button.active {
          color: var(--ed-text-color-primary);
          border-bottom: var(--ed-tab-active-border-bottom-height) solid var(--ed-primary-base);
          font-weight: var(--ed-tab-active-font-weight);
        }

        a:not(.active):hover,
        button:not(.active):hover {
          color: var(--ed-text-color-primary);
          border-bottom: var(--ed-tab-active-border-bottom-height) solid
            var(--ed-tab-hover-underline-color);
        }

        button#dropdown-menu-button {
          padding: var(--ed-tab-padding-y) var(--ed-tab-padding-x);
        }
      }
    }

    .tab-content-sec {
      padding-bottom: var(--ed-spacing-base);
    }
  }

  /* Target only the TabBar in the search page */
  .tab-bar.search-keyboard-focus-tabs {
    .tabs {
      .tab {
        /* Add padding to ensure focus outline is fully visible for search tabs */
        padding-left: var(--ed-spacing-5xs);
      }
    }
  }

  .extra-tabs-container.block {
    position: absolute;
    top: rem-calc(40);
    right: 0;
    padding: var(--ed-spacing-lg) 0;
    z-index: 12;
    min-width: rem-calc(193);
    margin: 0;
    .more-text-opt {
      white-space: normal;
      align-items: baseline;
      word-break: break-all;
    }
    li.tab {
      a {
        color: var(--ed-text-color-primary);
        font-size: var(--ed-font-size-base);
        display: block;
        padding: var(--ed-spacing-2xs) var(--ed-spacing-base);
        transition: background-color 0.5s ease;
        position: relative;
        width: 100%;
        cursor: pointer;
        outline: none; /* Ensure no default outline */
        &:hover {
          background-color: var(--ed-input-hover-bg-color);
        }
        &:focus {
          border: 2px solid var(--ed-primary-base); /* Add blue outline on focus */
        }
        &:disabled {
          cursor: not-allowed !important;
          color: var(--ed-neutral-6) !important;
          &:hover {
            border-bottom: none !important;
          }
        }
        .icon-check {
          display: none !important;
        }
        &.active {
          .icon-check {
            color: var(--ed-state-active-color);
            display: inline-block !important;
          }
        }
      }
      a.disabled {
        cursor: not-allowed !important;
        color: var(--ed-neutral-6) !important;
        &:hover {
          border-bottom: none !important;
        }
      }
    }
  }
}
