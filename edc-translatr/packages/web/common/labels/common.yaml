prefix: common.common
labels:
  OneFilter:
      context: missing
      value: '1 filter'
  OneMonth:
      context: missing
      value: '1 Month'
  OneStarNotRelevantTo5StarsVeryRelevant:
      context: missing
      value: '1 Star (Not Relevant) to 5 Stars (Very Relevant)'
  OneYear:
      context: missing
      value: '1 Year'
  Abcexamplecom:
      context: missing
      value: <EMAIL>
  About:
      context: missing
      value: About
  Accept:
      context: missing
      value: Accept
  Accepted:
      context: missing
      value: Accepted
  Acceptraisedhand:
      context: missing
      value: acceptRaisedHand
  AcceptTermsAndConditions:
      context: missing
      value: 'Accept terms and conditions'
  AccessDenied:
      context: missing
      value: 'Access denied'
  AccessMaterials:
      context: missing
      value: 'Access Materials'
  AccountLinking:
      context: missing
      value: 'Account Linking'
  AchievedIdealSkillLevel:
      context: missing
      value: '- Achieved Ideal Skill Level'
  ACompilationOfGoiIncentiveEligibleCourseAssessmentPairsCompletedByYou:
      context: missing
      value: 'A compilation of GoI Incentive eligible course-assessment pairs completed by you.'
  Action:
      context: missing
      value: Action
  Actions:
      context: missing
      value: Actions
  ActionsForThisSmartcard:
      context: missing
      value: 'Actions for this SmartCard'
  ActionsForThisSuggestion:
      context: missing
      value: 'Actions for this suggestion'
  ActiveFor:
      context: missing
      value: 'Active for'
  ActiveTopics:
      context: missing
      value: 'Active Topics'
  ActiveUniqueUsers:
      context: missing
      value: 'Active Unique Users'
  ActiveUsers:
      context: missing
      value: 'Active Users'
  ActiveUsersByPlatform:
      context: missing
      value: 'Active Users by Platform'
  Activity:
      context: missing
      value: Activity
  Add:
      context: missing
      value: Add
  AddAComment:
      context: missing
      value: 'Add a comment'
  AddAdmin:
      context: missing
      value: 'Add Admin...'
  AddAllGroups:
      context: missing
      value: 'Add All Groups'
  LaunchScormContentMessage:
      context: Message used to launch scorm in Content Player View
      value: 'The content is open in a new window. This window must remain open while the content is running in order to track your progress. Note: If the content is not visible, then disable the pop-up blocker in your browser and relaunch the content.'
  ScormContent:
      context: Title for showing scorm (Sharable Content Object Reference Model (SCORM) is a collection of standards and specifications for web-based electronic educational technology. It is one of the different standards of content) content in Content Player View
      value: 'Scorm Content'
  ManageScormContentSize:
      context: Manage the size of scorm (Sharable Content Object Reference Model (SCORM) is a collection of standards and specifications for web-based electronic educational technology. It is one of the different standards of content) content in Content Player View
      value: 'Manage Scorm Content Size'
  PleaseSelectLessonFromTableOfContents:
      context: Message that is displayed to the user to choose some other valid lesson from table of contents, when content is not launched inside the SCORM(Sharable Content Object Reference Model) player
      value: 'Please select a lesson from the table of contents to continue.'
  PleaseCloseContentByChoosingExitOption:
      context: Message that is displayed to the user to close the content, when content is not launched inside the SCORM(Sharable Content Object Reference Model) player
      value: 'Please close the content by choosing appropriate exit option.'
  PleaseChooseExitOption:
      context: Title of the modal with the valid exit options while closing the SCORM2004(Sharable Content Object Reference Model) content player
      value: 'Please choose an exit option'
  ExitAndFinish:
      context: Label for the button used to exit and close the SCORM(Sharable Content Object Reference Model) content player
      value: 'Exit and finish'
  ExitAndResumeLater:
      context: Label for the button used to exit the SCORM(Sharable Content Object Reference Model) content player and resume the content on next player launch
      value: 'Exit and resume later'
  ExitWithoutSaving:
      context: Label for the button used to exit the SCORM(Sharable Content Object Reference Model) content player without saving the current content progress state
      value: 'Exit without saving'
  UnexpSeqErrMsg:
      context: Error message that is displayed inside content player if there an unexpected error while navigating through content
      value: 'An unexpected error has occurred while handling the sequencer navigation'
  DiscardAttemptMsg:
      context: Discard message that is displayed when there is an issue while completing the content, user can either close the content or save the previous attempted state.
      value: 'A technical issue is preventing you from continuing the course where you left off. Please click the appropriate option.'
  DiscardAttemptMsgYes:
      context: Message displayed if the user decides to discard the progress and clicks on reset button
      value: 'Your previous attempt data has been cleared. Please close this window and relaunch the course.'
  DiscardAttemptMsgNo:
      context: Message displayed if the user cancels the content discard message
      value: 'Please contact your System Administrator or Help Desk for assistance with this course.'
  FullScreenOrMaximize:
      context: Title attribute for maximum icon inside content player
      value: 'Full Screen Or Maximize'
  MinimizeScreen:
      context: Title attribute for minimize screen icon inside content player
      value: 'Minimize Screen'
  TableOfContents:
      context: Title value for table of content component inside content player
      value: 'Table of contents'
  ClosePlayer:
    context: Label for the close player button inside inline content player for mobile.
    value: 'Close Player'
  ClickCloseButtonInAppToExit:
    context: Message displayed when the content player opens inside mobile applicationa and user needs to close the player.
    value: 'Please click the Close button in the application to exit the content player.'
  GoBackToCardDetails:
      context: Title attribute for close button inside content player
      value: 'Go back to card details'
  AddAnAccountScanTheQrCodeToContinue:
      context: missing
      value: '&amp;quot;Add an account&amp;quot; Scan the QR code to continue.'
  AddAnotherQuestion:
      context: missing
      value: 'Add Another Question'
  AddAnswerChoicesAndSelectCorrectOptions:
      context: missing
      value: 'Add answer choices and select correct options'
  AddAudioThumbnail:
      context: missing
      value: 'add audio thumbnail'
  AddCard:
      context: missing
      value: 'Add Card'
  AddCarousel:
      context: missing
      value: 'ADD CAROUSEL'
  AddChannel:
      context: missing
      value: 'Add Channel'
  AddChannelToGroup:
      context: missing
      value: 'Add Channel to Group'
  AddChannelToSection:
      context: missing
      value: 'Add Channel to Section'
  AddChoices:
      context: missing
      value: '+ Add Choices'
  AddCollaborators:
      context: missing
      value: 'Add Collaborators'
  AddCollaboratorsCollaboratorsCanPostToThisChannel:
      context: missing
      value: 'Add Collaborators, Collaborators can post to this channel'
  AddCollaboratorToChannel:
      context: missing
      value: 'Add Collaborator to Channel'
  AddComment:
      context: missing
      value: 'Add comment'
  AddCommentsToDescribeYourInterest:
      context: missing
      value: 'Add comments to describe your interest'
  AddContent:
      context: missing
      value: 'Add Content'
  AddContentSelection:
      context: missing
      value: 'Add Content Selection'
  AddContentToSection:
      context: missing
      value: 'Add Content to Section'
  AddContentToYourMyLearningPlan:
      context: missing
      value: 'Add content to your My Learning Plan'
  AddCsv:
      context: missing
      value: 'Add CSV'
  AddCurator:
      context: missing
      value: 'Add Curator...'
  AddCuratorToChannel:
      context: missing
      value: 'Add Curator to Channel'
  AddDescription:
      context: missing
      value: 'Add description'
  ReporteeCannotBeManagerError:
      context: Error message displayed when attempting to add a user's reportee as their manager
      value: 'Reportee cannot be added as your manager'
  AddDueDate:
      context: missing
      value: 'Add Due Date'
  AddeditManager:
      context: missing
      value: 'Add/Edit Manager'
  AddedToPlan:
      context: missing
      value: 'Added to Plan'
  AddFilter:
      context: missing
      value: 'Add filter'
  AddGroupAdmin:
      context: missing
      value: 'Add Group Admin'
  AddGroupLeader:
      context: missing
      value: 'Add Group Leader'
  AddGroupMember:
      context: missing
      value: 'Add Group Member'
  AddImage:
      context: missing
      value: 'Add Image'
  Adding:
      context: missing
      value: Adding...
  AdditionalComments:
      context: missing
      value: 'Additional Comments'
  AdditionalRecommendations:
      context: missing
      value: 'Additional recommendations'
  AddJobRole:
      context: missing
      value: 'Add Job Role'
  AddLeader:
      context: missing
      value: 'Add Leader...'
  AddLocation:
      context: missing
      value: 'Add location'
  AddManager:
      context: missing
      value: 'Add Manager'
  AddMembers:
      context: missing
      value: 'Add Members...'
  AddMore:
      context: missing
      value: 'Add More'
  AddNewSection:
      context: title text of modal which is used to add new section
      value: 'Add New Section'
  AddNewSkill:
      context: missing
      value: 'Add New Skill'
  AddNewSkillcredential:
      context: missing
      value: 'Add New Skill/Credential'
  AddNow:
      context: missing
      value: 'Add Now'
  AddOpenings:
      context: missing
      value: 'Add openings'
  AddOption:
      context: missing
      value: 'Add option'
  AddPricingInfo:
      context: missing
      value: 'Add Pricing Info'
  AddressLine1:
      context: missing
      value: 'Address Line 1 '
  AddressLine2:
      context: missing
      value: 'Address Line 2'
  AddressLine3:
      context: missing
      value: 'Address Line 3'
  AddSkill:
      context: missing
      value: 'Add Skill'
  AddSkillcoinsToWallet:
      context: missing
      value: 'Add Skillcoins to Wallet'
  AddSkills:
      context: missing
      value: 'Add Skills'
  AddSkillsYouHave:
      context: missing
      value: 'Add skills you have'
  AddSkillToYourLearningGoals:
      context: missing
      value: 'Add skill to your learning goals'
  AddSmartcard:
      context: missing
      value: 'Add SmartCard'
  AddSmartcards:
      context: missing
      value: 'Add SmartCards'
  AddStartDate:
      context: missing
      value: 'Add Start Date'
  AddSubmission:
      context: missing
      value: 'Add Submission'
  AddThisSkillToYourLearningGoal:
      context: missing
      value: 'Add this skill to your learning goal'
  AddThumbnail:
      context: missing
      value: 'add thumbnail'
  AddTitle:
      context: missing
      value: 'Add title'
  AddTitledescription:
      context: missing
      value: 'Add title/description'
  AddTitledescriptionmandatory:
      context: missing
      value: 'Add Title/Description(Mandatory)'
  AddTitledescriptionOptional:
      context: missing
      value: 'Add title/description (optional)'
  AddTitlemandatory:
      context: missing
      value: 'Add title(Mandatory)'
  AddToChannel:
      context: missing
      value: 'Add to channel'
  AddToPathwayOpenSnackbar:
      context: informative text when adding a card to a pathway or journey
      value: Card successfully added to {label}(s)!
  AddToPathwayJourneyHeader:
      context: informative text when adding a card to a pathway or journey
      value: Add to {cardType}(s)
  AddToPathwayBody:
      context: informative text when adding a card to a pathway
      value: Select the Pathway(s) you want to add this content into. Content will be added at the end of the selected Pathway(s).
  AddToPathwayJourneyWarning:
      context: warning text when adding a card to a pathway or journey
      value: Create a {cardType} before you start adding content to it
  AddToJourneyBody:
      context: informative text when adding a card to a journey
      value: 'Select the Journey(s) you want to add this content into. For each Journey selected, indicate the sections you want the content to be displayed. Content will be added at the end of each section.'
  AddToFeatured:
      context: missing
      value: 'Add to Featured'
  AddToFeaturedSection:
      context: missing
      value: 'Add to featured section'
  AddToJourney:
      context: missing
      value: 'Add to Journey'
  AddToLearningPlan:
      context: missing
      value: 'Add to learning plan'
  AddToMdp:
      context: missing
      value: 'Add to MDP'
  AddToMyLearningPlan:
      context: missing
      value: 'Add to My Learning Plan'
  AddToPathway:
      context: missing
      value: 'Add to Pathway'
  AddTopics:
      context: missing
      value: 'Add Topics'
  AddToSkillsPassport:
      context: missing
      value: 'Add to skills passport'
  AddToYourPathways:
      context: missing
      value: 'Add to your Pathways'
  AddTrustedCollaboratorToChannel:
      context: missing
      value: 'Add Trusted Collaborator to Channel'
  AddUrl:
      context: missing
      value: 'Add URL'
  AddWorkHistory:
      context: missing
      value: 'Add Work History'
  AddYourEducationDetails:
      context: missing
      value: 'Add your Education details'
  AddYourRemarksBelow:
      context: missing
      value: 'Add your remarks below'
  ADescriptionExplainingUsersWhatThisChannelIsAbout:
      context: missing
      value: 'A description explaining users what this channel is about.'
  ADescriptionExplainingUsersWhatThisGroupIsAbout:
      context: missing
      value: 'A description explaining users what this group is about.'
  AdjustImage:
      context: missing
      value: 'Adjust Image'
  Admin:
      context: missing
      value: Admin
  AdminConsole:
      context: missing
      value: 'Admin Console'
  Advanced:
      context: missing
      value: Advanced
  advanced:
      context: refers to a skill level, lowercase needed for dynamic key translation.
      value: advanced
  AdvancedSettings:
      context: missing
      value: 'Advanced Settings'
  Ago:
      context: missing
      value: ago
  Alert:
      context: missing
      value: Alert!
  All:
      context: missing
      value: All
  AllActivity:
      context: missing
      value: 'All Activity'
  AllAnswersCorrect:
      context: missing
      value: 'All answers correct'
  AllAssociatedStatisticsAndCommentsWillBePermanentlyRemoved:
      context: missing
      value: 'All associated statistics and comments will be permanently removed.'
  AllChangesInThisSectionAreSavedAutomatically:
      context: Info message shown below to close section button
      value: 'All changes in this section are saved automatically.'
  AllChannels:
      context: missing
      value: 'All Channels'
  AllCompleted:
      context: missing
      value: 'All Completed'
  AllContent:
      context: missing
      value: 'All Content'
  AllContentPosts:
      context: missing
      value: 'All Content Posts'
  AllFilters:
      context: missing
      value: 'All Filters'
  AllGroups:
      context: missing
      value: 'All Groups'
  AllInProgressItemsOnThePlatformWillBeListedHere:
      context: missing
      value: 'All in progress items on the platform will be listed here'
  AllLanguages:
      context: missing
      value: 'All Languages'
  AllNotifications:
      context: missing
      value: 'All Notifications'
  AllowFollowersToPostContent:
      context: missing
      value: 'Allow Followers to Post Content'
  AllowTheUserToAnswerTheQuizAgain:
      context: missing
      value: 'Allow the user to answer the Quiz again'
  AllQuestionsAreMandatory:
      context: missing
      value: 'All questions are mandatory'
  AllSmartcard:
      context: missing
      value: 'All SmartCard'
  AllSmartcardFirst:
      context: informative message
      value: All the SmartCards needs to be completed first
  AllTeams:
      context: missing
      value: 'All Teams'
  AllTheSmartcardsNeedsToBeCompletedFirst:
      context: missing
      value: 'All the SmartCards needs to be completed first'
  AllTime:
      context: missing
      value: 'All time'
  AllTimesInUtc:
      context: missing
      value: 'All times in UTC'
  AllYourActivityOnThePlatformWillBeListedHere:
      context: missing
      value: 'All your activity on the platform will be listed here'
  Amount:
      context: missing
      value: Amount
  AmountToPay:
      context: missing
      value: 'Amount to pay'
  Analytics:
      context: missing
      value: Analytics
  AnalyticsLandingPageTbd:
      context: missing
      value: 'Analytics landing page (TBD)'
  And:
      context: missing
      value: 'and '
  AndLearnAnywhere:
      context: missing
      value: 'and learn anywhere!'
  Android:
      context: missing
      value: Android
  Annually:
      context: reassignment frecuency label
      value: Annually
  AnnualLearningGoal:
      context: missing
      value: 'Annual Learning Goal'
  Answer:
      context: missing
      value: Answer
  AnswerAgain:
      context: missing
      value: 'Answer Again'
  Answered:
      context: missing
      value: Answered
  AnyLocation:
      context: missing
      value: 'Any location'
  AnyTimeZone:
      context: missing
      value: 'Any time zone'
  ApologizeWeDontHaveAnySkillRelatedToYourJobFamilyRoleAtThisTime:
      context: missing
      value: "Apologize we don't have any skill related to your job family role at this time."
  App:
      context: missing
      value: APP
  AppInstalledPleaseUseTheButtonBelowToReAuthenticateYourselfOnTheApp:
      context: missing
      value: 'app installed, please use the button below to re-authenticate yourself on the app.'
  Applications:
      context: missing
      value: Applications
  Applied:
      context: missing
      value: Applied
  Apply:
      context: missing
      value: Apply
  ApplyFilters:
      context: missing
      value: 'Apply Filters'
  ApplyForGoiIncentive:
      context: missing
      value: 'Apply for GOI Incentive'
  ApplyForTitle:
      context: missing
      value: 'Apply for {title}'
  Applying:
      context: missing
      value: Applying...
  ApplyNow:
      context: missing
      value: 'Apply Now'
  Applystatus:
      context: missing
      value: 'Apply/Status:'
  AppOnThisDevice:
      context: missing
      value: 'app on this device?'
  Approve:
      context: missing
      value: Approve
  Approved:
      context: missing
      value: Approved
  ApprovedDate:
      context: missing
      value: 'Approved Date: '
  AppStore:
      context: missing
      value: 'app store'
  AReceiptIsSentToYourEmail:
      context: missing
      value: 'A receipt is sent to your email'
  AReceiptIsSentToYourEmail2:
      context: missing
      value: 'A receipt is sent to your Email'
  ARecieptIsSentToYourEmail:
      context: missing
      value: 'A reciept is sent to your Email'
  AreYouAnInternapprenticeOrDoYouHaveAJobOffer:
      context: missing
      value: 'Are you an intern/apprentice or do you have a job offer'
  AreYouSureThatYouWantToRemoveThisFromYourAssignmentList:
      context: missing
      value: 'Are you sure that you want to remove this from your assignment list?'
  AreYouSureThatYouWantToRemoveThisFromYourCareerGrowthList:
      context: missing
      value: 'Are you sure that you want to remove this from your Career Growth list?'
  AreYouSureYouWantToCancelRegistration:
      context: missing
      value: 'Are you sure you want to cancel registration?'
  VILTUnregisteredConfirmation:
      context: Unregister confirmation message, displayed on unregister confirmation modal to inform user live event card unregister action.
      value: 'Are you sure you want to unregister? Once unregistered, you will no longer have access to the live event.'
  AreYouSureYouWantToEndLivestream:
      context: missing
      value: 'Are you sure you want to end Livestream?'
  AreYouSureYouWantToPermanentlyDeleteThisSmartcard:
      context: missing
      value: 'Are you sure you want to permanently delete this SmartCard?'
  AreYouSureYouWantToRemoveThisCardFromThePathway:
      context: missing
      value: 'Are you sure you want to remove this card from the Pathway?'
  AreYouSureYouWantToRemoveThisPathwayFromTheJourney:
      context: missing
      value: 'Are you sure you want to remove this Pathway from the Journey?'
  AreYouSureYouWantToRemoveThisSection:
      context: missing
      value: 'Are you sure you want to remove this section?'
  AreYouSureYouWantToStopCoBroadcastingAndContinueAsAViewer:
      context: missing
      value: 'Are you sure you want to stop co-broadcasting and continue as a viewer?'
  AreYouSureYouWantToSubmit:
      context: missing
      value: 'Are you sure you want to submit?'
  AreYouSureYouWantToUnbookmarkThisContentFromTheList:
      context: missing
      value: 'Are you sure you want to Unbookmark this content from the list?'
  AreYouSureYouWantToUnfollowThisChannel:
      context: missing
      value: 'Are you sure you want to unfollow this channel?'
  AreYouSureYouWantToWithdraw:
      context: missing
      value: 'Are you sure you want to withdraw?'
  Article:
      context: refer to a content card type, Capitalize is required
      value: Article
  article:
      context: refer to a content card type, lowercase is required
      value: article
  ARTICLE:
      context: refer to a content card type, uppercase is required
      value: ARTICLE
  ArticlesFromRssAndBooksFromMiscSources:
      context: missing
      value: 'Articles (from RSS) and Books (from misc sources)'
  Ascending:
      context: missing
      value: Ascending
  AscendingAndDescending:
      context: missing
      value: 'ascending and descending'
  AspiringRole:
      context: missing
      value: 'Aspiring Role'
  AssesmentDate:
      context: missing
      value: 'Assesment Date'
  AssessementReports:
      context: missing
      value: 'Assessement Reports'
  Assessment:
      context: missing
      value: Assessment
  AssessmentComplete:
      context: missing
      value: 'Assessment Complete'
  AssessmentDate:
      context: missing
      value: 'Assessment Date'
  AssessmentName:
      context: missing
      value: 'Assessment Name'
  AssessmentOfYourSkillsByYoursPeers:
      context: missing
      value: 'Assessment of your skills by yours peers'
  AssessmentPreview:
      context: missing
      value: 'Assessment Preview'
  Assessments:
      context: missing
      value: 'Assessments(#)'
  AssessmentUrl:
      context: missing
      value: 'Assessment URL'
  AssessPeers:
      context: missing
      value: 'Assess Peers'
  AssessSelf:
      context: missing
      value: 'Assess Self'
  AssessSkillsIdentifySkillsGapsAndDiscover:
      context: missing
      value: 'Assess skills, identify skills gaps and discover'
  Assign:
      context: missing
      value: Assign
  AssignContentTo:
      context: missing
      value: 'Assign content to ...'
  AssignContentToAnEntireGroupOrIndividualsWithinYourGroup:
      context: missing
      value: 'Assign content to an entire group or individuals within your group'
  Assigned:
      context: missing
      value: Assigned
  AssignedBy:
      context: missing
      value: 'Assigned by'
  AssignedByMe:
      context: missing
      value: 'Assigned by me'
  AssignedByMe2:
      context: missing
      value: 'Assigned by Me'
  AssignedDate:
      context: missing
      value: 'Assigned Date: '
  AssignedDate2:
      context: missing
      value: 'Assigned Date'
  AssignedDateMostRecent:
      context: missing
      value: 'Assigned Date: Most Recent'
  AssignedOn:
      context: missing
      value: 'Assigned On'
  AssignedOnly:
      context: missing
      value: 'Assigned only'
  AssignedTo:
    value: Assigned to
    context: text info for content Assigned To a user
  AssignedToGroups:
      context: missing
      value: 'Assigned to Groups'
  Assigner:
      context: missing
      value: 'Assigner:'
  AssignerInfoAboutAssigner:
      context: missing
      value: 'Assigner : info about assigner'
  Assignment:
      context: missing
      value: Assignment
  AssignmentPerformance:
      context: missing
      value: 'Assignment Performance'
  Assignments:
      context: missing
      value: Assignments
  AssignmentsCompleted:
      context: missing
      value: 'Assignments Completed'
  AssignmentType:
      context: missing
      value: 'Assignment Type'
  Assignor:
      context: missing
      value: Assignor
  Assignors:
      context: missing
      value: Assignors
  AssignThisContentToSpecificIndividualsOrGroupsFirst:
      context: missing
      value: 'Assign this content to specific Individuals or Groups first.'
  AssignToMe:
      context: missing
      value: 'Assign to Me'
  Associate:
      context: missing
      value: Associate
  Associations:
      context: missing
      value: Associations
  At:
      context: missing
      value: at
  AtLeastOneAcceptedGroupLeaderIsRequired:
      context: missing
      value: 'At least one accepted group leader is required'
  AtLeastOneLetterShouldBeCapital:
      context: missing
      value: 'At least one letter should be capital.'
  AttachAFile:
      context: missing
      value: 'Attach a File'
  Audio:
      context: missing
      value: Audio
  Author:
      context: missing
      value: Author
  AuthorChangedSuccessfully:
      context: missing
      value: 'Author changed successfully'
  AuthoredBy:
      context: missing
      value: 'Authored by'
  AuthorName:
      context: missing
      value: 'Author name...'
  AutoAssignContentOfThisGroupToGroupMembers:
      context: missing
      value: 'Auto assign content of this group to group members'
  AutoAssignContentOfThisGroupToTheMembersOfTheGroup:
      context: missing
      value: 'Auto Assign content of this group to the members of the group'
  AutoAssignContentToNewGroupMembers:
      context: missing
      value: 'Auto Assign Content to New Group Members'
  Automatically:
      context: missing
      value: Automatically
  AutomaticallyMarkAsComplete:
      context: missing
      value: 'automatically mark as complete'
  Available:
      context: missing
      value: Available
  AvailableAssessmentSlots:
      context: missing
      value: 'Available Assessment Slots:'
  AvailableInvites:
      context: missing
      value: 'AVAILABLE INVITES'
  AvailableRoles:
      context: missing
      value: 'Available Roles:'
  AvailableSeats:
      context: missing
      value: 'Available Seats'
  Avatar:
      context: missing
      value: Avatar
  Average:
      context: missing
      value: 'Average:'
  AveragePeerAssessment:
      context: missing
      value: 'Average Peer Assessment'
  AverageScore:
      context: missing
      value: 'Average Score'
  AvgSession:
      context: missing
      value: 'Avg Session'
  Back:
      context: missing
      value: Back
  BackToLoginPage:
      context: missing
      value: 'Back to Login page?'
  BackToPreviousSection:
      context: missing
      value: 'Back to previous section'
  BackToSignIn:
      context: missing
      value: 'Back to sign in'
  Badge:
      context: missing
      value: 'Badge'
  BadgeTitle:
      context: input label for badge title on edit pathway/journey modal
      value: Badge title
  BadgeTitlePlaceholder:
      context: placeholder for input badge title on edit pathway/journey modal
      value: Enter here the badge title
  BadgeTitleErrorMsg:
      context: error message
      value: Please enter Badge title.
  BadgeSelectErrorMsg:
      context: error message
      value: 'Please select badge icon'
  BadgeEarned:
      context: missing
      value: 'Badge Earned'
  BadgeId:
      context: missing
      value: 'Badge ID'
  BadgeImage:
      context: missing
      value: 'Badge image'
  BadgeName:
      context: missing
      value: 'Badge Name'
  BadgePreview:
      context: missing
      value: 'Badge Preview'
  Badges:
      context: missing
      value: Badges
  BadgesAwardedByCompletingContentOnThePlatformOrOutsideThePlatform:
      context: missing
      value: 'Badges awarded by completing content on the platform or outside the platform'
  BadgesEarned:
      context: missing
      value: 'Badges Earned'
  BadgeUrl:
      context: missing
      value: 'Badge URL'
  BannerImage:
      context: missing
      value: 'Banner Image'
  BannerWidget:
      context: missing
      value: 'Banner Widget'
  BannerWidgetContent:
      context: missing
      value: 'Banner Widget Content'
  Bar:
      context: missing
      value: bar
  BecauseYouAreFollowing:
      context: missing
      value: 'Because you are following '
  Beginner:
      context: missing
      value: Beginner
  beginner:
      context: refers to a skill level, lowercase needed for dynamic key translation.
      value: beginner
  BeginTypingIeJavascriptPython:
      context: missing
      value: 'Begin typing ... i.e. Javascript, Python'
  BeginTypingSourceNameOrADomainName:
      context: missing
      value: 'Begin typing source name or a domain name'
  BeTheFirstOneToComment:
      context: missing
      value: 'Be the first one to comment...'
  BeTheFirstToReact:
      context: missing
      value: 'Be the first to react'
  BigCardView:
      context: missing
      value: 'Big Card View'
  BigCardViewSelected:
      context: missing
      value: 'Big Card View Selected'
  BigView:
      context: missing
      value: 'Big view'
  Bio:
      context: missing
      value: Bio
  Biography:
      context: missing
      value: Biography
  BlockDontShowSimilarContentItIsNotRelevant:
      context: missing
      value: "Block: Don't show similar content, it is not relevant"
  Book:
      context: missing
      value: Book
  BookAssessment:
      context: missing
      value: 'Book Assessment'
  Bookmark:
      context: missing
      value: Bookmark
  SnakebarMsgIfCardBookmarked:
      context: informative text when a card is bookmarked
      value: 'Done! This {cardTypeLabel} has been Bookmarked and can now be found in your bookmarked content.'
  SnakebarMsgIfCardBookmarkedRemoved:
      context: informative text when a card bookmark is removed
      value: 'This {cardTypeLabel} has been removed from your Bookmarks.'
  Bookmarked:
      context: missing
      value: Bookmarked
  BookmarkedCards:
      context: missing
      value: 'Bookmarked Cards'
  BookmarkedDateMostRecent:
      context: missing
      value: 'Bookmarked Date: Most Recent'
  BookmarkedOn:
      context: missing
      value: 'Bookmarked On'
  Bookmarks:
      context: missing
      value: Bookmarks
  Broadcasters:
      context: missing
      value: Broadcasters
  Browse:
      context: missing
      value: Browse...
  BrowseAllCards:
      context: missing
      value: 'Browse All Cards'
  Btntext:
      context: missing
      value: '{btnText}'
  BulkUpload:
      context: missing
      value: 'Bulk Upload'
  BulkUploadHistory:
      context: missing
      value: 'Bulk Upload History'
  BusinessUnit:
      context: missing
      value: 'Business Unit'
  BuyNow:
      context: missing
      value: 'BUY NOW'
  BuyNow2:
      context: missing
      value: 'Buy Now'
  BuyWithSkillcoins:
      context: missing
      value: 'BUY WITH SKILLCOINS'
  BuyWithSkillcoins2:
      context: missing
      value: 'Buy with SkillCoins'
  By:
      context: missing
      value: by
  ByName:
      context: missing
      value: 'By name'
  Bytes:
      context: missing
      value: Bytes
  ByUsername:
      context: missing
      value: 'By {username}'
  ByYou:
      context: missing
      value: 'By you'
  CanBeUsedToControlAwardingBadgesForPathwayCompletion:
      context: missing
      value: '(can be used to control awarding badges for Pathway completion)'
  Cancel:
      context: missing
      value: Cancel
  CancelDowngradeRequest:
      context: missing
      value: 'Cancel Downgrade Request'
  CancelEnrollment:
      context: missing
      value: 'Cancel Enrollment?'
  Cancelinvite:
      context: missing
      value: cancelInvite
  Cancelraisehand:
      context: missing
      value: cancelRaiseHand
  CancelWaitingListEnrollment:
      context: missing
      value: 'Cancel Waiting List Enrollment?'
  CanMembersShareWithThisGroup:
      context: missing
      value: 'Can Members share with this group?'
  CannotResetPassword:
      context: missing
      value: 'Cannot Reset Password'
  Card:
      context: missing
      value: Card
  card:
      context: lowercase needed for dynamic use between singular/plural, it refers to a Content 'card' like smartcard pathway journet etc.
      value: card
  cards:
      context: lowercase needed for dynamic use between singular/plural, it refers to a Content 'card' like smartcard pathway journet etc.
      value: cards
  CardCompletionTrend:
      context: missing
      value: 'Card Completion Trend'
  CardContributions:
      context: missing
      value: 'Card Contributions'
  CardCurrentcardcardcount:
      context: missing
      value: 'Card {currentCard}/{cardCount}'
  CardFetchLimit500:
      context: missing
      value: 'Card fetch limit 500'
  CardHasBeenRemovedFromTheFeaturedCarousel:
      context: missing
      value: 'Card has been removed from the featured carousel'
  CardHasBeenSuccessfullyAddedToFeaturedContentSection:
      context: missing
      value: 'Card has been successfully added to featured content section.'
  CardHasBeenSuccessfullyAddedToMdp:
      context: missing
      value: 'Card has been successfully added to MDP'
  CardHasBeenSuccessfullyRemovedFromFeaturedContentSection:
      context: missing
      value: 'Card has been successfully removed from featured content section.'
  CardImage:
      context: missing
      value: 'Card image'
  CardInsights:
      context: missing
      value: 'Card Insights'
  CardPush:
      context: missing
      value: 'Card push'
  Cards:
      context: missing
      value: Cards
  CardsAssignedToTheGroup:
      context: missing
      value: 'Cards assigned to the group'
  CardsCreated:
      context: missing
      value: 'Cards Created'
  CardsLiked:
      context: missing
      value: 'Cards Liked'
  CardStatistics:
      context: missing
      value: 'Card Statistics'
  CardSuccessfullyAddedToJourneys:
      context: missing
      value: 'Card successfully added to Journey(s)!'
  CardSuccessfullyAddedToPathways:
      context: missing
      value: 'Card successfully added to Pathway(s)!'
  CardSuccessfullyRemovedFromGroup:
      context: missing
      value: 'Card successfully removed from Group'
  CardTitle:
      context: missing
      value: 'Card Title'
  CardType:
      context: missing
      value: 'Card Type'
  CareerGrowth:
      context: missing
      value: 'Career Growth'
  CareerPath:
      context: missing
      value: 'Career path'
  CareerPathing:
      context: missing
      value: 'Career Pathing'
  CareerPreferences:
      context: missing
      value: 'Career Preferences'
  CareerPreferencesUpdated:
      context: missing
      value: 'Career preferences updated'
  CareerPreferencesUpdateFailed:
      context: missing
      value: 'Career preferences update failed'
  Carousel:
      context: missing
      value: Carousel
  CarouselControls:
      context: missing
      value: 'Carousel controls'
  CarouselHasBeenDeleted:
      context: missing
      value: 'Carousel has been deleted.'
  CarouselName:
      context: missing
      value: 'Carousel Name'
  CarouselNameNotAbleToUpdate:
      context: missing
      value: 'Carousel name not able to update'
  CarouselNameShouldNotBeBlank:
      context: missing
      value: 'Carousel name should not be blank'
  CarouselNameSuccessfullyUpdated:
      context: missing
      value: 'Carousel name successfully updated'
  CarouselShouldNotBeBlank:
      context: missing
      value: 'Carousel should not be blank'
  CarouselType:
      context: missing
      value: 'Carousel Type'
  Categories:
      context: missing
      value: CATEGORIES
  Category:
      context: missing
      value: Category
  Certificate:
      context: missing
      value: Certificate
  CertificateId:
      context: missing
      value: 'Certificate ID'
  CertificatePreview:
      context: missing
      value: 'Certificate Preview'
  Certificates:
      context: missing
      value: Certificates
  CertificatesAdded:
      context: missing
      value: 'Certificates Added'
  CertificateUrl:
      context: missing
      value: 'Certificate URL'
  Certification:
      context: missing
      value: Certification
  CertificationName:
      context: missing
      value: 'Certification Name'
  Certifications:
      context: missing
      value: Certifications
  ChangeAudio:
      context: missing
      value: 'Change Audio'
  ChangeAuthor:
      context: missing
      value: 'Change Author'
  ChangeBannerImage:
      context: missing
      value: 'Change Banner Image'
  ChangeImage:
      context: missing
      value: 'Change Image'
  ChangeJobFamilyRole:
      context: aria label of a button
      value: Change Job Family & Role
  ChangeLog:
      context: missing
      value: 'Change Log'
  ChangeManager:
      context: missing
      value: 'Change Manager'
  ChangePlan:
      context: missing
      value: 'CHANGE PLAN'
  ChangeProfileImage:
      context: missing
      value: 'Change Profile Image'
  ChangesMadeToYourLearningGoalsHaveBeenSaved:
      context: missing
      value: 'Changes made to your Learning Goals have been saved.'
  ChangesUpdatedSuccessfully:
      context: missing
      value: 'Changes updated successfully!'
  Channel:
      context: missing
      value: Channel
  ChannelCarousels:
      context: missing
      value: 'Channel Carousels'
  ChannelCollaborators:
      context: missing
      value: 'Channel Collaborators'
  ChannelCuration:
      context: missing
      value: 'Channel Curation'
  ChannelCurator:
      context: missing
      value: 'channel Curator'
  ChannelCuratorsAdministrators:
      context: missing
      value: 'Channel Curators (Administrators)'
  ChannelImage:
      context: missing
      value: 'Channel Image'
  ChannelName:
      context: missing
      value: 'Channel Name'
  ChannelNotFound:
      context: missing
      value: 'Channel not found'
  ChannelRankInput:
      context: missing
      value: 'Channel Rank Input'
  Channels:
      context: missing
      value: Channels
  ChannelSavedSuccessfully:
      context: missing
      value: 'Channel saved successfully!'
  ChannelSections:
      context: missing
      value: 'Channel Sections'
  ChannelSettings:
      context: missing
      value: 'Channel Settings'
  ChannelsIAmACollaboratorOf:
      context: missing
      value: 'Channels I am a collaborator of'
  ChannelsIAmACuratorOf:
      context: missing
      value: 'Channels I am a curator of'
  ChannelsIFollow:
      context: missing
      value: 'Channels I follow'
  ChannelsYouFollow:
      context: missing
      value: 'Channels you follow'
  ChannelTitle:
      context: missing
      value: 'Channel Title'
  ChannelTopics:
      context: missing
      value: 'Channel Topic(s)'
  ChannelTraffic:
      context: missing
      value: 'Channel Traffic'
  ChannelTrustedCollaborators:
      context: missing
      value: 'Channel Trusted Collaborators'
  ChannelWidgets:
      context: missing
      value: 'Channel Widgets'
  CharactersRemaining:
      context: missing
      value: 'Characters Remaining'
  Chat:
      context: missing
      value: Chat
  Chat2:
      context: missing
      value: Chat
  CheckForDuplicates:
      context: missing
      value: 'Check for Duplicates'
  Choice:
      context: missing
      value: Choice
  ChoiceCantBeEmpty:
      context: missing
      value: "Choice can't be empty"
  ChooseFrequency:
      context: reassignment frequency label
      value: Choose Frequency
  ChooseLevelOfTheSkill:
      context: missing
      value: 'Choose level of the skill'
  ChooseOptions:
      context: missing
      value: 'Choose Options'
  ChooseQualification:
      context: missing
      value: 'Choose Qualification'
  ChooseTheCardForLeap:
      context: missing
      value: 'Choose the card for leap'
  ChooseTypeForJourney:
      context: missing
      value: 'Choose type for Journey'
  ChromeExetension:
      context: missing
      value: 'chrome exetension'
  City:
      context: missing
      value: City
  Citydistrict:
      context: missing
      value: City/District
  Classes:
      context: missing
      value: Classes
  ClcBadges:
      context: missing
      value: 'CLC Badges'
  Clear:
      context: missing
      value: clear
  ClearSearchFieldErrorMsg:
      context: missing
      value: "You can't share card in draft state! Please clear Share/Post to field."
  ClearFilters:
      context: "Label for Clear Button"
      value: 'Clear filters'
  ClearAllFilters:
      context: missing
      value: 'Clear all filters'
  ClearAllFilters2:
      context: missing
      value: 'Clear All Filters'
  ClearAllFilters3:
      context: missing
      value: 'Clear All Filters'
  ClearDate:
      context: missing
      value: 'Clear date'
  ClearDateRange:
      context: missing
      value: 'Clear date range'
  ClearFiltersOfFiltertype:
      context: missing
      value: 'Clear Filters of {filterType}'
  ClearHistory:
      context: missing
      value: 'Clear History'
  ClearLevel:
      context: missing
      value: 'Clear level'
  ClickHere:
      context: missing
      value: 'Click here '
  ClickHere2:
      context: missing
      value: '&amp;nbsp;Click here'
  ClickHereToAccessTheCourse:
      context: missing
      value: 'Click here to access the course'
  ClickHereToAddSummary:
      context: missing
      value: 'Click here to add summary'
  ClickHereToSelectContentTypes:
      context: missing
      value: 'Click here to select content types'
  ClickHereToSelectLanguages:
      context: missing
      value: 'Click here to select languages'
  ClickHereToSelectSources:
      context: missing
      value: 'Click here to select sources'
  ClickHereToSelectTopics:
      context: missing
      value: 'Click here to select topics'
  ClickHereToUnregister:
      context: missing
      value: 'Click here to unregister'
  ClickOnCardToViewLivestream:
      context: missing
      value: 'Click on card to view Livestream'
  ClickOrTypeHereToSearchForUsers:
      context: missing
      value: 'Click or type here to search for users'
  ClickToLikeThisComment:
      context: missing
      value: 'Click to like this comment'
  ClickToUploadNewImage:
      context: missing
      value: 'Click to upload new image'
  ClickToViewCommentatorsPage:
      context: missing
      value: "Click to view commentator's page"
  Close:
      context: missing
      value: Close
  Close2:
      context: missing
      value: close
  Close3:
      context: missing
      value: close
  Close4:
      context: missing
      value: close
  CloseChannelDescription:
      context: missing
      value: 'Close Channel Description'
  Closed:
      context: missing
      value: Closed
  ClosedOn:
      context: missing
      value: 'Closed on:'
  CloseRegistration:
      context: missing
      value: 'Close Registration'
  CloseSection:
      context: Section drop-down menu option that is used to close section
      value: 'Close Section'
  CloseTeamActivity:
      context: missing
      value: 'Close Team Activity'
  CloudComputing:
      context: missing
      value: '#cloud computing'
  CoLeadCenter:
      context: missing
      value: 'Co-Lead Center'
  Collaborator:
      context: missing
      value: Collaborator
  Collaborators:
      context: missing
      value: 'Collaborators:'
  Collaborators2:
      context: missing
      value: 'Collaborators'
  Collaborators3:
      context: missing
      value: 'Collaborator(s)'
  CollapseAll:
      context: missing
      value: 'Collapse All'
  CollapseSection:
      context: missing
      value: 'Collapse Section'
  CollegeOrUniversity:
      context: missing
      value: 'College or University'
  ComingSoon:
      context: missing
      value: 'Coming Soon'
  Comment:
      context: card or content comment
      value: Comment
  CommentLowerCase:
      context: comment label on content or channels
      value: comment
  CommentedOn:
      context: missing
      value: 'commented on'
  CommentHasBeenDeletedSuccessfully:
      context: missing
      value: 'Comment has been deleted successfully'
  Comments:
      context: missing
      value: Comments
  CommentsLowerCase:
      context: comments label on content or channels
      value: comments
  EditedBy:
      context: Edited by label on list
      value: Edited by
  Commute:
      context: missing
      value: Commute
  Company:
      context: missing
      value: Company
  CompanyDepartment:
      context: missing
      value: 'Company &amp; Department'
  CompanyName:
      context: missing
      value: 'Company Name'
  Complete:
      context: missing
      value: Complete
  CompleteAllThePreviousContentToUnlockThis:
      context: missing
      value: 'Complete all the previous content to unlock this'
  Completed:
      context: missing
      value: Completed
  CompletedAssignment:
      context: missing
      value: 'completed Assignment'
  CompletedDate:
      context: missing
      value: 'Completed date'
  CompletedDate2:
      context: My learning plan completion date
      value: 'Completed Date'
  CompletedJourney:
      context: missing
      value: 'completed Journey'
  CompletedOn:
      context: missing
      value: 'Completed on'
  CompletedPathway:
      context: missing
      value: 'completed Pathway'
  CompletedSmartcard:
      context: missing
      value: 'completed SmartCard'
  CompleteYourTransaction:
      context: missing
      value: 'Complete your transaction'
  CompletionBadge:
      context: missing
      value: 'Completion Badge'
  CompletionDate:
      context: missing
      value: 'Completion Date: '
  CompletionDate2:
    context: This is content completion date
    value: Completion Date
  CompletionStatus:
      context: missing
      value: 'Completion Status'
  ConferencingTool:
      context: missing
      value: 'Conferencing Tool'
  Configurations:
      context: missing
      value: Configurations
  ConfigurationUpdated:
      context: missing
      value: 'Configuration Updated'
  Confirm:
      context: missing
      value: Confirm
  ConfirmAndProceed:
      context: missing
      value: 'Confirm and Proceed*'
  ConfirmNewPassword:
      context: missing
      value: 'Confirm New Password'
  ConfirmOrder:
      context: missing
      value: 'CONFIRM ORDER'
  ConfirmPassword:
      context: missing
      value: 'Confirm Password'
  ConfirmSubmit:
      context: missing
      value: 'Confirm Submit'
  Congratulations:
      context: missing
      value: Congratulations!
  Connect:
      context: missing
      value: Connect
  ConnectAdditionalSources:
      context: missing
      value: 'Connect Additional Sources'
  Connected:
      context: missing
      value: Connected
  ConnectToSlack:
      context: missing
      value: 'Connect to Slack'
  ConsiderMakingThisYourLearningGoal:
      context: missing
      value: 'Consider making this your learning goal'
  ConsumingEveryPieceOfContentYouConsumeEarnsYouPoints:
      context: missing
      value: 'Consuming: Every piece of content you consume earns you points.'
  Consumption:
      context: missing
      value: Consumption
  Container:
      context: missing
      value: Container
  Content:
      context: missing
      value: Content
  ContentAssignmentIsInProgressAndWillBeCompletedShortly:
      context: missing
      value: 'Content assignment is in progress and will be completed shortly'
  ContentCategories:
      context: missing
      value: 'Content categories'
  ContentDoesNotExist:
      context: missing
      value: 'Content does not exist'
  ContentFromAllOverTheWeb:
      context: missing
      value: 'Content from all over the web'
  ContentHasBeenSuccessfullyAddedToFeaturedSection:
      context: missing
      value: 'Content has been successfully added to featured section.'
  PendingContentSync:
      context: error messge returned from BE Api
      value: 'Content sync is pending, please try again later.'
  ContentHasBeenSuccessfullyRemovedFromFeaturedSection:
      context: missing
      value: 'Content has been successfully removed from featured section.'
  ContentItems:
      context: missing
      value: 'Content Items'
  ContentLayout:
      context: missing
      value: 'Content Layout'
  ContentLibrary:
      context: missing
      value: 'Content library'
  ContentLibrary2:
      context: missing
      value: 'Content Library'
  ContentName:
      context: missing
      value: 'Content Name'
  ContentNotAvailable:
      context: missing
      value: 'Content not available.'
  ContentOnly:
      context: missing
      value: 'content only'
  ContentProvider:
      context: Show as alt text if provider name for logo is missing in channel
      value: 'Content Provider'
  ContentPreview:
      context: missing
      value: 'Content Preview'
  ContentProviderLogo:
      context: missing
      value: 'Content Provider Logo'
  ContentReport:
      context: missing
      value: 'Content Report'
  ContentSource:
      context: missing
      value: 'Content Source'
  ContentSources:
      context: missing
      value: 'Content Source(s)'
  ContentTab:
      context: missing
      value: 'Content tab'
  ContentThatYouHaveBookmarkedWillAppearHere:
      context: missing
      value: 'Content that you have Bookmarked will appear here.'
  ContentTopics:
      context: missing
      value: 'Content Topics: '
  ContentType:
      context: missing
      value: 'Content Type'
  ContentTypes:
      context: missing
      value: 'Content Type(s)'
  ContentViews:
      context: missing
      value: 'Content Views'
  ContentWillBeFilteredBasedOnTheSearchParameterEnte:
      context: missing
      value: 'Content will be filtered based on the search parameter entered and Selecting the following checkboxes.'
  Continue:
      context: missing
      value: Continue
  ContinueToApplyForGoiIncentive:
      context: missing
      value: 'Continue to apply for GOI Incentive?'
  ContinueToTheCardtypeYouLastViewed:
      context: missing
      value: 'Continue to the {cardType} you last viewed:'
  ContinueUsingFutureskillsPrime:
      context: missing
      value: 'Continue using FutureSkills Prime?'
  ContinueWithoutSave:
      context: missing
      value: 'Continue Without Save'
  ContentWillBeFilteredBasedOnSearchParameterEnter:
      context: hidden instruction for search input in group page
      value: 'Content will be filtered based on the search parameter entered'
  ContinuousLearningCredits:
      context: missing
      value: 'Continuous Learning Credits'
  ContinuousLearningHours:
      context: missing
      value: 'Continuous Learning Hours'
  ContinuousLearningHoursIn:
      context: missing
      value: 'Continuous Learning Hours in'
  ContinuousLearningHoursInClcdate:
      context: missing
      value: 'Continuous Learning Hours in {clcDate}'
  Contract:
      context: missing
      value: Contract
  ContractualEmployee:
      context: missing
      value: 'Contractual Employee'
  Contributions:
      context: missing
      value: Contributions
  Contributors:
      context: missing
      value: Contributors
  ContributorsCreateMsg:
      context: informative text when collaborator added to a pathway or journey
      value: Collaborators added successfully! Collaborators might not have access to private cards.
  ContributorsUpdateMsg:
      context: informative text when collaborator updated to a pathway or journey
      value: '{type} updated successfully! Collaborators might not have access to private cards.'
  ContributorsDefaultMsg:
      context: warning text when collaborator added to a pathway or journey
      value: Collaborators might not have access to private cards.
  CopyLink:
      context: missing
      value: 'Copy link'
  Copyright:
      context: missing
      value: Copyright
  Correct:
      context: missing
      value: Correct
  CouldNotFindUserYouTriedToAccess:
      context: missing
      value: 'Could not find user you tried to access'
  CountActiveUsers:
      context: missing
      value: '{count} Active Users'
  CountItems:
      context: missing
      value: '{count} items'
  CountNewUsers:
      context: missing
      value: '{count} New Users'
  Country:
      context: missing
      value: Country
  CountVote:
      context: missing
      value: '{count} Vote'
  CountVotes:
      context: missing
      value: '{count} Votes'
  Course:
      context: refer to a content card type, Capitalize is required
      value: Course
  course:
      context: refer to a content card type, lowecase is required
      value: course
  COURSE:
      context: refer to a content card type, uppercase is required
      value: COURSE
  Course2:
      context: missing
      value: 'Course:'
  CourseassessmentTitle:
      context: missing
      value: 'Course/Assessment Title'
  CourseDates:
      context: missing
      value: 'Course Dates'
  CourseEvents:
      context: missing
      value: 'Course &amp; Events'
  CourseIsGettingUploaded:
      context: missing
      value: 'Course is getting uploaded'
  CourseName:
      context: missing
      value: 'Course Name:'
  Courses:
      context: missing
      value: 'Courses '
  CourseType:
      context: missing
      value: 'Course Type'
  CoverImage:
      context: missing
      value: 'Cover Image'
  Cpe:
      context: missing
      value: CPE
  CpeCourse:
      context: missing
      value: 'CPE course'
  CpeCredits:
      context: missing
      value: 'CPE Credits'
  CpeSubject:
      context: missing
      value: 'CPE Subject'
  Create:
      context: missing
      value: Create
  CreateAccount:
      context: missing
      value: 'create account'
  CreateChannel:
      context: missing
      value: 'Create Channel'
  CreateContent:
      context: missing
      value: 'Create Content'
  Created:
      context: missing
      value: created
  CreatedJourney:
      context: missing
      value: 'created Journey'
  CreatedOn:
      context: missing
      value: 'Created On'
  CreatedPathway:
      context: missing
      value: 'created Pathway'
  CreatedSmartcard:
      context: missing
      value: 'created SmartCard'
  CreateGroup:
      context: missing
      value: 'Create Group'
  CreateJourney:
      context: missing
      value: 'Create Journey'
  CreateNewGroupWithSelectedIndividuals:
      context: missing
      value: 'Create New Group with selected individuals'
  CreateNewSection:
      context: missing
      value: 'Create New Section'
  CreateNewSmartcard:
      context: missing
      value: 'Create New SmartCard'
  CreateNewVersion:
      context: missing
      value: 'Create New Version'
  CreatePathway:
      context: missing
      value: 'Create Pathway'
  CreatePlan:
      context: missing
      value: 'Create Plan'
  CreateVersion:
      context: missing
      value: 'Create Version'
  CreateYourOrganisationStructureAndCompleteYourProfile:
      context: missing
      value: 'Create your organisation structure and complete your profile!'
  Creating:
      context: missing
      value: Creating...
  Creator:
      context: missing
      value: 'Creator:'
  Creator2:
      context: missing
      value: 'Creator'
  Credential:
      context: missing
      value: 'Credential:'
  CredentialIcon:
      context: missing
      value: 'Credential Icon'
  CredentialImage:
      context: missing
      value: 'Credential Image'
  CredentialName:
      context: missing
      value: 'Credential Name'
  Credentials:
      context: missing
      value: Credentials
  CredentialUrl:
      context: missing
      value: 'Credential URL'
  CredentialUrlIsNotValid:
      context: missing
      value: 'Credential URL is not valid'
  Credit:
      context: missing
      value: Credit
  CreditedToTheBankOnDate:
      context: missing
      value: 'Credited to the Bank on {date}'
  Crop:
      context: missing
      value: Crop
  CuratableChannel:
      context: missing
      value: 'Curatable Channel'
  Curate:
      context: missing
      value: CURATE
  CurateContent:
      context: missing
      value: 'Curate Content'
  CurateContentForChannel:
      context: missing
      value: 'Curate content for channel'
  CurateFilter:
      context: missing
      value: 'Curate filter'
  CurateFollowersContent:
      context: missing
      value: 'Curate followers content'
  CurateUserPostedContent:
      context: missing
      value: 'Curate User Posted Content'
  CurationAndPosting:
      context: missing
      value: 'Curation and Posting'
  CurationDashboard:
      context: missing
      value: 'Curation Dashboard'
  Curator:
      context: missing
      value: 'Curator :'
  Curators:
      context: missing
      value: 'Curators: '
  Curators2:
      context: missing
      value: Curators
  CuratorsAndCollaboratorsOnly:
      context: missing
      value: 'Curators and Collaborators only'
  CuratorsCollaboratorsAndFollowers:
      context: missing
      value: 'Curators, Collaborators, and Followers'
  Current:
      context: missing
      value: (Current)
  CurrentAssessment:
      context: missing
      value: 'Current Assessment'
  CurrentEmploymentDetails:
      context: missing
      value: 'Current Employment Details'
  CurrentLearningGoal:
      context: missing
      value: 'Current learning goal'
  CurrentLearningPace:
      context: missing
      value: 'Current Learning Pace'
  CurrentlyActiveLearningGoals:
      context: missing
      value: 'Currently active Learning Goals'
  CurrentlyThereAreNoEventsAssociatedToThisCourse:
      context: missing
      value: 'Currently there are no events associated to this course.'
  CurrentlyThereAreNoEventsFoundThatMatchYourCriteria:
      context: missing
      value: 'Currently there are no events found that match your criteria.'
  CurrentlyThereAreNoUsersUnderThisStatus:
      context: missing
      value: 'Currently there are no users under this status'
  CurrentOccupation:
      context: missing
      value: 'Current Occupation'
  CurrentPassword:
      context: missing
      value: 'Current Password'
  CurrentRole:
      context: missing
      value: 'Current Role'
  CurrentSubscription:
      context: missing
      value: 'Current Subscription'
  CustomChannelsCarousels:
      context: missing
      value: 'Custom Channels Carousels'
  DataFormats:
      context: missing
      value: 'Data Formats'
  DataScientists:
      context: missing
      value: 'Data Scientists'
  Date:
      context: missing
      value: Date
  DateJoined:
      context: missing
      value: 'Date Joined'
  DateOfBirth:
      context: missing
      value: 'Date of Birth'
  DateOfBirthAsPerAadhaar:
      context: missing
      value: 'Date of Birth as per Aadhaar'
  DateOfJoining:
      context: missing
      value: 'Date of Joining'
  DateOfOfferLetter:
      context: missing
      value: 'Date of Offer Letter'
  DateRange:
      context: missing
      value: 'Date Range'
  DateUploaded:
      context: missing
      value: 'Date Uploaded'
  Day:
      context: missing
      value: Day
  Days:
      context: missing
      value: days
  Debit:
      context: missing
      value: Debit
  Debitcredit:
      context: missing
      value: Debit/Credit
  Declaration:
      context: missing
      value: Declaration
  Decline:
      context: missing
      value: Decline
  DeclineGroupInvitation:
      context: missing
      value: 'Decline Group Invitation'
  DeclineStream:
      context: missing
      value: 'decline stream'
  DeclineTitle:
      context: missing
      value: 'Decline {title}'
  DeferShowMeAgainLater:
      context: missing
      value: 'Defer: Show me again later'
  DegreeOrSpecialization:
      context: missing
      value: 'Degree or Specialization'
  Delete:
      context: missing
      value: Delete
  DeleteComment:
      context: missing
      value: 'Delete Comment?'
  Deleted:
      context: missing
      value: DELETED
  Deleted2:
      context: missing
      value: Deleted
  DeletedContent:
      context: missing
      value: 'Deleted Content'
  DeleteDropdown:
      context: missing
      value: 'delete dropdown'
  DeleteGroup:
      context: missing
      value: 'delete group'
  DeleteReason:
      context: missing
      value: 'Delete Reason'
  DeleteSearchHistory:
      context: missing
      value: 'Delete Search History'
  DeleteSection:
      context: missing
      value: 'Delete Section'
  DeleteSkill:
      context: missing
      value: 'Delete skill'
  DeleteSnackbartype:
      context: missing
      value: 'Delete {snackBarType}'
  DeleteSnackbartype2:
      context: missing
      value: 'Delete {snackBarType}?'
  DeleteTheComment:
      context: missing
      value: 'Delete the comment'
  Deleting:
      context: missing
      value: DELETING...
  DeletingComment:
      context: missing
      value: 'Deleting comment.'
  ArchiveSnackbartype:
    context: header text for archival confirmation modal
    value: 'Archive {snackBarType}'
  UnArchiveSnackbartype:
    context: header text for archival confirmation modal
    value: 'Unarchive {snackBarType}'
  Archive:
    context: label in card edit action to archive the content
    value: 'Archive'
  UnArchive:
    context: label in card edit action to Unarchive the content
    value: 'Unarchive'
  Archived:
    context: label for Archived tab in content
    value: 'Archived'
  DeliveryLanguage:
      context: missing
      value: 'Delivery Language'
  DemotedToMember:
      context: missing
      value: 'Demoted to Member'
  DemoteToMember:
      context: missing
      value: 'Demote to Member'
  Denied:
      context: missing
      value: Denied
  Deny:
      context: missing
      value: Deny
  Department:
      context: missing
      value: 'Department *'
  DepartmentRoles:
      context: missing
      value: 'Department Roles'
  Departments:
      context: missing
      value: Departments
  Descending:
      context: missing
      value: Descending
  DescErrorMsg:
      context: description error message
      value: Please enter a description to complete the creation process.
  Description:
      context: missing
      value: Description
  Descriptionmandatory:
      context: missing
      value: Description(Mandatory)
  Design:
      context: missing
      value: Design
  Designation:
      context: missing
      value: Designation
  Details:
      context: missing
      value: Details
  DetailsConfiguration:
      context: missing
      value: 'Details &amp;amp; Configuration'
  DidNotFindYourReasonTellUsWhatIsWrongWithThisContent:
      context: missing
      value: 'Did not find your reason? Tell us what is wrong with this content'
  DidYouMean:
      context: missing
      value: 'Did you mean:'
  DigitalCopyOfTheSkillsOrKnowledgeGainedByCompletingACourse:
      context: missing
      value: 'Digital copy of the skills or knowledge gained by completing a course'
  DigitalCreditBalance:
      context: missing
      value: 'Digital Credit Balance:'
  DigitAlphaNumeric:
      context: missing
      value: '8 Digit Alpha Numeric'
  DigitalWallet:
      context: missing
      value: 'Digital Wallet'
  Director:
      context: missing
      value: Director
  DirectReports:
      context: missing
      value: 'Direct Reports'
  Discard:
      context: missing
      value: Discard
  DiscardChanges:
      context: missing
      value: 'Discard Changes?'
  DiscardDraft:
      context: missing
      value: 'Discard Draft?'
  Discover:
      context: missing
      value: Discover
  DiscoverPage:
      context: missing
      value: 'discover page'
  Dismiss:
      context: missing
      value: Dismiss
  DismissAssignment:
      context: missing
      value: 'Dismiss Assignment'
  DismissAssignmentTitle:
      context: missing
      value: 'Dismiss Assignment-{title}'
  Dismissed:
      context: missing
      value: Dismissed
  DismissFromAssignments:
      context: missing
      value: 'Dismiss from Assignments'
  DismissSmartcard:
      context: missing
      value: 'Dismiss SmartCard'
  District:
      context: missing
      value: District
  Divisions:
      context: missing
      value: Divisions
  DocumentIcon:
      context: missing
      value: 'Document Icon'
  Done:
      context: missing
      value: Done
  DoNotChallengeMeOnThisDeviceAgain:
      context: missing
      value: 'Do not challenge me on this device again'
  DontRefreshThePage:
      context: missing
      value: "Don't refresh the page"
  Download:
      context: missing
      value: Download
  DownloadCsv:
      context: missing
      value: 'download csv'
  DownloadCsvFile:
      context: missing
      value: 'Download CSV file'
  DownloadExcel:
      context: missing
      value: 'Download Excel'
  DownloadReport:
      context: missing
      value: 'Download Report'
  DownloadSampleFile:
      context: missing
      value: 'Download Sample file'
  DownloadTranscript:
      context: missing
      value: 'Download Transcript'
  DoYouHaveAJobOffer:
      context: missing
      value: 'Do you have a job offer?'
  DoYouHaveThe:
      context: missing
      value: 'Do you have the'
  DoYouReallyWantToRejectSelectedCardFrom:
      context: missing
      value: 'Do you really want to reject selected card from'
  DoYouReallyWantToRemoveSelectedCardFrom:
      context: missing
      value: 'Do you really want to remove selected card from'
  DoYouWantToConfirmPlacingYourOrderWithSkillcoins:
      context: missing
      value: 'Do you want to confirm placing your order with Skillcoins?'
  DoYouWantToContinueWithoutSaveYourChanges:
      context: missing
      value: 'Do you want to continue without save your changes?'
  DoYouWantToDeleteTheComment:
      context: missing
      value: 'Do you want to delete the comment?'
  DoYouWantToDeleteTheCustomCarousel:
      context: missing
      value: 'Do you want to delete the custom carousel?'
  DoYouWantToDeleteTheStream:
      context: missing
      value: 'Do you want to delete the stream?'
  DoYouWantToRemoveThisEducationSource:
      context: missing
      value: 'Do you want to remove this education source?'
  Draft:
      context: missing
      value: DRAFT
  Draft2:
      context: filter option label
      value: Draft
  Drafts:
      context: missing
      value: Drafts
  DragAndDropResumeHere:
      context: missing
      value: 'Drag and Drop resume here'
  DragDropYourCsvFileHere:
      context: missing
      value: 'Drag &amp; Drop Your CSV File Here'
  Dropdown:
      context: missing
      value: dropdown
  DropImageHereOrUseUpload:
      context: missing
      value: 'Drop image here or use Upload.'
  Due:
      context: missing
      value: Due
  DueAt:
      context: missing
      value: 'Due At'
  DueDate:
      context: missing
      value: 'Due Date: '
  DueDate2:
      context: missing
      value: 'Due Date'
  DueDateYearActiveyear:
      context: missing
      value: 'Due Date (Year) {activeYear}'
  DueIn2Weeks:
      context: missing
      value: 'Due in 2 weeks'
  DueOn:
      context: missing
      value: 'Due On'
  Duration:
      context: missing
      value: 'Duration: '
  Duration2:
      context: missing
      value: 'Duration'
  DurationCannotBeZero:
      context: missing
      value: 'Duration cannot be zero'
  DurationHighToLow:
      context: missing
      value: 'Duration: High to Low'
  DurationLowToHigh:
      context: missing
      value: 'Duration: Low to High'
  DynamicPathway:
      context: missing
      value: 'Dynamic Pathway'
  DynamicPathways:
      context: missing
      value: 'Dynamic Pathways'
  DynamicSelection:
      context: missing
      value: 'Dynamic Selection'
  Edit:
      context: missing
      value: Edit
  EditAdmins:
      context: missing
      value: 'Edit Admins'
  EditAssessment:
      context: missing
      value: 'Edit Assessment'
  EditBadge:
      context: missing
      value: 'Edit Badge'
  EditBia:
      context: missing
      value: 'edit BIA'
  EditCertificate:
      context: missing
      value: 'Edit Certificate'
  EditChannels:
      context: missing
      value: 'edit Channels'
  EditContent:
      context: missing
      value: 'Edit Content'
  EditCurators:
      context: missing
      value: 'Edit Curators'
  EditEdittopic:
      context: missing
      value: 'Edit {editTopic}'
  EditGroup:
      context: missing
      value: 'edit group'
  EditLeaders:
      context: missing
      value: 'Edit Leaders'
  EditLearningGoal:
      context: missing
      value: 'Edit Learning Goal'
  EditPaymentInfo:
      context: missing
      value: 'Edit Payment Info'
  EditProfile:
      context: missing
      value: 'Edit Profile'
  EditPublicProfile:
      context: missing
      value: 'Edit Public Profile'
  EditReason:
      context: missing
      value: 'Edit Reason'
  EditRecommendations:
      context: missing
      value: 'Edit Recommendations'
  EditSection:
      context: Section drop-down menu option that is used to edit section
      value: 'Edit Section'
  EditSkill:
      context: missing
      value: 'Edit Skill'
  EditTitle:
      context: missing
      value: 'Edit {title}'
  EditTitleDescription:
      context: missing
      value: 'Edit Title &amp; Description'
  EditYourGroupToAddContentRelevantChannelsThatMembe:
      context: missing
      value: 'Edit your group to add content relevant channels that members can follow.'
  Education:
      context: missing
      value: Education
  EducationDetails:
      context: missing
      value: 'Education Details'
  EduWork:
      context: missing
      value: 'Edu @ Work'
  EgDesignAssertiveCommunication:
      context: missing
      value: 'eg: Design, Assertive Communication...'
  EgLeadershipFinanceSales:
      context: missing
      value: 'e.g. leadership, finance, sales'
  EligibilityForGoiIncentiveMayChangeBasisTermsOfTheScheme:
      context: missing
      value: '*Eligibility for GOI Incentive may change basis Terms of the scheme'
  EligibleAmount:
      context: missing
      value: 'Eligible Amount: '
  Email:
      context: text info for email address
      value: Email
  EmailAddressAsPerAadhaar:
      context: missing
      value: 'Email Address as per Aadhaar'
  EmailId:
      context: missing
      value: 'Email ID'
  EmailIdChanged:
      context: missing
      value: 'Email ID changed'
  EmailIsRequired:
      context: missing
      value: 'Email is required'
  EmailMeTheReport:
      context: missing
      value: 'Email me the report'
  EmailSent:
      context: missing
      value: 'Email Sent!'
  EmbededVideoPreview:
      context: missing
      value: 'Embeded Video Preview'
  Employed:
      context: missing
      value: Employed
  EmployeesListUpdated:
      context: missing
      value: 'Employees list updated'
  EmployerName:
      context: missing
      value: 'Employer Name'
  EmptyPagePlaceholder:
      context: missing
      value: 'Empty page placeholder'
  EnableChannelCuration:
      context: missing
      value: 'Enable Channel Curation'
  EnableContentSharing:
      context: missing
      value: 'Enable Content Sharing'
  Enabledisable:
      context: missing
      value: Enable/Disable
  Enabled:
      context: informative text if notification is enabled
      value: enabled
  Disabled:
      context: informative text if notification is disable
      value: disabled
  EnableNotificationsForThisChannel:
      context: missing
      value: 'Enable notifications for this channel'
  EnableQuestionCard:
      context: missing
      value: 'Enable question card'
  EnableTheBannerWidget:
      context: missing
      value: 'Enable the Banner Widget'
  EndDate:
      context: missing
      value: 'End Date'
  EndDateAndTime:
      context: missing
      value: 'End Date and Time'
  EndDateAndTimeShouldBeGreaterThan:
      context: missing
      value: 'End Date and Time should be greater than'
  EndDateAndTimeShouldBeGreaterThanCurrentTime:
      context: missing
      value: 'End Date and Time should be greater than current time'
  EndLivestream:
      context: missing
      value: 'End Livestream'
  EndTime:
      context: missing
      value: 'End Time'
  EndTimeIsInvalid:
      context: missing
      value: 'End Time is invalid'
  EnforceASequenceOfUnlockingWithinTheSectionsOfTheJourney:
      context: missing
      value: 'Enforce a sequence of unlocking within the sections of the Journey'
  EngagementIndex:
      context: missing
      value: 'Engagement Index'
  Enroll:
      context: missing
      value: Enroll
  Enrolled:
      context: missing
      value: Enrolled
  EnrollForWaitingList:
      context: missing
      value: 'Enroll for Waiting List?'
  EnrollmentCancelled:
      context: missing
      value: 'Enrollment Cancelled'
  Enrollments:
      context: missing
      value: Enrollments
  EnterABadgeName:
      context: missing
      value: 'Enter a Badge name'
  EnterABriefBackgroundAboutThisSkill:
      context: missing
      value: 'Enter a brief background about this skill'
  EnterAName:
      context: missing
      value: 'Enter a name'
  EnterAspiringRole:
      context: missing
      value: 'Enter Aspiring Role...'
  EnterAssessmentUrl:
      context: missing
      value: 'Enter Assessment URL'
  EnterBadgeId:
      context: missing
      value: 'Enter Badge ID'
  EnterBadgeUrl:
      context: missing
      value: 'Enter Badge URL'
  EnterCarouselName:
      context: missing
      value: 'Enter Carousel Name'
  EnterCertificateId:
      context: missing
      value: 'Enter Certificate ID'
  EnterCertificateUrl:
      context: missing
      value: 'Enter Certificate URL'
  EnterCode:
      context: missing
      value: 'Enter Code *'
  EnterCompanyName:
      context: missing
      value: 'Enter company name'
  EnterCurrentRole:
      context: missing
      value: 'Enter Current Role'
  EnterCurrentRoleOrUploadResume:
      context: missing
      value: 'Enter Current Role or Upload Resume'
  EnterDateOfBirth:
      context: missing
      value: 'Enter date of birth'
  EnterDateOfBirthAsPerAadhaar:
      context: missing
      value: 'Enter Date of Birth as per Aadhaar'
  EnterDesiredTag:
      context: missing
      value: 'Enter desired tag'
  EnterDueDate:
      context: missing
      value: 'enter due date'
  EnterEmail:
      context: missing
      value: 'Enter email'
  EnterEmailWithFormatNamedomaincom:
      context: missing
      value: 'enter email with format, <EMAIL>'
  EnterExpiryDate:
      context: missing
      value: 'Enter expiry date'
  EnterFullName:
      context: missing
      value: 'Enter full name'
  EnterGroupDescriptionHere:
      context: missing
      value: 'Enter Group description here'
  EnterGroupNameHere:
      context: missing
      value: 'Enter Group Name here'
  EnterJobTitle:
      context: missing
      value: 'Enter job title'
  EnterMobile:
      context: missing
      value: 'Enter mobile'
  EnterName:
      context: missing
      value: 'Enter Name'
  EnterNewPasswordForYourAccount:
      context: missing
      value: 'Enter new password for your account'
  EnterNewSectionTitleHere:
      context: missing
      value: 'Enter new section title here'
  EnterOrder:
      context: missing
      value: 'Enter Order'
  EnterPassword:
      context: missing
      value: 'Enter Password'
  EnterProviderName:
      context: missing
      value: 'Enter Provider Name'
  EnterProviderNameHere:
      context: missing
      value: 'Enter Provider Name here'
  EnterRegistrationDetails:
      context: missing
      value: 'Enter Registration Details'
  EnterRole:
      context: missing
      value: 'Enter Role...'
  EnterSectionTitleHere:
      context: missing
      value: 'Enter section title here'
  EnterSkill:
      context: missing
      value: 'Enter Skill'
  EnterSkillDescription:
      context: missing
      value: 'Enter skill description'
  EnterSmartcardDescriptionHere:
      context: missing
      value: 'Enter SmartCard description here'
  EnterSmartcardTitleHere:
      context: missing
      value: 'Enter SmartCard title here'
  EnterStartDate:
      context: missing
      value: 'enter start date'
  EnterTheCredentialName:
      context: missing
      value: 'Enter the Credential Name'
  EnterTheCredentialUrl:
      context: missing
      value: 'Enter the Credential URL'
  EnterTitle:
      context: missing
      value: 'Enter Title'
  EnterTitleHere:
      context: missing
      value: 'ENTER TITLE HERE...'
  EnterTopicHere:
      context: missing
      value: 'Enter topic here'
  EnterUsersNameToInvite:
      context: missing
      value: 'Enter users name to invite'
  EnterValidEmailId:
      context: missing
      value: 'Enter valid email-Id'
  EnterVersionChangesHere:
      context: missing
      value: 'Enter version changes here'
  EnterYourCompanyEmailAddress:
      context: missing
      value: 'Enter your company email address'
  EnterYourFirstName:
      context: missing
      value: 'Enter your first name'
  EnterYourLastName:
      context: missing
      value: 'Enter your last name'
  EnterYourNameHere:
      context: missing
      value: 'Enter your {name} here'
  EnterYourPassword:
      context: missing
      value: 'Enter your Password'
  EnterYourPasswordToContinue:
      context: missing
      value: 'Enter your password to Continue'
  EnterYourSearchTerm:
      context: missing
      value: 'Enter your search term'
  EntryLevel:
      context: missing
      value: 'Entry level'
  Error:
      context: missing
      value: Error
  ErrorAddingCardToMdpPleaseContactAdministrator:
      context: missing
      value: 'Error adding card to MDP. Please contact Administrator'
  Errormsg:
      context: missing
      value: '{errorMsg}'
  ErrorSubmitting:
      context: missing
      value: 'Error Submitting'
  ErrorUpdating:
      context: missing
      value: 'Error Updating!'
  ErrorUpdatingLanguage:
      context: missing
      value: 'Error Updating Language!'
  EvaluationReportsOnSuccessfullyCompletingAnAssessment:
      context: missing
      value: 'Evaluation reports on successfully completing an Assessment'
  EventsForGroup:
      context: missing
      value: 'Events for {meetUpGroup} group :'
  Excellent:
      context: missing
      value: Excellent
  ExclTaxes:
      context: missing
      value: '*Excl. Taxes'
  ExcludeGroupMembers:
      context: missing
      value: 'Exclude Group members'
  Executive:
      context: missing
      value: Executive
  ExistingMembersLogin:
      context: missing
      value: 'Existing Members Login:'
  ExitPublicProfile:
      context: missing
      value: 'Exit Public Profile'
  ExpandAll:
      context: missing
      value: 'Expand All'
  ExpandSection:
      context: missing
      value: 'Expand Section'
  Experience:
      context: missing
      value: Experience
  ExperienceInMonths:
      context: missing
      value: 'Experience in months'
  ExperienceLevel:
      context: missing
      value: 'Experience Level'
  Expertise:
      context: missing
      value: Expertise
  ExpiryDate:
      context: missing
      value: 'Expiry Date'
  ExpiryDateMmddyyyy:
      context: missing
      value: 'Expiry Date (MM/DD/YYYY)'
  Explanation:
      context: missing
      value: Explanation
  ExploreCareers:
      context: missing
      value: 'Explore Careers'
  Fail:
      context: missing
      value: Fail
  Failed:
      context: missing
      value: Failed
  FailedToFilterByCardTypes:
      context: missing
      value: 'Failed to filter by Card Types'
  FailedToLoadPleaseRefreshThePage:
      context: missing
      value: 'Failed to load. Please refresh the page'
  FailedToPublishPleaseTryAgain:
      context: missing
      value: 'Failed to publish. Please try again.'
  FailedToRejectPleaseTryAgain:
      context: missing
      value: 'Failed to reject. Please try again.'
  FailedToSearchByCardTitle:
      context: missing
      value: 'Failed to Search By Card Title'
  Fair:
      context: missing
      value: Fair
  Featured:
      context: missing
      value: Featured
  FeaturedContent:
      context: missing
      value: 'Featured Content'
  FeaturedPosts:
      context: missing
      value: 'Featured Posts'
  Feed:
      context: missing
      value: Feed
  FeelFreeToUpdateYourLearningGoals:
      context: missing
      value: 'Feel free to update your Learning Goals'
  Female:
      context: missing
      value: Female
  FetchingSections:
      context: missing
      value: 'Fetching Sections...'
  Field:
      context: missing
      value: Field
  FieldIsRequired:
      context: missing
      value: 'Field is required'
  File:
      context: missing
      value: file
  FileIsProcessingPleaseWait:
      context: missing
      value: 'File is processing. Please wait...'
  FileName:
      context: missing
      value: 'File Name'
  FilePreview:
      context: missing
      value: 'File Preview'
  Filter:
      context: missing
      value: Filter
  FilterByStatus:
      context: missing
      value: 'Filter by status'
  FilterKeyword:
      context: missing
      value: 'Filter keyword'
  FilterOnlyTheLanguagesYouWouldLikeToDriveYourRecommendations:
      context: missing
      value: 'Filter only the languages you would like to drive your recommendations'
  FilterOnSourcesAndorDomains:
      context: missing
      value: 'Filter on sources and/or domains'
  Filters:
      context: missing
      value: Filters
  FiltersizeFilters:
      context: missing
      value: '{filterSize} filters'
  Find:
      context: missing
      value: Find
  FindChannels:
      context: missing
      value: 'Find Channels'
  FindGroups:
      context: missing
      value: 'Find Groups'
  FindTheGroupsNonMembersInPendingList:
      context: missing
      value: "Find the group's non-members in pending list"
  Firefox56OrLaterSafari11OrLaterOpera45OrLater:
      context: missing
      value: 'Firefox 56 or later, Safari 11 or later, Opera 45 or later.'
  FirstName:
      context: missing
      value: 'First Name'
  FirstNameCannotContainTheFollowingCharacters:
      context: missing
      value: 'First Name cannot contain the following characters:'
  FirstNameIsInvalid:
      context: missing
      value: 'First Name is invalid.'
  FirstNameIsRequired:
      context: missing
      value: 'First Name is required'
  FirstNameSpecialCharacterErrorMessage:
      context: Error message for using special characters
      value: 'First Name cannot contain the following characters: {unpermitted_name_char}'
  Flag:
      context: missing
      value: Flag
  Flexible:
      context: missing
      value: Flexible
  Follow:
      context: missing
      value: Follow
  Follower:
      context: missing
      value: Follower
  Followers:
      context: missing
      value: 'followers'
  FollowersPermissions:
      context: missing
      value: 'Followers Permissions'
  Following:
      context: missing
      value: Following
  FollowingIsTheStructureThatExplainsThisVeryWell:
      context: missing
      value: 'Following is the structure that explains this very well.'
  FollowSubjectMatterExpertsSmeAndChannels:
      context: missing
      value: 'Follow Subject Matter Experts (SME) and Channels'
  Foo:
      context: missing
      value: foo
  Foo2:
      context: missing
      value: foo
  For:
      context: missing
      value: for
  ForFirstLetterCapitalized:
      context: missing
      value: For
  ForCorrectAnswer:
      context: missing
      value: 'For Correct Answer'
  ForgotYourPassword:
      context: missing
      value: 'forgot your password?'
  ForIncorrectAnswer:
      context: missing
      value: 'For Incorrect Answer'
  FormerUser:
      context: missing
      value: 'Former User'
  ForOpenSourceAtLeastOneTopicNeedToBeSelected:
      context: missing
      value: 'For open source at least one topic need to be selected'
  Free:
      context: missing
      value: Free
  FrequencyWithWhichSubsequentReassignmentsAreMade:
      context: missing
      value: 'Frequency with which subsequent reassignments are made'
  From:
      context: missing
      value: From
  FromBookmark:
      context: missing
      value: 'From Bookmark'
  FromInternalContent:
      context: missing
      value: 'from internal content'
  FromList:
      context: missing
      value: 'From list'
  FromYear:
      context: missing
      value: 'From Year'
  FromYourComputer:
      context: missing
      value: 'from your Computer'
  FullName:
      context: missing
      value: 'Full Name'
  FullNameAsPerAadhaar:
      context: missing
      value: 'Full Name as per Aadhaar'
  FullNameCannotContainTheFollowingCharacters:
      context: missing
      value: 'Full Name cannot contain the following characters: '
  FutureskillsPrimeProgramRegistration:
      context: missing
      value: 'FutureSkills Prime Program Registration'
  FutureskillsPrimeProgramRegistration12:
      context: missing
      value: 'FutureSkills Prime Program Registration 1/2'
  FutureskillsPrimeProgramRegistration22:
      context: missing
      value: 'FutureSkills Prime Program Registration 2/2'
  Gender:
      context: missing
      value: Gender
  GenderAsPerAadhaar:
      context: missing
      value: 'Gender as per Aadhaar'
  GeneralTermsAndConditions:
      context: missing
      value: 'General Terms and Conditions '
  Generate:
      context: missing
      value: Generate
  GenerateMeeting:
      context: missing
      value: 'Generate Meeting'
  GetAbstract:
      context: This is one type of source of content
      value: GetAbstract
  GetabstractContent:
      context: missing
      value: 'getAbstract Content'
  GetOtp:
      context: missing
      value: 'GET OTP'
  GetOurFreeAppForAppleOrAndroid:
      context: missing
      value: 'Get our free App for Apple or Android'
  GetPersonalizedRecommendationsBy:
      context: missing
      value: 'Get personalized recommendations by'
  GetResetLink:
      context: missing
      value: 'GET RESET LINK'
  GetStartedOnYourLearningJourneyWithGovtOfIndiaIncentives:
      context: missing
      value: 'Get Started on your learning journey with Govt. Of India Incentives !'
  GettingStartedWithRestApi:
      context: missing
      value: 'Getting started with REST API'
  GetTranscript:
      context: missing
      value: 'Get Transcript'
  Goals:
      context: missing
      value: Goals
  GoAndApplyForGreatJobPositions:
      context: missing
      value: 'Go and apply for great job positions!'
  GoBack:
      context: missing
      value: 'Go Back'
  GoBack2:
      context: missing
      value: 'Go back'
  GoBackToPreviousStep:
      context: missing
      value: 'Go Back to Previous Step?'
  GoLiveNow:
      context: missing
      value: 'GO LIVE NOW'
  Good:
      context: missing
      value: Good
  GotIt:
      context: missing
      value: 'Got it!!'
  GoTo:
      context: missing
      value: 'Go to:'
  GoToCourse:
      context: missing
      value: 'GO TO COURSE'
  GoToCourse2:
      context: missing
      value: 'Go To Course'
  GoToHomepage:
      context: missing
      value: 'Go to Homepage'
  GoToMyContent:
      context: missing
      value: 'Go to My Content'
  GoToMyLearningPlan:
      context: missing
      value: 'Go to my Learning Plan'
  GoToNextCard:
      context: missing
      value: 'Go to next card'
  GoToPreviousCard:
      context: missing
      value: 'Go to previous card'
  GoToTheCourse:
      context: missing
      value: 'Go to the course'
  GoToYour:
      context: missing
      value: 'Go to your'
  Government:
      context: missing
      value: Government
  GovernmentEmployee:
      context: missing
      value: 'Government Employee'
  Grade:
      context: missing
      value: Grade
  Graded:
      context: missing
      value: Graded
  Grader:
      context: missing
      value: Grader
  GradeScale:
      context: missing
      value: 'Grade Scale'
  PassingGrade:
      context: EP-93987 Showing Passing grades on the Project Smartcard
      value: 'Passing Grade'
  MaximumReattempts:
      context: EP-93987 Showing Passing grades on the Project Smartcard
      value: 'Maximum Reattempts'
  UnlimitedReattempts:
      context: EP-93987 Showing Passing grades on the Project Smartcard
      value: 'Unlimited'
  Group:
      context: missing
      value: Group
  Group81:
      context: missing
      value: 'Group 81'
  GroupAdmin:
      context: missing
      value: 'Group Admin'
  GroupAdminEdit:
      context: missing
      value: 'Group admin edit'
  GroupAdminManageGroupContentGroupUsersAndGroupAnalytics:
      context: missing
      value: 'Group Admin: Manage Group Content, Group Users, and Group Analytics'
  GroupAdmins:
      context: missing
      value: 'Group Admins'
  GroupBannerImage:
      context: missing
      value: 'Group Banner Image'
  GroupChannelUpdatedSuccessfully:
      context: missing
      value: 'Group Channel Updated Successfully'
  GroupCreation:
      context: missing
      value: 'Group Creation'
  GroupImage:
      context: missing
      value: 'Group Image'
  GroupLeader:
      context: missing
      value: 'Group Leader'
  GroupLeaderClose:
      context: missing
      value: 'Group Leader close'
  GroupLeaderEdit:
      context: missing
      value: 'Group Leader edit'
  GroupLeaders:
      context: missing
      value: 'Group Leaders'
  GroupMember:
      context: missing
      value: 'Group Member'
  GroupMembers:
      context: missing
      value: 'Group member(s)'
  GroupMemberViewAndConsumeContentAvailableWithinTheGroup:
      context: missing
      value: 'Group Member: View and consume content available within the group'
  GroupName:
      context: missing
      value: 'Group Name'
  Groupname:
      context: missing
      value: groupName
  GroupNameGroupname:
      context: missing
      value: 'Group name: {groupName}'
  Groups:
      context: missing
      value: Groups
  GroupSections:
      context: missing
      value: 'Group Sections'
  GroupSetting:
      context: missing
      value: 'Group Setting'
  GroupSettings:
      context: missing
      value: 'Group Settings'
  GroupsIAmALeaderOf:
      context: missing
      value: 'Groups I am a leader of'
  GroupsIAmAMemberOf:
      context: missing
      value: 'Groups I am a member of'
  GroupsIAmAnAdminOf:
      context: missing
      value: 'Groups I am an admin of'
  GroupTitlemandatory:
      context: missing
      value: 'Group Title(Mandatory)'
  GrowYourSkills:
      context: missing
      value: 'Grow your skills'
  GstCode:
      context: missing
      value: 'GST Code'
  Handle:
      context: missing
      value: Handle
  HappyLearningWatchThisSpaceForMore:
      context: missing
      value: 'Happy Learning! Watch this space for more.'
  HasDeclinedYourInvite:
      context: missing
      value: 'has declined your invite.'
  HasDeclinedYourRequest:
      context: missing
      value: 'has declined your request.'
  HasntPostedAnythingYet:
      context: missing
      value: " hasn't posted anything yet."
  HasRaisedHand:
      context: missing
      value: 'has raised hand'
  Headertext:
      context: missing
      value: '{headerText}'
  Heading:
      context: missing
      value: heading
  Help:
      context: missing
      value: Help
  HelpUsUnderstandTheIssue:
      context: missing
      value: 'Help us understand the issue'
  Here:
      context: missing
      value: Here
  HereWillBeFilterSoon:
      context: missing
      value: 'Here will be filter soon'
  Hi:
      context: missing
      value: Hi
  HiddenCards:
      context: missing
      value: 'hidden cards'
  HideMeFromLeaderboards:
      context: missing
      value: 'Hide me from Leaderboards'
  HideTags:
      context: missing
      value: 'Hide Tags'
  HighestEducationalQualification:
      context: missing
      value: 'Highest Educational Qualification'
  History:
      context: missing
      value: History
  HmmThePasswordsYouEnteredDidntMatchPleaseTryAgain:
      context: missing
      value: 'Hmm, the passwords you entered didn’t match. Please try again!'
  Host:
      context: missing
      value: Host
  Hours:
      context: missing
      value: hours
  HowDoYouWishToProceed:
      context: missing
      value: 'How do you wish to proceed?'
  HowImageAppears:
      context: missing
      value: 'How image appears. '
  HowManyItemsPerCarouselYouWouldLikeToSee:
      context: missing
      value: 'How many items per carousel you would like to see'
  HowWillYouRateHisherSkills:
      context: missing
      value: 'How will you rate his/her skills?'
  HowWillYouRateYourSkills:
      context: missing
      value: 'How will you rate your skills?'
  HowWouldYouLikeToProceed:
      context: missing
      value: 'How would you like to proceed?'
  Hr:
      context: missing
      value: hr
  Hrs:
      context: missing
      value: hrs
  HtmlWidget:
      context: missing
      value: 'HTML Widget'
  HtmlWidgetEnabledisable:
      context: missing
      value: 'HTML Widget enable/disable'
  HtmlWidgetSavedSuccessfully:
      context: missing
      value: 'HTML widget saved successfully'
  HtmlWidgetUnableToStorePleaseTryAgain:
      context: missing
      value: 'HTML widget unable to store, Please try again!'
  HtmlWidgetUpdatedSuccessfully:
      context: missing
      value: 'HTML widget updated successfully'
  Hybrid:
      context: missing
      value: Hybrid
  IAcknowledgeAndAcceptThatIHaveReadThe:
      context: missing
      value: 'I acknowledge and accept that I have read the'
  IdealSkillLevel:
      context: missing
      value: '- Ideal Skill Level'
  IDoNotFallUnderAnyOfTheAboveCategories:
      context: missing
      value: ' I DO NOT fall under any of the above categories'
  IeNodejs:
      context: missing
      value: 'i.e. nodejs'
  IfThisIsNotTheCorrectEmailPleaseRequestANewPasswordReset:
      context: missing
      value: 'If this is not the correct email, please request a new password reset.'
  IfYouAreUsingADeviceWith:
      context: missing
      value: 'If you are using a device with'
  IfYouHaveTroubleViewingTheContentPleaseClickOnOpenContentAbove:
      context: missing
      value: 'If you have trouble viewing the content, please click on "Open Content" above'
  IHaveReadAndAgreedToThe:
      context: missing
      value: 'I have read and agreed to the'
  IHaveReadAndAgreeToThe:
      context: missing
      value: 'I have read and agree to the'
  Image:
      context: missing
      value: Image
  ImageEditor:
      context: missing
      value: 'Image Editor'
  ImageIsBeingProcessedAndWillBeUpdatedShortly:
      context: missing
      value: 'Image is being processed and will be updated shortly'
  ImagePhotoCamera:
      context: missing
      value: 'Image Photo Camera'
  ImageViewer:
      context: missing
      value: 'Image Viewer'
  ImportCourses:
      context: missing
      value: 'Import Courses'
  InCaseOfAnyIssuesPleaseWriteTo:
      context: missing
      value: 'In case of any issues, please write to'
  IncentiveAmount:
      context: missing
      value: 'Incentive Amount'
  IncludeAMessage:
      context: missing
      value: 'Include a Message'
  IncludeAtLeastANumberAndSymbol:
      context: missing
      value: 'Include at least a number and symbol (#?!@$%^&amp;amp;*-).'
  IncludeAttachment:
      context: missing
      value: 'Include Attachment'
  IncludedInYourSubscription:
      context: missing
      value: 'Included in your subscription'
  IncludeMessage:
      context: missing
      value: 'Include Message'
  IncludesAllContentPostedToChannel:
      context: missing
      value: 'Includes all content posted to channel'
  Incorrect:
      context: missing
      value: Incorrect
  IncorrectOtp:
      context: missing
      value: 'Incorrect OTP'
  India:
      context: missing
      value: India
  IndicatesRequired:
      context: missing
      value: '* indicates required'
  IndicateTheTopicsForThisChannelTheseWillBeDisplaye:
      context: missing
      value: "Indicate the topics for this channel. These will be displayed in the channel's header."
  Individual:
      context: missing
      value: Individual
  IndividualCourse:
      context: missing
      value: 'Individual Course'
  Individuals:
      context: missing
      value: individuals
  Individuals2:
      context: missing
      value: Individuals
  Influencer:
      context: missing
      value: Influencer
  InProgress:
      context: missing
      value: 'In Progress'
  InputSkillName:
      context: missing
      value: 'Input skill name'
  Insights:
      context: missing
      value: Insights
  InspiredBy:
      context: missing
      value: 'Inspired by'
  InteractWith:
      context: missing
      value: 'Interact with'
  InterestedParticipants:
      context: missing
      value: 'Interested participants'
  Interests:
      context: missing
      value: Interests
  Intermediate:
      context: missing
      value: Intermediate
  intermediate:
      context: refers to a skill level, lowercase needed for dynamic key translation.
      value: intermediate
  IntermediateSkills:
      context: missing
      value: 'Intermediate Skills'
  Internal:
      context: missing
      value: Internal
  InternalCards:
      context: missing
      value: 'Internal Cards'
  Internapprentice:
      context: missing
      value: Intern/Apprentice
  Internship:
      context: missing
      value: Internship
  InvalidZoomLink:
      context: missing
      value: 'Invalid Zoom link'
  InvitationMessage:
      context: missing
      value: 'Invitation message*'
  InvitationsSent:
      context: missing
      value: 'Invitations Sent'
  Invite:
      context: missing
      value: Invite
  InviteAdpEmployees:
      context: missing
      value: 'INVITE ADP EMPLOYEES'
  InvitedOn:
      context: missing
      value: 'Invited on'
  InviteManagerAndPeers:
      context: missing
      value: 'Invite Manager and Peers*'
  InviteManagerAndPeersToRateYourSkills:
      context: missing
      value: 'Invite Manager and Peers to rate your skills'
  InviteManagersAndPeersToRateYourSkills:
      context: missing
      value: 'Invite Managers and Peers to rate your skills'
  InviteNew:
      context: missing
      value: 'Invite New'
  InviteNewUsers:
      context: missing
      value: 'Invite New Users'
  InvitePeers:
      context: missing
      value: 'Invite Peers?'
  InvitePeople:
      context: missing
      value: 'Invite People'
  InvitesSent:
      context: missing
      value: 'INVITES SENT'
  InviteSuccessfullySent:
      context: missing
      value: 'Invite successfully sent!'
  InviteTeamMembers:
      context: missing
      value: 'Invite Team Members'
  Inviting:
      context: missing
      value: Inviting
  Iphone:
      context: missing
      value: iPhone
  IsOverTheMaximum:
      context: missing
      value: 'is over the maximum'
  IssueDate:
      context: missing
      value: 'Issue Date'
  Issuer:
      context: missing
      value: Issuer
  IssuerName:
      context: missing
      value: 'Issuer Name'
  ItEmployeesInItFirmsAndNonItFirms:
      context: missing
      value: 'IT employees in IT firms and Non-IT firms'
  Items:
      context: missing
      value: Items
  ItemsInProgress:
      context: missing
      value: 'Items in Progress'
  ItIsSafeToCloseThisSection:
      context: Info message shown below close section button
      value: 'It is safe to close this section.'
  ItItes:
      context: missing
      value: 'IT - ITES'
  ItLooksLikeYouHaventHadAnyRecentActivity:
      context: missing
      value: "It looks like you haven't had any recent activity."
  JobFamilyRole:
      context: missing
      value: 'Job Family &amp; Role'
  JobHours:
      context: missing
      value: 'Job Hours'
  JobRole:
      context: missing
      value: 'Job Role'
  JobRoleAddedSuccessfully:
      context: missing
      value: 'Job Role added successfully!'
  JobRoles:
      context: missing
      value: 'Job Roles'
  JobRoleShouldBeLessThanLength:
      context: missing
      value: 'Job role should be less than {length}'
  JobTitle:
      context: missing
      value: 'Job Title'
  JobType:
      context: missing
      value: 'Job Type'
  JobVacancies:
      context: missing
      value: 'Job Vacancies'
  JobTitleSpecialCharacterErrorMessage:
      context: Error message for using special characters
      value: 'Job Title cannot contain the following characters: {unpermitted_name_char}'
  Join:
      context: missing
      value: Join
  Joined:
      context: missing
      value: Joined
  JoinGroup:
      context: missing
      value: 'Join Group'
  JoinNow:
      context: missing
      value: 'Join Now'
  Journey:
      context: missing
      value: Journey
  journey:
      context: journey keyword is a type of a smarcards, lowercase is needed in order to complete partial strings translations
      value: journey
  JourneyAccordionTitle:
      context: missing
      value: 'Journey accordion title'
  JourneyDescription:
      context: missing
      value: 'Journey Description'
  JourneyDuedate:
      context: missing
      value: 'Journey dueDate'
  JourneyEditor:
      context: missing
      value: 'Journey editor'
  Journeys:
      context: missing
      value: Journeys
  JourneyStartDate:
      context: missing
      value: 'Journey start date'
  JourneyTitle:
      context: missing
      value: 'Journey Title'
  JourneyTitlemandatory:
      context: missing
      value: 'Journey Title(Mandatory)'
  JourneyDynamicTitle:
      context: informative text for journey title
      value: 'Journey title: {title}'
  JourneyType:
      context: missing
      value: 'Journey Type'
  JpegPngAndPdfUpto2Mb:
      context: missing
      value: 'jpeg, png and pdf upto 2 MB'
  JpegPngUpto2Mb:
      context: missing
      value: 'jpeg, png upto 2 mb'
  JpgJpegPngPdfUpto2Mb:
      context: missing
      value: 'jpg, jpeg, png, pdf upto 2 MB'
  KindlyClickOn:
      context: missing
      value: 'Kindly click on'
  Language:
      context: missing
      value: Language
  Languages:
      context: missing
      value: Languages
  LanguageUpdated:
      context: missing
      value: 'Language updated!'
  Langugae:
      context: missing
      value: Langugae
  Last30Days:
      context: missing
      value: 'Last 30 days'
  Last90Days:
      context: missing
      value: 'Last 90 days'
  LastDataRefreshTime:
      context: missing
      value: 'Last data refresh time:'
  LastDataRefreshTimeLastupdatedUtc:
      context: missing
      value: 'Last data refresh time: {lastUpdated} UTC'
  LastMonth:
      context: missing
      value: 'Last Month'
  LastName:
      context: missing
      value: 'Last Name'
  LastNameCannotContainTheFollowingCharacters:
      context: missing
      value: 'Last Name cannot contain the following characters:'
  LastNameIsInvalid:
      context: missing
      value: 'Last Name is invalid.'
  LastNameIsRequired:
      context: missing
      value: 'Last Name is required'
  LastNameSpecialCharacterErrorMessage:
      context: Error message for using special characters
      value: 'Last Name cannot contain the following characters: {unpermitted_name_char}'
  LastNumberDays:
      context: missing
      value: 'Last {number} days'
  LastUpdated:
      context: missing
      value: 'Last updated:'
  LastUpdatedAt:
      context: missing
      value: 'Last Updated at:'
  LastWeek:
      context: missing
      value: 'Last Week'
  LastYear:
      context: missing
      value: 'Last Year'
  Launch:
      context: missing
      value: LAUNCH
  Launch2:
      context: missing
      value: Launch
  LaunchOktaVerifyOnYourMobileDeviceAndSelect:
      context: missing
      value: 'Launch Okta Verify on your mobile device and select'
  LayerOfSecurityWhenSigningInToYourOktaAccount:
      context: missing
      value: 'layer of security when signing in to your Okta account'
  LeadCenter:
      context: missing
      value: 'Lead Center'
  Leader:
      context: missing
      value: Leader
  Leaderboard:
      context: missing
      value: Leaderboard
  LeaderboardByScore:
      context: missing
      value: 'Leaderboard by Score'
  LeaderboardPosition:
      context: missing
      value: 'Leaderboard position'
  LeaderBoardUserImage:
      context: missing
      value: 'leader board user image'
  Leaders:
      context: missing
      value: Leaders
  Leap:
      context: missing
      value: LEAP
  LeapToAnotherSmartcard:
      context: missing
      value: 'Leap to another SmartCard'
  LearningGoal:
      context: missing
      value: 'Learning Goal'
  LearningGoals:
      context: missing
      value: 'Learning Goals'
  LearningGoalsAddedByYou:
      context: missing
      value: 'Learning Goals added by you'
  LearningHistory:
      context: missing
      value: 'Learning History'
  LearningHours:
      context: missing
      value: 'Learning Hours'
  LearningPlan:
      context: missing
      value: 'Learning Plan'
  LearningQueue:
      context: missing
      value: 'Learning Queue'
  Leave:
      context: missing
      value: Leave
  LeaveACommentUseToTagPeers:
      context: missing
      value: 'Leave a comment. Use @ to tag peers'
  LeaveGroup:
      context: missing
      value: 'Leave Group'
  Left:
      context: missing
      value: Left
  LengthofremainingskillsMore:
      context: missing
      value: '{lengthOfRemainingSkills} more'
  PlusMoreSkills:
    context: more skills information with link on the opportunity cards/detail, for accessibility purposes
    value: plus {num} more skills.
  LessSkills:
    context: show less skills information with link on the opportunity cards/detail, for accessibility purposes
    value: show less skills.
  Less:
      context: missing
      value: Less
  LessThanAYear:
      context: missing
      value: 'Less than a year'
  Level:
      context: missing
      value: Level
  Like:
      context: missing
      value: Like
  Like1:
      context: missing
      value: 'Like 1'
  Like2:
      context: missing
      value: 'Like 2'
  Like3:
      context: missing
      value: 'Like 3'
  Like4:
      context: missing
      value: like
  Liked:
      context: missing
      value: Liked
  Liked2:
      context: missing
      value: liked
  LikedSnippet:
      context: missing
      value: "liked '{snippet}'"
  Likes:
      context: missing
      value: Likes
  LikesLowercase:
      context: text for multiples like
      value: likes
  LikescountLikes:
      context: missing
      value: '{likesCount} Likes'
  Link:
      context: missing
      value: Link
  Linkedin:
      context: missing
      value: LinkedIn
  LinkedinIcon:
      context: missing
      value: 'LinkedIn Icon'
  Linkmandatory:
      context: missing
      value: Link(Mandatory)
  List:
      context: missing
      value: List
  ListIcons:
      context: missing
      value: 'List Icons'
  ListPrice:
      context: missing
      value: 'List Price:'
  ListView:
      context: missing
      value: 'List View'
  ListViewSelected:
      context: missing
      value: 'List View Selected'
  Live:
      context: missing
      value: LIVE
  LiveChat:
      context: missing
      value: 'Live Chat'
  LiveEvent:
      context: missing
      value: 'Live Event'
  LiveStream:
      context: missing
      value: 'Live Stream'
  Livestream:
      context: missing
      value: Livestream
  LivestreamEnded:
      context: missing
      value: 'Livestream Ended'
  LmsAdmin:
      context: missing
      value: 'LMS Admin'
  LoadCredit:
      context: missing
      value: 'Load Credit'
  Loading:
      context: missing
      value: Loading...
  LoadingCareerPath:
      context: missing
      value: 'Loading Career Path...'
  LoadingGraphData:
      context: missing
      value: 'Loading graph data'
  LoadingResultsFor:
      context: missing
      value: 'Loading results for '
  LoadingYourListOfLearningGoals:
      context: missing
      value: 'Loading your list of learning goals ...'
  LoadingYourListOfSkills:
      context: missing
      value: 'Loading your list of skills ...'
  LoadMore:
      context: missing
      value: 'Load more'
  LoadMoreContent:
      context: 'Text for the button to load more content'
      value: 'Load more content'
  Location:
      context: missing
      value: Location
  Locations:
      context: missing
      value: Locations
  Locked:
      context: missing
      value: LOCKED
  Login:
      context: missing
      value: Login
  LogIn:
      context: missing
      value: 'Log In'
  LoginHere:
      context: missing
      value: 'Login here'
  LoginUsingYourCredentials:
      context: missing
      value: 'Login using your credentials'
  LoginWith:
      context: missing
      value: 'Login with'
  LoginWithEnterpriseSso:
      context: missing
      value: 'LOGIN WITH ENTERPRISE SSO'
  LoginWithLabel:
      context: missing
      value: 'Login with {label}'
  Logo:
      context: missing
      value: logo
  LooksLikeWeDontHaveAnyMoreRecommendationsSettingsYouSpecified:
      context: missing
      value: "Looks like we don't have any more recommendations settings you specified."
  Low:
      context: missing
      value: Low
  LrsOff:
      context: missing
      value: 'LRS Off'
  LrsOn:
      context: missing
      value: 'LRS On'
  MachineRecommendedContent:
      context: missing
      value: 'Machine recommended content'
  MakeACopy:
      context: missing
      value: 'Make a Copy'
  MakeADecisionAndThenSubmitYouCards:
      context: missing
      value: 'Make a decision and then submit you cards.'
  MakeFeatured:
      context: missing
      value: 'Make featured'
  MakePayment:
      context: missing
      value: 'Make Payment'
  Male:
      context: missing
      value: Male
  ManageNotifications:
    context: label for link which opens the notifications settings
    value: Manage notifications
  ManageGroup:
      context: missing
      value: 'Manage Group'
  ManagerAlreadyAdded:
      context: missing
      value: 'Manager already added!'
  ManagerDashboard:
      context: missing
      value: 'Manager Dashboard'
  ManageSubscription:
      context: missing
      value: 'Manage subscription'
  Mandatory:
      context: missing
      value: Mandatory
  MandatoryFields:
      context: missing
      value: 'Mandatory Fields'
  MandatoryGroup:
      context: missing
      value: 'Mandatory group'
  Manually:
      context: missing
      value: Manually
  ManuallyMarkAsComplete:
      context: missing
      value: 'manually mark as complete'
  MarkAsComplete:
      context: missing
      value: 'Mark As Complete'
  MarkAsFeatured:
      context: missing
      value: 'Mark as Featured'
  MarkAsIncomplete:
      context: missing
      value: 'Mark as Incomplete'
  MarkAsPrivate:
      context: missing
      value: 'Mark as Private'
  MarkedAsComplete:
      context: missing
      value: 'Marked as complete'
  MarkAsCompleted:
      context: informative text for card marked as completed
      value: Mark as Completed
  Summarize:
      context: Summarize button label
      value: Summarize
  ShowInsights:
      context: Show insights button label
      value: Show insights
  MarkedAsFeaturedSuccessfully:
      context: missing
      value: 'Marked as featured successfully'
  Marketing:
      context: missing
      value: Marketing
  MaxCharacters:
      context: missing
      value: 'Max characters'
  MaxFileSizeIs2MbWeSupportDocDocxPdf:
      context: missing
      value: 'Max. file size is 2 MB. We support .doc, .docx, .pdf.'
  Maximum10OptionsPerQuestion:
      context: missing
      value: 'Maximum 10 options per question'
  Maximum30QuestionsPerCard:
      context: missing
      value: 'Maximum 30 questions per card'
  Maximum50TopicsCanBeSelected:
      context: missing
      value: 'Maximum 50 topics can be selected.'
  MaximumCharacterLimitReached:
      context: missing
      value: 'Maximum character limit reached.'
  MaximumLanguagesLimitReached:
      context: missing
      value: 'Maximum languages limit reached'
  MaximumLocationsLimitReached:
      context: missing
      value: 'Maximum locations limit reached'
  MaximumOf15Collaborators:
      context: missing
      value: '(Maximum of 15 collaborators)'
  MaximumOf3Skills:
      context: missing
      value: '(Maximum of 3 skills)'
  MaximumSkillsLimitReached:
      context: missing
      value: 'Maximum skills limit reached'
  MaximumTimeZonesLimitReached:
      context: missing
      value: 'Maximum time zones limit reached'
  MaximumUsers:
      context: missing
      value: 'Maximum Users'
  MaxNumberOfRecommendations:
      context: missing
      value: 'Max Number of Recommendations'
  Me:
      context: missing
      value: Me
  Member:
      context: missing
      value: Member
  Members:
      context: text info for user members
      value: Members
  MembersCanShareContentToThisGroup:
      context: missing
      value: 'Members can share content to this group'
  MembersWhoFindThisSmartcardInteresting:
      context: missing
      value: 'Members who find this SmartCard interesting'
  Mentorships:
      context: missing
      value: Mentorships
  Message:
      context: missing
      value: Message
  MessageNumberOfCharacters:
      context: missing
      value: 'Message (Number of Characters)'
  MessageNumberOfCharactersmandatory:
      context: missing
      value: 'Message (Number of Characters)(Mandatory)'
  Messages:
      context: missing
      value: Messages
  MidseniorLevel:
      context: missing
      value: 'Mid-Senior level'
  Min:
      context: missing
      value: min
  Min5Characters:
      context: missing
      value: 'Min 5 characters'
  Mins:
      context: missing
      value: mins
  MinsMSecsS:
      context: missing
      value: '{mins} m {secs} s'
  Minutes:
      context: missing
      value: minutes
  MmDdYyyy:
      context: missing
      value: MM-DD-YYYY
  Mmddyyyy:
      context: missing
      value: MM/DD/YYYY
  MobileNumber:
      context: missing
      value: 'Mobile Number '
  MobileNumber2:
      context: missing
      value: 'Mobile Number'
  MobileNumberAsPerAadhaar:
      context: missing
      value: 'Mobile Number as per Aadhaar'
  Moderate:
      context: missing
      value: Moderate
  Modified:
      context: missing
      value: Modified
  Month:
      context: missing
      value: Month
  Months:
      context: missing
      value: Months
  Monthly:
      context: reassignment frecuency option label
      value: Monthly
  More:
      context: missing
      value: More
  MoreAboutBio:
      context: missing
      value: 'More About Bio'
  MoreComments:
      context: missing
      value: 'more comments'
  MoreDetailsAboutBundlename:
      context: missing
      value: 'More Details about {bundleName}'
  MoreOptions:
      context: missing
      value: 'More options'
  MostPopular:
      context: missing
      value: 'MOST POPULAR'
  MoveSection:
      context: missing
      value: 'Move Section'
  MoveSmartcard:
      context: missing
      value: 'Move SmartCard'
  MoveToTop:
      context: missing
      value: 'Move to top'
  Mth:
      context: missing
      value: mth
  Mths:
      context: missing
      value: mths
  MultipleLocations:
      context: missing
      value: 'Multiple locations'
  Mute:
      context: missing
      value: mute
  MyActivities:
      context: missing
      value: 'My Activities'
  MyActivity:
      context: missing
      value: 'My Activity'
  MyAppliedOpportunities:
      context: missing
      value: 'My Applied Opportunities'
  MyAssignedLearning:
      context: missing
      value: 'My Assigned Learning'
  MyBadges:
      context: missing
      value: 'My Badges'
  MyChannels:
      context: missing
      value: 'My Channels'
  MyContent:
      context: missing
      value: 'My Content'
  MyContributionsPage:
      context: missing
      value: 'My Contributions page'
  MyGroups:
      context: missing
      value: 'My Groups'
  MyGroupsPage:
      context: missing
      value: 'My Groups page'
  Myguide:
      context: missing
      value: MyGuide
  MyGuide:
      context: missing
      value: 'My Guide'
  MyLearningAnalytics:
      context: missing
      value: 'My Learning Analytics'
  MyLearningPlan:
      context: missing
      value: 'My Learning Plan'
  MyLearningPlan2:
      context: missing
      value: 'My Learning Plan.'
  MyOrganization:
      context: missing
      value: 'My Organization'
  MyPendingRequests:
      context: missing
      value: 'My pending requests'
  MyResults:
      context: missing
      value: 'My Results'
  MyScoreHelpsYouMeasureYourProgress:
      context: missing
      value: 'My Score helps you measure your progress'
  MySkillsAssessment:
      context: missing
      value: 'My Skills Assessment'
  MyTeamsAssignedLearning:
      context: missing
      value: 'My Teams Assigned Learning'
  MyTeamSkills:
      context: missing
      value: 'My Team Skills'
  Na:
      context: missing
      value: n/a
  NaUpperCase:
    context: missing
    value: N/A
  NaHighestLevelAchieved:
      context: missing
      value: 'n/a Highest Level Achieved'
  Name:
      context: missing
      value: Name
  NameDoNotHaveAnythingInProgress:
      context: missing
      value: '{name} do not have anything in progress.'
  NameProfile:
      context: missing
      value: '{name} Profile'
  New:
      context: missing
      value: New!
  NewestFirst:
      context: missing
      value: 'Newest First'
  NewGroup:
      context: missing
      value: 'New Group'
  NewMembersInterested:
      context: missing
      value: 'New Members Interested?'
  NewPassword:
      context: missing
      value: 'New Password'
  NewUsers:
      context: missing
      value: 'New Users'
  NewUsers2:
      context: missing
      value: 'New Users'
  NewValue:
      context: missing
      value: 'New Value'
  Next:
      context: missing
      value: next
  Next2:
      context: missing
      value: Next
  NextCard:
      context: missing
      value: 'Next Card'
  NextSlide:
      context: missing
      value: 'Next slide'
  No:
      context: missing
      value: 'No'
  NoActiveJobListingAvailableForTheJobRole:
      context: missing
      value: 'No active job listing available for the Job Role'
  NoAppliedOpportunities:
      context: missing
      value: 'No Applied Opportunities'
  NoAssignmentsDue:
      context: missing
      value: 'No assignments due'
  NoBadgesFound:
      context: missing
      value: 'No badges found.'
  NoBadgesToShowCurrently:
      context: missing
      value: 'No Badges to show currently'
  NoBookmarkFound:
      context: missing
      value: 'No bookmark found.'
  NoCardsAvailable:
      context: missing
      value: 'No cards available.'
  NoCardsAvailableForTheParticularSkill:
      context: missing
      value: 'No cards available for the particular skill'
  NoCertificatesToShowCurrently:
      context: missing
      value: 'No Certificates to show currently'
  NoChannelFound:
      context: missing
      value: 'No Channel Found'
  NoChannelsFollowed:
      context: missing
      value: 'No channels followed'
  NoContent:
      context: missing
      value: 'No content'
  NoContentAvailable:
      context: missing
      value: 'No content available'
  NoContentForThisPeriod:
      context: missing
      value: 'No content for this period'
  NoContentFound:
      context: missing
      value: 'No content found.'
  NoContentFoundForTopiclabel:
      context: missing
      value: 'No content found for {topicLabel}'
  NoContentHasBeenAddedToThisSectionYet:
      context: Info shown to user when no content is present in section
      value: 'No content has been added to this section yet.'
  NoContentPendingForCuration:
      context: missing
      value: 'No content pending for curation.'
  NoContentSelected:
      context: missing
      value: 'No Content Selected'
  NoDataFound:
      context: missing
      value: 'No data found.'
  NoDataRelatedToSearchAndFilter:
      context: missing
      value: 'No data related to search and filter.'
  NoDataToDisplay:
      context: missing
      value: 'No Data to Display'
  NoDueDate:
      context: missing
      value: 'No Due Date'
  NoEventFound:
      context: missing
      value: 'No Event found.'
  NoFilesUploadedYet:
      context: missing
      value: 'No files uploaded yet'
  NoFollowersYet:
      context: missing
      value: 'No followers yet'
  NoIntegrationsYetCheckBackLater:
      context: missing
      value: 'No integrations yet. Check back later!'
  NoItemsInProgress:
      context: missing
      value: 'No items in progress'
  NoItemsToCurate:
      context: missing
      value: 'No items to curate'
  NoJobRoleUnderSelectedJobFamily:
      context: missing
      value: 'No job role under selected job family'
  NoLedgerFound:
      context: missing
      value: 'No Ledger Found'
  NoLedgersFound:
      context: missing
      value: 'No Ledgers Found'
  NoManagerpeersAvailableToInvite:
      context: missing
      value: 'No Manager/Peers available to invite'
  NoMatchesFound:
      context: missing
      value: 'No matches found'
  NoMatchFound:
      context: missing
      value: 'No match found'
  NoMatchingItems:
      context: missing
      value: 'No matching items'
  NoMembersFound:
      context: missing
      value: 'NO MEMBERS FOUND'
  None:
      context: missing
      value: None
  NonItItes:
      context: missing
      value: 'Non IT - ITES'
  NoOneHasAddedSubmissionsYet:
      context: missing
      value: 'No one has added submissions yet.'
  NoOneHasRegisteredYet:
      context: missing
      value: 'No one has registered yet.'
  NoPendingMembersFound:
      context: missing
      value: 'NO PENDING MEMBERS FOUND'
  Nopx:
      context: missing
      value: '{no}px'
  NoReasonSelectedPleaseProvideAReasonBeforeReporting:
      context: missing
      value: 'No reason selected. Please provide a reason before reporting.'
  NoRecommendedCards:
      context: missing
      value: 'No recommended cards'
  NoRecurringAssignmentsWillBeMadeAfterTheEndDate:
      context: missing
      value: 'No recurring Assignments will be made after the End Date'
  NoResultFoundForSearch:
      context: missing
      value: 'No result found for search'
  NoResults:
      context: missing
      value: 'No results'
  NoResultsFor:
      context: missing
      value: 'No results for'
  NoResultsFound:
      context: missing
      value: 'NO RESULTS FOUND...'
  NoResultsFound2:
      context: missing
      value: 'No results found.'
  NoResultsFoundForYourSearch:
      context: missing
      value: 'No results found for your search'
  NoSearch:
      context: missing
      value: No-Search
  NoSearchResultFor:
      context: missing
      value: 'No search result for'
  NoSearchResultsFound:
      context: missing
      value: 'No search results found.'
  NoSkillLevelDescriptions:
      context: missing
      value: 'No Skill Level Descriptions'
  NoSkills:
      context: missing
      value: 'No skills'
  NoSkillsToShowCurrently:
      context: missing
      value: 'No Skills to show currently'
  NoSmartcardsAdded:
      context: missing
      value: 'No SmartCards added'
  NoSuggestions:
      context: missing
      value: 'No recommendations!'
  Not:
      context: negation
      value: 'not'
  NotActive:
      context: missing
      value: 'Not Active'
  NotAvailable:
      context: missing
      value: 'Not Available'
  NotAValidDateAndDateShouldBeGreaterThanOfferLetter:
      context: missing
      value: 'Not a valid date and Date should be greater than offer letter'
  Note:
      context: missing
      value: 'Note:'
  NothingInYourActivityQueueYetYourTeamActivityWillAppearHere:
      context: missing
      value: 'Nothing in your activity queue yet. Your team activity will appear here.'
  NothingInYourQueueYetYourAnnouncementsWillAppearHere:
      context: missing
      value: 'Nothing in your queue yet. Your Announcements will appear here.'
  NothingInYourQueueYetYourBookmarksAndLearningWillAppearHere:
      context: missing
      value: 'Nothing in your queue yet. Your Bookmarks and Learning will appear here.'
  NothingInYourQueueYetYourHeadlinesWillAppearHere:
      context: missing
      value: 'Nothing in your queue yet. Your Headlines will appear here.'
  NothingInYourQueueYetYourLearningPlanWillAppearHere:
      context: missing
      value: 'Nothing in your queue yet. Your Learning Plan will appear here.'
  NothingInYourQueueYetYourRecommendationsWillAppearHere:
      context: missing
      value: 'Nothing in your queue yet. Your Recommendations will appear here.'
  NothingPromotedYet:
      context: missing
      value: 'Nothing promoted yet'
  NothingToShowHereContentAddedToYourTeamsLearningPlanWillAppearHere:
      context: missing
      value: 'Nothing to show here. Content added to your team’s learning plan will appear here'
  Notifications:
      context: missing
      value: Notifications
  NotificationSettings:
      context: missing
      value: 'Notification Settings'
  NotificationsForTheChannelAreEnabled:
      context: missing
      value: 'Notifications for the channel are enabled'
  NotificationSubscribeunsubscribe:
      context: missing
      value: 'Notification Subscribe/Unsubscribe'
  NotifyGroupMembers:
      context: missing
      value: 'Notify Group Members'
  NotifyIndividuals:
      context: missing
      value: 'Notify individuals'
  NotNow:
      context: missing
      value: 'Not Now'
  NotProvided:
      context: missing
      value: 'not provided'
  NoTransactionsDoneYet:
      context: missing
      value: 'No transactions done yet'
  NotSpecified:
      context: missing
      value: 'Not specified'
  NotStarted:
      context: missing
      value: 'Not Started'
  NoUserMatchingYourSearch:
      context: missing
      value: 'No user matching your search'
  NoUsers:
      context: missing
      value: 'No Users'
  NoUserSelected:
      context: missing
      value: 'No User Selected!'
  NoUsersFound:
      context: missing
      value: 'No Users Found'
  NoVersionsCreated:
      context: missing
      value: 'No versions created'
  NoViewers:
      context: missing
      value: 'No Viewers'
  Novice:
      context: text label for skill level
      value: Novice
  NoVotesYet:
      context: missing
      value: 'No votes yet'
  NumberOfCards:
      context: missing
      value: 'Number of cards'
  NumberOfItems:
      context: missing
      value: 'Number of Items'
  NumberOfOpenings:
      context: missing
      value: 'Number of Openings'
  NumMonths:
      context: missing
      value: '{num} Months'
  NumYears:
      context: missing
      value: '{num} Years'
  OfFutureskillsPrimeSchemeOfMeityGoi:
      context: missing
      value: 'of FutureSkills Prime scheme of MeitY GoI'
  Ok:
      context: missing
      value: OK
  OktaVerify:
      context: missing
      value: 'Okta Verify'
  OktaVerifyFromTheAppStore:
      context: missing
      value: 'Okta Verify from the App Store'
  OldestFirst:
      context: missing
      value: 'Oldest First'
  On:
      context: missing
      value: 'on'
  OneTopic:
      context: missing
      value: 'one topic'
  Only50TopicsAreAllowed:
      context: missing
      value: 'Only 50 topics are allowed.'
  OnlyEditablePathwaysAreVisibleHere:
      context: missing
      value: 'Only editable Pathways are visible here'
  OnlyNumbersBetween1And500Allowed:
      context: missing
      value: 'Only numbers between 1 and 500 allowed'
  OntoYourMobileDevice:
      context: missing
      value: 'onto your mobile device.'
  Oops:
      context: missing
      value: Oops!
  Open:
      context: missing
      value: Open
  OpenAssessment:
      context: missing
      value: 'Open Assessment'
  OpenCards:
      context: missing
      value: 'Open Cards'
  OpenContent:
      context: missing
      value: 'Open Content'
  OpenGroup:
      context: missing
      value: 'Open Group'
  OpenGroupCanBeSearched:
      context: missing
      value: 'Open Group (Can be Searched)'
  Openings:
      context: missing
      value: Openings
  OpeningsAvailable:
      context: missing
      value: 'Openings available'
  OpenInNewTab:
      context: missing
      value: 'open in new tab'
  OpenLeaderboard:
      context: missing
      value: 'Open Leaderboard'
  OpenLearningPlan:
      context: missing
      value: 'Open Learning Plan'
  OpenQuiz:
      context: missing
      value: 'Open Quiz'
  OpenSkillsAssessment:
      context: missing
      value: 'Open Skills Assessment'
  OpenToRelocate:
      context: missing
      value: 'Open to relocate'
  OpenType:
      context: missing
      value: 'Open {type}'
  OpportunityType:
      context: missing
      value: 'Opportunity Type'
  Option:
      context: missing
      value: Option
  Optional:
      context: missing
      value: Optional
  OptionalField:
      context: missing
      value: 'Optional field'
  Options:
      context: missing
      value: Options
  Or:
      context: missing
      value: or
  Order:
      context: missing
      value: Order
  OrderDetails:
      context: missing
      value: 'Order Details'
  OrEnterCode:
      context: missing
      value: 'Or enter code'
  Organization:
      context: missing
      value: Organization
  OrganizationPrescribed:
      context: missing
      value: 'Organization prescribed'
  OriginalValue:
      context: missing
      value: 'Original Value'
  OrLoginWith:
      context: missing
      value: 'or login with'
  OrSignUpUsing:
      context: missing
      value: 'Or Sign up using'
  OrSignupUsing:
      context: missing
      value: 'Or Signup Using'
  Other:
      context: missing
      value: Other
  OtherChannelToExploreAndFollow:
      context: missing
      value: 'other channel to explore and follow'
  OtherDetailsHasBeenSavedSuccessfully:
      context: missing
      value: 'Other Details has been saved successfully.'
  OtherGroupToExploreAndJoin:
      context: missing
      value: 'other group to explore and join'
  OtherPublicGroups:
      context: missing
      value: 'Other public groups'
  Others:
      context: missing
      value: others
  OtherUsersToExploreAndFollow:
      context: missing
      value: 'other users to explore and follow'
  Otp:
      context: missing
      value: OTP
  OtpMatched:
      context: missing
      value: 'OTP Matched'
  OutYourPasswordUseTheButtonBelowToAutomaticallySignYourselfIn:
      context: missing
      value: 'out your password. Use the button below to automatically sign yourself in.'
  Over5Years:
      context: missing
      value: 'Over 5 years'
  Overdue:
      context: missing
      value: Overdue
  Overview:
      context: missing
      value: Overview
  OverviewOfActivitiesPerformedByUsersOfThisOrganization:
      context: missing
      value: 'Overview of activities performed by users of this organization'
  OwnedBy:
      context: missing
      value: 'Owned by'
  OwnedByMe:
      context: missing
      value: 'Owned by me'
  Owner:
      context: missing
      value: Owner
  PaceRequired:
      context: missing
      value: 'Pace Required'
  PageNotFound:
      context: missing
      value: 'Page not found'
  Paid:
      context: missing
      value: Paid
  PaidContent:
      context: missing
      value: 'Paid Content'
  Participants:
      context: missing
      value: Participants
  Pass:
      context: missing
      value: Pass
  Passed:
      context: missing
      value: Passed
  PassingCriteria:
      context: missing
      value: 'Passing Criteria'
  Password:
      context: missing
      value: Password
  PasswordAndConfirmPasswordAreNotTheSame:
      context: missing
      value: 'Password and Confirm password are not the same.'
  PasswordCannotBeSameAsPrevious8Password:
      context: missing
      value: 'Password cannot be same as previous 8 password.'
  PasswordDoesNotSatisfyTheGivenCriteria:
      context: missing
      value: 'Password does not satisfy the given criteria.'
  PasswordExpired:
      context: missing
      value: 'Password Expired'
  PasswordIsCaseSensitive:
      context: missing
      value: 'Password is case sensitive.'
  PasswordIsInvalid:
      context: missing
      value: 'Password is invalid'
  PasswordReset:
      context: missing
      value: 'Password Reset'
  Past:
      context: missing
      value: past
  PasteLinkHere:
      context: missing
      value: 'Paste link here ...'
  PasteLinkHeremandatory:
      context: missing
      value: 'Paste link here(Mandatory)'
  PastEmploymentDetails:
      context: missing
      value: 'Past Employment Details'
  Pathway:
      context: missing
      value: Pathway
  pathway:
      context: pathway keyword as type of smartcard, lowercase is needed in order to complete partial strings translations.
      value: pathway
  PathwayBadges:
      context: missing
      value: 'Pathway Badges'
  PathwayDescription:
      context: missing
      value: 'Pathway Description'
  PathwayEditor:
      context: missing
      value: 'Pathway editor'
  Pathways:
      context: missing
      value: Pathways
  Pathways2:
      context: missing
      value: Pathways
  PathwayTitle:
      context: missing
      value: 'Pathway Title'
  PathwayDynamicTitle:
      context: informative text Pathway Title
      value: 'Pathway title: {title}'
  PathwayTitlemandatory:
      context: missing
      value: 'Pathway Title(Mandatory)'
  PauseAnnouncementCarousel:
      context: missing
      value: 'Pause announcement carousel'
  Payer:
      context: missing
      value: Payer
  PaymentFailed:
      context: missing
      value: 'Payment Failed'
  PaymentSuccessful:
      context: missing
      value: 'Payment Successful'
  PaymentSuccessfull:
      context: missing
      value: 'Payment Successfull'
  Pdf:
      context: missing
      value: pdf
  PdfFilePreview:
      context: missing
      value: 'Pdf File Preview'
  PdfJpegPngUpto2Mb:
      context: missing
      value: 'pdf, jpeg, png upto 2 mb'
  PdfViewer:
      context: missing
      value: 'PDF Viewer'
  Peer:
      context: missing
      value: Peer
  PeerAssessments:
      context: missing
      value: 'PEER ASSESSMENTS'
  Peers:
      context: missing
      value: Peers
  Pending:
      context: missing
      value: Pending
  PendingApproval:
      context: missing
      value: 'Pending Approval'
  PendingForAdminApproval:
      context: missing
      value: 'Pending for Admin Approval'
  PendingInvitation:
      context: missing
      value: 'Pending Invitation'
  PendingMembers:
      context: missing
      value: 'Pending Members'
  PendingUserSuccessfullyRemovedFromGroup:
      context: missing
      value: 'Pending user successfully removed from Group'
  People:
      context: missing
      value: People
  PeopleByName:
      context: missing
      value: 'People (By Name)'
  PeopleBySkill:
      context: missing
      value: 'People (By Skill)'
  PeopleFollowingMe:
      context: missing
      value: 'People following me'
  PeopleIAmFollowing:
      context: missing
      value: 'People I am following'
  Per:
      context: missing
      value: per
  PermanentlyDelete:
      context: missing
      value: 'Permanently Delete'
  Personal:
      context: missing
      value: Personal
  Photo:
      context: missing
      value: Photo
  PhotosByUnsplash:
      context: missing
      value: 'Photos by Unsplash'
  PickALanguage:
      context: missing
      value: 'Pick a language'
  PictureOfFullname:
      context: missing
      value: 'Picture of {fullName}'
  Pin:
      context: missing
      value: pin
  Pincode:
      context: missing
      value: Pincode
  PinCode:
      context: missing
      value: 'Pin Code'
  PinToTop:
      context: missing
      value: 'Pin to top'
  Play:
      context: missing
      value: Play
  PlayAnnouncementCarousel:
      context: missing
      value: 'Play announcement carousel'
  PlayStore:
      context: missing
      value: 'play store'
  PlayVideo:
      context: missing
      value: 'Play video'
  PlayVideoOf:
      context: missing
      value: 'Play video of'
  PlayVideoOfDesc:
      context: missing
      value: 'Play video of {desc}'
  PleaseAcceptTermsAndConditionsBeforeSigningIn:
      context: missing
      value: 'Please accept terms and conditions before signing in.'
  PleaseAddADescriptionForYourGroup:
      context: missing
      value: 'Please add a description for your group'
  PleaseAddAnEndDate:
      context: missing
      value: 'Please add an end date'
  PleaseAddCardType:
      context: missing
      value: 'Please add Card Type'
  PleaseAddContentType:
      context: missing
      value: 'Please add Content Type'
  PleaseAddCorrectAnswer:
      context: missing
      value: 'Please add correct answer!'
  PleaseAddDescriptionForYourSkill:
      context: missing
      value: 'Please add description for your Skill'
  PleaseAddOneOrMoreLearningGoalsToViewRecommendations:
      context: missing
      value: 'Please add one or more learning goal(s) to view recommendations'
  PleaseAddOption:
      context: missing
      value: 'Please Add Option'
  PleaseAddYourMessage:
      context: missing
      value: 'Please add your message ...'
  PleaseAgreeToGeneralTermsAndConditions:
      context: missing
      value: 'Please agree to General Terms and Conditions'
  PleaseBeAwareThatAnyChangesMadeHereWillBeAutomaticallySaved:
      context: missing
      value: 'Please be aware that any changes made here will be automatically saved.'
  PleaseBeAwareThatDoingSoWillRemoveAllAssociatedStatisticsAndComments:
      context: missing
      value: 'Please be aware that doing so will remove all associated statistics and comments.'
  PleaseBookAnAssessment:
      context: missing
      value: 'Please book an assessment'
  PleaseBookmarkYourJobRolesToGetHere:
      context: missing
      value: 'Please bookmark your job roles to get here'
  PleaseCheckAgainInSomeTimeAndThankYouForYourPatience:
      context: missing
      value: 'Please check again in some time and thank you for your patience.'
  PleaseClickOnTheGeneralTermsAndConditionsToProceedFurther:
      context: missing
      value: '(Please click on the General Terms and Conditions to proceed further)'
  PleaseClickOnTheLinkInsideToVerifyYourAccount:
      context: missing
      value: 'Please click on the link inside to verify your account.'
  PleaseComeBackLater:
      context: missing
      value: 'Please come back later.'
  PleaseDescribeYourOtherReasonBeforeReporting:
      context: missing
      value: 'Please describe your Other reason before reporting.'
  PleaseDescribeYourReasonHere:
      context: missing
      value: 'Please describe your reason here.'
  PleaseDeselectDiffTopicsAndUpdate:
      context: missing
      value: 'Please deselect {diff} topics and update'
  PleaseDeselectOneTopicAndUpdate:
      context: missing
      value: 'Please deselect one topic and update'
  PleaseEnableSectionsToDisplayContentForThisGroup:
      context: missing
      value: 'Please enable sections to display content for this group'
  PleaseEnterADescription:
      context: missing
      value: 'Please enter a description.'
  PleaseEnterAScoreBetween0100:
      context: missing
      value: 'Please enter a score between 0 - 100.'
  PleaseEnterASectionTitleToCompleteTheCreationProcess:
      context: missing
      value: 'Please enter a section title to complete the creation process.'
  PleaseEnterASectionTitleToCreateNewSection:
      context: missing
      value: 'Please enter a section title to create new section'
  PleaseEnterATitle:
      context: missing
      value: 'Please enter a title'
  PleaseEnterAValidEmail:
      context: missing
      value: 'Please enter a valid email'
  PleaseEnterCorrectCompanyName:
      context: missing
      value: 'Please enter correct company name'
  PleaseEnterDescription:
      context: missing
      value: 'Please Enter Description'
  PleaseEnterOnlyCompanyEmail:
      context: missing
      value: 'Please enter only company email.'
  PleaseEnterTheScore:
      context: missing
      value: 'Please enter the score'
  PleaseEnterTheTitle:
      context: missing
      value: 'Please enter the title'
  PleaseEnterValidAddress:
      context: missing
      value: 'Please enter valid address'
  PleaseEnterValidCompanyName:
      context: missing
      value: 'Please enter valid company name'
  PleaseEnterValidCourseName:
      context: missing
      value: 'Please enter valid course name'
  PleaseEnterValidDate:
      context: missing
      value: 'Please enter valid date'
  PleaseEnterValidDesignationName:
      context: missing
      value: 'Please enter valid designation name'
  PleaseEnterValidEmailAddress:
      context: missing
      value: 'Please enter valid email address'
  PleaseEnterValidEmployerName:
      context: missing
      value: 'Please enter valid employer name'
  PleaseEnterValidFullName:
      context: missing
      value: 'Please enter valid full name'
  PleaseEnterValidMobileNumber:
      context: missing
      value: 'Please enter valid mobile number'
  PleaseEnterValidMobileNumberOrItIsNotVerified:
      context: missing
      value: 'Please enter valid mobile number or it is not verified'
  PleaseEnterValidNumber:
      context: missing
      value: 'Please enter valid number'
  PleaseEnterValidPincodeNumber:
      context: missing
      value: 'Please enter valid pincode number'
  PleaseEnterValidUniversityName:
      context: missing
      value: 'Please enter valid university name'
  PleaseEnterYourCompanyName:
      context: missing
      value: 'Please enter your company name'
  PleaseEnterYourDesignation:
      context: missing
      value: 'Please enter your designation'
  PleaseFillAllTheRequiredFields:
      context: missing
      value: 'Please fill all the required fields'
  PleaseMakePaymentToAccessTheCourse:
      context: missing
      value: 'Please make payment to access the course'
  PleaseMarkWhenDidYouObtainThisSkill:
      context: missing
      value: 'Please mark when did you obtain this skill.'
  PleaseNote:
      context: missing
      value: 'Please note'
  PleaseProvideDetailsAboutTheChangesDoneInThisVersi:
      context: missing
      value: 'Please provide details about the changes done in this version'
  PleaseReadAndAgreeTo:
      context: missing
      value: 'Please read and agree to'
  PleaseReadTheGeneralTermsAndConditions:
      context: missing
      value: 'Please read the General Terms and Conditions'
  PleaseRequestANewOne:
      context: missing
      value: 'Please request a new one.'
  PleaseRequestForTheTranscriptAfterSomeTime:
      context: missing
      value: 'Please request for the transcript after some time.'
  PleaseReviewYourSkillsUsingTheLink:
      context: missing
      value: 'Please review your skills using the {link}'
  PleaseReviewYourSkillsUsingTheSkillsAssessment:
      context: missing
      value: 'Please review your skills using the Skills Assessment!'
  PleaseSelectACarouselType:
      context: missing
      value: 'Please select a carousel type'
  PleaseSelectAChannelBelowToFollowAndPostTheContent:
      context: missing
      value: 'Please select a channel below to follow and post the content'
  PleaseSelectASkillNeedMinimum3Characters:
      context: missing
      value: 'Please select a skill. (need minimum 3 characters).'
  PleaseSelectAtLeastOneSkill:
      context: missing
      value: 'Please select at least one skill.'
  PleaseSelectAtLeastOneSmartcardAndThenAddItToThePathway:
      context: missing
      value: 'Please select at least one SmartCard and then add it to the Pathway'
  PleaseSelectBothLeaps:
      context: missing
      value: 'Please select both leaps!'
  PleaseSelectCategory:
      context: missing
      value: 'Please select category'
  PleaseSelectClc:
      context: missing
      value: 'Please select CLC'
  PleaseSelectInternalContentTypeToViewResults:
      context: missing
      value: 'Please select Internal content type to view results.'
  PleaseSelectInternalOrContentLibraryContentTypeToViewResults:
      context: missing
      value: 'Please select Internal or Content Library content type to view results.'
  PleaseSelectMonth:
      context: missing
      value: 'Please select month'
  PleaseSelectSkillLevel:
      context: missing
      value: 'Please Select skill level'
  PleaseSelectSkillName:
      context: missing
      value: 'Please select skill name'
  PleaseSelectSkillToProceed:
      context: missing
      value: 'Please select skill to proceed.'
  PleaseSelectTechnology:
      context: missing
      value: 'please select Technology'
  PleaseSelectTheCategory:
      context: missing
      value: 'Please select the category'
  PleaseSelectTheCategoryWhichIsApplicableToYou:
      context: missing
      value: 'Please select the category which is applicable to you '
  PleaseSelectTheDateOnWhichFirstAssignmentWillBeDue:
      context: missing
      value: 'Please select the date on which First Assignment will be due'
  PleaseSelectTheDateOnWhichFirstAssignmentWillBeMade:
      context: missing
      value: 'Please select the date on which First Assignment will be made'
  PleaseSelectTheJobSector:
      context: missing
      value: 'Please select the job sector'
  PleaseSelectTheLeapIfUserFailsTheQuiz:
      context: missing
      value: 'Please select the leap if user fails the quiz'
  PleaseSelectTheLeapIfUserPassesTheQuiz:
      context: missing
      value: 'Please select the leap if user passes the quiz'
  PleaseSelectTheNatureOfRole:
      context: missing
      value: 'Please select the Nature of Role'
  PleaseSelectTheSkillLevel:
      context: missing
      value: 'Please select the {activeCredential} level.'
  PleaseSelectTheTrainerCategoryAppliesToYou:
      context: missing
      value: 'Please select the trainer category applies to you'
  PleaseSelectValidCoLeadCenter:
      context: missing
      value: 'Please select valid Co-lead center'
  PleaseSelectValidDateRange:
      context: missing
      value: 'Please select valid date range'
  PleaseSelectValidJobType:
      context: missing
      value: 'Please select valid job type'
  PleaseSelectValidLeadCenter:
      context: missing
      value: 'Please select valid lead center'
  PleaseSelectValidQualification:
      context: missing
      value: 'Please select valid qualification'
  PleaseSelectValidTechnology:
      context: missing
      value: 'Please select valid technology'
  PleaseSelectValidTrainerCategory:
      context: missing
      value: 'Please select valid trainer category'
  PleaseSelectYear:
      context: missing
      value: 'Please select year'
  PleaseSelectYourResourceCenterOrAffiliatedSource:
      context: missing
      value: 'Please select your resource center or affiliated source'
  PleaseSpecifyNumberOfOpenings:
      context: missing
      value: 'Please specify number of openings'
  PleaseTrySearchingSomethingElse:
      context: missing
      value: 'Please try searching something else'
  PleaseTypeAmountPressAdd:
      context: missing
      value: 'Please type amount &amp; press Add'
  PleaseUpdateYourDetails:
      context: missing
      value: 'Please update your details'
  PleaseUploadAThumbnail:
      context: missing
      value: 'Please upload a thumbnail'
  PleaseUploadValidFile:
      context: missing
      value: 'Please upload valid file'
  PleaseVerifyTheNumber:
      context: missing
      value: 'Please verify the number'
  PleaseVisitLinkUrl:
      context: missing
      value: 'Please visit link url'
  PleaseWait:
      context: missing
      value: 'Please wait...'
  Points:
      context: missing
      value: Points
  Points2:
      context: missing
      value: Points
  PointsAccruedBasedOnActivitiesCompletedOnPlatform:
      context: missing
      value: 'Points accrued based on activities completed on platform'
  Poland:
      context: missing
      value: Poland
  Poll:
      context: missing
      value: Poll
  Polloptiontext:
      context: missing
      value: '{pollOptionText}'
  PositionsavailableOpening:
      context: missing
      value: '{positionsAvailable} opening'
  Post:
      context: missing
      value: ' Post'
  PostedBy:
      context: missing
      value: 'Posted By:'
  PostedOn:
      context: missing
      value: 'Posted on'
  Posting:
      context: missing
      value: Posting...
  PostingToPrivateChannelWillNotMakeCardPrivate:
      context: missing
      value: 'Posting to private channel will not make card private.'
  PostingToPrivateChannelWillNotMakeThisCardPrivate:
      context: missing
      value: 'Posting to private channel will not make this card private.'
  Posts:
      context: missing
      value: ' Posts'
  PostTo:
      context: missing
      value: 'Post to'
  PostToChannel:
      context: When some card is posted to channel
      value: 'Post to Channel'
  PostToChannels:
      context: missing
      value: 'Post to Channel(s)'
  PostToProfile:
      context: missing
      value: 'Post to Profile'
  PoweredByEdcast:
      context: missing
      value: 'powered by edcast'
  PreferredLanguage:
      context: missing
      value: 'Preferred Language'
  PreparingLearning:
      context: missing
      value: 'Preparing learning...'
  Present:
      context: missing
      value: present
  PreSignUp:
      context: missing
      value: 'Pre-Sign Up'
  PressReturnEnterToAddTheTag:
      context: missing
      value: 'Press Return (Enter) to add the tag.'
  Prev:
      context: missing
      value: Prev
  Preview:
      context: missing
      value: Preview
  PreviewData:
      context: missing
      value: 'Preview Data'
  PreviewMode:
      context: missing
      value: 'preview mode'
  Previous:
      context: missing
      value: previous
  PreviousCard:
      context: missing
      value: 'Previous Card'
  PreviousSlide:
      context: missing
      value: 'Previous slide'
  PreviousVersions:
      context: missing
      value: 'Previous Versions'
  Price:
      context: missing
      value: 'PRICE :'
  Price2:
      context: missing
      value: 'Price:'
  Price3:
      context: missing
      value: 'Price:'
  Pricing:
      context: missing
      value: Pricing
  PrivacyPolicy:
      context: missing
      value: 'Privacy Policy'
  Private:
      context: missing
      value: Private
  PrivateChannel:
      context: missing
      value: 'Private Channel'
  PrivateContent:
      context: missing
      value: 'Private content'
  PrivateContentMsg:
      context: warning message
      value: "This content is private and therefore won't be visible to the user this is shared with. You can change this in your SmartCard settings."
  PrivateView:
      context: missing
      value: 'Private View'
  Processed:
      context: missing
      value: Processed
  Processing:
      context: missing
      value: Processing...
  Product:
      context: missing
      value: Product
  ProfessionalExperienceYouHaveGainedFromPreviousJobsInternshipsOrContractPositions:
      context: missing
      value: 'Professional experience you have gained from previous jobs, internships or contract positions.'
  Profile:
      context: missing
      value: Profile
  ProfileBanner:
      context: missing
      value: 'Profile Banner'
  ProfileDetails:
      context: missing
      value: 'Profile details'
  ProfileImage:
      context: missing
      value: 'Profile Image'
  ProfilePicture:
      context: missing
      value: 'Profile picture'
  ProfilePictureName:
      context: missing
      value: 'Profile picture : {name}'
  ProfilePictureUserimg:
      context: missing
      value: 'Profile picture {userImg}'
  ProfileSettings:
      context: missing
      value: 'Profile Settings'
  Progress:
      context: missing
      value: Progress
  ProgressiveUnlocking:
      context: missing
      value: 'Progressive Unlocking'
  Project:
      context: missing
      value: Project
  ProjectedLearningHoursAtEndOfClc:
      context: missing
      value: 'Projected learning hours at end of CLC'
  Projects:
      context: missing
      value: Projects
  Promote:
      context: missing
      value: Promote
  PromotedToGroupLeader:
      context: missing
      value: 'Promoted to Group Leader'
  PromoteToGroupLeader:
      context: missing
      value: 'Promote to Group Leader'
  Promotion:
      context: missing
      value: Promotion
  PromotionalPrice:
      context: missing
      value: 'Promotional Price:'
  PromotionPrice:
      context: missing
      value: 'Promotion Price'
  PromoValidUptoDiscountvalidupto:
      context: missing
      value: 'Promo valid upto: {discountValidUpto}'
  ProvideAHighLevelOverviewToHelpCandidatesDecideIfT:
      context: missing
      value: 'Provide a high level overview to help candidates decide if they are interested.'
  Provider:
      context: missing
      value: Provider
  Provider2:
      context: missing
      value: Provider
  ProviderImage:
      context: missing
      value: 'Provider Image'
  ProviderLogo:
      context: missing
      value: 'Provider Logo'
  Pts:
      context: missing
      value: pts
  Public:
      context: missing
      value: Public
  PublicView:
      context: missing
      value: 'Public View'
  Publish:
      context: missing
      value: Publish
  Published:
      context: missing
      value: Published
  PublishedContent:
      context: missing
      value: 'Published content'
  PublishedDate:
      context: missing
      value: 'Published Date'
  PublishedOn:
      context: missing
      value: 'Published On'
  PublishedOnDate:
      context: Label describing date of publication, date need to be passed as variable
      value: 'Published on {date}'
  PublishedToName:
      context: missing
      value: 'Published to {name}.'
  Publishing:
      context: missing
      value: Publishing...
  Purchased:
      context: missing
      value: Purchased
  PurchaseOptions:
      context: missing
      value: 'Purchase options'
  PushNotificationSentToYourDevicepleaseCheck:
      context: missing
      value: 'Push Notification sent to your device.Please check'
  Qualification:
      context: missing
      value: Qualification
  Quarterly:
      context: reassignment frecuency option label
      value: Quarterly
  Quiz:
      context: missing
      value: Quiz
  Raisehand:
      context: missing
      value: raiseHand
  RaiseHands:
      context: missing
      value: 'Raise Hands'
  RaiseYourHand:
      context: missing
      value: 'Raise Your Hand'
  RaiseYourHandToAskTheHostToAllowYouToCoBroadcastInTheLivestream:
      context: missing
      value: 'Raise your hand to ask the host to allow you to co-broadcast in the Livestream.'
  RateContent:
      context: missing
      value: 'Rate Content'
  Rating:
      context: missing
      value: Rating
  Ratings:
      context: missing
      value: Ratings
  ReachedMaximumLimitToAddLearningGoals:
      context: missing
      value: 'Reached maximum limit to add learning goals'
  ReadLess:
      context: missing
      value: 'read less'
  ReadLikeOrCommentOnTheFeedCardToStartScoring:
      context: missing
      value: 'Read, like or comment on the feed card to start scoring'
  ReadMore:
      context: missing
      value: 'Read More'
  ReasonForRejectionoptional:
      context: missing
      value: 'Reason for Rejection(Optional)'
  ReasonToReport:
      context: missing
      value: 'Reason to Report'
  ReassignmentFrequency:
      context: missing
      value: 'Reassignment Frequency'
  ReceiveContentRecommendationsBasedOnTheSelectedTopics:
      context: missing
      value: 'Receive content recommendations based on the selected topics.'
  ReceiveContentRecommendationsFromSelectedSources:
      context: missing
      value: 'Receive content recommendations from selected sources.'
  ReceiveContentRecommendationsOfTheSelectedTypes:
      context: missing
      value: 'Receive content recommendations of the selected types.'
  ReceiveContentRecommendationsOnlyInTheSelectedLang:
      context: missing
      value: 'Receive content recommendations only in the selected languages.'
  ReceiveOtpOnMobile:
      context: missing
      value: 'Receive OTP on mobile'
  ReceiveReSetLinkOnEmail:
      context: missing
      value: 'Receive re-set link on email'
  Recent:
      context: missing
      value: Recent
  RecentActivity:
      context: missing
      value: 'Recent Activity'
  RechargeSuccessful:
      context: missing
      value: 'Recharge successful'
  Recommend:
      context: missing
      value: Recommend
  RecommendASkill:
      context: missing
      value: 'Recommend a Skill'
  RecommendationsAreBasedOnYourSkills:
      context: missing
      value: 'Recommendations are based on your skills.'
  RecommendationsBasedOnAveragePeerRating:
      context: missing
      value: 'Recommendations Based On Average Peer Rating'
  RecommendationsBasedOnSkillsGaps:
      context: missing
      value: 'Recommendations Based On Skills Gaps'
  Recommended:
      context: missing
      value: Recommended
  RecommendedChannels:
      context: missing
      value: 'Recommended Channels'
  RecommendedIn:
      context: missing
      value: 'Recommended in'
  RecommendedLearning:
      context: missing
      value: 'Recommended Learning'
  RecommendedLearningFor:
      context: missing
      value: 'Recommended learning for'
  RecommendedPathways:
      context: missing
      value: 'Recommended Pathways'
  RecommendedPeople:
      context: missing
      value: 'Recommended People'
  RecommendedRoles:
      context: missing
      value: 'Recommended Roles'
  RecommendedSize:
      context: missing
      value: 'Recommended Size:'
  RecommendedSize480pxX320px:
      context: missing
      value: 'Recommended Size: 480px x 320px'
  RecommendedSkillLevel:
      context: missing
      value: 'Recommended Skill Level'
  RecommendSkill:
      context: missing
      value: 'Recommend Skill'
  RecommendToUpskill:
      context: missing
      value: 'Recommend to Upskill'
  RedirectToMembersPageMentionedInComment:
      context: missing
      value: "Redirect to member's page mentioned in comment"
  Refer:
      context: missing
      value: Refer
  ReferenceNumber:
      context: missing
      value: 'Reference number'
  RefineYourSearch:
      context: missing
      value: 'Refine your search'
  Register:
      context: missing
      value: Register
  Registered:
      context: missing
      value: Registered
  RegisteredOn:
      context: header label
      value: Registered On
  RegisterToGetTheDetails:
      context: missing
      value: 'Register to get the details'
  Registration:
      context: missing
      value: Registration
  RegistrationDetails:
      context: missing
      value: 'Registration Details'
  RegistrationDenied:
      context: missing
      value: 'Registration Denied'
  RegistrationImage:
      context: missing
      value: 'registration image'
  RegistrationType:
      context: missing
      value: 'Registration Type'
  Reject:
      context: missing
      value: Reject
  RejectCard:
      context: missing
      value: 'Reject Card'
  Rejected:
      context: missing
      value: Rejected
  RejectedCardFromChannelSuccessfully:
      context: missing
      value: 'Rejected card from channel successfully.'
  Rejecting:
      context: missing
      value: Rejecting...
  Rejectinvite:
      context: missing
      value: rejectInvite
  RejectReason:
      context: missing
      value: 'Reject Reason'
  RelatedChannelsToThisTopic:
      context: missing
      value: 'Related channels to this topic'
  RelatedSkills:
      context: missing
      value: 'Related Skills'
  RelatedSmartcards:
      context: missing
      value: 'Related SmartCards'
  Relevance:
      context: missing
      value: Relevance
  Relevancy:
      context: missing
      value: Relevancy
  RemainingCharacters:
      context: missing
      value: 'Remaining Characters: '
  Remarks:
      context: missing
      value: Remarks
  RemindMeLater:
      context: missing
      value: 'Remind me later'
  Remote:
      context: missing
      value: Remote
  RemotePossible:
      context: missing
      value: 'Remote possible'
  RemoteWorkPossible:
      context: missing
      value: 'Remote Work Possible'
  Remove:
      context: missing
      value: Remove
  RemoveBanner:
      context: missing
      value: 'Remove Banner'
  RemoveCard:
      context: missing
      value: 'Remove Card'
  RemovedContentFromChannelSuccessfully:
      context: missing
      value: 'Removed content from channel successfully!'
  RemovedItemFromSectionSuccessfully:
      context: confirmation message shown after successfully removing of content from section
      value: 'Removed item from section successfully!'
  RemoveFeaturedCard:
      context: missing
      value: 'Remove Featured Card'
  RemoveFilter:
      context: missing
      value: 'Remove Filter'
  RemoveFromChannel:
      context: missing
      value: 'Remove from channel'
  RemoveFromFeatured:
      context: missing
      value: 'Remove from Featured'
  RemoveFromFeaturedSection:
      context: missing
      value: 'Remove from featured section'
  RemoveFromGroup:
      context: missing
      value: 'Remove from Group'
  RemoveFromHeadertext:
      context: missing
      value: 'Remove from {headerText}'
  RemoveFromProfile:
      context: missing
      value: 'Remove from Profile'
  RemoveFromThisSection:
      context: content drop-down menu option that is used to remove content from section
      value: 'Remove from this section'
  RemoveGroup:
      context: missing
      value: 'remove group'
  RemoveImage:
      context: missing
      value: 'Remove Image'
  RemoveItem:
      context: missing
      value: 'Remove Item'
  RemoveOrder:
      context: missing
      value: 'Remove order'
  RemoveSection:
      context: Section drop-down menu option that is used to remove section
      value: 'Remove Section'
  RemoveSelection:
      context: missing
      value: 'Remove Selection'
  RemoveSkillFromYourLearningGoals:
      context: missing
      value: 'Remove skill from your learning goals'
  RemoveSmartcard:
      context: missing
      value: 'Remove SmartCard'
  RemoveTagnameTag:
      context: missing
      value: 'Remove {tagName} tag'
  RemoveUser:
      context: missing
      value: 'remove user'
  Removing:
      context: missing
      value: Removing...
  ReplaceFile:
      context: missing
      value: 'Replace File'
  ReplaceImage:
      context: Change the current image
      value: 'Replace image.'
  ReplaceImage2:
      context: Change the current image
      value: Replace image
  ReplaceVideoThumbnail:
      context: missing
      value: 'replace video thumbnail'
  Report:
      context: missing
      value: Report
  Reportees:
      context: missing
      value: Reportees
  ReportIt:
      context: missing
      value: 'Report it'
  Reports:
      context: missing
      value: Reports
  RequestACopy:
      context: missing
      value: 'Request a copy'
  RequestId:
      context: missing
      value: 'Request ID'
  RequestingDataFromTheApis:
      context: missing
      value: 'Requesting data from the APIs'
  RequestNewPasswordReset:
      context: missing
      value: 'Request New Password Reset'
  RequestSent:
      context: missing
      value: 'Request Sent'
  Required:
      context: missing
      value: Required
  RequiredField:
      context: missing
      value: 'Required field'
  RequiredLearning:
      context: missing
      value: 'Required Learning'
  Requirements:
      context: missing
      value: Requirements
  RescheduleAssessment:
      context: missing
      value: 'Reschedule Assessment'
  RescheduleBooking:
      context: missing
      value: 'Reschedule Booking?'
  ResendOtp:
      context: missing
      value: 'Resend OTP?'
  Reset:
      context: missing
      value: Reset
  ResetDueDate:
      context: missing
      value: 'Reset due date'
  ResetPassword:
      context: missing
      value: 'RESET PASSWORD'
  ResetStartDate:
      context: missing
      value: 'Reset start date'
  ResourceCenterrc:
      context: missing
      value: 'Resource Center(RC)'
  ResourceDescription:
      context: missing
      value: 'resource description'
  Response:
      context: missing
      value: Response
  Responses:
      context: missing
      value: Responses
  RestrictContentToUserGroup:
      context: missing
      value: 'Restrict content to user, group'
  RestrictedTo:
      context: missing
      value: 'Restricted to'
  RestrictTo:
      context: missing
      value: 'Restrict to'
  Result:
      context: missing
      value: result
  Results:
      context: missing
      value: results
  ResultsFor:
      context: missing
      value: 'Results for '
  ResultsCount:
      context: Result label for search result with counter
      value: 'Results ({count})'
  Retake:
      context: missing
      value: Retake
  ReturnToTheHomePage:
      context: missing
      value: 'Return to the home page'
  ReviewingYourLearningGoals:
      context: missing
      value: 'reviewing your learning goals.'
  Role:
      context: text info for user role
      value: Role
  RoleoptionsMatchesFound:
      context: missing
      value: '{roleOptions} matches found'
  Rotate:
      context: missing
      value: Rotate
  RotateImageToLeft:
      context: missing
      value: 'Rotate Image to Left'
  RotateImageToRight:
      context: missing
      value: 'Rotate Image to Right'
  Rotations:
      context: missing
      value: Rotations
  Salaried:
      context: missing
      value: Salaried
  Salary:
      context: missing
      value: Salary
  Sales:
      context: missing
      value: Sales
  SampleJsonResponse:
      context: missing
      value: 'Sample JSON response'
  Save:
      context: missing
      value: Save
  SaveAsDraft:
      context: missing
      value: 'Save As Draft'
  SaveAssessment:
      context: missing
      value: 'Save Assessment'
  SaveAssignment:
      context: missing
      value: 'Save assignment'
  SaveBadge:
      context: missing
      value: 'Save Badge'
  SaveChanges:
      context: missing
      value: 'Save Changes'
  SaveLater:
      context: missing
      value: 'save later'
  SaveProceed:
      context: missing
      value: 'Save &amp;amp; Proceed'
  SaveSkill:
      context: missing
      value: 'Save skill'
  Saving:
      context: missing
      value: Saving...
  Sc:
      context: missing
      value: SC
  ScanResume:
      context: missing
      value: 'Scan Resume'
  ScheduledAssessmentSlots:
      context: missing
      value: 'Scheduled Assessment Slots'
  Score:
      context: missing
      value: Score
  Score2:
      context: missing
      value: 'Score :'
  Scorm:
      context: refer to card type SCORM, capitalize is required
      value: Scorm
  scorm:
      context: refer to card type SCORM, lowercase is required
      value: scorm
  SCORM:
      context: refer to card type SCORM, uppercase is required
      value: SCORM
  ScrollRight:
      context: missing
      value: 'Scroll right'
  SemiAnnually:
      context: reassignment frecuency option label
      value: Semi-Annually
  Search:
      context: missing
      value: Search
  Search2:
      context: missing
      value: 'Search ...'
  Search3:
      context: missing
      value: Search
  SearchAndSelectCard:
      context: missing
      value: 'Search and select Card'
  SearchAndSelectChannel:
      context: missing
      value: 'Search and select Channel'
  SearchAndSelectContentSources:
      context: missing
      value: 'Search and select content sources'
  SearchAndSelectContentTopics:
      context: missing
      value: 'Search and select content topics'
  SearchAndSelectContentTypesEgvideoPoll:
      context: missing
      value: 'Search and select content types eg.video, poll'
  SearchAndSelectJobFamily:
      context: missing
      value: 'Search and select job family'
  SearchAndSelectLanguagesEgenglishHindi:
      context: missing
      value: 'Search and select languages eg.English, Hindi'
  SearchAuthor:
      context: missing
      value: 'Search Author...'
  SearchBy:
      context: missing
      value: 'Search By'
  SearchByAcceptedCriteriaToFindContentHere:
      context: missing
      value: 'Search by {accepted criteria to find content here}.'
  SearchByCardNameToFindAndAddContentToThisSection:
      context: description text to hint user about action need taken to add content in section
      value: 'Search by Card name to find and add content to this section'
  SearchByCardTitle:
      context: missing
      value: 'Search By Card Title'
  SearchByChannelNameToFindAndAddChannelToThisSection:
      context: missing
      value: 'Search by Channel name to find and add channel to this section'
  SearchByEmail:
      context: missing
      value: 'Search by email...'
  SearchByEmail2:
      context: missing
      value: 'Search by email'
  SearchByUserName:
      context: missing
      value: 'Search By User Name'
  SearchChannel:
      context: missing
      value: 'Search Channel'
  SearchChannels:
      context: missing
      value: 'Search Channels...'
  SearchContent:
      context: missing
      value: 'Search Content...'
  SearchContent2:
      context: missing
      value: 'Search content'
  SearchForChannels:
      context: missing
      value: 'Search for channels'
  SearchForExistingSmartcards:
      context: missing
      value: 'Search for existing SmartCards...'
  SearchForGroupMembers:
      context: missing
      value: 'Search for Group Members'
  SearchForGroups:
      context: missing
      value: 'Search for groups'
  SearchForPeople:
      context: missing
      value: 'Search for People'
  SearchForRolesInYourOrganization:
      context: missing
      value: 'Search for roles in your organization'
  EnteringDataInInputFieldWillUpdateContent:
      context: Hidden instruction to user before user provide input to input field
      value: 'On entering data into the following input field, the content will update below'
  SearchForUsers:
      context: missing
      value: 'Search for users'
  SearchGroup:
      context: missing
      value: 'Search Group'
  SearchGroups:
      context: missing
      value: 'Search Groups...'
  SearchHere:
      context: missing
      value: 'Search here...'
  Searching:
      context: missing
      value: 'Searching ...'
  SearchJobRole:
      context: missing
      value: 'Search Job Role'
  SearchJourney:
      context: missing
      value: 'Search Journey'
  SearchLanguages:
      context: missing
      value: 'Search Languages'
  SearchLocations:
      context: missing
      value: 'Search locations'
  SearchManagersPeers:
      context: missing
      value: 'Search Managers / Peers'
  SearchPathway:
      context: missing
      value: 'Search Pathway'
  SearchPeopleByNameAndSkill:
      context: missing
      value: 'Search people by name and skill'
  SearchPeopleByNameSkill:
      context: missing
      value: 'Search people by name &amp;amp; skill...'
  SearchPhotos:
      context: missing
      value: 'Search Photos...'
  SearchResultCategories:
      context: missing
      value: 'Search result categories'
  SearchResults:
      context: missing
      value: 'Search Results'
  SearchRolesMinimum3CharactersRequired:
      context: missing
      value: 'Search Roles (minimum 3 characters required)'
  SearchSelectAChannel:
      context: missing
      value: 'Search &amp;amp; Select a Channel'
  SearchSelectAChannel2:
      context: missing
      value: 'Search &amp; Select a Channel'
  SearchSkills:
      context: missing
      value: 'Search Skills...'
  SearchSkills2:
      context: missing
      value: 'Search skills'
  SearchSmartcard:
      context: missing
      value: 'Search SmartCard'
  SearchTags:
      context: missing
      value: 'Search tags'
  SearchTeamMembers:
      context: missing
      value: 'Search team members...'
  SearchTimeZones:
      context: missing
      value: 'Search Time Zones'
  SearchUser:
      context: missing
      value: 'Search User'
  SearchUsers:
      context: missing
      value: 'Search Users...'
  SearchYourJobRole:
      context: missing
      value: 'Search your Job Role'
  Seats:
      context: missing
      value: Seats
  SeatsAvailable:
      context: missing
      value: 'Seats Available'
  Seconds:
      context: missing
      value: seconds
  SecsS:
      context: missing
      value: '{secs} s'
  Section:
      context: missing
      value: Section
  SectionIndexsection:
      context: missing
      value: 'Section {indexSection}'
  SectionPosition:
      context: missing
      value: 'Section Position'
  Sections:
      context: missing
      value: Sections
  SeeDetails:
      context: missing
      value: 'See Details'
  SeeMore:
      context: missing
      value: 'See more'
  Select:
      context: missing
      value: Select
  UnSelect:
      context: this is option for unselecting all registers
      value: 'unselect'
  SelectACredlyBadge:
      context: missing
      value: 'Select a Credly Badge'
  SelectADate:
      context: missing
      value: 'Select a Date'
  SelectAFile:
      context: missing
      value: 'Select a File'
  SelectAJobFamilyRole:
      context: missing
      value: 'Select a Job Family &amp; Role'
  SelectALanguage:
      context: missing
      value: 'Select a Language'
  SelectAllGroups:
      context: missing
      value: 'Select All groups'
  SelectAllUsers:
      context: missing
      value: 'Select all Users'
  SelectAnOption:
      context: missing
      value: 'Select an option'
  SelectARange:
      context: missing
      value: 'Select a range'
  SelectASkill:
      context: missing
      value: 'Select a Skill'
  SelectASkillToAddToYourLearningGoals:
      context: missing
      value: 'Select a skill to add to your Learning goals'
  SelectAssignment:
      context: missing
      value: 'Select Assignment'
  SelectAssociatedSkills:
      context: missing
      value: 'Select Associated Skills'
  SelectATopicOrASkillThatWouldDriveYourRecommendations:
      context: missing
      value: 'Select a topic or a skill that would drive your recommendations'
  SelectBadgeIcon:
      context: missing
      value: 'Select badge icon'
  SelectCard:
      context: missing
      value: 'Select Card'
  SelectCardType:
      context: missing
      value: 'Select Card Type'
  SelectCarouselType:
      context: missing
      value: 'Select carousel type'
  SelectCategory:
      context: missing
      value: 'Select Category'
  SelectChannel:
      context: missing
      value: 'Select Channel'
  SelectCoLeadCenter:
      context: missing
      value: 'Select Co-Lead Center'
  SelectConferencingTool:
      context: missing
      value: 'Select Conferencing Tool'
  SelectContentType:
      context: missing
      value: 'Select Content Type'
  SelectCurrency:
      context: missing
      value: 'Select currency'
  SelectDate:
      context: missing
      value: 'Select Date'
  SelectDateErrorMsg:
      context: error message
      value: "Date can't be empty"
  SelectDay:
      context: missing
      value: 'Select Day'
  SelectDialCode:
      context: missing
      value: 'Select dial code'
  SelectDueDateFromCalendar:
      context: missing
      value: 'Select due Date from calendar'
  Selected:
      context: missing
      value: Selected
  SelectedBadge:
      context: missing
      value: 'Selected Badge'
  SelectedForAcception:
      context: missing
      value: 'Selected for Acception'
  SelectedGroups:
      context: missing
      value: 'Selected Groups'
  SelectFile:
      context: missing
      value: 'Select file'
  SelectGender:
      context: missing
      value: 'Select Gender'
  SelectGenderAsPerAadhaar:
      context: missing
      value: 'Select Gender as per Aadhaar'
  SelectGroup:
      context: missing
      value: 'select group'
  SelectGroupToInteractWith:
      context: missing
      value: 'Select Group to Interact with'
  SelectJobType:
      context: missing
      value: 'Select job type'
  SelectLanguage:
      context: missing
      value: 'Select Language'
  SelectLeadCenter:
      context: missing
      value: 'Select Lead Center'
  SelectLevelOfTheSkill:
      context: missing
      value: 'Select Level of the Skill'
  SelectLocation:
      context: missing
      value: 'Select Location'
  SelectMonths:
      context: missing
      value: 'Select Months'
  SelectMonthsOfTotalExperience:
      context: missing
      value: 'Select months of total experience'
  SelectNumberOfCards:
      context: missing
      value: 'Select number of cards'
  SelectOccupation:
      context: missing
      value: 'Select Occupation'
  SelectOneOrMoreIndividuals:
      context: missing
      value: 'Select one or more Individuals'
  SelectPathwayFromTheList:
      context: missing
      value: 'select Pathway from the list'
  SelectPreferedLanguage:
      context: missing
      value: 'Select Prefered Language'
  SelectPreferedTimezone:
      context: missing
      value: 'Select Prefered Timezone'
  SelectPriceInfo:
      context: missing
      value: 'Select price info'
  SelectSkillLevel:
      context: missing
      value: 'Select Skill Level'
  SelectSmartcard:
      context: missing
      value: 'Select SmartCard'
  SelectStartDateFromCalendar:
      context: missing
      value: 'Select start Date from calendar'
  SelectState:
      context: missing
      value: 'Select State'
  SelectStateAsPerAadhaar:
      context: missing
      value: 'Select State as per Aadhaar'
  SelectStatus:
      context: missing
      value: 'Select Status'
  SelectTechnology:
      context: missing
      value: 'Select Technology'
  SelectTheJourneyFirst:
      context: missing
      value: 'Select the Journey first'
  SelectTheOneWhichAppliesToYou:
      context: missing
      value: 'Select the one which applies to you'
  SelectTheOptionMostAppropriateForYou:
      context: missing
      value: 'Select the option most appropriate for you'
  SelectTimeZone:
      context: missing
      value: 'Select Time Zone'
  SelectUser:
      context: missing
      value: 'Select User'
  SelectValidDateUserShouldBeAbove18Year:
      context: missing
      value: 'select valid date (user should be above 18 year)'
  SelectWeeks:
      context: missing
      value: 'Select Weeks'
  SelectWeeksErrorMsg:
      context: error message
      value: 'Select weeks value!'
  SelectWhichCategoryYouFallUnder:
      context: missing
      value: 'Select which category you fall under'
  SelectYear:
      context: missing
      value: 'Select year'
  SelectYears:
      context: missing
      value: 'Select Years'
  SelectYearsOfTotalExperience:
      context: missing
      value: 'Select years of total experience'
  SelectYourDeviceType:
      context: missing
      value: 'Select your device type'
  SelectZone:
      context: missing
      value: 'select zone'
  SelfAssessedSkillLevel:
      context: missing
      value: 'Self Assessed Skill Level'
  SelfAssigned:
      context: missing
      value: 'Self Assigned'
  SelfDecclarationPdf:
      context: missing
      value: 'self decclaration pdf'
  SelfDeclaration:
      context: missing
      value: 'Self Declaration'
  SelfDeclarationImage:
      context: missing
      value: 'self declaration image'
  SelfEmployed:
      context: missing
      value: 'Self Employed'
  SelfPaced:
      context: missing
      value: 'Self Paced'
  SelfSkillsAssessment:
      context: missing
      value: 'Self Skills Assessment'
  Send:
      context: missing
      value: Send
  SendComment:
      context: missing
      value: 'Send Comment'
  SendingInvite:
      context: missing
      value: 'Sending invite...'
  SendingParametersToTheApis:
      context: missing
      value: 'Sending parameters to the APIs'
  SendInvitations:
      context: missing
      value: 'Send Invitations'
  SendMessage:
      context: missing
      value: 'send message...'
  SendPush:
      context: missing
      value: 'Send Push'
  SendPushAutomatically:
      context: missing
      value: 'Send push automatically'
  Sendraisehand:
      context: missing
      value: sendRaiseHand
  SendReminder:
      context: missing
      value: 'Send Reminder'
  SendReminders:
      context: missing
      value: 'Send Reminder(s)'
  SendTo:
      context: missing
      value: 'Send to:'
  SetCareerProfile:
      context: missing
      value: 'Set Career Profile'
  SetFilters:
      context: missing
      value: 'Set Filters'
  SetNewPassword:
      context: missing
      value: 'set new password'
  Settings:
      context: missing
      value: Settings
  Setup:
      context: missing
      value: Setup
  SetUpMultifactorAuthentication:
      context: missing
      value: 'Set up multifactor authentication'
  SetupOktaVerify:
      context: missing
      value: 'Setup Okta Verify'
  Share:
      context: card or content share
      value: Share
  ShareACardWithTheGroup:
      context: missing
      value: 'Share a card with the group'
  ShareAPrivateContent:
      context: missing
      value: 'Share a private content'
  ShareChannel:
      context: missing
      value: 'Share Channel'
  ShareContentWith:
      context: missing
      value: 'Share Content with ...'
  Shared:
      context: missing
      value: Shared
  SharedBy:
      context: missing
      value: 'Shared by'
  SharedByMe:
      context: missing
      value: 'Shared by me'
  SharedByMe2:
      context: missing
      value: 'Shared by Me'
  SharedOn:
      context: missing
      value: 'Shared On'
  SharedWithGroups:
      context: missing
      value: 'Shared with Groups'
  SharedWithMe:
      context: missing
      value: 'Shared with me'
  ShareFeedback:
      context: Share feedback modal title
      value: 'Share feedback'
  ShareItOn:
      context: missing
      value: 'Share it on'
  ShareOnLinkedin:
      context: missing
      value: 'Share on LinkedIn'
  Sharescreen:
      context: missing
      value: ShareScreen
  ShareThisContentToSpecificIndividualsOrGroupsFirst:
      context: missing
      value: 'Share this content to specific Individuals or Groups first.'
  ShareToTheMembers:
      context: missing
      value: 'Share to the members'
  ShareWith:
      context: missing
      value: 'Share with'
  ShareWithLinkedin:
      context: missing
      value: 'Share with Linkedin'
  ShareWithUsersGroupsAndChannels:
      context: missing
      value: 'Share with users, groups, and channels'
  ShareYourKnowledgeHere:
      context: missing
      value: 'Share your knowledge here...'
  SharingAndAssignment:
      context: missing
      value: 'Sharing and Assignment'
  Show:
      context: missing
      value: Show
  ShowAll:
      context: missing
      value: 'Show all'
  ShowCareerPath:
      context: missing
      value: 'Show Career Path'
  ShowComments:
      context: missing
      value: 'Show Comments'
  ShowedInterest:
      context: missing
      value: 'Showed interest'
  ShowedInterestInTitle:
      context: missing
      value: 'Showed interest in {title}'
  Showing1000OfTotalcount:
      context: missing
      value: 'showing 1000 of {totalCount}'
  Showing2000OfTotal:
      context: missing
      value: 'showing 2000 of {total}'
  ShowingTotalcountOfTotalcount:
      context: missing
      value: 'showing {totalCount} of {totalCount}'
  ShowLess:
      context: missing
      value: 'Show Less'
  ShowLikes:
      context: missing
      value: 'Show Likes'
  ShowMore:
      context: missing
      value: 'Show More'
  ShowMoreResults:
      context: missing
      value: 'Show More Results'
  ShownInYourChannelsHeaderAndItsCards:
      context: missing
      value: "Shown in your channel's header and its cards."
  ShowParticipants:
      context: missing
      value: 'Show Participants'
  ShowTags:
      context: missing
      value: 'Show Tags'
  SignUp:
      context: missing
      value: 'Sign up'
  SignupPageCheckbox:
      context: missing
      value: 'signup page checkbox'
  SignUpWith:
      context: missing
      value: 'Sign Up with'
  SimilarTopicIsAlreadyCreated:
      context: missing
      value: 'Similar topic is already created'
  SimilarTopicIsAlreadySelected:
      context: missing
      value: 'Similar topic is already selected'
  SimpleTable:
      context: missing
      value: 'simple table'
  Size:
      context: missing
      value: size
  Skill:
      context: missing
      value: Skill
  SkillAddedToLearningGoals:
      context: missing
      value: 'Skill added to Learning Goals!'
  SkillAddedUnderSkillsPassportSuccessfully:
      context: missing
      value: 'Skill added under skills passport successfully.'
  SkillClear:
      context: missing
      value: skill-clear
  Skillcoins:
      context: missing
      value: SkillCoins
  SkillCoins:
      context: missing
      value: 'Skill coins'
  Skillcoins2:
      context: missing
      value: Skillcoins
  Skillcoins3:
      context: missing
      value: Skillcoins
  SkillLevel:
      context: missing
      value: 'Skill Level'
  SkillLevelDescription:
      context: missing
      value: 'Skill Level Description'
  SkillModal:
      context: missing
      value: 'skill modal'
  SkillName:
      context: missing
      value: 'Skill Name'
  SkillPassport:
      context: missing
      value: 'Skill Passport'
  SkillRemovedFromLearningGoals:
      context: missing
      value: 'Skill removed from Learning Goals!'
  Skills:
      context: missing
      value: Skills
  SkillsAdded:
      context: missing
      value: 'Skills Added'
  SkillsAssessment:
      context: missing
      value: 'Skills Assessment'
  SkillsDirectory:
      context: missing
      value: 'Skills Directory'
  SkillsGap:
      context: missing
      value: 'Skills Gap'
  SkillsGraph:
      context: missing
      value: 'Skills Graph'
  SkillsMap:
      context: missing
      value: 'Skills Map'
  SkillsPassport:
      context: missing
      value: 'Skills Passport'
  SkillsPassportUpdated:
      context: missing
      value: 'Skills passport updated'
  SkillsThatYouHaveAcquiredDuringYourLearningJourney:
      context: missing
      value: 'Skills that you have acquired during your learning journey'
  SkillsToLearnForJobtitle:
      context: missing
      value: 'Skills to learn for {jobTitle}'
  SkillsWithoutProficiencyLevel:
      context: missing
      value: 'Skills without proficiency level'
  SkillTrends:
      context: missing
      value: 'Skill Trends'
  Skip:
      context: missing
      value: Skip
  SkipDontShowAgainContentIsRelevant:
      context: missing
      value: "Skip: Don't show again, content is relevant"
  SkipNotifyingUsers:
      context: missing
      value: 'Skip Notifying Users'
  Skipping:
      context: missing
      value: Skipping...
  SkypeCall:
      context: missing
      value: 'Skype Call'
  Smartbite:
      context: missing
      value: smartbite
  Smartbiteeditor:
      context: missing
      value: SmartBiteEditor
  Smartcard:
      context: missing
      value: SmartCard
  SmartCard:
      context: missing
      value: SmartCard
  SmartcardAddedToPathwayTitle:
      context: missing
      value: 'SmartCard added to Pathway \&quot;{title}\&quot;.'
  SmartcardInsightDismissedItWillNeverShowAgainInYourFeed:
      context: missing
      value: 'SmartCard Insight dismissed, It will never show again in your feed.'
  SmartcardJourneyPartEditor:
      context: missing
      value: 'SmartCard Journey Part Editor'
  SmartcardPosition:
      context: missing
      value: 'SmartCard Position'
  Smartcards:
      context: missing
      value: SmartCards
  SmartCards:
      context: missing
      value: SmartCards
  SmartcardsSelected:
      context: missing
      value: 'SmartCards selected'
  SmartcardsWillBeDisplayedHereWhenYouAddThem:
      context: missing
      value: 'SmartCards will be displayed here when you add them.'
  Sme:
      context: missing
      value: SME
  SmeFor:
      context: missing
      value: 'SME FOR'
  SnackbartypeDeletedItWillNeverShowAgain:
      context: missing
      value: '{snackBarType} deleted. It will never show again.'
  SnackbartypeArchiveSuccess:
      context: missing
      value: '{snackBarType} has been successfully archived.'
  SnackbartypeUnArchiveSuccess:
      context: missing
      value: '{snackBarType} has been successfully restored from the archive.'
  Social:
      context: missing
      value: Social
  SoftSkills:
      context: missing
      value: 'Soft Skills'
  SomethingWentWrongPleaseTryAgain:
      context: missing
      value: 'Something went wrong! Please try again'
  SomethingWentWrongPleaseTryAgainLater:
      context: missing
      value: 'Something went wrong! Please try again later.'
  SomethingWentWrongPleaseTryLater:
      context: missing
      value: 'Something went wrong! Please try later.'
  SorryNoTags:
      context: missing
      value: 'Sorry, no tags!'
  SorrySomeServerErrorPleaseTryAgainLater:
      context: missing
      value: 'Sorry, some server error! Please, try again later!'
  SorryTheContentYouAreTryingToPostIsFromUnapprovedWebsiteOrWords:
      context: 'This will be notification message for posting unapproved content'
      value: 'Sorry, the content you are trying to post is from unapproved website or words.'
  SorryTheTransactionHasFailedPleaseTryAgain:
      context: missing
      value: 'Sorry! The transaction has failed. Please try again'
  SorryThisPageIsNotAvailable:
      context: missing
      value: 'Sorry, this page is not available.'
  SorryWeCannotDisplayRequestedContent:
      context: missing
      value: 'Sorry, we cannot display requested content.'
  SorryYouDontHaveAnyAvailableChannels:
      context: missing
      value: "Sorry, you don't have any available channels"
  SortBy:
      context: missing
      value: 'Sort By'
  SortBy2:
      context: missing
      value: 'Sort by'
  SortByAZ:
      context: missing
      value: 'Sort By A-Z'
  SortByNewestFirst:
      context: missing
      value: 'Sort by: Newest First'
  SortByOldestFirst:
      context: missing
      value: 'Sort by: Oldest First'
  SortByZA:
      context: missing
      value: 'Sort By Z-A'
  Source:
      context: missing
      value: Source
  SourceOfMdp:
      context: missing
      value: 'Source of MDP'
  SourceOfTheContent:
      context: missing
      value: 'Source of the content'
  Sources:
      context: missing
      value: Sources
  SpecifyOwnersToBeResponsible:
      context: missing
      value: 'Specify owner(s) to be responsible'
  Standalone:
      context: missing
      value: Standalone
  StandardTypes:
      context: missing
      value: 'Standard Types'
  Star:
      context: missing
      value: Star
  Start:
      context: missing
      value: Start
  StartDate:
      context: missing
      value: 'Start Date: '
  StartDate2:
      context: Text info about content start date
      value: 'Start Date'
  StartDateAndTime:
      context: missing
      value: 'Start Date and Time'
  StartDateAndTimeShouldBeGreaterThanCurrentTime:
      context: missing
      value: 'Start Date and Time should be greater than current time'
  StartDateCanTBeResetIfDueDateIsPresent:
      context: missing
      value: 'Start date can&amp;#39;t be reset if due date is present.'
  StartDateEndDate:
      context: missing
      value: 'Start Date - End Date'
  StartDateMostRecent:
      context: missing
      value: 'Start Date: Most Recent'
  Started:
      context: missing
      value: Started
  StartedDate:
      context: missing
      value: 'Started Date: '
  StartedOn:
      context: missing
      value: 'Started on'
  StartLearning:
      context: missing
      value: 'Start Learning'
  StartTime:
      context: missing
      value: 'Start Time'
  StartTimeIsInvalid:
      context: missing
      value: 'Start Time is invalid'
  StartTyping:
      context: missing
      value: 'Start Typing'
  StartTypingATitleForYourJourney:
      context: missing
      value: 'Start typing a title for your Journey'
  StartTypingThenUseATabKeyToSelectAnOptionFromTheList:
      context: missing
      value: 'Start typing, then use a Tab key to select an option from the list'
  StartTypingThenUseTheUpAndDownArrowsToSelectAnOptionFromTheList:
      context: missing
      value: 'Start typing, then use the up and down arrows to select an option from the list'
  StartTypingToFilterSkills:
      context: missing
      value: 'Start typing to filter skills'
  StartTypingYourQuestionOrPasteLinkHere:
      context: missing
      value: 'Start typing your question or paste link here'
  StartWritingHereNumberOfCharacters:
      context: missing
      value: 'Start writing here (Number of Characters)'
  StartYourLearningJourneyByFindingContentToConsume:
      context: missing
      value: 'Start your learning journey by finding content to consume!'
  State:
      context: missing
      value: State
  StateAsPerAadhaar:
      context: missing
      value: 'State as per Aadhaar'
  Statistics:
      context: missing
      value: Statistics
  Status:
      context: missing
      value: 'Status: '
  Step1ToRegisterYourself:
      context: missing
      value: 'Step 1 : To Register yourself '
  Step2CompleteEligibleCoursesOfferedOnThePlatform:
      context: missing
      value: 'Step 2 : Complete eligible courses offered on the platform.'
  Step3GetSuccessfullyCertifiedInIndustryApprovedSscAssessments:
      context: missing
      value: 'Step 3 : Get successfully certified in industry approved SSC assessments.'
  StepNum:
      context: missing
      value: 'Step {num}'
  StepsToApplyForGoiIncentive:
      context: missing
      value: 'Steps to apply for GoI Incentive'
  StopCoBroadcasting:
      context: missing
      value: 'Stop Co-broadcasting'
  Stopstream:
      context: missing
      value: stopStream
  StowatchIcon:
      context: missing
      value: 'Stowatch Icon'
  Streams:
      context: missing
      value: Streams
  SubcompetencySelection:
      context: missing
      value: 'Sub-Competency selection'
  SubjectMatterExpertsSme:
      context: missing
      value: 'Subject Matter Experts (SME)'
  Submissions:
      context: missing
      value: Submissions
  Submit:
      context: missing
      value: Submit
  Submitted:
      context: missing
      value: Submitted
  SubmittedBy:
      context: missing
      value: 'Submitted By'
  SubmittedOn:
      context: missing
      value: 'Submitted On'
  Submitting:
      context: missing
      value: Submitting
  Subscribe:
      context: missing
      value: Subscribe
  Subscribed:
      context: missing
      value: Subscribed
  SubscribeToChannel:
      context: missing
      value: 'Subscribe to channel'
  SubscribeToChannelNotification:
      context: missing
      value: 'Subscribe to channel Notification'
  SubscribeunsubscribeNotification:
      context: missing
      value: 'Subscribe/Unsubscribe Notification'
  Subscriptions:
      context: missing
      value: Subscriptions
  Successful:
      context: missing
      value: Successful
  SuccessfullyEnrolledForWaitingList:
      context: missing
      value: 'Successfully Enrolled for Waiting List'
  Suggested:
      context: missing
      value: Suggested
  SuggestedLearningGoals:
      context: missing
      value: 'Suggested Learning Goals'
  Summary:
      context: missing
      value: Summary
  SummaryOf:
      context: missing
      value: 'Summary of'
  SwitchAccount:
      context: missing
      value: 'Switch Account'
  SwitchingRequestIsInProgress:
      context: missing
      value: 'Switching Request is in progress'
  SwitchToTheApp:
      context: missing
      value: 'Switch to the App'
  Tab:
      context: missing
      value: tab
  TabActive:
      context: missing
      value: 'tab active'
  Tag:
      context: missing
      value: Tag
  TagErrorMsg:
      context: error message
      value: Please add the tag.
  Tags:
      context: missing
      value: Tags
  Tags2:
      context: Tag with an optional plural in a parenthesis
      value: Tag(s)
  TakeThePreAssessment:
      context: missing
      value: 'Take the Pre-assessment'
  TakeTheSkillsAssessment:
      context: missing
      value: 'Take the Skills Assessment '
  Talent:
    context: missing
    value: 'Talent'
  TeamActivity:
      context: missing
      value: 'Team Activity'
  TeamActivityDiscussion:
      context: missing
      value: 'Team Activity discussion'
  TeamMember:
      context: missing
      value: 'Team Member'
  TeamMembers:
      context: missing
      value: 'Team Members'
  Technology:
      context: missing
      value: Technology
  Temporary:
      context: missing
      value: Temporary
  TermsAndConditions:
      context: missing
      value: 'terms and conditions'
  TermsOfService:
      context: missing
      value: 'Terms of Service'
  TermsOfService2:
      context: missing
      value: 'Terms of Service'
  TermsOfUse:
      context: missing
      value: 'Terms of Use'
  Text:
      context: missing
      value: Text
  ThanksForFeedback:
      context: info after sending feedback
      value: 'Thanks for your feedback!'
  ThanksForJoiningPleaseCheckForOurVerificationEmail:
      context: missing
      value: 'Thanks for joining! Please check for our verification email.'
  ThanksForJoiningPleaseCheckForYourVerificationEmail:
      context: missing
      value: 'Thanks for joining! Please check for your verification email'
  ThanksForRegistering:
      context: missing
      value: 'Thanks for Registering'
  ThanksForRegisteringWellGetBackToYouSoon:
      context: missing
      value: "Thanks for Registering! We'll get back to you soon."
  ThanksForStreaming:
      context: missing
      value: 'Thanks for Streaming!'
  ThankYouForRegisteringForTheFutureskillsPrimeProgram:
      context: missing
      value: 'Thank you for registering for the FutureSkills Prime program.'
  ThatCanBeMadeAtATime:
      context: missing
      value: 'that can be made at a time.'
  TheAiEngineWillProvideAMaximumNumberOfRecommendati:
      context: missing
      value: 'The AI engine will provide a maximum number of recommendations every 24 hours. This setting is only available when AI recommendations are not being curated.'
  TheBelowRemarkscommentsIsVisibleOnlyToYou:
      context: missing
      value: 'The below remarks/comments is visible only to you'
  TheContentPostedByTheFollowersOfThisChannelCanBeCurated:
      context: missing
      value: 'The Content posted by the followers of this channel can be curated'
  TheFollowingCheckboxesWillUpdateTheContentBelow:
      context: missing
      value: 'The following checkboxes will update the content below'
  ActivatingFollowingButtonsWillUpdateTheContentInMainContentArea:
      context: hidden instruction for buttons in group page
      value: 'Activating following buttons will update the content in main content area'
  TheFollowingUsersHaveAlreadyCompletedTheContent:
      context: missing
      value: 'The following user(s) have already completed the content'
  TheHostHasPausedHisVideo:
      context: missing
      value: 'The host has Paused his video.'
  TheImageSizeShouldNotExceed2Mb:
      context: missing
      value: 'The image size should not exceed 2 MB'
  TheMaximumValueOfDate01014000:
      context: missing
      value: 'The maximum value of date: 01.01.4000'
  TheMinimumRecommendedSkillLevelSetUpByYourCompany:
      context: missing
      value: 'The minimum recommended skill level set up by your company'
  ThePaymentHasBeenCancelled:
      context: missing
      value: 'The payment has been cancelled'
  ThereAre:
      context: missing
      value: 'There are'
  ThereAreCurrentlyNoPendingLearningItemsAtThisTime:
      context: missing
      value: 'there are currently no pending learning items at this time.'
  ThereAreMoreCardsToCurate:
      context: missing
      value: 'There are more cards to curate'
  ThereAreNoAvailableCards:
      context: missing
      value: 'There are no available cards.'
  ThereAreNoCardsAvailableToDisplayRightNow:
      context: missing
      value: 'There are no Cards available to display right now'
  ThereAreNoCoursesAvailable:
      context: missing
      value: 'There are no courses available.'
  ThereAreNoFeaturedContentInThisGroup:
      context: missing
      value: 'There are no featured content in this group'
  ThereAreNoImagesToDisplay:
      context: missing
      value: 'There are no images to display'
  ThereAreNoJourneysAvailable:
      context: missing
      value: 'There are no Journeys available.'
  ThereAreNoLiveStreamsAvailable:
      context: missing
      value: 'There are no Live Streams available.'
  ThereAreNoNewNotifications:
      context: missing
      value: 'There are no new notifications.'
  ThereAreNoPathwaysAvailable:
      context: missing
      value: 'There are no Pathways available.'
  ThereAreNoUsersMatchingYourFilter:
      context: missing
      value: 'There are no users matching your filter'
  ThereAreNoUsersMatchingYourFilters:
      context: missing
      value: 'There are no users matching your filters'
  ThereIsAnErrorWhileUpdatingTheEmployeesDetailsPleaseTryAgain:
      context: missing
      value: 'There is an error while updating the employees details. Please try again.'
  ThereIsNoContent:
      context: missing
      value: 'There is no content.'
  ThereIsNoContentAvailableBasedOnTheCriteriaYouSelected:
      context: missing
      value: 'There is no content available based on the criteria you selected.'
  ThereIsNoContentToShow:
      context: missing
      value: 'There is no content to show'
  ThereWasAnErrorRetrievingActions:
      context: missing
      value: 'There was an error retrieving actions'
  ThereWasAnErrorRetrievingAvailableFilters:
      context: missing
      value: 'There was an error retrieving available filters.'
  ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain:
      context: missing
      value: 'There was an error retrieving search results. Please try again.'
  ThereWasAProblemRetrievingGraphDataTryAgainLater:
      context: missing
      value: 'There was a problem retrieving graph data. Try again later.'
  TheSkillIsAlreadyAddedpleaseSelectADifferentSkill:
      context: missing
      value: 'The skill is already added.Please select a different skill.'
  TheSkillsLevelsProvidedByYou:
      context: missing
      value: 'The skills levels provided by you'
  TheStreamIsEndedPleaseWaitTheStreamIsGettingSavedToTheCloud:
      context: missing
      value: 'The stream is Ended !!. please wait the stream is getting saved to the cloud.'
  TheTopicIsAlreadyAdded:
      context: missing
      value: 'The topic is already added!'
  TheTopRecommendedContentFromThisChannel:
      context: missing
      value: 'The top recommended content from this channel'
  TheUserHasEndedThisStreamThankYouForWatching:
      context: missing
      value: 'The user has ended this stream. Thank you for watching.'
  TheUserIsNotAMemberOfTheGroupYouCantAddItAsGroupAdmin:
      context: missing
      value: 'The user is not a member of the group. You can’t add it as Group Admin'
  TheUserIsNotAMemberOfTheGroupYouCantAddItAsGroupLeader:
      context: missing
      value: 'The user is not a member of the group. You can’t add it as Group Leader'
  ThisActionCanNotBeUndoneHowDoYouWishToProceed:
      context: missing
      value: 'This action can not be undone. How do you wish to proceed?'
  ThisActionCannotBeUndoneHowWouldYouLikeToProceed:
      context: missing
      value: 'This action cannot be undone. How would you like to proceed?'
  ThisActionIsCurrentlyUnavailable:
      context: missing
      value: 'This action is currently unavailable'
  ThisCardIsRestrictedAndCanBeSharedOnlyByTheAuthor:
      context: missing
      value: 'This card is restricted and can be shared only by the author.'
  ThisContentIsNotSharedWithAnyone:
      context: missing
      value: 'This content is not shared with anyone.'
  ThisContentWasNotAssignedToAnyoneYet:
      context: missing
      value: 'This content was not assigned to anyone yet.'
  ThisContentWasNotSharedWithAnyoneYet:
      context: missing
      value: 'This content was not shared with anyone yet.'
  ThisFeatureIsCurrentlyDisabled:
      context: missing
      value: 'This feature is currently disabled'
  ThisFileWillNotBeStoredAfterScan:
      context: missing
      value: 'This file will not be stored after scan.'
  ThisIconAppearsWhenYouAchieveTheIdealSkillLevel:
      context: missing
      value: 'This icon appears when you achieve the ideal skill level.'
  ThisIsAChannelImage:
      context: missing
      value: 'This is a channel image'
  ThisIsAGroupImage:
      context: missing
      value: 'This is a group image'
  ThisIsTheRequiredSkillLevelForYourJobRole:
      context: missing
      value: 'This is the required skill level for your job role.'
  ThisLivestreamIsScheduledFor:
      context: missing
      value: 'This LiveStream is scheduled for'
  ThisPageIsDisplayingContentInYourPreferredLanguage:
      context: missing
      value: 'This page is displaying content in your preferred language'
  ThisProfileIsPrivate:
      context: missing
      value: 'This profile is Private'
  ThisWeek:
      context: missing
      value: 'This Week'
  ThisWidgetWillNotBeVisibleOnYourPublicProfile:
      context: missing
      value: 'This Widget will not be visible on your public profile.'
  Tile:
      context: missing
      value: Tile
  TileView:
      context: missing
      value: 'Tile View'
  TileViewSelected:
      context: missing
      value: 'Tile View Selected'
  Time:
      context: missing
      value: Time
  TimeCommitment:
      context: missing
      value: 'Time Commitment'
  TimeSpentLearningThisYear:
      context: missing
      value: 'Time Spent Learning This Year'
  TimeZone:
      context: missing
      value: 'Time Zone'
  Timezone:
      context: missing
      value: Timezone
  TimeZones:
      context: missing
      value: 'Time Zones'
  Title:
      context: missing
      value: Title
  TitleCantBeEmpty:
      context: missing
      value: "Title can't be empty"
  TitleDeletedItWillNeverShowAgain:
      context: missing
      value: '{title} deleted. It will never show again.'
  Titledescription:
      context: missing
      value: Title/Description
  TitledescriptionIsRequired:
      context: missing
      value: 'Title/Description is required.'
  Titledescriptionmandatory:
      context: missing
      value: Title/Description(Mandatory)
  TitleIsRequired:
      context: missing
      value: 'Title is required.'
  Titlemandatory:
      context: missing
      value: Title(Mandatory)
  TitleErrorMsg:
      context: missing
      value: Please enter a title to complete the creation process.
  To:
      context: missing
      value: to
  ToBeAnInternapprentice:
      context: missing
      value: 'to be an intern/apprentice?'
  ToBridgeTheGaps:
      context: missing
      value: 'to bridge the gaps.'
  ToChangeRegistrationDetailsBeforeApplyingForGoiIncentive:
      context: missing
      value: 'to change registration details before applying for GOI Incentive'
  ToChangeRegistrationDetailsBeforeApplyingForTheGoiIncentive:
      context: missing
      value: 'to change registration details before applying for the GOI Incentive'
  ToContinueLearning:
      context: missing
      value: 'to continue learning.'
  ToExploreYourCareerPathSelectJobRole:
      context: missing
      value: 'To explore your career path, select Job Role.'
  ToFindNewContent:
      context: missing
      value: 'to find new content.'
  Toggle:
      context: missing
      value: Toggle
  TogglePoll:
      context: missing
      value: 'Toggle Poll'
  ToggleToAllRegisters:
      context: toggle to select or unselect all registers
      value: 'toggle to {variable} all registers'
  ToJourneyHome:
      context: missing
      value: 'To Journey Home'
  ToLogBackIn:
      context: missing
      value: 'to log back in'
  TooManySectionsAllowedOnlyUpTo12:
      context: missing
      value: 'Too many sections. Allowed only up to 12'
  Top:
      context: missing
      value: TOP
  ToPathwayHome:
      context: missing
      value: 'To Pathway Home'
  TopCards:
      context: missing
      value: 'Top Cards'
  TopContent:
      context: missing
      value: 'Top Content'
  TopContributors:
      context: missing
      value: 'Top Contributors'
  TopCreatorsBenchmark:
      context: missing
      value: 'Top Creators Benchmark'
  Topics:
      context: missing
      value: Topic(s)
  TopicSkillLearningGoal:
      context: missing
      value: 'Topic / Skill / Learning Goal'
  TopicsRelatedToThisChannel:
      context: missing
      value: 'Topics related to this channel'
  TopicsYouMightAlsoBeInterestedIn:
      context: missing
      value: 'Topics you might also be interested in'
  TopOfList:
      context: missing
      value: 'Top of List'
  TopPercentile:
      context: missing
      value: 'TOP {percentile}%'
  ToResetYourPasswordEnterYourEmailAddress:
      context: missing
      value: 'To reset your password, enter your email address.'
  ToSkillsPassportToImproveAccuracy:
      context: missing
      value: 'to Skills passport to improve accuracy.'
  ToSkillsPassportToImproveYourSuggestions:
      context: missing
      value: 'to skills passport to improve your suggestions.'
  Total:
      context: missing
      value: 'Total:'
  TotalAssignees:
      context: missing
      value: 'Total Assignees'
  TotalAssignments:
      context: missing
      value: 'Total Assignments'
  TotalChannels:
      context: missing
      value: 'Total Channels:'
  TotalitemsResults:
      context: missing
      value: '{totalItems} Results'
  TotalitemsResultsFor:
      context: missing
      value: '{totalItems} results for'
  TotalLearningHours:
      context: missing
      value: 'Total Learning Hours'
  TotalMembersTotalmembers:
      context: missing
      value: 'Total Members: {totalMembers}'
  TotalNumberOfAssignmentsWithDueDateInPast:
      context: missing
      value: 'Total number of assignments with due date in past'
  TotalNumberOfContentAssignedToYourTeamInTheDuration:
      context: missing
      value: 'Total number of content assigned to your team in the duration'
  TotalPoints:
      context: missing
      value: 'Total Points'
  TotalPrice:
      context: missing
      value: 'Total Price'
  TotalRecords:
      context: missing
      value: 'Total Records'
  TotalVotesPolled:
      context: missing
      value: 'Total Votes Polled'
  ToTypeHome:
      context: missing
      value: 'To {type} Home'
  ToViewRecommendedRolesBasedOnYourSkills:
      context: missing
      value: 'to view recommended roles based on your skills'
  ToYear:
      context: missing
      value: 'To Year'
  TrackAllYourLearningInOnePlace:
      context: missing
      value: 'Track all your learning in one place.'
  Trainer:
      context: missing
      value: Trainer
  TrainersOfCollaboratedAcademia:
      context: missing
      value: 'Trainers of Collaborated Academia'
  TrainersOfPartnerAgenciesEgSpoke:
      context: missing
      value: 'Trainers of Partner Agencies (eg. Spoke)'
  TrainersOfResourceCentres:
      context: missing
      value: 'Trainers of Resource Centres'
  TransactionSummary:
      context: missing
      value: 'Transaction Summary'
  Transgender:
      context: missing
      value: Transgender
  Translations:
      context: missing
      value: Translations
  TrendingInterests:
      context: missing
      value: 'Trending interests'
  TrustedCollaborators:
      context: missing
      value: 'Trusted Collaborators'
  Tuning:
      context: missing
      value: Tuning
  Twitter:
      context: missing
      value: Twitter
  Type:
      context: missing
      value: Type
  TypeCollaborators:
      context: missing
      value: '{type} Collaborators'
  TypeHereToSearchAndSelect:
      context: missing
      value: 'Type here to search and select'
  TypeInYourMessageHere:
      context: missing
      value: 'Type in your message here'
  TypeOfJourneyInstructorPaced:
      context: missing
      value: 'type of Journey, instructor paced'
  TypeOfJourneyProgressiveUnlocking:
      context: missing
      value: 'type of Journey, progressive unlocking'
  TypeOfJourneySelfPaced:
      context: missing
      value: 'type of Journey, Self Paced'
  TypeSavedAsDraftSuccessfully:
      context: missing
      value: '{type} saved as draft successfully'
  TypeYourFeedback:
      context: placeholder for textarea input field in feedback form
      value: 'Type your feedback...'
  TypesOfCardReturned:
      context: missing
      value: 'Types of card returned'
  TypesOfGroup:
      context: missing
      value: 'Types of Group'
  TypeTheTopicYouWantToLearnAbout:
      context: missing
      value: 'Type the topic you want to learn about'
  TypeToSearch:
      context: missing
      value: 'Type to search'
  UnableToAddCustomCarouselPleaseTryAgain:
      context: missing
      value: 'Unable to add custom carousel, Please try again!'
  UnassignedOnly:
      context: missing
      value: 'Unassigned only'
  Unbookmark:
      context: missing
      value: Unbookmark
  UnbookmarkTitle:
      context: missing
      value: 'Unbookmark-{title}'
  UncompletedJourney:
      context: missing
      value: 'uncompleted Journey'
  UncompletedPathway:
      context: missing
      value: 'uncompleted Pathway'
  UncompletedSmartcard:
      context: missing
      value: 'uncompleted SmartCard'
  UnderstandingParametersBeingSentToApis:
      context: missing
      value: 'Understanding parameters being sent to APIs'
  Undismiss:
      context: missing
      value: Undismiss
  Undo:
      context: missing
      value: undo
  Unemployed:
      context: missing
      value: Unemployed
  UnemployedWithJobOffer:
      context: missing
      value: 'Unemployed with job offer'
  UnemployedWithoutJobOffer:
      context: missing
      value: 'Unemployed without job offer'
  Unfeature:
      context: missing
      value: Unfeature
  UnFeatured:
      context: missing
      value: Un-featured
  Unfollow:
      context: missing
      value: UNFOLLOW
  UnfollowChannel:
      context: missing
      value: 'Unfollow Channel?'
  Unfollowing:
      context: missing
      value: UNFOLLOWING...
  UnfortunatelyYourPasswordResetEmailHasExpired:
      context: missing
      value: 'Unfortunately, your password reset email has expired.'
  UnitedKingdom:
      context: missing
      value: 'United Kingdom'
  UnitedStates:
      context: missing
      value: 'United States'
  UniversityName:
      context: missing
      value: 'University Name'
  Unknown:
      context: missing
      value: Unknown
  UnlimitedOpenings:
      context: missing
      value: 'Unlimited openings'
  UnlockTheBadgeAfterQuizIsPassedSuccessfully:
      context: missing
      value: 'Unlock the badge after Quiz is passed successfully'
  Unpin:
      context: missing
      value: unpin
  UnPin:
      context: missing
      value: Un-pin
  Unpromote:
      context: missing
      value: Unpromote
  Unregister:
      context: missing
      value: Unregister
  UnregisterConfirmation:
      context: title for live event unregiter modal header
      value: Unregister Confirmation
  UnsavedChanges:
      context: missing
      value: 'Unsaved Changes'
  Unspecified:
      context: missing
      value: Unspecified
  UnsureIfThatEmailAddressWasCorrect:
      context: missing
      value: 'Unsure if that email address was correct?'
  Untimed:
      context: missing
      value: Untimed
  UpcomingStream:
      context: missing
      value: 'Upcoming Stream'
  Update:
      context: missing
      value: Update
  Update2:
      context: missing
      value: Update
  UpdateCareerProfile:
      context: missing
      value: 'Update Career Profile'
  UpdateChannel:
      context: missing
      value: 'Update Channel'
  UpdateChannelMappedOrganizations:
      context: missing
      value: 'Update Channel Mapped Organizations'
  Updated:
      context: missing
      value: updated
  UpdatedToday:
      context: missing
      value: 'updated today'
  UpdateEmployeesList:
      context: missing
      value: 'Update Employees list'
  UpdateExpertise:
      context: missing
      value: 'UPDATE EXPERTISE'
  UpdateGroup:
      context: missing
      value: 'Update Group'
  UpdateInterest:
      context: missing
      value: 'update interest'
  UpdateInterests:
      context: missing
      value: 'update interests'
  UpdateInterests2:
      context: missing
      value: 'Update Interests'
  UpdateIsSuccessfulPleaseClickCancelbackButtonToVisitChannel:
      context: missing
      value: 'Update is successful, please click cancel/back button to visit channel'
  UpdateJobRoleFamily:
      context: missing
      value: 'Update Job Role &amp; Family'
  UpdateLeap:
      context: missing
      value: 'UPDATE LEAP'
  UpdateLearningGoal:
      context: missing
      value: 'Update Learning Goal'
  UpdateLearningGoals:
      context: missing
      value: 'UPDATE LEARNING GOALS'
  UpdateLearningInterest:
      context: missing
      value: 'Update Learning Interest'
  UpdateSectionName:
      context: title text of modal that is used to update section name
      value: 'Update Section Name'
  UpdateSkills:
      context: missing
      value: 'UPDATE SKILLS'
  UpdateTheSkillsYouWantToAccess:
      context: missing
      value: 'Update the skills you want to access'
  UpdateYourLearningGoals:
      context: missing
      value: 'Update Your Learning Goals'
  Updating:
      context: missing
      value: Updating
  Upload:
      context: missing
      value: Upload
  UploadChannelImage:
      context: missing
      value: 'Upload Channel Image'
  UploadCredential:
      context: missing
      value: 'Upload Credential'
  UploadedDate:
      context: missing
      value: 'Uploaded Date'
  UploadFile:
      context: missing
      value: 'upload file'
  UploadFilesVideosAndImages:
      context: missing
      value: 'Upload Files, Videos, and Images'
  UploadGroupImage:
      context: missing
      value: 'Upload Group Image'
  UploadIcon:
      context: missing
      value: 'Upload Icon'
  UploadImage:
      context: missing
      value: 'Upload image'
  UploadImageError:
      context: missing
      value: 'Upload Image Error!'
  UploadInternshipApprenticeOfferLetter:
      context: missing
      value: 'Upload internship / Apprentice Offer letter'
  UploadJobOfferLetter:
      context: missing
      value: 'Upload Job Offer letter'
  UploadNewBadge:
      context: missing
      value: 'Upload New Badge'
  UploadNewFile:
      context: missing
      value: 'Upload New File'
  UploadRecentPhoto:
      context: missing
      value: 'Upload recent photo'
  UploadResume:
      context: missing
      value: 'Upload Resume'
  UploadThumbnail:
      context: missing
      value: 'Upload Thumbnail'
  UploadVideo:
      context: missing
      value: 'Upload Video'
  UponCompletingThisJourneyAssigneeWillGetABadge:
      context: missing
      value: 'Upon completing this Journey, assignee will get a badge.'
  UponCompletingThisPathwayAssigneeWillGetABadge:
      context: missing
      value: 'Upon completing this Pathway, assignee will get a badge.'
  UptoInr:
      context: missing
      value: 'Upto INR'
  UpToInr:
      context: missing
      value: 'up to INR'
  UrlSubmission:
      context: missing
      value: 'URL Submission'
  UseAPushNotificationSentToTheMobileApp:
      context: missing
      value: 'Use a push notification sent to the mobile app'
  UseAtLeast8Characters:
      context: missing
      value: 'Use at least 8 characters.'
  UseBelowLinksToSwitchToOtherAccountYouAreMemberOf:
      context: missing
      value: 'Use below links to switch to other account you are member of'
  User:
      context: missing
      value: User
  UserAadhaarImage:
      context: missing
      value: 'User aadhaar image'
  UserAvatarImage:
      context: missing
      value: 'User avatar image'
  UserGeneratedCards:
      context: missing
      value: 'User Generated Cards'
  UserGeneratedContent:
      context: missing
      value: 'User Generated Content'
  UserList:
      context: missing
      value: 'user list'
  UserLockIcon:
      context: missing
      value: 'user lock icon'
  UserMadeChanges:
      context: missing
      value: '{user} made changes'
  UserMustClickOnTheMarkAsCompleteButton:
      context: missing
      value: 'User must click on the Mark as Complete button'
  UserName:
      context: missing
      value: 'User Name'
  UsernamesActivity:
      context: missing
      value: "{userName}'s Activity"
  UserNotFound:
      context: missing
      value: 'User not found'
  UserPostedContent:
      context: missing
      value: 'User posted content'
  UserProfilePicture:
      context: missing
      value: 'user profile picture'
  UserReport:
      context: missing
      value: 'User Report'
  UserRole:
      context: missing
      value: 'User Role'
  Users:
      context: missing
      value: Users
  Users2:
      context: missing
      value: Users
  UsersDataWillUpdateEvery24Hours:
      context: missing
      value: 'Users data will update every 24 hours'
  UsersLimit:
      context: missing
      value: 'users limit'
  UsersList:
      context: missing
      value: 'Users List'
  UsersListToTagInComment:
      context: missing
      value: 'Users list to tag in comment'
  UsersSuccessfullyAdded:
      context: missing
      value: 'Users successfully added'
  UsersSuccessfullyRemoved:
      context: missing
      value: 'Users successfully removed'
  UsersWillBeAbleToFindAndJoinTheGroup:
      context: missing
      value: 'Users will be able to find and join the group'
  UsersWillBeAbleToFindAndJoinThisGroup:
      context: missing
      value: 'Users will be able to find and join this group.'
  V2ManagerDashboard:
      context: missing
      value: 'V2 Manager Dashboard'
  V2Reports:
      context: missing
      value: 'V2 Reports'
  ValidUpto:
      context: missing
      value: 'Valid Upto:'
  ValueHrs:
      context: missing
      value: '{value} hrs'
  Verified:
      context: state that something is true
      value: 'Verified'
  Verify:
      context: missing
      value: Verify
  VerifyCode:
      context: missing
      value: 'Verify Code'
  VerifyMobileNumber:
      context: missing
      value: 'Verify Mobile Number'
  Version:
      context: missing
      value: version
  VersionHistory:
      context: missing
      value: 'Version History'
  VersionType:
      context: missing
      value: 'Version Type'
  Video:
      context: missing
      value: Video
  Video2:
      context: missing
      value: video
  VideoPreview:
      context: missing
      value: 'Video Preview'
  VideoResource:
      context: missing
      value: 'Video Resource'
  VideoStream:
      context: missing
      value: 'Video Stream'
  View:
      context: missing
      value: View
  ViewAll:
      context: missing
      value: 'View All'
  ViewAllActivity:
      context: missing
      value: 'view all activity'
  ViewAllAssignmentsInYourLearningPlan:
      context: missing
      value: 'View all assignments in your Learning Plan'
  ViewAllChannels:
      context: missing
      value: 'view all channels'
  ViewAllChannelsYouFollow:
      context: missing
      value: 'View all channels you follow'
  ViewAllGroupChannels:
      context: missing
      value: 'View All Group Channels'
  ViewAllGroups:
      context: missing
      value: 'view all groups'
  ViewAllInProgressItems:
      context: missing
      value: 'view all in progress items'
  ViewAndAddToSkillsPassport:
      context: missing
      value: 'View and add to skills passport'
  ViewAndInviteTeamMembersOrRemoveTeamMembersFromTheGroup:
      context: missing
      value: 'View and invite team members or remove team members from the group'
  ViewAttendees:
      context: missing
      value: 'View Attendees'
  ViewBadge:
      context: missing
      value: 'View Badge'
  ViewBy:
      context: missing
      value: 'View by'
  ViewCarousellabel:
      context: missing
      value: 'View {carouselLabel}'
  ViewCertificate:
      context: missing
      value: 'View Certificate'
  ViewCertification:
      context: missing
      value: 'View Certification'
  ViewComment:
      context: missing
      value: 'View Comment'
  ViewContent:
      context: missing
      value: 'View Content'
  ViewCourses:
      context: missing
      value: 'View courses'
  ViewDetails:
      context: missing
      value: 'View Details'
  Viewers:
      context: missing
      value: Viewers
  ViewFullCommentMessage:
      context: missing
      value: 'View full comment message'
  ViewLearning:
      context: missing
      value: 'View Learning'
  ViewLearningPlan:
      context: missing
      value: 'View Learning Plan'
  ViewLess:
      context: missing
      value: 'View less'
  ViewMatchDetails:
      context: missing
      value: 'View Match Details'
  ViewMessage:
      context: missing
      value: 'View message'
  ViewMessages:
      context: missing
      value: 'View messages'
  ViewMore:
      context: missing
      value: 'View More'
  ViewMore2:
      context: missing
      value: 'View more'
  ViewMoreChannels:
      context: missing
      value: 'View More Channels'
  ViewMorePeople:
      context: missing
      value: 'View More People'
  ViewMoreResultsFor:
      context: missing
      value: 'View more results for '
  ViewOnCareerSite:
      context: missing
      value: 'View on career site'
  ViewOptions:
      context: missing
      value: 'view options'
  ViewPeopleInRelatedRoles:
      context: missing
      value: 'View people in related roles'
  ViewPlan:
      context: missing
      value: 'View Plan'
  ViewProfile:
      context: missing
      value: 'View Profile'
  ViewPromotedPlans:
      context: missing
      value: 'View Promoted Plans'
  ViewPublicProfile:
      context: missing
      value: 'View Public Profile'
  ViewPurchase:
      context: missing
      value: 'View Purchase'
  ViewRemainingMore:
      context: missing
      value: 'View {remaining} more'
  ViewReport:
      context: missing
      value: 'View Report'
  ViewResults:
      context: missing
      value: 'View results'
  Views:
      context: missing
      value: views
  ViewSubmission:
      context: missing
      value: 'View Submission'
  ViewSubmissions:
      context: missing
      value: 'View Submissions'
  ViewTeam:
      context: missing
      value: 'VIEW TEAM'
  ViewTmtmmentorProfile:
      context: missing
      value: 'View {tm_tm_mentor} Profile'
  ViewUploadHistory:
      context: missing
      value: 'View Upload History'
  Visibility:
      context: missing
      value: Visibility
  VisibilityInprogress:
      context: missing
      value: 'Visibility - InProgress'
  VisibilityMyActivity:
      context: missing
      value: 'Visibility - My Activity'
  Visible:
      context: missing
      value: VISIBLE
  VisitChannel:
      context: missing
      value: 'Visit channel'
  VisitTheChannel:
      context: missing
      value: 'Visit the channel'
  VisitTheDetailedPageOf:
      context: missing
      value: 'visit the detailed page of'
  VisitTheFeaturedPathway:
      context: missing
      value: 'Visit the featured Pathway'
  VisitTheGroup:
      context: missing
      value: 'Visit the group,'
  VisitTheGroupName:
      context: missing
      value: 'Visit the group, {name}'
  VisitThePathwayTitle:
      context: missing
      value: 'Visit the Pathway, {title}'
  Volunteer:
      context: missing
      value: Volunteer
  Vote:
      context: missing
      value: Vote
  WaitingList:
      context: missing
      value: 'Waiting List'
  WaitingListEnrollmentCancelled:
      context: missing
      value: 'Waiting List Enrollment Cancelled'
  WaitingSeats:
      context: missing
      value: 'Waiting Seats'
  WantsToBroadcastWithYou:
      context: missing
      value: 'wants to broadcast with you'
  WantToLearnSomethingNewJustAddItToYourInterests:
      context: missing
      value: "Want to learn something new? Just add it to your 'Interests'."
  WantToLearnSomethingNewJustAddItToYourLearningGoals:
      context: missing
      value: "Want to learn something new? Just add it to your 'Learning Goals'."
  WarningMaximumCharacterLimitReached:
      context: missing
      value: 'Warning: Maximum character limit reached'
  WebUrl:
      context: missing
      value: 'Web URL'
  WeCanScanYourResumeToFillInYourWorkHistory:
      context: missing
      value: 'We can scan your Resume to fill in your work history.'
  WeCouldntFindThePageYouWereLookingFor:
      context: missing
      value: "We couldn't find the page you were looking for"
  Week:
      context: missing
      value: week
  Week2:
      context: missing
      value: Week
  Weeks:
      context: missing
      value: weeks
  WeHaveNothingToSuggestYouYet:
      context: missing
      value: 'We have nothing to suggest you yet'
  WeHaveSentAVerificationEmailToYourRegisteredEmailAddress:
      context: missing
      value: 'We have sent a verification email to your registered email address.'
  WelcomeToYourLedger:
      context: missing
      value: 'Welcome to your Ledger'
  WereYouEmployedPreviously:
      context: missing
      value: 'Were you employed previously?'
  WeWereNotAbleToProvideAnySuggestions:
      context: missing
      value: 'We were not able to provide any suggestions.'
  WeWereUnableToSetYourManager:
      context: missing
      value: 'We were unable to set your manager'
  WeWereUnableToUpdateYourJobFamily:
      context: missing
      value: 'We were unable to update your Job Family'
  WhatActionWouldYouLikeToTakeOnThisPathway:
      context: missing
      value: 'What action would you like to take on this Pathway?'
  WhatActionWouldYouLikeToTakeOnThisSection:
      context: missing
      value: 'What action would you like to take on this section?'
  WhatActionWouldYouLikeToTakeOnThisSmartcard:
      context: missing
      value: 'What action would you like to take on this SmartCard?'
  WhatDoYouWishToAdd:
      context: missing
      value: 'What do you wish to add?'
  WhatDoYouWishToShareWithThisGroup:
      context: missing
      value: 'What do you wish to share with this group?'
  WhatSkillsDoYouWantToShare:
      context: missing
      value: 'What skills do you want to share?'
  WhatWouldYouLikeToLearn:
      context: missing
      value: 'What would you like to learn?'
  WhatWouldYouLikeToLearnAbout:
      context: missing
      value: 'What would you like to learn about?'
  WhenUserFailsTheQuizLeapTo:
      context: missing
      value: 'When user fails the quiz, leap to'
  WhenUserFailsTheQuizLeapTo2:
      context: missing
      value: 'When user fails the quiz, leap to:'
  WhenUserPassesTheQuizLeapTo:
      context: missing
      value: 'When user passes the quiz, leap to'
  WhenUserPassesTheQuizLeapTo2:
      context: missing
      value: 'When user passes the quiz, leap to:'
  WhereYouLeftOff:
      context: missing
      value: 'Where you left off...'
  WhoCanPostToThisChannel:
      context: missing
      value: 'Who can post to this channel?'
  WidgetDoNotAllowScript:
      context: missing
      value: 'Widget do not allow script!'
  WillYouReachYourGoal:
      context: missing
      value: 'Will You Reach Your Goal?'
  WorkHistory:
      context: missing
      value: 'Work History'
  WriteAComment:
      context: missing
      value: 'Write a comment'
  WriteACommentUseToTagPeers:
      context: missing
      value: 'Write a comment. Use @ to tag peers'
  WriteTheReasonPlease:
      context: missing
      value: 'Write the reason, please'
  WriteTheReasonPleaseMax100Character:
      context: missing
      value: 'Write the reason, please (max. 100 character)'
  WriteToUs:
      context: missing
      value: 'write to us'
  WriteYourCommentsHere:
      context: missing
      value: 'Write your comments here'
  WriteYourMessageHere:
      context: missing
      value: 'Write your message here'
  WriteYourMessageHerereplacesg:
      context: missing
      value: "Write your message here"
  XAxis:
      context: missing
      value: 'X Axis'
  YAxis:
      context: missing
      value: 'Y Axis'
  Year:
      context: missing
      value: Year
  YearRange:
      context: missing
      value: 'Year Range'
  Years:
      context: missing
      value: Years
  YearsOfExperience:
      context: missing
      value: 'Years of experience'
  Yes:
      context: missing
      value: 'Yes'
  You:
      context: missing
      value: You
  YouAreAboutToAddThisContent:
      context: missing
      value: 'You are about to add this content'
  YouAreAboutToDeclineTheInvitationFor:
      context: missing
      value: 'You are about to decline the invitation for'
  YouAreAboutToUnfollow:
      context: missing
      value: 'You are about to unfollow'
  YouAreAllCaughtUp:
      context: missing
      value: 'You are all caught up!'
  YouAreHere:
      context: missing
      value: 'You are here'
  YouAreNotAuthorisedToEditThisGroup:
      context: missing
      value: 'You are not authorised to edit this group'
  YouAreNotAuthorisedToUpdateMembersOfThisGroup:
      context: missing
      value: 'You are not authorised to update members of this group'
  YouAreNotAuthorizedForThisAction:
      context: missing
      value: 'You are not authorized for this action'
  YouAreNotAuthorizedToAccessThisChannel:
      context: missing
      value: 'You are not authorized to access this channel.'
  YouAreNotAuthorizedToAccessThisGroupAnalytics:
      context: missing
      value: 'You are not authorized to access this group analytics.'
  YouAreNotAuthorizedToAccessThisPathway:
      context: missing
      value: 'You are not authorized to access this Pathway.'
  YouAreNotAuthorizedToViewThisViltCard:
      context: missing
      value: 'You are not authorized to view this VILT card'
  YouAreNotFollowingAnyChannels:
      context: missing
      value: 'You are not following any channels.'
  YouAreNotPartOfAnyChannelYet:
      context: missing
      value: 'You are not part of any channel yet.'
  YouAreNotPartOfAnyGroupYet:
      context: missing
      value: 'You are not part of any group yet.'
  YouAreRegisteredForThisEvent:
      context: missing
      value: 'You are registered for this event.'
  YouAreSeeingThisCardtypelabelBecauseYouAssignedItTo:
      context: missing
      value: 'You are seeing this {cardTypeLabel} because you assigned it to'
  YouAreUnauthorizedForThisAction:
      context: missing
      value: 'You are unauthorized for this action'
  YouAreUnregisteredForThisEvent:
      context: missing
      value: 'You are unregistered for this event.'
  ViltUnregisteredSnackBar:
      context: Unregistration successful message on SnackBar, when user unrestigered for a live event card
      value: 'You have successfully unregistered from the event'
  YouCanEarnPointsByCompletingContentAndByEarningBadges:
      context: missing
      value: 'You can earn points by completing content and by earning badges'
  YouCanEnableDisableAndCustomizeThisFeatureByEditingThe:
      context: missing
      value: 'You can enable, disable, and customize this feature by editing the'
  YouCanFindYourTypesOnMyContentPage:
      context: missing
      value: 'You can find your {type}s on ‘My Content’ page'
  YouCanInviteUpToInvitelimitPeers:
      context: missing
      value: 'You can invite up to {inviteLimit} peers'
  YouCanManageYourApplicationsFromYour:
      context: missing
      value: 'You can manage your applications from your'
  YouCanNotAddYourselfAsManager:
      context: missing
      value: 'You can not add yourself as manager!'
  YouCanNotMarkedMoreThan10FeaturedCardsInAGroup:
      context: missing
      value: 'You can not marked more than 10 featured cards in a group.'
  YouCanPinMax10Cards:
      context: missing
      value: 'You can pin max 10 cards!'
  YouCanPinMaxCountCards:
      context: missing
      value: 'You can pin max {count} cards!'
  YouCanSelectAMaximumOfCountTopicsOnly:
      context: missing
      value: 'You can select a maximum of {count} topics only.'
  YouCanSelectAMaximumOfOnlyLimitTopics:
      context: missing
      value: 'You can select a maximum of only {limit} topics.'
  YouCanSelectAMaximumOfOnlyMaxlimitTopics:
      context: missing
      value: 'You can select a maximum of only {maxLimit} topics'
  YouCanSelectAMaximumOfOnlyOneTopic:
      context: missing
      value: 'You can select a maximum of only one topic.'
  YouCanSelectOneSkill:
      context: missing
      value: 'You can select one skill'
  YouCanUploadOnlyASquareShapedImage:
      context: missing
      value: 'You can upload only a square shaped image'
  YouDoNotHaveAccessToTheSystemPleaseContactTheAdministratorOrWriteTo:
      context: missing
      value: 'You do not have access to the system, please contact the administrator or write to'
  YouDoNotHaveAccessToThisPage:
      context: missing
      value: 'You do not have access to this page'
  YouDoNotHaveAnyActiveSubscriptions:
      context: missing
      value: 'You do not have any active subscriptions'
  YouDoNotHaveAnyContentHistoryVisitThe:
      context: missing
      value: 'You do not have any content history, visit the'
  YouDoNotHaveAnyGroup:
      context: missing
      value: 'You do not have any group.'
  YouDoNotHaveAnythingInProgress:
      context: missing
      value: 'You do not have anything in progress.'
  PermissionDenied:
      context: 'Permission Denied error message'
      value: 'Permission denied'
  ThisCardCannotBeTransferred:
      context: 'Card cannot be transferred error message'
      value: 'This card cannot be transferred.'
  NoUserWasSelected:
      context: 'No user selected error message'
      value: 'No user was selected.'
  YouDoNotHavePermissionToViewThisCard:
      context: missing
      value: 'You do not have permission to view this card'
  YouDoNotHavePermissionViewThisSection:
      context: missing
      value: 'You do not have permission view this section'
  YouDontHaveAccessToThisProvider:
      context: missing
      value: "You don't have access to this provider"
  YouDontHaveAnyReviewRequestsFromYourPeers:
      context: missing
      value: "You don't have any review requests from your peers"
  YouDontHaveAnyReviewRequestsFromYourReportees:
      context: missing
      value: "You don't have any review requests from your reportees"
  YouDontHavePermissionToEditThisPathway:
      context: missing
      value: "You don't have permission to edit this Pathway"
  YouEarnedABadge:
      context: missing
      value: 'You earned a badge.'
  YouHaveAlreadyAddedThisCardToMdp:
      context: missing
      value: 'You have already added this card to MDP'
  YouHaveAlreadyReportedThisCard:
      context: missing
      value: 'You have already reported this card'
  YouHaveAlreadyReportedThisComment:
      context: missing
      value: 'You have already reported this comment'
  YouHaveChosenToRejectThisLogin:
      context: missing
      value: 'You have chosen to reject this login'
  YouHaveCompletedThisTaskYouCanViewItUnderMeTab:
      context: missing
      value: 'You have completed this task. You can view it under Me tab.'
  YouHaveEarnedANewBadge:
      context: missing
      value: 'You have earned a new Badge.'
  YouHaveMarkedThisJourneyAsIncomplete:
      context: missing
      value: 'You have marked this Journey as incomplete.'
  YouHaveMarkedThisPathwayAsIncomplete:
      context: missing
      value: 'You have marked this Pathway as incomplete.'
  YouHaveMarkedThisSmartcardAsIncomplete:
      context: missing
      value: 'You have marked this SmartCard as incomplete'
  YouHaveMarkedThisTaskAsIncomplete:
      context: missing
      value: 'You have marked this task as incomplete'
  YouHaveMarkedThisTypeAsIncomplete:
      context: missing
      value: 'You have marked this {type} as incomplete.'
  YouHaveNotAssessedYourSkills:
      context: missing
      value: 'You have not assessed your skills'
  YouHaveReachedTheMaximumNumberOfPasswordResetRequests:
      context: missing
      value: 'You have reached the maximum number of password reset requests'
  YouHaveSuccessfullyCompletedTheJourney:
      context: missing
      value: 'You have successfully completed the Journey'
  YouHaveSuccessfullyCompletedThePathway:
      context: missing
      value: 'You have successfully completed the Pathway'
  YouHaveSuccessfullyCompletedTheType:
      context: missing
      value: 'You have successfully completed the {type}.'
  YouHaveSuccessfullySubmittedTheSkillsAssessmentForYourPeer:
      context: missing
      value: 'You have successfully submitted the Skills Assessment for your peer'
  YouHaveSuccessfullyUpdatedTheAssessment:
      context: missing
      value: 'You have successfully updated the assessment.'
  YouHaveToConfirmYourAccountBeforeContinuing:
      context: missing
      value: 'You have to confirm your account before continuing.'
  YouMustMarkTheCardAsPrivateToBeAbleToAddRestrictions:
      context: missing
      value: 'You must mark the card as private to be able to add restrictions'
  YourAccessToNameHasBeenSuspended:
      context: missing
      value: 'Your access to {name} has been suspended'
  YourAnnualLearningGoal:
      context: missing
      value: 'Your Annual Learning Goal'
  YourAnnualLearningGoalOfTarget:
      context: missing
      value: 'Your annual learning goal of {target}'
  YourApplicationForGoiIncentiveWillBeUpdatedOnceDisposed:
      context: missing
      value: 'Your application for GOI Incentive will be updated once disposed'
  YourBrowserDoesNotSupportIframes:
      context: missing
      value: 'Your browser does not support iframes.'
  YourBrowserDoesNotSupportTheVideoTag:
      context: missing
      value: 'Your browser does not support the video tag.'
  YourChangesMayNotBeSaved:
      context: missing
      value: 'Your changes may not be saved'
  YourCompanyRequiresMultifactorAuthenticationToAddAnAdditional:
      context: missing
      value: 'Your company requires multifactor authentication to add an additional'
  YourContent:
      context: missing
      value: 'Your Content'
  YourCoursesWillBeImportedShortly:
      context: missing
      value: 'Your courses will be imported shortly'
  YourCurrentIncentiveQuotaUsageAsPerCourseCategory:
      context: missing
      value: 'Your current Incentive Quota usage as per course category'
  YourCurrentSkills:
      context: missing
      value: 'Your current skills'
  YourCurrentSkills2:
      context: missing
      value: 'Your current skills'
  YourDraftJourneyIsSaved:
      context: missing
      value: 'Your draft Journey is saved!'
  YourDraftPathwayIsSaved:
      context: missing
      value: 'Your draft Pathway is saved!'
  YoureAlreadyBroadcastingThisLivestreamFromAnotherP:
      context: missing
      value: "You're already broadcasting this Livestream from another place"
  YourEdcastData:
      context: missing
      value: 'Your EdCast Data'
  YourEndDateMustBeTheSameOrAfterYourStartDate:
      context: missing
      value: 'Your end date must be the same or after your start date'
  YourHaveMarkedThisTaskAsIncomplete:
      context: missing
      value: 'Your have marked this task as incomplete'
  YourInvitationHasBeenSent:
      context: missing
      value: 'Your invitation has been sent!'
  YourLearningGoals:
      context: missing
      value: 'Your Learning Goals'
  YourLearningPlanIsEmptyAssignedContentWillShowHere:
      context: missing
      value: 'Your learning plan is empty, assigned content will show here.'
  YourLearningTranscriptIsBeingProcessedAndWillBeEmailedToYouShortly:
      context: missing
      value: 'Your Learning Transcript is being processed and ​will be emailed to you shortly'
  YourLimitHasBeenExceed:
      context: missing
      value: 'Your limit has been exceed.'
  YourNotUpdatedLeaps:
      context: missing
      value: 'Your not updated leaps!'
  YourOtpIsExpired:
      context: missing
      value: 'Your OTP is expired'
  YourOtpWillBeValidFor5Minutes:
      context: missing
      value: 'Your OTP will be valid for 5 minutes'
  YourOtpWillBeValidForDigitMinutes:
      context: missing
      value: 'Your OTP will be valid for {digit} minutes:'
  YourPasswordHasBeenUpdatedSuccessfully:
      context: missing
      value: 'Your password has been updated Successfully'
  YourPasswordResetEmailHasExpiredPleaseTapBelowToRequestANewOne:
      context: missing
      value: 'Your password reset email has expired. Please tap below to request a new one.'
  YourPaymentWasSuccessful:
      context: missing
      value: 'Your Payment was Successful'
  YourProfilePicture:
      context: missing
      value: 'Your Profile picture'
  YourProgress:
      context: missing
      value: 'Your Progress:'
  YourPushNotificationHasExpiredPleaseSendPushAgain:
      context: missing
      value: 'Your push notification has expired. Please Send push again.'
  YourRecruiter:
      context: missing
      value: 'Your recruiter'
  YourReportWillBeSentToYouToRegisteredEmailOnceItsProcessed:
      context: missing
      value: 'Your report will be sent to you to registered email once its processed'
  YourSearchHistoryHasBeenDeleted:
      context: missing
      value: 'Your search history has been deleted.'
  YourSkills:
      context: missing
      value: 'Your Skills'
  YourWalletBalanceIs:
      context: missing
      value: 'Your wallet balance is'
  YouShouldHaveAtLeast1TopicSelected:
      context: missing
      value: 'You should have at least 1 topic selected.'
  YouStillHaventAddedSkillOrCertificationToYourPassport:
      context: missing
      value: 'You still haven’t added skill or certification to your passport.'
  YouWillNoLongerBeNotifiedAboutTheContentPostedInTheChannel:
      context: missing
      value: 'You will no longer be notified about the content posted in the channel'
  ContentHasBeenPosted:
      context: 'Notification message when content is posted'
      value: 'Content has been posted to the selected channel(s).'
  ContentHasBeenPostedAndVisibleOnceApproved:
      context: 'Notification message when content is posted on curated channel'
      value: 'Content has been posted to the selected channel(s). If it is a curated channel, your post will be visible once approved by the curator.'
  YouWontBeAbleToAccessThisGroupDoYouReallyWantToLeave:
      context: missing
      value: 'You won’t be able to access this group, do you really want to leave ?'
  Yr:
      context: missing
      value: yr
  Yrs:
      context: missing
      value: yrs
  ZeroOpeningsAvailable:
      context: missing
      value: '0 openings available'
  Zone:
      context: missing
      value: zone
  AllSmartcardsInTheJourneyAreCompletedPleaseMarkTheJourneyAsCompleted:
    context: when the user Start journey with manual completion enabled, completes all the smartcard and enables autocompletion and then click on continue button in Journey page
    value: All SmartCards in the Journey are Completed. Please Mark the Journey as
        Completed.
  AllSmartcardsInThePathwayAreCompletedPleaseMarkThePathwayAsCompleted:
    context: when the user Start pathway with manual completion enabled, completes all the smartcard and enables autocompletion and then click on continue button in Pathway page
    value: All SmartCards in the Pathway are Completed. Please Mark the Pathway as
        Completed.
  GetStarted:
    context: missing
    value: Get started
  MatchesYourAspirationalRole:
    context: missing
    value: Matches your Aspirational Role
  NotificationOn:
    context: missing
    value: Notification on
  Withdraw:
    context: missing
    value: Withdraw
  Withdrawn:
    context: missing
    value: Withdrawn
  AddComments:
    context: missing
    value: Add Comments
  AddSkillsToSkillsPassport:
    context: missing
    value: Add Skills to Skills Passport
  CreateProfile:
    context: missing
    value: Create Profile
  DeleteDraft:
    context: missing
    value: Delete Draft?
  Duplicate:
    context: missing
    value: Duplicate
  DataRequestConfirmation:
    context: Message for Data Request
    value: Your request has been <NAME_EMAIL>. You will receive a copy of your data soon.
  GroupMembersWillReceiveEmailNotificationUponSharing:
    context: missing
    value: Group members will receive email notification upon sharing
  MarkAsAspirational:
    context: missing
    value: Mark as aspirational
  RemoveAsAspirational:
    context: missing
    value: Remove as aspirational
  SearchIndividuals:
    context: missing
    value: Search individuals...
  SearchSkillsFromYourSkillsPassport:
    context: missing
    value: Search skills from your skills passport.
  ThereWasAnErrorProcessingYourRequestPleaseTryAgain:
    context: missing
    value: There was an error processing your request. Please try again.
  ThereWasAnErrorSubmittingYourRequestPleaseTryAgain:
    context: missing
    value: There was an error submitting your request. Please try again.
  ThereWasAnErrorWhileFetchingActionsPleaseTryAgain:
    context: missing
    value: There was an error while fetching actions. Please try again.
  ThereWasAnErrorWhileTryingToCreateYourProfilePleaseTryAgain:
    context: missing
    value: There was an error while trying to create your profile. Please try again.
  ViewUserProfile:
    context: missing
    value: View User Profile
  YouHaveRemovedYourBookmark:
    context: missing
    value: You have removed your bookmark
  YouHaveSuccessfullyBookmarked:
    context: missing
    value: You have successfully bookmarked
  PotentialNextRole:
    context: missing
    value: Potential next role
  ActionsForThisSmartcardcardtitleByCardauthor:
    context: missing
    value: Actions for this SmartCard:{cardTitle} by {cardAuthor}
  AvailableOpeningFilled:
    context: missing
    value: "{available} opening filled"
  AvailableOpeningsFilled:
    context: missing
    value: "{available} openings filled"
  CountOfTotalUsersSelected:
    context: missing
    value: "{count} of {total} users selected"
  CountOutOfTotalGroupsSelected:
    context: missing
    value: "{count} out of {total} groups selected"
  CountOutOfTotalShown:
    context: missing
    value: "{count} out of {total} shown"
  CountSelected:
    context: missing
    value: "{count} selected"
  GrowInDemandSkillsNeededForTitle:
    context: missing
    value: Grow in demand skills needed for {title}
  MarkascompletelabelstandardizedtitleCardauthorname:
    context: missing
    value: "{markAsCompleteLabel}:{standardizedTitle} {cardAuthorName}"
  OnAssignedatformatteddate:
    context: missing
    value: on {assignedAtFormattedDate}
  PendingApplicationPending:
    context: missing
    value: "{pending} application pending"
  PendingApplicationsPending:
    context: missing
    value: "{pending} applications pending"
  ThereIsNotRecommendedContentForTitleAtThisTime:
    context: missing
    value: There is not recommended content for {title} at this time.
  ThisTypelabelWasPostedBy:
    context: missing
    value: This {typeLabel} was Posted by
  TotalTotalIndividuals:
    context: missing
    value: Total {total} individuals
  ValidUptoDate:
    context: missing
    value: 'Valid upto: {date}'
  ViewOptionsForMessageByCommentuser:
    context: missing
    value: View options for {message} by {commentUser}
  WeHaveLessCardsByYourRequestWeveJustGeneratedCountCards:
    context: missing
    value: We have less cards by your request. We've just generated {count} cards.
  YouAreViewingContentInLanguagenameDoYouWantToSeeAllContentInstead:
    context: missing
    value: You are viewing content in {languageName}. Do you want to see all content instead?
  YouHaveYourskillsnoOutOfAllskillsnoSkillsInYourSkillPassport:
    context: missing
    value: You have {yourSkillsNo} out of {allSkillsNo} skills in your skill passport.
  CreateAJourneyBeforeYouStartAddingContentToIt:
    context: missing
    value: Create a Journey before you start adding content to it
  JourneyNotFound:
    context: missing
    value: Journey not found
  PathwayNotFound:
    context: missing
    value: Pathway not found
  PleaseWaitDontRefreshThePage:
    context: missing
    value: Please Wait. Don't refresh the page.
  RemoveFromPathway:
    context: missing
    value: Remove from Pathway
  ThisContentHasBeenRemovedFromYourAssignmentList:
    context: missing
    value: This content has been removed from your assignment list.
  YouCanChangeThisInYourSmartcardSettings:
    context: missing
    value: You can change this in your SmartCard settings
  CreatedByCreator:
    context: missing
    value: 'Created by {creator} '
  DateAtTime:
    context: missing
    value: "{date}, at {time}"
  DeleteCardtype:
    context: missing
    value: Delete {cardType}
  EditCardtype:
    context: missing
    value: Edit {cardType}
  OpeningsOpening:
    context: missing
    value: "{openings} opening"
  OpeningsOpenings:
    context: missing
    value: "{openings} openings"
  PricesymbolPriceamount:
    context: missing
    value: " {priceSymbol} {priceAmount}"
  Scormtitle:
    context: missing
    value: "{scormTitle}"
  ViewCardtype:
    context: missing
    value: View {cardType}
  ViewCountMoreComments:
    context: missing
    value: View {count} more comments
  YouAreAboutToDeleteTheSnackbartype:
    context: missing
    value: You are about to delete the {snackBarType}
  YouAreABoutToArchiveContent:
    context: Confirmation modal before the content is archived
    value: Are you sure you want to archive the {snackBarType} {cardTitle}?
  ArchiveContentCanBeRestored:
    context: Note in confirmation modal for archival
    value: This process may take a while. The {snackBarType} will appear in the Archived tab and can be restored later.
  YouAreABoutToUnArchiveContent:
    context: Confirmation modal before the content is archived
    value: Are you sure you want to unarchive the {snackBarType} {cardTitle}?
  UnArchiveContentProcessWillTakeTime:
    context: Note in confirmation modal when unarchiving content
    value: The process might take a while, but soon it'll show up in your Content tab.
  YouAreAboutToReportCardtitle:
    context: missing
    value: You are about to report {cardTitle}
  YouAreAboutToReportCardtitleCreatedByCardcreator:
    context: missing
    value: You are about to report {cardTitle}, created by {cardCreator}
  YouAreNotAuthorizedToViewTheListOfUsermessage:
    context: missing
    value: You are not authorized to view the list of {userMessage}.
  YoureImpersonatingImpersonateeAndAllTheActivityIsBeingLogged:
    context: missing
    value: You're impersonating {impersonatee} and all the activity is being logged.
  CommentedOnSnippet:
    context: missing
    value: "commented on '{snippet}'"
  SelectCorrectLevel:
    context: Label when no skill level is set on skills modal
    value: Please set the correct level on the role
  NotDefined:
    context: When no value provided
    value: Not defined
  CantAddSkillBecauseUArchivedAdvanced:
    context: Tooltip for row explaining why its disabled
    value: We can't add this skill to your learning goals since you've already achieved advanced level.
  YouHaveXOfDesiredSkillsBasedOnYourProfile:
    context: Shows how much skills from list user already have
    value: You have {userSkills} out of {allSkills} of the desired skills based on your profile
  CardRedesignHeader:
    context: This will be the header for create smartcard section.
    value: Create a SmartCard, Pathway, Journey or Group
  CardRedesignSmartcardDescription:
    context: Description text for create smartcard section.
    value: Create your own content - articles, polls, quizzes and many more.
  CardRedesignPathwayDescription:
    context: Description text for create pathway section.
    value: A learning experience defined by a sequence of SmartCards.
  CardRedesignJourneyDescription:
    context: Description text for create journey section.
    value: A learning experience defined by a sequence of Pathways.
  CardRedesignGroupDescription:
    context: Description text for create group section.
    value: Invite your colleagues to share content and assignments with them.
  NoRecordsForTimePeriod:
    context: notification message for not found records on search table.
    value: We did not find any records present for selected time period
  MatchingSkills:
    context: Modal header for displaying list of skills and job levels
    value: Matching skills
  MatchedCandidateSkills:
    context: Show count of matched skills with total skills
    value: "{matchedSkills} of {totalSkills} skills match candidate's profile"
  SharePrivateCardMsg:
    context: warning message when user share a private card
    value: 'You are about to share a private {cardType}. Only users to whom this card has been shared will have access to it.'
  CreatorOfTheCard:
    context: name of the user who create the card
    value: Creator of the card
  YourLevel:
    context: Your skill level
    value: Your level
  CandidateLevel:
    context: Candidate skills level
    value: Candidate level
  RoleTargetLevel:
    context: level that is assigend to role
    value: Role target level
  StatusSkill:
    context: Skill status
    value: Status
  OnTarget:
    context: when user has sufficient level for skill
    value: On target
  OffTarget:
    context: when user doesnt have sufficient level for skill
    value: Off target
  ValueTargetLevel:
    context: 'e.g Job vacancy target level'
    value: '{value} target level'
  AlreadyHaveSomeSkills:
    context: Text that ask if user have this skill
    value: Already have some of these skills?
  AddThemToYourSkillsPassport:
    context: the second part of text AlreadyHaveSomeSkills
    value: Add them to your skills passport
  ValidDate:
    context: Date upto which card is valid
    value: 'Valid up to: {date}'
  SelectSkillsYouWantToAdd:
    context: Skills that user can add for the project
    value: Select Skills you want to add
  YourStreamIsScheduledAtStartDateScheduledTime:
    context: A message about a start time
    value: Your stream is scheduled at {startDate}, {scheduledTime}
  Generating:
    context: Loading indicator
    value: 'Generating...'
  GenerateContent:
    context: Action button
    value: Generate Content
  ErrorWhileAddingCardToTypeNoCardsWithThisTopic:
    context: Error message
    value: Error while adding card to {cardTypeLabel}. No cards with this topic
  DynamicPathwayAddsRandomSmartCardsAutomaticallyToThisPathwayUsingASpecificTopicOfYourChoice:
    context: Error message
    value: Dynamic Pathway adds random SmartCards automatically to this Pathway using a specific topic of your choice.
  EnterTopic:
    context: Error message command
    value: Enter topic
  EnterNumberOfCards:
    context: Error message command
    value: Enter number of cards
  YourManagerWillBeAbleToSeeYourProfilePerformanceReportsAndActivityData:
    context: Adding or editing your manager
    value: Your manager will be able to see your profile, performance, reports, and activity data
  UseThisFeaturToLeapTheUserToASpecifiedSmartCardInThePathwayWhenTheyPassOrFailTheQuiz:
    context: Popup message
    value: "Use this feature to 'leap' the user to a specified SmartCard in the Pathway when they pass or fail the Quiz"
  UpdatePathway:
    context: Editing and updating a pathway
    value: Update Pathway
  PathwayCreation:
    context: Creating a new pathway
    value: Pathway Creation
  Lock:
    context: Action to lock item
    value: LOCK
  SnackbarChannelEnableDisableMsg:
    context: notification message
    value: 'Notifications for this channel have been {text}. You will {notificationActive} receive notifications when new content is added to the channel.'
  ThisWillSendYouToASecuredEnvironment:
    context: Error warning
    value: This will send you to a secured environment
  YouAreAboutToCreateVersionForCardTitleCreatedByCardCreator:
    context: Warning about creating a new card version from a different author
    value: You are about to create version for {cardTitle}, created by {cardCreator}
  SuggestedSkills:
    context:  Skills suggested according to title and description
    value: Suggested Skills
  OpportunityBookmarked:
    context: "Bookmark action on opportunity, opportunity value: tm_job_role, tm_job_vacancy"
    value: "{opportunity} bookmarked"
  OpportunityUnbookmarked:
    context: "Unookmark action on opportunity, opportunity value: tm_job_role, tm_job_vacancy"
    value: "{opportunity} unbookmarked"
  OpportunityDismissed:
    context: "Dismiss action on opportunity, opportunity value: tm_job_role, tm_job_vacancy"
    value: "{opportunity} dismissed"
  OpportunityUndismissed:
    context: "Undismiss action on opportunity, opportunity value: tm_job_role, tm_job_vacancy"
    value: "{opportunity} undismissed"
  OpportunityMarkedAsAspirational:
    context: "Mark as aspirational action on opportunity, opportunity value: tm_job_role"
    value: "{opportunity} marked as aspirational"
  OpportunityUnmarkedAsAspirational:
    context: "Unmark as aspirational action on opportunity, opportunity value: tm_job_role"
    value: "{opportunity} unmarked as aspirational"
  MarkOpportunityAsAspirational:
    context: Action on role card to mark as aspirational
    value: Mark as aspirational {opportunity}
  UnMarkOpportunityAsAspirational:
    context: Action on role card to unmark as aspirational
    value: Remove as aspirational {opportunity}
  RemoveAspirationalRoleConfirmation:
    context: modal title for removing aspirational role which contains Action Plan
    value: Are you sure you want to remove this aspirational {opportunity}?
  RemoveAspirationalRoleDescription:
    context: warning message for removing aspirational role which contains Action Plan
    value: If you remove your aspirational {opportunity}, your action plan will be permanently lost
  MarkedAsAspirationalLimitWarning:
    context: "Warning displayed when a user reaches the limit of aspirated roles"
    value: "You have reached the maximum number ({max}) of aspirational {job_roles}."
  OpportunityMarkedAsAspirationalConfirmation:
    context: "Confirmation of marking opportunity as aspirational"
    value: Great, you have marked {title} as your Aspirational {opportunity}
  OpportunityShowedInterestConfirmation:
    context: "Confirmation of showing interest on opportunity"
    value: Thanks for your interest in this {opportunity}!
  OpportunityMatchesYourAspirational:
    context: "Indication on card that opportunity matches user aspirational"
    value: Matches your aspirational {opportunity}
  YourCurrentRole:
    context: Indicate that this is your current role
    value: Your current {role}
  VacancyAvailable:
    context: Indicate that there are vacancy available for that role
    value: "Similar {vacancy} available"
  MatchDescription:
    context: Explanation of matching on role card
    value: The match estimates how close your experience and skills fit this opportunity.
  ExcellentMatch:
    value: Excellent match
    context: Describes a match to an opportunity
  GoodMatch:
    value: Good match
    context: Describes a match to an opportunity
  FairMatch:
    value: Fair match
    context: Describes a match to an opportunity
  LowMatch:
    value: Low match
    context: Describes a match to an opportunity
  YourAspirationalRole:
    context: Indicate if current role is your aspirational
    value: Your aspirational {role}
  GrowSkillsRelatedToOpportunity:
     context: Recommended Learning description
     value: Grow in demand skills related to this {opportunity}.
  GoToOpportunityDetailsPageForTitle:
    context: Go to opportunity details page
    value: Go to {opportunity} details page for {title}
  BeginCourse:
    context: card suscription informative action
    value: Begin course
  SubscriptionViewAndConsume:
    context: card suscription informative action
    value: This will send you to a secured environment, where you can view and consume the course.
  SubscriptionCompleteYourTransaction:
    context: card suscription informative action
    value: This will send you to a secured environment, where you can complete your transaction.
  RescheduleBookingWarning:
    context: reschedule booking warning modal message.
    value: You are about to reschedule your booking. Keep in mind that you are allowed to reschedule it up to {maxAssessmentRebookingAllowed} times. After that no reschedules will be possible.
  RegistrationVILTSuccessSnackBar:
    context: informative message on SnackBar
    value: Your registration is awaiting approval. Once approved, you should be able to access the meeting.
  RegistrationVILTSuccessSnackBarRedesign:
    context: Registration successful message on SnackBar, when user restigered for a live event card
    value: "Registration successful! Please await approval from the event organizer"
  RecordingURL:
    context: informative message on vilt meeting label
    value: Recording URL
  MeetingDetails:
    context: informative message on vilt meeting label
    value: Meeting details
  MeetingLink:
    context: informative message on vilt meeting label
    value: Meeting Link
  RecurringAssignmentTooltip:
    context: informative tooltip message
    value: Recurring Assignments allows the assignor to create an assignment that will be reassigned to the learner at a predefined frequency
  NoReassignmentsInfoMsg:
    context: informative message
    value: Re-assignments cannot be made to users who have already completed the Quiz, Poll or Project Card
  ContentVisibilityLabel:
    context: informative message
    value: List shows only public Pathways/Journeys and count includes both public and private assets
  PreventDataLostMsg:
    context: informative message for data lost prevention
    value: There are unsaved changes on this tabs, please make sure you save your changes before switching or they would be lost.
  RemoveCardConfirmation:
    context: warning message on remove card modal
    value: Are you sure you want to remove this SmartCard from {type}?
  DeleteSkillConfirmation:
    context: warning message on delete skill modal
    value: Do you want to delete the {type} ?
  SkillPassportModalContentDesc:
    context: informative text on skill passport modal page
    value: "Skills Passport shows you list of all one's Skills, Credentials and Accomplishments"
  PublishContentSuccess:
    context: informative message on publish content successful
    value: "{type} {name} was successfully {updateText}!"
  PublishContentError:
    context: informative message on publish content error
    value: We have removed the Custom Code entered as it was already being used. Please edit the content and re-enter a new code.
  ProjectCardSubmissionErrorSnackBar:
    context: informative message on SnackBar
    value: Sorry, the URL you are trying to submit is from unapproved website or words.
  ContentManagementDescription:
    context: informative message on content management section
    value: See below the sections available to your channel. Each one is displayed as a horizontal list of content. Click on a section to see its content and use the options provided to make changes.
  ProviderLogoFileUploadDesc:
    context: provider logo file input description
    value: This logo will be displayed on the {type} instead of your profile picture
  WidgetPublicMsg:
    context: notification message for widget went public.
    value: This widget will be made public to your profile for everyone to see. You can check how it looks by viewing your public profile from the gear icon on the top right corner on your profile.
  OfExperience:
    context: time of experience or expertise
    value: '{value} of experience'
  SaveAndGo:
    context: text label
    value: Save & Go
  TotalIndividualsSelected:
    context: text label
    value: '{selected} out of {total} individuals selected'
  RecommendToUpskillFor:
    context: text label
    value: 'Recommend to Upskill for {skill_name}'
  UpskillModalDesc:
    context: text label
    value: Recommend new skill level as a learning goal to the team member who are beginners, so they can receive relevant recommendations and upskill themselves.
  NewSkillModalDesc:
    context: text label
    value: Recommend new skill as a learning goal to your team member, so they can receive relevant recommendations and upskill themselves.
  CongratsAccessSalute:
    context: text label
    value: 'Congratulations! You now have access to {orgName} till - '
  ContentManagementSectionHeader:
    context: text label for content type section
    value: '({sectionDetailsType} Section)'
  RemoveAssociatedContentMsg:
    context: error message when deleting content with assiated content.
    value: 'Content(card/Pathway) is associated with a few Pathways/Journeys, please remove the associations before deleting or ask admin to delete the content.'
  ContentCurationDetails:
    context: text description on content curation relation to a channel
    value: 'This {typeLabel} relates to channel {reasonLabel}'
  LockSmartCard:
    context: text label
    value: Lock SmartCard
  UnlockSmartCard:
    context: text label
    value: Unlock SmartCard
  AriaLabelForYesButtonOnLanguageBanner:
    context: aria-label for language banner to assist screen reader users.
    value: You are viewing content in {languageName}. Do you want to see all content instead? click on yes to proceed
  AlreadyShared:
    context: Indicates user with whom the opportunity is already shared
    value: Already shared
  AddOptionalNote:
    context: Optional message to user
    value: Add optional note
  RejectMentorshipRequest:
    context: Reject mentorship request modal title
    value: Reject {tm_tm_mentorship} request?
  AreYouSureYouWantToRejectAMentorshipRequest:
    context: Reject mentorship request modal title
    value: Are you sure you want to reject a request from {name}? This action can't be undone.
  OptionalNoteToTheRequester:
    context: placeholder for input box of rejection comment
    value: Optional note to the requester
  MentorshipRequestRejected:
    context: flash message displaying mentorship request rejected confirmation.
    value: '{tm_tm_mentorship} request rejected!'
  YesReject:
    context: Reject button on the rejection message modal
    value: Yes, reject
  NoCancel:
    context: Cancel button on the rejection message modal
    value: No, cancel
  MessageFromName:
    context: Title for mentor message modal
    value: Message from {name}
  MessageSentOn:
    context: Date on which the message has been sent
    value: 'Sent on: {date}'
  CurrentlySharedWith:
    context: Date on which the message has been sent
    value: Currently shared with
  GoToProjectsDetailsPageForTitle:
    context: Go to projects details page for title
    value: Go to {tm_tm_project} details page for {title}
  SME:
    value: SME
    context: SME role
  MarkAsComplete2:
    context: informative text for card marked as completed
    value: Mark as complete
  Uncomplete:
    context: Uncomplete action on the card
    value: Uncomplete
  CountOutOfTotalNounSelected:
    context: Total count selected
    value: '{count} out of {total} {noun} selected'
  LooksLikeWeAlreadyKnowYouALittleBitYouAreJustFewStepsAwayFromCreatingAProfile:
    context: Encouragement text of becoming a mentor
    value: Looks like we already know you a little bit! You are just few steps away from creating a profile.
  AllTheInformationMentionedBelowWillBeDisplayedOnYourMentorProfile:
    context: Hint text of becoming a mentor
    value: All the information mentioned below will be displayed on your {tm_tm_mentor} profile.
  YouCurrentlyDoNotHaveAnySkillsInYourSkillsPassportGetStartedByAddingSkills:
    context: Message for user to add skills in skills passport
    value: You currently do not have any skills in your skills passport. Get started by adding skills to be able to create your {tm_tm_mentor} profile.
  WeAreSorryButThereIsCurrentlyNoProjectsAvailableForThisSkill:
    context: No data placeholder for project recommendations
    value: We're sorry, but there is currently no {tm_tm_projects} available for this skill.
  WeAreSorryButThereIsCurrentlyNoMentorsAvailableForThisSkill:
    context: No data placeholder for mentor recommendations
    value: We're sorry, but there is currently no {tm_tm_mentors} available for this skill.
  WeAreSorryButThereAreCurrentlyNoProjectsAvailableForThisOpportunityType:
    context: No data placeholder for project recommendations based on opportunity type
    value: We're sorry, but there are currently no {tm_tm_projects} available for this {opportunityType}.
  WeAreSorryButThereAreCurrentlyNoMentorsAvailableForThisOpportunityType:
    context: No data placeholder for mentor recommendations based on opportunity type
    value: We're sorry, but there are currently no {tm_tm_mentors} available for this {opportunityType}.
  RequestedOn:
    context: Requested status
    value: Requested on
  RejectedOn:
    context: Rejected status
    value: Rejected on
  AcceptedOn:
    context: Accepted status
    value: Accepted on
  Preferences:
    context: Preferences title
    value: Preferences
  SetYourInterests:
    context: 'e.g Set your learning goals'
    value: 'Set your {interests}'
  HiName:
    context: Greet user
    value: Hi {name}!
  PleaseEnterBadgeName:
    context: input label description for skill passport badge name
    value: Please enter the Badge name.
  PleaseEnterBriefBadgeBackground:
    context: input label description to encurage the user to add a brief background about the badge is creating
    value: Please add a brief background about this badge.
  NoContentAvailableDefaultDescription:
    context: default description message for no content available components {type} is a dynamic paramater can be group/channel etc
    value: Edit your {type} to add content and customize its layout.
  NoContentAvailableEditButtonLabel:
    context: button label for edit ({type} can be group/channel) when not available content component is showed. e.g. Edit Group or Edit Channel
    value: Edit {type}
  NoContentAvailableMessageLabel:
    context: principal message when no content available component is showed, this is the body message where {type} can be group/channel
    value: Your {type} has no content yet!
  NoContentAvailableDescriptionLabel:
    context: description message when channel or group do not have description where {type} can be group/channel
    value: Your {type} has no description yet!
  NoContentAvailableAddDescriptionLabel:
    context: description message when channel or group to add description where {type} can be group/channel
    value: Edit your {type} to add description.
  NoContentAvailableMessageChannelDeleted:
    context: custom message for channels if some section is deleted or disabled
    value: This section of channel is deleted or disabled
  channel:
    context: label for channel feature common word mmust be lowercase.
    value: channel
  group:
    context: label for group feature common word mmust be lowercase.
    value: group
  NoContentAvailableMessageFeaturedTab:
    context: custom description message for no content available component
    value: Your featured tab has no content yet!
  NoContentAvailableMessageCustomTab:
    context: custom description message for no content available component
    value: Your custom tab has no content yet!
  NoContentAvailableMessageGroupNotAssigned:
    context: description message for no content available component
    value: Your group has no assigned content yet!
  NoContentAvailableMessageGroupNoChannelsAssociated:
    context: description message for no content available component when group has no associated channels
    value: Your group has no associated channels yet!
  NoContentToDisplay:
    context: custom message for No Content Available component
    value: No Content to Display
  YourCSVisBeingProcessed:
    context: message for csv file processed
    value: Your CSV is being processed, we'll send you a notification when it's done
  PleaseSelectOrDragCSVFile:
    context: message for csv file input error
    value: Please select or drag and drop CSV file from your computer
  PleaseCheckCSVFormatting:
    context: message for csv file input error
    value: Please check CSV for correct formatting. Use the template provided
  PleaseEnterCredentialName:
    context: input description to request user his active credential name
    value: Please Enter the {activeCredential} Name
  SelectUpTo6SkillForActiveCrendential:
    context: input description to request user to select up to 6 skill for his active credential
    value: Select up to 6 skills for this {activeCredential}
  PleaseAddBriefCredentialBackground:
    context: input description to request user to add a brief background of his active credential
    value: Please add a brief background about this {activeCredential}
  EditActiveCredentialTitle:
    context: modal title for edit active credential
    value: Edit {activeCredential}
  ActiveCredentialDoesNotExpire:
    context: descriptive text notifying user the active credential do not expire
    value: This {activeCredential} does not expire
  MarkWhenYouObtainActiveCredential:
    context: descriptive text notifying user to mark when obtained the active credential
    value: Please mark when did you obtain this {activeCredential}
  SkillClassificationErrorMessage:
    context: error message to notify user the skill selected is not as org skills classifications enabled
    value: This skill is not as per the skills classifications enabled for your organization. Please modify the skill and select from the suggested recommendations.
  EnterCredentialIssuerNameErrorMessage:
    context: error message to notify user to enter credential issuer name
    value: "Please enter the {activeCredential} issuer's name."
  PleaseEnterActiveCredentialIDErrorMessage:
    context: error message to notify user to enter active credential id
    value: Please enter the {activeCredential} ID.
  ID:
    context: Label for identification number
    value: ID
  URL:
    context: Label for URL Uniform Resource Locator
    value: URL
  PublicType:
    context: label to inform Public group or channel where 'type' value can be group or channel
    value: Public {type}
  PublicChannelDescription:
    context: description for a Public channel
    value: All users will be able to discover, access, follow, and unfollow this {type}
  PublicGroupDescription:
    context: description for a Public group
    value: All users will be able to discover, access, and join this group
  PrivateType:
    context: label to inform Private group or channel where 'type' value can be group or channel
    value: Private {type}
  PrivateChannelDescription:
    context: description for a private channel
    value: Only Restricted users will be able to discover and access this {type}.
  PrivateGroupDescription:
    context: description for a private group
    value: Only members or invited users will be able to discover and access this group
  WhoGrantedCredential:
    context: input label to request the user the name of the entity who granted his credential
    value: Who granted you this {activeCredential}
  SmartcardTypeTitle:
    context: Label for smartcard form title input where type can be pathway/journey
    value: "{type} Title"
  SmartcardTypePlaceholderTitle:
    context: Placeholder for smartcard form title input where type can be pathway/journey
    value: Enter {type} title here
  SmartcardTypeDescriptionPlaceholder:
    context: Descriptionn placeholder for smartcard form input where type can be pathway/journey
    value: Enter {type} description here
  ShareContentPrivateDescription:
    context: description body text on confirmation modal when user share a private content where {cardType} can be pathway/journey/smartcard
    value: You are about to share a private {cardType}. Only users to whom this card has been shared will have access to it.
  CLCIncompleteCardTooltip:
    context: tooltip message for continuous learning when pathway/journey marked as incomplete, {cardTypeLabel} is a dynamic value can be pathway or journey
    value: Marking the {cardTypeLabel} as incomplete will not change the Continuous Learning hour or the score associated with it.
  CLCCompleteAllCardsBeforeCompleteTooltip:
    context: tooltip message for continuous learning over mark as complete button on an incomplete content where {type} can be pathway or journey.
    value: Please complete all pending cards before marking the {type} as complete.
  CLCPublishCardsBeforeCompleteTooltip:
    context: tooltip message for continuous learning over mark as complete button on an unpublished content where {type} can be pathway or journey.
    value: You must publish the {type} before marking it as complete.
  CLCContentCompletedTooltip:
    context: tooltip message for continuous learning content marked as completed where {type} can be pathway or journey.
    value: You have successfully completed the {type}.
  CLCContentMarkedIncompletedTooltip:
    context: tooltip message for continuous learning content marked as incompleted where {type} can be pathway or journey.
    value: You have marked this {type} as incomplete.
  PrivatePathwayMessage:
    context: Private pathway description message.
    value: You do not have permission to access this Pathway. Please reach out to the user who has shared this Pathway with you to provide access.
  YouHaveAssignedThisContentToYourself:
    context: popup message box, it intent to notify the user that he has assigned content to himself.
    value: You have assigned this {typeOfCard} to yourself.
  ContentTypeSuccessfullyShared:
    context: popup message for content card shared successfully, where {cardType} is dynamic value can be SmartCard/Pathway/Journey
    value: "{cardType} successfully shared"
  ContentSuccessfullySharedWithTwoUsers:
    context: popup successful message for content card shared with two others, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have shared this {cardType} with {name} and {count} other
  ContentSuccessfullySharedWithTwoGroups:
    context: popup successful message for content card shared with two other groups, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have shared this {cardType} with {name} and {count} other group
  ContentSuccessfullySharedWithMoreThanTwoUsers:
    context: popup successful message for content card shared with more than two other users, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have shared this {cardType} with {name} and {count} others
  ContentSuccessfullySharedWithMoreThanTwoGroups:
    context: popup successful message for content card shared with more than two other groups, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have shared this {cardType} with {name} and {count} other groups
  ContentSuccessfullySharedWithOne:
    context: popup successful message for content card shared with one User/Group, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user
    value: You have shared this {cardType} with {name}
  ContentUnSharedWithTwoUsers:
    context: popup message for content card UN-shared with two others, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have unshared this {cardType} with {name} and {count} other
  ContentUnSharedWithTwoGroups:
    context: popup message for content card UN-shared with two other groups, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have unshared this {cardType} with {name} and {count} other group
  ContentUnSharedWithMoreThanTwoUsers:
    context: popup message for content card UN-shared with two other users, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have unshared this {cardType} with {name} and {count} others
  ContentUnSharedWithMoreThanTwoGroups:
    context: popup message for content card UN-shared with two other users, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have unshared this {cardType} with {name} and {count} other groups
  ContentUnSharedWithOne:
    context: popup message for content card UN-shared with one User/Group, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have unshared this {cardType} with {name}
  AssignmentUpdateInProgress:
    context: popup message intent to inform user that assignment update action is in progress.
    value: Assignment update is in progress and will be completed shortly
  ErrorMessageWhenDeleteAssignment:
    context: popup message intent to inform that an error occurred while deleting an assignment.
    value: An error occurred while delete assigning from users
  ContentAssignedWithMoreThanTwoUsers:
    context: popup message to inform user that content assign action done with more than two users, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have assigned this {type} to {name} and {count} others
  ContentAssignedWithTwoUsers:
    context: popup message to inform user that content assign action done with two other users, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have assigned this {type} to {name} and {count} other
  ContentAssignedWithOne:
    context: popup message to inform user that content assign action done with one user, where {cardType} is dynamic value can be SmartCard/Pathway/Journey {name} name of the first user, {count} is number of many users its shared
    value: You have assigned this {type} to {name}
  ErrorMessageWhenAssingningCard:
    context: popup message intent to inform that an error occurred while assigning a content card.
    value: An error occurred while assigning this card
  NothingHadBeenChanged:
    context: popup message intent to inform that nothing change with the action performed.
    value: Nothing had been changed!
  NoUserWasFoundForTheCurrentFilters:
    context: popup message intent to inform that no user was found for the current filters.
    value: No user was found for the current filters.
  AssignedToADynamicGroup:
    context: popup message to inform user, content action assigned to a dynamic group.
    value: Assigned to a dynamic group
  AfterAssigningContentNewGroup:
    context: support label description for create new group.
    value: After assigning the content, you will be asked for details for your new group
  AfterSharingContentNewGroup:
    context: support label description for create new group.
    value: After sharing the content, you will be asked for details for your new group
  YouMayNotHaveAccessToContentClickOnShowMore:
    context: Message shown to user when there are no accessible content asking user to click on show more btn
    value: You may not have access to all content. Click on Show More
  YouDoNotHavePermissionToAccessThisJourney:
    context: Privacy error message for Journey.
    value: You do not have permission to access this Journey. Please reach out to the user who has shared this Journey with you to provide access.
  ErrorCannotEditContent:
    context: Cannot edit content error popup message.
    value: Cannot edit this content as some of it's attributes may not be available on your platform
  YouAreSeeingThisCardBecauseYouFollowedEntity:
    context: notification message on card personalized content screen where {cardType} could be any existent type of card like pathway journeys etc, and {entityAttribute} the related followed entity
    value: You are seeing this {cardType} because you followed {entityAttribute}
  YouAreSeeingThisCardBecauseYouAreMemberOfEntity:
    context: notification message on card personalized content screen where {cardType} could be any existent type of card like pathway journeys etc, and {entityAttribute} the related entity you are member of
    value: You are seeing this {cardType} because you are member of {entityAttribute}
  PleaseSelectHowTheContentWillBeUnlocked:
    context: notification message for unlocked content consumption on pathway and journey modal where {type} could be pathway/journey
    value: Please select how the content in the {type} will be unlocked for consumption
  AllSmartCards:
    context: label input checkbox for selecting all smartcards, used in filters dropdown
    value: All SmartCards
  SmartCardFeedRemovalNotification:
    context: notification message smartcard removal from feed
    value: This SmartCard will be removed from your feed and you can't find it anymore. Do you want to remove this from your feed?
  UCanRemoveSkillInSkillsPassport:
    context: used by skill checkbox
    value: You can remove the skill in your Skills Passport
  CPEcourses:
    value: CPE courses
    context: CPE courses filter heading
  FilterCategories:
    context: Categories filter heading
    value: Categories
  YouDoNotHaveAccessToThisProfile:
    context: text to display that user does not exist
    value: You do not have access to this Profile.
  YouDoNotHaveAccessToViewThisProfile:
    context: text to display when profile view is restricted.
    value: You do not have access to view this Profile.
  Information:
    context: support label description for tooltips
    value: Information
  SkillVisibilityPrivateIfManagersAllowedToViewSkills:
    context: text description for Skill Private Visibility
    value: This widget will not be visible on your public profile. Your skills will also be hidden in other places like Leaderboard, People, etc. However, your skills will be visible to your Manager.
  SkillVisibilityPrivateIfManagersNotAllowedToViewSkills:
    context: text description for Skill Private Visibility when Manager Dashboard is enabled and the Allow Manager to view Skills toggle is OFF
    value: This widget will not be visible on your public profile. Your skills will also be hidden in other places like Leaderboard, Manager Dashboard, People, etc.
  FileHasDoubleExtension:
    context: This decription is used to guide users that double extension files are not allowed.
    value: File has double extension
  NameGroupAssignmentAlreadyExists:
    context: This description is used to show error when user tries to add similar name for group assignment
    value: Name Group Assignments already exists
  NameFeaturedPostsAlreadyExists:
    context: This description is used to show error when user tries to add similar name for Featured posts
    value: Name Featured Posts already exists
  NameAssociatedChannelsAlreadyExists:
    context: This description is used to show error when user tries to add similar name for Associated channel
    value: Name Associated Channels already exists
  NameAlreadyExists :
    context: This description is used to show error when user tries to add similar name on group section
    value: Name already exists
  BackToHomePage:
    context: Button title for going back to home page
    value: 'Back to home page'
  SubOrg:
    context: This is the heading to indentify users subOrg name.
    value: Sub-Org
  AddToCalendar:
    context: button label
    value: Add to Calendar
  SelectCityDistrict:
    context: This will be placeholder for textfield to enter address
    value: Select City/District
  DownloadRegistrationList:
    context: button label
    value: Download Registration List
  YouHaveSuccessfullyUnregisteredFromTheEvent:
    context: toast message
    value: You have successfully unregistered from the event
  RegistrationSuccessfuPleaseAwaitApproval:
    context: toast message
    value: "Registration successful! Please await approval from the event organizer"
  AreYouSureYouWantToMarkAllMember:
    context: confirmation message
    value: "Are you sure you want to mark all member(s) as complete/attended for this live event? Once confirmed, this action cannot be undone"
  ClickToSortNames:
    context: description of click sort button
    value: click to sort attendees names
  ClickToSortEmail:
    context: description of click sort button
    value: click to sort Email
  ClickToSortRegisteredOn:
    context: description of click sort button
    value: click to sort registered on
  ClickToSortRegisteredTime:
    context: description of click sort button
    value: click to sort registered Time
  ClickToSortStatus:
    context: description of click sort button
    value: click to sort status
  hour:
    context: complementary label to indicate the time of learning in hour
    value: hour
  hours:
    context: complementary label to indicate the time of learning in hours plural
    value: hours
  VersionName:
    context: Header in version history table of smartcard
    value: Version Name
  CreatedBy:
    context: Header in version history table of smartcard
    value: Created By
  Code:
    context: Header in association table in card insights of smartcard
    value: Code
  Move:
    context: label of move button that can be used to move anthing
    value: Move
  Moving:
    context: label of move button that is shown in the state of while content is moving state
    value: Moving
  Patent:
    context: missing
    value: Patent
  RegisterToReceiveTheMeetingLink:
    value: Register to receive the meeting link
    context: description of the live event registration
  Announcements:
    context: title for the Announcements component
    value: Announcements
  PleaseSelectStartDate:
    context: Error message for start date
    value: "Please select start date..."
  PleaseSelectEndDate:
    context: Error message for end date
    value: "Please select end date..."
  PleaseSelectATitle:
    context: Error message for title
    value: "Please select a title..."
  PleaseSelectCompanyName:
    context: Error message for company name
    value: "Please select company name..."
  PleaseSelectStartAndEndDate:
    context: Error message for start and end date
    value: "Please select start and end date..."
  SaveAndContinue:
    context: footer cta for complete your profile modal
    value: Save and continue
  SkipForNow:
    context: footer cta for complete your profile modal
    value: Skip for now
  Steps:
    context: current step number in complete your profile modal
    value: steps
  AddExperienceByScanningYourResumeOrAddItManually:
    context: Work experience step description in complete your profile modal
    value: Add experience by scanning your resume or add it manually
  WorkExperience:
    context: Work experience step title in complete your profile modal
    value: Work experience
  AddMoreExperience:
    context: Add more experience button in complete your profile modal
    value: Add more experience
  CurrentSkills:
    context: Current skills step title in complete your profile modal
    value: Current skills
  WeHaveIdentifiedTopSkillsThatYouPossessBasedOnYourJobRole:
    context: Current skills step description in complete your profile modal
    value: We have identified top skills that you possess based on your job role. You can add more skills or remove them.
  CompleteYourProfile:
    context: complete your profile modal title
    value: Complete your profile
  NameOfTheInventor:
    context: missing
    value: Name of the inventor(s)
  PatentURL:
    context: missing
    value: Patent URL
  ApplicationNumber:
    context: missing
    value: Application Number
  PatentNumber:
    context: missing
    value: Patent Number
  FiledOn:
    context: missing
    value: Filed On
  DateOfPatent:
    context: missing
    value: Date of Patent
  PdfJpegPngDocx:
    context: missing
    value: pdf, jpeg, jpg, png, docx, doc
  AwardedBy:
    context: missing
    value: Awarded By
  EnterText:
    context: missing
    value: Enter Text
  EnterURL:
    context: missing
    value: Enter URL
  EnterNumber:
    context: missing
    value: Enter Number
  ThereAreSomeUnsavedLearningGoals:
    context: Unsaved learning goals warning message
    value: There are some unsaved learning goals
  SkillsToDevelop:
    context: Complete your profile step title for skills to develop
    value: Skills to develop
  ChooseUpToNSkillsRelatedToYourRoleAndWellSuggestRelevantLearningContent:
    context: Complete your profile step description for skills to develop
    value: "Choose up to {skillLimit} skills related to your role and we’ll suggest relevant learning content"
  YouCannotAddMoreThanNSkills:
    context: Error message when user adds more than specified skills
    value: You can't add more than {skillLimit} skills
  SetYourCareerPreferencesToHelpUsRecommendSuitableJobVacanciesForYou:
    context: Complete your profile step description for career preferences
    value: Set your career preferences to help us recommend suitable job vacancies for you.
  YouHaveSuccessfullyCompletedYourProfile:
    context: Confirmation modal completed state
    value: You have successfully completed your profile
  YouHaveSuccessfullyAddedInformationToYourProfile:
    context: Confirmation modal updated state
    value: You have successfully added information to your profile
  ProfileCompleted:
    context: Confirmation modal completed state title
    value: Profile completed
  ProfileUpdated:
    context: Confirmation modal updated state title
    value: Profile updated
  Participate:
    context: Action label for starting a project
    value: Participate
  LetYourProjectOwnerKnowThatYoureStillInterestedIfYouCanNoLongerParticipateYouCanWithdraw:
    context: label for starting a limited capacity project
    value: Let your project owner know that you're still interested. If you can no longer participate, you can withdraw.
  GladYouAreParticipating:
    context: Icon label for participation confirmation modal
    value: Glad you're participating!
  FollowAnyInstructionsInTheProjectDescriptionOrConnectWithTheProjectOwnerForTheNextSteps:
    context: Description for participation confirmation modal
    value: Follow any instructions in the project description or connect with the project owner for the next steps.
  ParticipationConfirmed:
    context: Action label for starting a project
    value: Participation confirmed
  Integrations:
    context: Submenu for the settings page
    value: Integrations
  PleaseSelectACorrectDate:
    context: Error message for entering a date
    value: Please select a correct date...
  PleaseEnterATitle2:
    context:  Error message for entering a title
    value: Please enter a title...
  PleaseEnterACompanyName:
    context:  Error message for entering a company name
    value: Please enter a company name...
  ViltRegistrationTableSummarizeFooter:
    context:  Live event users registration list footer label to summarize user selection.
    value: '{userSelectedCount} out of {userTotalCount} individuals selected'
  ViltRegistrationTableEmpty:
    context:  Live event users registration list empty state table message
    value: The user list is currently empty
  ViltRegistrationTableMarkAsCompleteConfirmationMsg:
    context:  Live event users registration table mark as complete bulk action confirmation modal body message.
    value: "Are you sure you want to mark the cards as complete for the selected {totalUsersSelected} attendees? The attendees will be moved to the Completed tab."
  ViltRegistrationTableMarkAsInCompleteConfirmationMsg:
    context:  Live event users registration table mark as incomplete bulk action confirmation modal body message.
    value: "Are you sure you want to mark the users as 'Incomplete'? Doing so will move them back to the 'Approved' state, and they will be removed from the 'Completed' state."
  ViltRegistrationTableApproveConfirmationMsg:
    context:  Live event users registration table approve registration bulk action confirmation modal body message.
    value: "You're about to approve {totalUsersSelected} member requests. Are you sure you want to approve them?"
  ViltRegistrationTableDeclineConfirmationMsg:
    context:  Live event users registration table decline registration bulk action confirmation modal body message.
    value: "You're about to decline {totalUsersSelected} member requests. Are you sure you want to decline them?"
  Declined:
    context:  Live event users registration status Declined label
    value: Declined
  Unregistered:
    context:  Live event users registration status Unregistered label
    value: Unregistered
  Edited:
    context:  When the comment has been edited at a specific date
    value: Edited
  YouHaveWithdrawnFromTitleProject:
    context: Confirmation message for withdrawing a project
    value: 'You have withdrawn from "{title}" project'
  YouHaveConfirmedParticipationForTitleProject:
    context: Confirmation message for participating a project
    value:  'You have confirmed participation for "{title}" project'
  ChooseSkillsRelatedToYourRoleAndWellSuggestRelevantLearningContent:
    context: Complete your profile step description for skills to develop
    value: "Choose skills related to your role and we’ll suggest relevant learning content"
  ProfileTab:
    context: Submenu for the settings page
    value: Profile Details
  NotificationTab:
    context: Submenu for the settings page
    value: Notifications
  OtherTab:
    context: Submenu for the settings page
    value: Other Details
  CareerTab:
    context: Submenu for the settings page
    value: Career Preferences
  IntegrationDescription:
    context: Description text under Integration tab in settings
    value: Contribute to your overall learning progress. Connect through available integrations to monitor all your progress in one place.
  SkillDirectoryTitleText:
    context: Skill directory top label text
    value: 'Skills related to the Job Family {skillRole}'
  ViltRegistrationMarkAsCompleteSuccess:
    context: snackbar message for bulk mark as completed vilt card action on vilt registration table
    value: Member(s) have been successfully marked as completed.
  AddToPathwayHeader:
    context: Label to Add to Pathway header for card Options (3 dots on any Content card)
    value: Add to Pathway(s)
  AddToJourneyHeader:
    context: Label to Add to Pathway header for card Options (3 dots on any Content card)
    value: Add to Journey(s)
  CreateVersionContent:
    context: Label to Add to Pathway header for card Options (3 dots on any Content card)
    value: Create Version Content
  GroupAssignmentPreferences:
    context: Group Assignment Preferences heading
    value: Group Assignment Preferences
  AssignToBothExistingAndFutureGroupMembers:
    context: Assign to both existing and future Group members radio button label
    value: Assign to both existing and future Group members
  AssignToExistingGroupMembersOnly:
    context: Assign to existing Group members only radio button label
    value: Assign to existing Group members only
  AssignToFutureGroupMembersOnly:
    context: Assign to future Group members only radio button label
    value: Assign to future Group members only
  Custom:
    context: custom label for entering days
    value: Custom
  DaysValue:
    context: days format
    value: Days
  ContentAssignedToExistingGroupMembersOnly:
    context: inform that content is assigned  Existing Group members
    value: Content assigned to Existing Group Members only
  ContentAssignedToFutureGroupMembersOnly:
    context: inform that content is assigned  Future Group members
    value: Content assigned to Future Group Members only
  ContentAssignedToBothExistingAndFutureGroupMembers:
    context: inform that content is assigned to both Existing and Future Group members
    value: Content assigned to both Existing and Future Group members
  GroupPreferencesTooltip:
    context: tooltip for group assignment preference
    value: This setting takes priority over the Auto-Assign Content to New Group Members settings
  DueDateToolTip:
    context: tooltip when user is selecting due date
    value: The due date will be calculated based on the start date
  StartDateToolTip:
    context: tooltip when user is selecting start date
    value: The Assignment will start from specified start date
  content:
    context: to be used on reporting any card
    value: content
  GroupSmallCase:
    context: to be used in message where there is no content present in group
    value: group
  ConfigureHomePage:
    context: Edit landing page title to be used in menu tab
    value: Configure Home Page
  followers:
    context: filter label in people search
    value: followers
  following:
    context: filter label in people search
    value: following
  AutoArchiveSmartcardInfo:
    context: tooltip for auto-archive date of smartcard
    value: 'This SmartCard will be auto-archived on {archiveDate}'
  AutoArchiveJourneyInfo:
    context: tooltip for auto-archive date of journey
    value: 'This Journey will be auto-archived on {archiveDate}'
  AutoArchivePathwayInfo:
    context: tooltip for auto-archive date of pathway
    value: 'This Pathway will be auto-archived on {archiveDate}'
  OpenPoll:
    context: The label on the control allows opening the poll view with all questions. The label shows when there are more than 3 questions.
    value: Open poll
  SnakebarMsgIfPathwayBookmarkedRemoved:
    context: Label is used to display a message when a Pathway bookmark is removed
    value: This Pathway has been removed from your Bookmarks.
  SnakebarMsgIfJourneyBookmarkedRemoved:
    context: Label is used to display a message when a Pathway bookmark is removed
    value: This Journey has been removed from your Bookmarks.
  SnakebarMsgIfSmartCardBookmarkedRemoved:
    context: Label is used to display a message when a Pathway bookmark is removed
    value: This SmartCard has been removed from your Bookmarks.
  SnakebarMsgIfPathwayBookmarked:
    context: Label is used to display a message when a Pathway is bookmarked
    value: Done! This Pathway has been Bookmarked and can now be found in your bookmarked content.
  SnakebarMsgIfJourneyBookmarked:
    context: Label is used to display a message when a Journey is bookmarked
    value: Done! This Journey has been Bookmarked and can now be found in your bookmarked content.
  SnakebarMsgIfSmartCardBookmarked:
    context: Label is used to display a message when a SmartCard is bookmarked
    value: Done! This SmartCard has been Bookmarked and can now be found in your bookmarked content.
  SelectAllSkills:
    context: checkbox that selects all skills
    value: Select all skills
  CLCPathwayCompletedTooltip:
    context: tooltip message for continuous learning content Pathway marked as completed.
    value: You have successfully completed the Pathway.
  CLCJourneyCompletedTooltip:
    context: tooltip message for continuous learning content Journey marked as completed.
    value: You have successfully completed the Journey.
  CLCSmartCardCompletedTooltip:
    context: tooltip message for continuous learning content SmartCard marked as completed.
    value: You have successfully completed the SmartCard.
  CLCPathwayMarkedIncompletedTooltip:
    context: tooltip message for continuous learning content Pathway marked as incompleted.
    value: You have marked this Pathway as incomplete.
  CLCJourneyMarkedIncompletedTooltip:
    context: tooltip message for continuous learning content Journey marked as incompleted.
    value: You have marked this Journey as incomplete.
  CLCSmartCardMarkedIncompletedTooltip:
    context: tooltip message for continuous learning content SmartCard marked as incompleted.
    value: You have marked this SmartCard as incomplete.
  YouHaveAlreadyWithdrawnFromThisOpportunity:
    context: toast message for withdrawn project
    value: "You've already withdrawn from this {opportunity}"
  YouAreAlreadyParticipatingInThisOpportunity:
    context:  toast message for participated project
    value: "You're already participating in this {opportunity}"
  MoreOpportunityActions:
    context: "label for three dot action button, after this text, word button will be spoken (a11y)"
    value: "{opportunity} more actions"
  MentorRequestMentorship:
    context: "label for the button, after this text, word button will be spoken (a11y)"
    value: "{mentor} request mentorship"
  ShareOpportunityWithLocation:
    context: button for sharing job
    value: "Share {Opportunity}, location {location}"
  DismissOpportunity:
    context: button label
    value: "Dismiss {opportunity}"
  UndismissOpportunity:
    context: button label
    value: "Undismiss {opportunity}"
  DismissOpportunityWithLocation:
    context: "button label, after this text, word button will be spoken (a11y)"
    value: "Dismiss {opportunity}, location {location}"
  UndismissOpportunityWithLocation:
    context: "button label, after this text, word button will be spoken (a11y)"
    value: "Undismiss {opportunity}, location {location}"
  BookmarkOpportunity:
    context: button label
    value: "Bookmark {opportunity}"
  UnbookmarkOpportunity:
    context: button label
    value: "Unbookmark {opportunity}"
  BookmarkOpportunityWithLocation:
    context: "button label, after this text, word button will be spoken (a11y)"
    value: "Bookmark {opportunity}, location {location}"
  UnbookmarkOpportunityWithLocation:
    context: "button label, after this text, word button will be spoken (a11y)"
    value: "Unbookmark {opportunity}, location {location}"
  MoreSkillsForOpportunity:
    context: "button label, after this text, word button will be spoken (a11y)"
    value: "{opportunity} {nmbOfSkills} more skills"
  ShowLessSkillsOpportunity:
    context: "button label, after this text, word button will be spoken (a11y)"
    value: "{opportunity} show less skills"
  GoBackToPreviousPageDescriptionLabel:
    context: Prompt for going to the previous page, without saving changes
    value: "You are about to go back to the previous page. Please be aware that doing so will remove all the unsaved changes you have implemented on this page. This action cannot be undone. How would you like to proceed?"
  GoBackToPreviousStepDescriptionLabel:
    context: Prompt for going to thr previous step, without saving changes
    value: "You are about to go back to the previous step. Please be aware that doing so will remove all the unsaved changes you have implemented on this page. This action cannot be undone. How would you like to proceed?"
  GoBackToPreviousPage:
    context: go back to previous page button
    value: "Go Back to Previous page?"
  NotAValidFileType:
    context: Not a valid file type
    value: "This is not a valid file type"
  AddWorkExperienceByScanningYourResumeOrAddingItManually:
    context: Work experience step description in complete your profile modal
    value: Add work experience by scanning your resume or adding it manually.
  UploadingYourResumeWillPopulateYourSkillsAndWorkExperienceBasedOnTheScan:
    context: Work experience step description in complete your profile modal
    value: Uploading your resume will populate your skills and work experience based on the scan.
  OrAddExperienceManually:
    context: Separator between section upload and section for manually adding experience in complete your profile modal
    value: or add experience manually
  selectFileToUpload:
    context: Description for button select file
    value: Select file to upload
  CloseDialog:
    context: Label for the button used to close modal
    value: Close dialog
  InReview:
    context: Grade Status of a Submitted Project
    value: "In Review"
  ViewResult:
    context: View Result for the Owner of the Poll SmartCard
    value: "View Result"
  NoPermissionsForEditingCard:
    context: Permission for editing the smartcards in pathway
    value: You don't have permission to edit this card
  EditRestrictionForCompletionPollOrQuiz:
    context: Permission Restriction for editing poll or quiz after completion
    value: Can't edit completed poll/quiz
  YouAreNotAuthorizedToAccessThisCard:
    context: Message for representing authorization rights
    value: You are not authorized to access this card
  PrivateToYou:
    context: privacy toggle label used on my learning plan page.
    value: Private to you
  MDPrivateToYouToggleTooltipMsg:
    context: privacy toggle tooltip msg used on my learning plan page.
    value: "Turn on 'Private to you' to hide your completions and assignments from your manager."
  MakeACopySuccessMessage:
    context: Success message after the clicking the make a copy option in meat ball menu
    value: Your job has been submitted successfully and being processed. Please check under content section to see copied card.
  PatentTitleEmptyErrorMessage:
    context: Error message when the patent title is empty
    value: Please provide title
  PatentInventorNameEmptyErrorMessage:
    context: Error message when the patent provider name is empty
    value: Please provide Inventor's name
  Jan:
    context: January month
    value: Jan
  Feb:
    context: February Month
    value: Feb
  Mar:
    context: March Month
    value: Mar
  Apr:
    context: April Month
    value: Apr
  May:
    context: May Month
    value: May
  Jun:
    context: June Month
    value: Jun
  Jul:
    context: July Month
    value: Jul
  Aug:
    context: August Month
    value: Aug
  Sep:
    context: September Month
    value: Sep
  Oct:
    context: October Month
    value: Oct
  Nov:
    context: November Month
    value: Nov
  Dec:
    context: December Month
    value: Dec
  DescriptionIsRequired:
    context: Error message when the description is empty
    value: Description is required
  SeeDetails1:
    context: label for modal to see details
    value: See details
  Requested:
    context: Requested label
    value: Requested
  ViewList:
    context: view the list of approvers
    value: View list
  PlusRemainingMore:
    context: remaining data label
    value: '+{remaining} more'
  By1:
    context: By label
    value: 'By: '
  Date1:
    context: Date
    value: 'Date: '
  HelpChatBot:
    context: Aria label for Help Chat Bot button
    value: 'Help Chat Bot'
  Privacy:
    context: privacy
    value: Privacy
  ReRequestApproval:
    context: label for re-requesting pending approval
    value:  Re-request approval
  ByParticipant:
    context: By participant label
    value: 'By participant: '
  NoContentAvailableTitle:
    context: No content available Title
    value: This {type} has no content yet
  NoContentAvailableDescription:
    context: No content available Description
    value: Edit this {type} to add content and customize its layout.
  AcessForbidden:
    context: 403 access forbidden message
    value: 403 - Access Forbidden
  NoPermissionToAccessPage:
    context: no permission description
    value: You don't have permission to access this page.
  NotificationDisabled:
     context: Notification disabled toast message
     value: Notification disabled
  NotifyMe:
    context: Button text for Notify me
    value: Notify me
  LetTheProjectOwnersKnowWhenYouAreStartingTheProject:
    context: Button description for participate
    value: Let the project owners know when you are starting the project
  ToastMessageOnApprovalRequestApprovedOrRejected:
    context: Toast message for approval request
    value: Applicant {name} has been {status} for the {projectName} project.
  DurationHour:
    context: Time (hour) displayed in 1h 11m format
    value: hour
  DurationHours:
    context: Time (hours) displayed in 11h 11m format
    value: hours
  DurationMinute:
    context: Time (minute) displayed in 11h 1m format
    value: minute
  DurationMinutes:
    context: Time (minutes) displayed in 11h 11m format
    value: minutes
  ClearSelectedDate:
    context: Aria-label for clear button on datepicker start and due date
    value: 'Clear selected {date} Date'
  UploadingYourResumeWillPopulateWorkExperienceBasedOnTheScanAndInferSkills:
    context: Work experience step description in complete your profile modal
    value: Uploading your resume will populate work experience based on the scan and infer skills.
  WeCanScanYourResumeToQuicklyImportYourWorkExperience:
    context: Info message shown in upload resume modal in complete your profile modal
    value: We can scan your Resume to quickly import your work experience.
  AddedDeclaredSkillsCounter:
    context: Counter of added skills in the skills wizard
    value: "Added skills: {selectedSkills}/{skillsLimit}"
  Period:
    context: Label for showing selected date
    value: Period
  YouAreAlmostDoneCreatingYourProfile:
    context: Encouragement text of becoming a mentor
    value: You are almost done creating your profile
  HintTextForBecomingAMentor:
    context: Hint text of becoming a mentor
    value: Details mentioned here will also display on your {tm_tm_mentor} profile
  RemoveBookmark:
    context: Label for unbookmarking learning card
    value: Remove bookmark
  StartedDate2:
    context: Please see StartedDate, it's the same but without colon character at the end
    value: Started Date
  ShowDetails:
    context: Label for button to show more info on learning card
    value: Show details
  HideDetails:
    context: Label for button to hide additional info on learning card (negative of ShowDetails label)
    value: Hide details
  InfoAboutInvisibleSkills:
    context: "It is an information for the user during onboarding about invisible skills on skills step"
    value: "Some skills have been added for you but do not have an assigned level, so they may not be visible on this page"
  Subtitles:
    context: "aria label for subtitles button"
    value: Subtitles
  AllSkillsInputLabel:
    context: "Label for the input that includes skills without level"
    value: "All Skills"
  ClearAllLabel:
    value: Clear all
    context: Button label for the new search UI's single filter dropdown- clicking this will clear all selected options from the dropdown
  Translate:
    context: Translation button label
    value: Translate
  Writing:
    context: Translating button label
    value: Writing
  Translated:
    context: Translated button label
    value: Translated
  RejectMentorshipRequest1 :
    context: title for standard rejection menu modal
    value: Reject {tm_tm_mentorship} request
  RejectMentorshipReasonModalDescription :
    context: Description for mentorship standard rejection menu modal
    value: "This action cannot be undone. Are you sure that you want to reject a request from {name}"
  RejectProjectApplication :
    context: title for standard rejection menu modal
    value: Reject {tm_tm_project} application
  ApplicationRejected :
    context: toast message on rejecting project application
    value: Application rejected.
  SelectARejectionReason :
    context: placeholder for selecting rejection reason
    value: Select a rejection reason
  RejectionReasonSelectTitle :
    context: title for the select component in rejection reason
    value: "Tell the candidate why you're rejecting their application"
  MonthsAgo:
    context: User edited pathway at months ago in the edit pathway previous version tab
    value: months ago
  MonthAgo:
    context: User edited pathway month ago in the edit pathway previous version tab
    value: a month ago
  DaysAgo:
    context: User edited pathway some days ago in the edit pathway previous version tab
    value: days ago
  DayAgo:
    context: User edited pathway day ago in the edit pathway previous version tab
    value: a day ago
  WeeksAgo:
    context: User edited pathway weeks ago in the edit pathway previous version tab
    value: weeks ago
  WeekAgo:
    context: User edited pathway week ago in the edit pathway previous version tab
    value: week ago
  FewSecondsAgo:
    context: User edited pathway few seconds ago in the edit pathway previous version tab
    value: few seconds ago
  HoursAgo:
    context: User edited pathway hours ago in the edit pathway previous version tab
    value: hours ago
  HourAgo:
    context: User edited pathway hour ago in the edit pathway previous version tab
    value: an hour ago
  YearsAgo:
    context: User edited pathway years ago in the edit pathway previous version tab
    value: years ago
  YearAgo:
    context: User edited pathway year ago in the edit pathway previous version tab
    value: year ago
  MinutesAgo:
    context: User edited pathway minutes ago in the edit pathway previous version tab
    value: year ago
  MinuteAgo:
    context: User edited pathway minute ago in the edit pathway previous version tab
    value: minute ago
  StartsOnDate:
    context: Text to show when the mentorship starts followed by a date
    value: Starts on {date}
  StartedOnDate:
    context: Text to show when the mentorship started followed by a date
    value: Started on {date}
  ShowResults:
    context: Label of the show results button
    value: 'Show results'
  NumberOfDaysAgo:
    context: Number of days ago
    value: '{days} days ago'
  OptionIsRequired:
    context: Label for required option
    value: Option is Required
  ContainsXtokens:
    context: amount of items selected text a11y
    value: Contains {numberOf} tokens
  TokenDeletable:
    context: button description a11y
    value: token deletable
  MultiValueComboBox:
    context: button type a11y
    value: Multi Value Combo Box
  SeeMoreComments:
    context: Button to view more comments
    value: See more comments
  MalwareDetected:
    context: This message is shown when virus is detected in media file
    value: 'Malware Detected'
  WorkExperienceGuidanceWithCVParsing:
    context: Work experience step description in complete your profile modal (with CV parsing on)
    value: Add work experience by uploading your resume or adding it manually. To complete this step, add at least one previous job.
  WorkExperienceGuidanceWithoutCVParsing:
    context: Work experience step description in complete your profile modal (with CV parsing off)
    value: To complete this step, add at least one previous job.
  ScanResumeHint:
    context: Hint visible while uploading CV
    value: We can scan your resume to quickly import your work experience and infer skills for your review
  ValueMoreInfo:
    context: 'text to describe the button, e.g add button more info'
    value: '{value} more info'
  TooltipPrefix:
    context: 'tooltip message prefix a11y'
    value: 'Tooltip :'
  MultipleGraders:
    context: "Showing Grader field value for mixed graders on Project SmartCard Standalone View"
    value: "Multiple Graders"
  ViewMessageName:
    context: View message name
    value: "View {name}'s message"
  SeeDetailsOfName:
    context: See details of name
    value: "See details of {name}"
  SecondaryManagers:
    context: Profile, Organization tree, Secondary managers label
    value: Secondary managers
  YouDoNotHaveAccessToViewThisPage:
    context: Profile, Organization tree, Secondary managers label
    value: You do not have access to view this page
  WeAreSorryButThereIsCurrentlyNoOpportunitiesAvailable:
    context: No opportunities available message
    value: We are sorry, but there are currently no {opportunities} available
  WeAreSorryButThereIsCurrentlyNoProjectsAvailable:
    context: No projects available message
    value: We are sorry, but there are currently no {tm_tm_projects} available
  Training:
    context: Training
    value: Training
  NoSkillsGap:
    context: A statement explaining the lack of skills
    value: No skills gap
  MatchesYourAspirationalRoleConfigurable:
    context: missing
    value: Matches your {tm_aspirational_role}
  MarkAsAspirationalConfigurable:
    context: missing
    value: Mark as {tm_aspirational_role}
  RemoveAsAspirationalConfigurable:
    context: missing
    value: Remove as {tm_aspirational_role}
  OpportunityMarkedAsAspirationalConfigurable:
    context: "Mark as aspirational action on opportunity, opportunity value: tm_job_role"
    value: "{opportunity} marked as {tm_aspirational_role}"
  OpportunityUnmarkedAsAspirationalConfigurable:
    context: "Unmark as aspirational action on opportunity, opportunity value: tm_job_role"
    value: "{opportunity} unmarked as {tm_aspirational_role}"
  MarkOpportunityAsAspirationalConfigurable:
    context: Action on role card to mark as aspirational
    value: Mark as {tm_aspirational_role}
  UnMarkOpportunityAsAspirationalConfigurable:
    context: Action on role card to unmark as aspirational
    value: Remove as {tm_aspirational_role}
  RemoveAspirationalRoleConfirmationConfigurable:
    context: modal title for removing aspirational role which contains Action Plan
    value: Are you sure you want to remove this {tm_aspirational_role}?
  RemoveAspirationalRoleDescriptionConfigurable:
    context: warning message for removing aspirational role which contains Action Plan
    value: If you remove your {tm_aspirational_role}, your action plan will be permanently lost
  MarkedAsAspirationalLimitWarningConfigurable:
    context: "Warning displayed when a user reaches the limit of aspirated roles"
    value: "You have reached the maximum number ({max}) of {tm_aspirational_roles}."
  OpportunityMarkedAsAspirationalConfirmationConfigurable:
    context: "Confirmation of marking opportunity as aspirational"
    value: Great, you have marked {title} as your {tm_aspirational_role}
  OpportunityShowedInterestConfirmationConfigurable:
    context: "Confirmation of showing interest on opportunity"
    value: Thanks for your interest in this {tm_aspirational_role}!
  OpportunityMatchesYourAspirationalConfigurable:
    context: "Indication on card that opportunity matches user aspirational"
    value: Matches your {tm_aspirational_role}
  YourAspirationalRoleConfigurable:
    context: Indicate if current role is your aspirational
    value: Your {tm_aspirational_role}
  PreviousPage:
    context: Previous page button
    value: Previous Page
  NextPage:
    context: Next page button
    value: Next Page
  ZoomOut:
    context: Zoom out button
    value: Zoom Out
  ZoomIn:
    context: Zoom in button
    value: Zoom In
  FullScreen:
    context: Full screen button
    value: Full Screen
  Of:
    context: "text to describe the page number, e.g 1 of 5"
    value: of
  LoadingMessage:
    context: Label for loading indicator
    value: Loading
  StepXOfTotalSteps:
    context: Current step of total steps
    value: 'Step {num} of {totalSteps}'
  noDataAvailable:
    value: No data available
    context: placeholder text for no data available
  ThisYear:
    value: This Year
    context: Filter option for this year
  ActionsFor:
    value: "Actions for {value}"
    context: menu aria label description
  LevelWasDisabled:
    value: 'This level was disabled by your admin. Remove or change the level of any assigned skills to continue.'
    context: Error message is displayed when skill assigned to disabled level
  XMoreFiltersApplied:
    context: Text for button X more filters available
    value: "{count} more applied filters"
  LevelNameWasDisabled:
    value: 'The {levelName} level was disabled by your admin. You can still reassign existing skills to a different level.'
    context: Error message is displayed when a selected level is disabled
  FieldsMarkedWithAsteriskAreRequired:
    context: form validation message
    value: "Fields marked with ({asterisk}) are required"
