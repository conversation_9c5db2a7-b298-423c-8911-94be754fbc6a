prefix: skills-assessments-v2.skills-assessments-v2
labels:
  Achievements:
    value: Achievements
    context: Self Achievements
  Actions:
    value: Actions
    context: header of the Actions
  AddMyLearningSucceeded:
    value: Saved courses to my learning plan
    context: toast message when adding my learning plan successfully
  AdvancedLevel:
    value: advanced level
    context: description of skill level
  Assess:
    value: Assess
    context: Peer Assess action
  AssessmentRequests:
    value: Assessment requests
    context: peers assessment page header
  AssessmentRequestsSent:
    value: Assessment requests sent
    context: toast message when assessment requests sent
  AssessSkills:
    value: Assess skills
    context: Assess skills modal header text
  RateSkills:
    value: Rate skills
    context: Rate skills modal header text
  RateSkillsFor:
    value: Rate skills for
    context: Rate skills modal header text
  BeginnerLevel:
    value: beginner level
    context: description of skill level
  Cancel:
    value: No, cancel
    context: don't take any action
  ClickToOpenViewLearningPlanModal:
    value: Click to open view learning plan modal
    context: description of Click button
  ClickToSortSkillNames:
    value: click to sort skill names
    context: description of click sort button
  ClickToSortHeaderActions:
    value: click to sort actions
    context: description of click sort button
  ClickToSortHeaderExpectedLevel:
    value: click to sort expected level
    context: description of click sort button
  ClickToSortHeaderSelfRatedLevel:
    value: click to sort self rated level
    context: description of click sort button
  ClickToSortHeaderRater:
    value: click to sort rater
    context: description of click sort button
  ClickToSortHeaderDateReceived:
    value: click to sort date received
    context: description of click sort button
  ClickToSortHeaderRequestedBy:
    value: click to sort requested by
    context: description of click sort button
  ClickToSortHeaderDueDate:
    value: click to sort due date
    context: description of click sort button
  ClickToSortHeaderStatus:
    value: click to sort status
    context: description of click sort button
  ClickToSortHeaderRequestedTo:
    value: click to sort requested to
    context: description of click sort button
  ClickToSortHeaderSentOn:
    value: click to sort sent on
    context: description of click sort button
  CreateLearningPlan:
    value: Create a learning plan to reach the expected skill level. Recommended learning for
    context: description of creating a learning plan
  DateReceived:
    value: Date received
    context: peers assessments request header
  DueDate:
    value: Due date
    context: peers assessments request header
  ExpectedLevel:
    value: Expected level
    context: header of the Expected level
  ExpectedLevelsAchievedForRoleSpecificSkills:
    value: Expected levels achieved for role-specific skills
    context: description of achieved level
  GoToProfile:
    value: Go to Profile
    context: description of click button
  GoToSkillsPassport:
    value: Go to Skills Passport
    context: description of click button
  HowToProceed:
    value: Please be aware that this action cannot be undone. How would you like to proceed?
    context: description of action can not be undone
  IntermediateLevel:
    value: intermediate level
    context: description of skill level
  JobFamily:
    value: Job family
    context: user profile detail
  JobRole:
    value: Job role
    context: user profile detail
  Level:
    value: level
    context: description of skill level
  MySkills:
    value: My skills
    context: header of self assessment page
  More:
    value: more
    context: label of more button
  ManagerAssessed:
    value: Manager assessment
    context: label of manager assessment rating
  No:
    value: No, cancel confirmation
    context: label of cancel button
  NoCourses:
    value: Sorry, currently we do not have any recommendations for
    context: description of empty courses for a given skill
  NoData:
    value: No Assessment yet
    context: description of no assessment ready
  NoPeerAssessmentRequests:
    value: "You don't have any requests yet. If anyone sends a request it will appear here"
    context: description of no requests
  NoSelfSkills:
    value: "You don't have any skills to assess. Start by adding skills in your skills passport"
    context: description of no skills
  NoSkillsToAssessGoToProfile:
    value: "You don't have any skills to assess. Start by adding your Learning Goals"
    context: description of no skills to assess
  NoSkillsToAssess:
    value: "You don't have any skills to assess."
    context: description of no skills to assess
  OpenSkillsDropdown:
    value: open skills dropdown
    context: click button to open the skill names drop down
  OpenRequestPeersAssessmentModal:
    value: Open request peers assessment modal
    context: Open request peers assessment modal
  PeersAssessed:
    value: Avg. peer assessment
    context: label of avarage peers assessment level
  PeerAssessStatuscompleted:
    value: Completed
    context: peer assess status
  PeerAssessStatusinvited:
    value: Invited
    context: peer assess status
  PeerAssessStatusrejected:
    value: Rejected
    context: peer assess status
  PeerAssessStatusdeclined:
    value: Declined
    context: peer assess status
  PeerAssessStatusexpired:
    value: Expired
    context: peer assess status
  RecommendedLearningPlan:
    value: Recommended learning plan
    context: button description
  Select:
    value: select
    context: this is option for selecting all skills assessments
  Self:
    value: Self
    context: selb tab label
  SelfAssess:
    value: Self Assess
    context: button label
  SelfAssessedLevel:
    value: Self assessed level
    context: header of the Self assessed level
  SelfRatedLevel:
    value: Self rated level
    context: header of the Self rated level
  SelfLearningDescription:
    value: Declare your skill level. Use the learning plan to bridge any learning gaps.
    context: description of how to declare your skill level
  UseTheLearningPlanToBridgeAnyLearningGaps:
    value: Use the learning plan to bridge any learning gaps.
    context: description of how to declare your skill level
  SendAssessmentRequest:
    value: Send assessment request
    context: header of send assessment request modal
  SendRatingRequest:
    value: Send rating request
    context: header of send rating request modal
  SendRequest:
    value: Send request
    context: decription of click button
  Skill:
    value: Skill
    context: header of skill
  SkillsAssessment:
    value: Skills Assessment
    context: Skills Assessment
  SkillsAssessmentSubmitted:
    value: Skills assessment submitted
    context: Skills assessment submitted
  SearchIndividuals:
    value: Search individuals...
    context: search box init lable
  SelectedSkillsForAssessment:
    value: Selected skills for assessment
    context: Selected skills for assessment
  SelectedSkillsForRating:
    value: Selected skills for rating
    context: Description for Selected skills for rating
  Status:
    value: Status
    context: Status
  SubmitAssessment:
    value: Submit assessment?
    context: Submit assessment?
  SubmitAassessmentFor:
    value: You are about to submit skills assessment for
    context: description of submit
  Peers:
    value: Peers
    context: Peers
  OpenSelfAssessmentModal:
    value: Open self assessment modal
    context: Open self assessment modal action
  RequestAssessment:
    value: Request Assessment
    context: Description of click button to request peers assessment
  RequestedBy:
    value: Requested by
    context: description of who requested the assessment
  ToggleToAllSkills:
    value: 'toggle to {variable} all skills'
    context: toggle to select or unselect all skill names in self assessment
  UnSelect:
    value: unselect
    context: this is option for unselecting all skills assessments
  ViewLearningPlan:
    value: View learning plan
    context: description of click button
  Yes:
    value: yes, please submit the update ...
    context: confirm submit
  YesSubmit:
    value: Yes, submit
    context: confirmation of submitting
  YourAssessment:
    value: Your assessment
    context: header of your assessment
  YourLevel:
    value: Your level
    context: header of your skill level
  InvitationsSent:
    value: Invitations Sent
    context: table title label for skills assessment peers invitations table
  NoPeerInvitationsSent:
    value: "You don't send any invitations yet. If you sent an invitation it will appear here"
    context: description of no peer invitations sent for skills assessment peer tab
  ThereAreNoResultsToDisplay:
    value: There are no results to display
    context: description of no ratings
  youCanOnlyViewRatingsFromYourManager:
    value: Due to system limitations, you can only view ratings from your manager
    context: tooltip description
  Completed:
    value: Completed
    context: peer invitation status label for skills assessment peers invitations table
  NotStarted:
    value: Not Started
    context: peer invitation status label for skills assessment peers invitations table
  Peer:
    value: Peer
    context: peer invitation header label for skills assessment peers invitations table
  InvitedOn:
    value: Invited On
    context: peer invitation header label for skills assessment peers invitations table
  RequestSuccessfullyCanceled:
    value: Request successfully canceled
    context: toast message when request successfully canceled
  RequestSuccessfullyRejected:
    value: Request successfully rejected
    context: toast message when request successfully rejected
  ReAssessmentRequestSuccessfullySent:
    value: Re-assessment request successfully sent
    context: toast message when Re-assessment request successfully sent
  RequestedTo:
    value: Requested to
    context: peer invitation header label for skills assessment peers invitations table
  SentOn:
    value: Sent on
    context: peer invitation header label for skills assessment peers invitations table
  SendReminder:
    value: Send reminder
    context: description of button click
  RequestReAssessment:
    value: Request re-assessment
    context: description of button click
  AssessmentRequestSentFor:
    value: Assessment request sent for
    context: description of sending peer request
  RatingRequestedFor:
    value: Rating requested for
    context: description of sending peer request
  skills:
    value: skills
    context: description of sending peer request
  ShowSkills:
    value: Show skills
    context: tooltip message
  HideSkills:
    value: Hide skills
    context: tooltip message
  ViewAssessment:
    value: View assessment
    context: Peer Assess action
  CancelRequest:
    value: Cancel request
    context: header of the Cancel modal
  RejectRequest:
    value: Reject request
    context: header of the Cancel modal
  DoYouWantToCancelYourSkillsAssessmentRequestSentTo:
    value: Do you want to cancel your skills assessment request sent to
    context: confirmation of cancel
  DoYouWantToRejectTheSkillsAssessmentRequestedBy:
    value: Do you want to reject the skills assessment requested by
    context: confirmation of reject
  WriteAReminderNote:
    value: Write a reminder note
    context: placeholder description
  SkillLevelsWillBeUpdated:
    value: Skill levels will be updated based on new scores upon re-assessment completion.
    context: description of request re-assessment
  RequestsReceived:
    value: Requests received
    context: peers filter option
  RequestsSentd:
    value: Requests sent
    context: peers filter option
  RoleSpecific:
    value: Role-specific
    context: self filters option
  SelfDeclared:
    value: Self-declared
    context: self filters option
  LearningGoals:
    value: Learning Goals
    context: self filters option
  FilterByAssessmentRequests:
    value: Filter by assessment requests
    context: filter by assessment request
  SendReminderOnlyOnceIn24Hours:
    value: You can send a reminder only once in 24 hours
    context: reminder tooltip description
  LearningPlanHasBeenDisabled:
    value: Learning Plan has been disabled
    context: learning plan link disabled tooltip description
  UnspecifiedLevel:
    value: Unspecified level
    context: skill assessment unspecified level tooltip description
  NoRatingGiven:
    value: No rating given
    context: skill assessment no rating given tooltip description
  NoAssessment:
    value: No Assessment
    context: skill assessment no rating given
  InputText:
    value: Input text
    context: placeholder description
  ViewRating:
    value: View Rating
    context: button label
  ViewRating1:
    value: View rating
    context: button label
  Decline:
    value: Decline
    context: button label
  Rate:
    value: Rate
    context: button label
  Overview:
    value: Overview
    context: tab label
  Requests:
    value: Requests
    context: tab label
  Ratings:
    value: Ratings
    context: tab label
  RateYourSkills:
    value: Rate your skills
    context: button label
  RequestRating:
    value: Request rating
    context: button label
  Received:
    value: Received
    context: button label
  Sent:
    value: Sent
    context: button label
  RequestReRating:
    value: Request re-rating
    context: description of button click
  Rater:
    value: Rater
    context: header label
  RatingDetails:
    value: Rating details
    context: modal title
  FeedbackFrom:
    value: Feedback from
    context: modal description
  YourFeedbackFor:
    value: Your feedback for
    context: modal description
  FeedbackAbout:
    value: Feedback about
    context: modal description
  ShowActiveRequests:
    value: Show active requests
    context: description of toggle
  HideActiveRequests:
    value: Hide active requests
    context: description of toggle
  EditJobFamilyAndRole:
    value: Edit Job Family And role
    context: aria label of a button
  SelectedLevelIsDisabled:
    value: 'The {skillLevelName} level was disabled by your admin. You can still reassign existing skills to a different level.'
    context: Error message is displayed when a selected level is disabled
  AssignmentAdded:
    value: Assignment added to learning plan
    context: description of assignment added to learning plan toast message