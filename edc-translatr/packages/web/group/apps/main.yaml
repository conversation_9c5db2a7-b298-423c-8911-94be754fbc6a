app: group.main
labelKeys:
  - multilang.multilang:DefineTranslationGroup
  - talentmarketplace.talentmarketplace:UploadingThreeDots
  - fspregistration.fspregistration:Upload
  - group.group:User
  - group.group:JobTitle
  - group.group:BusinessUnit
  - group.group:Role
  - group.group:Joined
  - group.group:GroupMembers
  - group.group:InviteTeamMembers
  - group.group:InvitationRole
  - group.group:ViewAndInviteTeamMembersOrRemoveTeamMembersFromTheGroup
  - group.group:AvailableRoles
  - group.group:GroupMemberViewAndConsumeContentAvailableWithinTheGroup
  - group.group:GroupAdminManageGroupContentGroupUsersAndGroupAnalytics
  - group.group:ViewLess
  - group.group:ViewMore
  - group.group:GroupLeader
  - group.group:GroupMember
  - group.group:GroupModerator
  - group.group:GroupAdmin
  - group.group:SearchForGroupMembers
  - group.group:NoUsersFound
  - group.group:HowWouldYouLikeToProceed
  - group.group:Close
  - group.group:Removing
  - group.group:Remove
  - group.group:TotalMembersTotalmembers
  - group.group:SearchForPeople
  - group.group:Cancel
  - group.group:Invite
  - group.group:Inviting
  - group.group:Name
  - group.group:DownloadReport
  - group.group:DownloadCsvFile
  - group.group:UploadedDate
  - group.group:Status
  - group.group:BulkUploadHistory
  - group.group:NoFilesUploadedYet
  - group.group:500UsersLimit
  - group.group:DownloadSampleFile
  - group.group:DragDropYourCsvFileHere
  - group.group:Or
  - group.group:Browse
  - group.group:UnfollowChannel
  - group.group:Confirm
  - group.group:GroupImage
  - group.group:ShownInYourGroupsCard
  - group.group:GroupBannerImage
  - group.group:UploadGroupBannerImage
  - group.group:ShownInYourGroupsHeader
  - group.group:Configurations
  - group.group:GroupSettings
  - group.group:Optional
  - group.group:OpenGroup
  - group.group:UsersWillBeAbleToFindAndJoinThisGroup
  - group.group:MandatoryGroup
  - group.group:SharingAndAssignment
  - group.group:EnableContentSharing
  - group.group:MembersCanShareContentToThisGroup
  - group.group:AutoAssignContentToNewGroupMembers
  - group.group:ContentLayout
  - group.group:PleaseBeAwareThatAnyChangesMadeHereWillBeAutomaticallySaved
  - group.group:Sections
  - group.group:GroupChannelUpdatedSuccessfully
  - group.group:Channels
  - group.group:Details
  - group.group:AddChannel
  - group.group:SubscribeunsubscribeNotification
  - group.group:NoChannelFound
  - group.group:TotalChannels
  - group.group:UpdateGroup
  - group.group:Updating
  - group.group:UploadDate
  - group.group:NewGroup
  - group.group:DetailsConfiguration
  - group.group:IndicatesRequired
  - group.group:GroupName
  - group.group:Groupname
  - group.group:About
  - group.group:ADescriptionExplainingUsersWhatThisGroupIsAbout
  - group.group:AddGroupLeader
  - group.group:AddGroupAdmin
  - group.group:AddGroupModerator
  - group.group:AddGroupMember
  - group.group:Creating
  - group.group:CreateGroup
  - group.group:Topics
  - group.group:More
  - group.group:Channel
  - group.group:SearchByAcceptedCriteriaToFindContentHere
  - group.group:Add
  - group.group:CardHasBeenSuccessfullyRemovedFromFeaturedContentSection
  - group.group:CardHasBeenSuccessfullyAddedToFeaturedContentSection
  - group.group:YouCanNotMarkedMoreThan10FeaturedCardsInAGroup
  - group.group:LeaveGroup
  - group.group:JoinGroup
  - group.group:Analytics
  - group.group:Leaderboard
  - group.group:InviteAdpEmployees
  - group.group:InvitePeople
  - group.group:Decline
  - group.group:Accept
  - group.group:GroupLeaders
  - group.group:GroupAdmins
  - group.group:GroupModerators
  - group.group:YouWontBeAbleToAccessThisGroupDoYouReallyWantToLeave
  - group.group:DeclineTitle
  - group.group:JoinGroupTitle
  - group.group:ButtonlabelTitle
  - group.group:ViewAll
  - group.group:EditGroup
  - group.group:ManageGroup
  - group.group:PendingMembers
  - group.group:Insights
  - group.group:DeleteGroup
  - group.group:DeleteGroupToastMessage
  - group.group:WhatDoYouWishToShareWithThisGroup
  - group.group:Smartcard
  - group.group:Pathway
  - group.group:Journey
  - group.group:OpenLeaderboard
  - group.group:You
  - group.group:Points
  - group.group:ThisIsAChannelImage
  - group.group:RemoveFromGroup
  - group.group:FeaturedContent
  - group.group:ThereAreNoFeaturedContentInThisGroup
  - group.group:DeclineGroupInvitation
  - group.group:YouAreAboutToDeclineTheInvitationFor
  - group.group:ThisActionCanNotBeUndoneHowDoYouWishToProceed
  - group.group:Groups
  - group.group:Type
  - group.group:Group
  - group.group:ThisIsAGroupImage
  - group.group:YouDoNotHaveAnyGroup
  - group.group:AllFilters
  - group.group:Apply
  - group.group:Save
  - group.group:SearchGroups
  - group.group:MyGroups
  - group.group:SelectRoleOfUpdatedMember
  - group.group:DeleteUpdatedMember
  - group.group:GroupsIAmAnAdminOf
  - group.group:GroupsIAmALeaderOf
  - group.group:GroupsIAmAMemberOf
  - group.group:CommunitiesIAmAModeratorOf
  - group.group:MyPendingRequests
  - group.group:OtherPublicGroups
  - group.group:Private
  - group.group:Public
  - group.group:All
  - group.group:SelectedusersOutOfTotalusersIndividualsSelected
  - group.group:ShowingMemberscountOutOfTotalmembers
  - group.group:YouAreAboutToRemoveUsernameFromGroupnameGroup
  - group.group:YouHaveGrouplengthNewGroupInvitation
  - group.group:GroupMembersWillNoLongerBeAbleToSeeThisCardInTheGroup
  - group.group:ActivityFeed
  - group.group:AllContentFilters
  - group.group:GroupLeaderDescription
  - group.group:GroupInviteDescription
  - group.group:UserUploadViaCSV
  - group.group:CSVinProcess
  - group.group:CSVneedsValidation
  - group.group:FileProcessing
  - group.group:ChannelRemoval
  - group.group:AutomaticPastAssignment
  - group.group:AddingMemberWarning
  - group.group:AssociateChannelsMsg
  - group.group:ChannelRemovedSuccessfully
  - group.group:RemoveGroupConfirmation
  - group.group:RemoveCommunityConfirmation
  - group.group:SearchGroupLeader
  - group.group:GroupLeaders
  - group.group:GroupLeadersUserTypes
  - group.group:GroupsWithApostrophe
  - group.group:SearchGroupAdmin
  - group.group:SearchGroupModerator
  - group.group:GroupAdminsUserType
  - group.group:SearchGroupMember
  - group.group:GroupMembersUserType
  - group.group:GroupModeratorUserType
  - group.group:GroupFeaturedTabNoContentMessage
  - channel.channel:YouDoNotHaveAccessToContentInThisSection
  - group.group:GrpContentInaccessibleRefineQueryOrShowMore
  - group.group:GrpContentInaccessibleRefineQuery
  - group.group:featuredPosts
  - group.group:groupAssignments
  - group.group:associatedChannels
  - group.group:BulkRemovalHistory
  - group.group:BulkRemoval
  - group.group:YouAreAboutToInitiateABulkRemovalOfUsers
  - group.group:AreYouSureYouWantToProceedWithTheBulkRemoval
  - group.group:RemoveGroupMember
  - group.group:RemoveGroupMemberContent
  - group.group:SelectedUsersToRemove
  - group.group:GroupSectionDeletedOrDisabled
  - group.group:SelectToRemoveUser
  - group.group:RemoveSectionFromGroupConsentMessage
  - group.group:UploadCsv
  - group.group:Home
  - group.group:MyAssignments
  - group.group:NoGroupContentAvailableDescriptionLabel
  - group.group:UserInvitedSuccessfully
  - group.group:SearchOrRemoveUserTotalGroupLeadersLabel
  - group.group:SearchOrRemoveUserTotalGroupAdminsLabel
  - group.group:SearchOrRemoveUserTotalGroupMembersLabel
  - group.group:SearchOrRemoveUserTotalGroupModeratorLabel
  - group.group:NoContentAvailableDefaultDescriptionGroup
  - group.group:NoContentAvailableEditButtonLabelGroup
  - group.group:NoContentAvailableMessageLabelGroup
  - group.group:GroupCreatedSuccessfully
  - group.group:SomeUsersWereAlreadyInvited
  - group.group:AtleastOneAcceptedGroupUserIsRequired
  - group.group:Settings
  - group.group:Members
  - group.group:BulkUpload
  - group.group:Individuals
  - group.group:BulkRemoveGroupMembers
  - group.group:RemoveUser
  - group.group:GroupUpdatedSuccessfully
  - group.group:PublicGroup
  - group.group:NoContentGroupLabel
  - group.group:ContentManagementSectionHeaderContent
  - group.group:ContentManagementSectionHeaderChannel
  - group.group:GroupFilters
  - group.group:ShowAutomaticGroups
  - group.group:ShowAutomaticGroupsTooltip
  - group.group:StartAPost
  - group.group:EnableRestrictTo
  - group.group:MembersCanRestrict
  - group.group:CreatePost
  - group.group:PostRemovedSuccessfully
  - group.group:GbacInfoMessage
  - group.group:Engagement
  - group.group:PostFeed
  - group.group:NoCommunity
  - group.group:TopicBasedCommunity
  - group.group:LearningCommunity
  - group.group:TopicBasedCommunityDescription
  - group.group:LearningCommunityDescription
  - group.group:Community
  - group.group:CommunityDescription
  - group.group:AllPostsNavigation
  - group.group:WaitingForApprovalNavigation
  - group.group:ArchivedNavigation
  - group.group:ApprovedNavigation
  - group.group:PostColumnName
  - group.group:AuthorColumnName
  - group.group:StatusColumnName
  - group.group:CreatedColumnName
  - group.group:ViewsColumnName
  - group.group:LikesColumnName
  - group.group:RepliesColumnName
  - group.group:ActionsColumnName
  - group.group:ApprovePostsAction
  - group.group:RejectPostsAction
  - group.group:EditPostsAction
  - group.group:DeletePostsAction
  - group.group:ButtonAddPostLabel
  - group.group:ReportedNavigation
  - group.group:TrashedNavigation
  - group.group:NameColumnName
  - group.group:TopicColumnName
  - group.group:TypeColumnName
  - group.group:ReasonColumnName
  - group.group:ReportersColumnName
  - group.group:DismissReportedContentAction
  - group.group:DeleteReportedContentAction
  - group.group:CommunityPostReportedContentDismissed
  - group.group:CommentReportedContentDismissed
  - group.group:CommunityPostReportedContentTrashed
  - group.group:CommentReportedContentTrashed
  - group.group:TopicFormFieldName
  - group.group:TopicFormFieldNamePlaceholder
  - group.group:TopicFormFieldDescription
  - group.group:TopicFormFieldDescriptionPlaceholder
  - group.group:TopicFormNameRequiredMessage
  - group.group:TopicModalCreateTitle
  - group.group:TopicModalEditTitle
  - group.group:TopicFormButtonSavingInProgress
  - group.group:TopicFormButtonSave
  - group.group:TopicFormSavingSuccessfullyNotification
  - group.group:TopicFormUpdatingSuccessfullyNotification
  - group.group:TopicFormSavingFailureNotification
  - group.group:AllTopicsNavigation
  - group.group:LastUpdateColumnName
  - group.group:PostsColumnName
  - group.group:EditTopicAction
  - group.group:DeleteTopicAction
  - group.group:ButtonAddTopicLabel
  - group.group:ManagePostPageTitle
  - group.group:TopicsTabLabel
  - group.group:PostsTabLabel
  - group.group:ReportedContentTabLabel
  - group.group:SelectTopicsDefaultItem
  - group.group:ManagePostsTableErrorEmptyStateTitle
  - group.group:ManagePostsTableErrorEmptyStateDescription
  - group.group:ManagePostsTableErrorEmptyStateButtonLabel
  - group.group:ManagePostsTableNoResultEmptyStateTitle
  - group.group:ManagePostsTableNoResultEmptyStateDescription
  - group.group:SeeMoreReplies
  - group.group:CollapseReplies
  - group.group:LastReply
  - group.group:Reply
  - group.group:ReplyTo
  - group.group:Replies
  - group.group:OneReply
  - group.group:NewestFirstSortLabel
  - group.group:LatestFirstSortLabel
  - group.group:DeleteTopicConfirmationModalTitle
  - group.group:DeleteTopicConfirmationModalDescription
  - group.group:PleaseSelectAtLeastOneTopic
  - group.group:ManageTopics
  - group.group:TopicDeletedErroredMessage
  - group.group:TopicDeletedSuccessMessage
  - group.group:LastActiveTopicErrorMessage
  - group.group:PostCreateChangePostTypeConfirmationTitle
  - group.group:PostCreateChangePostTypeConfirmationMessage
  - group.group:AddMention
  - group.group:AddMedia
  - group.group:AddPoll
  - group.group:AddQuiz
  - group.group:AddFile
  - group.group:QuizQuestion
  - group.group:PollQuestion
  - group.group:PleaseAddHereYourQuestion
  - group.group:EnterQuestion
  - group.group:QuizOptions
  - group.group:PollOptions
  - group.group:QuizOption
  - group.group:PollOption
  - group.group:NameIsRequired
  - group.group:OptionIsRequired
  - group.group:AddQuestion
  - group.group:InvalidPostMessage
  - group.group:TopicIsRequired
  - group.group:TopicContainsPostsErrorMessage
  - group.group:SelectStatusLabel
  - group.group:SelectTopicsLabel
  - group.group:Widget
  - group.group:BannerWidget
  - group.group:BannerWidgetHint
  - group.group:BannerWidgetContent
  - group.group:BannerWidgetPlaceholder
  - group.group:BannerWidgetContentDescription
  - group.group:SearchWithEllipsis
  - group.group:DeletePostConfirmationModalTitle
  - group.group:DeletePostConfirmationModalDescription
  - group.group:NoPostAdded
  - group.group:CreateFirstPostInGroup
  - group.group:CreateFirstPostInCommunity
  - group.group:GroupWidgetTitle
  - group.group:GroupWidgetDescription
  - group.group:AddWidget
  - group.group:PostWaitingForApprovalTitle
  - group.group:PostSubmittedForApprovalInCommunity
  - group.group:PostSubmittedForApprovalInGroup
  - group.group:Topic
  - group.group:EditPost
  - group.group:ContentApproval
  - group.group:ContentApprovalDesc
  - group.group:PostsCountAriaLabel
  - group.group:AddLearningMaterials
  - group.group:MaximumLearningMaterials
  - group.group:FileLabel
  - group.group:LinkLabel
  - group.group:MaterialTitle
  - group.group:MaterialDescription
  - group.group:SelectTypeLabel
  - group.group:EnterLinkLabel
  - group.group:MaterialModalCreateTitle
  - group.group:MaterialModalEditTitle
  - group.group:MaterialEditLabel
  - group.group:MaterialSavingSuccessfullyNotification
  - group.group:MaterialUpdatingSuccessfullyNotification
  - group.group:MaterialDeletingSuccessfullyNotification
  - group.group:PleaseCreateAMaterial
  - group.group:MaterialFormTitleRequiredMessage
  - group.group:MaterialFormLinkRequiredMessage
  - group.group:MaterialFormFileRequiredMessage
