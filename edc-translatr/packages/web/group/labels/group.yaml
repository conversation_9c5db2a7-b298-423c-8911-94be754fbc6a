prefix: group.group
labels:
  User:
    value: User
    context: missing
  JobTitle:
    value: Job Title
    context: missing
  BusinessUnit:
    value: Business Unit
    context: missing
  Role:
    value: Role
    context: missing
  Joined:
    value: Joined
    context: missing
  GroupMembers:
    value: Group member(s)
    context: missing
  InviteTeamMembers:
    value: Invite Team Members
    context: missing
  InvitationRole:
    value: Invitation Role
    context: missing
  ViewAndInviteTeamMembersOrRemoveTeamMembersFromTheGroup:
    value: View and invite team members or remove team members from the group
    context: missing
  AvailableRoles:
    value: 'Available Roles:'
    context: missing
  GroupMemberViewAndConsumeContentAvailableWithinTheGroup:
    value: 'View and consume content available within the group'
    context: missing
  GroupAdminManageGroupContentGroupUsersAndGroupAnalytics:
    value: 'Manage Group Content, Group Users, and Group Analytics'
    context: missing
  ViewLess:
    value: View Less
    context: missing
  ViewMore:
    value: View More
    context: missing
  GroupLeader:
    value: Group Leader
    context: missing
  GroupMember:
    value: Group Member
    context: missing
  GroupModerator:
    value: Group Moderator
    context: Label for user role
  GroupAdmin:
    value: Group Admin
    context: missing
  SearchForGroupMembers:
    value: Search for Group Members
    context: missing
  NoUsersFound:
    value: No Users Found
    context: missing
  HowWouldYouLikeToProceed:
    value: How would you like to proceed?
    context: missing
  Close:
    value: Close
    context: missing
  Removing:
    value: Removing...
    context: missing
  Remove:
    value: Remove
    context: missing
  TotalMembersTotalmembers:
    value: 'Total Members: {totalMembers}'
    context: missing
  SearchForPeople:
    value: Search for People
    context: missing
  Cancel:
    value: Cancel
    context: missing
  Invite:
    value: Invite
    context: missing
  Inviting:
    value: Inviting
    context: missing
  Name:
    value: Name
    context: missing
  DownloadReport:
    value: Download Report
    context: missing
  DownloadCsvFile:
    value: Download CSV file
    context: missing
  UploadedDate:
    value: Uploaded Date
    context: missing
  Status:
    value: Status
    context: missing
  BulkUploadHistory:
    value: Bulk Upload History
    context: missing
  NoFilesUploadedYet:
    value: No Files Uploaded yet.
    context: missing
  500UsersLimit:
    value: 500 users limit
    context: missing
  DownloadSampleFile:
    value: Download Sample file
    context: missing
  DragDropYourCsvFileHere:
    value: Drag & Drop your CSV File here
    context: missing
  Or:
    value: or
    context: missing
  Browse:
    value: Browse...
    context: missing
  UnfollowChannel:
    value: Unfollow Channel
    context: missing
  Confirm:
    value: Confirm
    context: missing
  GroupImage:
    value: Group Image
    context: missing
  ShownInYourGroupsCard:
    value: Shown in your group's card (jpg, jpeg, png, webp up to {size})
    context: Group image label subtext
  GroupBannerImage:
    value: Group Banner Image
    context: missing
  UploadGroupBannerImage:
    value: Upload Group Banner Image
    context: aria label for upload group banner image button
  ShownInYourGroupsHeader:
    value: Shown in your group's header (jpg, jpeg, png, webp up to {size})
    context: Group banner image label subtext
  SelectRoleOfUpdatedMember:
    value: "Select role of {updatedMemberName}"
    context: Select role for member in manage group page
  DeleteUpdatedMember:
    value: "Delete {updatedMemberName}"
    context: Delete member in manage group page
  Configurations:
    value: Configurations
    context: missing
  GroupSettings:
    value: Group Settings
    context: missing
  Optional:
    value: Optional
    context: missing
  OpenGroup:
    value: Open Group
    context: missing
  UsersWillBeAbleToFindAndJoinThisGroup:
    value: Users will be able to find and join this group.
    context: missing
  MandatoryGroup:
    value: Mandatory Group
    context: missing
  SharingAndAssignment:
    value: Sharing and Assignment
    context: missing
  EnableContentSharing:
    value: Enable Content Sharing
    context: missing
  MembersCanShareContentToThisGroup:
    value: Members can share content to this group
    context: missing
  AutoAssignContentToNewGroupMembers:
    value: Auto Assign Content to New Group Members
    context: missing
  ContentLayout:
    value: Content Layout
    context: missing
  PleaseBeAwareThatAnyChangesMadeHereWillBeAutomaticallySaved:
    value: Please be aware that any changes made here will be automatically saved.
    context: missing
  Sections:
    value: Sections
    context: missing
  GroupChannelUpdatedSuccessfully:
    value: Group Channel Updated Successfully
    context: missing
  Channels:
    value: Channels
    context: missing
  Details:
    value: Details
    context: missing
  AddChannel:
    value: Add Channel
    context: missing
  SubscribeunsubscribeNotification:
    value: Subscribe/Unsubscribe Notification
    context: missing
  NoChannelFound:
    value: No Channel Found
    context: missing
  TotalChannels:
    value: 'Total Channels:'
    context: missing
  UpdateGroup:
    value: Update Group
    context: missing
  Updating:
    value: Updating...
    context: missing
  UploadDate:
    value: Upload Date
    context: missing
  NewGroup:
    value: New Group
    context: missing
  DetailsConfiguration:
    value: Details & Configuration
    context: missing
  IndicatesRequired:
    value: "* indicates required"
    context: missing
  GroupName:
    value: Group Name
    context: missing
  Groupname:
    value: groupName
    context: missing
  About:
    value: About
    context: missing
  ADescriptionExplainingUsersWhatThisGroupIsAbout:
    value: A description explaining users what this group is about.
    context: missing
  AddGroupLeader:
    value: Add Group Leader
    context: missing
  AddGroupAdmin:
    value: Add Group Admin
    context: missing
  AddGroupModerator:
    value: Add Group Moderator
    context: Label used on the button that allows add new group moderators
  AddGroupMember:
    value: Add Group Member
    context: missing
  Creating:
    value: Creating...
    context: missing
  CreateGroup:
    value: Create Group
    context: missing
  Topics:
    value: Topics
    context: missing
  More:
    value: more
    context: missing
  Channel:
    value: Channel
    context: missing
  SearchByAcceptedCriteriaToFindContentHere:
    value: Search by {accepted criteria to find content here}.
    context: missing
  Add:
    value: Add
    context: missing
  CardHasBeenSuccessfullyRemovedFromFeaturedContentSection:
    value: Card has been successfully removed from featured content section.
    context: missing
  CardHasBeenSuccessfullyAddedToFeaturedContentSection:
    value: Card has been successfully added to featured content section.
    context: missing
  YouCanNotMarkedMoreThan10FeaturedCardsInAGroup:
    value: You can not marked more than 10 featured cards in a group.
    context: missing
  LeaveGroup:
    value: Leave Group
    context: missing
  JoinGroup:
    value: Join Group
    context: missing
  Analytics:
    value: Analytics
    context: missing
  Leaderboard:
    value: Leaderboard
    context: missing
  InviteAdpEmployees:
    value: Invite ADP Employees
    context: missing
  InvitePeople:
    value: Invite People
    context: missing
  Decline:
    value: Decline
    context: missing
  Accept:
    value: Accept
    context: missing
  GroupLeaders:
    value: Group Leader(s)
    context: missing
  GroupAdmins:
    value: Group admin(s)
    context: missing
  GroupModerators:
    value: Group moderator(s)
    context: The label for list of moderators
  YouWontBeAbleToAccessThisGroupDoYouReallyWantToLeave:
    value: You won’t be able to access this group, do you really want to leave ?
    context: missing
  DeclineTitle:
    value: Decline {title}
    context: missing
  JoinGroupTitle:
    value: Join Group {title}
    context: missing
  ButtonlabelTitle:
    value: "{buttonLabel} {title}"
    context: missing
  ViewAll:
    value: View All
    context: missing
  EditGroup:
    value: Edit Group
    context: missing
  ManageGroup:
    value: Manage Group
    context: missing
  PendingMembers:
    value: Pending Members
    context: missing
  Insights:
    value: Insights
    context: missing
  DeleteGroup:
    value: Delete Group
    context: missing
  DeleteGroupToastMessage:
    value: "{group_name} has been deleted permanently and will no longer be visible."
    context: Toast message when the group is deleted
  WhatDoYouWishToShareWithThisGroup:
    value: What do you wish to share with this group?
    context: missing
  Smartcard:
    value: SmartCard
    context: missing
  Pathway:
    value: Pathway
    context: missing
  Journey:
    value: Journey
    context: missing
  OpenLeaderboard:
    value: Open Leaderboard
    context: missing
  You:
    value: You
    context: missing
  Points:
    value: points
    context: missing
  ThisIsAChannelImage:
    value: This is a channel image
    context: missing
  RemoveFromGroup:
    value: Remove from Group
    context: missing
  FeaturedContent:
    value: Featured Content
    context: missing
  ThereAreNoFeaturedContentInThisGroup:
    value: There are no featured content in this group
    context: missing
  DeclineGroupInvitation:
    value: Decline Group Invitation
    context: missing
  YouAreAboutToDeclineTheInvitationFor:
    value: You are about to decline the invitation for
    context: missing
  ThisActionCanNotBeUndoneHowDoYouWishToProceed:
    value: This action can not be undone. How do you wish to proceed?
    context: missing
  Groups:
    value: Groups
    context: missing
  Type:
    value: Type
    context: missing
  Group:
    value: Group
    context: missing
  ThisIsAGroupImage:
    value: This is a group image
    context: missing
  YouDoNotHaveAnyGroup:
    value: You do not have any group.
    context: missing
  AllFilters:
    value: All Filters
    context: missing
  Apply:
    value: Apply
    context: missing
  Save:
    value: Save
    context: missing
  SearchGroups:
    value: Search Groups...
    context: missing
  MyGroups:
    value: My groups
    context: missing
  GroupsIAmAnAdminOf:
    value: Groups I am an admin of
    context: missing
  GroupsIAmALeaderOf:
    value: Groups I am a leader of
    context: missing
  GroupsIAmAMemberOf:
    value: Groups I am a member of
    context: missing
  CommunitiesIAmAModeratorOf:
    value: Communities I am a moderator of
    context: use as a label for the list of communities the user is a moderator of
  MyPendingRequests:
    value: My pending requests
    context: missing
  OtherPublicGroups:
    value: Other public groups
    context: missing
  Private:
    value: Private
    context: missing
  Public:
    value: Public
    context: missing
  All:
    value: All
    context: missing
  SelectedusersOutOfTotalusersIndividualsSelected:
    context: missing
    value: "{selectedUsers} out of {totalUsers} individuals selected"
  ShowingMemberscountOutOfTotalmembers:
    context: missing
    value: Showing {membersCount} out of {totalMembers}
  YouAreAboutToRemoveUsernameFromGroupnameGroup:
    context: missing
    value: You are about to remove {userName} from {groupName} group
  YouHaveGrouplengthNewGroupInvitation:
    context: missing
    value: You have {groupLength} new group invitation
  GroupMembersWillNoLongerBeAbleToSeeThisCardInTheGroup:
    context: missing
    value: Group members will no longer be able to see this card in the group.
  ActivityFeed:
    value: Activity Feed
    context: Activity feed toggle in Group create/Edit page
  AllContentFilters:
    value: All Content Filters
    context: Show all content filters
  GroupLeaderDescription:
    value: "Manage group membership, Assign content to the members of the group"
    context: Group leader role description
  GroupInviteDescription:
    value: Invite team members to the group. You can invite individuals or bulk upload users to the group
    context: Group invite description
  UserUploadViaCSV:
    value: For users uploaded via CSV, system will directly add users to the group skipping group request acceptance flow
    context: descriptive text for csv user upload process
  CSVinProcess:
    value: Your CSV is being processed, we'll send you a notification when it's done
    context: descriptive text for csv user upload process
  CSVneedsValidation:
    value: Please check CSV for correct formatting. Use the template provided
    context: descriptive text for csv user upload process
  FileProcessing:
    value: File is processing. Please wait...
    context: descriptive text for csv user upload process
  ChannelRemoval:
    value: Do you really want to remove {channel_title} channel from the group?
    context: descriptive text for channel removal from group process
  AutomaticPastAssignment:
    value: This will automatically assign all past group assignments to any new members that join.
    context: informative text
  AddingMemberWarning:
    value: Users will not be able to leave the group once added. Only a group admin or a group leader can remove users.
    context: Warning text when adding new user to the group
  AssociateChannelsMsg:
    value: Associate relevant Channels with your Group. All the Group members will automatically follow the Associated Channels and will be unable to leave these Channels.
    context: informative text
  ChannelRemovedSuccessfully:
    value: Channel Removed Successfully
    context: informative text
  RemoveGroupConfirmation:
    value: "Are you sure you want to remove {groupName} group? This can't be undone."
    context: warning text when group removal.
  RemoveCommunityConfirmation:
    value: "Are you sure you want to delete this community? This action cannot be undone, and all associated content, discussions, and topics will be permanently removed."
    context: warning text when community removal
  SearchGroupLeader:
    value: Search Group Leader
    context: Placeholder text for Search Group Leader search input
  GroupLeadersUserTypes:
    value: Group Leaders
    context: Label text for Group Leaders users type
  GroupsWithApostrophe:
    value: "group's"
    context: Label text for Groups user belong to
  SearchGroupAdmin:
    value: Search Group Admin
    context: Placeholder text for Search Group Admin search input
  SearchGroupModerator:
    value: Search Group Moderator
    context: Placeholder text for Search Group Moderator search input
  GroupAdminsUserType:
    value: Group Admins
    context: Label text for Group Admins user type
  SearchGroupMember:
    value: Search Group Member
    context: Placeholder text for Search Group Member search input
  GroupMembersUserType:
    value: Group Members
    context: Label text for Group Members user type
  GroupModeratorUserType:
    value: Group Moderators
    context: Label text for Group Moderators user type
  GroupFeaturedTabNoContentMessage:
    value: 'Your featured tab has no content yet!'
    context: 'Message displayed if featured tab has no content'
  GrpContentInaccessibleRefineQueryOrShowMore:
    value: You may not have access to all content in this Group. Please refine your search query or click on Show More
    context: Message shown in add content modal of grp when no accessible result on search and no more content
  GrpContentInaccessibleRefineQuery:
    value: You may not have access to all content in this Group. Please refine your search query
    context: Message shown in add content modal of grp when no accessible result on search and no more content
  featuredPosts:
    context: This is for displaying the name for group content section featured
    value: Featured Posts
  groupAssignments:
    context: This is for displaying the name for group content section assignments
    value: Group Assignments
  associatedChannels:
    context: This is for displaying the name for group content section channels
    value: Associated Channels
  BulkRemovalHistory:
    context: Header for the section with the table of uploaded bulk removal files
    value: Bulk Removal History
  BulkRemoval:
    context: Header for a tab, that opens settings for bulk remove users.
    value: Bulk Removal
  YouAreAboutToInitiateABulkRemovalOfUsers:
    context: Paragraph no.1 for bulk removal modal
    value: You are about to initiate a bulk removal of users from the group using a CSV upload. This action cannot be undone. Please review the CSV file carefully to ensure accuracy.
  AreYouSureYouWantToProceedWithTheBulkRemoval:
    context: Paragraph no.2 for bulk removal modal
    value: Are you sure you want to proceed with the bulk removal?
  RemoveGroupMember:
    context: Bulk removal modal title
    value: Remove Group Member
  RemoveGroupMemberContent:
    context: Bulk removal modal content
    value: You are about to remove {numberOfMembers} members(s) from {groupName} group
  SelectedUsersToRemove:
    context: Amount of selected users to be removed from group
    value: "{usersToRemoveCount} out of {totalMembers} members selected"
  GroupSectionDeletedOrDisabled:
    context: This section is used when no content section is found on standalone page.
    value: This section of group is deleted or disabled
  SelectToRemoveUser:
    context: Aria Label for the checkbox to select the user to delete from the group.
    value: Select to remove the user {userName} from the group.
  RemoveSectionFromGroupConsentMessage:
    context: Consent message shown on modal which is used to remove section from group
    value: You are about to remove section {name}. This action only removes the section however doesn't remove the content from the group and cannot be undone
  UploadCsv:
    context: Additional label for the Browse button used by the assistive technology (WCAG)
    value: Clicking this button allows to upload file
  Home:
    context: Default Carousel name for channel top navigation bar.
    value: Home
  MyAssignments:
    context: Default Carousel name for channel top navigation bar.
    value: My Assignments
  NoGroupContentAvailableDescriptionLabel:
    context: description message when channel do not have description
    value: Your group has no description yet!
  UserInvitedSuccessfully:
    context: This label is used to display a sucess message after inviting a user to a group sucesfully
    value: User Invited Successfully
  SearchOrRemoveUserTotalGroupLeadersLabel:
    context: Label for totalize users from a specific type on search or remove user list smartcard modal collaborators.
    value: 'Total Group Leaders'
  SearchOrRemoveUserTotalGroupAdminsLabel:
    context: Label for totalize users from a specific type on search or remove user list smartcard modal collaborators.
    value: 'Total Group Admins'
  SearchOrRemoveUserTotalGroupMembersLabel:
    context: Label for totalize users from a specific type on search or remove user list smartcard modal collaborators.
    value: 'Total Group Members'
  SearchOrRemoveUserTotalGroupModeratorLabel:
    context: Label for totalize users from a specific type on search or remove user list smartcard modal collaborators.
    value: 'Total Group Moderators'
  NoContentAvailableDefaultDescriptionGroup:
    context: default description message for no content available group component
    value: Edit your group to add content and customize its layout.
  NoContentAvailableEditButtonLabelGroup:
    context: button label for edit channel when not available content component is showed. e.g. Edit Group
    value: Edit Group
  NoContentAvailableMessageLabelGroup:
    context: principal message when no content available component group is showed
    value: Your group has no content yet!
  GroupCreatedSuccessfully:
    context: Default message on successful creation for group.
    value: Group Created Successfully
  SomeUsersWereAlreadyInvited:
    context: Error message on inviteing already member users.
    value: Some users were already invited, or are part of the group
  AtleastOneAcceptedGroupUserIsRequired:
    context: Warning message on deleting all users of group
    value: At least one accepted group user is required
  Settings:
    context: Navigation tab in group settings
    value: Settings
  Members:
    context: Navigation tab in view members for group
    value: Members
  Individuals:
    context: Navigation tab in invite team members for group
    value: Individuals
  BulkUpload:
    context: header label for bulk upload of team members in group
    value: Bulk Upload
  BulkRemoveGroupMembers:
    context: popup header label on bulk removal of group members
    value: Remove Group Member
  RemoveUser:
    context: popup header label for removing a member from group where {user} is role of the member
    value: 'Remove {user}'
  GroupUpdatedSuccessfully:
    context: Default message on successful creation for group.
    value: Group Updated Successfully
  PublicGroup:
    context: label to inform Public group
    value: Public group
  NoContentGroupLabel:
    context: principal message when no content available component group is showed
    value: Your Group has no content yet!
  ContentManagementSectionHeaderContent:
    context: text label for content type section
    value: (Content Section)
  ContentManagementSectionHeaderChannel:
    context: text label for content type section
    value: (Channel Section)
  GroupFilters:
    context: Group filters heading text
    value: Group Filters
  ShowAutomaticGroups:
    context: Show automatic group toggle text
    value: Show Automatic Groups
  ShowAutomaticGroupsTooltip:
    context: Show automatic group toggle info tooltip
    value: Users who are not members of any Automatic groups will not see these groups when the toggle is enabled. However, if an Automatic group is set to public, it will remain visible to all users.
  StartAPost:
    context: Label for button to create new group post
    value: Start a post
  EnableRestrictTo:
    context: Checkbox label for restricting private content
    value: Enable "Restrict To" for Private Content
  MembersCanRestrict:
    context: Checkbox sub text for restricting private content
    value: Members can restrict private content to this group
  CreatePost:
    context: Label for create post modal
    value: Create post
  PostRemovedSuccessfully:
    context: Message after post removing
    value: Post Removed Successfully
  GbacInfoMessage:
    context: Show gbac message for bulk upload
    value: "Only the users that you have access to can be uploaded via CSV. Users that you don’t have access to would be ignored and will not be added to the Group."
  Engagement:
    context: Engagement section name on group creation form
    value: "Engagement"
  PostFeed:
    context: Label for switch to enable feed in posting (group wall) feed
    value: "Post Feed"
  NoCommunity:
    context: Label for radio button to enable feed in posting (group wall) feed
    value: "No community"
  TopicBasedCommunity:
    context: Label for radio button to enable feed in posting (group wall) feed with topic
    value: "Topic-Based Community"
  LearningCommunity:
    context: Label for radio button to enable feed in posting (group wall) feed with topic and attachments
    value: "Learning community"
  TopicBasedCommunityDescription:
    context: Description for radio button to enable feed in posting (group wall) feed with topic
    value: "Create a structured community where discussions are organized by topics. Members can post, engage, and explore content within specific topics for better navigation and engagement."
  LearningCommunityDescription:
    context: Description for radio button to enable feed in posting (group wall) feed with topic and attachments
    value: "Create a community focused on learning, where members can access structured content, participate in discussions, share knowledge, and track progress to enhance their learning experience."
  Community:
    context: Label for description for switch to enable feed in posting (group wall) feed
    value: "Community"
  CommunityDescription:
    context: Description for switch to enable feed in posting (group wall) feed
    value: "Allow users to manage memberships, content, and social interactions in the groups."
  AllPostsNavigation:
    context: The navigation element in select, by selecting shows all posts
    value: All posts
  WaitingForApprovalNavigation:
    context: The navigation element in select that filter waiting for approval posts
    value: Waiting for approval
  ApprovedNavigation:
    context: The navigation element in select that filter only approved items
    value: Approved
  ArchivedNavigation:
    context: The navigation element in select that filter only archived items
    value: Archived
  PostColumnName:
    context: The column name for posts table
    value: Post
  AuthorColumnName:
    context: The column name for posts, reported content and topic table
    value: Author
  StatusColumnName:
    context: The column name for posts table
    value: Status
  CreatedColumnName:
    context: The column name for posts, reported content and topic table table
    value: Created
  ViewsColumnName:
    context: The column name for posts table
    value: Views
  LikesColumnName:
    context: The column name for posts table
    value: Likes
  RepliesColumnName:
    context: The column name for posts table
    value: Replies
  ActionsColumnName:
    context: The column name for posts, reported content and topic table
    value: Actions
  ApprovePostsAction:
    context: The name for action for the posts table row
    value: Approve
  RejectPostsAction:
    context: The name for action for the posts table row
    value: Reject
  EditPostsAction:
    context: The name for action for the posts table row
    value: Edit
  DeletePostsAction:
    context: The name for action for the posts table row
    value: Delete
  ButtonAddPostLabel:
    context: The label for button that allows to add posts
    value: Add Post
  ReportedNavigation:
    context: The navigation element in select for reported content
    value: Reported
  TrashedNavigation:
    context: The navigation element in select for reported content
    value: Trashed
  NameColumnName:
    context: The column name for Reported Content table
    value: Name
  TopicColumnName:
    context: The column name for Reported Content table
    value: Topic
  TypeColumnName:
    context: The column name for Reported Content table
    value: Type
  ReasonColumnName:
    context: The column name for Reported Content table
    value: Reason
  ReportersColumnName:
    context: The column name for Reported Content table
    value: Reporters
  DismissReportedContentAction:
    context: The name for the action for the reported content table row
    value: Dismiss
  DeleteReportedContentAction:
    context: The name for the action for the reported content table row
    value: Delete
  CommunityPostReportedContentDismissed:
    context: Message for successful post reported content dismiss action
    value: Post reported content dismissed
  CommentReportedContentDismissed:
    context: Message for successful comment reported content dismiss action
    value: Comment reported content dismissed
  CommunityPostReportedContentTrashed:
    context: Message for successful post reported content trash action
    value: Post trashed
  CommentReportedContentTrashed:
    context: Message for successful comment reported content trash action
    value: Comment trashed
  TopicFormFieldName:
    context: The text field title for name in the topic form
    value: Name
  TopicFormFieldNamePlaceholder:
    context: Placeholder fo the text field name in the topic form
    value: Enter topic name
  TopicFormFieldDescription:
    context: The text field title for description in the topic form
    value: Description
  TopicFormFieldDescriptionPlaceholder:
    context: Placeholder fo the text field description in the topic form
    value: Describe this topic
  TopicFormNameRequiredMessage:
    context: Topic form error message for required name field
    value: Topic name is required
  TopicModalCreateTitle:
    context: Topic modal title
    value: Create Topic
  TopicModalEditTitle:
    context: Topic modal title
    value: Edit Topic
  TopicFormButtonSavingInProgress:
    context: Button label during the saving process
    value: Saving...
  TopicFormButtonSave:
    context: Button label for saving the topic
    value: Save
  TopicFormSavingSuccessfullyNotification:
    context: Notification message for successful saving of the topic
    value: Topic has been successfully created
  TopicFormUpdatingSuccessfullyNotification:
    context: Notification message for successful updating of the topic
    value: Topic has been successfully updated
  TopicFormSavingFailureNotification:
    context: Notification message for failed saving of the topic
    value: Topic cannot be saved
  AllTopicsNavigation:
    context: The navigation element in select for topics
    value: All topics
  LastUpdateColumnName:
    context: The column name for topic table
    value: Last Update
  PostsColumnName:
    context: The column name for topic table
    value: Posts
  EditTopicAction:
    context: The name for the action for the topic table row
    value: Edit
  DeleteTopicAction:
    context: The name for the action for the topic table row
    value: Delete
  ButtonAddTopicLabel:
    context: The label for button that allows to add topics
    value: Add Topic
  ManagePostPageTitle:
    context: The title for the manage posts page
    value: Manage Posts
  TopicsTabLabel:
    context: The label for tab in the manage posts page
    value: Topics
  PostsTabLabel:
    context: The label for tab in the manage posts page
    value: Posts
  ReportedContentTabLabel:
    context: The label for tab in the manage posts page
    value: Reported Content
  SelectTopicsDefaultItem:
    context: The first element in select for topics
    value: Select topic
  ManagePostsTableErrorEmptyStateTitle:
    context: The title for empty state in the manage posts table
    value: Something went wrong
  ManagePostsTableErrorEmptyStateDescription:
    context: The description for empty state in the manage posts table
    value: Please try again
  ManagePostsTableErrorEmptyStateButtonLabel:
    context: The label for button in the empty state in the manage posts table
    value: Refresh page
  ManagePostsTableNoResultEmptyStateTitle:
    context: The title for empty state in the manage posts table when no results
    value: No results found
  ManagePostsTableNoResultEmptyStateDescription:
    context: The description for empty state in the manage posts table when no results
    value: Looks like the result you are looking for doesn't exist.
  SeeMoreReplies:
    context: Button's label to fetch/load more replies, than there are already visible, for given comment
    value: See more replies
  CollapseReplies:
    context: Button's label to hide displayed list of comments
    value: Collapse replies
  LastReply:
    context: Info label to show last reply date
    value: Last reply
  Reply:
    context: Button's label to reply to given comment
    value: Reply
  ReplyTo:
    context: Label indicating who the user is replying to
    value: 'Reply to {user}'
  Replies:
    context: Label for displaying an amount of replies
    value: '{number} replies'
  OneReply:
    context: Label indicating that there was only one reply to given comment
    value: 1 reply
  NewestFirstSortLabel:
    context: A suffix appended to the label in a select input, specifying the sorting order of the table columns.
    value: Newest first
  LatestFirstSortLabel:
    context: A suffix appended to the label in a select input, specifying the sorting order of the table columns.
    value: Latest first
  DeleteTopicConfirmationModalTitle:
    context: Title for confirmation modal for deleting topic
    value: Are you sure you want to delete this topic?
  DeleteTopicConfirmationModalDescription:
    context: Description for confirmation modal for deleting topic
    value: Deleting the item will stop content sharing in this topic and remove the topic from linked instances.
  PleaseSelectAtLeastOneTopic:
    context: Error message when a user tries to save community without any topics
    value: "Please create a topic."
  ManageTopics:
    context: Label for the button to navigate to manage topics
    value: "Manage topics"
  TopicDeletedErroredMessage:
    context: Error message when a topic cannot be deleted
    value: "Topic cannot be deleted"
  TopicDeletedSuccessMessage:
    context: Success message when a topic is deleted
    value: "Topic has been deleted"
  LastActiveTopicErrorMessage:
    context: Error message when a topic cannot be deleted due to last remain topic
    value: You need to keep at least one topic in your community.
  PostCreateChangePostTypeConfirmationTitle:
    context: Title of confirmation modal when user change post type
    value: Are you sure you want to proceed with this action?
  PostCreateChangePostTypeConfirmationMessage:
    context: Message of confirmation modal when user change post type
    value: This action will remove the content specific to your selected post type. Your post description, topic, and tags will remain unchanged.
  AddMention:
    context: Tooltip message for button placed on create post modal toolbar
    value: Add Mention
  AddMedia:
    context: Tooltip message for button placed on create post modal toolbar
    value: Add Media
  AddPoll:
    context: Tooltip message for button placed on create post modal toolbar
    value: Add Poll
  AddQuiz:
    context: Tooltip message for button placed on create post modal toolbar
    value: Add Quiz
  AddFile:
    context: Tooltip message for button placed on create post modal toolbar
    value: Add File
  QuizQuestion:
    context: Text field title for quiz form
    value: Quiz question
  PollQuestion:
    context: Text field title for poll form
    value: Poll question
  PleaseAddHereYourQuestion:
    context: Text field placeholder text for poll/question form
    value: Please add here your question
  EnterQuestion:
    context: Text field description for poll/question form
    value: Enter question
  PollOptions:
    context: Label for options in poll form
    value: Poll options
  QuizOptions:
    context: Label for options in question form
    value: Quiz options
  PollOption:
    context: Placeholder for option in poll form
    value: Poll option
  QuizOption:
    context: Placeholder for option in question form
    value: Quiz option
  NameIsRequired:
    context: Error label for poll form
    value: Name is required
  OptionIsRequired:
    context: Error label for poll form
    value: Option is required
  AddQuestion:
    context: Button text for adding question
    value: Add question
  InvalidPostMessage:
    context: Error label for create post form
    value: Invalid post message
  TopicIsRequired:
    context: Error label for create post form
    value: Topic is required
  TopicContainsPostsErrorMessage:
    context: Error message when a topic cannot be deleted due to containing posts
    value: You cannot remove topic that includes posts inside.
  SelectStatusLabel:
    context: Label for the select to filter by status
    value: "Status"
  SelectTopicsLabel:
    context: Label for the select to filter topics
    value: "Topics"
  Widget:
    context: Widget section name on group creation form
    value: 'Widget'
  BannerWidget:
    context: Label for checkbox to enable widget input
    value: 'Banner widget'
  BannerWidgetHint:
    context: Description for checkbox to enable widget input
    value: 'Display a customizable banner between the header and content area to share updates, announcements, or important information.'
  BannerWidgetContent:
    context: Label for input of widget content
    value: 'Banner widget content'
  BannerWidgetPlaceholder:
    context: Placeholder for input of widget content
    value: 'Add content here'
  BannerWidgetContentDescription:
    context: Description for input of widget content
    value: 'The banner widget can be customized to show text or embed HTML code.'
  SearchWithEllipsis:
    context: Label ending with three dots for search input
    value: Search...
  DeletePostConfirmationModalTitle:
    context: Delete post confirmation dialogs title
    value: Are you sure you want to delete this post?
  DeletePostConfirmationModalDescription:
    context: Delete post confirmation dialogs description
    value: Deleting the item will remove all linked instances and activities.
  NoPostAdded:
    context: Label for empty state when there are no posts created in the group
    value: No post added
  CreateFirstPostInGroup:
    context: Hint label to suggest user to create a new post in the group
    value: Create first post in your group
  CreateFirstPostInCommunity:
    context: Hint label to suggest user to create a new post in the community
    value: Create first post in your community
  GroupWidgetTitle:
    context: If the HTML widget is off, shown this title for the header widget
    value: 'Add your banner widget'
  GroupWidgetDescription:
    context: Description for group header widget
    value: 'Get started by adding your banner widget and customize it with your own text or embed HTML to create a unique and engaging experience.'
  AddWidget:
    context: Button text for group header widget
    value: 'Add widget'
  PostWaitingForApprovalTitle:
    context: Title of a banner informing user that this post needs to be approved by the moderator
    value: Your post is waiting for approval
  PostSubmittedForApprovalInCommunity:
    context: Info message of a banner informing user that this post for given community needs to be approved by the moderator first
    value: Your post has been submitted for review. It will be visible in the community once approved by the moderator. If not approved, it will not be published.
  PostSubmittedForApprovalInGroup:
    context: Info message of a banner informing user that this post for given group needs to be approved by the moderator first
    value: Your post has been submitted for review. It will be visible in the group once approved by the moderator. If not approved, it will not be published.
  Topic:
    context: Singular form of label Topics (single community topic)
    value: Topic
  EditPost:
    context: Title of a modal where user can change post content
    value: Edit post
  ContentApproval:
    context: config title in the group configuration page
    value: Content approval
  ContentApprovalDesc:
    context: config description in the group configuration page
    value: All posts in the group must be approved by an admin or moderator.
  PostsCountAriaLabel:
    context: Aria label text for button that redirect to posts tab
    value: 'View posts for topic {topicName}'
  AddLearningMaterials:
    context: Label for button to add new learning materials to community group
    value: 'Add learning materials'
  MaximumLearningMaterials:
    context: Description under the button add new learning materials when materials are the maximum allowed number of materials is reached
    value: 'Maximum 2 learning materials'
  FileLabel:
    context: Label for file radio button in the add learning materials form
    value: 'File'
  LinkLabel:
    context: Label for link radio button in the add learning materials form
    value: 'Link'
  MaterialTitle:
    context: The text field title for title in the material form
    value: 'Material title'
  MaterialDescription:
    context: The text field title for description in the material form
    value: 'Material description'
  SelectTypeLabel:
    context: The label for radio button in the material form
    value: 'Select type'
  EnterLinkLabel:
    context: The label for input with link in the material form
    value: 'Enter link'
  MaterialModalCreateTitle:
    context: Material modal title
    value: 'Create learning materials'
  MaterialModalEditTitle:
    context: Material modal title
    value: 'Edit learning materials'
  MaterialEditLabel:
    context: The text of button for editing the material
    value: 'Edit material'
  MaterialSavingSuccessfullyNotification:
    context: Notification message for successful saving of the material
    value: 'Material has been successfully created'
  MaterialUpdatingSuccessfullyNotification:
    context: Notification message for successful updating of the material
    value: 'Material has been successfully updated'
  MaterialDeletingSuccessfullyNotification:
    context: Notification message for successful deleting of the material
    value: 'Material has been deleted'
  PleaseCreateAMaterial:
    context: Error message when a user tries to save community without any materials
    value: 'Please create a material.'
  MaterialFormTitleRequiredMessage:
    context: Material form error message for required title field
    value: 'Material title is required'
  MaterialFormLinkRequiredMessage:
    context: Material form error message for required link field
    value: 'Material link is required'
  MaterialFormFileRequiredMessage:
    context: Material form error message for required file field
    value: 'Material file is required'
