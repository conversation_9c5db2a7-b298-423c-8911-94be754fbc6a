prefix: flare.flare
labels:
  # Common labels
  Scroll Up:
    value: Scroll up
    context: Used as a button title in ScrollWizard component in RCLv2 and any other ui components that are using ScrollWizard
  Scroll Down:
    value: Scroll down
    context: Used as a button title in ScrollWizard component in RCLv2 and any other ui components that are using ScrollWizard
  FLR.TOTAL_COUNT_OF_SELECTED_ROWS:
    value: Total {0} of {1} row(s) selected
    context: Label for the number of selected rows in the data grid. {0} is the number of selected rows, {1} is the total number of rows
  FLR.Common.CLEAR_SEARCH:
    value: Clear search
    context: Button label which clears the search input
  FLR.Common.FIRST_PAGE:
    value: First page
    context: Button label which navigates to the first page
  FLR.Common.LAST_PAGE:
    value: Last page
    context: Button label which navigates to the last page
  FLR.Common.NEXT_PAGE:
    value: Next page
    context: Button label which navigates to the next page
  FLR.Common.PREVIOUS_PAGE:
    value: Previous page
    context: Button label which navigates to the previous page
  RCL2.TextField.CHARACTERS_COUNT_MESSAGE:
    value: You have {0} characters remaining
    context: label for characters count in textfield. {0} have value greater that 1 as value
  RCL2.TextField.CHARACTER_COUNT_MESSAGE:
    value: You have {0} character remaining
    context: label for character count in textfield. {0} have 0 or 1 as value
  # File uploader
  FLR.FileUploader.CLEAR_ALL:
    value: Clear all
    context: Button label which clears all files selected for upload
  FLR.FileUploader.BROWSE:
    value: Browse
    context: Button label which opens a file window to select a file to upload
  # Preloader
  FLR.Preloader.LOADING_ANNOUNCEMENT_TEXT:
    value: Please wait, content is loading.
    context: Message announcing that content is loading
  FLR.Preloader.LOADED_ANNOUNCEMENT_TEXT:
    value: Content has been loaded.
    context: Message announcing that content has been loaded
  # Data Grid
  FLR.DataGrid.ACTIONS:
    value: Actions
    context: Table column name
  FLR.DataGrid.AND:
    value: and
    context: Extra label for the concatenation of two labels
  FLR.DataGrid.CANCEL:
    value: Cancel
    context: Button label which cancels the current action
  FLR.DataGrid.COLLAPSE_GROUPED_ROWS:
    value: Collapse {0}, {1} rows
    context: After data is grouped by column, {0} will be string. {1} will be a number
  FLR.DataGrid.EXPAND_GROUPED_ROWS:
    value: Expand {0}, {1} rows
    context: After data is grouped by column, {0} will be string. {1} will be a number
  FLR.DataGrid.DROP_TO_GROUP_BY:
    value: Drop to group by {0}
    context: Label for the drag and drop action to group by column
  FLR.DataGrid.SELECTED_COUNT_OF_ROW_COUNT_ROWS_SELECTED:
    value: '{0} of {1} row(s) selected'
    context: Label for the number of selected rows in the data grid. {0} is the number of selected rows, {1} is the total number of rows
  FLR.DataGrid.UNSORTED:
    value: Unsorted
    context: Label for the unsorted state of a column
  FLR.DataGrid.UNPIN_ALL:
    value: Unpin all
    context: Button label which unpins all columns
  FLR.DataGrid.UNPIN:
    value: Unpin
    context: Button label which unpins the column
  FLR.DataGrid.TOGGLE_VISIBILITY:
    value: Toggle visibility
    context: Button label which toggles the visibility of the column
  FLR.DataGrid.THEN_BY:
    value: ', then by '
    context: It refers to grouping column names
  FLR.DataGrid.SORT_BY_COLUMN_DESC:
    value: Sort by {0} descending
    context: It refers to sorting column names
  FLR.DataGrid.SHOW_SELECTED:
    value: Show selected
    context: Button label which shows the selected rows
  FLR.DataGrid.SHOW_SEARCH:
    value: Show search
    context: Button label which shows the search input
  FLR.DataGrid.SHOW_FILTERS:
    value: Show filters
    context: Button label which shows the filters
  FLR.DataGrid.SHOW_ALL_COLUMNS:
    value: Show all columns
    context: Button label which shows all columns
  FLR.DataGrid.SHOW_ALL:
    value: Show all
    context: Button label which shows all rows
  FLR.DataGrid.SELECT_ALL:
    value: Select all
    context: Button label which selects all rows
  FLR.DataGrid.SELECT:
    value: Select
    context: Button label which selects the row
  FLR.DataGrid.SEARCH:
    value: Search
    context: Button label which opens a search input
  FLR.DataGrid.SAVE_VIEW:
    value: Save view
    context: Button label for saving a view configuration of Table
  FLR.DataGrid.SAVE:
    value: Save
    context: Button label which saves the current action
  FLR.DataGrid.ROWS_PER_PAGE:
    value: Rows per page
    context: Label for the number of rows per page
  FLR.DataGrid.ROW_SPACING_EXPANDED:
    value: Expanded row spacing
    context: Label for the expanded row spacing
  FLR.DataGrid.ROW_SPACING_DEFAULT:
    value: Default row spacing
    context: Label for the default row spacing
  FLR.DataGrid.ROW_SPACING_CONDENSED:
    value: Condensed row spacing
    context: Label for the condensed row spacing
  FLR.DataGrid.ROW_NUMBERS:
    value: Row numbers
    context: Label for the row numbers
  FLR.DataGrid.ROW_NUMBER:
    value: Row number
    context: Label for the row number
  FLR.DataGrid.ROW_ACTIONS:
    value: Row actions
    context: Label for the row actions
  FLR.DataGrid.RESET_TO_DEFAULT:
    value: Reset to default
    context: Button label which resets the current action to default
  FLR.DataGrid.RESET_ORDER:
    value: Reset order
    context: Button label which resets the current action to order
  FLR.DataGrid.RESET_COLUMN_SIZE:
    value: Reset column size
    context: Button label which resets the current action to column size
  FLR.DataGrid.PREVIOUS_PAGE:
    value: Previous page
    context: Button label which goes to the previous page
  FLR.DataGrid.PIN_TO_RIGHT:
    value: Pin to right
    context: Button label which pins the column to the right
  FLR.DataGrid.PIN_TO_LEFT:
    value: Pin to left
    context: Button label which pins the column to the left
  FLR.DataGrid.OR:
    value: or
    context: Extra label for the concatenation of two labels
  FLR.DataGrid.OF:
    value: of
    context: Extra label for the concatenation of two labels
  FLR.DataGrid.NO_RESULTS_FOUND:
    value: No results found
    context: Label for the no results found state of the data grid
  FLR.DataGrid.NO_RECORDS_TO_DISPLAY:
    value: No records to display
    context: Label for the no records found state of the data grid
  FLR.DataGrid.NEXT_PAGE:
    value: Next page
    context: Button label which goes to the next page
  FLR.DataGrid.MOVE:
    value: Move
    context: Button label which moves the column
  FLR.DataGrid.MIN:
    value: Min
    context: Label for the minimum value of the column
  FLR.DataGrid.MAX:
    value: Max
    context: Label for the maximum value of the column
  FLR.DataGrid.MANAGE_COLUMNS:
    value: Manage columns
    context: Button label which opens the manage columns dialog
  FLR.DataGrid.LAST_PAGE:
    value: Last page
    context: Button label which goes to the last page
  FLR.DataGrid.HIDE_SEARCH:
    value: Hide search
    context: Button label which hides the search input
  FLR.DataGrid.HIDE_FILTERS:
    value: Hide filters
    context: Button label which hides the filters
  FLR.DataGrid.HIDE_ALL:
    value: Hide all
    context: Button label which hides all rows
  FLR.DataGrid.GROUPED_BY:
    value: Grouped by
    context: Label for the grouped by state of the data grid
  FLR.DataGrid.GRAB:
    value: Grab
    context: It denotes the action of dragging an icon into droppable area
  FLR.DataGrid.FULL_SCREEN:
    value: Full screen
    context: Button label which opens the data grid in full screen mode
  FLR.DataGrid.FIRST_PAGE:
    value: First page
    context: Button label which goes to the first page
  FLR.DataGrid.FILTERING_BY_COLUMN:
    value: Filtering by {0} - {1} {2}
    context: Label for the filtering by column state of the data grid
  FLR.DataGrid.CHANGE_FILTER_MODE:
    value: Change filter mode
    context: Button label which changes the filter mode
  FLR.DataGrid.CHANGE_SEARCH_MODE:
    value: Change search mode
    context: Button label which changes the search mode
  FLR.DataGrid.CLEAR_FILTER:
    value: Clear filter
    context: Button label which clears the filter
  FLR.DataGrid.CLEAR_SEARCH:
    value: Clear search
    context: Button label which clears the search input
  FLR.DataGrid.CLEAR_SORT:
    value: Clear sort
    context: Button label which clears the sort
  FLR.DataGrid.CLICK_TO_COPY:
    value: Click to copy
    context: Button label which copies the content to clipboard
  FLR.DataGrid.COLLAPSE:
    value: Collapse
    context: Button label which collapses a section
  FLR.DataGrid.COLLAPSE_ALL:
    value: Collapse all
    context: Button label which collapses all sections
  FLR.DataGrid.COLUMN_ACTIONS:
    value: Column actions
    context: Label for column actions menu
  FLR.DataGrid.COLUMN_RESIZE:
    value: Column resize
    context: Label for resizing a column
  FLR.DataGrid.COPIED_TO_CLIPBOARD:
    value: Copied to clipboard
    context: Message indicating content has been copied to clipboard
  FLR.DataGrid.DESELECT:
    value: Deselect
    context: Button label which deselects a row
  FLR.DataGrid.DESELECT_ALL:
    value: Deselect all
    context: Button label which deselects all rows
  FLR.DataGrid.EDIT:
    value: Edit
    context: Button label which allows editing
  FLR.DataGrid.EXIT_FULL_SCREEN:
    value: Exit full screen
    context: Button label which exits full screen mode
  FLR.DataGrid.EXPAND:
    value: Expand
    context: Button label which expands a section
  FLR.DataGrid.EXPAND_ALL:
    value: Expand all
    context: Button label which expands all sections
  FLR.DataGrid.FILTER_ARR_INCLUDES:
    value: Includes
    context: Label for filter condition "includes"
  FLR.DataGrid.FILTER_ARR_INCLUDES_ALL:
    value: Includes all
    context: Label for filter condition "includes all"
  FLR.DataGrid.FILTER_ARR_INCLUDES_SOME:
    value: Includes
    context: Label for filter condition "includes some"
  FLR.DataGrid.FILTER_BETWEEN:
    value: Between
    context: Label for filter condition "between"
  FLR.DataGrid.FILTER_BETWEEN_INCLUSIVE:
    value: Between inclusive
    context: Label for filter condition "between inclusive"
  FLR.DataGrid.FILTER_CONTAINS:
    value: Contains
    context: Label for filter condition "contains"
  FLR.DataGrid.FILTER_EMPTY:
    value: Empty
    context: Label for filter condition "empty"
  FLR.DataGrid.FILTER_ENDS_WITH:
    value: Ends with
    context: Label for filter condition "ends with"
  FLR.DataGrid.FILTER_EQUALS:
    value: Equals
    context: Label for filter condition "equals"
  FLR.DataGrid.FILTER_EQUALS_STRING:
    value: Equals
    context: Label for filter condition "equals string"
  FLR.DataGrid.FILTER_FUZZY:
    value: Fuzzy
    context: Label for filter condition "fuzzy"
  FLR.DataGrid.FILTER_GREATER_THAN:
    value: Greater than
    context: Label for filter condition "greater than"
  FLR.DataGrid.FILTER_GREATER_THAN_OR_EQUAL_TO:
    value: Greater than or equal to
    context: Label for filter condition "greater than or equal to"
  FLR.DataGrid.FILTER_IN_NUMBER_RANGE:
    value: Between
    context: Label for filter condition "in number range"
  FLR.DataGrid.FILTER_INCLUDES_STRING:
    value: Contains
    context: Label for filter condition "includes string"
  FLR.DataGrid.FILTER_INCLUDES_STRING_SENSITIVE:
    value: Contains
    context: Label for filter condition "includes string case sensitive"
  FLR.DataGrid.FILTER_LESS_THAN:
    value: Less than
    context: Label for filter condition "less than"
  FLR.DataGrid.FILTER_LESS_THAN_OR_EQUAL_TO:
    value: Less than or equal to
    context: Label for filter condition "less than or equal to"
  FLR.DataGrid.FILTER_MODE:
    value: Filter mode
    context: Label for filter mode
  FLR.DataGrid.FILTER_NOT_EMPTY:
    value: Not empty
    context: Label for filter condition "not empty"
  FLR.DataGrid.FILTER_NOT_EQUALS:
    value: Not equals
    context: Label for filter condition "not equals"
  FLR.DataGrid.FILTER_STARTS_WITH:
    value: Starts with
    context: Label for filter condition "starts with"
  FLR.DataGrid.FILTER_WEAK_EQUALS:
    value: Weak equals
    context: Label for filter condition "weak equals"
  FLR.DataGrid.GLOBAL_FILTER:
    value: Global filter
    context: Label for global filter input
  FLR.DataGrid.TOGGLE_SELECT_ROW:
    value: Toggle select row
    context: Button label which toggles row selection
  FLR.DataGrid.GLOBAL_FILTER_RESULTS:
    value: Global filter results
    context: Label for global filter results
  FLR.DataGrid.SORTED_BY_COLUMN_DESC:
    value: Sorted by {0} descending
    context: Label for sorted column in descending order
  FLR.DataGrid.SORTED_BY_COLUMN_ASC:
    value: Sorted by {0} ascending
    context: Label for sorted column in ascending order
  FLR.DataGrid.FILTER_BY_COLUMN:
    value: Filter by {0}
    context: Label for filtering by a specific column
  FLR.DataGrid.SORT_BY_COLUMN_ASC:
    value: Sort by {0} ascending
    context: Label for sorting by a specific column in ascending order
  FLR.DataGrid.HIDE_COLUMN:
    value: Hide {0} column
    context: Button label which hides a column
  FLR.DataGrid.UNGROUP_BY_COLUMN:
    value: Ungroup by {0}
    context: Label for ungrouping by a specific column
  FLR.DataGrid.GROUP_BY_COLUMN:
    value: Group by {0}
    context: Label for grouping by a specific column
  # Pagination
  FLR.AdvancePagination.PAGINATION_INFO:
    value: '{0}-{1} of {2}'
    context: Pagination info text example 1-10 of 100
