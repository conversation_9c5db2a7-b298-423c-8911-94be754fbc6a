app: flare.flare
labelKeys:
  # Common
  - flare.flare:Scroll Up
  - flare.flare:Scroll Down
  - flare.flare:FLR.TOTAL_COUNT_OF_SELECTED_ROWS
  - flare.flare:FLR.Common.CLEAR_SEARCH
  - flare.flare:FLR.Common.FIRST_PAGE
  - flare.flare:FLR.Common.LAST_PAGE
  - flare.flare:FLR.Common.NEXT_PAGE
  - flare.flare:FLR.Common.PREVIOUS_PAGE
  - flare.flare:RCL2.TextField.CHARACTERS_COUNT_MESSAGE
  - flare.flare:RCL2.TextField.CHARACTER_COUNT_MESSAGE
  # File uploader
  - flare.flare:FLR.FileUploader.CLEAR_ALL
  - flare.flare:FLR.FileUploader.BROWSE
  # Preloader
  - flare.flare:FLR.Preloader.LOADING_ANNOUNCEMENT_TEXT
  - flare.flare:FLR.Preloader.LOADED_ANNOUNCEMENT_TEXT
  # Data grid
  - flare.flare:FLR.DataGrid.ACTIONS
  - flare.flare:FLR.DataGrid.AND
  - flare.flare:FLR.DataGrid.CANCEL
  - flare.flare:FLR.DataGrid.COLLAPSE_GROUPED_ROWS
  - flare.flare:FLR.DataGrid.EXPAND_GROUPED_ROWS
  - flare.flare:FLR.DataGrid.DROP_TO_GROUP_BY
  - flare.flare:FLR.DataGrid.SELECTED_COUNT_OF_ROW_COUNT_ROWS_SELECTED
  - flare.flare:FLR.DataGrid.UNSORTED
  - flare.flare:FLR.DataGrid.UNPIN_ALL
  - flare.flare:FLR.DataGrid.UNPIN
  - flare.flare:FLR.DataGrid.TOGGLE_VISIBILITY
  - flare.flare:FLR.DataGrid.THEN_BY
  - flare.flare:FLR.DataGrid.SORT_BY_COLUMN_DESC
  - flare.flare:FLR.DataGrid.SHOW_SELECTED
  - flare.flare:FLR.DataGrid.SHOW_SEARCH
  - flare.flare:FLR.DataGrid.SHOW_FILTERS
  - flare.flare:FLR.DataGrid.SHOW_ALL_COLUMNS
  - flare.flare:FLR.DataGrid.SHOW_ALL
  - flare.flare:FLR.DataGrid.SELECT_ALL
  - flare.flare:FLR.DataGrid.SELECT
  - flare.flare:FLR.DataGrid.SEARCH
  - flare.flare:FLR.DataGrid.SAVE_VIEW
  - flare.flare:FLR.DataGrid.SAVE
  - flare.flare:FLR.DataGrid.ROWS_PER_PAGE
  - flare.flare:FLR.DataGrid.ROW_SPACING_EXPANDED
  - flare.flare:FLR.DataGrid.ROW_SPACING_DEFAULT
  - flare.flare:FLR.DataGrid.ROW_SPACING_CONDENSED
  - flare.flare:FLR.DataGrid.ROW_NUMBERS
  - flare.flare:FLR.DataGrid.ROW_NUMBER
  - flare.flare:FLR.DataGrid.ROW_ACTIONS
  - flare.flare:FLR.DataGrid.RESET_TO_DEFAULT
  - flare.flare:FLR.DataGrid.RESET_ORDER
  - flare.flare:FLR.DataGrid.RESET_COLUMN_SIZE
  - flare.flare:FLR.DataGrid.PREVIOUS_PAGE
  - flare.flare:FLR.DataGrid.PIN_TO_RIGHT
  - flare.flare:FLR.DataGrid.PIN_TO_LEFT
  - flare.flare:FLR.DataGrid.OR
  - flare.flare:FLR.DataGrid.OF
  - flare.flare:FLR.DataGrid.NO_RESULTS_FOUND
  - flare.flare:FLR.DataGrid.NO_RECORDS_TO_DISPLAY
  - flare.flare:FLR.DataGrid.NEXT_PAGE
  - flare.flare:FLR.DataGrid.MOVE
  - flare.flare:FLR.DataGrid.MIN
  - flare.flare:FLR.DataGrid.MAX
  - flare.flare:FLR.DataGrid.MANAGE_COLUMNS
  - flare.flare:FLR.DataGrid.LAST_PAGE
  - flare.flare:FLR.DataGrid.HIDE_SEARCH
  - flare.flare:FLR.DataGrid.HIDE_FILTERS
  - flare.flare:FLR.DataGrid.HIDE_ALL
  - flare.flare:FLR.DataGrid.GROUPED_BY
  - flare.flare:FLR.DataGrid.GRAB
  - flare.flare:FLR.DataGrid.FULL_SCREEN
  - flare.flare:FLR.DataGrid.FIRST_PAGE
  - flare.flare:FLR.DataGrid.FILTERING_BY_COLUMN
  - flare.flare:FLR.DataGrid.CHANGE_FILTER_MODE
  - flare.flare:FLR.DataGrid.CHANGE_SEARCH_MODE
  - flare.flare:FLR.DataGrid.CLEAR_FILTER
  - flare.flare:FLR.DataGrid.CLEAR_SEARCH
  - flare.flare:FLR.DataGrid.CLEAR_SORT
  - flare.flare:FLR.DataGrid.CLICK_TO_COPY
  - flare.flare:FLR.DataGrid.COLLAPSE
  - flare.flare:FLR.DataGrid.COLLAPSE_ALL
  - flare.flare:FLR.DataGrid.COLUMN_ACTIONS
  - flare.flare:FLR.DataGrid.COLUMN_RESIZE
  - flare.flare:FLR.DataGrid.COPIED_TO_CLIPBOARD
  - flare.flare:FLR.DataGrid.DESELECT
  - flare.flare:FLR.DataGrid.DESELECT_ALL
  - flare.flare:FLR.DataGrid.EDIT
  - flare.flare:FLR.DataGrid.EXIT_FULL_SCREEN
  - flare.flare:FLR.DataGrid.EXPAND
  - flare.flare:FLR.DataGrid.EXPAND_ALL
  - flare.flare:FLR.DataGrid.FILTER_ARR_INCLUDES
  - flare.flare:FLR.DataGrid.FILTER_ARR_INCLUDES_ALL
  - flare.flare:FLR.DataGrid.FILTER_ARR_INCLUDES_SOME
  - flare.flare:FLR.DataGrid.FILTER_BETWEEN
  - flare.flare:FLR.DataGrid.FILTER_BETWEEN_INCLUSIVE
  - flare.flare:FLR.DataGrid.FILTER_CONTAINS
  - flare.flare:FLR.DataGrid.FILTER_EMPTY
  - flare.flare:FLR.DataGrid.FILTER_ENDS_WITH
  - flare.flare:FLR.DataGrid.FILTER_EQUALS
  - flare.flare:FLR.DataGrid.FILTER_EQUALS_STRING
  - flare.flare:FLR.DataGrid.FILTER_FUZZY
  - flare.flare:FLR.DataGrid.FILTER_GREATER_THAN
  - flare.flare:FLR.DataGrid.FILTER_GREATER_THAN_OR_EQUAL_TO
  - flare.flare:FLR.DataGrid.FILTER_IN_NUMBER_RANGE
  - flare.flare:FLR.DataGrid.FILTER_INCLUDES_STRING
  - flare.flare:FLR.DataGrid.FILTER_INCLUDES_STRING_SENSITIVE
  - flare.flare:FLR.DataGrid.FILTER_LESS_THAN
  - flare.flare:FLR.DataGrid.FILTER_LESS_THAN_OR_EQUAL_TO
  - flare.flare:FLR.DataGrid.FILTER_MODE
  - flare.flare:FLR.DataGrid.FILTER_NOT_EMPTY
  - flare.flare:FLR.DataGrid.FILTER_NOT_EQUALS
  - flare.flare:FLR.DataGrid.FILTER_STARTS_WITH
  - flare.flare:FLR.DataGrid.FILTER_WEAK_EQUALS
  - flare.flare:FLR.DataGrid.GLOBAL_FILTER
  - flare.flare:FLR.DataGrid.TOGGLE_SELECT_ROW
  - flare.flare:FLR.DataGrid.GLOBAL_FILTER_RESULTS
  - flare.flare:FLR.DataGrid.SORTED_BY_COLUMN_DESC
  - flare.flare:FLR.DataGrid.SORTED_BY_COLUMN_ASC
  - flare.flare:FLR.DataGrid.FILTER_BY_COLUMN
  - flare.flare:FLR.DataGrid.SORT_BY_COLUMN_ASC
  - flare.flare:FLR.DataGrid.HIDE_COLUMN
  - flare.flare:FLR.DataGrid.UNGROUP_BY_COLUMN
  - flare.flare:FLR.DataGrid.GROUP_BY_COLUMN
  # Pagination
  - flare.flare:FLR.AdvancePagination.PAGINATION_INFO
