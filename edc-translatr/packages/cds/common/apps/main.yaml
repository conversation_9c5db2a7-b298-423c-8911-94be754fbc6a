app: common.main
labelKeys:
  - common.common:AllUsers
  - common.common:PeopleFollowingMe
  - common.common:PeopleIAmFollowing
  - common.common:Unspecified
  - common.common:SelectContentType
  - common.common:More
  - common.common:RemoveName
  - common.common:FromList
  - common.common:SearchHere
  - common.common:CollaboratorsMayNotHaveAccessToPrivateCards
  - common.common:Message
  - common.common:Unfollow
  - common.common:Following
  - common.common:Followers
  - common.common:SmartCard
  - common.common:Follow
  - common.common:Clear
  - common.common:Close
  - common.common:Days
  - common.common:NoResultsFound
  - common.common:TypeSomething
  - common.common:ProxySettings
  - common.common:SelectModuleThatTheProxyCanAccess
  - common.common:PleaseSelectAnOptionFromBelow
  - common.common:AddMemberWhoRequiresProxy
  - common.common:SelectProxy
  - common.common:Loading
  - common.common:SearchAndAdd
  - common.common:StartDate
  - common.common:EndDate
  - common.common:ViewMore
  - common.common:Cancel
  - common.common:Designate
  - common.common:Impersonate
  - common.common:Upcoming
  - common.common:Expired
  - common.common:SelectAnyUserFromTheOrgForReadOnlyImpersonation
  - common.common:Search
  - common.common:NoProxiesFound
  - common.common:YouCannotAddYourselfAsProxyUser
  - common.common:ProxyEditSettings
  - common.common:Impersonator
  - common.common:Impersonatee
  - common.common:ProxyType
  - common.common:Save
  - common.common:CreateASmartcard
  - common.common:CreateA
  - common.common:UpdateSmartcard
  - common.common:ThisIsAnExclusiveContentAndCannotBeMadePublic
  - common.common:YourPrivateCardHasBeenCreated
  - common.common:PsThisSmartcardWillNotBeVisibleToAnyoneButTheAuthor
  - common.common:YourCardHasBeenPublishedPubliclyAndWillBeAccessibleToEveryone
  - common.common:SelectPassingScoreGrade
  - common.common:SorryTheContentYouAreTryingToPostIsFromUnapprovedWebsiteOrWords
  - common.common:Free
  - common.common:Paid
  - common.common:Premium
  - common.common:Subscription
  - common.common:Private
  - common.common:Public
  - common.common:SmartcardUpdatedSuccessfully
  - common.common:History
  - common.common:ShowPreviousVersions
  - common.common:HidePreviousVersions
  - common.common:DigitAlphaNumeric
  - common.common:Social
  - common.common:TypeHereToSearchAndSelect
  - common.common:PrivacySettings
  - common.common:MaximumOf15Collaborators
  - common.common:TypeCollaborators
  - common.common:Pricing
  - common.common:PaidContent
  - common.common:SpecifyPrice
  - common.common:Add
  - common.common:Provider
  - common.common:Pathway
  - common.common:Options
  - common.common:Skills
  - common.common:Language
  - common.common:ContentType
  - common.common:Level
  - common.common:FieldIsRequired
  - common.common:Duration
  - common.common:Optional
  - common.common:SelectTheDurationOfTheSmartcard
  - common.common:Hours
  - common.common:Mins
  - common.common:DurationIsRequired
  - common.common:OptionalField
  - common.common:RequiredField
  - common.common:Details
  - common.common:CreateCard
  - common.common:UpdateCard
  - common.common:Saving
  - common.common:Title
  - common.common:TheFollowingWillBeUsedToIdentifyYourSmartcard
  - common.common:PleaseProvideTheContentsForYourSmartcard
  - common.common:EnterSmartcardTitleHere
  - common.common:UploadScormFile
  - common.common:UploadThumbnail
  - common.common:NoPassingCriteria
  - common.common:EnterTitle
  - common.common:TitleFirstQuestionInfo
  - common.common:QuizQuestion
  - common.common:EnterQuestionForTheQuiz
  - common.common:Question
  - common.common:DeleteQuestion
  - common.common:QuizOptions
  - common.common:SelectCorrectOption
  - common.common:SelectOneOrMoreCorrectOptions
  - common.common:Option
  - common.common:DeleteOption
  - common.common:AddOption
  - common.common:PleaseSelectTheCorrectOption
  - common.common:AddAnotherQuestion
  - common.common:RetakePolicy
  - common.common:AllowTheUserToAnswerThisQuizAgain
  - common.common:MandatoryPolicy
  - common.common:AllQuestionsAreMandatory
  - common.common:PassingCriteria
  - common.common:CanBeUsedToControlAwardingBadgesForPathwayCompletion
  - common.common:UploadFile
  - common.common:PassingScoreGrade
  - common.common:NumberOfReAttemptsAllowed
  - common.common:Eg3
  - common.common:CompletionBehavior
  - common.common:KeepTheCardIncompleteIfUserReceivesAFailingGrade
  - common.common:ShowPassingGradeToLearners
  - common.common:ShowNumberOfReattemptsToTheLearner
  - common.common:PollQuestion
  - common.common:PollOptions
  - common.common:PollOption
  - common.common:RemoveOption
  - common.common:Description
  - common.common:ErrorInUploadingPleaseUploadScormFileAgain
  - common.common:PleaseProvideAnyAdditionalDetails
  - common.common:SelectFilesToUpload
  - common.common:PleaseProvideAnyAdditionalDetailsForThisEvent
  - common.common:ForbiddenContentInTheLinkField
  - common.common:UrlIsNotAValidLink
  - common.common:Link
  - common.common:EnterTheLinkUrlAddressThatYouWouldLikeToPost
  - common.common:CheckForDuplicateCards
  - common.common:LinkTitle
  - common.common:UploadSmartcardImage
  - common.common:SelectDefaultLanguage
  - common.common:UseSameImageForAllLanguageVariants
  - common.common:SelectLanguage
  - common.common:DefaultLanguage
  - common.common:Other
  - common.common:UnableToCreateMeetingLink
  - common.common:PleaseFillInTheNecessaryDetailsToGenerateTheMeeting
  - common.common:LiveEventTitle
  - common.common:ConferencingTool
  - common.common:StartDateAndTime
  - common.common:StartTime
  - common.common:StartDateAndTimeShouldBeGreaterThanCurrentTime
  - common.common:EndDateAndTime
  - common.common:EndTime
  - common.common:EndDatetimeIsBeforeStartDatetime
  - common.common:Timezone
  - common.common:RecordingUrlIsRequired
  - common.common:GenerateMeeting
  - common.common:RegistrationType
  - common.common:CloseRegistration
  - common.common:Confirm
  - common.common:MeetingDetails
  - common.common:ClickingThisLinkWillOpenTheDetailsInANewTab
  - common.common:MeetingLink
  - common.common:ViewMeetingDetails
  - common.common:Calendar
  - common.common:Name
  - common.common:SearchUsers
  - common.common:NoIndividualsSelectedPleaseSelectAtLeastOneIndividualFirst
  - common.common:ChooseOptions
  - common.common:SelectMonth
  - common.common:SelectYear
  - common.common:ChooseOption
  - common.common:Completed
  - common.common:ValueShouldBeBetweenMinAndMax
  - common.common:MaximumCharacterLimitReached
  - common.common:RemainingtextmaxlenLeft
  - common.common:SelectAll
  - common.common:TypeHereToSearch
  - common.common:TypeHereToSearchMinimum3CharactersAreRequired
  - common.common:MaximumMaxsupportednodesTopicsCanBeSelected
  - common.common:InProgressCard
  - common.common:Journey
  - common.common:ContentDoesNotExist
  - common.common:By
  - common.common:Content
  - common.common:User
  - common.common:Group
  - common.common:Group2
  - common.common:Channels
  - common.common:NoHistory
  - common.common:Recent
  - common.common:ClearHistory
  - common.common:OptionsMatchesFound
  - common.common:EditProfile
  - common.common:NotificationSettings
  - common.common:Help
  - common.common:ManageSubscription
  - common.common:ManagerProxySettings
  - common.common:SignOut
  - common.common:SwitchAccount
  - common.common:Notifications
  - common.common:ViewAllNotifications
  - common.common:DueDate
  - common.common:ThereAreNoNewNotifications
  - common.common:SkillsGraph
  - common.common:SkillsDirectory
  - common.common:Edgraph
  - common.common:AnalyticsPlus
  - common.common:Reports
  - common.common:ManagerDashboard
  - common.common:Admin
  - common.common:CourseEvents
  - common.common:LmsAdmin
  - common.common:SelectALanguage
  - common.common:CareerPathing
  - common.common:SkipToContent
  - common.common:UseBelowLinksToSwitchToOtherAccountYouAreMemberOf
  - common.common:GoTo
  - common.common:YourAccessToNameHasBeenSuspended
  - common.common:Accessibility
  - common.common:ThisGroupIsPrivateYouShouldAcceptInvitationToOpenGroupDetailPage
  - common.common:ComingSoon
  - common.common:Follower
  - common.common:LeaveGroup
  - common.common:Joined
  - common.common:JoinGroup
  - common.common:Members
  - common.common:Member
  - common.common:Decline
  - common.common:DeclineTitle
  - common.common:PoweredByEdcast
  - common.common:PrivacyPolicy
  - common.common:TermsOfService
  - common.common:AppStore
  - common.common:PlayStore
  - common.common:ChromeExetension
  - common.common:Apply
  - common.common:UploadNewFile
  - common.common:PdfFilePreview
  - common.common:Month
  - common.common:Year
  - common.common:Present
  - common.common:Done
  - common.common:Reset
  - common.common:DueDateForCoursetitle
  - common.common:ReturnToChannelPage
  - common.common:GoToChannelSettings
  - common.common:Congratulations
  - common.common:PictureOfAuthorname
  - common.common:Unfollowing
  - common.common:Channel
  - common.common:FollowingTitle
  - common.common:CarouselControlsScrollLeft
  - common.common:Carousel
  - common.common:CarouselControlsScrollRight
  - common.common:Field
  - common.common:OriginalValue
  - common.common:Or
  - common.common:NewValue
  - common.common:UserMadeChanges
  - common.common:PictureOfName
  - common.common:CloseTab
  - common.common:CollaboratorsCanHelpYouMakeChangesToTheType
  - common.common:AddCollaboratorToType
  - common.common:ValueShouldBeBetweenMinAndMax
  - common.common:JoinGroupTitle
  - common.common:ButtonlabelTitle
  - common.common:UnfollowingTitle
  - common.common:FollowTitle
  - common.common:FollowingTitle
  - common.common:ComingSoonTitle
  - common.common:ClickableTagsChips
  - common.common:CompanyName
  - common.common:CurrentlyWorkingHere
  - common.common:DeleteTheWorkHistory
  - common.common:EditTheWorkHistory
  - common.common:EnterLinkAddressHere
  - common.common:EnterMeetingDetails
  - common.common:EnterProviderNameHere
  - common.common:EnterRecordingDetails
  - common.common:EnterSmartcardDescriptionHere
  - common.common:EnterSmartcardTitleHere2
  - common.common:EventImage
  - common.common:FilterTags
  - common.common:Filter
  - common.common:Icons
  - common.common:Industry
  - common.common:Links
  - common.common:Location
  - common.common:Next
  - common.common:PreviewImage
  - common.common:Previous
  - common.common:ProviderLogo
  - common.common:RadioButtons
  - common.common:RecordingDetails
  - common.common:RestrictTo
  - common.common:SelectDurationMinutes
  - common.common:Selected
  - common.common:SelectTagsDefault
  - common.common:SelectTagsSingle
  - common.common:Share
  - common.common:Skillcoins
  - common.common:StartTyping
  - common.common:Tags
  - common.common:ThisIsEdcPrimaryHttpsLink
  - common.common:ThisIsEdcPrimaryTextLink
  - common.common:ThisIsEdcSecondaryHttpsLink
  - common.common:ThisIsEdcSecondaryTextLink
  - common.common:ThisIsEdcSupportingHttpsLink
  - common.common:ThisIsEdcSupportingTextLink
  - common.common:ThisIsNormalHttpsLink
  - common.common:UserImage
  - common.common:YouCanNotCreateASmartcardWithTheAboveMentionedUrl
  - common.common:IfTheCardIsPostedToACuratedChannel
  - common.common:ChangesMadeUsingTheSourcePluginWillBeSavedOnly
  - common.common:LeaveThisBlankForUnlimitedReAttempts
  - common.common:AllUploadedFilesHaveErrors
  - common.common:NoOptions
  - common.common:PleaseCreateANewSmartcardToUploadCorrectFiles
  - common.common:TypeOfContentDoesNotMatchWithThePreviouslyUploadedType
  - common.common:ContentStudio
  - common.common:ContentsOfThisFieldWillOverrideTheDefaultDescriptionTextPresentInScormFile
  - common.common:SetLanguage
  - common.common:MaxUserTypeLimitSelectionExceed
  - common.common:TagsPlaceholderForLargeScreen
  - common.common:TagsPlaceholderForSmallScreen
  - common.common:Users
  - common.common:Groups
  - common.common:SortLabel
  - common.common:PrivacyTooltipPrivate
  - common.common:PrivacyTooltipPublic
  - common.common:PrivacyTooltipPrivateReDesign
  - common.common:PrivacyTooltipPublicReDesign
  - common.common:MaximumUsersMsg
  - common.common:TagsRequired
  - common.common:MaximumLimitExceeded
  - common.common:EnterValidUrl
  - common.common:PaidContentEmptyMsg
  - common.common:AmountShouldBeGreater
  - common.common:AmountShouldBeLess
  - common.common:ClickAddButton
  - common.common:MaximumCharacterLimit
  - common.common:WebURL
  - common.common:UploadedContent
  - common.common:Poll
  - common.common:Quiz
  - common.common:TextCard
  - common.common:SCORMFile
  - common.common:Project
  - common.common:LiveEvent
  - common.common:LiveStream
  - common.common:CardRedesignHeading
  - common.common:CardRedesignSmartCard
  - common.common:CardRedesignPathway
  - common.common:CardRedesignJourney
  - common.common:CardRedesignGroup
  - common.common:CardRedesignChannel
  - common.common:CardRedesignPathway
  - common.common:Eih
  - common.common:ManageBlockUsers
  - common.common:TalentSourcing
  - common.common:Beta
  - common.common:LearnNewSkillsByTakingUpAProject
  - common.common:UseOnlyForThumbnail
  - common.common:InfoUseOnlyForThumbnail
  - common.common:PrivacySettingsRadioDescription
  - common.common:ShareSearchInputDescription
  - common.common:RestrictToSearchInputDescription
  - common.common:SmartCardRemoveCollaboratorSearchInputLabel
  - common.common:SearchOrRemoveUserTotalUserTypeLabel
  - common.common:RemoveUserConfirmationModal
  - common.common:RemoveUserType
  - common.common:ChannelsLowercaseWithApostrophe
  - common.common:SmartCardsWithApostrophe
  - common.common:Collaborator
  - common.common:Collaborators
  - common.common:AddCollaborators
  - common.common:EnterProviderNameDescription
  - common.common:ProviderName
  - common.common:FileUploadProviderLogoDescription
  - common.common:FileUploadButtonDisabledMessage
  - common.common:SelectLevel
  - common.common:NoLevel
  - common.common:TooltipForTags
  - common.common:TooltipForDuration
  - common.common:PercentageCompleted
  - common.common:WorkHistoryDescription
  - common.common:SearchWithSuggestionsEnabled
  - common.common:SearchWithSuggestionsDisabled
  - common.common:AltTextUploadDesc
  - common.common:AltTextUploadTitle
  - common.common:FileNameIsTooLong
  - common.common:SkillsStudio
  - common.common:UserProvidedImage
  - common.common:ImageAutoGenerated
  - common.common:People
  - common.common:Leaderboard
  - common.common:Bookmarks
  - common.common:AwardSkills
  - common.common:EnablingThisOptionWillAwardUsersWithAssociatedSkills
  - common.common:LessonStatus
  - common.common:CompletionStatus
  - common.common:Score
  - common.common:Passed
  - common.common:Failed
  - common.common:Incomplete
  - common.common:Browsed
  - common.common:NotAttempted
  - common.common:Reviewed
  - common.common:Unknown
  - common.common:Expand
  - common.common:Collapse
  - common.common:ExpandCollapseLessons
  - common.common:CloseTableOfContents
  - common.common:OpenTableOfContents
  - common.common:Required
  - common.common:AreYouSureYouWantToUnfollowThisChannel
  - common.common:UnfollowChannelWarningContentStart
  - common.common:UnfollowChannelWarningContentEnd
  - common.common:LeaveGroupModalContent
  - common.common:UnfollowChannel
  - common.common:EnterQuestionForThePoll
  - common.common:NonUgcContentDataLostWarningMessage
  - common.common:SelectDays
  - common.common:DueDateIsDynamicallySetLabel
  - common.common:UserSelectedCount
  - common.common:CollaboratorsCanHelpYouMakeChangesToTheSmartcard
  - common.common:ArchiveDateInfo
  - common.common:ArchiveThisContent
  - common.common:ArchiveContent
  - common.common:WeeksLabel
  - common.common:DaysLabel
  - common.common:YearsLabel
  - common.common:Mandatory
  - common.common:ConfigureHomePage
  - common.common:Warning
  - common.common:Error
  - common.common:Success
  - common.common:Neutral
  - common.common:Jan
  - common.common:Feb
  - common.common:Mar
  - common.common:Apr
  - common.common:May
  - common.common:Jun
  - common.common:Jul
  - common.common:Aug
  - common.common:Sep
  - common.common:Oct
  - common.common:Nov
  - common.common:Dec
  - common.common:WorkHistoryDescriptionErrorMessage
  - common.common:WorkHistoryIndustryErrorMessage
  - common.common:WorkHistoryLocationErrorMessage
  - common.common:WorkHistoryTitleErrorMessage
  - common.common:WorkHistoryCustomCompanyNameErrorMessage
  - common.common:WorkHistorySelectedEndDateErrorMessage
  - common.common:WorkHistorySelectedStartDateErrorMessage
  - common.common:ViewLess
  - common.common:LinkIsRequired
  - common.common:FileIsRequired
  - common.common:SpecifyWhoHasTheAbilityToMarkAnEventAsCompleted
  - common.common:ClarifyWhoHasTheAbilityToMarkAnEventAsCompleted
  - common.common:MemberAndOrganizor
  - common.common:OnlyOrganizer
  - common.common:AltTextUploadDescription
  - common.common:CalendarIsExpanded
  - common.common:CalendarIsCollapsed
  - common.common:Guide
  - common.common:WeHaveRemovedTheCustomCodeEnteredAsItWasAlreadyBeingUsedPleaseEditTheContentAndReEnterANewCode
  - common.common:SupportedFile
  - common.common:AutoGenerate
  - common.common:MonthsAgo
  - common.common:MonthAgo
  - common.common:DaysAgo
  - common.common:DayAgo
  - common.common:WeekAgo
  - common.common:WeeksAgo
  - common.common:FewSecondsAgo
  - common.common:HoursAgo
  - common.common:HourAgo
  - common.common:YearsAgo
  - common.common:YearAgo
  - common.common:MinutesAgo
  - common.common:A
  - common.common:B
  - common.common:C
  - common.common:D
  - common.common:E
  - common.common:F
  - common.common:TooltipForPassingScoreGrade
  - common.common:ViewXMore
  - common.common:GoToExternalLink
  - common.common:SelectedStartDateIs
  - common.common:SelectedEndDateIs
  - common.common:SelectedDateIs
  - common.common:Pagination
  - common.common:DeleteTheWorkHistoryWithName
  - common.common:EditTheWorkHistoryWithName
  - common.common:MoreCardActions
  - common.common:ValueMoreInfo
  - common.common:ExcludeContent
  - common.common:ExcludeFromSearch
  - common.common:ExcludeFromRecommendations
  - common.common:ExcludeContentHint
  - common.common:OptionIsRequired
  - common.common:OpensInNewTab
  - common.common:PageIndexLabel
  - common.common:NextPageLabel
  - common.common:PreviousPage
  - common.common:RequiredFieldAriaLabel
  - common.common:LevelIsUnsupported
  - common.common:warningAreaAlert
  - common.common:errorAreaAlert
  - common.common:successAreaAlert
  - common.common:infoAreaAlert
  - common.common:SelectedSourcesAddedAboveFormField
  - common.common:SelectedTypesAddedAboveFormField
  - common.common:SelectedLanguagesAddedAboveFormField
  - common.common:PasswordIsRequired
  - common.common:PasswordProtectedDocument
  - common.common:IncorrectPassword
  - common.common:EnterPasswordToAccess
  - common.common:ViewingCancelled
