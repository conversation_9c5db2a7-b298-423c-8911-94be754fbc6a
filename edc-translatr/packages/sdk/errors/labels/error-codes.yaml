prefix: errors.error-codes
labels:
  E000000:
    value: "{class_name}"
    context: "General class-related error"
  E000001:
    value: "{attribute} can't be blank"
    context: "Presence validation error when a field is blank"
  E000002:
    value: "{attribute} must be blank"
    context: "Absence validation error when a field is present but should be blank"
  E000003:
    value: "{attribute} is too short (minimum is {count} characters)"
    context: "Length validation error when a field is shorter than the minimum allowed length"
  E000004:
    value: "{attribute} is too long (maximum is {count} characters)"
    context: "Length validation error when a field is longer than the maximum allowed length"
  E000005:
    value: "{attribute} is the wrong length (should be {count} characters)"
    context: "Length validation error when a field is not the required length"
  E000006:
    value: "{attribute} has already been taken"
    context: "Uniqueness validation error when a field's value is not unique"
  E000007:
    value: "{attribute} is invalid"
    context: "Format validation error when a field does not match the required format"
  E000008:
    value: "{attribute} is not included in the list"
    context: "Inclusion validation error when a field's value is not included in the allowed list"
  E000009:
    value: "{attribute} is reserved"
    context: "Exclusion validation error when a field's value is in the excluded list"
  E000010:
    value: "{attribute} must exist"
    context: "Validation error when a non-optional association is missing"
  E000011:
    value: "{attribute} is not a number"
    context: "Numericality validation error when a field is not a number"
  E000012:
    value: "{attribute} must be greater than {count}"
    context: "Numericality validation error when a field's value is not greater than the specified number"
  E000013:
    value: "{attribute} must be greater than or equal to {count}"
    context: "Numericality validation error when a field's value is not greater than or equal to the specified number"
  E000014:
    value: "{attribute} must be equal to {count}"
    context: "Numericality validation error when a field's value is not equal to the specified number"
  E000015:
    value: "{attribute} must be less than {count}"
    context: "Numericality validation error when a field's value is not less than the specified number"
  E000016:
    value: "{attribute} must be less than or equal to {count}"
    context: "Numericality validation error when a field's value is not less than or equal to the specified number"
  E000017:
    value: "{attribute} must be other than {count}"
    context: "Numericality validation error when a field's value is not other than the specified number"
  E000018:
    value: "{attribute} must be an integer"
    context: "Numericality validation error when a field's value is not an integer"
  E000019:
    value: "{attribute} is not included in the list"
    context: "Numericality validation error when a field's value is not included in the specified range"
  E000020:
    value: "{attribute} must be odd"
    context: "Numericality validation error when a field's value is not an odd number"
  E000021:
    value: "{attribute} must be even"
    context: "Numericality validation error when a field's value is not an even number"
  E000022:
    value: "Token is invalid"
    context: "Authentication error when a provided token is invalid"
  E000023:
    value: "JWT token has expired"
    context: "Authentication error when a JWT token has expired"
  E000024:
    value: "{attribute} not found"
    context: "Error when a specified attribute is not found"
  E000025:
    value: "Something went wrong"
    context: "General error for unspecified issues"
  E000026:
    value: "Could not perform the action"
    context: "Error when an action could not be performed"
  E000027:
    value: "Required parameter {attribute} is missing"
    context: "Error when a required parameter is missing"
  E000028:
    value: "{attribute} is unavailable"
    context: "Error when a specified attribute is unavailable"
  E000029:
    value: "Should contain characters, numbers, - or _"
    context: "Validation error when input format is incorrect"
  E000030:
    value: "{name} cannot contain the following characters: {unpermitted_name_char}"
    context: "Validation error when input contains unpermitted characters"
  E000031:
    value: "{attribute} accept http and https only"
    context: "Validation error when a URL does not use http or https"
  E000032:
    value: "{attribute} invalid double extension detected"
    context: "Validation error when a file has an invalid double extension"
  E000033:
    value: "Missing required params. Required params are {attribute}"
    context: "Error when required parameters are missing"
  E000034:
    value: "Invalid {attribute}: {message}"
    context: "Error when a attribute is invalid"
  E000035:
    value: "Provide {attribute} {message}"
    context: "Error when a attribute is not provided and a message provides more detail"
  E000036:
    value: "Value is null or empty"
    context: "When the value is null or empty"
  E000037:
    value: "Size must be less than or equal to {default_max_size}, but found {size}"
    context: "Error when size is greater than default max size"
  E000038:
    value: "Max difference between limit and offset is {max_difference}"
    context: "Error when max difference between limit and offset is greater than max difference"
  E000039:
    value: "{attribute} does not exist"
    context: "Error when an attribute does not exist"
  E000040:
    value: "Missing `{attribute}` for {index} array element"
    context: "Error when attribute is missing"
  E000041:
    value: "{attribute} should be greater than or equal to current date"
    context: "Error when a date attribute is not greater than or equal to the current date"
  E000042:
    value: "start_date cannot be empty/missing if due_at is passed"
    context: "Error when start_date is missing while due_at is passed"
  E000043:
    value: "Users limit exceeded, Max: {attribute}"
    context: "Error when users limit is exceeded"
  E000044:
    value: "{attribute} should be an array"
    context: "Error when attribute is not an array"
  E000045:
    value: "Year should be less than or equal to current year"
    context: "Error when Year should be less than or equal to current year"
  E000046:
    value: "Missing {attribute}: {message}"
    context: "Error when a required attribute is missing with a specific message"
  E000047:
    value: "Invalid value for {attribute}"
    context: "Error when the value for an attribute is invalid"
  E000048:
    value: "Missing in request."
    context: "Error when a required attribute is missing in the request"
  E000049:
    value: "Missing or value is empty"
    context: "Error when an attribute is missing or its value is empty"
  E000050:
    value: "completed_at should be less than or equal to current date and time"
    context: "Error when completed_at is not less than or equal to the current date and time"
  E000051:
    value: "Duplicate concurrent request"
    context: "Error when a duplicate request is made"
  E000052:
    value: "Started at date is greater than completion date"
    context: "Error when Started at date is greater than completion date"
  E000053:
    value: "Cannot update"
    context: "Error when attempting to update"
  E000054:
    value: "Duplicate {attribute}"
    context: "Error indicating that a duplicate entry exists for the specified attribute"
  E000055:
    value: "Duplicate currency"
    context: "Error indicating that the currency provided is a duplicate"
  E000056:
    value: "Option missing"
    context: "Error indicating that an expected option is missing"
  E000057:
    value: "{attribute} must be true or false"
    context: "Error indicating that the attribute must be a boolean value"
  E000058:
    value: "{attribute} does not have enough permission"
    context: "Error when a user or action lacks sufficient permission"
  E000059:
    value: "Required param {attribute} is missing/empty in {key} at index {index}"
    context: "Error when a required parameter is either missing or empty"
  E000060:
    value: "{attribute} is missing"
    context: "Error when a required attribute is missing"
  E000061:
    value: "{attribute} already exists"
    context: "Error when {attribute} already exists"
  E000062:
    value: "Cannot set {attribute}"
    context: "Error when {attribute} cannot be set"
  E000063:
    value: "{attribute} is invalid in content_languages at index {index}"
    context: "Error when attribute is invalid for in content_languages at index"
  E000064:
    value: "{attribute} is already present in the {subject}"
    context: "Error when the {attribute} already exists in the {subject}"
  E000065:
    value: "Can not delete the content"
    context: "Error when content can not be deleted"
  E000066:
    value: 'Invalid {attribute}: Valid keys are {keys}'
    context: 'error when attribute is invalid'
  E000067:
    value:   'Invalid {attribute}, Valid values: {keys}'
    context: 'error when attribute is invalid with valid values'
  E000068:
    value:   'Provide sort key to order data asc or desc: Valid key {key}'
    context: 'error when Provide sort key to order data asc or desc: Valid key created_at'
  E000069:
    value:   'Invalid {attribute}: Valid content_types {types}'
    context: 'error when Provide sort key to order data asc or desc: Valid key created_at'
  E000070:
    value:   'Invalid {attribute}: expiration date can not be less than the published at'
    context: 'error when Provide sort key to order data asc or desc: Valid key created_at'
  E000071:
    value:   'Invalid date format for expiration_date/published_at : Valid format is- dd/mm/yyyy hh:mm:ss'
    context: 'error when Provide sort key to order data asc or desc: Valid key created_at'
  E000072:
    value:   'Invalid {attribute}: Valid values are {keys}'
    context: 'error when attribute is invalid and values are provided'
  E000073:
    value:   "Invalid teams found for the provided IDs: {ids}"
    context: 'error when teams with ids are invalid'
  E000074:
    value:   'Invalid {attribute}: Valid format is {format}'
    context: 'error when attribute format is invalid and values are provided'
  E000075:
    value:   'Invalid status: Valid statuses are {statuses}'
    context: 'error when status is invalid and values are provided'
  E000076:
    value:   'Invalid {attribute}. Valid {attribute} are {valid_attributes}'
    context: 'error when attribute is invalid'
  E000077:
    value:   "Invalid {attribute}. Valid values are case sensitive: {valid_values}"
    context: 'error when attribute is invalid with valid values'
  E000078:
    value:   "Invalid {attribute}: Valid Currencies {currency}"
    context: 'error when currency is invalid'
  E000079:
    value:   "Invalid price amount: Amount should be greater than 0"
    context: 'error when price amount attribute is invalid'
  E000080:
    value:   "Invalid {attribute}: date cant be greater than today's date"
    context: 'error when date attribute is invalid'
  E000081:
    value:   "Invalid value: Valid readable_content_type {valid_values}"
    context: 'error when readable_content_type attribute is invalid'
  E000082:
    value:  "Invalid or missing. Valid content_type {valid_values}"
    context: 'error when attribute is invalid or missing'
  E000083:
    value:  "Invalid content_type or content_type is missing : Valid content_type {valid_values}"
    context: 'error when content_type or content_type is missing'
  E000084:
    value:  "Invalid amount: Amount should be non zero number"
    context: 'error when amount attribute is invalid'
  E000085:
    value:  "Invalid date. {param_type}_from_date can't be greater that {param_type}_to_date"
    context: 'error when date attribute is greater than to date'
  E000086:
    value:  "Invalid role for team user"
    context: 'error when role is invalid'
  E000087:
    value:  "Invalid {attribute}, Valid: {valid_values}"
    context: 'error when attribute is invalid'
  E000088:
    value:  "Invalid role type for user"
    context: 'error when role is invalid'
  E000089:
    value:  "Invalid user_identifier_type for user_ids"
    context: 'error when user_identifier_type is invalid'
  E000090:
    value:  "Invalid from_date: should be less than till_date"
    context: 'error when from_date is invalid'
  E000091:
    value:  "Invalid {attribute}: should be less than or equal to current date"
    context: 'error when attribute is invalid'
  E000092:
    value:  "Provide sort key to order data asc or desc: Valid keys are {keys}"
    context: 'error when sort key is not provided'
  E000093:
    value:  "Provide query string to search_by name skills or expertise"
    context: 'error when  query string is not provided'
  E000094:
    value:  "Missing required date range parameter: {param_type}_from_date/{param_type}_to_date"
    context: 'error when required date range parameter is missing'
  E000095:
    value:  "Missing {attribute}"
    context: 'error when attribute is missing'
  E000096:
    value:  "Invalid end date, End date should be greater than start date"
    context: 'error when end date is less than start date'
  E000097:
    value:  "Something went wrong, please try again in sometime"
    context: 'error when something is wrong in creating subscription'
  E000098:
    value:  "{attribute} is blank"
    context: 'error when attribute is blank'
  E000099:
    value: "Required parameter {attribute} is missing in {area}"
    context: "Error when a required parameter is missing in area"
  E000100:
    value: '{attribute} is invalid or blank'
    context: 'error when attribute is invalid or blank'
  E000101:
    value: 'Matching taxonomy topics not found for any given skill'
    context: 'error when matching taxonomy topics not found for any given skill'
  E000102:
    value: '{attribute} is a restricted name. Please try with a different name'
    context: 'error when attribute is restricted to use'
  E000103:
    value: 'Years in experience should be greater than or equal to 1'
    context: 'Error when given year of experience value is less than 1'
  E000104:
    value: 'Months in experience must be between 1 & 11'
    context: 'Error when given number of months are not in range of 1 to 11'
  E000105:
    value: '{attribute} parameter is required'
    context: 'Error when required parameter is missing in request'
  E000106:
    value: 'Data type of a custom field should be either one of {attribute}'
    context: 'Error when given custom field is invalid'
  E000107:
    value: 'Not found'
    context: 'Error when an attribute not found and it is not specified'
  E000108:
    value: "Display name field can't contain invalid characters. only alphanumeric characters and spaces are allowed"
    context: "Error when any character apart from space or alphanumeric character passed in custom field"
  E000109:
    value: '25 characters is the maximum allowed'
    context: 'error when the limit for maximum characters allowed is reached'
  E000110:
    value: 'Invalid {attribute}'
    context: 'error when the attribute is invalid'
  E000111:
     value: "Missing required params: {param_from_date} and {param_to_date}"
     context: "Error when required params are missing"
  E000112:
     value: "Missing required date range parameter: {param_from_date}/{param_to_date}"
     context: "error when required date range parameter is missing"
  E000113:
    value:  "Invalid date. {param_from_date} can't be greater that {param_to_date}"
    context: 'error when date attribute is greater than to date'
  E000114:
    value: "Invalid date format. Valid format is: {valid_date_format}"
    context: 'Error when date format provided is invalid'
  E000115:
    value: "{user_identifier} is not active"
    context: 'Error when user is not active'
  E000116:
    value: "Error while purchasing the course. Please note that you will not be charged for it."
    context: "Error when there is an issue while purchasing the course."
  E000117:
    value: "Please provide valid {attribute}"
    context: "Error when provided number is non-positive"
  E000118:
    value: "Invalid date format for {attribute}. Valid format is: DD-MM-YYYY"
    context: "Error when provided date is not in format of DD-MM-YYYY"
  E000119:
    value: "issue_date can't be greater than expiry_date"
    context: "Error when issue date is greater than expiry date"
  E000120:
    value: "Required params {attribute} missing"
    context: "Error when specified attribute is missing"
  E000121:
    value: "Invalid {attribute}. {attribute} should be less than or equal {param}"
    context: "Error when specified attribute is greater than given value"
  E000122:
    value: "Please provide {attribute} as positive number"
    context: "Error when provided number is non-positive"
  E000123:
    value: "Please provide a valid filter for {attribute}"
    context: "Error when provided attribute is invalid"
  E000124:
    value: "param is missing or the value is empty: {attribute}"
    context: "Error when attribute is missing or empty"
  E000125:
    value: "Notifications with {id_list} Not Found for given user"
    context: "Error when notifications with given id list not found for given user"
  E000126:
    value: "Error while {action_performed}{full_error}"
    context: "Error occuring when the specified action is performed with full errror message included"
  E000127:
    value: "Required params {attribute} is missing."
    context: "Error when specified params are missing"
  E000128:
    value: "Invalid {attribute}. {message}"
    context: "Error occuring when the specified attribute is invalid with message included"
  E000129:
    value: "Invalid {attribute1}, valid {attribute2}: {valid_values}"
    context: "Error when state is invalid"
  E000130:
    value: "Invalid {attribute}, {message}"
    context: "Error when the specified attribute is invalid with message included"
  E000131:
    value: "Invalid {attribute} update from {from} to {to}"
    context: "Error when the specified status update is invalid"
  E000132: 
    value: "Invalid {attribute}. Valid format: {format}"
    context: "Error when the specified attribute is invalid with message included"
  E000133:
    value: "{attribute} is not valid, Valid: {valid_values}"
    context: "Error when the specified attribute is invalid and valid values are provided"
  E000134:
    value: "{attribute} is not valid"
    context: "Error when the specified attribute is invalid"
  E000135:
    value: "Bad {param} parameter. Use one of {valid_params}"
    context: "Error when the specified param is invalid and valid params are provided"
  E000136: 
    value: "Invalid {attribute}. Valid values: {keys}"
    context: "Error when the specified attribute value is not one of the valid values"
  E000137:
    value: "Given {input_attribute} is not valid {attribute}"
    context: "Given input is not a valid attribute value"
  E000138:
    value: "Not a valid {attribute}"
    context: "Error when the given attribute value is not valid"
  E000139:
    value: "{attribute} not valid, Valid: {valid_format}"
    context: "Error when the specified attribute format is invalid and valid format is provided"
  E100001:
    value: "CURRENT-USER header not set. Should be one of these format ID-123, <EMAIL> or EXT-13jher314"
    context: "Error when the CURRENT-USER header is not set correctly"
  E100002:
    value: "User already suspended"
    context: "Error when a user is already suspended"
  E100003:
    value: "User already active"
    context: "Error when a user is already active"
  E100004:
    value: "Users not allowed to edit manager"
    context: "Error when users attempt to edit their manager"
  E100005:
    value: "User has no manager to unassign"
    context: "Error when there is no manager to unassign"
  E100006:
    value: "Existing email for the user"
    context: "Error when a user tries to use an existing email"
  E100007:
    value: "Invalid {attribute} for {param}"
    context: "Error when an invalid attribute is passed for a parameter"
  E100008:
    value: "You have exceeded the maximum bulk users limit"
    context: "Error when more users are added than the limit"
  E100009:
    value: "An account already exists with this email id"
    context: "Error when an account with the same Email ID is present"
  E100010:
    value: "There are no active clc present"
    context: "Error when no active CLC is present"
  E100011:
    value: "User already deleted"
    context: "Error when a user is already deleted"
  E100012:
    value: "User with {attribute} do not exist"
    context: "Error when a user with the given attribute does not exist"
  E100013:
    value: "Cannot destroy admin role."
    context: "Error when attempting to destroy an admin role"
  E100014:
    value: "The {attribute} role is not assigned to the user"
    context: "Error when a role is not assigned to a user"
  E100015:
    value: "Manager Already Added"
    context: "Error when a manager is already added"
  E100016:
    value: "Manager status is suspended"
    context: "Error when a manager's status is suspended"
  E100017:
    value: "Reporter status is suspended"
    context: "Error when a reporter's status is suspended"
  E100018:
    value: "Cannot add yourself as reportee"
    context: "Error when a user tries to add themselves as a reportee"
  E100019:
    value: "User {user_id} not found"
    context: "Error when user not found with passed user_id (can be id, email etc) parameter in API"
  E100020:
    value: "User is not a part of the team"
    context: "Error when a user is not a part of the team"
  E100021:
    value: "{attribute} is already {state}"
    context: "Error when the given attribute is already in the given state"
  E100022:
    value: "learning_topics are missing"
    context: "Error indicating that learning topics are not provided"
  E100023:
    value: "topic_id or topic_label is missing in learning interest details"
    context: "Error indicating that required fields are missing in learning interest details"
  E100024:
    value: "Learning interest already added."
    context: "Error indicating that the learning interest has already been added"
  E100025:
    value: "Unable to add {attribute}"
    context: "Error indicating a failure to add a learning interest"
  E100026:
    value: "Number of learning interests for the user is reached. At max {limit} can be added"
    context: "Error indicating that the maximum number of learning interests has been reached"
  E100027:
    value: "Maximum {attribute} learning interests are allowed for processing."
    context: "Error when more than maximum amount of learning interests are added for processing."
  E100028:
    value: "Invalid. {attribute} is disabled for preferences."
    context: "Error indicating that the specified attribute is disabled for preferences"
  E100029:
    value: "Invalid {attribute}. Locations are disabled for {context} context."
    context: "Error indicating that locations are disabled for the specified context"
  E100030:
    value: "Invalid. All organization types are disabled for {context} context."
    context: "Error indicating that all organization types are disabled for the specified context"
  E100031:
    value: "Suspended user creation not allowed"
    context: "Error when a user being created is suspended"
  E100032:
    value: "Reportee and Manager cannot have the same Email ID/External ID."
    context: "Error when a user tries to add themselves as a manager"
  E100033:
    value: 'This skill has already been added to your Skills Passport'
    context: 'Error when user is trying to add existing skill'
  E100034:
    value: "Unable to remove {attribute}."
    context: "Error when there is failure in removing the specified attribute."
  E100035:
    value: "Error while fetching {attribute}"
    context: "Error when specified attribute cannot be fetched"
  E100036:
    value: "You are not authorized to {action}"
    context: "Error when the user is not authorised to perform the given action"
  E100037:
    value: "Pricing details cannot be found, please contact admin for further details."
    context: "Error when pricing details cannot be found for content"
  E100038:
    value: "Payment has already been initiated/confirmed."
    context: "Error during duplicate orders when the payment has already been initiated/confirmed."
  E100039:
    value: "Verified credential cannot be deleted"
    context: "Error when specified attribute is not deletable"
  E100040:
    value: "Exceeded maximum limit for {attribute} (max limit: {param})"
    context: "Error when limit exceeded for given attribute"
  E100041:
    value: "{attribute} are missing"
    context: "Error when topic_ids are missing"
  E100042:
    value: "You don't have permissions to filter leaderboard information"
    context: "Error when user does not have permissions to filter leaderboard information"
  E100043:
    value: "User is not part of any groups"
    context: "Error when user is not part of any groups"
  E100044:
    value: "No reporters found for this manager"
    context: "Error when there are no reporters found for the manager"
  E100045:
    value: "You can not delete standard role"
    context: "Error when a standard role can not be deleted"
  E120001:
    value: "Only public channel can be made open"
    context: "Error when attempting to make a non-public channel open"
  E120002:
    value: "Description cannot be above 2500 characters."
    context: "Error when a description exceeds the character limit"
  E120003:
    value: "User and Channel are not in same organization"
    context: "Error when a user and channel are not in the same organization"
  E120010:
    value: "You can't unfollow this channel because you are a member of the group that is following this channel"
    context: "Error when a user cannot unfollow a channel because they are a member of a group following it"
  E120011:
    value: "Channel not followed"
    context: "Error when a channel is not followed"
  E120012:
    value: "Channel has already been followed"
    context: "Error when channel has already been followed by the user"
  E120013:
    value: "Maximum {count} user_ids are allowed."
    context: "Error when API requests has more than expected number of user ids."
  E120014:
    value: "Collaborators {ids} are not removed."
    context: "Error when removing collaborators from a channel fails."
  E120015:
    value: "Curators {ids} are not removed."
    context: "Error when removing curators from a channel fails."
  E120016:
    value: "No valid channels to follow for the team"
    context: "Error when there are no valid channels to follow for the team"
  E120017:
    value: "Provided params contains private channel_ids {attribute}. Only organization admin can follow private channels."
    context: "Error when trying to follow private channels without being an organization admin"
  E130001:
    value: "Only private and dynamic team can be mandatory"
    context: "Error when a team that is not private or dynamic is marked mandatory"
  E130002:
    value: "Group with the same name already exists. Please use a different group name to continue."
    context: "Error when a group with the same name already exists"
  E130003:
    value: "Unable to {attribute} team"
    context: "Error when a team cannot be created/updated"
  E130004:
    value: "You are not authorized to delete the team"
    context: "When you are not authorized to delete team"
  E130005:
    value: "Error while destroying team"
    context: "Error while destroying team"
  E130006:
    value: "User is already part of 5000 Groups"
    context: "Error when User is already part of 5000 Groups"
  E130007:
    value: "not authorized to assign to user with id {attribute}"
    context: "When you are not authorized to assign to user"
  E130008:
    value: "You are not authorized to assign the content"
    context: "When you are not authorized to  assign the content"
  E130009:
    value: "You are not authorized to add or remove member from the team {attribute}"
    context: "When you are not authorized to add or remove member from the team"
  E130010:
    value: "You are not authorized to delete the team"
    context: "When you are not authorized to delete team"
  E130011:
    value: "You are not authorized to cast this vote."
    context: "When you are not authorized to cast vote"
  E140000:
    value: "General ECL-related error {error}"
    context: "General error related to ECL processing"
  E140001:
    value: "Assignment is not created"
    context: "Error when an assignment is not successfully created"
  E140002:
    value: "Assignment does not exist or already completed"
    context: "Error when an assignment either does not exist or is already completed"
  E140003:
    value: "Can not search {attribute} with other content types"
    context: "Error indicating that the attribute cannot be searched with other content types"
  E140004:
    value: "Multiple languages are not allowed for authored content"
    context: "Error indicating that multiple languages cannot be used for authored content"
  E140005:
    value: "resource can't be blank unless content_type is text, pack or journey"
    context: "Error indicating that resource cannot be blank under certain content types"
  E140006:
    value: "CPE Credits should be non-negative integer value"
    context: "Error indicating that CPE credits must be a non-negative integer"
  E140007:
    value: "Cannot be free when prices are present"
    context: "Error indicating that an item cannot be free if prices exist"
  E140008:
    value: "Cannot be paid when prices are empty"
    context: "Error indicating that an item cannot be marked as paid if there are no prices"
  E140009:
    value: "Cannot be made free, as prices exists. Please send prices as empty array to make plan free"
    context: "Error indicating that an item cannot be made free due to existing prices"
  E140010:
    value: "Cannot be made paid, as prices do not exist. Please add prices to make plan paid"
    context: "Error indicating that an item cannot be made paid due to missing prices"
  E140011:
    value: "At least one question should be present."
    context: "Error indicating that at least one question is required"
  E140012:
    value: "Question options or question text missing."
    context: "Error indicating that question options or text are missing"
  E140013:
    value: "Failed to create content. Please try again later."
    context: "Error indicating a failure to create content"
  E140014:
    value: "Failed to create content"
    context: "General error indicating content creation failure"
  E140015:
    value: "Status should present"
    context: "Error indicating that a status must be provided"
  E140016:
    value: "existing_card must be a poll"
    context: "Error when existing_card is not a poll"
  E140017:
    value: "Passing Criteria should be less than number of questions"
    context: "Error when the passing criteria is set higher than the total number of questions"
  E140018:
    value: "Edit functionality is disabled to maintain integrity of the quiz"
    context: "Error when edit functionality is disabled to preserve quiz integrity"
  E140019:
    value: "Inconsistent languages for question at index {attribute}"
    context: "Error when a question has inconsistent language settings"
  E140020:
    value: "Card to update is not specified"
    context: "Error when no card is specified for update"
  E140021:
    value: "Only new polls are supported for updating using this service"
    context: "Error when attempting to update an old poll using this service"
  E140022:
    value: "Cards::PollCardCreationService - At least one question should be present to create poll"
    context: "Error when no questions are present while trying to create a poll"
  E140023:
    value: "At least one mandatory question is required"
    context: "Error when there is no mandatory question in a poll"
  E140024:
    value: "At least two options are required per each question"
    context: "Error when fewer than two options are provided for a question"
  E140025:
    value: "Cards::PollCardCreationService - Question options missing"
    context: "Error when question options are missing in a poll"
  E140026:
    value: "Languages mismatch between {base_language} and {language}"
    context: "Error when there is a mismatch between languages in the poll"
  E140027:
    value: "Unable to update structure of answered poll - {attribute} changed"
    context: "Error when updating a specific structure of poll where attibute changed"
  E140028:
    value: "At least one correct option should be present"
    context: "Error when there is no correct option provided for a question"
  E140029:
    value: "Limit exceeded. The max limit is {attribute}"
    context: "Error when a specified limit is exceeded"
  E140030:
    value: "Cards::QuizCardCreationService - quiz params is missing"
    context: "Error when quiz parameters are missing in Cards::QuizCardCreationService"
  E140031:
    value: "Cards::QuizCardCreationService - At least one question should be present to create quiz."
    context: "Error when there are no questions provided to create a quiz in Cards::QuizCardCreationService"
  E140032:
    value: "Cards::QuizCardCreationService - Question options or question text missing"
    context: "Error when question options or question text are missing in Cards::QuizCardCreationService"
  E140033:
    value: "Cards::QuizCardCreationService - Option missing"
    context: "Error when an option is missing in Cards::QuizCardCreationService"
  E140034:
    value: "Unable to create {attribute}"
    context: "Error encountered while creating {attribute}"
  E140035:
    value: "Url can't be blank/empty"
    context: "Error when a URL is blank or empty"
  E140036:
    value: "Missing/blank url param in resource {index}"
    context: "Error when a URL parameter is missing or blank in resource {index}"
  E140037:
    value: "Invalid url {index}"
    context: "Error when an invalid URL is provided for {index}"
  E140038:
    value: "Error while saving a poll, check logs for details!"
    context: "Error encountered while saving a poll"
  E140039:
    value: "Content is invalid, only pathway can be added"
    context: "Error when invalid content is added, and only pathways are allowed"
  E140040:
    value: "Error occurred while adding {attribute} to the {subject}"
    context: "Error during the addition of {attribute} to the {subject}"
  E140041:
    value: "Cannot be set for {state}"
    context: "Error when the attribute cannot be set for the given {state}"
  E140042:
    value: "started_at should be less than or equal to current date and time"
    context: "Error when the start date is not less than or equal to the current date and time"
  E140043:
    value: "started_at is greater than completed_at"
    context: "Error when the start date is greater than completed date"
  E140044:
    value: "Content not found with ecl_id: {id}"
    context: "Error when content not found with ecl id"
  E140045:
    value: "Error while destroying subscription: {attribute}"
    context: "Error when error present while destroying subscription"
  E140046:
    value: "Card is not shared with user"
    context: "Error when card is not shared with the user while unsharing"
  E140047:
    value: "Limit exceeded. The max limit is {limit} to choose skills"
    context: "Error when limit is exceeded to choose siklls"
  E140048:
    value: "User has already upvoted this item"
    context: "error when user has already upvoted for the item"
  E140049:
    value: "The card has already been shared with user"
    context: "error when card has already been shared with the user"
  E140050:
    value: "You are not authorized to share this content"
    context: "error when user is not authorized to share the content"
  E140051:
    value: "Response cannot be accepted because of: {errors}"
    context: "error when invalid response while answering a quiz"
  E140052:
    value: "Content is not a quiz content"
    context: "error when invalid content is used while answering a quiz"
  E140053:
    value: "You are not authorized to bookmark this content"
    context: "error when user is not authorized"
  E140054:
    value: "Content is not a quiz content"
    context: "error when non quiz content is passed"
  E140055:
    value: "Cannot archive the content"
    context: "Error when the system fails to archive the requested content"
  E140056:
    value: "Cannot unarchive the content"
    context: "Error when the system fails to unarchive the requested content"
  E140057:
    value: "Error while updating subscription: {arrors}"
    context: "Error when there is an issue updating a subscription, details provided in {arrors}"
  E140058:
    value: "{attribute} already published"
    context: "Error when attempting to publish content that is already published"
  E140059:
    value: "Please add cards to the collection before publishing."
    context: "Error when trying to publish a collection without adding cards"
  E140060:
    value: "Completion not allowed for content type {attribute}"
    context: "Error when completion is attempted for an unsupported content type"
  E140061:
    value: "Please complete all the cards inside the Pathway/Journey to complete."
    context: "Error when attempting to complete a Pathway/Journey without completing all included cards"
  E140062:
    value: "Content is already completed."
    context: "Error when trying to complete content that is already marked as completed"
  E140063:
    value: "Error while fetching {attribute}. Error {error}"
    context: "Error when fetching a specific attribute fails, with details provided in {error}"
  E140064:
    value: "Content should not be a pathway or journey."
    context: "Error when content is incorrectly assigned as a pathway or journey"
  E140065:
    value: "You are not authorized to access this action"
    context: "Authorization error when the user does not have permissions for an action"
  E140066:
    value: "Missing required params type"
    context: "Error when a required parameter type is not provided in a request"
  E140067:
    value: "Error while getting assignables for card."
    context: "Error when retrieving assignable items for a card fails"
  E140068:
    value: "Content is not a quiz content"
    context: "Error when an operation is performed on content that is not a quiz"
  E140069:
    value: "Report generation is in progress."
    context: "Informational message indicating a report is being generated"
  E140070:
    value: "Invalid content"
    context: "Error when the provided content is invalid or does not meet requirements"
  E140071:
    value: "Can not start already started or completed card."
    context: "Error when attempting to start a card that is already started or completed"
  E140072:
    value: "Content is already promoted"
    context: "Error when attempting to promote content that is already promoted"
  E140073:
    value: "Content is already unpromoted"
    context: "Error when attempting to unpromote content that is already unpromoted"
  E140074:
    value: "Either context_id or context_type is not present."
    context: "Error when required context_id or context_type is missing from the request"
  E140075:
    value: "Invalid context_type. It should be pathway or journey."
    context: "Error when an invalid context_type is provided, only pathway or journey are supported"
  E140076:
    value: "You cannot complete Project card. It will get completed automatically when author/assignor grades your submission"
    context: "Error when a user attempts to manually complete a Project card, which must be graded by an author or assignor"
  E140077:
    value: "Feature is disabled for this card."
    context: "Error when a feature is not enabled for a specific card"
  E140078:
    value: "Internal LMS content completion is disabled for this card."
    context: "Error when internal LMS content completion is attempted for a card that has it disabled"
  E140079:
    value: "Completion date can not be future date"
    context: "Error when Completion date is a future date"
  E140080:
    value: "Invalid completion date"
    context: "Error when completion date is invalid"
  E150001:
    value: "Invalid sub-organization details"
    context: "Error when sub-organization details are invalid"
  E150002:
    value: "Does not belong to the organization profile"
    context: "Error when an item does not belong to the organization profile"
  E150003:
    value: "Domain is already mapped to another sub-organization"
    context: "error when domain already mapped to another sub-organization"
  E200001:
    value: "Edpay: Billing service error"
    context: "Error related to billing service in Edpay"
  E200002:
    value: "Internal Error. {error}"
    context: "Error returned by internal service"
  E401000:
    value: "You are not authorized for this action"
    context: "Authorization error when a user is not authorized for an action"
  E401001:
    value: "Application is not authorized to access Learning Experience APIs"
    context: "Authorization error when an application is not authorized to access Learning Experience APIs"
  E401002:
    value: "JWT signature verification failed"
    context: "Authorization error when JWT signature verification fails"
  E401003:
    value: "Invalid X-INTERNAL-APP Value, valid values are {attribute}"
    context: "Authorization error when X-INTERNAL-APP value is invalid"
  E401004:
    value: "Missing Credentials. Make sure your API invocation call has either X-ACCESS-TOKEN or X-JWT-Assertion as a header"
    context: "Authorization error when credentials are missing"
  E401005:
    value: "Public key not set for RS256 algorithm"
    context: "Authorization error when public key is not set for RS256 algorithm"
  E401006:
    value: "Failed to parse public key for RS256"
    context: "Authorization error when parsing public key for RS256 fails"
  E401007:
    value: "Reset password token has expired, please request a new one"
    context: "Error when a reset password token has expired"
  E401008:
    value: "You are not authorized to access this {attribute}"
    context: "Authorization error when a user is not authorized to perform an action"
  E401009:
    value: "Profile was not found in talent marketplace"
    context: "Error when we request a recommendations for a profile who doesn't exists in TMP"
  E401010:
    value: "Provided lang code is invalid"
    context: "Error when we request a recommendations with language code not supported by TMP"
  E401011:
    value: "You are not authorized to this view this channel"
    context: "Authorization error when a user is not authorized to view an action"
  E401012:
    value: "You are not authorized to edit this {attribute}"
    context: "Authorization error when a user is not authorized to perform an action"
  E401013:
    value: "Permission denied"
    context: "Authorization error when access to a resource or action is denied"
  E500001:
    value: "Language code cannot be empty for translations"
    context: "Presence validation error indicating that the language code is missing"
  E500002:
    value: "Duplicate translation set for the same language code"
    context: "Uniqueness validation error when an attempt is made to submit multiple translations for the same language code"
  E500003:
    value: "Job Entity not found"
    context: "Not found error when the specified job entity cannot be located"
  E500004:
    value: "Location not found"
    context: "Not found error when the specified location cannot be located"
  E500005:
    value: "Organization not found"
    context: "Not found error when the specified organization cannot be located"
  E500006:
    value: "Invalid action"
    context: "Validation error indicating that an invalid or unsupported action has been requested"
  E500007:
    value: "Provided Date format is invalid"
    context: "Validation error when a provided date does not conform to the required format"
  E500008:
    value: "Provided Country is invalid"
    context: "Validation error when the provided country code is not valid while setting Location"
  E500009:
    value: "Provided TimeZone is invalid"
    context: "Validation error when the timezone provided is unrecognized or invalid"
  E500010:
    value: "Provided Job Level is invalid"
    context: "Validation error when the provided job level is unrecognized or invalid"
  E500011:
    value: "Provided Career Track is invalid"
    context: "Validation error when the provided career track is unrecognized or invalid"
  E500012:
    value: "Provided Job Function is invalid"
    context: "Validation error when the provided job function is unrecognized or invalid"
  E500013:
    value: "Provided Job Family is invalid"
    context: "Validation error when the provided job family is unrecognized or invalid"
  E500014:
    value: "Provided Parent Internal Id is invalid"
    context: "Validation error when the internal ID for the parent entity is invalid or does not exist"
  E500015:
    value: "Provided Organization Type is invalid"
    context: "Validation error when the organization type provided is unrecognized or invalid"
  E500016:
    value: "Provided Parent External Id is invalid"
    context: "Validation error when the external ID of the parent entity is invalid"
  E500017:
    value: "Title cannot be empty"
    context: "Presence validation error when the title field is empty"
  E500018:
    value: "Family External Id cannot be empty"
    context: "Presence validation error when the external ID for the job family is empty"
  E500019:
    value: "Status cannot be empty"
    context: "Presence validation error when the status field is empty"
  E500020:
    value: "Job Level cannot be empty"
    context: "Presence validation error when the job level is empty"
  E500021:
    value: "Visibility cannot be empty"
    context: "Presence validation error when the visibility field is empty"
  E500022:
    value: "Country cannot be empty"
    context: "Presence validation error when the country code is missing or not provided while setting Location"
  E500023:
    value: "Location Id cannot be empty"
    context: "Presence validation error when the location ID is missing or not provided"
  E500024:
    value: "Title cannot be null or empty"
    context: "Presence validation error when the title field is missing or empty"
  E500025:
    value: "Status cannot be null or empty"
    context: "Presence validation error when the status field is missing or empty"
  E500026:
    value: "Job Level cannot be null or empty"
    context: "Presence validation error when the job level field is missing or empty"
  E500027:
    value: "Visibility cannot be null or empty"
    context: "Presence validation error when the visibility field is missing or empty"
  E500028:
    value: "Job Family cannot be null or empty"
    context: "Presence validation error when the job family field is missing or empty"
  E500029:
    value: "Organization Unit Type cannot be null or empty"
    context: "Presence validation error when the organization unit type is missing or empty"
  E500030:
    value: "External Id cannot be null or empty"
    context: "Presence validation error when the external ID is missing or empty"
  E500031:
    value: "Visibility must be a boolean value"
    context: "Validation error when the visibility field is provided but has an invalid value"
  E500032:
    value: "Provided Status is invalid"
    context: "Validation error when the status field is provided but has an invalid value"
  E500033:
    value: "Provided Title already exists"
    context: "Uniqueness validation error when the provided entity title already exists"
  E500034:
    value: "Organization Unit Type change not allowed"
    context: "Action restriction that prevents the modification of the type of an existing organization unit"
  E500035:
    value: "External Id is mandatory"
    context: "Presence validation error when an external ID is required but not provided"
  E500036:
    value: "Error while getting skills from Skill Studio"
    context: "Service error when there is an issue retrieving skills data from Skill Studio"
  E500037:
    value: "Error while getting Proficiency Levels"
    context: "Service error when there is an issue retrieving proficiency levels data"
  E500038:
    value: "Error while getting valid Job Levels and Career Tracks"
    context: "Service error when there is an issue retrieving valid job levels or career tracks"
  E500039:
    value: "Error while retrieving Organization detail"
    context: "Data retrieval failure when there is an issue retrieving the details of an organization"
  E500040:
    value: "Error while retrieving Location detail"
    context: "Data retrieval failure when there is an issue retrieving the details of a location"
  E500041:
    value: "Job Function is associated to Job Family"
    context: "Action restriction that prevents the deletion of a job function because it is associated with a job family"
  E500042:
    value: "Job Family is associated to Job Role"
    context: "Action restriction that prevents the deletion of a job family because it is associated with a job role"
  E500043:
    value: "Job Family cannot be inactivated as it has active roles"
    context: "Action restriction preventing the inactivation of a job family because it has active roles associated with it."
  E500044:
    value: "Job Role is associated as next role or prior role"
    context: "Action restriction preventing inactivation of a job role that is currently linked as a next role or prior role"
  E500046:
    value: "Organization cannot be inactivated as it has active child organizations"
    context: "Action restriction preventing the inactivation of an organization that has active child organizations"
  E500047:
    value: "Updating parent organization creates cyclic dependency"
    context: "Action restriction preventing updates to the parent organization because it would result in a cyclic dependency"
  E500048:
    value: "Page size cannot be greater than 100"
    context: "Validation error when the requested page size exceeds the maximum limit of 100"
  E500049:
    value: "Failed while Organizations search"
    context: "Service error indicating a failure during organizations search operation"
  E500050:
    value: "Failed while Locations search"
    context: "Service error indicating a failure during locations search operation"
  E500051:
    value: "Failed while Job Family search"
    context: "Service error indicating a failure during job families search operation"
  E500052:
    value: "Failed while Job Roles search"
    context: "Service error indicating a failure during job roles search operation"
  E500053:
    value: "Invalid status, Valid statuses are : PUBLISHED, DRAFT, CLOSED"
    context: "Error when project status is invalid if provided in the request"
  E500054:
    value: "Page size cannot be greater than 100"
    context: "Error when page size is greater than 100 if provided in the request"
  E500055:
    value: "Start date and End Date should be later than January 1st, 2000"
    context: "Error when start date and end date are earlier than January 1st, 2000 if provided in the request"
  E500056:
    value: "Application Deadline should be later than January 1st, 2000"
    context: "Error when application deadline is earlier than January 1st, 2000 if provided in the request"
  E500057:
    value: "The project is non-editable, as the start date has passed and it has confirmed applications."
    context: "Error when the project is non-editable, as the start date has passed and it has confirmed applications."
  E500058:
    value: "End date should not be blank."
    context: "Error when end date is blank while creating and updating the project."
  E500059:
    value: "End date should not be greater than the start date."
    context: "Error when end date is greater than start date while creating and updating the project."
  E500060:
    value: "Start date should be greater than the current date."
    context: "Error when the Start date should be greater than the current date while creating and updating the project."
  E500061:
    value: "Application deadline should be before start date."
    context: "Error when the Application deadline should be before start date while creating and updating the project."
  E500062:
    value: "Application deadline can not be in past."
    context: "Error when the Application deadline can not be in past while creating and updating the project."
  E500063:
    value: "Status transition from Published to Draft is not allowed."
    context: "Error when the status transition from Published to Draft is not allowed while updating the project."
  E500064:
    value: "Closed Project update is not allowed."
    context: "Error when the Closed Project update is not allowed."
  E500065:
    value: "Number of openings should be between 0 - 999 if application required is applicable."
    context: "Error when the Number of openings should be between 0 - 999 if application required is applicable."
  E500066:
    value: "Required Application can not be changed since it is already published."
    context: "Error when Required application can't be changed since it is already published."
  E500067:
    value: "Skill {attribute} is duplicated."
    context: "Error when skill is duplicated while creating and updating the project."
  E500068:
    value: "Division {attribute} is duplicated."
    context: "Error when division is duplicated while creating and updating the project."
  E500069:
    value: "TimeZone {attribute} is duplicated."
    context: "Error when TimeZone is duplicated while creating and updating the project."
  E500070:
    value: "Location {attribute} is duplicated."
    context: "Error when location is duplicated while creating and updating the project."
  E500071:
    value: "Role {attribute} is duplicated."
    context: "Error when role is duplicated while creating and updating the project."
  E500072:
    value: "Language {attribute} is duplicated."
    context: "Error when language is duplicated while creating and updating the project."
  E500073:
    value: "Owner {attribute} is duplicated."
    context: "Error when Owner is duplicated while creating and updating the project."
  E500074:
    value: "User Id cannot be null or empty."
    context: "Error when user id is null while creating and updating the project."
  E500075:
    value: "Title cannot be null or empty."
    context: "Error when title is null while creating and updating the project."
  E500076:
    value: "Description cannot be null or empty."
    context: "Error when description is null while creating and updating the project."
  E500077:
    value: "Status cannot be null or empty."
    context: "Error when Status is null while creating and updating the project."
  E500078:
    value: "Description cannot be null or empty."
    context: "Error when description is null while creating and updating the project."
  E500079:
    value: "Number of skills must be between 0 to 50."
    context: "Error when Number of skills more than 50 if provided in the request."
  E500080:
    value: "Owner cannot be null or empty."
    context: "Error when Owner is null while creating and updating the project."
  E500081:
    value: "Max openings for opportunity between 0 to 999."
    context: "Error when Max openings is more than 999 if provided in the request."
  E500082:
    value: "Number of roles must be between 0 to 6."
    context: "Error when the number of roles is more than 6 if provided in the request while creating and updating the project."
  E500083:
    value: "Number of locations must be between 0 to 6."
    context: "Error when the number of locations is more than 6 if provided in the request while creating and updating the project."
  E500084:
    value: "Number of skills must be between 0 to 6."
    context: "Error when the number of skills is more than 6 if provided in the request while creating and updating the project."
  E500085:
    value: "Number of timezones must be between 0 to 6."
    context: "Error when the number of timezones is more than 6 if provided in the request while creating and updating the project."
  E500086:
    value: "Number of languages must be between 0 to 6."
    context: "Error when the number of languages is more than 6 if provided in the request while creating and updating the project."
  E500087:
    value: "Invalid project id."
    context: "Error when the project id is invalid while fetching the project details."
  E500088:
    value: "Delete is only supported in Draft state."
    context: "Error when the project is published or closed, and trying to delete."
  E500089:
    value: "User is not authorized."
    context: "Error when the user is not authorized to perform the action."
  E500090:
    value: "Max opening positions should be greater than 0 with require application."
    context: "Error when the max positions is less than 0 with require application."
  E500091:
    value: "Max opening positions should not be reduced once the status is Published"
    context: "Error when the max positions is reduced once the status is Published."
  E500092:
    value: "The project is non-editable because the start date has passed and applications have been confirmed."
    context: "Error when the project is non editable as start date has passed and it have confirmed applications."
  E500093:
     value: "The start date cannot be changed as the project has confirmed applications."
     context: "Error when the start date can not be changed as the project has confirmed applications."
  E500094:
     value: "Status transition from Draft to Closed is not allowed."
     context: "Error while updating status from Draft to Closed is not allowed."
  E500095:
      value: "Project cannot be edited as its startDate is in the past. Please update the start date if modification is required."
      context: "Error when the project is non editable as start date has passed."
  E500096:
      value: "Project cannot be edited as its submission deadline is in the past. Please update the submission deadline if modification is required."
      context: "Error when the project is non editable as submission deadline has passed."
  E500097:
      value: "Values must be non-negative"
      context: "Validation error when the value is negative"
  E500098:
      value: "Size cannot be greater than 100"
      context: "Validation error when the requested size exceeds the maximum limit of 100"
  E500099:
      value: "Updating parent location creates cyclic dependency"
      context: "Action restriction preventing updates to the parent location because it would result in a cyclic dependency"
  E500100:
      value: "Location cannot be inactivated as it has active child locations"
      context: "Action restriction preventing the inactivation of a location that has active child locations"
  E500101:
      value: "Parent organization type is not same as child"
      context: "Action restriction when parent organization type is different from child organization"
  E500102:
      value: "Override Family must be a boolean value"
      context: "Validation error when the override family field is provided but is not a boolean value"
  E500103:
      value: "External Id update is not allowed"
      context: "Action restriction when attempting to update the external ID"
  E500104:
    value: "Run Async must be a boolean value"
    context: "Validation error when the run async field is provided but is not a boolean value"
  E500105:
    value: "Cornerstone Internal Id update is not allowed"
    context: "Validation error when attempting to update the Cornerstone Internal Id which is not permitted"
  E500106:
    value: "An unexpected error occurred. Please try again."
    context: "A generic error message returned when an unexpected internal exception occurs and must not expose internal details"
