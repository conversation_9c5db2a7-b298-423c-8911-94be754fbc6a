import { EDC_TRANSLATIONS_DOMAIN } from 'edc-web-sdk/config/envConstants';

import { languageMap } from './languageMap';

export const defaultLocale = 'en';
export const defaultLanguages = [defaultLocale, 'en-US'];

function getTranslationWithParameters(str, params) {
  let updatedString = str;
  Object.keys(params).forEach(key => {
    const param = params[key];
    updatedString = updatedString.replace(`{${key}}`, param);
  });
  return updatedString;
}

export function translatr(appName, key, variables = {}, fallback) {
  if (window.localStorage.getItem('selectedLanguage') == 'id-key') {
    return '周' + appName + ':' + key + '周';
  }
  const currentLocale =
    window.localStorage.getItem('selectedLanguage') ||
    (window && window.__ED__ && window.__ED__.profile && window.__ED__.profile.language) ||
    defaultLocale;
  const repo = 'admin';
  let repoTS = getTimestampFromStorage(repo);
  let keyTrimmed = key.trim();
  const appTranslation = window.sessionStorage.getItem(
    `translatr_${appName}_${currentLocale}_${repoTS}`
  );
  const appDefault = window.sessionStorage.getItem(
    `translatr_${appName}_${defaultLocale}_${repoTS}_default`
  );
  if (!appTranslation && !appDefault) {
    return `(${appName}:${keyTrimmed})`;
  }
  if (!appTranslation) {
    let unTranslatedApps = window.sessionStorage.getItem('translatr_unTranslatedApps');
    if (!unTranslatedApps) {
      unTranslatedApps = [];
    } else {
      // get existing list of untranslated apps
      unTranslatedApps = JSON.parse(unTranslatedApps);
    }
    // push new untranslated apps.
    unTranslatedApps.push(appName);
    // remove duplicates
    let uniqueUnTranslatedApps = unTranslatedApps.filter((item, i, ar) => ar.indexOf(item) === i);
    // store new set of untranslated apps
    window.sessionStorage.setItem(
      'translatr_unTranslatedApps',
      JSON.stringify(uniqueUnTranslatedApps)
    );
  }

  let translation = appTranslation && JSON.parse(appTranslation)[keyTrimmed];
  let defaultString = appDefault && JSON.parse(appDefault)[keyTrimmed];
  if (!translation) {
    let unTranslatedStrings = window.sessionStorage.getItem('translatr_unTranslatedStrings');
    if (!unTranslatedStrings) {
      unTranslatedStrings = [];
    } else {
      // get existing list of untranslated strings
      unTranslatedStrings = JSON.parse(unTranslatedStrings);
    }
    // push new untranslated strings.
    unTranslatedStrings.push(`${appName}:${keyTrimmed}`);
    // remove duplicates
    let uniqueUnTranslatedStrings = unTranslatedStrings.filter(
      (item, i, ar) => ar.indexOf(item) === i
    );
    // store new set of untranslated strings
    window.sessionStorage.setItem(
      'translatr_unTranslatedStrings',
      JSON.stringify(uniqueUnTranslatedStrings)
    );
    if (defaultString) {
      return getTranslationWithParameters(defaultString, variables);
    }
    return fallback || `{${appName}:${keyTrimmed}}`;
  }
  if (translation === defaultString) {
    return getTranslationWithParameters(defaultString, variables);
  }
  return getTranslationWithParameters(translation, variables);
}

export const storeTranslatr = (key, data = {}) => {
  window.sessionStorage.setItem(key, JSON.stringify(data));
};
export const getTranslatrUrl = (key, timestamp, locale = 'en') => {
  const [repo, module, app] = key.split('.');
  return `https://${EDC_TRANSLATIONS_DOMAIN}/translations/edcast/${repo}/${timestamp}/${module}/${app}/${languageMap[
    locale
  ] || defaultLanguages[1]}.json`;
};

export const getTranslatrTimestamp = async repo => {
  try {
    const res = await fetch(
      `https://${EDC_TRANSLATIONS_DOMAIN}/translations/edcast/${repo}/timestamp.json`
    );
    const jsonResponse = await res.json();
    if (jsonResponse.timestamp) {
      window.sessionStorage.setItem(`translatr_${repo}_ts`, Object.values(jsonResponse)[0]);
      return Object.values(jsonResponse)[0];
    }
  } catch (err) {
    console.error(err);
  }
};

export const getLastRequested = repo => {
  return JSON.parse(window.sessionStorage.getItem(`translatr_${repo}_lastRequested`));
};

export const setLastRequested = (repo, value) => {
  return window.sessionStorage.setItem(`translatr_${repo}_lastRequested`, value);
};

export const getTimestampFromStorage = repo => {
  return JSON.parse(window.sessionStorage.getItem(`translatr_${repo}_ts`));
};

export const PromiseHelperAllSettled = promises => {
  return Promise && Promise.all(
        promises.map(function(promise) {
          return promise
            .then(function(value) {
              return { state: 'fulfilled', value: value };
            })
            .catch(function(reason) {
              return { state: 'rejected', reason: reason };
            });
        })
      );
};
