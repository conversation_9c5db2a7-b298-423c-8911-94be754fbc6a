import { translatr } from '@translatr/utils';
import {
  getBulkLocationById,
  getBulkOrgById,
  getJobRoleDetailsById
} from 'edc-web-sdk/requests/hrData.v2';
export const STATUS = [
  { label: 'Active', value: 'active', checked: false, customValueType: 'one' }
];

export const OU_FIELD = 'ou_field';
export const STANDARD_FIELD = 'standard_field';
export const CUSTOM_FIELD = 'custom_field';

export const JOB_ROLE = 'job_role';
export const JOB_TITLE = 'job_title';
export const ROLE = 'roles';
export const STATUS_FIELD = 'status';
export const TERMINATION_DATE = 'termination_date';
export const MANAGER = 'manager';
export const LOCATION = 'location';
export const LEARNING_GOALS = 'learning_goals';
export const SKILLS = 'skills';
export const ACTIVE = 'ACTIVE';

export const SELECT_COLUMN = () => translatr('common.common', 'selectColumn');

export const createOptionsList = (
  customFieldOptions,
  organizationUnitsOptions,
  standardTypeOptions
) => {
  const optionsList = [];
  
  // Helper function to create option group
  const createOptionGroup = (label, value, children) => {
    if (children && children.length > 0) {
      optionsList.push({
        label,
        value,
        hasChild: true,
        children
      });
    }
  };

  // Process standard type options
  const isHrDataServiceEnabled = getConfigValue('hr_data_service_enablement');
  const filteredStandardTypeOptions = isHrDataServiceEnabled
    ? standardTypeOptions
    : standardTypeOptions.filter(option => ![JOB_ROLE, LOCATION].includes(option.value));
  
  const standardChildren = filteredStandardTypeOptions.map(option => ({
    label: option.displayName,
    value: option.value,
    dataType: option.dataType
  }));
  
  createOptionGroup(
    translatr('workflow.main', 'StandardFields'),
    STANDARD_FIELD,
    standardChildren
  );

  // Process organization units options
  const ouChildren = organizationUnitsOptions.map(orgUnit => ({
    ...orgUnit,
    dataType: 'text'
  }));
  
  createOptionGroup(
    translatr('common.common', 'OrganizationUnit'),
    OU_FIELD,
    ouChildren
  );

  // Process custom field options
  createOptionGroup(
    translatr('common.common', 'customFields'),
    CUSTOM_FIELD,
    customFieldOptions
  );

  return optionsList;
};
export const createNewRule = (rule, name, selectedValue) => {
  const newRule = {
    ...rule,
    customFieldValue: [],
    ...(name === 'operator' && { operator: selectedValue }),
    ...(name !== 'operator' && { selectedParentColumn: name, field: selectedValue })
  };

  if ([CUSTOM_FIELD, STANDARD_FIELD, OU_FIELD].includes(name)) {
    newRule.operator = '';
  }

  if (selectedValue.dataType === 'boolean') {
    newRule.operator = { label: 'Equals to', value: 'equals' };
  }

  return newRule;
};

export const constructSelectedOUOrg = async rule => {
  const selectedOUorg = {};
  const ouCustomFieldValueIds = rule.customFieldValue.map(field => field.value);
  try {
    const response = await getBulkOrgById({ ids: ouCustomFieldValueIds });
    if (response) {
      selectedOUorg[rule.field.label] = response.data.map(childOrg => ({
        value: childOrg.id,
        label: childOrg.title,
        title: childOrg.title,
        orgType: childOrg.orgType,
        hasChild: childOrg.hasChild,
        status: childOrg.status
      }));
      return selectedOUorg;
    }
  } catch (err) {
    console.error('Error in DynamicWorkflowRule.getBulkOrgById', err);
  }
};

export const getLocationFieldValue = async rule => {
  const locationIds = rule.customFieldValue.map(field => field.value);
  try {
    const response = await getBulkLocationById({ ids: locationIds });
    const locationData = response.data.map(location => ({
      value: location.id,
      label: location.title,
      status: location.status,
      customValueType: 'one'
    }));
    return locationData;
  } catch (error) {
    console.error('Error in DynamicWorkflowRule.getLocationFieldValue', error);
  }
};

export const transformOrganizationUnits = data => {
  return data.reduce((result, orgUnit) => {
    if (orgUnit['keyValues'] && JSON.parse(orgUnit['keyValues']['enable'])) {
      const transformedEl = {
        label: orgUnit.keyValues['l.en'] || orgUnit.keyValues.default,
        value: orgUnit.id
      };
      return [...result, transformedEl];
    }
    return result;
  }, []);
};

export const getJobRoleLabel = async rule => {
  const jobRoleIds = rule.customFieldValue.map(field => field.value);
  try {
    const response = await getJobRoleDetailsById({ ids: jobRoleIds });
    const jobRoleData = response.topics.map(jobRole => ({
      ...jobRole,
      value: jobRole.id,
      customValueType: 'one'
    }));
    return jobRoleData;
  } catch (error) {
    console.error('Error in DynamicWorkflowRule.getJobRoleLabel', error);
  }
};

export const getConfigValue = configName => {
  const configs = window.__edOrgData.configs;
  const config = configs.find(config => config.name === configName);
  return config ? config.value : null;
};

export const isOULocationOrJobRole = rule =>
  rule.selectedParentColumn === OU_FIELD || [JOB_ROLE, LOCATION].includes(rule.field.value);

export const validateShowWarning = rule => {
  const hasInactiveValues = rule.customFieldValue?.some(value => value?.status === 'INACTIVE');
  const isHrDataServiceDisabled = !getConfigValue('hr_data_service_enablement');
  const fieldPresent = isOULocationOrJobRole(rule);

  if (fieldPresent && isHrDataServiceDisabled) {
    return {
      value: isHrDataServiceDisabled,
      message: translatr('workflow.main', 'HrDataServiceDisabledInfo')
    };
  }

  if (fieldPresent && hasInactiveValues) {
    return {
      value: hasInactiveValues,
      message: translatr('workflow.main', 'FieldNoLongerActive')
    };
  }
};