import { translatr } from '@translatr/utils';
import React from 'react';
import { connect } from 'react-redux';
import { debounce } from 'lodash';
import { queryTopics, getTopicSuggest } from 'edc-web-sdk/requests/topics';


import {WithContext as ReactTagInput} from 'react-tag-input-vulnerability-fix';
import isUrlValid from 'validator/lib/isURL';
import InputMask from 'react-text-mask';

import LD from '../../LDStore'
import ConfirmEditModal from './confirmEditModal';
import ImageUpload from '../common/imageUpload.jsx';
import Select, { Async } from 'react-select';
import {usersv2} from "edc-web-sdk/requests/index";
import {convertContentType} from '../../../common/utils/displayTypeConverter';
import getStandardizedTitle from '../../../common/utils/getStandardizedTitle';
import getStandardizedMessage from '../../../common/utils/getStandardizedMessage';
import {Permissions} from '../../../common/utils/checkPermissions';
import customAutoCorrectedDatePipe from '../common/CustomAutoCorrectedDatePipe';
import {createResource, checkDuplicateCard} from 'edc-web-sdk/requests/cards';
import unescape from 'lodash/unescape';
import * as actions from './../../actions/contents';
import * as overlayActions from '../../actions/overlayActions';
import { UPLOAD_OBJECT_TYPE, UPLOAD_TYPE } from '../MediaUpload/constants.js';
import {COURSE, MEDIA, TRAINING, VILT_DISPLAY, PROJECT, QUIZ, SCORM} from '../../constants/cardTypes';
import { displayNoLevelInProficiencyLevels } from '../shared/getProficiencyLevel';


import {
    Col,
    Button,
    Modal,
    FormGroup,
    Form,
    ControlLabel,
    Checkbox,
} from '@sketchpixy/rubix';

import getOrgConfig from '../../actions/getConfig';
import isTextCard from '../../../common/utils/isTextCard';
import updateMessageForCKEditor from '../../../common/utils/updateMessageForCKEditor';
import getCoverImageForTextCard from '../../../common/utils/getCoverImageForTextCard';
import SociativeTaxonomySelector from '../common/SociativeTaxonomySelector';
import SkillLevelDropDown from '../common/SkillLevelDropdown';
import { makeSuggestionsUniq } from '../../Utils/makeSuggestionsUniq';
import { checkIfArticleCard, checkIfScormCard, checkIfTextCard } from '../../../common/utils/checkCardTypes';
import isUploadTypeCard from '../../../common/utils/isUploadTypeCard';
import { checkIfLiveCard } from '../../../common/utils/checkCardTypes';
import {getDetails} from 'edc-web-sdk/requests/orgSettings';
import {noLevelOptions} from '../common/SkillLevelDropdown';

function getCardLanguages(langDetails, cardLangs, isNonUgc, orgDefaultLanguage){
  const theDetails = [...Object.values(langDetails || {}), {language: 'un', label: 'Unspecified'}];
  const orgLangDetails = {};
  theDetails.forEach(lang => {
    orgLangDetails[lang.language] = {
      displayName: lang.label,
      code: lang.language
    };
  });

  let cardLanguages = [];
  if(isNonUgc) {
    cardLangs.forEach(cardLang => {
      const code = cardLang.language;
      const { displayName } = orgLangDetails[code];
      if (displayName && displayName.length > 0) {
        cardLanguages.push({
          displayName,
          value: code
        });
      }
    });
  } else {
    theDetails.forEach(orgLang => {
      const { label, language } = orgLang;
      cardLanguages.push({
        displayName: label,
        value: language
      });
    });
  }

  if(!cardLanguages.length){
    theDetails.forEach(orgLang => {
      const { label, language } = orgLang;
      if (orgDefaultLanguage === language) {
        cardLanguages.push({
          displayName: label,
          value: language
        });
      }
    });
  }
  return cardLanguages;
}

function getCardLanguageData(selectedCardLanguage, card){
    const cardLangs = card && card.languages || [];

    let cardLangData = {
        message: getStandardizedMessage(card),
        title: getStandardizedTitle(card),
        language: card.language
    };

    if(selectedCardLanguage){
        cardLangData = cardLangs.filter(cardLang => {
            return cardLang.language === selectedCardLanguage;
        })[0];
    }

    return cardLangData;
}

function closeWarning(dispatch) {
  setTimeout(() => {
    dispatch(overlayActions.closeWarning());
  }, 3000);
}

@connect((state) => state)
export default class EditModal extends React.Component {
    constructor(props) {
        super(props);
        const checkDuplicateConfig = this.props.user.userData && this.props.user.userData.organization && this.props.user.userData.organization.configs && this.props.user.userData.organization.configs.find((config) => config.name === "duplicate_card_creation_disabled");

        this.orgDefaultLanguage = getOrgConfig.call(this, 'DefaultOrgLanguage');
        this.showLXMediaHub = this.props.orgSettings.showLXMediaHub;
        this.languageDetails = this.props.orgLanguages || [];
        this.emptyState = {
        imageInput : {},
        unselectedUsers : [],
        selectIsLoading: false,
        selectedUser: null,
        userSuggestionsLimit: 10,
        userSuggestionsOffset: 0,
        searchUserText: '',
        isLoadingUserExternally: false,
        disableSave: false,
        linkError: '',
        resource: {},
        cardMessage:"",
        cardLanguageData:{},
        cardLanguages:[],
        showMessageForExistingTextCard: false,
        updateMessageForExistingTextCardAfterUpdating: false,
        onboardingVersion : LD.getOnBoardingVersion(),
        userTaxonomyTopics: [],
        selectedDomain : null,
        selectedNode : [],
        mandatoryFields: {},
        shouldAwardSkills: false,
        excludeFromSearch: null,
        excludeFromRecommendation: null
       };

       this.state = {...this.emptyState , ...{
            checkDuplicate: checkDuplicateConfig && checkDuplicateConfig.value,
            selectedCardLanguage: 'un',
        }};
        this.autoCorrectedDatePipe = customAutoCorrectedDatePipe("HHH MM");
        this.skillsForUgcLimit = getOrgConfig.call(this, 'skills_for_ugc');
        this.userTaxonomyTopicsMaxLimit = (this.skillsForUgcLimit && this.skillsForUgcLimit.value) || 3;
    }

    componentDidMount() {
      this.getConfigDetails()
    }

    getConfigDetails() {

      getDetails().then((details) => {
        let orgCustomizationConfig = details && details.configs && details.configs.find(config => config.name === "OrgCustomizationConfig");
        let configs = orgCustomizationConfig.value.web.mandatoryFields && orgCustomizationConfig.value.web.mandatoryFields['web/mandatoryFields'];
        this.setState({
          mandatoryFields: Object.assign({},configs)
        });
      })
    }

    componentDidUpdate(){
        const smartcardEditModal = document.getElementById('smartcard-edit-modal');
        if (smartcardEditModal && smartcardEditModal.hasAttribute("tabindex")) {
            smartcardEditModal.removeAttribute("tabindex");
        }
    }

    topicInputChangeHandler(queryStr) {
        this.props.dispatch(actions.getTopicSuggest(queryStr));
    }

    topicInputClickHandler() {
        this._topicInput.child.textInput.focus();
    }

    topicAddHandler(topic) {
        let topicsList = this.props.contents.editingContent.tags && this.props.contents.editingContent.tags.map(tag=>tag.name);
        if (topicsList.indexOf(topic.trim()) === -1 && topic.trim() !== '') {
            this.props.dispatch(actions.addTopicToModal(topic.trim()));
        }
    }

    removeTopicClickHandler(index) {
        this.props.dispatch(actions.removeTopicFromModal(index));
    }

    channelInputClickHandler() {
        this._channelInput.child.textInput.focus();
    }

    channelInputChangeHandler(queryStr) {
        this.props.dispatch(actions.getChannelSuggest(queryStr));
    }

    channelAddHandler(channel) {
        this.props.contents.editingContent.channelSuggestions.some(suggestion => {
            if (suggestion.text === channel) {
                suggestion.text = unescape(suggestion.text)
                suggestion.name = unescape(suggestion.name)
                this.props.dispatch(actions.addChannelToModal(suggestion));
                return true;
            }
            return false;
        });
    }

    removeChannelClickHandler(index) {
        this.props.dispatch(actions.removeChannelFromModal(index));
    }

    cancelClickHandler = () => {
        this._projectCardWithEmptyTitle = false;
        this.setState(this.emptyState);
        this.props.dispatch(actions.closeModal());
        this.props.setTitleUpdate(false);

    }

    handleUserChange = (selectedUser) => {
      this.setState({ selectedUser });
    }

    scrollUserToBottom = () => {
      this.getOptions(true, this.state.searchCuratorText)
    };

    clearUserSearchOptions = () => {
      this.setState({
          userSuggestionsOffset: 0,
          userOptions: []
      })
    };

    getOptions = (isScrollToBottom, input) => {
      let isChanged = input !== this.state.searchUserText;
      this.setState({
          isLoadingUserExternally: true,
          searchUserText: input,
          userSuggestionsOffset: isScrollToBottom && !isChanged ? this.state.userSuggestionsOffset + this.state.userSuggestionsLimit : 0
      }, () => {
          usersv2.searchForUsersData({
            q: input,
            limit: this.state.userSuggestionsLimit || 10,
            offset:this.state.userSuggestionsOffset || 0,
            fields: 'id,first_name,last_name,email'
          }).then(data => {
              let currentOptions = this.state.unselectedUsers;
              let unselectedUsers = data.users.map((item)=>{
                let label = item.firstName ? item.firstName+' ' : '';
                    label += item.lastName ? item.lastName : '';
                    label += `(${item.email})`;
                return {value: item.id, label: label}
              });
              unselectedUsers = isScrollToBottom ? currentOptions.concat(unselectedUsers) : unselectedUsers;

              this.setState({
                  unselectedUsers,
                  isLoadingUserExternally: false
              })
          })
          .catch(err => console.error("Error in Content.EditModal.getOptions.searchForUsersData", err));
      });
    };

    sortIds(permissions) {
      const permissionsIds = permissions.map(permission => permission.id);
      return permissionsIds;
    }

    saveClickHandler() {
      let card = {
        ...this.props.contents.editingContent,
        teams_with_permission_ids: this.sortIds(this.props.contents.editingContent.teamsPermitted),
        users_with_permission_ids: this.sortIds(this.props.contents.editingContent.usersPermitted)
      };
        const {
          cardLanguages,
          cardLanguageData,
          selectedCardLanguage,
          showMessageForExistingTextCard,
          updateMessageForExistingTextCardAfterUpdating,
          userTaxonomyTopics,
          onboardingVersion,
          selectedNode,
          cardMessage,
          isOnlyThumbnailImage,
          shouldAwardSkills
        } = this.state;

        let currentDuration = card && card.eclDurationMetadata && card.eclDurationMetadata.calculated_duration;
        if (!this._message.value.trim()) {
            if (card.resource && card.resource.title) {
                this._message.value = card.resource.title;
            } else {
                this._message.focus();
                this.props.dispatch(overlayActions.showWarning('Field Title is required!', 'Validation Error'));
                closeWarning(this.props.dispatch);
                return
            }
        } else if(isTextCard(card) && this._message.value.length > 256) {
          this._message.focus();
          this.props.dispatch(overlayActions.showWarning('Maximum 256 characters allowed in the Title.', 'Validation Error'));
          closeWarning(this.props.dispatch);
          return
        }

        // key 'selectedCardBiaLevel' is attached to card Obj whenever user changed the level from DD.
        const hasChangedBiaLevel = card.hasOwnProperty('selectedCardBiaLevel');
        // skip the null value because for 'No level' we are getting card.skillLevel = null
          const checkCardBiaLevel = !card.skillLevel && card.skillLevel !== null;

        if (
          this.props.isBiaConfigEnabled &&
          (( hasChangedBiaLevel && !card.selectedCardBiaLevel) ||
            (!hasChangedBiaLevel && checkCardBiaLevel))
        ) {
          this.props.dispatch(
            overlayActions.showWarning('Please Select Skills Level first.', 'Validation Error')
          );
          closeWarning(this.props.dispatch);
          return;
        }
        let topics;
        if(this.state.mandatoryFields.smartCardTags){
          if (card.tags.length > 0) {
            topics = card.tags.map(tag => tag.name);
          } else {
            this.props.dispatch(
              overlayActions.showWarning('Tag is required!', 'Validation Error')
            );
            closeWarning(this.props.dispatch);
            return;
          }
        }else{
          topics = card.tags && card.tags.map((tag)=> {
            return tag.name;
        });
        }
        let channel_ids = card.channels.map((channel) => {
            return channel.id;
        });
        const nonCuratedChannelIds = card.nonCuratedChannelIds
        channel_ids = new Set([...channel_ids, ...nonCuratedChannelIds])
        if(this.state.resource.id) {
            card.resource_id = this.state.resource.id;
        }
        if(card.cardType === QUIZ){
          card.title = cardMessage;
        }

        if (isTextCard(card) || card.cardType === PROJECT) {
          card.title = this._message.value;
          card.message = card.cardMessage;
        } else if (isUploadTypeCard(card) || checkIfScormCard(card) || checkIfLiveCard(card)) {
           /**
           * Title and message are swapped intentionally based on the backend changes
           */
          card.title = card.cardMessage;
          card.message = this._message.value;
        } else {
          card.message = this._message.value;
        }

        // we need to send null in payload when selected level is No Level
        if(card.selectedCardBiaLevel === noLevelOptions().value){
          card.card_metadatum_attributes = {level: null}
        }else if (card.selectedCardBiaLevel) {
          card.card_metadatum_attributes = {level: card.selectedCardBiaLevel}
        }
        if(this.props.isBiaConfigEnabled) {
          const cardLevel = card.selectedCardBiaLevel || card.skillLevel;
          const selectedIncorrectLevel = (window.__edOrgData.proficiencyLevels || []).find(level => {
            return level.hidden &&  level.name === cardLevel;
          });
          const selectedNoLevel = (cardLevel === noLevelOptions().value || cardLevel === null)
          if((selectedNoLevel && !displayNoLevelInProficiencyLevels()) || selectedIncorrectLevel) {
            const cardLevelName = (cardLevel && (cardLevel.name || cardLevel.label)) || noLevelOptions().label;
            this.props.dispatch(overlayActions.showWarning(translatr('common.common', 'pleaseSelectSupportedLevelInFieldByPortal', { field: translatr('common.common', 'level'), name: cardLevelName }), 'Validation Error'));
            closeWarning(this.props.dispatch);
            return;
          }
        }

        const userTaxonomyTopicsToBeSent = userTaxonomyTopics.map(skill => {
          const {topic_id, path, label, topic_label} = skill;
          const finalLabel = label || topic_label;
          return { topic_id, path, label: finalLabel, topic_label: finalLabel };
        });

        const selectedNodeToSendInAPI = selectedNode.map(node => ({
          label: node.label || node.topic_label,
          topic_label: node.label || node.topic_label,
          path: node.path,
          topic_id: node.id
        }));

        card.user_taxonomy_topics = ['v3', 'FS-onboarding'].includes(onboardingVersion)
          ? selectedNodeToSendInAPI
          : userTaxonomyTopicsToBeSent;

        if (Permissions.has('AWARD_SKILLS')) {
          const cardMetadatum  = card.cardMetadatum ;
          if (card.cardMetadatum) {
            delete cardMetadatum.awardSkills;
          }
          card.card_metadatum_attributes = {
            ...card.card_metadatum_attributes,
            award_skills: !userTaxonomyTopicsToBeSent.length ? false : shouldAwardSkills
          }
        }

        //Exclude content attributes
        if (card.cardMetadatum) {
          const cardMetadatum = card.cardMetadatum;

          card.card_metadatum_attributes = {
            ...card.card_metadatum_attributes,
            exclude_from_search: cardMetadatum.excludeFromSearch || false,
            exclude_from_recommendation: cardMetadatum.excludeFromRecommendation || false
          }
        }

        card.topics = topics ? topics : [];
        card.channel_ids = channel_ids ? channel_ids: [];
        card.imageInput = this.state.imageInput;
        card.duration = this.calculateSeconds(this._duration.inputElement.value) === null ? currentDuration : this.calculateSeconds(this._duration.inputElement.value);
        let new_author_id = this.state.selectedUser && this.state.selectedUser.value;

        if (card.author_id !== new_author_id && new_author_id !== null){
            card.author_id = new_author_id
        }
        if (card.is_public) {
            card.is_public = card.isPublic.toString();
        }
        let newDuration = this.calculateSeconds(this._duration.inputElement.value);
        if(card.teams && card.teams.length) {
            card.team_ids = card.teams.map(team => team.id);
        }
        if (newDuration === 0) {
          this.props.dispatch(overlayActions.showWarning('Duration cannot be zero', 'Validation Error'));
          closeWarning(this.props.dispatch);
          return
        }
        if(this.state.mandatoryFields.smartCardDuration && card.duration === (null || 0)){
          this.props.dispatch(overlayActions.showWarning('Duration is required!', 'Validation Error'));
          closeWarning(this.props.dispatch);
          return;
        }
        if(this.state.userTaxonomyTopics.length > this.userTaxonomyTopicsMaxLimit) {
            this.props.dispatch(overlayActions.showWarning('Amount of taxonomy topics exceeded!', 'Validation Error'));
            closeWarning(this.props.dispatch);
            return;
        }
        if (this.state.isUniqueCodeChanged) {
            card.unique_code = this.state.newUniqueCode || null;
        }

          card.language = selectedCardLanguage;
        if(card.resource && cardLanguageData.resource){
          card.resource = cardLanguageData.resource;
        }

        if (checkIfTextCard(card)) {
          const isThumbnailImageBoolValue = typeof isOnlyThumbnailImage == 'boolean';
          if (this.showLXMediaHub) {
            card?.imageInput?.file?.signed_id && (card.media = card?.imageInput?.file?.signed_id);
            isThumbnailImageBoolValue && (card.is_only_thumbnail_image = isOnlyThumbnailImage);
          } else if (Object.keys(card.imageInput).length && isThumbnailImageBoolValue) {
            card.imageInput.file.is_only_thumbnail_image = isOnlyThumbnailImage;
          } else {
            const index = card.filestack && card.filestack.findIndex(x => x.is_cover_image);
            if (index >= 0 && isThumbnailImageBoolValue) {
              card.filestack[index].is_only_thumbnail_image = isOnlyThumbnailImage;
            }
          }
        }

        card.card_type = card.cardType;
        card.card_subtype = card.cardSubtype
        //For CKEditor filestack object Handling
        card.message = updateMessageForCKEditor(card);

        const isQuizCard = card.card_type === 'quiz';
        let quizPayload = {};
        if (isQuizCard) {
          quizPayload = actions.getQuizCardPayload(card);
        }

        delete card.cardTitle;
        delete card.cardMessage;
        delete card.htmlContentFileStackArray;
        delete card.uniqueCode;
        delete card.userTaxonomyTopics
        delete card.selectedCardBiaLevel
        delete card.languages
        delete card.cardType;
        delete card.cardSubtype;

        // Need `cardMetadatum` key when updating Article card and TextCard
        if (!checkIfArticleCard(card) && !checkIfTextCard(card)) {
          delete card.cardMetadatum;
        }

        if (checkIfScormCard(card)) {
          delete card.resource_id
        }

        const payload = isQuizCard ? quizPayload : card;

        if (currentDuration !== 0 && newDuration !== null && currentDuration !== newDuration) {
          this.props.dispatch(actions.openConfirmModal(payload));
          return;
        }

        this.props.dispatch(actions.saveEditContent(payload,this.showLXMediaHub));
        if(showMessageForExistingTextCard && !updateMessageForExistingTextCardAfterUpdating) {
            this.setState({
              updateMessageForExistingTextCardAfterUpdating: true
            });
        }
        this.props.setTitleUpdate(false);

    }
    handleImageChange = (e) => {
        this.setState({imageInput : e});
    }

    getUploadMeta = (item) => {
      const uploadMeta = {
        allowedMediaMimeTypes: window.__edOrgData?.allowedMediaMimeTypes?.image || ['image/*'],
        restrictMediaType: { video: false },
        isUGC: true,
        callback: this.handleImageChange,
        uploadParams: {
          objectType: UPLOAD_OBJECT_TYPE.CARD
        }
      };
      if (!checkIfScormCard(item) && (item?.resource?.id || this.state.resource_id)) {
        uploadMeta.uploadParams.uploadType = UPLOAD_TYPE.RESOURCE;
      } else {
        uploadMeta.uploadParams.uploadType = UPLOAD_TYPE.MEDIA;
      }
      return uploadMeta;
    }

    calculateFromSeconds = (seconds) =>  {
        if (seconds > 3599940){
          return '';
        }
        let hours = Math.floor(seconds / 3600);
        let total_hours_converted_to_minutes = (hours * 60);
        let mins = Math.floor((seconds / 60) - total_hours_converted_to_minutes);

        return `${hours}hrs ${mins}mins`;
    };

    calculateSeconds = (string) => {
        if (string) {
          let timeArr = string.split(':')
            let hrs = parseInt(timeArr[0]);
            let mins = parseInt(timeArr[1]);
            let hrSeconds = 0;
            let minSeconds = 0;
            if (!isNaN(hrs)) {
                hrSeconds = hrs * 3600;
            }
            if (!isNaN(mins)) {
                minSeconds = mins * 60;
            }
            const total_seconds = hrSeconds + minSeconds
            if (total_seconds > 3599940){
              return null
            }
            return total_seconds;
        } else {
            return null;
        }
    };

    linkChangeHandler = () => {
        if(this.checkEmptyLink()){
            return;
        }
        let isUrl = isUrlValid(this._link.value.trim(), {require_valid_protocol: false});
        if(!isUrl) {
            this.setState({
                disableSave: true,
                linkError: "Please give a valid link"
            });
            return;
        }
        this.setState({disableSave: true});
        if(this.state.checkDuplicate) {
            let payload = {
                resource_url: this._link.value,
                limit: 10,
                offset: 0
            };
            checkDuplicateCard(payload).then((data) => {
                if(data.cards.length) {
                    this.setState({linkError: 'You can not create a SmartCard with this URL as a SmartCard already exists with the same URL', disableSave: true});
                } else {
                    this.linkCreateResource();
                }
            });
        } else {
            this.linkCreateResource();
        }
    }

    linkCreateResource = () => {
        createResource(this._link.value, false).then((resource) => {
            let {cardLanguageData} = this.state;
            cardLanguageData.resource = resource;
            this.setState(
              {
                resource,
                disableSave: false,
                linkError: '',
                cardLanguageData
              },()=>{
                this.messageChangeHandle(resource.description || '');
                this.props.dispatch(actions.updateCardData({...this.props.contents.editingContent, ...{resource: resource}}));
              }
            );
            this.checkEmptyLink();
        }).catch((error) => {
            this.setState({linkError: 'Please give a valid link'});
            this.checkEmptyLink();
        });
    }

    checkEmptyLink = () => {
        if(!this._link.value.trim()) {
            this.setState({
                disableSave: true,
                linkError: "Link can't be empty"
            });
            return true;
        }
        return false;
    }

    componentWillReceiveProps(nextProps) {
        if(nextProps.contents.editingContent){
            this.languageDetails = nextProps.orgLanguages || [];
            const card = nextProps.contents.editingContent;
            let {showMessageForExistingTextCard, updateMessageForExistingTextCardAfterUpdating, userTaxonomyTopics} = this.state;
            const selectedCardLanguage = card && card.language || 'un';
            const cardLanguageData = getCardLanguageData(selectedCardLanguage, card) || {};
            const cardLanguages = getCardLanguages(
                                    this.languageDetails,
                                    card && card.languages || [],
                                    nextProps.isNonUgc,
                                    selectedCardLanguage
                                  );
            let selectedUser = null;
            if(card.author) {
              selectedUser = {value: card.author.id, label: card.author.fullName}
            }
            let message = nextProps.isTitleUpdated ? card.cardTitle : getStandardizedTitle(card, true) || (cardLanguageData && cardLanguageData.message);
            const isCurrentTextCard = isTextCard(card);
            const {editingContent} = this.props.contents;
            if(card.cardType === PROJECT && card.title) {
                if(cardLanguageData && cardLanguageData.title === '') {
                    cardLanguageData.title = card.title;
                }
                message = (cardLanguageData && (cardLanguageData.title || cardLanguageData.message));
            }
            if(card.id !== (editingContent && editingContent.id)) {
                showMessageForExistingTextCard = isCurrentTextCard && !card.cardTitle;
            } else if(updateMessageForExistingTextCardAfterUpdating && showMessageForExistingTextCard) {
                showMessageForExistingTextCard = false;
            }
            if(this._projectCardWithEmptyTitle) {
                message = '';
            }

            const topicsFromResp = !!card.userTaxonomyTopics
              ? card.userTaxonomyTopics.map(topic => ({
                  ...topic,
                  value: topic.path || topic.topic_id
                }))
              : [];
            const cardMetadatum = card.cardMetadatum;
            this.setState({
              cardMessage: message || (this._message && this._message.value),
              selectedCardLanguage,
              cardLanguageData,
              cardLanguages,
              selectedUser,
              showMessageForExistingTextCard,
              resource: cardLanguageData.resource || {},
              userTaxonomyTopics: nextProps.contents.showEditModal
                ? !!userTaxonomyTopics.length
                  ? userTaxonomyTopics
                  : topicsFromResp
                : [],
              shouldAwardSkills: !!cardMetadatum ? cardMetadatum.awardSkills : false,
              excludeFromSearch: !!cardMetadatum?.excludeFromSearch,
              excludeFromRecommendation: !!cardMetadatum?.excludeFromRecommendation
            });
        }
    }

    languageChangeHandler=(e)=>{
        const selectedCardLanguage = e.target.value;
        let item = this.props.contents.editingContent;
        let cardLanguageData = this.state.cardLanguageData;
        if(this.props.isNonUgc){
            cardLanguageData = getCardLanguageData(e.target.value, item) || {};
        }else{
            cardLanguageData.language = selectedCardLanguage;
        }

        if(cardLanguageData && cardLanguageData.resource && cardLanguageData.resource.url || item.resource && item.resource.url){
          this._link.value = cardLanguageData && cardLanguageData.resource && cardLanguageData.resource.url || item.resource && item.resource.url;
        }
        let message = cardLanguageData && cardLanguageData.message;
        if((item.cardType === PROJECT && item.title) || isTextCard(item) || isUploadTypeCard(item)) {
            message = cardLanguageData && (cardLanguageData.title || cardLanguageData.message);
        }
        this.setState({
            selectedCardLanguage,
            cardMessage : message || (this._message && this._message.value),
            cardLanguageData,
            resource: cardLanguageData && cardLanguageData.resource || (this.state.resource)
        });
        this.props.dispatch(actions.updateCardData({...this.props.contents.editingContent, ...{language: selectedCardLanguage}}));
    }

    messageChangeHandle=(cardMessage)=>{
        let card = this.props.contents.editingContent;
        let {cardLanguageData} = this.state;
        if(card.cardType === PROJECT && card.title) {
            cardLanguageData.title = cardMessage;
        } else if(isTextCard(card)) {
          cardLanguageData.title = cardMessage;
        } else {
            cardLanguageData.message = cardMessage;
        }
        this.setState({
            cardMessage,
            cardLanguageData
        });
        if(card.cardType === PROJECT && card.title) {
            if(!cardMessage.trim()){
                this._projectCardWithEmptyTitle = true;
            } else {
                this._projectCardWithEmptyTitle = false;
            }
            this.props.dispatch(actions.updateCardData({...this.props.contents.editingContent, ...{title: cardMessage}}));
        } else {
            this._projectCardWithEmptyTitle = false;
            this.props.dispatch(actions.updateCardData({...this.props.contents.editingContent, ...{cardTitle: cardMessage}}));
        }
       if(!this.props.isTitleUpdated) {
        this.props.setTitleUpdate(true);
       }
    }

    queryTaxonomyTopics = debounce((input, callback) => {
        if(this.state.userTaxonomyTopics.length < this.userTaxonomyTopicsMaxLimit && input.length > 0){
          queryTopics(input, undefined, undefined, 'id,label,name').then((data) => {
            if(this.state.userTaxonomyTopics.length >= this.userTaxonomyTopicsMaxLimit){
              this.runCallbackwithoutSuggestion(callback)
              return;
            }
            const uniqueSuggestions = makeSuggestionsUniq(data.topics,this.state.userTaxonomyTopics);
            callback(uniqueSuggestions);
          });
          callback(uniqueSuggestions);
        }
        else{
          this.runCallbackwithoutSuggestion(callback)
        }
      }, 3000)

    runCallbackwithoutSuggestion(callback){
    callback([]);
    }

    handleTaxonomyTopicsChange(topics) {
        const {userTaxonomyTopics} = this.state
        if((topics.length <= this.userTaxonomyTopicsMaxLimit) || (topics.length < userTaxonomyTopics.length)){
          this.setState({userTaxonomyTopics: topics});
        }
    }

    handleShouldAwardSkills() {
      this.setState({ shouldAwardSkills: !this.state.shouldAwardSkills});
    }

    handleDomainChange= (selectedDomain) => {
        this.setState({ selectedDomain });
    }

    handleNodeChange= (selectedNode) => {
        this.setState({ selectedNode });
    }

    handleLevelChange = (e) => {
      const value = e.target.value;
      this.props.dispatch(
        actions.updateCardData({
          ...this.props.contents.editingContent,
          ...{ selectedCardBiaLevel: value }
        })
      );
    }

    handleExcludeContentChange = (field) => (e) => {
      this.props.dispatch(actions.updateCardData({
        ...this.props.contents.editingContent,
        cardMetadatum: {
          ...this.props.contents.editingContent.cardMetadatum,
          [field]: e.target.checked
        }
      }));
    }

    checkHandler = (e) => {
      this.setState({
        isOnlyThumbnailImage: e.target.checked
      });
    };

    updateUniqueCode = () => {
        const card = this.props.contents.editingContent;
        const newUniqueCode = this._uniqueCode.value.trim();
        this.setState(prevState => ({
            isUniqueCodeChanged: newUniqueCode !== card.uniqueCode,
            newUniqueCode
        }))
      };
    
    isMediaOrThumbnailPresent =(obj)=>{
      return (
        (obj?.media && Object.keys(obj.media).length) ||
        (obj?.thumbnail && Object.keys(obj.thumbnail).length)
      );
    } 

    getCardSubType = (isFileAttached,dataItem,card) => {
      let isVideo = !!(isFileAttached && ~dataItem?.mimetype.indexOf('video/'));
      let isImage = !!(isFileAttached && ~dataItem?.mimetype.indexOf('image/'));
      let isFile = isFileAttached && !isVideo && !isImage;
      let cardSubtype = isVideo ? 'video' : (isImage ? 'image': (isFile ? 'file': (card && card.cardSubtype)));
      return cardSubtype;
    }

    getKeyViaMimeType = (imageItem) => {
      return (
        imageItem?.mimetype &&
        (~imageItem.mimetype.indexOf('video/') ||
          ~imageItem.mimetype.indexOf('audio/') ||
          ~imageItem.mimetype.indexOf('/pdf') ||
          ~imageItem.mimetype.indexOf('application/'))
      );
    }

    getLXMediaHubImage = (cardItem) => {
      let isFileAttached = cardItem?.media && Object.keys(cardItem.media).length;
      let cardSubtype = this.getCardSubType(isFileAttached,cardItem?.media,cardItem); 
      let isScormCard = isFileAttached && checkIfScormCard(cardItem);
      let imageUrl;
      let isMediaOrThumbnailPresent = this.isMediaOrThumbnailPresent(cardItem);
      let isOnlyThumbnail = checkIfTextCard(cardItem) && cardItem?.media?.is_only_thumbnail_image;
      if(cardItem) {
          if (isMediaOrThumbnailPresent){
              let key = this.getKeyViaMimeType(cardItem?.media) ? 'thumbnail' : 'media';
              if(~cardItem[key]?.mimetype.indexOf('image/')){ 
                  imageUrl = cardItem[key]?.url;
              }
              if(isScormCard){
                  imageUrl = cardItem?.thumbnail?.url;
              }
          }
          else if (cardItem?.fileResources?.length) {
            // TODO: Identify the flow and confirm the approach
              let images = cardItem.fileResources.filter(function(f_r) { return f_r.fileType === 'image' });
              let image  = images[0];
              if(image) {
                  imageUrl = image.fileUrl;
              }
          } else if (cardItem?.resource?.imageUrl) {
              imageUrl = cardItem.resource.imageUrl;
          }
      }
      let allowReplace = !!(cardItem && (cardItem.cardSubtype !== "video" || isMediaOrThumbnailPresent || (checkIfArticleCard(cardItem) && cardItem.resource)));
      return {
        isFileAttached,
        cardSubtype,
        imageUrl,
        allowReplace,
        isScormCard,
        isOnlyThumbnail
      }
    }

    getFileStackImage = (cardFilestackItem) =>{
      let isFileAttached = cardFilestackItem?.filestack && !!cardFilestackItem.filestack.length && Object.keys(cardFilestackItem.filestack?.[0] || {}).length > 0;
      let cardSubtype = this.getCardSubType(isFileAttached,cardFilestackItem?.filestack[0],cardFilestackItem);
      let isScormCard = isFileAttached && cardFilestackItem?.filestack[0]?.scorm_course;
      let isOnlyThumbnail= checkIfTextCard(cardFilestackItem) && !!cardFilestackItem?.filestack[0]?.is_only_thumbnail_image;
      let imageUrl;
      if(cardFilestackItem) {
          if (cardFilestackItem?.filestack?.length){
              let num = this.getKeyViaMimeType(cardFilestackItem?.filestack[0]) ? 1 : 0;
              if(~cardFilestackItem?.filestack[num]?.mimetype?.indexOf('image/')){
                  imageUrl = cardFilestackItem.filestack[num]?.url; // Video card, thumbnail not coming in array
              }
          }
          if(isScormCard){
              imageUrl = cardFilestackItem?.filestack[1]?.url;
          }
          else if (cardFilestackItem?.fileResources?.length) {
              let images = cardFilestackItem.fileResources.filter(function(f_r) { return f_r.fileType === 'image' });
              let image  = images[0];
              if(image) {
                  imageUrl = image.fileUrl;
              }
          } else if (cardFilestackItem?.resource?.imageUrl) {
              imageUrl = cardFilestackItem.resource.imageUrl;
          }
      }
      imageUrl = getCoverImageForTextCard(cardFilestackItem, imageUrl);
      let allowReplace = !!(cardFilestackItem && (cardFilestackItem.cardSubtype !== "video" || cardFilestackItem?.filestack?.length || (checkIfArticleCard(cardFilestackItem) && cardFilestackItem.resource)));
      return {
        isFileAttached,
        cardSubtype,
        imageUrl,
        allowReplace,
        isScormCard,
        isOnlyThumbnail
      }
    }

    render() {
        let channelSuggestions;
        let details;
        let item = this.props.contents.editingContent;
        if (item && item.channelSuggestions) {
          channelSuggestions = item.channelSuggestions.map(suggestionObj => {
            return suggestionObj.text;
          });
        } else {
          channelSuggestions = [];
        }
        if (this.showLXMediaHub && this.isMediaOrThumbnailPresent(item)) {
          details = this.getLXMediaHubImage(item);
        } else {
          details = this.getFileStackImage(item);
        }
        const { isFileAttached, cardSubtype, isScormCard, imageUrl, allowReplace,isOnlyThumbnail } = details;
        const cardType = item && ((item.cardType === MEDIA) ? item.cardSubtype : item.cardType);
        const isCardTypeScormOrCourse = [COURSE, SCORM].includes(cardType);
        let CardDisplayType =  cardType === TRAINING ? VILT_DISPLAY : (isCardTypeScormOrCourse ? cardType : convertContentType(cardType, cardSubtype));
            CardDisplayType = (CardDisplayType === "poll" && !!item.isAssessment) ? 'quiz' : CardDisplayType;
        let imageBlock;
        let scormimage = isScormCard ? imageUrl : this.state.resource.imageUrl;
        imageBlock = (
          <FormGroup>
            <Col xs={12} md={3} lg={2}>
              <label>{translatr('common.common', 'image')}</label>
            </Col>
            <Col xs={12} md={9} lg={10}>
              <ImageUpload
                defaultValue={scormimage || imageUrl}
                onChange={this.handleImageChange}
                isFileStack={true}
                allowReplace={allowReplace}
                channelImg=""
                showLXMediaHub={this.showLXMediaHub}
                {...(this.showLXMediaHub && { uploadMeta: this.getUploadMeta(item) })}
              />
              {isTextCard(item) && (imageUrl || !!Object.keys(this.state.imageInput).length) && (
                <div>
                  <Checkbox
                    checked={this.state.isOnlyThumbnailImage}
                    defaultChecked={isFileAttached && isOnlyThumbnail}
                    onChange={this.checkHandler}
                  >
                    <span>{translatr('common.common', 'UseOnlyForThumbnail')}</span>
                  </Checkbox>
                  <div className="display-flex">
                    <div className="rubix-icon icon-fontello-info-circled-alt" />
                          <div className="use-thumbnail-info">{translatr('common.common', 'InfoUseOnlyForThumbnail')}</div>
                  </div>
                </div>
              )}
            </Col>
          </FormGroup>
        );

        let allTags = (item && item.tags) || [];
        allTags.map(function(tag, index){
            tag.text = tag.name;
        });

        let allChannels = (item && item.channels) || [];
        allChannels.map(function(tag, index){
            if(tag.label){
                tag.label = unescape(tag.label);
                tag.name = tag.label;
                tag.text = tag.name;
            }
        });
        let duration = item && item.eclDurationMetadata &&
            item.eclDurationMetadata.calculated_duration && this.calculateFromSeconds(item.eclDurationMetadata.calculated_duration);

        const {selectedCardLanguage, cardMessage, cardLanguages, showMessageForExistingTextCard} = this.state;

        const isMultiQuiz = this.props.orgConfigs.find((item)=>{
            return (item.name === "multiple_questions_quiz")
        });

        const isQuiz = isMultiQuiz && cardType === QUIZ;
        const uniqueCodeConfig = this.props.user.userData.organization.configs.find((item)=>{
            return (item.name === "uniquecode_card_creation")
          });
        const uniqueCodeEnabled = (uniqueCodeConfig && uniqueCodeConfig.value) || false;
        const uniqueCode = item && item.uniqueCode
        const displayCardType = item ? (item.cardTypeDisplay === 'Vilt') ? 'Live Event' : item.cardTypeDisplay : '';
        const showAwardSkills = Permissions.has('AWARD_SKILLS') && !!this.state.userTaxonomyTopics.length;
        const skillLevel = item && item.skillLevel
        const selectedBiaLevel =
          item &&
          (item.hasOwnProperty('selectedCardBiaLevel')
            ? item.selectedCardBiaLevel
            : skillLevel);

        function getDefaultValueForLang(languages, key) {
          function extractLangObject(langToFind) {
            const foundLangObj = languages
              .find(language => language.language === langToFind);
            return foundLangObj && foundLangObj[key];
          }
          return extractLangObject(selectedCardLanguage) || extractLangObject(item.language) || "";
        }

        const isMultiQuestionPoll = (item) => { return CardDisplayType === 'poll' && item.poll && Array.isArray(item.poll.questions) }
        const isSingleQuestionPoll = (item) => { return CardDisplayType === 'poll' && !isMultiQuestionPoll(item) && item.quizQuestionOptions }
        return (
          <Modal
            aria-label="Edit content"
            show={this.props.contents.showEditModal}
            onHide={this.cancelClickHandler}
            id="smartcard-edit-modal"
          >
            <ConfirmEditModal />
            <Modal.Header closeButton>
              <Modal.Title>{translatr('common.common', 'editContent')}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form horizontal className="edit-channel-form">
                  <FormGroup controlId="formEditContentLink">
                    <Col sm={2}>
                      <ControlLabel bsClass="left">{translatr('common.common', 'cardLanguages')}</ControlLabel>
                    </Col>
                    <Col sm={10}>
                      <select
                        className="form-control"
                        aria-label={translatr('common.common', 'selectCardLanguageFromList')}
                        onChange={e => {
                          this.languageChangeHandler(e);
                        }}
                        value={selectedCardLanguage}
                      >
                        {cardLanguages.map(language => {
                          return <option value={language.value}>{language.displayName}</option>;
                        })}
                      </select>
                    </Col>
                  </FormGroup>
                <FormGroup controlId="formEditContentLink">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">{translatr('common.common', 'link')}</ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <input
                      type="text"
                      ref={node => (this._link = node)}
                      aria-label={translatr('common.common', 'enterLink')}
                      onChange={this.linkChangeHandler}
                      className={
                        !(item && item.resource && !isScormCard)
                          ? 'disabled-input form-control'
                          : 'form-control'
                      }
                      defaultValue={item && item.resource ? item.resource.url : ''}
                      disabled={!(item && item.resource && !isScormCard)}
                    />
                    {!!this.state.linkError && <p>{this.state.linkError}</p>}
                  </Col>
                </FormGroup>
                <FormGroup controlId="formEditContentType">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">{translatr('common.common', 'type')}</ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <input
                      type="text"
                      className="disabled-input form-control"
                      aria-label={translatr('common.common', 'enterType')}
                      defaultValue={displayCardType}
                      disabled
                    />
                  </Col>
                </FormGroup>
                {imageBlock}
                <FormGroup controlId="formEditContentMessage">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">
                    {translatr('common.common', 'title')}<span style={{ color: 'red', fontSize: '15px' }}>*</span>
                    </ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <textarea
                      ref={node => (this._message = node)}
                      className="editing-message form-control"
                      value={unescape(cardMessage)}
                      aria-label={translatr('common.common', 'enterTitleMandatory')}
                      onChange={e => {
                        this.messageChangeHandle(e.target.value);
                      }}
                      disabled={isQuiz}
                    />
                    {showMessageForExistingTextCard && (
                      <span>{translatr('common.common', 'pleaseAddATitleToSaveChanges')}</span>
                    )}
                  </Col>
                </FormGroup>

                {CardDisplayType !== 'poll' && (
                  <FormGroup controlId="formEditTaxonomyTopics">
                    <Col sm={2}>
                      <ControlLabel bsClass="left">{translatr('common.common', 'userSkillsMaximumLimit', {userTaxonomyTopicsMaxLimit: this.userTaxonomyTopicsMaxLimit})}</ControlLabel>
                    </Col>
                    <Col sm={10}>
                      {['v3', 'FS-onboarding'].includes(this.state.onboardingVersion) ? (
                        <SociativeTaxonomySelector
                          handleDomainChange={this.handleDomainChange.bind(this)}
                          handleNodeChange={this.handleNodeChange.bind(this)}
                          userTaxonomyTopics={this.state.userTaxonomyTopics}
                        />
                      ) : (
                        <Async
                          isMulti
                          loadOptions={this.queryTaxonomyTopics.bind(this)}
                          value={this.state.userTaxonomyTopics}
                          aria-label={translatr('common.common', 'selectSkills')}
                          placeholder={translatr('common.common', 'selectSkills')}
                          onChange={this.handleTaxonomyTopicsChange.bind(this)}
                          isSearchable={
                            this.state.userTaxonomyTopics.length < this.userTaxonomyTopicsMaxLimit
                          }
                        />
                      )}
                    </Col>
                    {showAwardSkills && (
                      <div>
                        <input
                          className="margin-left-25"
                          onChange={this.handleShouldAwardSkills.bind(this)}
                          aria-label={translatr('common.common', 'selectIfShouldAwardSkills')}
                          checked={this.state.shouldAwardSkills}
                          type="checkbox"
                        />
                        &nbsp;&nbsp; {translatr('common.common', 'ShouldAwardSkillsOnContentCompletion')}
                      </div>
                    )}

                  </FormGroup>
                )}

                {this.props.isBiaConfigEnabled && (
                  <FormGroup controlId="formEditContentLink">
                    <Col sm={2}>
                      <ControlLabel bsClass="left">
                        {translatr('common.common', 'level')}<span className='mandatory-field'>*</span>
                      </ControlLabel>
                    </Col>
                    <Col sm={10}>
                      <SkillLevelDropDown
                        onChangeHandler={this.handleLevelChange}
                        value={selectedBiaLevel}
                        initValue={skillLevel}
                      />
                    </Col>
                  </FormGroup>
                )}
                {isMultiQuestionPoll(item) && item.poll.questions.map((question, index) => (
                      <div>
                        <FormGroup controlId="formEditContentMessage">
                          <Col sm={2}>
                            <ControlLabel
                              bsClass="left">{translatr('common.common', 'question')} {index + 1}</ControlLabel>
                          </Col>
                          <Col sm={6}>
                            <input
                              className="disabled-input form-control"
                              defaultValue={Array.isArray(question.languages) ? getDefaultValueForLang(question.languages, 'question') : ''}
                              disabled
                            />
                          </Col>
                        </FormGroup>
                        {question.options && Array.isArray(question.options) &&
                          question.options.map((option, index) => {
                            return (
                              <FormGroup controlId="formEditContentMessage">
                                <Col sm={2}>
                                  <ControlLabel
                                    bsClass="left">{translatr('common.common', 'option')} {index + 1}</ControlLabel>
                                </Col>
                                <Col sm={6}>
                                  <input
                                    className="disabled-input form-control"
                                    defaultValue={Array.isArray(option.languages) ? getDefaultValueForLang(option.languages, 'option') : ''}
                                    disabled
                                  />
                                </Col>
                              </FormGroup>);
                          })
                        }
                      </div>
                    )
                  )
                }
                {isSingleQuestionPoll(item) && item.quizQuestionOptions.map((Options, index) => (
                      <FormGroup controlId="formEditContentMessage">
                        <Col sm={2}>
                          <ControlLabel bsClass="left">{translatr('common.common', 'option')} {index + 1}</ControlLabel>
                        </Col>
                        <Col sm={6}>
                          <input
                            className="disabled-input form-control"
                            defaultValue={Options.label}
                            disabled
                          />
                        </Col>
                      </FormGroup>
                    )
                  )
                }
                <FormGroup controlId="formEditContentTopics">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">{translatr('common.common', 'tags')}</ControlLabel>
                  </Col>
                  <Col sm={10} onClick={this.topicInputClickHandler.bind(this)}>
                    {item ? (
                      <ReactTagInput
                        ref={node => (this._topicInput = node)}
                        tags={allTags}
                        suggestions={item.topicSuggestions}
                        handleInputChange={this.topicInputChangeHandler.bind(this)}
                        handleAddition={this.topicAddHandler.bind(this)}
                        handleDelete={this.removeTopicClickHandler.bind(this)}
                        placeholder={translatr('common.common', 'addNewTag')}
                        minQueryLength={1}
                        allowDeleteFromEmptyInput={false}
                        autofocus={false}
                      />
                    ) : null}
                  </Col>
                </FormGroup>
                {(!item || (item && !item.hidden)) && (
                  <FormGroup controlId="formEditContentChannels">
                    <Col sm={2}>
                      <ControlLabel bsClass="left">{translatr('common.common', 'channels')}</ControlLabel>
                    </Col>
                    <Col sm={10} onClick={this.channelInputClickHandler.bind(this)}>
                      {item ? (
                        <ReactTagInput
                          ref={node => (this._channelInput = node)}
                          tags={allChannels}
                          suggestions={channelSuggestions}
                          handleInputChange={this.channelInputChangeHandler.bind(this)}
                          handleAddition={this.channelAddHandler.bind(this)}
                          handleDelete={this.removeChannelClickHandler.bind(this)}
                          placeholder={translatr('common.common', 'addNewChannel2')}
                          minQueryLength={1}
                          allowDeleteFromEmptyInput={false}
                          autofocus={false}
                          labelField={'name'}
                        />
                      ) : null}
                    </Col>
                  </FormGroup>
                )}
                <FormGroup controlId="formEditContentState">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">{translatr('common.common', 'state')}</ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <input
                      type="text"
                      className="disabled-input form-control"
                      value={item ? item.state : ''}
                      disabled
                    />
                  </Col>
                </FormGroup>
                <FormGroup controlId="formEditContentDate">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">{translatr('common.common', 'createDate')}</ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <input
                      type="text"
                      className="disabled-input form-control"
                      value={item ? item.createdAt.slice(0, 10) : ''}
                      disabled
                    />
                  </Col>
                </FormGroup>
                <FormGroup controlId="formEditContentDuration">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">
                      {translatr('common.common', 'duration2')}
                      <br />
                      <small>(HRS : MINS)</small>
                      {this.state.mandatoryFields.smartCardDuration && <span className='mandatory-field'>*</span>}
                    </ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <InputMask
                      ref={node => (this._duration = node)}
                      className="editing-message form-control"
                      placeholder={!!duration ? duration : translatr('common.common', 'enterTime')}
                      mask={[/\d/, /\d/, /\d/, ':', /\d/, /\d/]}
                      keepCharPositions={true}
                      pipe={this.autoCorrectedDatePipe}
                    />
                  </Col>
                </FormGroup>
                {this.props.user &&
                  this.props.user.userData &&
                  this.props.user.userData.permissions &&
                  this.props.user.userData.permissions.includes('CHANGE_AUTHOR') &&
                  item &&
                  !item.isPaid &&
                  !this.props.isNonUgc && (
                    <FormGroup controlId="formEditContentAuthor">
                      <Col sm={2}>
                        <ControlLabel bsClass="left">{translatr('common.common', 'changeAuthor')}</ControlLabel>
                      </Col>
                      <Col sm={10}>
                        <Select
                          value={this.state.selectedUser}
                          onChange={this.handleUserChange.bind(this)}
                          onMenuScrollToBottom={this.scrollUserToBottom}
                          options={this.state.unselectedUsers}
                          onBlur={this.clearUserSearchOptions}
                          onInputChange={this.getOptions.bind(this, false)}
                          isLoading={this.state.isLoadingUserExternally}
                          placeholder={translatr('common.common', 'selectUsers')}
                          aria-label={translatr('common.common', 'selectUserToChangeAsAuthor')}
                          noOptionsMessage={
                            ()=>this.state.isLoadingUserExternally ? translatr('common.common', 'loading2') : translatr('common.common', 'noResultsFound')
                          }
                          isClearable={true}
                        />
                      </Col>
                    </FormGroup>
                  )}
                <FormGroup controlId="formExcludeContent">
                  <Col sm={2}>
                    <ControlLabel bsClass="left">{translatr('common.common', 'excludeContent')}</ControlLabel>
                  </Col>
                  <Col sm={10}>
                    <div>
                      <Checkbox
                        checked={this.state.excludeFromSearch}
                        onChange={this.handleExcludeContentChange('excludeFromSearch')}
                        >
                        <span>{translatr('common.common', 'excludeFromSearch')}</span>
                      </Checkbox>
                      <Checkbox
                        checked={this.state.excludeFromRecommendation}
                        onChange={this.handleExcludeContentChange('excludeFromRecommendation')}
                        >
                        <span>{translatr('common.common', 'excludeFromRecommendations')}</span>
                      </Checkbox>
                    </div>
                    {translatr('common.common', 'excludeContentHint')}
                  </Col>
                </FormGroup>
                {uniqueCodeEnabled && (
                  <FormGroup controlId="formEditContentUniqueCode">
                    <Col sm={2}>
                      <ControlLabel bsClass="left">{translatr('common.common', 'customCourseCode')}</ControlLabel>
                    </Col>
                    <Col sm={10}>
                      <input
                        ref={node => (this._uniqueCode = node)}
                        className="form-control"
                        type="text"
                        onChange={this.updateUniqueCode}
                        aria-label={translatr('common.common', 'enterCustomCourseCode')}
                        defaultValue={uniqueCode}
                      />
                    </Col>
                  </FormGroup>
                )}
                {this.props.isNonUgc && <label>{translatr('content.main','NonUgcContentDataLostWarningMessage')}</label>}
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button onClick={this.cancelClickHandler.bind(this)}>{translatr('common.common', 'cancel')}</Button>
              {!isQuiz && (
                <Button
                  bsStyle="primary"
                  onClick={this.saveClickHandler.bind(this)}
                  disabled={this.state.disableSave}
                >
                  {translatr('common.common', 'save')}
                </Button>
              )}
            </Modal.Footer>
          </Modal>
        );
    }
}
