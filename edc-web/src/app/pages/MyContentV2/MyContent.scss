@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui.my-content-page {
  max-width: 75rem;
  margin: 0 auto;
}

@media only screen and (min-width: $breakpoint-sm) and (max-width: #{$breakpoint-lg - 1px}) {
  .ed-ui.my-content-page {
    padding: 0 1rem;
  }
}

.my-content-header {
  margin-bottom: rem-calc(16);
}

.my-content-container {
  display: flex;
  flex-direction: row;
  gap: rem-calc(16);
  justify-content: center;

  .my-content-card-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: rem-calc(16);

    h2.my-profile-v2_card-title {
      //temporary - probably the size should goes from component library?
      color: var(--ed-gray-7);
      font-size: var(--ed-font-size-lg) !important;
      font-weight: var(--ed-font-weight-semibold);
      line-height: rem-calc(27);
      margin: 0;
    }
  }

  .my-content-navigation {
    flex: 2;
  }

  .my-content-panel {
    flex: 7;
    display: flex;
    flex-direction: column;
    gap: rem-calc(16);

    .card-component_container {
      .content-list-pagination {
        padding-top: var(--ed-spacing-xs);
        float: right;
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .my-content-container {
    flex-direction: column;

    .my-content-navigation {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: rem-calc(8);
      width: 100%;

      .ed-input-container {
        max-width: 100%;
        width: 100%;
      }
    }
  }
}

@media (max-width: $breakpoint-lg) {
  .my-content-container {
    padding: 0 rem-calc(8);

    .my-content-navigation {
      flex: 3;
    }

    .my-content-panel {
      flex: 7;
    }
  }
}
