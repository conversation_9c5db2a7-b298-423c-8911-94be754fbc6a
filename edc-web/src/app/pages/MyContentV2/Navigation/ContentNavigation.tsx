import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Permissions } from '@utils/checkPermissions';
import StandalonePageNavigation from '@components/StandalonePageNavigation';
import { Navigation } from '@components/StandalonePageNavigation/types';
import { useContent } from '../ContentProvider';
import { translate } from '../utils';
import { useSelector } from 'react-redux';
import { getSection } from './constants';
import { SectionPath } from '../types';

const ContentNavigation = () => {
  const navigate = useNavigate();

  const { state: { selectedSection }, dispatch } = useContent();

  const edcastPricing = useSelector((state: any) => state.team.get('config')?.enable_smart_card_price_field);
  const sections: Array<Navigation<SectionPath>> = getSection(Permissions.has('DISMISS_ASSIGNMENT'), edcastPricing);

  const gotoSection = (section: Navigation<SectionPath>) => {
    dispatch({ type: 'SECTION_CHANGED', selectedSection: section.path })
    navigate(section.path !== "content" ? `/content/${section.path}` : "/content");
  }

  return <div className="my-content-navigation">
    <StandalonePageNavigation label={translate("DisplayDropdownLabel")} navigations={sections} selectedNavigationElement={selectedSection} gotoNavigationElement={gotoSection} />
  </div>
};

export default ContentNavigation;
