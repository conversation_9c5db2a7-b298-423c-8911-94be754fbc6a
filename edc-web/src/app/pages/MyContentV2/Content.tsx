import React, { useEffect } from 'react';
import { translate } from './utils';
import ContentNavigation from './Navigation/ContentNavigation';
import ContentPanel from '@components/ContentFilter/ContentPanel';
import { useContent } from '@pages/MyContentV2/ContentProvider';
import ContentStats from '@pages/MyContentV2/Statistics/ContentStats';
import { useSelector } from 'react-redux';

const Content = () => {
  const { dispatch, state: { selectedSection } } = useContent();
  const currentUserId = useSelector((state: any) => state.currentUser.get('id'));


  const assignmentCountShouldBeUpdated = useSelector((state: any) => state.assignments.get('assignmentCountShouldBeUpdated'));

  const isStatsVisible = selectedSection === 'content';

  const reloadStatistics = () => {
    dispatch({ type: "CONTENT_STATISTICS_CHANGED" });
  };

  useEffect(() => {
    if(assignmentCountShouldBeUpdated) {
      reloadStatistics();
    }
  }, [assignmentCountShouldBeUpdated]);

  return (
    <div className="ed-ui my-content-page">
      <div className="my-content-header">
        <h1>{translate('ContentTitle')}</h1>
      </div>
      <div className="my-content-container">
        <ContentNavigation />
        <div className="my-content-panel">
          {isStatsVisible && <ContentStats />}
          <ContentPanel
            authorId={currentUserId}
            selectedSection={selectedSection}
            cardWrapperCallback={reloadStatistics}
            showCreateButton
            showTitle
          />
        </div>
      </div>
    </div>
  );
};

export default Content;
