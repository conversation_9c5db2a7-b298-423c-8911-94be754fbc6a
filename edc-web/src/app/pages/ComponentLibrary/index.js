import './library.scss';

import PropTypes from 'prop-types';

// Components
import Alerts from 'centralized-design-system/src/Alerts/test';
import Accordion from 'centralized-design-system/src/Accordion/test';
import Buttons from 'centralized-design-system/src/Buttons/test';
import DatePicker from 'centralized-design-system/src/DatePickers/test';
import DatePickerV2 from 'centralized-design-system/src/DatePickerV2/test';
import Dividers from 'centralized-design-system/src/Dividers/test';
import DynamicInlineComponents from 'centralized-design-system/src/DynamicInlineComponents/test';
import Inputs from 'centralized-design-system/src/Inputs/test';
import Links from 'centralized-design-system/src/Links/test';
import Pagination from 'centralized-design-system/src/Pagination/test';
import Radio from 'centralized-design-system/src/Radio/test';
import RadioButtonV2 from 'centralized-design-system/src/RadioButtonV2/test';
import Switch from 'centralized-design-system/src/Switch/test';
import Tags from 'centralized-design-system/src/Tags/test';
import Typography from 'centralized-design-system/src/Typography/test';
import Tables from 'centralized-design-system/src/Table/test';
import Icons from 'centralized-design-system/src/Icons/test';
import CheckboxGroup from 'centralized-design-system/src/CheckboxGroup/test';
import Widgets from 'centralized-design-system/src/Widgets/test';
import PeopleCard from 'centralized-design-system/src/PeopleCard/test';
import ChannelCard from 'centralized-design-system/src/ChannelCard/test';
import LeftNavigationTabs from 'centralized-design-system/src/NavigationTabs/test';
import GroupChannelCard from 'centralized-design-system/src/GroupChannelCard/test';
import SkeletonAnimations from 'centralized-design-system/src/SkeletonAnimations/test';
import SearchFilterTest from 'centralized-design-system/src/SearchFilter/test';
import ProviderCard from 'centralized-design-system/src/ProviderCard/test';
import CourseCard from 'centralized-design-system/src/CourseCard/test';
import ChannelCuration from 'centralized-design-system/src/ChannelCuration/test';
import FileUpload from 'centralized-design-system/src/FileUpload/test';
import OptionListWithSearchFilter from 'centralized-design-system/src/OptionListWithSearchFilter/test';
import FilterWithSubCheckbox from 'centralized-design-system/src/FilterWithSubCheckbox/test';
import InProgressCard from 'centralized-design-system/src/InProgressCard/test';
import ProgressTab from 'centralized-design-system/src/ProgressTab/test';
import TabBar from 'centralized-design-system/src/TabBar/test';
import Stack from 'centralized-design-system/src/Stack/test';
import Filters from 'centralized-design-system/src/Filter/test';
import TableOfContents from 'centralized-design-system/src/TableOfContents/test';
import DropdownWithCustomInput from 'centralized-design-system/src/DropdownWithCustomInput/test';
import { connect } from 'react-redux';
import { saveThemeId } from '../../actions/themeActions';
import SwitchCmp from 'centralized-design-system/src/Switch';
import TreeViewOrg from 'centralized-design-system/src/TreeView/testOrganizations';
import TreeViewMock from 'centralized-design-system/src/TreeView/test';
import MUI from 'centralized-design-system/src/MUIComponents/test';
import Theming from 'centralized-design-system/src/Theme/test';
import Tooltip from 'centralized-design-system/src/Tooltip/test';
import Flyout from 'centralized-design-system/src/Flyout/test';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import Avatar from 'centralized-design-system/src/Avatar/test';
import HorizontalCards from 'centralized-design-system/src/Card/HorizontalCard/tests';
import TextClamp from 'centralized-design-system/src/TextClamp/tests';
import Chips from 'centralized-design-system/src/Chips/test';
import ChipsV2 from 'centralized-design-system/src/ChipsV2/test';
import CardContainer from 'centralized-design-system/src/Card/CardContainer/tests';
import EmptyState from 'centralized-design-system/src/EmptyState/tests';
import EmptyStateV2 from 'centralized-design-system/src/EmptyStatev2/tests';
import DropdownList from 'centralized-design-system/src/DropdownList/tests';
import LazyImageTest from 'centralized-design-system/src/LazyImage/test';

const ComponentLibrary = ({ dispatch, theme }) => {
  const toggleTheme = ({ target }) => {
    const { checked } = target;
    dispatch(saveThemeId(checked ? ThemeId.PLARE : ThemeId.PICASSO));
  };
  return (
    <div className="library ed-ui">
      <div className="left-panel">
        <div className="theme-switch">
          <SwitchCmp
            onChange={toggleTheme}
            name="Picasso / Plare"
            defaultChecked={theme === ThemeId.PLARE}
          />
        </div>
        <ul>
          <li>
            <a href="#theme">Theme</a>
          </li>
          <li>
            <a href="#typography">Typography</a>
          </li>
          <li>
            <a href="#icons">Icons</a>
          </li>
          <li>
            <a href="#buttons">Buttons</a>
          </li>
          <li>
            <a href="#inputs">Inputs</a>
          </li>
          <li>
            <a href="#dynamicinline">DynamicInlines</a>
          </li>
          <li>
            <a href="#accordion">Accordion</a>
          </li>
          <li>
            <a href="#datepickers">DatePickers</a>
          </li>
          <li>
            <a href="#radio">Radio</a>
          </li>
          <li>
            <a href="#checkbox">Checkbox</a>
          </li>
          <li>
            <a href="#switch">Switch</a>
          </li>
          <li>
            <a href="#alerts">Alerts</a>
          </li>
          <li>
            <a href="#links">Links</a>
          </li>
          <li>
            <a href="#tags">Tags</a>
          </li>
          <li>
            <a href="#pagination">Pagination</a>
          </li>
          <li>
            <a href="#dividers">Dividers</a>
          </li>
          <li>
            <a href="#avatar">Avatar</a>
          </li>
          <li>
            <a href="#tables">Tables</a>
          </li>
          <li>
            <a href="#widgets">Widgets</a>
          </li>
          <li>
            <a href="#people">PeopleCards</a>
          </li>
          <li>
            <a href="#channelcard">ChannelCard</a>
          </li>
          <li>
            <a href="#providercard">ProviderCard</a>
          </li>
          <li>
            <a href="#coursecard">CourseCard</a>
          </li>
          <li>
            <a href="#channelcuration">ChannelCuration</a>
          </li>
          <li>
            <a href="#progress-tab">ProgressTab</a>
          </li>
          <li>
            <a href="#lef-nav-tab">LeftNavigationTabs</a>
          </li>
          <li>
            <a href="#table-of-contents">TableOfContents</a>
          </li>
          <li>
            <a href="#group-channel-card">GroupChannelCard</a>
          </li>
          <li>
            <a href="#loading-animations">Loading Animations</a>
          </li>
          <li>
            <a href="#search-filter">Search filter</a>
          </li>
          <li>
            <a href="#filter-with-sub-checkbox">Filter with Sub checkbox</a>
          </li>
          <li>
            <a href="#filters">Filters</a>
          </li>
          <li>
            <a href="#file-upload">File Upload</a>
          </li>
          <li>
            <a href="#option-list-with-search-filter">Option List with Search filter</a>
          </li>
          <li>
            <a href="#in-progress-card">In Progress Card</a>
          </li>
          <li>
            <a href="#tab-bar">Tab Bar</a>
          </li>
          <li>
            <a href="#stack">Stack</a>
          </li>
          <li>
            <a href="#custom-inputDropDown">Dropdown With CustomInput</a>
          </li>
          <li>
            <a href="#tree-view">TreeView</a>
          </li>
          <li>
            <a href="#mui">MUI</a>
          </li>
          <li>
            <a href="#tooltip">Tooltip</a>
          </li>
          <li>
            <a href="#flyout">Flyout</a>
          </li>
          <li>
            <a href="#textclamp">Text Clamp</a>
          </li>
          <li>
            <a href="#horizontal-card">Horizontal Cards</a>
          </li>
          <li>
            <a href="#chips">Chips</a>
          </li>
          <li>
            <a href="#card-conatiner">Card (container)</a>
          </li>
          <li>
            <a href="#empty-state-generic">Empty State v2</a>
          </li>
          <li>
            <a href="#empty-state-generic-depraciated">Empty State</a>
          </li>
          <li>
            <a href="#dropdown-list">DropdownList</a>
          </li>
          <li>
            <a href="#lazy-image">Lazy Image</a>
          </li>
        </ul>
      </div>
      <div className="right-panel">
        <div id="theme">
          <Theming theme={theme} />
        </div>
        <div id="typography">
          <Typography />
        </div>
        <div id="icons">
          <Icons />
        </div>
        <div id="buttons">
          <Buttons />
        </div>
        <div id="inputs">
          <Inputs />
        </div>
        <div id="dynamicinline">
          <DynamicInlineComponents />
        </div>
        <div id="accordion">
          <Accordion />
        </div>
        <div id="datepickers">
          <DatePicker />
        </div>
        <div id="datepickersv2">
          <DatePickerV2 />
        </div>
        <div id="radio">
          <Radio />
        </div>
        <div id="radio">
          <RadioButtonV2 />
        </div>
        <div id="checkbox">
          <CheckboxGroup />
        </div>
        <div id="alerts">
          <Alerts />
          <div id="switch">
            <Switch />
          </div>
          <div id="links">
            <Links />
          </div>
          <div id="tags">
            <Tags />
          </div>
          <div id="pagination">
            <Pagination />
          </div>
          <div id="dividers">
            <Dividers />
          </div>
          <div id="avatar">
            <Avatar />
          </div>
          <div id="tables">
            <Tables />
          </div>
          <div id="widgets">
            <Widgets />
          </div>
          <div id="people">
            <PeopleCard />
          </div>
          <div id="channelcard">
            <ChannelCard />
          </div>
          <div id="providercard">
            <ProviderCard />
          </div>
          <div id="coursecard">
            <CourseCard />
          </div>
          <div id="channelcuration">
            <ChannelCuration />
          </div>
          <div id="progress-tab">
            <ProgressTab />
          </div>
          <div id="lef-nav-tab">
            <LeftNavigationTabs />
          </div>
          <div id="table-of-contents">
            <TableOfContents />
          </div>
          <div id="group-channel-card">
            <GroupChannelCard />
          </div>
          <div id="loading-animations">
            <SkeletonAnimations />
          </div>
          <div id="search-filter">
            <SearchFilterTest />
          </div>
          <div id="filter-with-sub-checkbox">
            <FilterWithSubCheckbox />
          </div>
          <div id="filters">
            <Filters />
          </div>
          <div id="file-upload">
            <FileUpload />
          </div>
          <div id="option-list-with-search-filter">
            <OptionListWithSearchFilter />
          </div>
          <div id="in-progress-card">
            <InProgressCard />
          </div>
          <div id="tab-bar">
            <TabBar />
          </div>
          <div id="stack">
            <Stack />
          </div>
          <div id="custom-inputDropDown">
            <DropdownWithCustomInput />
          </div>
          <div id="tree-view">
            <TreeViewOrg />
          </div>
          <div>
            <TreeViewMock />
          </div>
          <div id="mui">
            <MUI />
          </div>
          <div id="tooltip">
            <Tooltip />
          </div>
          <div id="flyout">
            <Flyout />
          </div>
          <div id="textclamp">
            <TextClamp />
          </div>
          <div id="horizontal-card">
            <HorizontalCards />
          </div>
          <div id="chips">
            <Chips />
          </div>
          <div id="chipsv2">
            <ChipsV2 />
          </div>
          <div id="card-conatiner">
            <CardContainer />
          </div>
          <div id="empty-state-generic">
            <EmptyStateV2 />
          </div>
          <div id="empty-state-generic-depraciated">
            <EmptyState />
          </div>
          <div id="dropdown-list">
            <DropdownList />
          </div>
          <div id="lazy-image">
            <LazyImageTest />
          </div>
        </div>
      </div>
    </div>
  );
};

ComponentLibrary.propTypes = {
  theme: PropTypes.string
};

function mapStateToProps(state) {
  return {
    theme: state.theme.get('themeId')
  };
}

export default connect(mapStateToProps)(ComponentLibrary);
