export const profileTabPaths = {
  overview: {
    key: 'web/profile/overview',
    path: '/me/overview',
    label: 'Overview',
    pathsList: [
      '/me/overview',
      '/me/overview/experiences',
      '/me/overview/certifications',
      '/me/overview/badges',
      '/me/overview/groups',
      '/me/overview/channels',
      '/me/overview/activity',
      '/me/overview/content',
      '/me/in-progress'
    ]
  },
  skills: {
    key: 'web/profile/skills',
    path: '/me/skills',
    label: 'Skills',
    pathsList: ['/me/skills']
  },
  profile: {
    key: 'web/profile/profile',
    path: '/me',
    label: 'Profile',
    pathsList: ['/me', '/me/groups', '/me/channels', '/me/activity', '/me/in-progress']
  },
  skillsPassport: {
    key: 'web/profile/skillsPassport',
    path: '/me/skills-passport',
    label: 'Skills Passport'
  },
  learning: {
    key: 'web/profile/learningQueue',
    path: '/me/learning',
    label: 'My Learning Plan',
    pathsList: [
      '/me/learning',
      '/me/learning/assigned',
      '/me/learning/shared',
      '/me/learning/bookmarked',
      '/me/learning/selfrequested',
      '/me/learning/explorer',
      '/me/learning/completed',
      '/me/learning/all',
      '/me/learning/active'
    ]
  },
  content: {
    key: 'web/profile/content',
    path: '/me/content',
    label: 'My Content',
    pathsList: [
      '/me/content',
      '/me/content/draft',
      '/me/content/assignedByMe',
      '/me/content/sharedByMe',
      '/me/content/purchased',
      '/me/content/dismissed',
      '/me/content/deleted',
      '/me/content/archived'
    ]
  },
  subscription: {
    key: 'web/profile/my-subscription',
    path: '/me/my-subscription',
    label: 'My Subscription',
    pathsList: ['/me/my-subscription']
  },
  ledger: {
    key: 'me/ledger',
    path: '/me/ledger',
    label: 'My Ledger'
  }
};
