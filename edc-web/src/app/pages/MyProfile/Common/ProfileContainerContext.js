import React, { useEffect, createContext, useContext, useState } from 'react';
import { string, func, bool, node, object, array, number, oneOfType } from 'prop-types';
import { connect } from 'react-redux';
import { useParams, useLocation } from 'react-router-dom';
import Loading from 'centralized-design-system/src/Loading';
import {
  getPublicProfile,
  getPublicUserPassport,
  removePublicProfile
} from '../../../../app/actions/usersActions';
import { getSpecificUserInfo } from '../../../../app/actions/currentUserActions';
import { getAvailableLocations } from 'actions/availableLocationsActions';
import { getUserBasicInfo } from 'edc-web-sdk/requests/users.v2';
import { BLOCKED_USER_API_RESPONSE, NO_USER_FOUND, RESTRICTED_BY_POLICY_ENGINE } from './constant';
import { isNewProfileEnabled } from '@pages/MyProfileV2/utils';

export const ProfileContainerContext = createContext();

export const useProfileContainerContext = () => {
  const context = useContext(ProfileContainerContext);
  if (!context) {
    throw new Error(`useProfileContainerContext must be used within ProfileContainerProvider`);
  }
  return context;
};

// Provider Function
const ProfileContainerProvider = ({
  children,
  currentUserLang,
  currentUserId,
  currentUserIsAdmin,
  dispatch,
  currentUser,
  currentUserHandle,
  skillAssessmentAccessible,
  languages,
  edcastWallet,
  showEmail,
  orgProfilePageSettings,
  profileShowUserContent,
  profileShowUserSkillsPassport,
  isClcActive,
  skillPassportConfig,
  dashboardInfo,
  isOcgEnabled,
  isTeamOcgEnabled,
  isTeamEgtEnabled,
  filestackUrlExpireAfterSeconds,
  assignmentPriority,
  isMultiOrg,
  isFsPrime,
  countries,
  availableLocations,
  locationsAssociation,
  locationsEnabled
}) => {
  const { handle: unformattedHandle } = useParams();
  const { pathname } = useLocation();

  const [isLoading, setIsLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [isBlockedPublicProfile, setIsBlockedPublicProfile] = useState(false);
  const [restrictProfile, setRestrictProfile] = useState(false);
  const [isUserExisting, setIsUserExisting] = useState(true);
  const [activeTabId, setActiveTabId] = useState('profile'); // Manages tab/tabpanel accessibility attributes
  const currentPathname = window.location.pathname;
  const handle = unformattedHandle?.replace('@', '');

  useEffect(() => {
    const userFields = [
      'handle',
      'bio',
      'roles',
      'userRoles',
      'rolesDefaultNames',
      'followersCount',
      'followingCount',
      'coverImage',
      'writableChannels',
      'first_name',
      'last_name',
      'onboardingCompletedDate',
      'followingChannels',
      'picture',
      'manager',
      'reporters',
      'skillAssessmentAccessible',
      'location',
      'organization_units'
    ];
    if (!handle) userFields.push('dashboardInfo');
    setIsLoading(true);
    if (!availableLocations) {
      dispatch(getAvailableLocations(currentUserLang));
    }
    dispatch(getSpecificUserInfo(userFields, currentUser));
    setIsLoading(false);
  }, []);

  useEffect(() => {
    if (
      ~currentPathname.indexOf(`/me`) ||
      handle ||
      ~currentPathname.indexOf(`/career`) ||
      ~currentPathname.indexOf(`/`)
    ) {
      setHasAccess(true);
    }
    if (handle) {
      setIsLoading(true);
      dispatch(removePublicProfile());
      dispatch(getPublicProfile('@' + handle))
        .then(response => {
          const { profile = {} } = response;
          getUserBasicInfo(profile?.id, isNewProfileEnabled() ? { show_users_relations: true } : {})
            .then(res => {
              dispatch({
                type: 'receive_public_profile_basic_info',
                userParam: res || {}
              });
            })
            .catch(err => {
              console.error(`Error in new profile ProfileContainer.getUserBasicInfo.func : ${err}`);
            });
          dispatch({
            type: 'update_public_profile_info',
            dashboardInfo: profile?.dashboardInfo || []
          });
          setIsLoading(false);
          profile?.id && dispatch(getPublicUserPassport(profile.id));
        })
        .catch(err => {
          setIsLoading(false);

          if (err.statusCode === 404 && err.body === NO_USER_FOUND) {
            setIsUserExisting(false);
          }
          if (err.statusCode === 404 && err.body.message === BLOCKED_USER_API_RESPONSE) {
            setIsBlockedPublicProfile(true);
          }
          if (err.statusCode === 403 && err.body.message === RESTRICTED_BY_POLICY_ENGINE) {
            setRestrictProfile(true);
          }
          console.error(`Error in new profile ProfileContainer.getPublicProfile.func : ${err}`);
        });
    } else {
      dispatch(removePublicProfile());
    }
  }, [handle, currentPathname]);

  const providerObject = {
    handle,
    currentUserLang,
    currentUserId,
    currentUserIsAdmin,
    dispatch,
    currentUser,
    currentUserHandle,
    skillAssessmentAccessible,
    languages,
    edcastWallet,
    showEmail,
    orgProfilePageSettings,
    profileShowUserContent,
    profileShowUserSkillsPassport,
    isClcActive,
    skillPassportConfig,
    pathname,
    dashboardInfo,
    isOcgEnabled,
    isTeamOcgEnabled,
    isTeamEgtEnabled,
    filestackUrlExpireAfterSeconds,
    assignmentPriority,
    isMultiOrg,
    isFsPrime,
    isBlockedPublicProfile,
    restrictProfile,
    isUserExisting,
    countries,
    availableLocations,
    locationsAssociation,
    locationsEnabled,
    activeTabId,
    setActiveTabId
  };

  if (isLoading || !hasAccess) {
    return <Loading />;
  }

  return (
    <ProfileContainerContext.Provider value={providerObject}>
      {children}
    </ProfileContainerContext.Provider>
  );
};

const mapStateToProps = ({
  team,
  currentUser,
  profile,
  availableLocations,
  locationsConfiguration
}) => {
  return {
    languages: team.get('languages'),
    isFsPrime: team.get('config')?.fs_prime,
    currentUserLang: currentUser.get('profile').get('language'),
    currentUserId: currentUser.get('id'),
    currentUserIsAdmin: currentUser.get('isAdmin'),
    currentUserHandle: currentUser.get('handle'),
    skillAssessmentAccessible: currentUser.get('skillAssessmentAccessible'),
    edcastWallet: !!team.get('config')?.wallet,
    showEmail: !!team.get('config')?.enable_show_email,
    orgProfilePageSettings: team.get('OrgConfig')?.profile,
    profileShowUserContent: !!team.get('OrgConfig')?.profileShowUserContent,
    profileShowUserSkillsPassport: !!team.get('OrgConfig')?.profileShowUserSkillsPassport,
    isClcActive: !!team?.get('isClcActive'),
    skillPassportConfig: team.get('config').skills_passport_options,
    dashboardInfo: profile.get('dashboardInfo').toJS(),
    isOcgEnabled: currentUser.get('isOcgEnabled'),
    isTeamOcgEnabled: team.get('isOcgEnabled'),
    isTeamEgtEnabled: team.get('isEgtEnabled'),
    currentUser,
    filestackUrlExpireAfterSeconds: team?.get('config')?.filestack_url_expire_after_seconds,
    assignmentPriority: team.get('OrgConfig')?.assignmentPriority || {},
    availableLocations: availableLocations.get('availableLocations'),
    countries: availableLocations.get('countries'),
    locationsEnabled: locationsConfiguration.get('enable'),
    locationsAssociation: locationsConfiguration.get('association'),
    isMultiOrg: team.get('config')['multi-org']
  };
};

ProfileContainerProvider.propTypes = {
  children: node,
  currentUserLang: string,
  currentUserId: string,
  currentUserIsAdmin: bool,
  dispatch: func,
  currentUser: object,
  currentUserHandle: string,
  skillAssessmentAccessible: bool,
  languages: object,
  edcastWallet: bool,
  showEmail: bool,
  orgProfilePageSettings: object,
  profileShowUserContent: bool,
  profileShowUserSkillsPassport: bool,
  isOcgEnabled: bool,
  isTeamOcgEnabled: bool,
  isTeamEgtEnabled: bool,
  filestackUrlExpireAfterSeconds: number,
  isClcActive: bool,
  skillPassportConfig: oneOfType([array, object]),
  dashboardInfo: array,
  assignmentPriority: object,
  isMultiOrg: bool,
  isFsPrime: bool,
  availableLocations: array,
  isBlockedPublicProfile: bool,
  restrictProfile: bool,
  isUserExisting: bool,
  locationsAssociation: array,
  locationsEnabled: bool,
  countries: array
};

export default connect(mapStateToProps)(ProfileContainerProvider);
