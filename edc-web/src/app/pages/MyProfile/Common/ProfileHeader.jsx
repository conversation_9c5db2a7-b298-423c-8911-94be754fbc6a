import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Translatr, omp, translatr } from 'centralized-design-system/src/Translatr';
import { shouldShowTMMentorship } from 'opportunity-marketplace/util';
import <PERSON><PERSON>, {
  <PERSON><PERSON><PERSON>ead<PERSON>,
  ModalContent,
  ModalFooter
} from 'centralized-design-system/src/Modals/index';
import { getUserEngagementStatistics } from 'edc-web-sdk/requests/profile.v2';
import {
  getMentorProfileByUserId,
  MENTOR_PROFILE_STATUS
} from 'edc-web-sdk/requests/careerOportunities.v2';
import Avatar from 'centralized-design-system/src/Avatar';
import Tooltip from 'centralized-design-system/src/Tooltip';
import './ProfileHeader.scss';
import { useProfileContainerContext } from './ProfileContainerContext';
import classNames from 'classnames';
import LD from '../../../../app/containers/LDStore';
import UserdashboardErrorBoundary from '@components/UserdashboardErrorBoundary/UserdashboardErrorBoundary';
import { getUserBasicInfo } from 'edc-web-sdk/requests/users.v2';
import { usersv2 } from 'edc-web-sdk/requests';
import { updateFollowingUsersCount } from '../../../../app/actions/currentUserActions';
import changeTimeLanguageLocale from 'centralized-design-system/src/Utils/changeTimeLanguageLocale';
import { useMediaQuery } from '@utils/hooks';
import { SkillscoinContainer } from './SkillscoinContainer';
import getStaticImgPath from 'edc-web-sdk/helpers/getStaticImgPath';
import LazyloadComponent from '@components/LazyloadComponent';

import { saveSearchTabState } from '@actions/searchActions';
import { UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID } from '../../../../app/constants/actionTypes';
import { isAdminFunc } from '@utils/utils';
import Dropdown from 'centralized-design-system/src/Dropdown';
import { getUserRolesWithIcon } from 'centralized-design-system/src/PeopleCard/utils';
import unescape from 'lodash/unescape';
import { FilterHostname } from '@components/common/FilterHostname';
import { defaultBannerImage } from '@pages/utils';

const BlockUser = LazyloadComponent(() => import('@components/modals/BlockUser/BlockUser'))();

const UserFollowerslistModal = LazyloadComponent(() =>
  import('@components/modals/UserFollowerslistModal')
)();

const PublicPrivateModal = LazyloadComponent(() =>
  import('@components/modals/PublicPrivateModal/PublicPrivateModal')
)();

const Details = LazyloadComponent(() => import('../Details/Details'))();

const moment = changeTimeLanguageLocale();

function ProfileHeader() {
  const navigate = useNavigate();

  const {
    edcastWallet,
    handle,
    currentUser,
    dispatch,
    currentUserIsAdmin,
    skillAssessmentAccessible,
    dashboardInfo,
    isOcgEnabled,
    isTeamOcgEnabled,
    isTeamEgtEnabled
  } = useProfileContainerContext();
  const [isOpen, setIsOpen] = useState(false);
  const [points, setPoints] = useState(0);
  const [openUserFollowlistModal, setOpenUserFollowlistModal] = useState({
    modalStatus: false,
    followType: null
  });
  const [isButtonHover, updateButtonHoverState] = useState(false);
  const [isPoints, setIsPoints] = useState(false);
  const [openBlockUser, setOpenBlockUser] = useState(false);
  const [openPublicPrivate, setOpenPublicPrivate] = useState(false);

  const isTablet = useMediaQuery('(max-width: 767px)');

  const isImpersonator = ['user_dashboard', 'manager_dashboard'].includes(
    window?.__ED__?.proxyType
  );
  const typeOfSection = 'Points in user profile';
  const enabledForPublicProfile =
    dashboardInfo?.find(elem => elem.name === typeOfSection)?.visible || false;

  const isAdmin = isAdminFunc(currentUserIsAdmin);

  const isPublicProfile = !!handle && handle !== currentUser.get('handle');
  const publicUser = currentUser?.get('publicProfileBasicInfo');
  const joinDate = isPublicProfile
    ? publicUser?.onboardingCompletedDate
    : currentUser?.get('onboardingCompletedDate');
  const bannerImage = defaultBannerImage(
    isPublicProfile ? publicUser?.coverimages?.banner_url : currentUser?.get('coverImage')
  );
  const userDetails = {
    id: isPublicProfile ? publicUser?.id : currentUser?.get('id'),
    followingCount: isPublicProfile
      ? publicUser?.followingCount
      : currentUser?.get('followingCount'),
    bio: isPublicProfile ? publicUser?.bio : currentUser?.get('bio'),
    followersCount: isPublicProfile
      ? publicUser?.followersCount
      : currentUser?.get('followersCount'),
    manager: isPublicProfile
      ? publicUser?.manager?.firstName
        ? `${publicUser?.manager?.firstName} ${publicUser?.manager?.lastName}`
        : null
      : currentUser?.get('manager')?.get('name'),
    jobTitle: isPublicProfile
      ? publicUser?.profile?.jobTitle
      : currentUser?.get('profile')?.get('jobTitle'),
    imgUrl: isPublicProfile ? publicUser?.avatarimages?.medium : currentUser?.get('picture'),
    coverImage: bannerImage.url,
    name: isPublicProfile ? publicUser?.name : currentUser?.get('name'),
    roles: isPublicProfile ? publicUser?.roles : currentUser?.get('roles'),
    userHandle: isPublicProfile ? handle : currentUser?.get('handle'),
    email: isPublicProfile ? publicUser?.email : currentUser?.get('email'),
    joinDate: joinDate ? moment(joinDate).format('DD MMM YYYY') : null,
    firstName: currentUser?.get('first_name'),
    userRoles: isPublicProfile
      ? currentUser?.get?.('publicProfile')?.profile?.userRoles
      : currentUser?.get('userRoles')?.toJS?.(),
    subOrgName: isPublicProfile && publicUser?.subOrgName
  };

  const {
    id,
    followingCount,
    bio,
    followersCount,
    jobTitle,
    coverImage,
    name,
    userHandle,
    firstName
  } = userDetails;

  const userRoles = getUserRolesWithIcon(userDetails.userRoles);

  const [isFollowingLoading, setIsFollowingLoading] = useState(false);
  const isFollowingLabel = publicUser?.isFollowing
    ? translatr('web.myprofile.main', 'Following')
    : translatr('web.myprofile.main', 'Follow');
  const [mentorProfile, setMentorProfile] = useState(null);

  const setButtonHoverState = value => {
    updateButtonHoverState(value);
  };

  const hrDataEnabled =
    window.__edOrgData.configs.find(config => config.name === 'hr_data_service_enablement')
      ?.value || false;
  let isSkillsAssessmentEnable = false;

  if (hrDataEnabled) {
    isSkillsAssessmentEnable = skillAssessmentAccessible;
  } else if (isTeamOcgEnabled || isOcgEnabled) {
    isSkillsAssessmentEnable = true;
  } else {
    isSkillsAssessmentEnable = isTeamEgtEnabled && LD.isSkillsAssessment();
  }
  const showSkillsAssessmentBtn = !isPublicProfile && isSkillsAssessmentEnable;

  useEffect(() => {
    setIsPoints(false);
    if (id) {
      let params = { 'statistics[fields]': 'points' };
      if (isPublicProfile) {
        params.user_id = id;
      }
      getUserEngagementStatistics(params)
        .then(data => {
          setPoints(data.points);
          setIsPoints(true);
        })
        .catch(err => {
          console.error(`Error in profile.getUserEngagementStatistics : ${err}`);
          setIsPoints(true);
        });
    }
  }, [id]);

  useEffect(() => {
    if (!shouldShowTMMentorship() || !id) {
      return;
    }

    getMentorProfileByUserId(id)
      .then(response => setMentorProfile(response.data?.result[0]))
      .catch(error => console.error(error));
  }, [id]);

  function keyDownHandler(e, source) {
    // Prevent default function only if either conditions are true;
    if (
      (source === 'close' && e.key === 'Tab' && !e.shiftKey) ||
      (source === 'modal_header' && e.shiftKey && e.key === 'Tab')
    ) {
      e.preventDefault();
      // Focus Heading Tag
      document.querySelector('#profile_details_header')?.focus();
    }
  }

  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
  }

  const openFollow = (e, type, value) => {
    e.preventDefault();
    if (value) {
      setOpenUserFollowlistModal({ modalStatus: true, followType: type });
    }
  };

  const onMentorRoleClick = e => {
    e.preventDefault();

    navigate(`/career/detail/mentorship/${mentorProfile.id}`);
  };

  const closeModalHandler = () => {
    setIsOpen(false);
    document.querySelector('#vd-' + id)?.focus();
  };

  const handleEditProfile = e => {
    e?.preventDefault();
    navigate('/settings', { isEditAccount: true });
  };

  const handlePublicProfile = e => {
    e?.preventDefault();
    navigate('/@' + userHandle, { replace: true });
  };

  const handleExitPublicProfile = e => {
    e?.preventDefault();
    navigate('/me', { replace: true });
  };

  const handleNavigateSkillCoins = e => {
    e?.preventDefault();
    navigate('/me/skill-coins');
  };

  const handleBlockUser = e => {
    e?.preventDefault();
    setOpenBlockUser(true);
  };

  useEffect(() => {
    setTimeout(() => {
      const path = window.location.pathname;
      if (path.includes('@') && path.split('/').length < 3) {
        const title = `${translatr('web.common.main', 'Profile')} ${name}`;
        document.title = FilterHostname(title);
      }
    }, 1000);
  }, [name]);

  const getIconUI = (hidden, label, iconClass) => {
    if (!hidden)
      return (
        <span className="make-center s-margin-right tooltip-icon" key={`icon-${label}`}>
          <Tooltip message={label} tooltipParentRole="tooltip" isTranslated={true} tabIndex="0">
            <i className={classNames('pointer', iconClass)} aria-label={label} role="img" />
          </Tooltip>
        </span>
      );
  };

  const redirectSkillsAssessment = e => {
    e?.preventDefault();
    navigate('/skills-assessment');
  };

  const defaultBanner = getStaticImgPath('/i/images/default_banner_user_image.png');

  const getButtonUI = (hidden, onClickButton, child, ariaLabel = '') => {
    return (
      <li>
        <button
          hidden={hidden}
          className="pointer public-profile s-margin-left"
          onClick={onClickButton}
          aria-label={ariaLabel}
        >
          {child}
        </button>
      </li>
    );
  };

  const followClickHandler = () => {
    if (isFollowingLoading) return;
    setIsFollowingLoading(true);
    const isFollowing = publicUser?.isFollowing;
    const followRequest = !isFollowing ? usersv2.follow : usersv2.unfollow;
    followRequest(publicUser?.id)
      .then(() => {
        getUserBasicInfo(publicUser?.id)
          .then(res => {
            dispatch({
              type: 'receive_public_profile_basic_info',
              userParam: res || {}
            });
            const updatedObject = {
              id: Number(id),
              updatedProperty: 'isFollowing',
              updatedValue: !isFollowing
            };
            dispatch({
              type: UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID,
              payload: { ...updatedObject }
            });
            dispatch(
              saveSearchTabState('user', {
                data: {
                  data: {
                    users: [
                      {
                        id: Number(id),
                        isFollowing: !isFollowing
                      }
                    ]
                  }
                }
              })
            );
            setIsFollowingLoading(false);
          })
          .catch(err => {
            setIsFollowingLoading(false);
            console.error(`Error in new profile ProfileContainer.getUserBasicInfo.func : ${err}`);
          });
        dispatch(updateFollowingUsersCount(!isFollowing));
      })
      .catch(err => {
        setIsFollowingLoading(false);
        console.error(`Error in UserListItem.toggleFollow.func : ${err}`);
      });
  };

  const closePublicPrivateModal = () => {
    setOpenPublicPrivate(!openPublicPrivate);
  };

  const viewFeedBackRequests = window?.__ED__?.csxFeedbackPageUrl;

  return (
    <div className="ed-ui profile-header">
      <div className="banner relative width-100">
        <ul className="buttons position-absolute justflex">
          {!isImpersonator &&
            getButtonUI(
              !!handle || !showSkillsAssessmentBtn,
              redirectSkillsAssessment,
              translatr('web.myprofile.main', 'SkillsAssessment')
            )}
          {getButtonUI(
            !handle || !!isPublicProfile,
            handleExitPublicProfile,
            translatr('web.myprofile.main', 'ExitPublicProfile')
          )}
          {getButtonUI(
            !!handle,
            handlePublicProfile,
            <i className="icon-eye supporting-text-color" />,
            translatr('web.myprofile.main', 'ViewPublicProfile')
          )}
          {!isImpersonator &&
            getButtonUI(
              !!handle,
              handleEditProfile,
              <i className="icon-edit-light" />,
              translatr('web.myprofile.main', 'EditPublicProfile')
            )}
          {LD.isBlockUser() && isPublicProfile && !isAdmin && (
            <Dropdown
              wrapperClass="block-user-dropDown public-profile s-margin-left"
              icon={<span className="icon-ellipsis-v-alt" />}
            >
              <ul>
                <li className="no-padding">
                  <button
                    onClick={handleBlockUser}
                    className="cursor-pointer width-100 text-left negative-color s-padding"
                  >
                    {translatr('web.myprofile.main', 'Block')}
                  </button>
                </li>
              </ul>
            </Dropdown>
          )}
        </ul>

        {openBlockUser && (
          <Translatr apps={['web.user.block-user-modal']}>
            <BlockUser
              fullName={publicUser?.fullName}
              id={publicUser?.id}
              setBlockUser={setOpenBlockUser}
            />
          </Translatr>
        )}

        <img
          className="width-100 height-100"
          src={coverImage}
          onError={e => (e.target.src = defaultBannerImage(defaultBanner).url)}
          alt={bannerImage.altText}
        />
      </div>
      <div className="profile-container justflex">
        <div className="profile-summary justflex">
          <div className="profile-img">
            <Avatar user={userDetails} blankAlt={true} />
          </div>
          <div className="profile-info">
            <h1
              id={`name-${firstName}`}
              className="no-margin m-padding-top text-ellipsis font-size-24 ed-text-color"
            >
              {name}
            </h1>
            {jobTitle && (
              <Tooltip message={unescape(jobTitle)} customClass="display-block">
                <p className="no-margin font-normal job-title supporting-text-color">
                  {unescape(jobTitle)}
                </p>
              </Tooltip>
            )}
            {bio && <div className="s-padding-ends supporting-text-color">{unescape(bio)}</div>}
            <button
              id={`vd-${id}`}
              aria-labelledby={`vd-${id} name-${firstName}`}
              onClick={() => setIsOpen(true)}
              className="supporting-text view-details pointer"
            >
              {translatr('web.myprofile.main', 'ViewDetails')}
            </button>
            {!isPublicProfile && viewFeedBackRequests && (
              <div className="mb-12">
                <a
                  href={viewFeedBackRequests}
                  className="ed-btn-v2 ed-btn-secondary-ghost-v2 ed-btn-size-medium-v2 ed-btn-padding-medium"
                >
                  {translatr('web.myprofile.main', 'ViewFeedbackRequests') ||
                    'View feedback requests'}
                </a>
              </div>
            )}
            {isTablet && (
              <SkillscoinContainer
                isPoints={isPoints}
                points={points}
                edcastWallet={edcastWallet}
                handle={handle}
                handleNavigateSkillCoins={handleNavigateSkillCoins}
                isPublicProfile={isPublicProfile}
                isButtonHover={isButtonHover}
                isFollowingLabel={isFollowingLabel}
                followClickHandler={followClickHandler}
                publicUser={publicUser}
                setButtonHoverState={setButtonHoverState}
                firstName={firstName}
                setOpenPublicPrivate={setOpenPublicPrivate}
                openPublicPrivate={openPublicPrivate}
                enabledForPublicProfile={enabledForPublicProfile}
              />
            )}
            {!handle && (
              <div className="user-follow-data supporting-text no-padding flex-center supporting-text-color">
                <button
                  className={classNames(
                    'supporting-text-color',
                    { pointer: !!followersCount },
                    { 'not-allowed': !followersCount }
                  )}
                  aria-disabled={!followersCount}
                  onClick={e => openFollow(e, 'follower', followersCount)}
                >
                  <span className="font-weight-600">{followersCount}</span>
                  {translatr('web.myprofile.main', 'Followers')}
                </button>
                <span className="bullet font-size-xl m-margin-left supporting-text-color"></span>
                <button
                  className={classNames(
                    'supporting-text-color',
                    { pointer: !!followingCount },
                    { 'not-allowed': !followingCount }
                  )}
                  aria-disabled={!followingCount}
                  onClick={e => openFollow(e, 'following', followingCount)}
                >
                  <span className="font-weight-600">{followingCount}</span>
                  {translatr('web.myprofile.main', 'Following')}
                </button>
              </div>
            )}
            <div className={classNames('icon-container flex', { 'm-margin-top': handle })}>
              {userRoles.map(userRole => {
                return getIconUI(
                  !userRole.name,
                  translatr('web.common.main', capitalizeFirstLetter(userRole.name)) ||
                    userRole.name,
                  userRole.icon
                );
              })}
              {mentorProfile?.status === MENTOR_PROFILE_STATUS.ACTIVE && (
                <a
                  aria-label={translatr('web.myprofile.main', 'ViewMentorProfile', {
                    mentor: omp('tm_tm_mentor')
                  })}
                  href={`/career/detail/mentorship/${mentorProfile?.id}`}
                  onClick={onMentorRoleClick}
                >
                  {getIconUI(
                    false,
                    translatr('web.myprofile.main', 'ViewMentorProfile', {
                      mentor: omp('tm_tm_mentor')
                    }),
                    'icon-hand-holding-person'
                  )}
                </a>
              )}
            </div>
          </div>
        </div>
        {!isTablet && (
          <SkillscoinContainer
            isPoints={isPoints}
            points={points}
            edcastWallet={edcastWallet}
            handle={handle}
            handleNavigateSkillCoins={handleNavigateSkillCoins}
            isPublicProfile={isPublicProfile}
            isButtonHover={isButtonHover}
            isFollowingLabel={isFollowingLabel}
            followClickHandler={followClickHandler}
            publicUser={publicUser}
            setButtonHoverState={setButtonHoverState}
            firstName={firstName}
            setOpenPublicPrivate={setOpenPublicPrivate}
            openPublicPrivate={openPublicPrivate}
            enabledForPublicProfile={enabledForPublicProfile}
          />
        )}
      </div>
      {isOpen && (
        <Modal size="small">
          <ModalHeader
            id="profile_details_header"
            title={translatr('web.myprofile.main', 'ProfileDetails')}
            onClose={() => closeModalHandler()}
            onKeyDown={e => {
              keyDownHandler(e, 'modal_header');
            }}
          />
          <ModalContent>
            <Details userDetails={userDetails} />
          </ModalContent>
          <ModalFooter>
            <button
              id="profile_details_close"
              aria-label={translatr('web.common.main', 'Close')}
              className="ed-btn ed-btn-neutral"
              onClick={() => closeModalHandler()}
              onKeyDown={e => {
                keyDownHandler(e, 'close');
              }}
            >
              {translatr('web.myprofile.main', 'Close')}
            </button>
          </ModalFooter>
        </Modal>
      )}
      {openUserFollowlistModal.modalStatus && (
        <UserFollowerslistModal
          followType={openUserFollowlistModal.followType}
          dispatch={dispatch}
          closeHandler={() =>
            setOpenUserFollowlistModal({ ...openUserFollowlistModal, modalStatus: false })
          }
          currentUserID={currentUser.id}
        />
      )}
      {openPublicPrivate && (
        <PublicPrivateModal
          closeClickHandler={closePublicPrivateModal}
          enabledForPublicProfile={enabledForPublicProfile}
          typeOfSection={typeOfSection}
        />
      )}
    </div>
  );
}

export default UserdashboardErrorBoundary(ProfileHeader);
