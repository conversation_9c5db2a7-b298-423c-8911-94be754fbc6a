import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getListV2 } from 'edc-web-sdk/requests/groups.v2';
import { connect } from 'react-redux';
import teamFields from '../../../../app/utils/apiFieldsParams';
import { object } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import GroupChannelCard from 'centralized-design-system/src/GroupChannelCard/GroupChannelCard';
import Carousel from 'centralized-design-system/src/Carousel';
import EmptyCard from '@components/EmptyCard/EmptyCard';
import Loading from 'centralized-design-system/src/Loading';
import classNames from 'classnames';
import './grouplisting.scss';
import { Permissions } from '../../../../app/utils/checkPermissions';
import { useProfileContainerContext } from '../../MyProfile/Common/ProfileContainerContext';
import UserdashboardErrorBoundary from '@components/UserdashboardErrorBoundary/UserdashboardErrorBoundary';
import { snackBarOpenClose } from 'actions/channelsActionsV2';
import LazyloadComponent from '@components/LazyloadComponent';

const PublicPrivateModal = LazyloadComponent(() =>
  import('@components/modals/PublicPrivateModal/PublicPrivateModal')
)();

const GroupListing = ({ dispatch, shouldShowGroup }) => {
  const navigate = useNavigate();

  const [groups, setGroups] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const { handle, dashboardInfo } = useProfileContainerContext();
  const [openPublicPrivate, setOpenPublicPrivate] = useState(false);
  const createGroupPermission = Permissions.has('CREATE_GROUP');

  if (!!handle) return null;
  const typeOfSection = dashboardInfo[8]?.name || '';
  const enabledForPublicProfile = dashboardInfo[8]?.visible || false;

  useEffect(() => {
    setIsLoading(true);
    let payload = {
      limit: 20,
      fields: teamFields
    };
    getListV2(payload)
      .then(data => {
        setIsLoading(false);
        setGroups(data.teams);
        setTotalCount(data.total);
      })
      .catch(err => {
        setIsLoading(false);
        console.error('Error in fetchgroups', err);
      });
  }, []);

  const handleViewAll = e => {
    e?.preventDefault();
    navigate('/org-groups');
  };

  const handleCreateGroup = e => {
    e?.preventDefault();
    navigate('org-groups/create');
  };

  const goToStandAloneView = transitionUrl => {
    navigate(transitionUrl);
  };

  const snackbarErrorHandler = error => dispatch(snackBarOpenClose(error, 3000));

  return (
    <div className="ed-ui groups">
      <div className="my-group-wrapper">
        <div className="flex flex-column">
          <div className="group-container xs-margin-ends flex flex-space-between align-items-center">
            <div className="justflex">
              <h2 className="no-margin font-size-xl ed-text-color" aria-level="2">
                {groups.length
                  ? translatr('web.myprofile.main', 'MyGroups')
                  : translatr('web.myprofile.main', 'GroupsIAmPartOf')}
              </h2>
              <span className="group-length s-margin-left font-weight-600">({totalCount})</span>
            </div>
            <div
              className={classNames('flex-space-between view-icon-wrapper align-items-center', {
                hidden: !groups.length
              })}
            >
              {/* Pending : commented below code due to their is no support from API for Other User Grp on Public Profile
                when API of Group comes with user_id param , It can be uncommented and intergate the
                user_id in payload as  "user_id: isPublicProfile ? publicUser.id : currentUser?.get('id')" */}

              {/* {!handle && (
                <button onClick={() => setOpenPublicPrivate(!openPublicPrivate)}>
                  <i
                    className={classNames({
                      'icon-user-lock s-margin-right pointer': !enabledForPublicProfile,
                      'icon-eye font-size-l  s-margin-right pointer': enabledForPublicProfile
                    })}
                  />
                </button>
              )} */}
              {!handle && (
                <a
                  href="/org-groups"
                  aria-label={translatr('web.myprofile.main', 'ViewAllGroups')}
                  onClick={handleViewAll}
                  className="ed-link-dark width-100"
                  role="button"
                >
                  {translatr('web.myprofile.main', 'ViewAll')}
                </a>
              )}
            </div>
          </div>
        </div>
        {isLoading ? (
          <div className="ledger-loader text-center">
            <Loading />
          </div>
        ) : !groups.length ? (
          <EmptyCard
            iconClass="icon-users"
            description={translatr('web.myprofile.main', 'YouAreNotPartOfAnyGroupYet')}
            background={false}
          >
            <button
              aria-label={translatr('web.common.main', 'Find')}
              className="ed-btn ed-btn-neutral"
              onClick={handleViewAll}
            >
              <span>{translatr('web.myprofile.main', 'FindGroups')}</span>
            </button>

            {createGroupPermission && shouldShowGroup && shouldShowGroup.visible && (
              <button
                aria-label={translatr('web.myprofile.main', 'CreateGroup')}
                className="ed-btn ed-btn-primary"
                onClick={handleCreateGroup}
              >
                {translatr('web.myprofile.main', 'CreateGroup')}
              </button>
            )}
          </EmptyCard>
        ) : (
          <Carousel addScroll={16}>
            {groups.map(group => {
              const isPartOfGroup =
                group.isMember ||
                group.isTeamAdmin ||
                group.isTeamSubAdmin ||
                group.isTeamModerator;
              const isGroupAccessible = isPartOfGroup || !group.isPrivate;
              return (
                <div className="group-card" key={group.id}>
                  <GroupChannelCard
                    groupOrChannelId={group.id}
                    group={group}
                    tagName={translatr('web.myprofile.main', 'Group')}
                    cardImage={group.imageUrls.medium}
                    imageAltText={group.imageAltText}
                    title={group.name}
                    transitionUrl={`/teams/${group.slug}`}
                    userCount={group.membersCount}
                    imageAlternateText={translatr('web.myprofile.main', 'ThisIsAGroupImage')}
                    isPrivate={group.isPrivate}
                    isUserIsPartOfGroupOrChannel={isPartOfGroup}
                    goToStandAloneView={goToStandAloneView}
                    isGroupAccessible={isGroupAccessible}
                    isMandatory={group.isMandatory}
                    snackbarErrorHandler={snackbarErrorHandler}
                    isEveryoneTeam={group.isEveryoneTeam}
                  />
                </div>
              );
            })}
          </Carousel>
        )}
      </div>
      {openPublicPrivate && (
        <PublicPrivateModal
          closeClickHandler={() => setOpenPublicPrivate(!openPublicPrivate)}
          enabledForPublicProfile={enabledForPublicProfile}
          typeOfSection={typeOfSection}
        />
      )}
    </div>
  );
};

GroupListing.propTypes = {
  shouldShowGroup: object
};

const mapStoreStateToProps = ({ team }) => {
  return {
    shouldShowGroup: team.get('OrgConfig').labels?.['web/labels/create_group']
  };
};

export default UserdashboardErrorBoundary(connect(mapStoreStateToProps)(GroupListing));
