import { translatr } from 'centralized-design-system/src/Translatr';
import LD from '../../../containers/LDStore';
import { profileTabPaths, } from './profileTabPaths';
import { isNewProfileEnabled } from '../../MyProfileV2/utils';
import { isNewContentEnabled } from '../../MyContentV2/utils';
import { isNewMyLearningEnabled } from '../../MyLearningV2/utils';
import { ProfileTabKey, ProfileTabToShow } from './types';

export const fetchAllTabs = (orgProfilePageSettings:  { [K in ProfileTabKey]: ProfileTabToShow }, isFsPrime: boolean): Array<ProfileTabToShow> => {
  const newProfileEnabled: boolean = isNewProfileEnabled();
  const newContentEnabled: boolean = isNewContentEnabled();
  const newMyLearningEnabled: boolean = isNewMyLearningEnabled();

  /**
   * Make sure not to modify global state as it will affect profile dropdown menus so
   * making profile dropdown menus show only what is in cms config. If you wish to have your tab
   * show up as a profile dropdown menu, then need to properly add key to cms
   **/
  const tabsToShow: { [K in ProfileTabKey]: ProfileTabToShow } = { ...orgProfilePageSettings };

  tabsToShow[profileTabPaths.profile.key] = {
    ...tabsToShow[profileTabPaths.profile.key],
    visible: !newProfileEnabled && tabsToShow[profileTabPaths.profile.key]?.visible,
  };
  tabsToShow[profileTabPaths.skillsPassport.key] = {
    key: profileTabPaths.skillsPassport.key,
    index: 1,
    defaultLabel: profileTabPaths.skillsPassport.label,
    label: '',
    visible: !newProfileEnabled
  };
  tabsToShow[profileTabPaths.overview.key] = {
    key: profileTabPaths.overview.key,
    index: 0,
    defaultLabel: profileTabPaths.overview.label,
    label: translatr('web.myprofile.main', 'OverviewTabLabel'),
    visible: newProfileEnabled
  };
  tabsToShow[profileTabPaths.skills.key] = {
    key: profileTabPaths.skills.key,
    index: 1,
    defaultLabel: profileTabPaths.skills.label,
    label: translatr('web.myprofile.main', 'SkillsTabLabel'),
    visible: newProfileEnabled
  };
  tabsToShow[profileTabPaths.content.key] = {
    ...tabsToShow[profileTabPaths.content.key],
    visible: !newContentEnabled && tabsToShow[profileTabPaths.content.key]?.visible,
  };

  tabsToShow[profileTabPaths.learning.key] = {
    ...tabsToShow[profileTabPaths.learning.key],
    visible: !newMyLearningEnabled && tabsToShow[profileTabPaths.learning.key]?.visible,
  };

  // If enabled and not saved in Admin, add it in as a default true
  tabsToShow[profileTabPaths.subscription.key] = {
    key: profileTabPaths.subscription.key,
    index: 10,
    defaultLabel: profileTabPaths.subscription.label,
    label: '',
    visible: LD.isSubscriptionFeatureEnabled() && !window.__edOrgData.OrgConfig.web.profile[profileTabPaths.subscription.key],
  };

  /**
   * For ledger we do not have any admin config rather, hence adding a custom config from here
   */
  tabsToShow[profileTabPaths.ledger.key] = {
    key: profileTabPaths.ledger.key,
    index: 12,
    defaultLabel: profileTabPaths.ledger.label,
    label: '',
    visible: isFsPrime
  };

  return Object.keys(tabsToShow || {})
    .map((key: ProfileTabKey) => {
    return {
      ...tabsToShow[key],
      key,
      index: tabsToShow[key].index || -1
    }
  }).sort((a, b) => a.index - b.index)
    .filter(tab => tab.visible);
};
