import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';

import UserdashboardErrorBoundary from '@components/UserdashboardErrorBoundary/UserdashboardErrorBoundary';
import { useProfileContainerContext } from '../Common/ProfileContainerContext';
import { isNewProfileEnabled } from '../../../pages/MyProfileV2/utils';
import { isNewContentEnabled } from '../../../pages/MyContentV2/utils';

import './ProfileTabs.scss';
import {
  PublicProfileTabInfo,
  PublicProfileTabKey,
  PublicProfileTabs
} from './types';
import PublicTabElement from './PublicTabElement';
import { getTabId } from '@pages/utils';

function PublicProfileTabs() {
  const navigate = useNavigate();

  const newProfileEnabled: boolean = isNewProfileEnabled();
  const newMyContentEnabled: boolean = isNewContentEnabled();

  const {
    handle,
    profileShowUserContent,
    profileShowUserSkillsPassport,
    pathname,
    orgProfilePageSettings,
    setActiveTabId,
  } = useProfileContainerContext();

  const profileTabVisible = !newProfileEnabled && orgProfilePageSettings['web/profile/profile'].visible;
  const skillsPassportTabVisible = !newProfileEnabled && profileShowUserSkillsPassport;
  const contentTabVisible = profileShowUserContent && !newMyContentEnabled;

  const publicTabs: PublicProfileTabs = {
    Overview: {
      label: translatr('web.myprofile.main', 'OverviewTabLabel'),
      path: `/@${handle}/overview`,
      pathsList: [`/@${handle}/overview/experiences`, `/@${handle}/overview/certifications`, `/@${handle}/overview/badges`, `/@${handle}/overview/groups`, `/@${handle}/overview/channels`, `/@${handle}/overview/activity`, `/@${handle}/overview/content`, `/@${handle}/in-progress`],
      visible: newProfileEnabled
    },
    Skills: {
      label: translatr('web.myprofile.main', 'SkillsTabLabel'),
      path: `/@${handle}/skills`,
      visible: newProfileEnabled
    },
    Profile: {
      label: translatr('web.myprofile.main', 'Profile'),
      path: `/@${handle}`,
      visible: profileTabVisible
    },
    SkillsPassport: {
      label: translatr('web.myprofile.main', 'SkillsPassport'),
      path: `/@${handle}/skills-passport`,
      visible: skillsPassportTabVisible
    },
    Content: {
      label: translatr('web.myprofile.main', 'Content'),
      path: `/@${handle}/content`,
      visible: contentTabVisible
    }
  }

  useEffect(() => {
    let firstAvailableTab: PublicProfileTabKey;
    for (const [key, details] of Object.entries(publicTabs) as Array<[PublicProfileTabKey, PublicProfileTabInfo]>) {
      if (details?.visible) {
        if (!firstAvailableTab) {
          firstAvailableTab = key;
        }
        const isTabActive = pathname === details.path || details.pathsList?.includes(pathname);
        if (isTabActive) {
          const id = getTabId(details.label)
          setActiveTabId(id);
          return;
        }
      }
    }
    if (firstAvailableTab) {
      navigate(publicTabs[firstAvailableTab].path, { replace: true });
    }
  }, [pathname]);


  return (
    <div className="ed-ui profile-tabs-ui flex-space-between">
      <div className="no-margin ul-flex ul-grid">
        <ul className="tabs m-padding-sides m-margin-ends flex" role="tablist">
          {
            Object.entries(publicTabs)
              .filter(([, details]: [key: PublicProfileTabKey, details: PublicProfileTabInfo]) => details.visible)
              .map(([, details]: [key: PublicProfileTabKey, details: PublicProfileTabInfo]) => (
                <PublicTabElement tabDetails={details} id={getTabId(details.label)} />
              ))
          }
        </ul>
      </div>
    </div>
  );
}

export default UserdashboardErrorBoundary(PublicProfileTabs);

