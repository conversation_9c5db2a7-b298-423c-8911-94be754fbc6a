import React, { useState, useEffect } from 'react';
import { func, array, object, string, bool } from 'prop-types';
import { connect } from 'react-redux';
import isEqual from 'lodash/isEqual';
import { Fields } from '../types';
import SkillByLevel from './SkillByLevel';
import { SuggestedSkills } from './SuggestedSkills';
import { SKILL_LEVEL, SKILL_STATUS, filterItemByKey, getSkillLevels } from './helpers';
import { useSuggestedSkills } from './hooks';
import { useDebounce } from '@components/TopNav/SearchV2/utils';
import {
  getAllProficiencyLevels,
  noLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';

const SkillWrapper = ({
  suggestionsPayload,
  skillsItem,
  updateFieldValue,
  currentUserLanguage,
  isNoLevelEnabled = false,
  defaultProficiencySkillLevel,
  required = false
}) => {
  const [skills, setSkills] = useState(skillsItem ? skillsItem : []);
  const [suggestedSkills, setSuggestedSkills] = useState([]);
  const [getSuggestedSkills, { data: suggestedSkillsData }] = useSuggestedSkills();
  const debouncedSuggestions = useDebounce(suggestionsPayload, 1000);
  const proficiencyLevels = getSkillLevels([
    ...(isNoLevelEnabled
      ? [{ ...noLevelPlaceholderOption(), level: '-1', range: { from: -1, to: -1 } }]
      : []),
    ...getAllProficiencyLevels(window.__edOrgData.proficiencyLevels, currentUserLanguage)
  ]);

  useEffect(() => {
    if (debouncedSuggestions?.title) {
      getSuggestedSkills(debouncedSuggestions);
    }
  }, [debouncedSuggestions]);

  useEffect(() => {
    if (!isEqual(skills, skillsItem || [])) {
      updateFieldValue(Fields.SKILLS, skills);
    }
    const tempSuggs = [];
    suggestedSkills.forEach(suggSkill => {
      if (!skills.find(skill => skill.id === suggSkill.id)) {
        tempSuggs.push(suggSkill);
      }
    });
    setSuggestedSkills(tempSuggs);
  }, [skills]);

  useEffect(() => {
    const tempSuggSkills = [];
    suggestedSkillsData?.forEach(({ id, name, label, external_data }) => {
      if (!skills.find(skill => skill.id === id)) {
        tempSuggSkills.push({
          id,
          label,
          name,
          selected: false,
          level: SKILL_LEVEL.INTERMEDIATE,
          external_data
        });
      }
    });
    setSuggestedSkills([...tempSuggSkills]);
  }, [suggestedSkillsData]);

  const handleRemoveSkill = skill => {
    const tempSkills = [...skills];
    const updatedSkills = filterItemByKey(tempSkills, 'id', skill.id);
    setSkills(updatedSkills);
  };

  const handleSuggestedSkillAdd = suggestedSkillsToAdd => {
    const skillMap = suggestedSkillsToAdd?.map(skill => ({
      id: skill.id,
      value: skill.id,
      label: skill.label,
      name: skill.name,
      level: skill.level,
      status: SKILL_STATUS.DETECTED,
      external_data: skill.external_data
    }));
    const temp = [...skills, ...skillMap];
    setSkills(temp);
  };

  const handleSkillAdd = selectedSkill => {
    const temp = [
      ...skills,
      {
        id: selectedSkill.val?.id,
        value: selectedSkill.val?.value,
        name: selectedSkill.val?.name,
        label: selectedSkill.val?.label,
        level: selectedSkill.level,
        status: selectedSkill.status,
        external_data: selectedSkill.val?.external_data
      }
    ];
    setSkills(temp);
  };

  return (
    <div className="skill-container" aria-live="polite">
      {suggestedSkills?.length !== 0 && (
        <SuggestedSkills
          selectedSkillsLength={skills.length}
          skills={suggestedSkills}
          skillCallback={handleSuggestedSkillAdd}
          skillLevels={proficiencyLevels}
          defaultProficiencySkillLevel={defaultProficiencySkillLevel}
        />
      )}

      <SkillByLevel
        skills={skills}
        onSkillChange={handleSkillAdd}
        onRemoveSkill={handleRemoveSkill}
        skillLevels={proficiencyLevels}
        required={required}
      />
    </div>
  );
};

SkillWrapper.propTypes = {
  updateFieldValue: func,
  suggestionsPayload: object,
  skillsItem: array,
  currentUserLanguage: string,
  isNoLevelEnabled: bool,
  defaultProficiencySkillLevel: string,
  required: bool
};

export default connect(state => ({
  currentUserLanguage: state.currentUser.get('profile')?.get?.('language') || 'en'
}))(SkillWrapper);
