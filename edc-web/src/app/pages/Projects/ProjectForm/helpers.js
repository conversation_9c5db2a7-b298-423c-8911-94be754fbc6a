import React from 'react';
import omitBy from 'lodash/omitBy';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import every from 'lodash/every';
import flow from 'lodash/flow';
import omit from 'lodash/omit';

import { IMAGE_SOURCE } from '@components/ImageUploader';
import { Fields } from '../types';
import { translatr } from 'centralized-design-system/src/Translatr';

export const getFormValues = fields => ({
  capacity: fields[Fields.CAPACITY],
  description: fields[Fields.DESCRIPTION],
  organizations: fields[Fields.ORGANIZATIONS],
  endDate: fields[Fields.DURATION_END_DATE],
  jobRoles: fields[Fields.JOB_ROLES],
  languages: fields[Fields.LANGUAGES],
  locations: fields[Fields.LOCATIONS],
  projectOwners: fields[Fields.OWNERS],
  remoteWorkPossible: fields[Fields.IS_REMOTE_WORK_POSSIBLE],
  requireApplication: fields[Fields.IS_APPLICATION_AVAILABLE],
  skills: fields[Fields.SKILLS],
  startDate: fields[Fields.DURATION_START_DATE],
  thumbnail: fields[Fields.THUMBNAIL],
  timeZones: fields[Fields.TIME_ZONES],
  timeCommitment: fields[Fields.TIME_COMMITMENT],
  title: fields[Fields.TITLE]?.trim(),
  applicationDeadline: fields[Fields.APPLICATION_DEADLINE]
});

export const createProjectPayload = (
  {
    capacity,
    description,
    endDate,
    jobRoles,
    languages,
    locations,
    organizations,
    projectOwners,
    remoteWorkPossible,
    requireApplication,
    skills,
    startDate,
    thumbnail = {},
    timeCommitment,
    timeZones,
    title,
    applicationDeadline
  },
  status
) => {
  const PROJECT_FIELDS_CONFIG = global?.__edOrgData?.configs?.find(
    f => f.name === 'project_fields_config'
  )?.value;
  const correctedEndDate = endDate && new Date(endDate);
  correctedEndDate?.setHours(23, 59, 59);
  if (thumbnail?.source === IMAGE_SOURCE.LXMEDIAHUB) {
    // camelCase expected by the backend
    thumbnail.contentType = thumbnail['content_type'];
    thumbnail.uploadSource = thumbnail['upload_source'];
    delete thumbnail['content_type'];
    delete thumbnail['upload_source'];
    delete thumbnail['url'];
  }
  const payload = {
    title,
    description,
    thumbnail:
      thumbnail?.source === IMAGE_SOURCE.FILESTACK ? { ...thumbnail, url: undefined } : thumbnail,
    startDate,
    endDate: correctedEndDate,
    timeCommitment,
    status,
    remoteWorkPossible,
    requireApplication: PROJECT_FIELDS_CONFIG
      ? PROJECT_FIELDS_CONFIG['project-application-required']?.showField
        ? requireApplication
        : true
      : requireApplication,
    owners: projectOwners.map(o => o.value),
    roles: jobRoles.map(jr => jr.id),
    timezones: timeZones.map(tz => tz.value),
    languages: languages.map(l => l.value),
    locations: locations,
    maxPositions: parseInt(capacity),
    skillDetails: skills.map(s => ({
      id: s.id,
      level: s.level,
      status: s.status
    })),
    divisions: organizations.map(div => div.value),
    applicationDeadline
  };

  return flow(
    // removes null or undefined values
    prev => omitBy(prev, isNil),
    // removes empty strings
    prev =>
      omitBy(prev, v => {
        if (typeof v === 'string') {
          return !v;
        }
      }),
    // removes empty arrays
    prev =>
      omitBy(prev, v => {
        if (Array.isArray(v)) {
          return isEmpty(v);
        }
      }),
    // removes objects that has all null or undefined values
    prev =>
      omitBy(prev, v => {
        if (typeof v === 'object' && !(v instanceof Date)) {
          return every(v, isNil);
        }
      }),
    // removes max positions if require application is false
    prev => {
      if (!prev.requireApplication) {
        return omit(prev, 'maxPositions');
      }

      return prev;
    },
    // convert dates to isostring
    prev => {
      if (prev.startDate) {
        prev.startDate = prev.startDate.toISOString();
      }

      if (prev.endDate) {
        prev.endDate = prev.endDate.toISOString();
      }

      if (prev.applicationDeadline) {
        prev.applicationDeadline = prev.applicationDeadline.toISOString();
      }

      return prev;
    }
  )(payload);
};

export const getInitialFieldsFromData = (data, availableLanguages) => {
  const PROJECT_FIELDS_CONFIG = global?.__edOrgData?.configs?.find(
    f => f.name === 'project_fields_config'
  )?.value;

  return {
    [Fields.CAPACITY]: !data.maxPositions || data.maxPositions < 0 ? '1' : `${data.maxPositions}`,
    [Fields.DESCRIPTION]: data.description || '',
    [Fields.DURATION_END_DATE]: data.endDate || undefined,
    [Fields.DURATION_START_DATE]: data.startDate || undefined,
    [Fields.IS_APPLICATION_AVAILABLE]:
      data.isApplicationRequired !== undefined
        ? data.isApplicationRequired
        : PROJECT_FIELDS_CONFIG &&
          !PROJECT_FIELDS_CONFIG['project-application-required']?.showField,
    [Fields.IS_REMOTE_WORK_POSSIBLE]: data.isRemotePossible,
    [Fields.JOB_ROLES]: data.jobRoles || [],
    [Fields.LANGUAGES]:
      data.languages?.map(l => ({
        id: l,
        value: l,
        label: availableLanguages[l]
      })) || [],
    [Fields.LOCATIONS]: data.locations || [],
    [Fields.ORGANIZATIONS]: data.divisions || [],
    [Fields.OWNERS]:
      data.projectOwners?.map(o => {
        const handle = o.handle[0] === '@' ? o.handle : `@${o.handle}`;
        return {
          ...o,
          label: (
            <>
              <i className="icon-user-light s-margin-right" />
              {`${o.fullName} (${handle})`}
            </>
          ),
          textLabel: `${o.fullName} (${handle})`
        };
      }) || [],
    [Fields.SKILLS]: data.skills || [],
    [Fields.THUMBNAIL]: data.thumbnail,
    [Fields.TIME_COMMITMENT]: data.timeCommitment || '',
    [Fields.TIME_ZONES]: data.timeZones || [],
    [Fields.TITLE]: data.title || '',
    [Fields.APPLICATION_DEADLINE]: data.applicationDeadline || undefined
  };
};

export const SKILL_STATUS = {
  DETECTED: 'detected',
  DECLARED: 'declared'
};

export const SKILL_LEVEL = {
  BEGINNER: 'Beginner',
  INTERMEDIATE: 'Intermediate',
  ADVANCED: 'Advanced'
};

export const filterItemByKey = (data, key, value) => {
  return data.filter(skill => skill[key] !== value);
};

export const getCurrentUserLanguage = () => {
  const orgDefaultLang = global?.__edOrgData?.configs?.find(
    config => config.name === 'DefaultOrgLanguage'
  )?.value;
  const userLang = global?.__ED__?.profile?.language;
  return userLang || orgDefaultLang || 'en';
};

export function getLevellabel() {
  let skillLabelArray = [];
  const userLanguage = getCurrentUserLanguage();
  const supportedLanguages = window?.__edOrgData?.languages || {};
  const language = supportedLanguages
    ? Object.keys(supportedLanguages)
        .find(key => userLanguage === supportedLanguages[key])
        ?.toLowerCase()
    : supportedLanguages;
  const config =
    global?.__edOrgData?.configs?.find(f => f.name === 'OrgCustomizationConfig')?.value?.web
      ?.labels || {};
  const configKeys = [
    { [SKILL_LEVEL.BEGINNER]: 'web/labels/beginner' },
    { [SKILL_LEVEL.INTERMEDIATE]: 'web/labels/intermediate' },
    { [SKILL_LEVEL.ADVANCED]: 'web/labels/advanced' }
  ];

  configKeys.forEach(keyValuePair => {
    const key = Object.keys(keyValuePair)[0];
    const value = keyValuePair[key];
    const skillLabelToUse =
      language && config[value]?.languages && config[value]?.languages[language]
        ? config[value]?.languages[language]
        : config[value]?.label || translatr('web.common.main', key) || config[value]?.defaultLabel;
    skillLabelArray[key] = skillLabelToUse;
  });
  return skillLabelArray;
}

export const getSkillLevels = proficiencyLevels => {
  return [
    ...proficiencyLevels.map(item => ({
      label: item.label,
      value: item.level,
      range: item.range,
      hidden: item.hidden,
      translatedLevelDescription: item.translatedLevelDescription
    }))
  ];
};

export const sortByIndex = data => {
  return data.slice().sort((a, b) => {
    const indexA = parseInt(a.index, 10);
    const indexB = parseInt(b.index, 10);
    return indexA - indexB;
  });
};

export const getInitialOrg = (organizations, id) => {
  const data = organizations.filter(item => item.orgType === id);
  return data.length > 0 ? data : null;
};
