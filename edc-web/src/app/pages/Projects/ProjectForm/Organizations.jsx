import React, { useState, useEffect } from 'react';
import { func, object, string, array, bool, number } from 'prop-types';
import { Field } from './Form';
import { Fields } from '../types';
import { AsyncSearchInput } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getInitialOrg } from './helpers';
import { organizationUsage } from '@actions/organizationsActions';
import { searchOrgV2 } from 'edc-web-sdk/requests/organizations.js';
import {
  transformOrganizationsToTreeData,
  orgDataToSearchResults
} from 'centralized-design-system/src/TreeView/dataMappers';
import TreeView from 'centralized-design-system/src/TreeView/TreeView';

const Organizations = ({
  orgType,
  organizations,
  organizationsByTypeData,
  showTreeView,
  levelsToDisplay,
  updateFieldValue,
  language,
  error,
  required = false
}) => {
  const [selectedOrg, setSelectedOrg] = useState(getInitialOrg(organizations, orgType.id));
  const [currSelectedIds, setCurrSelectedIds] = useState([]);
  const [orgData, setOrgData] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const MIN_DATA_LEVEL = 1;
  const title = orgType.label;

  useEffect(() => {
    if (!showTreeView) setSelectedOrg(getInitialOrg(organizations, orgType.id));
  }, [showTreeView, organizations]);

  useEffect(() => {
    if (showTreeView && organizationsByTypeData && organizationsByTypeData[orgType.id]) {
      const convertedData = transformOrganizationsToTreeData({
        orgs: organizationsByTypeData[orgType.id],
        maxLevel: levelsToDisplay,
        childLevelRequirement: levelsToDisplay
      });
      setOrgData(convertedData);
      const currSelectedOrgs = getInitialOrg(organizations, orgType.id);
      if (currSelectedOrgs) {
        setCurrSelectedIds([currSelectedOrgs[0].value]);
      }
    }
  }, [showTreeView, organizationsByTypeData]);

  useEffect(() => {
    if (!Array.isArray(selectedOrg)) {
      let data = [];
      if (selectedOrg) {
        data = [
          ...organizations,
          { label: selectedOrg?.label, value: selectedOrg?.value, orgType: orgType.id }
        ];
      } else {
        data = organizations.filter(org => org.orgType !== orgType.id);
      }
      updateFieldValue(Fields.ORGANIZATIONS, data);
    }
  }, [selectedOrg]);

  const handleOrgSelect = data => {
    const selectedIds = [...data?.treeState?.selectedIds];
    const selectedId = selectedIds?.length > 0 ? selectedIds[0] : null;
    if (selectedId) {
      const currentOrgData = organizationsByTypeData[orgType.id];
      const selectedOrganization = currentOrgData.filter(item => item.id === selectedId);
      setSelectedOrg({ label: selectedOrganization.title, value: selectedId, orgType: orgType.id });
    } else {
      setSelectedOrg(null);
    }
  };

  const onOrgSearch = async query => {
    setIsSearching(true);
    try {
      const response = await searchOrgV2(
        {
          orgType: orgType.id,
          minLevel: MIN_DATA_LEVEL,
          maxLevel: levelsToDisplay,
          searchText: query,
          pageSize: 2000,
          filterInactive: true,
          resolveParent: true,
          resolveAllParents: true
        },
        language
      );

      if (!response?.divisions) {
        throw new Error('No data found');
      }

      const convertedData = orgDataToSearchResults(
        response.divisions,
        MIN_DATA_LEVEL,
        levelsToDisplay
      );
      setIsSearching(false);
      return Promise.resolve(convertedData);
    } catch (err) {
      setIsSearching(false);
      console.error('Error fetching data:', err);
    }
  };

  return (
    <div>
      <Field
        id={
          showTreeView
            ? `ed-tree-view-search-input-${orgType.id}`
            : `project-custom-organization-unit-${orgType.id}`
        }
        length={selectedOrg ? 1 : 0}
        maxLength={1}
        suffix={title}
        title={title}
        error={error}
        isProjectOrganizationField={true}
      >
        {showTreeView ? (
          <TreeView
            key={`org1-${orgData?.length}`}
            uniqueId={orgType.id}
            data={orgData}
            defaultSelectedIds={currSelectedIds}
            variant="single"
            onSelect={props => {
              handleOrgSelect(props);
            }}
            isSearching={isSearching}
            onSearch={onOrgSearch}
            searchPlaceholder={translatr('web.talentmarketplace.main', 'SearchOrganizations', {
              tm_organizations: title
            })}
            ariaLabel={translatr('web.talentmarketplace.main', 'SearchOrganizations', {
              tm_organizations: title
            })}
            searchable={true}
            required={required}
          />
        ) : (
          <AsyncSearchInput
            ariaLabelledby={`project-custom-organization-unit-${orgType.id}-label`}
            fieldRequired={required}
            organizations
            users={false}
            channels={false}
            groups={false}
            items={selectedOrg}
            extraData={{
              currentUserLang: language,
              orgType: orgType.id,
              context: organizationUsage.PROJECTS_FORM
            }}
            placeholder={translatr('web.talentmarketplace.main', 'SearchOrganizations', {
              tm_organizations: title
            })}
            onChange={val => setSelectedOrg(val)}
            isClearable
          />
        )}
      </Field>
    </div>
  );
};

Organizations.propTypes = {
  updateFieldValue: func,
  organizations: array,
  organizationsByTypeData: object,
  showTreeView: bool,
  levelsToDisplay: number,
  orgType: object,
  language: string,
  error: string,
  required: bool
};

export default Organizations;
