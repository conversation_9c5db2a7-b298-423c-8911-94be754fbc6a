import React, { useCallback, useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import every from 'lodash/every';
import values from 'lodash/values';
import entries from 'lodash/entries';
import isEqual from 'lodash/isEqual';
import {
  createProject,
  updateProject,
  PROJECT_STATUS,
  OPPORTUNITY_TYPE,
  getApplicationStats
} from 'edc-web-sdk/requests/careerOportunities.v2';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';

import TextField from 'centralized-design-system/src/Inputs/TextField';
import Checkbox from 'centralized-design-system/src/Checkbox';
import Switch from 'centralized-design-system/src/Switch';
import { AsyncSearchInput, MultiSelectSearch } from 'centralized-design-system/src/Inputs';
import Tooltip from 'centralized-design-system/src/Tooltip';
import DatePickerV2 from 'centralized-design-system/src/DatePickerV2';

import { focusOnFirstErroredField, scrollToFirstError } from '@utils/scrollToFirstError';
import { transformLanguageDetails } from '@utils/transformLanguages';
import BackArrow from '@components/backarrow';
import ImageUploader from '@components/ImageUploader';
import LazyloadComponent from '@components/LazyloadComponent';
import ImagePlaceHolderSvg from '@components/ProjectCard/shared/ImagePlaceHolderSvg';

import { open_v2 as openSnackBar } from 'actions/snackBarActions';

import { reportOpportunityPublished } from '@analytics/OmpEventRecorder';

import { Fields } from '../types';

import Header from './Header';
import { Field, Form } from './Form';
import FooterActions from './FooterActions';
import PostPublishModal from './PostPublishModal';
import SameTitleWarningModal from './SameTitleWarningModal';
import {
  MAX_JOB_ROLES,
  MAX_LANGUAGES,
  MAX_LOCATIONS,
  MAX_PROJECT_OWNERS,
  MAX_TIME_COMMITMENT_LENGTH,
  MAX_TIME_ZONES,
  MAX_TITLE_LENGTH,
  MAX_PROJECT_CAPACITY,
  validate
} from './validator';
import {
  createProjectPayload,
  getFormValues,
  getInitialFieldsFromData,
  sortByIndex
} from './helpers';
import SkillWrapper from './SkillWrapper';
import Organizations from './Organizations';
import {
  loadAllOrganizations,
  getOrgConfiguration,
  orgVisibility,
  orgAssociation,
  organizationUsage
} from '@actions/organizationsActions';
import './styles.scss';
import { getAvailableLocations } from '@actions/availableLocationsActions';
import { LOCATION_ASSOCIATION } from '../../TalentMarketplace/helpers';
import { getTimeZone } from '@actions/timeZonesActions';

import { getGenAIConfig } from 'edc-web-sdk/requests/superAdmin';
import { ENTITY } from 'centralized-design-system/src/constants/genAiConstants';
import { useGenAiAssistantContext } from 'centralized-design-system/src/GenAiAssistant';
import formatDate from '@utils/formatDate';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';
import { useLocTreeViewSearch } from '@pages/TalentMarketplace/shared/filters/hooks';
import ProjectTreeView from './ProjectTreeView';
import { transformOrganizationsToTreeData } from 'centralized-design-system/src/TreeView/dataMappers';
import { getSelectionForInput } from 'centralized-design-system/src/TreeView/utils';
const Editor = LazyloadComponent(() => import('centralized-design-system/src/Inputs/ckeditor'))();

const ProjectsContainer = ({
  currentUser,
  data = {},
  duplicating,
  editing,
  languageDetails,
  timeZoneDetails,
  projectId: pid,
  toast,
  organizations,
  loadOrganizations,
  getOrganizationsConfig,
  locationsEnabled,
  locationsAssociation,
  availableLocations,
  dispatch
}) => {
  const status = data.status;
  const initialTitle = data.title;
  const PROJECT_FIELDS_CONFIG = global?.__edOrgData?.configs?.find(
    f => f.name === 'project_fields_config'
  )?.value;

  const navigate = useNavigate();

  const [fields, setFields] = useState(getInitialFieldsFromData(data, languageDetails));
  const [descriptionLoading, setDescriptionLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [drafting, setDrafting] = useState(false);
  const [publishing, setPublishing] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [showPostPublishModal, setShowPostPublishModal] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [showSameTitleWarningModal, setShowSameTitleWarningModal] = useState(false);
  const [projectId, setProjectId] = useState(pid);
  const [orgTypes, setOrgTypes] = useState([]);
  const [integrateGenAIAssistedDescription, setIntegrateGenAIAssistedDescription] = useState(false);
  const { updateGenAiContext, initializeContextForGenAi } = useGenAiAssistantContext();
  const [organizationsByTypeData, setOrganizationsByTypeData] = useState(null);
  const [treeViewVisibility, setTreeViewVisibilty] = useState({});
  const [levelsToDisplay, setLevelsToDisplay] = useState({});
  const [stats, setStats] = useState();
  const [originalStartDate, setOriginalStartDate] = useState();
  const [disableStartDate, toggleDisabledStartDate] = useState(false);
  const [showHiddenFieldWarning, toggleHiddenFieldWarning] = useState(false);
  const [treeViewLocations, setTreeViewLocations] = useState([]);
  const previousData = useRef(null);
  const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
  useEffect(() => {
    if (!organizations.get('config')) {
      getOrganizationsConfig();
    }
    if (!availableLocations) {
      dispatch(getAvailableLocations(currentUser.profile.language));
    }

    if (!timeZoneDetails) {
      dispatch(getTimeZone());
    }
    setDescriptionLoading(true);
    getGenAIConfig()
      .then(res => {
        setIntegrateGenAIAssistedDescription(res?.enable_genai?.value);
        setDescriptionLoading(false);
      })
      .catch(err => {
        console.error('Error while fetching genAI config: ', err);
        setDescriptionLoading(false);
      });

    initializeContextForGenAi(
      {
        [ENTITY.TITLE]: fields[Fields.TITLE],
        [ENTITY.DESCRIPTION]: fields[Fields.DESCRIPTION],
        [ENTITY.PROJECT_DESCRIPTION]: fields[Fields.DESCRIPTION],
        [ENTITY.SKILLS]: fields[Fields.SKILLS],
        [ENTITY.OWNER_JOB_ROLE]: currentUser?.jobFamily?.label,
        [ENTITY.RELATED_JOB_ROLE]: fields[Fields.JOB_ROLES],
        [ENTITY.TIME_COMMITMENT]: fields[Fields.TIME_COMMITMENT],
        [ENTITY.START_DATE]: fields[Fields.DURATION_START_DATE],
        [ENTITY.END_DATE]: fields[Fields.DURATION_END_DATE]
      },
      getConfig('DefaultOrgLanguage', 'en')
    );
    if (editing) {
      getApplicationStats(projectId)
        .then(res => {
          setStats(res.data);
          const { confirmed, inProgress, completed } = res.data;
          toggleDisabledStartDate(confirmed + inProgress + completed > 0);
        })
        .catch(err => {
          console.error(err);
        });

      if (
        (PROJECT_FIELDS_CONFIG &&
          PROJECT_FIELDS_CONFIG['project-duration-selection'] &&
          !PROJECT_FIELDS_CONFIG['project-duration-selection']?.showField &&
          fields[Fields.DURATION_START_DATE]) ||
        (PROJECT_FIELDS_CONFIG &&
          PROJECT_FIELDS_CONFIG['project-application-deadline'] &&
          !PROJECT_FIELDS_CONFIG['project-application-deadline']?.showField &&
          fields[Fields.APPLICATION_DEADLINE])
      ) {
        toggleHiddenFieldWarning(true);
      }
    }
  }, []);

  useEffect(() => {
    if (availableLocations) {
      const availableItems = availableLocations.filter(l => l.title);
      const currentSelectedLocations = fields[Fields.LOCATIONS];
      const availableLocationIds = new Set(availableItems.map(item => item.id));
      const activeLocations = currentSelectedLocations.filter(locationId =>
        availableLocationIds.has(locationId)
      );
      updateFieldValue(Fields.LOCATIONS, activeLocations);
      setTreeViewLocations(
        transformOrganizationsToTreeData({ orgs: availableItems, maxLevel: 99 })
      );
    }
  }, [availableLocations]);

  useEffect(() => {
    if (!isEqual(data, previousData.current)) {
      previousData.current = data;
      setOriginalStartDate(data.startDate);
      setFields(getInitialFieldsFromData(data, languageDetails));
    }
  }, [data]);

  useEffect(() => {
    if (
      organizations.get('config')?.enable &&
      !organizations.get('organizationsByAssociation')[orgAssociation.PROJECT]
    ) {
      loadOrganizations(
        currentUser.profile.language,
        orgVisibility.PROJECT_DETAILS,
        orgAssociation.PROJECT
      );
    }

    if (organizations.get('organizationsByAssociation')[orgAssociation.PROJECT]) {
      const organizationTypes = organizations.get('orgTypes');
      const projectFormOrgs = organizations.get('organizationsByAssociation')[
        orgAssociation.PROJECT
      ];
      const visibleOrgs = organizationTypes.filter(type => projectFormOrgs.includes(type.id));
      setOrgTypes(sortByIndex(visibleOrgs));
    }
  }, [
    organizations.get('config'),
    organizations.get('organizationsByAssociation')[orgAssociation.PROJECT]
  ]);

  useEffect(() => {
    if (organizations.get('organizationsByType')) {
      setOrganizationsByTypeData(organizations.get('organizationsByType'));
    }

    if (organizations.get('organizationsTreeViewVisibility')) {
      setTreeViewVisibilty(organizations.get('organizationsTreeViewVisibility'));
    }

    if (organizations.get('levelForTypeAndVisibility')) {
      setLevelsToDisplay(organizations.get('levelForTypeAndVisibility'));
    }
  }, [
    organizations.get('organizationsByType'),
    organizations.get('organizationsTreeViewVisibility'),
    organizations.get('levelForTypeAndVisibility')
  ]);

  const updateCapacityFieldValue = useCallback(val => {
    // validate if Capacity value is having onlu 0-9
    const regex = /^[0-9]+$|^$/;
    if (regex.test(val)) {
      updateFieldValue(Fields.CAPACITY, val);
    }
  }, []);

  const updateFieldValue = useCallback(
    (fieldId, value) => {
      setFields(f => ({ ...f, [fieldId]: value }));
      setShowCancelConfirmation(true);
    },
    [setFields, setShowCancelConfirmation]
  );

  const onSaveDraftClick = useCallback(() => {
    setSubmitted(true);

    const formValues = getFormValues(fields);
    const formErrors = validate(
      { submitted: true, editing, originalCapacity: `${data?.maxPositions}` },
      formValues
    );

    if (every(values(formErrors), v => !v)) {
      setDrafting(true);

      createProject(createProjectPayload(formValues, PROJECT_STATUS.DRAFT))
        .then(() => {
          setDrafting(false);
          toast(
            translatr('web.projects.main', 'ProjectSavedAsDraftSuccessfully', {
              tm_tm_project: omp('tm_tm_project')
            })
          );
          navigate(`/career/${OPPORTUNITY_TYPE.PROJECT}/drafts`);
        })
        .catch(() => {
          setDrafting(false);
          toast(
            translatr(
              'web.projects.main',
              'ThereWasAnErrorWhileTryingToSaveYourDraftPleaseTryAgain',
              { tm_tm_project: omp('tm_tm_project') }
            )
          );
        });
    } else {
      setErrors(formErrors);
      scrollToFirstError();
      focusOnFirstErroredField();
    }
  }, [fields, setDrafting, setErrors, setSubmitted, toast]);

  const onPublishClick = useCallback(() => {
    setSubmitted(true);
    const formValues = getFormValues(fields);
    const formErrors = validate({ submitted: true, publishing: true }, formValues);

    if (every(values(formErrors), v => !v)) {
      setPublishing(true);

      // need to update vs create when publishing a draft project
      const request = projectId
        ? updateProject(projectId, createProjectPayload(formValues, PROJECT_STATUS.PUBLISHED))
        : createProject(createProjectPayload(formValues, PROJECT_STATUS.PUBLISHED));

      request
        .then(response => {
          setProjectId(response.data.id);
          setPublishing(false);
          setShowPostPublishModal(true);
          reportOpportunityPublished(OPPORTUNITY_TYPE.PROJECT, response.data.id, {
            published_by: response.data.publishedBy,
            published_at: response.data.publishedOn
          });
        })
        .catch(() => {
          setPublishing(false);
          toast(
            translatr('web.projects.main', 'ThereWasAnErrorWhileTryingToPublishYourProject', {
              tm_tm_project: omp('tm_tm_project')
            })
          );
        });
    } else {
      setErrors(formErrors);
      scrollToFirstError();
      focusOnFirstErroredField();
    }
  }, [
    fields,
    projectId,
    setErrors,
    setProjectId,
    setPublishing,
    setShowPostPublishModal,
    setSubmitted,
    toast
  ]);

  const onPublishDuplicateClick = useCallback(() => {
    const formValues = getFormValues(fields);
    const formErrors = validate({ submitted: true, publishing: true }, formValues);

    setSubmitted(true);

    if (every(values(formErrors), v => !v)) {
      if (initialTitle?.trim() === formValues.title?.trim()) {
        setShowSameTitleWarningModal(true);
      } else {
        onPublishClick();
      }
    } else {
      setErrors(formErrors);
      scrollToFirstError();
      focusOnFirstErroredField();
    }
  }, [fields, setErrors, setShowSameTitleWarningModal, setSubmitted]);

  const onPublishDuplicateConfirm = useCallback(() => {
    onPublishClick();
    setShowSameTitleWarningModal(false);
  }, [onPublishClick, setShowSameTitleWarningModal]);

  const onUpdateClick = useCallback(() => {
    setSubmitted(true);

    const formValues = getFormValues(fields);
    const formErrors = validate(
      {
        submitted: true,
        editing,
        originalCapacity: `${data?.maxPositions}`,
        stats,
        status: data?.status,
        originalStartDate
      },
      formValues
    );

    if (every(values(formErrors), v => !v)) {
      setUpdating(true);

      updateProject(projectId, createProjectPayload(formValues, status))
        .then(() => {
          setUpdating(false);
          setShowCancelConfirmation(false);
          toast(
            translatr('web.projects.main', 'ProjectSavedSuccessfully', {
              tm_tm_project: omp('tm_tm_project')
            })
          );
          navigate(-1);
        })
        .catch(() => {
          setUpdating(false);
          toast(
            translatr(
              'web.projects.main',
              'ThereWasAnErrorWhileTryingToSaveYourProjectPleaseTryAgain',
              { tm_tm_project: omp('tm_tm_project') }
            )
          );
        });
    } else {
      setErrors(formErrors);
      scrollToFirstError();
      focusOnFirstErroredField();
    }
  }, [fields, setErrors, setSubmitted, setUpdating, toast]);

  useEffect(() => {
    setErrors(
      validate(
        {
          publishing: true,
          submitted,
          editing: editing && status !== PROJECT_STATUS.DRAFT,
          originalCapacity: `${data?.maxPositions}`,
          stats,
          status,
          originalStartDate
        },
        {
          description: fields[Fields.DESCRIPTION],
          endDate: fields[Fields.DURATION_END_DATE],
          jobRoles: fields[Fields.JOB_ROLES],
          languages: fields[Fields.LANGUAGES],
          locations: fields[Fields.LOCATIONS],
          organizations: fields[Fields.ORGANIZATIONS],
          projectOwners: fields[Fields.OWNERS],
          skills: fields[Fields.SKILLS],
          startDate: fields[Fields.DURATION_START_DATE],
          thumbnail: fields[Fields.THUMBNAIL],
          timeZones: fields[Fields.TIME_ZONES],
          timeCommitment: fields[Fields.TIME_COMMITMENT],
          title: fields[Fields.TITLE],
          capacity: fields[Fields.CAPACITY],
          requireApplication:
            PROJECT_FIELDS_CONFIG &&
            PROJECT_FIELDS_CONFIG['project-application-required']?.showField
              ? fields[Fields.IS_APPLICATION_AVAILABLE]
              : true,
          applicationDeadline: fields[Fields.APPLICATION_DEADLINE]
        }
      )
    );
  }, [fields, setErrors, submitted]);

  const resetCalendar = () => {
    if (!disableStartDate) updateFieldValue(Fields.DURATION_START_DATE, undefined);
    updateFieldValue(Fields.DURATION_END_DATE, undefined);
  };

  const resetApplicationDeadline = () => {
    updateFieldValue(Fields.APPLICATION_DEADLINE, undefined);
  };

  const getDayBeforeStartDate = date => {
    const updatedDate = new Date(date);
    updatedDate.setDate(updatedDate.getDate() - 1);
    return updatedDate;
  };

  const getNextDate = () => {
    const updatedDate = new Date();
    updatedDate.setDate(updatedDate.getDate() + 1);
    return updatedDate;
  };

  const applicationRequiredWarningMessage =
    editing && status !== PROJECT_STATUS.DRAFT
      ? translatr(
          'web.projects.main',
          'ThisCantBeChangedSinceItIsAlreadyPublishedAndMightConflictWithExistingApplications'
        )
      : translatr(
          'web.projects.main',
          'ThisCantBeChangedOncePublishedSinceItMightConflictWithExistingApplications'
        );
  return (
    <div className="ed-ui create-edit-projects-wrapper">
      <BackArrow
        goToPrevPage
        needWarningModal={showCancelConfirmation}
        label={translatr('web.common.main', 'Back')}
        pageHistory={[]}
      />
      <div className="projects-form-container block no-bottom-radius">
        <div className="projects-inner-container">
          <Header
            duplicating={duplicating}
            editing={editing}
            status={status}
            showHiddenFieldWarning={showHiddenFieldWarning}
          />
          <Form id="create-edit-form" aria-labelledby="create-edit-projects-header">
            <Field
              required
              error={errors[Fields.TITLE]}
              id="project-title"
              length={fields[Fields.TITLE].length}
              maxLength={MAX_TITLE_LENGTH}
              suffix="left"
              title={translatr('web.projects.main', 'ProjectTitle', {
                tm_tm_project: omp('tm_tm_project')
              })}
              type="text"
            >
              <TextField
                required={PROJECT_FIELDS_CONFIG?.['project-title']?.required}
                placeholder={translatr('web.projects.main', 'EnterProjectTitle', {
                  tm_tm_project: omp('tm_tm_project')
                })}
                setValue={val => {
                  updateFieldValue(Fields.TITLE, val);
                  if (integrateGenAIAssistedDescription) {
                    updateGenAiContext({ key: ENTITY.TITLE, value: val });
                  }
                }}
                defaultValue={fields[Fields.TITLE]}
              />
            </Field>
            {!descriptionLoading && (
              <Field
                required
                error={errors[Fields.DESCRIPTION]}
                id="project-description"
                title={translatr('web.projects.main', 'Description')}
                type="text"
              >
                <Editor
                  defaultValue={fields[Fields.DESCRIPTION]}
                  placeholder={translatr(
                    'web.common.main',
                    'ProvideAHighLevelOverviewToHelpCandidatesDecideIfT'
                  )}
                  setValue={val => {
                    updateFieldValue(Fields.DESCRIPTION, val);
                    if (
                      integrateGenAIAssistedDescription &&
                      !window.CKEDITOR?.instances['project-description']?.isGenAiGenerated
                    ) {
                      updateGenAiContext({
                        key: ENTITY.DESCRIPTION,
                        value: val
                      });
                      updateGenAiContext({
                        key: ENTITY.PROJECT_DESCRIPTION,
                        value: val
                      });
                    }
                  }}
                  name="project-description"
                  required={PROJECT_FIELDS_CONFIG?.['project-description']?.required}
                  editorType="inline"
                  genAiAssistantProps={{
                    showGenAiAssistant: integrateGenAIAssistedDescription,
                    entity: 'project_description'
                  }}
                />
              </Field>
            )}
            <Field
              required
              description={translatr(
                'web.projects.main',
                'SelectAnImageToRepresentThisProjectForCandidatesToDiscover',
                { tm_tm_project: omp('tm_tm_project') }
              )}
              error={errors[Fields.THUMBNAIL]}
              id="project-thumbnail"
              title={translatr('web.projects.main', 'ProjectThumbnail', {
                tm_tm_project: omp('tm_tm_project')
              })}
            >
              <ImageUploader
                ariaDescribedBy="project-thumbnail-description"
                fallback={<ImagePlaceHolderSvg />}
                thumbnail={fields[Fields.THUMBNAIL]}
                onSelect={val => updateFieldValue(Fields.THUMBNAIL, val)}
                isLXMediaHubEnabled={isLXMediaHubEnabled}
              />
            </Field>
            <Field
              required
              error={errors[Fields.OWNERS]}
              id="project-owners"
              length={fields[Fields.OWNERS].length}
              maxLength={MAX_PROJECT_OWNERS}
              suffix="Owners"
              title={translatr('web.projects.main', 'ProjectOwners', {
                tm_tm_project: omp('tm_tm_project')
              })}
            >
              <AsyncSearchInput
                fieldRequired={PROJECT_FIELDS_CONFIG?.['project-owners']?.required}
                multiselect
                channels={false}
                groups={false}
                items={fields[Fields.OWNERS]}
                placeholder={translatr('web.projects.main', 'SpecifyOwnersToBeResponsible')}
                onChange={val => updateFieldValue(Fields.OWNERS, val)}
              />
            </Field>
            <Field
              id={
                stats?.maxPositions == -1 ? 'application-required' : 'project-application-required'
              }
            >
              <div className="application-required">
                <div className="application-required-toggle-container">
                  <Tooltip message={applicationRequiredWarningMessage}>
                    <div className="application-required-toggle">
                      <Switch
                        name={translatr('web.projects.main', 'ApplicationRequired')}
                        disabled={editing && status !== PROJECT_STATUS.DRAFT}
                        onChange={() =>
                          updateFieldValue(
                            Fields.IS_APPLICATION_AVAILABLE,
                            !fields[Fields.IS_APPLICATION_AVAILABLE]
                          )
                        }
                        defaultChecked={fields[Fields.IS_APPLICATION_AVAILABLE]}
                      />
                    </div>
                  </Tooltip>
                  {!editing || status === PROJECT_STATUS.DRAFT ? (
                    <>
                      <p>
                        {translatr(
                          'web.projects.main',
                          'EnableToRequireCandidatesToApplyForTheProject',
                          { tm_tm_project: omp('tm_tm_project') }
                        )}
                      </p>
                      <p>
                        {translatr('web.projects.main', 'IfDisabledAnyoneMayCompleteTheProject', {
                          tm_tm_project: omp('tm_tm_project')
                        })}
                      </p>
                      <p className="sr-only">{applicationRequiredWarningMessage}</p>
                    </>
                  ) : (
                    <p>{applicationRequiredWarningMessage}</p>
                  )}
                </div>
                {fields[Fields.IS_APPLICATION_AVAILABLE] && (
                  <Field
                    error={errors[Fields.CAPACITY]}
                    length={fields[Fields.CAPACITY] ? parseInt(fields[Fields.CAPACITY]) : 0}
                    maxLength={
                      fields[Fields.IS_APPLICATION_AVAILABLE] && !errors[Fields.CAPACITY]
                        ? MAX_PROJECT_CAPACITY
                        : null
                    }
                    suffix="Openings"
                  >
                    <TextField
                      required
                      placeholder={translatr('web.projects.main', 'TypeNumber')}
                      setValue={updateCapacityFieldValue}
                      defaultValue={fields[Fields.CAPACITY]}
                      ariaLabel={translatr('web.common.main', 'NumberOfOpenings')}
                      id="application-required-openings-available-selection"
                      name="openings-available"
                      title={translatr('web.common.main', 'NumberOfOpenings')}
                    />
                  </Field>
                )}
              </div>
            </Field>
            {PROJECT_FIELDS_CONFIG &&
              PROJECT_FIELDS_CONFIG['project-application-required'] &&
              !PROJECT_FIELDS_CONFIG['project-application-required']?.showField &&
              stats?.maxPositions != -1 && (
                <Field
                  error={errors[Fields.CAPACITY]}
                  length={fields[Fields.CAPACITY] ? parseInt(fields[Fields.CAPACITY]) : 0}
                  maxLength={
                    fields[Fields.IS_APPLICATION_AVAILABLE] && !errors[Fields.CAPACITY]
                      ? MAX_PROJECT_CAPACITY
                      : null
                  }
                  fieldClass="project-openings-field"
                  suffix="Openings"
                >
                  <TextField
                    required
                    placeholder={translatr('web.projects.main', 'TypeNumber')}
                    setValue={updateCapacityFieldValue}
                    defaultValue={fields[Fields.CAPACITY]}
                    ariaLabel={translatr('web.common.main', 'NumberOfOpenings')}
                    id="application-required-openings-available-selection"
                    name="openings-available"
                    title={translatr('web.common.main', 'NumberOfOpenings')}
                  />
                </Field>
              )}
            {organizations.get('config')?.enable &&
              orgTypes.length > 0 &&
              orgTypes.map(orgType => (
                <Organizations
                  key={orgType.id}
                  orgType={orgType}
                  organizationsByTypeData={organizationsByTypeData}
                  showTreeView={
                    treeViewVisibility.hasOwnProperty(orgType.id)
                      ? treeViewVisibility[orgType.id]?.[organizationUsage.PROJECTS_FORM] ?? false
                      : false
                  }
                  levelsToDisplay={Number(
                    levelsToDisplay[orgType.id]?.[organizationUsage.PROJECTS_FORM] ?? 0
                  )}
                  organizations={fields[Fields.ORGANIZATIONS]}
                  language={currentUser.profile.language}
                  updateFieldValue={updateFieldValue}
                  error={errors[Fields.ORGANIZATIONS]}
                  required={PROJECT_FIELDS_CONFIG?.['project-custom-organization-unit']?.required}
                />
              ))}
            {locationsEnabled &&
              locationsAssociation.includes(LOCATION_ASSOCIATION.PROJECT) &&
              treeViewLocations?.length !== 0 && (
                <Field
                  optional
                  error={errors[Fields.LOCATIONS]}
                  id="project-locations"
                  length={fields[Fields.LOCATIONS].length}
                  maxLength={MAX_LOCATIONS}
                  suffix="Locations"
                  title={translatr('web.projects.main', 'Locations')}
                >
                  <ProjectTreeView
                    key="key-locations"
                    uniqueId="project-locations"
                    data={treeViewLocations}
                    currentUserLang={currentUser.profile.language}
                    treeViewSearchHook={useLocTreeViewSearch}
                    defaultSelectedIds={fields[Fields.LOCATIONS]}
                    onSelect={({ treeState }) => {
                      const selectedIds = [...treeState?.selectedIds];
                      const newSelection = getSelectionForInput(treeViewLocations, selectedIds);
                      const allIDs = newSelection.includes('0')
                        ? selectedIds.filter(id => id !== '0')
                        : newSelection;
                      updateFieldValue(Fields.LOCATIONS, allIDs);
                    }}
                    required={PROJECT_FIELDS_CONFIG?.['project-locations']?.required}
                  />
                </Field>
              )}
            <Field
              optional
              error={errors[Fields.TIME_ZONES]}
              id="project-time-zones"
              length={fields[Fields.TIME_ZONES].length}
              maxLength={MAX_TIME_ZONES}
              suffix="Time Zones"
              title={translatr('web.projects.main', 'TimeZones')}
            >
              <MultiSelectSearch
                id="project-time-zones"
                ariaLabelledBy="project-time-zones-label"
                placeholder={translatr('web.projects.main', 'SearchTimeZones')}
                value={fields[Fields.TIME_ZONES]}
                onChange={val => updateFieldValue(Fields.TIME_ZONES, val)}
                options={timeZoneDetails?.map(item => ({
                  value: item.defaultName,
                  label: item.translatedName
                }))}
                required={PROJECT_FIELDS_CONFIG?.['project-time-zones']?.required}
              />
            </Field>
            <Field id="project-remote-work-possible">
              <Checkbox
                label={translatr('web.common.main', 'RemoteWorkPossible')}
                checked={fields[Fields.IS_REMOTE_WORK_POSSIBLE]}
                onChange={() =>
                  updateFieldValue(
                    Fields.IS_REMOTE_WORK_POSSIBLE,
                    !fields[Fields.IS_REMOTE_WORK_POSSIBLE]
                  )
                }
              />
            </Field>
            <Field
              error={errors[Fields.DURATION_END_DATE]}
              id="project-duration-selection"
              title={translatr('web.projects.main', 'ProjectDuration', {
                tm_tm_project: omp('tm_tm_project')
              })}
            >
              <React.Fragment>
                <DatePickerV2
                  ariaLabelledBy="project-duration-selection-label"
                  placeHolder={translatr('web.projects.main', 'StartDateEndDate')}
                  title={translatr('web.projects.main', 'ProjectDuration', {
                    tm_tm_project: omp('tm_tm_project')
                  })}
                  singleDatePicker={false}
                  minDate={getNextDate()}
                  startDate={
                    disableStartDate ? originalStartDate : fields[Fields.DURATION_START_DATE]
                  }
                  endDate={fields[Fields.DURATION_END_DATE]}
                  opens="left"
                  onChange={dates => {
                    const [d1, d2] = dates;
                    updateFieldValue(Fields.DURATION_START_DATE, d1);
                    updateFieldValue(Fields.DURATION_END_DATE, d2);
                    updateGenAiContext({
                      key: ENTITY.START_DATE,
                      value: formatDate(d1)
                    });
                    updateGenAiContext({
                      key: ENTITY.END_DATE,
                      value: formatDate(d2)
                    });
                  }}
                  required={PROJECT_FIELDS_CONFIG?.['project-duration-selection']?.required}
                />
                {fields[Fields.DURATION_END_DATE] && (
                  <button
                    className="ed-btn ed-btn-sm ed-btn-link text-left"
                    onClick={resetCalendar}
                  >
                    {translatr('web.projects.main', 'Clear')}
                  </button>
                )}
              </React.Fragment>
            </Field>

            <Field
              error={errors[Fields.APPLICATION_DEADLINE]}
              id="project-application-deadline"
              title={translatr('web.projects.main', 'SubmissionDeadline')}
            >
              <React.Fragment>
                <DatePickerV2
                  ariaLabelledBy="project-application-deadline-label"
                  placeHolder={translatr('web.projects.main', 'SubmissionDeadline')}
                  singleDatePicker={true}
                  startDate={fields[Fields.APPLICATION_DEADLINE]}
                  minDate={new Date()}
                  maxDate={getDayBeforeStartDate(fields[Fields.DURATION_START_DATE])}
                  opens="left"
                  onChange={date => {
                    const d = new Date(date);
                    d.setHours(23, 59, 59);
                    updateFieldValue(Fields.APPLICATION_DEADLINE, d);
                  }}
                  required={PROJECT_FIELDS_CONFIG?.['project-application-deadline']?.required}
                />
                {fields[Fields.APPLICATION_DEADLINE] && (
                  <button
                    className="ed-btn ed-btn-sm ed-btn-link text-left"
                    onClick={resetApplicationDeadline}
                  >
                    {translatr('web.projects.main', 'Clear')}
                  </button>
                )}
              </React.Fragment>
            </Field>
            <Field
              error={errors[Fields.TIME_COMMITMENT]}
              id="project-time-commitment"
              length={fields[Fields.TIME_COMMITMENT].length}
              maxLength={MAX_TIME_COMMITMENT_LENGTH}
              suffix="left"
              title={translatr('web.projects.main', 'TimeCommitment')}
              type="text"
            >
              <TextField
                placeholder={translatr('web.projects.main', 'ProvideEstimatedTimeCommitment')}
                required={PROJECT_FIELDS_CONFIG?.['project-time-commitment']?.required}
                setValue={val => {
                  updateFieldValue(Fields.TIME_COMMITMENT, val);
                  updateGenAiContext({
                    key: ENTITY.TIME_COMMITMENT,
                    value: val
                  });
                }}
                defaultValue={fields[Fields.TIME_COMMITMENT]}
              />
            </Field>
            <Field
              error={errors[Fields.JOB_ROLES]}
              id="project-job-roles"
              length={fields[Fields.JOB_ROLES].length}
              maxLength={MAX_JOB_ROLES}
              suffix={omp('tm_tm_job_roles')}
              title={omp('tm_tm_job_roles')}
            >
              <AsyncSearchInput
                multiselect
                roles
                extraData={{ currentUserLang: currentUser.profile.language }}
                users={false}
                channels={false}
                groups={false}
                items={fields[Fields.JOB_ROLES]}
                fieldRequired={PROJECT_FIELDS_CONFIG?.['project-job-roles']?.required}
                placeholder={translatr('web.projects.main', 'SearchJobRoles', {
                  tm_tm_job_roles: omp('tm_tm_job_roles')
                })}
                onChange={val => {
                  updateFieldValue(Fields.JOB_ROLES, val);
                  updateGenAiContext({
                    key: ENTITY.RELATED_JOB_ROLE,
                    value: val
                  });
                }}
              />
            </Field>
            <Field
              optional
              id="project-skills"
              length={fields[Fields.SKILLS].length}
              error={errors[Fields.SKILLS]}
              suffix="Skills"
              title={translatr('web.projects.main', 'SelectSkills')}
            >
              <SkillWrapper
                required={PROJECT_FIELDS_CONFIG?.['project-skills']?.required}
                suggestionsPayload={{
                  title: fields[Fields.TITLE],
                  description: fields[Fields.DESCRIPTION],
                  lang: global?.__ED__?.profile?.language,
                  context: OPPORTUNITY_TYPE.PROJECT
                }}
                skillsItem={fields[Fields.SKILLS]}
                updateFieldValue={(fieldType, val) => {
                  updateFieldValue(fieldType, val);
                  updateGenAiContext({
                    key: ENTITY.SKILLS,
                    value: val
                  });
                }}
              />
            </Field>
            <Field
              error={errors[Fields.LANGUAGES]}
              id="project-languages"
              length={fields[Fields.LANGUAGES].length}
              maxLength={MAX_LANGUAGES}
              suffix="Languages"
              title={translatr('web.projects.main', 'Languages')}
            >
              <MultiSelectSearch
                id="project-languages"
                required={PROJECT_FIELDS_CONFIG?.['project-languages']?.required}
                ariaLabelledBy="project-languages-label"
                placeholder={translatr('web.projects.main', 'SearchLanguages')}
                value={fields[Fields.LANGUAGES]}
                onChange={val => updateFieldValue(Fields.LANGUAGES, val)}
                options={entries(languageDetails)
                  .sort()
                  .map(([code, label]) => ({
                    id: code,
                    value: code,
                    label
                  }))}
              />
            </Field>
            <FooterActions
              drafting={drafting}
              editing={editing}
              publishing={publishing}
              onSaveDraftClick={onSaveDraftClick}
              onPublishClick={duplicating ? onPublishDuplicateClick : onPublishClick}
              onUpdateClick={onUpdateClick}
              shouldShowCancelConfirmation={showCancelConfirmation}
              status={status}
              updating={updating}
              startDate={fields[Fields.DURATION_START_DATE]}
              hasConfirmedApplications={disableStartDate}
            />
          </Form>
        </div>
      </div>
      {showPostPublishModal && (
        <PostPublishModal projectId={projectId} setShowPostPublishModal={setShowPostPublishModal} />
      )}
      {showSameTitleWarningModal && (
        <SameTitleWarningModal
          onCancel={() => setShowSameTitleWarningModal(false)}
          onConfirm={onPublishDuplicateConfirm}
          title={fields[Fields.TITLE]}
        />
      )}
    </div>
  );
};

ProjectsContainer.propTypes = {
  currentUser: PropTypes.object,
  data: PropTypes.object,
  duplicating: PropTypes.bool,
  editing: PropTypes.bool,
  languageDetails: PropTypes.arrayOf(PropTypes.string),
  projectId: PropTypes.string,
  toast: PropTypes.func,
  organizations: PropTypes.object,
  loadOrganizations: PropTypes.func,
  getOrganizationsConfig: PropTypes.func,
  locationsEnabled: PropTypes.bool,
  locationsAssociation: PropTypes.array,
  locationFieldVisibility: PropTypes.object,
  availableLocations: PropTypes.array,
  timeZoneDetails: PropTypes.array
};

export default connect(
  ({
    currentUser,
    team,
    organizations,
    locationsConfiguration,
    availableLocations,
    timeZones
  }) => ({
    currentUser: currentUser.toJS(),
    languageDetails: transformLanguageDetails(team.toJS().languageDetails),
    availableLocations: availableLocations.get('availableLocations'),
    locationsEnabled: locationsConfiguration.get('enable'),
    locationsAssociation: locationsConfiguration.get('association') || [],
    timeZoneDetails: timeZones.get('timeZones'),
    organizations
  }),
  dispatch => ({
    dispatch,
    toast: message => dispatch(openSnackBar(message)),
    getOrganizationsConfig: () => dispatch(getOrgConfiguration()),
    loadOrganizations: (lang, currentVisibility, currentAssociation) =>
      dispatch(loadAllOrganizations(lang, currentVisibility, currentAssociation))
  })
)(ProjectsContainer);
