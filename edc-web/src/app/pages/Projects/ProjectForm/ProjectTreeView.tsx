import { ITreeNode, TreeNodeId, TreeViewSearchResult } from 'centralized-design-system/src/TreeView/types';
import TreeView from 'centralized-design-system/src/TreeView/TreeView';

type OptionsType = { [key: string]: { value: TreeNodeId; label: string }[] };

interface ProjectTreeViewProps {
  uniqueId: string;
  label: string;
  data: ITreeNode[];
  defaultSelectedIds: TreeNodeId[];
  onSelect: (props: any) => void;
  currentUserLang: string;
  treeViewSearchHook: (obj: { lang: string }) => [boolean, (params: { orgType: string; query: string; maxLevel?: number }) => Promise<TreeViewSearchResult[]>];
  orgId: string;
  maxLevel: number;
  isLoading?: boolean;
  onUpdateFilter?: (key: string, options: OptionsType) => void;
  placeholderLabel?: string;
  required?: boolean
}

const ProjectTreeView = ({
  uniqueId,
  label,
  data,
  defaultSelectedIds,
  onSelect,
  currentUserLang,
  treeViewSearchHook,
  isLoading,
  required = false
}: ProjectTreeViewProps) => {
  const [isSearching, onSearch] = treeViewSearchHook({ lang: currentUserLang });

  return (
    <TreeView
      uniqueId={uniqueId}
      label={label}
      data={data}
      variant="multi"
      defaultSelectedIds={defaultSelectedIds}
      onSelect={onSelect}
      isLoading={isLoading}
      onSearch={query => onSearch({ orgType: 'project', query })}
      isSearching={isSearching}
      ariaLabel={label}
      searchable={true}
      required={required}
    />
  );
};

export default ProjectTreeView;
