import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { func, array, bool, object, number } from 'prop-types';
import { AsyncSearchInput, Select } from 'centralized-design-system/src/Inputs';
import { SKILL_STATUS } from './helpers';
import { translatr } from 'centralized-design-system/src/Translatr';
import LD from '../../../../app/containers/LDStore';
import { Tags } from 'centralized-design-system/src/Tags';
import { Button } from 'centralized-design-system/src/Buttons';
import { Tooltip as TmTooltip } from '@components/TextClamp/TextClamp';
import cn from 'classnames';
import { openSkillFlyout, isMfeEnabled } from '@components/MfeSkillsFlyout';

const SkillByLevel = ({
  skills,
  onSkillChange,
  onRemoveSkill,
  getTopicsFromV3Domain,
  taxonomyDomain,
  currentUser,
  skillLevels,
  required = false
}) => {
  const [skillData, setSkillData] = useState({});
  const [selectedLevel, setSelectedLevel] = useState();
  const [selectedSkill, setSelectedSkill] = useState();

  const levelOptions = [
    { label: translatr('web.projects.main', 'SelectSkillLevel'), value: '-2' },
    ...skillLevels
  ];

  useEffect(() => {
    const updatedSkillData = {};
    skillLevels.forEach(item => {
      updatedSkillData[item.label] = skills
        ?.filter(skill => {
          const level = parseFloat(skill.level);
          return level >= parseFloat(item.range.from) && level <= parseFloat(item.range.to);
        })
        ?.map(s => ({ ...s, translatedLevelDescription: item.translatedLevelDescription }));
    });
    setSkillData(updatedSkillData);
  }, [skills]);

  const handleSkillChange = () => {
    const data = {
      val: selectedSkill,
      level: selectedLevel,
      status: SKILL_STATUS.DECLARED
    };
    setSelectedSkill();
    onSkillChange(data);
  };

  const getSkillByLevel = () => {
    return (
      <div className="skill-level-container m-margin-bottom" key="skill">
        <div className="skill-search-input" data-testid="skill-search-input" aria-live="off">
          <AsyncSearchInput
            isClearable
            id={`skill-search`}
            fieldRequired={required}
            placeholder={translatr('web.projects.main', 'SearchSkills1')}
            skills={getTopicsFromV3Domain}
            users={false}
            channels={false}
            groups={false}
            cacheOptions={false}
            title={translatr('web.common.main', 'Skills')}
            items={selectedSkill}
            onChange={skill => setSelectedSkill(skill)}
            topics={!getTopicsFromV3Domain}
            extraPayload={{
              fields: 'id,label,name',
              external_source: 'csx,external'
            }}
            extraData={{ taxonomyDomain, currentUserLang: currentUser.profile.language }}
            excludeSkillsIds={skills.map(s => s.id)}
          />
        </div>
        <div className="level-select-input" data-testid="level-select-input">
          <Select
            required
            title={translatr('web.common.main', 'Level')}
            id={`level-select`}
            items={levelOptions}
            disabled={!selectedSkill}
            onChange={level => setSelectedLevel(level.value)}
            ariaLabel={translatr('web.common.main', 'Level')}
          />
        </div>
        <Button
          color="primary"
          onClick={handleSkillChange}
          disabled={!selectedSkill || !selectedLevel || selectedLevel === '-2'}
        >
          {translatr('web.common.main', 'Add')}
        </Button>
      </div>
    );
  };
  const SkillSection = (title, items) => {
    return (
      <div className="ed-ui ed-skill-section" key={`skill-section-${title}`}>
        <div>
          <h2 className="ed-input-title">{title}</h2>
          <TmTooltip
            id={`tag-${title}`}
            title={items?.[0]?.translatedLevelDescription}
            placement="bottom"
            arrow
          >
            <button
              aria-label={translatr('web.common.main', 'ValueMoreInfo', { value: title })}
              aria-describedby="mui-tooltip-popper"
              onClick={e => e.stopPropagation()}
            >
              <i className={cn('icon-info-circle radio-tooltip pointer')} aria-hidden="true" />
            </button>
          </TmTooltip>
        </div>
        <ul className="ed-skill-list">
          {items.map(item => {
            return (
              <li
                key={`tag-${item.id}`}
                data-testid={`skill-tag-${item.label}`}
                className="ed-tag-list"
              >
                <Tags
                  id={item.id}
                  name={item.label}
                  cb={() => onRemoveSkill(item)}
                  defaultIconClass="icon-cross-circle"
                  changeOnHover={false}
                  onLabelClick={isMfeEnabled() && (() => openSkillFlyout(item))}
                />
              </li>
            );
          })}
        </ul>
      </div>
    );
  };

  const displaySkills = skillItems => {
    return (
      <div>
        {Object.keys(skillItems).map(
          key => skillItems[key].length > 0 && SkillSection(key, skillItems[key])
        )}
      </div>
    );
  };

  return (
    <div>
      {getSkillByLevel()}
      {displaySkills(skillData)}
    </div>
  );
};

SkillByLevel.propTypes = {
  skills: array,
  onSkillChange: func,
  onRemoveSkill: func,
  getTopicsFromV3Domain: bool,
  taxonomyDomain: object,
  currentUser: object,
  maxAssignedSkills: number,
  skillLevels: array,
  required: bool
};

export default connect(({ currentUser, team }) => ({
  currentUser: currentUser.toJS(),
  getTopicsFromV3Domain: ['v3', 'FS-onboarding'].includes(LD.onboardingVersion()),
  taxonomyDomain: team.get('config')?.taxonomy_domain,
  maxAssignedSkills: parseInt(team?.get('ecsConfig')?.max_assigned_skills)
}))(SkillByLevel);
