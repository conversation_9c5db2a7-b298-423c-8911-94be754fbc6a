import React, { useEffect, useState } from 'react';
import { node, string, bool, number, oneOfType } from 'prop-types';
import cx from 'classnames';
import { tr } from 'edc-web-sdk/helpers/translations';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import TitleDescription from 'centralized-design-system/src/Inputs/TitleDescription';
import { projectFields } from '../helpers';

export const Form = ({ id, children, ...rest }) => (
  // todo use form element. can't yet because many buttons need to have explicit type of button or it will trigger on enter press
  <div id={id} {...rest}>
    {children}
  </div>
);

Form.propTypes = {
  id: string,
  children: node
};

export const FieldFooter = ({ error, length, maxLength, suffix, type, typeId = '' }) => {
  const [errorMessage, setErrorMessage] = useState(error);
  const text = `%{remainingText}/%{maxLen} ${suffix}`;
  const remainingText =
    type === 'text' ? (maxLength - length >= 0 ? maxLength - length : 0) : length;

  /**
   * Because of live polite region, alert can sometimes not be read due to priority confusion.
   * Delay render of alert so that it can be picked up by most screen readers more consistently.
   */
  useEffect(() => {
    if (!error) {
      setErrorMessage('');
    } else {
      // delay render of error message so sr can abort live polite region speech
      setTimeout(() => setErrorMessage(error), 500);
    }
  }, [error]);

  return (
    <div className={cx('text-field-meta-group', { 'input-error': error })}>
      <span
        role="alert"
        aria-live="assertive"
        className="input-error"
        dangerouslySetInnerHTML={{ __html: safeRender(errorMessage) }}
      />
      {maxLength && (
        <span id={typeId} aria-live="polite" className="search-meta">
          {tr(text, { remainingText, maxLen: maxLength })}
        </span>
      )}
    </div>
  );
};

FieldFooter.propTypes = {
  error: oneOfType([string, bool]),
  length: number,
  maxLength: number,
  suffix: string,
  type: string,
  typeId: string
};

export const Field = ({
  children,
  description,
  error,
  id,
  length,
  maxLength,
  required,
  suffix,
  title,
  type,
  fieldClass = 'project-form-field',
  isProjectOrganizationField = false
}) => {
  const PROJECT_FIELDS_CONFIG = global?.__edOrgData?.configs?.find(
    f => f.name === 'project_fields_config'
  )?.value;

  const configId = isProjectOrganizationField ? projectFields.ORGANIZATIONS : id;
  const fieldOrigin = isProjectOrganizationField ? 'project' : id && id.split('-')[0];
  if (
    fieldOrigin == 'project' &&
    PROJECT_FIELDS_CONFIG &&
    Object.keys(PROJECT_FIELDS_CONFIG).length > 0 &&
    PROJECT_FIELDS_CONFIG[configId]
  ) {
    return (
      PROJECT_FIELDS_CONFIG[configId]?.showField && (
        <div className={cx(fieldClass, { 'has-error': !!error })}>
          <TitleDescription
            controlId={id}
            title={title}
            description={description}
            required={PROJECT_FIELDS_CONFIG[configId].required}
            optional={!PROJECT_FIELDS_CONFIG[configId].required}
          />
          {React.cloneElement(children, { id, name: id })}
          {(error || maxLength) && (
            <FieldFooter
              error={error}
              length={length}
              maxLength={maxLength}
              suffix={suffix}
              type={type}
            />
          )}
        </div>
      )
    );
  } else {
    return (
      <div className={cx(fieldClass, { 'has-error': !!error })}>
        <TitleDescription
          controlId={id}
          title={title}
          description={description}
          required={required}
          optional={!required}
        />
        {React.cloneElement(children, { id, name: id })}
        {(error || maxLength) && (
          <FieldFooter
            error={error}
            length={length}
            maxLength={maxLength}
            suffix={suffix}
            type={type}
            typeId="job-roles-input-count"
          />
        )}
      </div>
    );
  }
};
Field.propTypes = {
  children: node,
  description: string,
  error: oneOfType([string, bool]),
  id: string,
  length: number,
  maxLength: number,
  required: bool,
  suffix: string,
  title: string,
  type: string,
  fieldClass: string,
  isProjectOrganizationField: bool
};
FieldFooter.propTypes = {
  description: string,
  error: oneOfType([string, bool]),
  id: string,
  length: number,
  maxLength: number,
  required: bool,
  suffix: string,
  title: string,
  type: string
};
