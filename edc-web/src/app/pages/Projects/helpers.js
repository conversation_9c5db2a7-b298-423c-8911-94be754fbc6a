import entries from 'lodash/entries';
import { TrackEventProperties } from '@analytics/TrackEvents';
import { ProjectStatus } from '@components/ProjectCard/shared/types';
import { translatr } from 'centralized-design-system/src/Translatr';

// Transform languages from store to something more easily accessible -> { [value]: code} => { [code]: value }
export const transformLanguages = languages =>
  entries(languages).reduce((accum, [value, code]) => {
    accum[code] = value;

    return accum;
  }, {});

/**
 *
 * @param {string} userId Any user id
 * @param {Object} project Object represeting project
 * @returns {Bool} Result of whether user is a project owner
 */
export const isProjectOwner = (userId, project) => {
  return !!project?.owners?.find(owner => `${owner.id}` === userId) || project?.creator === userId;
};

export const getClustreeIdLabel = item => {
  return item?.map(r => ({
    id: r.id,
    label: r.label
  }));
};

export const getProjectMetaForAnalytics = (projectData = {}) => {
  const { OMP } = TrackEventProperties;
  const skills = getClustreeIdLabel(projectData.skills);
  const roles = getClustreeIdLabel(projectData.roles);
  return {
    [OMP.PROJECT_ID]: projectData.id,
    [OMP.PROJECT_TITLE]: projectData.title,
    [OMP.APPLICATION_REQUIRED]: projectData.requireApplication,
    [OMP.OPENINGS]: projectData.maxPositions > -1 ? projectData.maxPositions : null,
    [OMP.NUMBER_OF_OWNERS]: projectData.owners.length,
    [OMP.THUMBNAIL_TYPE]: projectData.thumbnail?.source,
    [OMP.PROJECT_TIME_ZONES]: projectData.timezones,
    [OMP.REMOTE_WORK]: projectData.remoteWorkPossible,
    [OMP.DURATION_SPECIFIED]: projectData.endDate ? true : false,
    [OMP.TIME_COMMITMENT]: projectData.timeCommitment || null,
    [OMP.NUMBER_OF_SKILLS]: skills ? skills.length : 0,
    [OMP.PROJECT_SKILLS]: skills,
    [OMP.NUMBER_OF_JOB_ROLES]: roles ? roles.length : 0,
    [OMP.PROJECT_ROLES]: roles,
    [OMP.NUMBER_OF_LANGUAGES]: projectData.languages ? projectData.languages.length : 0,
    [OMP.PROJECT_LANGUAGES]: projectData.languages,
    [OMP.NUMBER_OF_LOCATIONS]: projectData.locations?.length || 0,
    [OMP.LOCATION_IDS]: projectData.locations?.map(({ id }) => id),
    [OMP.LOCATION_NAMES]: projectData.locations?.map(({ city, state, country }) =>
      [city, state, country].filter(m => m).join(', ')
    )
  };
};

export const mapFormDataFromProject = ({
  maxPositions,
  description,
  divisions,
  endDate,
  languages,
  locations,
  owners,
  remoteWorkPossible,
  requireApplication,
  roles,
  startDate,
  status,
  thumbnail,
  timeCommitment,
  timeZoneDetails,
  title,
  skillDetails,
  applicationDeadline
}) => ({
  description,
  divisions: divisions?.map(division => ({
    orgType: division.orgType,
    value: division.id,
    label: division.title,
    level: division.level
  })),
  endDate: endDate && new Date(endDate),
  isApplicationRequired: requireApplication,
  isRemotePossible: remoteWorkPossible,
  jobRoles: roles?.map(r => ({
    id: r.id,
    value: r.id,
    label: r.parent?.label ? `${r.parent.label} - ${r.label}` : r.label
  })),
  languages,
  locations: locations?.map(location => location.id),
  maxPositions,
  projectOwners: owners?.map(o => ({
    id: o.id,
    fullName: o.fullName,
    handle: o.handle,
    value: o.id
  })),
  skills: skillDetails?.map(s => ({
    id: s.id,
    value: s.id,
    level: s.level,
    name: s.name,
    label: s.label,
    source: s.source,
    status: s.status,
    external_data: s.external_data
  })),
  startDate: startDate && new Date(startDate),
  status,
  thumbnail,
  timeCommitment,
  timeZones: timeZoneDetails?.map(item => ({
    value: item.defaultName,
    label: item.translatedName
  })),
  title,
  applicationDeadline: applicationDeadline && new Date(applicationDeadline)
});

export const applicantStatus = [
  ProjectStatus.COMPLETED,
  ProjectStatus.INPROGRESS,
  ProjectStatus.PENDING,
  ProjectStatus.REJECTED,
  ProjectStatus.ACCEPTED
];

export const terminatedUserLabel = translatr('web.common.main', 'FormerUser');

export const isTerminatedUser = user => {
  return !user || typeof user === 'string' ? true : false;
};

export const projectFields = {
  TITLE: 'project-title',
  DESCRIPTION: 'project-description',
  THUMBNAIL: 'project-thumbnail',
  OWNERS: 'project-owners',
  APPLICATION_REQUIRED: 'project-application-required',
  LOCATIONS: 'project-locations',
  TIMEZONES: 'project-time-zones',
  REMOTE_WORK_POSSIBLE: 'project-remote-work-possible',
  DURATION: 'project-duration-selection',
  TIME_COMMITMENT: 'project-time-commitment',
  SKILLS: 'project-skills',
  JOB_ROLES: 'project-job-roles',
  LANGUAGES: 'project-languages',
  ORGANIZATIONS: 'project-custom-organization-unit',
  APPLICATION_DEADLINE: 'project-application-deadline'
};
