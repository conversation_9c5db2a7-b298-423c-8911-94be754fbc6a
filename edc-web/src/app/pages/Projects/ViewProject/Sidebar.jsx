import React from 'react';
import { connect } from 'react-redux';
import { array, arrayOf, bool, func, number, object, string, shape, any } from 'prop-types';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import ProjectThumbnail from '@components/ProjectCard/shared/ProjectThumbnail';
import Meta from 'opportunity-marketplace/shared/SidebarMeta';
import formatDate from '@utils/formatDate';
import ApplicationStatusLabel from '@components/ProjectCard/shared/ApplicationStatusLabel';
import IconButton from '@components/IconButton';
import cx from 'classnames';
import { orgVisibility } from '../../../../app/actions/organizationsActions';
import { sortByIndex } from '../ProjectForm/helpers';
import SidebarOrgMeta from '../../TalentMarketplace/shared/SidebarOrgMeta';
import {
  LOCATION_ASSOCIATION,
  LOCATION_FIELDS,
  LOCATION_USAGE_OPTIONS,
  isLocationFieldVisible
} from '../../TalentMarketplace/helpers';
import { projectFields } from '../helpers';
import SidebarLocationsMeta from '@pages/TalentMarketplace/shared/SidebarLocationsMeta';

const Sidebar = ({
  canApply,
  canSubscribe,
  canStart,
  canConfirm,
  canWithdraw,
  endDate,
  image = {},
  isApplicationRequired,
  isBookmarked,
  isOwner,
  isRemoteWorkPossible,
  languages = [],
  locations = [],
  divisions = [],
  maxPositions,
  onApply,
  onBookmark,
  onShare,
  onSubscribe,
  positionsFilled = 0,
  projectStatus,
  startDate,
  timeCommitment,
  timeZones = [],
  title,
  isLoading,
  isSubscribed,
  isOrgEnabled,
  visibleOrgs,
  orgTypes,
  locationsEnabled,
  onStart,
  onWithdraw,
  onConfirm,
  locationsAssociation,
  locationsVisibility,
  applicationDeadline,
  projectStatusRef
}) => {
  const positionsAvailable = maxPositions - positionsFilled;
  const dateRangeLabel =
    startDate && endDate
      ? `${formatDate(startDate)} - ${formatDate(endDate)}`
      : translatr('web.projects.main', 'Flexible');

  const applicationDeadlineLabel =
    applicationDeadline && `${formatDate(applicationDeadline, true)}`;
  const timeCommitmentLabel = timeCommitment
    ? timeCommitment
    : translatr('web.projects.main', 'Flexible');

  let maxPositionsLabel = '';

  if (!isApplicationRequired) {
    maxPositionsLabel = translatr('web.projects.main', 'UnlimitedOpenings');
  } else if (positionsAvailable === 1) {
    maxPositionsLabel = translatr('web.projects.main', 'PositionsavailableOpening', {
      positionsAvailable
    });
  } else {
    maxPositionsLabel = translatr('web.projects.main', 'PositionsavailableOpenings', {
      positionsAvailable
    });
  }

  const renderFooterSection = () => {
    if (isLoading) {
      return <Skeleton height={24} />;
    }

    const sections = [];

    if (canApply || isSubscribed || canSubscribe) {
      sections.push(
        <button
          className="ed-btn ed-btn-primary cta"
          disabled={isLoading || (!canApply && (isSubscribed || canSubscribe))}
          onClick={onApply}
        >
          {isApplicationRequired
            ? translatr('web.projects.main', 'Apply')
            : translatr('web.common.main', 'Participate')}
        </button>
      );
    }

    if (canStart && isApplicationRequired) {
      sections.push(
        <div className={cx('start-container', 'm-margin-top')}>
          <span className="text">
            {translatr('web.common.main', 'LetTheProjectOwnersKnowWhenYouAreStartingTheProject') ||
              'Let the project owners know when you are starting the project'}
          </span>
          <button className="ed-btn ed-btn-primary cta" disabled={isLoading} onClick={onStart}>
            {translatr('web.common.main', 'Participate')}
          </button>
          <button className={cx('withdraw-button', 'm-margin-top')} onClick={onWithdraw}>
            {translatr('web.common.main', 'Withdraw')}
          </button>
        </div>
      );
    }

    if (canConfirm && isApplicationRequired) {
      sections.push(
        <div className={cx('start-container', 'm-margin-top')}>
          <span className="text">
            {translatr(
              'web.common.main',
              'LetYourProjectOwnerKnowThatYoureStillInterestedIfYouCanNoLongerParticipateYouCanWithdraw'
            )}
          </span>
          <button className="ed-btn ed-btn-primary cta" disabled={isLoading} onClick={onConfirm}>
            {translatr('web.common.main', 'Confirm')}
          </button>
          <button className={cx('withdraw-button', 'm-margin-top')} onClick={onWithdraw}>
            {translatr('web.common.main', 'Withdraw')}
          </button>
        </div>
      );
    }

    if (canSubscribe && !isSubscribed) {
      sections.push(
        <div className={cx('notify-container', 'm-margin-top')}>
          <span>
            {translatr(
              'web.projects.main',
              'ThisProjectIsFullSignUpToGetNotifiedIfOpeningsAreAdded',
              { tm_tm_project: omp('tm_tm_project') }
            )}
          </span>
          <div>
            <button onClick={onSubscribe}>{translatr('web.projects.main', 'NotifyMe')}</button>
          </div>
        </div>
      );
    }

    if (!canStart && !canConfirm && canWithdraw && isApplicationRequired) {
      sections.push(
        <div className={cx('start-container', 'm-margin-top')}>
          <button className="ed-btn ed-btn-primary cta" onClick={onWithdraw}>
            {translatr('web.common.main', 'Withdraw')}
          </button>
        </div>
      );
    }

    if (projectStatus && !canApply && !canStart && !canConfirm) {
      if (isSubscribed) {
        sections.push(
          <div className={cx('subscribed-container', 'm-margin-top')}>
            {translatr(
              'web.projects.main',
              'ThanksForYourInterestWeWillNotifyYouIfOpeningsAreAdded'
            )}
          </div>
        );
      } else {
        sections.pop();
      }
      sections.push(
        <div className="cta" ref={projectStatusRef} role="region" tabIndex={-1}>
          <ApplicationStatusLabel status={projectStatus} />
        </div>
      );
    }

    return <div aria-live="polite">{sections}</div>;
  };

  const renderOrg = () => {
    const visibleOrgTypes = sortByIndex(orgTypes.filter(org => visibleOrgs.includes(org.id)));
    const PROJECT_FIELDS_CONFIG = global?.__edOrgData?.configs?.find(
      f => f.name === 'project_fields_config'
    )?.value;

    return (
      <>
        {(!PROJECT_FIELDS_CONFIG ||
          PROJECT_FIELDS_CONFIG[projectFields.ORGANIZATIONS]?.showField) &&
          visibleOrgTypes.map(item => {
            const data = divisions.find(division => division.orgType === item.id);
            const divisionHierarchy = [
              ...(data?.allParentDetail?.map(division => division.title) ?? []),
              data?.title
            ];
            if (data) {
              return (
                <SidebarOrgMeta
                  key={item.id}
                  icon="tree-graph"
                  loading={isLoading}
                  items={divisionHierarchy}
                  child={data.title}
                  title={item.label}
                />
              );
            }

            return null; // If there's no data, return null
          })}
      </>
    );
  };

  const renderLocations = () => {
    const locationsData = [];
    locations.map(loc => {
      const locationsHierarchy = [
        ...(loc?.allParentDetail?.map(item => item.title) ?? []),
        loc?.title
      ];
      locationsData.push(locationsHierarchy);
    });
    return (
      <SidebarLocationsMeta
        id="project-locations"
        title={translatr('web.projects.main', 'Locations')}
        loading={isLoading}
        icon="pin-on-street-square"
        locationsData={locationsData}
      />
    );
  };

  return (
    <div className="sidebar block">
      <ProjectThumbnail className="l-margin-bottom" thumbnail={image} />
      <Meta icon="users-audience" label={maxPositionsLabel} />
      <Meta
        id="project-duration-selection"
        icon="calendar-days"
        label={dateRangeLabel}
        loading={isLoading}
        title={translatr('web.projects.main', 'StartDateEndDate')}
      />
      {applicationDeadlineLabel && (
        <Meta
          id="project-application-deadline"
          icon="calendar-x"
          label={applicationDeadlineLabel}
          loading={isLoading}
          title={translatr('web.projects.main', 'SubmissionDeadline') || 'Submission deadline'}
        />
      )}
      <Meta
        id="project-time-commitment"
        icon="clock"
        label={timeCommitmentLabel}
        title={translatr('web.projects.main', 'TimeCommitment')}
      />
      {locationsEnabled &&
        locations.length > 0 &&
        locationsAssociation.includes(LOCATION_ASSOCIATION.PROJECT) &&
        isLocationFieldVisible(
          locationsVisibility,
          LOCATION_FIELDS.NAME,
          LOCATION_USAGE_OPTIONS.PROJECT_DETAILS
        ) &&
        renderLocations()}

      <Meta
        id="project-time-zones"
        icon="globe-2"
        label={timeZones.length === 0 ? translatr('web.projects.main', 'AnyTimeZone') : ''}
        items={timeZones.map(item => item.translatedName)}
        title={translatr('web.projects.main', 'TimeZones')}
      />
      {isRemoteWorkPossible && (
        <Meta
          id="project-remote-work-possible"
          icon="laptop-with-person"
          label={translatr('web.projects.main', 'RemotePossible')}
        />
      )}
      {isOrgEnabled && renderOrg()}
      {languages.length > 0 && (
        <Meta
          id="project-languages"
          icon="globe"
          items={languages}
          maxItems={6}
          title={translatr('web.projects.main', 'Languages')}
        />
      )}
      {!isOwner && renderFooterSection()}
      <div className="actions-container">
        <IconButton
          icon="share1"
          aria-label={translatr('web.projects.main', 'ShareTitle', { title })}
          title={translatr('web.projects.main', 'Share')}
          onClick={onShare}
          size="large"
        />
        <IconButton
          icon={isBookmarked ? 'bookmark-fill' : 'bookmark'}
          aria-label={
            isBookmarked
              ? translatr('web.projects.main', 'UnbookmarkTitle', { title })
              : translatr('web.projects.main', 'BookmarkTitle', { title })
          }
          title={
            isBookmarked
              ? translatr('web.projects.main', 'Unbookmark')
              : translatr('web.projects.main', 'Bookmark')
          }
          onClick={onBookmark}
          size="large"
        />
      </div>
    </div>
  );
};

Sidebar.propTypes = {
  canApply: bool,
  canSubscribe: bool,
  canStart: bool,
  canConfirm: bool,
  canWithdraw: bool,
  endDate: string,
  image: object,
  isApplicationRequired: bool,
  isBookmarked: bool,
  isOwner: bool,
  isRemoteWorkPossible: bool,
  languages: arrayOf(string),
  locations: arrayOf(string),
  divisions: arrayOf(string),
  maxPositions: number,
  onApply: func.isRequired,
  onBookmark: func,
  onShare: func,
  onSubscribe: func,
  positionsFilled: number,
  isNotificationOn: bool,
  projectStatus: string,
  startDate: string,
  timeCommitment: string,
  timeZones: arrayOf(string),
  title: string.isRequired,
  isLoading: bool,
  isSubscribed: bool,
  isOrgEnabled: bool,
  visibleOrgs: array,
  orgTypes: array,
  locationsEnabled: bool,
  onStart: func,
  onWithdraw: func,
  onConfirm: func,
  locationsAssociation: array,
  locationsVisibility: object,
  applicationDeadline: string,
  projectStatusRef: shape({ current: any })
};

export default connect(
  ({ organizations, locationsConfiguration }) => ({
    isOrgEnabled: organizations.get('config')?.enable || false,
    visibleOrgs:
      organizations.get('organizationsByVisibility')[orgVisibility.PROJECT_DETAILS] || [],
    orgTypes: organizations.get('orgTypes') || [],
    locationsEnabled: locationsConfiguration.get('enable'),
    locationsAssociation: locationsConfiguration.get('association') || [],
    locationsVisibility: locationsConfiguration.get('visibility')
  }),
  null
)(Sidebar);
