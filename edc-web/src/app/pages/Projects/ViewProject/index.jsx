import React, { useCallback, useEffect, useState } from 'react';
import { omitBy, isNil } from 'lodash';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { track } from '@analytics/TrackWrapper';
import { TrackEvents, TrackEventProperties } from '@analytics/TrackEvents';
import {
  getProject,
  getProjectActios,
  applyToProject,
  subscribeToProject,
  JOB_TYPE,
  OPPORTUNITY_TYPE,
  startUserApplication,
  withdrawUserApplication,
  confirmToProject,
  getProjectApplicationStatus
} from 'edc-web-sdk/requests/careerOportunities.v2';
import { search } from 'edc-web-sdk/requests/extOpportunities';
import { transformLanguages } from '@utils/transformLanguages';
import EditorDisplay from 'opportunity-marketplace/shared/EditorDisplay';
import ShowMore from 'opportunity-marketplace/shared/ShowMore';
import Section from 'opportunity-marketplace/shared/Section';
import { shouldShowTMJobRole } from 'opportunity-marketplace/util';
import { mapJobRoles } from 'opportunity-marketplace/Api';
import { open_v2 as openSnackBar } from 'actions/snackBarActions';
import BackArrow from '@components/backarrow';
import TMNotFound from '@components/TMNotFound/TMNotFound';
import { ComponentType, useCardActions } from '@components/ProjectCard/shared/hooks';
import { Actions, ProjectStatus } from '@components/ProjectCard/shared/types';
import { useLazyApi, useMediaQuery } from '@utils/hooks';
import { PROJECT_STATUS } from '../ManageProject/utils';
import Header from './Header';
import Sidebar from './Sidebar';
import RelatedRolesCarousel from './RelatedRolesCarousel';
import ApplyForProjectModal from './ApplyForProjectModal';
import ApplicationConfirmationModal from './ApplyConfirmationModal';
import { getAvailableLocations } from 'actions/availableLocationsActions';

import './styles.scss';
import ViewProjectLoader from './ViewProjectLoader';
import { getProjectMetaForAnalytics, isProjectOwner, projectFields } from '../helpers';
import { SkillsSection } from '@components/SkillsSection';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import {
  getOrgConfiguration,
  loadAllOrganizations,
  orgAssociation,
  orgVisibility
} from '../../../../app/actions/organizationsActions';
import { recordCarrerGrowthDetailPageVisitedEvent } from '../../TalentMarketplace/DetailPage/utils';
import { searchByOrgIds, searchLocationsByIds } from 'edc-web-sdk/requests/organizations';
import { openWithdrawCommentModal } from 'actions/modalActions';
import ParticipationConfirmationModal from './ParticipationConfirmationModal';
import { getCheckinsConfig } from 'edc-web-sdk/requests/superAdmin';
import { MfeLoader, getEventBus } from 'centralized-design-system/src/MfeLoader';
import withAspirationsContext from '@pages/TalentMarketplace/shared/WithAspirationsContext';

const ViewProject = ({
  currentUserId,
  currentUserLang,
  dispatch,
  languagesMap,
  availableLocations,
  organizations
}) => {
  const isMobile = useMediaQuery('(max-width: 992px)');
  const PROJECT_FIELDS_CONFIG = global?.__edOrgData?.configs?.find(
    f => f.name === 'project_fields_config'
  )?.value;
  const location = useLocation();
  const { slug } = useParams();
  const navigate = useNavigate();

  const [getRelatedRoles, { data: relatedRolesData, loading: relatedRolesLoading }] = useLazyApi(
    search
  );
  const [project, setProject] = useState(location?.state?.data);
  const [actionIds, setActionIds] = useState([]);
  const [loadError, setLoadError] = useState(false);
  const [projectLoading, setProjectLoading] = useState(true);
  const [showApplyForProjectModal, setShowApplyForProjectModal] = useState(false);
  const [showApplyConfirmationModal, setShowApplyConfirmationModal] = useState(false);
  const [applyLoading, setApplyLoading] = useState(false);
  const [projectStatus, setProjectStatus] = useState(null);
  const [subscribeLoading, setSubscribeLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(null);
  const [divisionsData, setDivisionsData] = useState([]);
  const [locationsData, setLocationsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showParticipationConfirmationModal, setShowParticipationConfirmationModal] = useState(
    false
  );
  const [isLoading, setIsLoading] = React.useState(true);
  const [isCheckinEnabled, setIsCheckinEnabled] = React.useState(false);
  const lastFocusedElementRef = React.useRef(null);
  const projectStatusRef = React.useRef(null);

  // reference to remember whether page has been mounted once
  const [mounted, setMounted] = useState(false);

  // this can come as array of ids or array of objects
  const rolesToFetch = project?.roles?.map(r => r?.id || r);

  const getProjectActions = () => {
    getProjectActios({
      ids: [slug],
      type: OPPORTUNITY_TYPE.PROJECT,
      sections: 'actions'
    })
      .then(response => setActionIds(response.data[0]?.actions))
      .catch(() =>
        dispatch(
          openSnackBar(translatr('web.projects.main', 'ThereWasAnErrorRetrievingActions'), 'error')
        )
      );
  };

  const {
    actions: projectActions,
    bookmarked,
    completed,
    setBookmarked,
    setCompleted
  } = useCardActions({
    id: slug,
    bookmarked: location?.state?.data?.isBookmarked,
    component: ComponentType.PAGE,
    dispatch,
    onClose: () =>
      getProjectActios({
        ids: [slug],
        type: OPPORTUNITY_TYPE.PROJECT,
        sections: 'actions'
      })
        .then(response => setActionIds(response.data[0]?.actions))
        .catch(() =>
          dispatch(
            openSnackBar(
              translatr('web.projects.main', 'ThereWasAnErrorRetrievingActions'),
              'error'
            )
          )
        ),
    onComplete: () => {
      setCompleted(true);
      setProjectStatus(ProjectStatus.COMPLETED);
    },
    onStarted: () => {
      setShowParticipationConfirmationModal(true);
      setProjectStatus(ProjectStatus.INPROGRESS);
      getProjectActions();
    },
    onWithdraw: () => {
      setProjectStatus();
      getProjectActions();
    },
    onConfirm: () => {
      setProjectStatus(ProjectStatus.CONFIRMED);
      getProjectActions();
    },
    projectTitle: project?.title,
    isApplicationRequired: project?.requireApplication
  });

  const onApply = useCallback(
    event => {
      if (project.requireApplication) {
        lastFocusedElementRef.current = event.currentTarget;
        setShowApplyForProjectModal(true);
      } else {
        setApplyLoading(true);
        const payload = { type: OPPORTUNITY_TYPE.PROJECT, comment: '' };
        applyToProject(project.id, payload)
          .then(onNoRequireApplicationPostSubmit)
          .catch(() => {
            dispatch(
              openSnackBar(
                translatr(
                  'web.projects.main',
                  'ThereWasAnErrorApplyingForThisProjectPleaseContactAdministratorOrTA',
                  { tm_tm_project: omp('tm_tm_project') }
                ),
                'error'
              )
            );
          });
      }
    },
    [project]
  );

  const onSubscribe = useCallback(() => {
    setSubscribeLoading(true);
    const payload = { type: OPPORTUNITY_TYPE.PROJECT, comment: '' };
    subscribeToProject(project.id, payload)
      .then(onSubscribeSubmit)
      .catch(() => {
        dispatch(
          openSnackBar(
            translatr(
              'web.projects.main',
              'ThereWasAnErrorSubscribingToThisProjectPleaseContactAdministratorOrTA',
              { tm_tm_project: omp('tm_tm_project') }
            ),
            'error'
          )
        );
      });
  }, [project]);

  const onStart = useCallback(() => {
    setLoading(true);
    const payload = { id: project.id, type: OPPORTUNITY_TYPE.PROJECT };
    startUserApplication(project.id, payload)
      .then(onStartSubmit)
      .catch(() => {
        dispatch(
          openSnackBar(
            translatr('web.projects.main', 'AnErrorHasOccurredPleaseContactYourAdministrationTA'),
            'error'
          )
        );
        setLoading(false);
      });
  }, [project]);

  const onConfirm = useCallback(() => {
    setLoading(true);
    const payload = { type: OPPORTUNITY_TYPE.PROJECT };
    confirmToProject(project.id, payload)
      .then(onConfirmSubmit)
      .catch(() => {
        dispatch(
          openSnackBar(
            translatr('web.projects.main', 'AnErrorHasOccurredPleaseContactYourAdministrationTA'),
            'error'
          )
        );
        setLoading(false);
      });
  }, [project]);

  const onWithdraw = () => {
    const withdrawModalConfig = {
      withdrawModalTitle: translatr('web.common.main', 'AreYouSureYouWantToWithdraw'),
      withdrawModalDescription: translatr(
        'web.projects.main',
        'WithdrawingFromTheTitleProjectCannotBeUndoneToRejoinYouWillHaveToReapply',
        { tm_tm_project: omp('tm_tm_project'), title: project?.title }
      ),
      withdrawModalInputPlaceholder: translatr(
        'web.projects.main',
        'OptionalNoteToTheProjectOwner',
        {
          tm_tm_project: omp('tm_tm_project').toLowerCase()
        }
      ),

      withdrawToastMessage: translatr('web.projects.main', 'TheProjectHasBeenWithdrawn', {
        tm_tm_project: omp('tm_tm_project')
      }),
      withdrawAction: message => {
        return withdrawUserApplication(project.id, {
          id: project.id,
          type: OPPORTUNITY_TYPE.PROJECT,
          comment: message
        })
          .then(() => {
            setProjectStatus(ProjectStatus.WITHDRAWN);
            getProjectActions();
          })
          .catch(() => {});
      }
    };

    dispatch(openWithdrawCommentModal(withdrawModalConfig));
  };

  const onNoRequireApplicationPostSubmit = useCallback(() => {
    setApplyLoading(false);
    setShowParticipationConfirmationModal(true);
    setProjectStatus(ProjectStatus.INPROGRESS);
    getProjectActions();
  }, []);

  const onRequireApplicationPostSubmit = useCallback(() => {
    setApplyLoading(false);
    setShowApplyConfirmationModal(true);
    setProjectStatus(ProjectStatus.PENDING);
    getProjectActions();
  }, []);

  const onSubscribeSubmit = useCallback(() => {
    setSubscribeLoading(false);
    setProjectStatus(ProjectStatus.SUBSCRIBED);
    setIsSubscribed(true);
  }, []);

  const onStartSubmit = useCallback(async () => {
    setShowParticipationConfirmationModal(true);
    setProjectStatus(ProjectStatus.INPROGRESS);
    getProjectActions();
    setLoading(false);
  }, []);

  const onConfirmSubmit = useCallback(() => {
    setProjectStatus(ProjectStatus.CONFIRMED);
    getProjectActions();
    setLoading(false);
  }, []);

  React.useEffect(() => {
    const bus = getEventBus();
    return bus.subscribe('perf.checkins.mfe:show-conversation', data => {
      navigate(`/checkin/${data.conversationId}`);
    });
  }, []);

  useEffect(() => {
    if (!slug) {
      return;
    }

    if (!availableLocations) {
      dispatch(getAvailableLocations(currentUserLang));
    }
    getProjectApplicationStatus(slug)
      .then(({ data }) => {
        const status = data?.status;
        const approvalStatus = data?.approvalStatus;

        setProjectStatus(
          status === PROJECT_STATUS.COMPLETED ||
            status === PROJECT_STATUS.INPROGRESS ||
            status === PROJECT_STATUS.SUBSCRIBED ||
            status === PROJECT_STATUS.WITHDRAWN ||
            status === PROJECT_STATUS.CONFIRMED
            ? status
            : approvalStatus
        );
      })
      .catch(err => {
        console.error(err);
      });

    getProject(slug)
      .then(({ data }) => {
        setProject(data);
        setBookmarked(data.isBookmarked);
        setProjectLoading(false);
        setIsSubscribed(status === ProjectStatus.SUBSCRIBED);

        const orgIds = data.divisions ? data.divisions.map(item => item.id) : [];
        const locationIds = data.locations ? data.locations.map(loc => loc.id) : [];
        const payload = {
          resolveParent: true,
          ids: orgIds,
          visibilityContext: orgVisibility.PROJECT_DETAILS,
          filterInActive: true,
          resolveAllParents: true
        };
        searchByOrgIds(payload, currentUserLang)
          .then(response => {
            setDivisionsData(response.divisions);
          })
          .catch(error => {
            setDivisionsData(data.divisions);
            console.error(`error in searching by org ids ${error}`);
          });
        if (locationIds.length > 0) {
          searchLocationsByIds({ ...payload, ids: locationIds }, currentUserLang)
            .then(response => {
              setLocationsData(response.locations);
            })
            .catch(error => {
              setLocationsData(data.locations);
              console.error(`Error in searching by locations by ids ${error}`);
            });
        }
      })
      .catch(() => {
        setLoadError(true);
        setProjectLoading(false);
      });

    getProjectActios({
      ids: [slug],
      type: OPPORTUNITY_TYPE.PROJECT,
      sections: 'actions'
    })
      .then(({ data }) => {
        setActionIds(data[0]?.actions);
      })
      .catch(() =>
        dispatch(
          openSnackBar(
            translatr('web.projects.main', 'ThereWasAnErrorRetrievingProjectPTA', {
              tm_tm_project: omp('tm_tm_project')
            }),
            'error'
          )
        )
      );
  }, [slug]);

  useEffect(() => {
    if (project) {
      document.title = project.title;
    }
  }, [project]);

  useEffect(() => {
    // Get related roles
    if (shouldShowTMJobRole() && rolesToFetch?.length > 0) {
      getRelatedRoles({
        type: JOB_TYPE.ROLE,
        opportunityId: rolesToFetch,
        language: currentUserLang
      });
    }
  }, [rolesToFetch?.length]);

  useEffect(() => {
    if (!projectLoading && project) {
      const { PAGE_URL, IS_OWNER, PROJECT_PUBLISHED_DATE } = TrackEventProperties.OMP;

      const isOwner = isProjectOwner(currentUserId, project);

      track(TrackEvents.OMP.PROJECT_DETAILS_PAGE, {
        ...getProjectMetaForAnalytics(project),
        [PAGE_URL]: window.location.href,
        [IS_OWNER]: isOwner,
        [PROJECT_PUBLISHED_DATE]: project.publishedOn
      });
    }
  }, [project, projectLoading]);

  // allow partial render if possible on first load
  useEffect(() => {
    if (mounted) {
      setProjectLoading(true);
      setProject(null);
    }
  }, [slug]);

  useEffect(() => {
    setMounted(true);
    if (!organizations.get('config')) {
      dispatch(getOrgConfiguration());
    }

    getCheckinsConfig()
      .then(data => {
        setIsCheckinEnabled(data?.enable_checkins?.value);
        setIsLoading(false);
      })
      .catch(err => {
        console.error('Error while fetching checkin config: ', err);
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    if (
      organizations.get('config')?.enable &&
      !organizations.get('organizationsByVisibility')[orgVisibility.PROJECT_DETAILS]
    ) {
      dispatch(
        loadAllOrganizations(currentUserLang, orgVisibility.PROJECT_DETAILS, orgAssociation.PROJECT)
      );
    }
  }, [
    organizations.get('config')?.enable,
    organizations.get('organizationsByVisibility')[orgVisibility.PROJECT_DETAILS]
  ]);

  useEffect(() => {
    recordCarrerGrowthDetailPageVisitedEvent({ pageType: OPPORTUNITY_TYPE.PROJECT, id: slug });
  }, []);

  const focusToLastTriggeringElement = useCallback(() => {
    setTimeout(() => {
      lastFocusedElementRef.current?.focus();
    }, 0);
  }, [lastFocusedElementRef]);

  if (project) {
    const {
      createdOn,
      description,
      endDate,
      languages,
      maxPositions,
      owners = [],
      positionsFilled,
      remoteWorkPossible,
      requireApplication,
      skillDetails: skills = [],
      startDate,
      thumbnail,
      timeCommitment,
      timeZoneDetails,
      title,
      id: projectId,
      applicationDeadline
    } = omitBy(project, isNil);

    const roles = mapJobRoles(relatedRolesData?.values);
    const showRelatedRolesCarousel =
      (!PROJECT_FIELDS_CONFIG || PROJECT_FIELDS_CONFIG[projectFields.JOB_ROLES]?.showField) &&
      rolesToFetch?.length > 0 &&
      (relatedRolesLoading || roles?.length > 0);

    const visibleDropdownActionIds =
      actionIds?.filter(
        actionId =>
          actionId !== Actions.BOOKMARK &&
          actionId !== Actions.UNBOOKMARK &&
          actionId !== Actions.SHARE &&
          actionId !== Actions.VIEW &&
          actionId !== Actions.APPLY
      ) || [];

    let dropdownActions = visibleDropdownActionIds
      .map(actionId => projectActions[actionId])
      .filter(a => !!a);

    if (completed) {
      dropdownActions = dropdownActions.filter(action => action.id !== Actions.COMPLETE);
    }

    return (
      <>
        {!isLoading && isCheckinEnabled && (
          <MfeLoader baseUrl="checkins-mfe-wizard" name="checkins-mfe-wizard" />
        )}
        <div className="ed-ui tm__project-details tmp-detail">
          <BackArrow label={translatr('web.common.main', 'Back')} />
          <main>
            <div className="tm__project-details-container">
              <div className="tm__project-details-main-panel">
                <Header
                  loading={projectLoading}
                  actions={dropdownActions}
                  owners={owners}
                  publishedDate={createdOn}
                  title={title}
                  isLoading={isLoading}
                  isCheckinEnabled={isCheckinEnabled}
                  userId={currentUserId}
                  projectStatus={projectStatus}
                />
                <Section
                  title={translatr('web.projects.main', 'ProjectDescription', {
                    tm_tm_project: omp('tm_tm_project')
                  })}
                >
                  <ShowMore>
                    <EditorDisplay dangerouslySetHTML={description} />
                  </ShowMore>
                </Section>
                {(!PROJECT_FIELDS_CONFIG ||
                  PROJECT_FIELDS_CONFIG[projectFields.SKILLS]?.showField) &&
                  skills.length > 0 && (
                    <Section
                      title={
                        translatr('web.projects.main', 'RelatedSkills') + ` (${skills?.length})`
                      }
                    >
                      <SkillsSection loading={projectLoading} items={skills} />
                    </Section>
                  )}
                {/* For Displaying RelatedRolesCarousel below related skills in large screens */}
                {showRelatedRolesCarousel && !isMobile && (
                  <RelatedRolesCarousel loading={relatedRolesLoading} roles={roles} />
                )}
              </div>
              <div className="tm__project-details-right-panel">
                <Sidebar
                  canApply={actionIds.includes(Actions.APPLY)}
                  canSubscribe={actionIds.includes(Actions.SUBSCRIBE)}
                  canStart={actionIds.includes(Actions.START)}
                  canConfirm={actionIds.includes(Actions.CONFIRM)}
                  canWithdraw={actionIds.includes(Actions.WITHDRAW)}
                  endDate={endDate}
                  image={thumbnail}
                  isApplicationRequired={requireApplication}
                  isBookmarked={bookmarked}
                  isOwner={isProjectOwner(currentUserId, project)}
                  isRemoteWorkPossible={remoteWorkPossible}
                  languages={languages?.map(code => languagesMap[code])}
                  locations={locationsData}
                  maxPositions={maxPositions}
                  onApply={onApply}
                  onBookmark={projectActions[Actions.BOOKMARK].onClick}
                  onShare={projectActions[Actions.SHARE].onClick}
                  onSubscribe={onSubscribe}
                  positionsFilled={positionsFilled}
                  projectStatus={projectStatus}
                  startDate={startDate}
                  timeCommitment={timeCommitment}
                  timeZones={timeZoneDetails}
                  title={title}
                  isLoading={projectLoading || applyLoading || subscribeLoading || loading}
                  isSubscribed={isSubscribed}
                  divisions={divisionsData}
                  onStart={onStart}
                  onWithdraw={onWithdraw}
                  onConfirm={onConfirm}
                  applicationDeadline={applicationDeadline}
                  projectStatusRef={projectStatusRef}
                />
              </div>
            </div>
            {/* For Displaying RelatedRolesCarousel below sidebar in small screens */}
            {showRelatedRolesCarousel && isMobile && (
              <RelatedRolesCarousel loading={relatedRolesLoading} roles={roles} />
            )}
            {showApplyForProjectModal && (
              <ApplyForProjectModal
                closeModal={() => {
                  setShowApplyForProjectModal(false);
                  focusToLastTriggeringElement();
                }}
                onPostSubmit={onRequireApplicationPostSubmit}
                projectTitle={title}
                projectId={projectId}
              />
            )}
            {showApplyConfirmationModal && (
              <ApplicationConfirmationModal
                closeModal={() => setShowApplyConfirmationModal(false)}
                isApplicationRequired={requireApplication}
                projectTitle={title}
                projectOwners={owners}
              />
            )}
            {showParticipationConfirmationModal && (
              <ParticipationConfirmationModal
                closeModal={() => {
                  setShowParticipationConfirmationModal(false);
                  setTimeout(() => projectStatusRef.current.focus(), 0);
                }}
                projectOwners={owners}
              />
            )}
          </main>
        </div>
      </>
    );
  }

  if (loadError) {
    return <TMNotFound />;
  }

  return <ViewProjectLoader />;
};

ViewProject.propTypes = {
  currentUserId: PropTypes.string,
  currentUserLang: PropTypes.string,
  dispatch: PropTypes.func,
  languagesMap: PropTypes.object.isRequired,
  availableLocations: PropTypes.array,
  availableLocationsError: PropTypes.object,
  organizations: PropTypes.object
};

export default connect(
  ({ currentUser, team, availableLocations, organizations }) => ({
    currentUserId: currentUser.get('id'),
    languagesMap: transformLanguages(team.toJS().languages),
    availableLocations: availableLocations.get('availableLocations'),
    availableLocationsError: availableLocations.get('availableLocationsError'),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations
  }),
  dispatch => ({ dispatch })
)(withAspirationsContext(ViewProject));
