import React from 'react';
import { connect } from 'react-redux';

import { translatr } from 'centralized-design-system/src/Translatr';
import Checkbox from 'centralized-design-system/src/Checkbox';
import Tooltip from 'centralized-design-system/src/Tooltip';
import remCalc from 'centralized-design-system/src/Utils/remCalc';
import { getNewTranslatedLabel, getProfileLanguage } from '@components/common/TranslatedLabel';

interface ExcludeContentProps {
  excludeFromSearch: boolean;
  excludeFromRecommendation: boolean;
  setExcludeContent: (value: boolean, excludeFrom: string) => void;
  todaysLearningSearchObj: object;
  allLangs: { [language: string]: string; };
  currentUserLang: string;
  currentAppLang: string;
}

interface RootState {
  currentUser: Map<string, any>;
  team: Map<string, any>;
}

const ExcludeContent: React.FC<ExcludeContentProps> = ({
  excludeFromSearch,
  excludeFromRecommendation,
  setExcludeContent,
  todaysLearningSearchObj,
  allLangs,
  currentUserLang,
  currentAppLang
}) => {

  const excludeContentTooltipMsg = translatr(
    'cds.common.main',
    'ExcludeContentHint'
  );

  const profileLang = getProfileLanguage({
    langs: allLangs,
    currentUserLang: currentUserLang || currentAppLang || 'en'
  });

  const recommendationLabel = getNewTranslatedLabel({
    labelObj: todaysLearningSearchObj,
    appName: 'web.home.main',
    profileLanguage: profileLang,
    newDefaultLabel: 'Todays Insights',
    orgLanguages: allLangs,
    skipLabelCheck: false
  });

  const excludeFromRecommendationLabel = translatr('cds.common.main', 'ExcludeFromRecommendations', {
    excludeFromRecommendationLabel: recommendationLabel
  });

  return (
    <div className="mt-16" role="group" aria-labelledby="exclude-content-label">
      <div className="ed-input-container">
        <label className="ed-input-title">
          <span id="exclude-content-label">
            {translatr('cds.common.main', 'ExcludeContent')}
            <span className="optional-text" aria-label={` ${translatr('cds.common.main', 'Optional')}`}>
              {translatr('cds.common.main', 'Optional')}
            </span>
          </span>
          <Tooltip
            message={excludeContentTooltipMsg}
            pos={'right'}
            isTranslated
            tooltipParentRole={'tooltip'}
            tabIndex='0'
            tooltipCardInlineCss={{
              'max-width': remCalc(450)
            }}
          >
            <i
              className="icon-info-circle radio-tooltip mt-5"
              aria-label={excludeContentTooltipMsg}
              role="img"
            />
          </Tooltip>
        </label>
      </div>
      <div className="exclude-content-container">
        <div className="exclude-search-checkbox">
          <Checkbox
            label={translatr('cds.common.main', 'ExcludeFromSearch')}
            checked={excludeFromSearch}
            ariaLabel={translatr('cds.common.main', 'ExcludeFromSearch')}
            onChange={() => setExcludeContent(!excludeFromSearch, 'search')}
            isTranslated
          />
        </div>
        <div className="exclude-recommendation-checkbox">
          <Checkbox
            label={excludeFromRecommendationLabel}
            checked={excludeFromRecommendation}
            ariaLabel={excludeFromRecommendationLabel}
            onChange={() => setExcludeContent(!excludeFromRecommendation, 'recommendation')}
            isTranslated
          />
        </div>
      </div>
    </div>
  );
};

const mapStoreStateToProps = (state: RootState) => {
  const { currentUser, team } = state;

  return {
    todaysLearningSearchObj: team?.get('Feed')?.['feed/todaysLearningSearch'],
    allLangs: team.get('languages'),
    currentUserLang: currentUser.get('profile').get('language'),
    currentAppLang: team.get('currentAppLanguage')
  };
};

export default connect(mapStoreStateToProps)(ExcludeContent);
