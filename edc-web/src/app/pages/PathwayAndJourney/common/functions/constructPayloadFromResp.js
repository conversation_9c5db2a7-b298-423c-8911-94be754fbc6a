import { getSelectedProficiencyLevelObj } from 'centralized-design-system/src/Utils/proficiencyLevels';
import { ENTITY_TYPE } from '../Settings/constants';
import {
  getMarkAsCompleteRadioOptions,
  getJourneyRadioOptions,
  getTransformedDataBasedOnType,
  extractSkills
} from '../Settings/utils';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';

function getEntityDefaultValue(entityFields, field_name, defaultValue) {
  let returnValue = defaultValue;
  if (entityFields) {
    try {
      const respFields = entityFields.filter(ef => ef.field_name === field_name)[0];
      if (respFields) {
        const respLanguages = respFields.entity_field_languages_attributes;
        for (let fieldLang of respLanguages) {
          if (fieldLang.is_default_language) {
            returnValue = fieldLang.value;
            break;
          }
        }
      }
    } catch (err) {
      console.error('Unable to parse entity_fields_languages_attributiions for cardTitle');
    }
  }
  return returnValue;
}

export default function constructPayloadFromResponse({
  resp,
  pathwayCompleteDefaultValue,
  isJourney = false,
  proficiencyLevels,
  currentUserLang = 'en'
}) {
  const {
    cardTitle,
    cardMessage,
    cardMetadatum,
    filestack,
    provider,
    providerImage,
    isPublic,
    language,
    uniqueCode,
    autoComplete,
    userTaxonomyTopics,
    tags,
    badging,
    skillLevel,
    usersWithAccess,
    teams,
    channels,
    usersPermitted,
    teamsPermitted,
    contributors: collaborators,
    cardSubtype,
    journeySection,
    journeySections,
    state,
    id: cardId,
    slug,
    packCards,
    entityFields,
    media
  } = resp;

  const {
    title: badgeTitle,
    imageUrl,
    allQuizzesAnswered,
    badgeId,
    badgeTemplateId,
    canBeAssigned,
    imageOriginalUrl,
    imageAltText
  } = badging || {};

  const skills = (userTaxonomyTopics && extractSkills(userTaxonomyTopics)) || [];

  const JourneySectionArr = journeySection || journeySections || [];

  const selectedWeek = JourneySectionArr?.length;
  const selectedDate = JourneySectionArr?.[0]?.start_date;

  const usersWithAccessArr = getTransformedDataBasedOnType(usersWithAccess, ENTITY_TYPE.user);
  const teamsWithAccess = getTransformedDataBasedOnType(teams, ENTITY_TYPE.team);
  const channelsWithAccess = getTransformedDataBasedOnType(channels, ENTITY_TYPE.channel);

  const usersRestrictedWith = getTransformedDataBasedOnType(usersPermitted, ENTITY_TYPE.user);
  const teamsRestrictedWith = getTransformedDataBasedOnType(teamsPermitted, ENTITY_TYPE.team);

  // Check to see if there is entityFields and get the default as the cardTitle/cardMessage
  let cardTitleOrEntityField = getEntityDefaultValue(entityFields, 'title', cardTitle);
  let cardMessageOrEntityField = getEntityDefaultValue(entityFields, 'message', cardMessage);
  const LXMediaHubConfig = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
  return {
    state,
    cardId,
    slug,
    packCards,
    cardMetadatum,
    skillLevel,

    // Details
    title: cardTitleOrEntityField,
    nonCuratedChannelIds: resp.nonCuratedChannelIds,
    description: cardMessageOrEntityField,
    thumbnailObj: { file: LXMediaHubConfig ? media || {} : filestack?.[0] || {} },
    markAsComplete: getMarkAsCompleteRadioOptions([], pathwayCompleteDefaultValue, autoComplete),
    entityFields,

    // Options
    skills,
    tagsSelected: tags.map(tag => ({ value: tag.name, label: tag.name })),
    language: { value: language },
    uniqueCode,
    level: getSelectedProficiencyLevelObj(skillLevel, proficiencyLevels, currentUserLang),
    excludeFromSearch: cardMetadatum.excludeFromSearch || false,
    excludeFromRecommendation: cardMetadatum.excludeFromRecommendation || false,

    //JourneyType
    ...(isJourney && {
      journeyTypeOptions: getJourneyRadioOptions([], cardSubtype, !!resp.publishedAt),
      selectedWeek,
      selectedDate,
      oldJourneySections: journeySection
    }),

    // Provider Options
    providerName: provider || '',
    providerThumbNailObj: {
      file: {
        url: providerImage?.url || providerImage,
        filename: providerImage?.filename,
        mimetype: providerImage?.mimetype
      }
    },

    // Social
    privacySettings: isPublic ? 'public' : 'private',
    shareWith: [...usersWithAccessArr, ...teamsWithAccess, ...channelsWithAccess],
    restrictWith: [...usersRestrictedWith, ...teamsRestrictedWith],
    collaborators,
    collaboratorIds: collaborators.map(collabObj => collabObj.id),

    // Badge Options
    badgeTitle,
    selectedBadge: {
      imageUrl,
      imageAltText,
      id: badgeId,
      title: badgeTitle,
      allQuizzesAnswered,
      canBeAssigned,
      badgeTemplateId,
      imageOriginalUrl
    },
    unlockBadgeAfterQuizSuccess: [{ checked: allQuizzesAnswered }], // If allQuizzesAnswered then enable allQuizzesAnswered checkbox.
    getBadgeOnCompletionOption: [{ checked: !!canBeAssigned, disabled: false }] // EP-88056
  };
}
