import {
  DefaultImage,
  NavigationType,
  TopicDetails,
  TopicFormData, TopicSortOptionId,
  
} from '@pages/ExploreByTopics/types';
import {
  archiveSearchUrlTopic,
  createSearchUrlTopic,
  deleteSearchUrlTopic,
  getAllActiveSearchUrlTopics,
  getAllArchivedSearchUrlTopics,
  getAllSearchUrlTopicsCreatedByCurrentUser,
  restoreArchivedSearchUrlTopic,
  updateSearchUrlTopic
} from 'edc-web-sdk/requests/serachUrlTopics';
import { getDefaultImage } from 'centralized-design-system/src/Utils/filestack';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';

const mapTopic = (topic: any): TopicDetails => {
  const defaultImage = getDefaultImage(window.__ED__.id) as DefaultImage;
  return {
    id: topic.id,
    name: topic.name,
    description: topic.description,
    searchLink: topic.search_link,
    archived: <PERSON><PERSON><PERSON>(topic.is_archived),
    image: topic.image,
    imagePreviewUrl: topic.image_preview_url || defaultImage?.url || '',
  };
}

const mapResponse = (response: any): { list: Array<TopicDetails>, count: number } => {
  return {
    list: response.data.map((topic: any) => mapTopic(topic)),
    count: response.count
  };
}

export const TOPIC_LIMIT = 9;

export const loadTopics = async (navigation: NavigationType, sortOption: TopicSortOptionId, searchValue: string, offset: number = 0): Promise<{ list: Array<TopicDetails>, count: number }> => {
  const payload = (() => {
    switch (sortOption) {
      case 'alphabetical_asc': {
        return {
          sort: 'name',
          order: 'asc',
          limit: TOPIC_LIMIT,
          offset,
          query: searchValue
        };
      }
      case 'alphabetical_desc': {
        return {
          sort: 'name',
          order: 'desc',
          limit: TOPIC_LIMIT,
          offset,
          query: searchValue
        };
      }
      case 'creation_date_asc': {
        return {
          sort: 'created_at',
          order: 'asc',
          limit: TOPIC_LIMIT,
          offset,
          query: searchValue
        };
      }
      case 'creation_date_desc': {
        return {
          sort: 'created_at',
          order: 'desc',
          limit: TOPIC_LIMIT,
          offset,
          query: searchValue
        };
      }
    }
  })();

  switch (navigation) {
    case "active": {
      const response = await getAllActiveSearchUrlTopics(payload);
      return mapResponse(response);
    }
    case "createdByMe": {
      const response = await getAllSearchUrlTopicsCreatedByCurrentUser(payload);
      return mapResponse(response);
    }
    case "archived": {
      const response = await getAllArchivedSearchUrlTopics(payload);
      return mapResponse(response);
    }
    default:
      console.error("Invalid navigation");
      return { list: [], count: 0 };
  }
}

export const saveTopic = async (formData: TopicFormData, uuid?: number): Promise<TopicDetails> => {
  if(uuid) {
    const topic = await updateSearchUrlTopic(uuid, {
      name: formData.name,
      search_link: formData.searchLink,
      description: formData.description,
      ...getImagePayload(formData)
    });
    return mapTopic(topic);
  } else {
    const topic = await createSearchUrlTopic({
      name: formData.name,
      search_link: formData.searchLink,
      description: formData.description,
      ...getImagePayload(formData)
    });
    return mapTopic(topic);
  }
}

export const deleteTopic = async (uuid: number): Promise<number> => {
  const topic = await deleteSearchUrlTopic(uuid);
  return topic.id
}

export const archiveTopic = async (uuid: number): Promise<TopicDetails> => {
  const topic = await archiveSearchUrlTopic(uuid);
  return mapTopic(topic);
}


export const restoreTopic = async (uuid: number): Promise<TopicDetails> => {
  const topic = await restoreArchivedSearchUrlTopic(uuid);
  return mapTopic(topic);
}
const getImagePayload = (formData: TopicFormData) => {
  const isLxMediaEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
  if (isLxMediaEnabled) {
    return formData.image ? { image: formData.image } : {};
  } else {
    return { image: formData.image || getDefaultImage(window.__ED__.id) };
  }
};