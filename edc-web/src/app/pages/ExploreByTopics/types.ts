export type TopicState = {
  navigation: NavigationType,
  sortOption: TopicSortOptionId,
  searchValue: string,
  topics: {
    list: Array<TopicDetails>,
    total: number,
    offset: number
  }
  loading: boolean,
  error: boolean
}

export type TopicAction =
  | { type: 'TOPIC_DATA_LOADING' }
  | { type: 'TOPIC_DATA_LOADED'; data: TopicsData }
  | { type: 'TOPIC_DATA_LOADING_FAILED' }
  | { type: 'MORE_TOPICS_LOADED'; data: TopicsData, offset: number }
  | { type: 'MORE_TOPICS_LOADING_FAILED' }
  | { type: 'NAVIGATION_CHANGED'; navigation: NavigationType }
  | { type: 'SORT_CHANGED'; sortOption: TopicSortOptionId }
  | { type: 'CONTENT_SEARCH'; query: string }
  | { type: 'MORE_TOPIC_LOADED'; data: any; offset: number }
  | { type: 'MORE_TOPIC_LOADING_FAILED' }
  | { type: 'TOPIC_CREATED'; newTopic: TopicDetails }
  | { type: 'TOPIC_EDITED'; editedTopic: TopicDetails }
  | { type: 'TOPIC_ARCHIVED'; topicId: number }
  | { type: 'TOPIC_RESTORED'; topicId: number }
  | { type: 'TOPIC_DELETED'; topicId: number }
  | { type: 'TOPIC_SEARCH_VALUE_CHANGED'; searchValue: string }

export type NavigationType = "active" | "createdByMe" | "archived";

export type TopicDetails = {
  id: number,
  name: string,
  description?: string,
  image?: TopicImage,
  searchLink: string,
  archived: boolean,
  imagePreviewUrl?: string
}

export interface TopicFormData {
  name: string;
  searchLink: string;
  description: string;
  image?: TopicImage
}

export interface TopicFormError {
  name?: string,
  searchLink?: string,
}

export interface TopicImage {
  key: string,
  filename: string,
  url: string,
  handle: string,
  mimetype: string,
  size: number,
  source: string,
  status: string,
}

export type TopicSortOptionId = "creation_date_desc" | "creation_date_asc" | "alphabetical_asc" | "alphabetical_desc"

export type TopicSortOption = {
  id: TopicSortOptionId,
  value: string
}

export type TopicsData = {
  list: Array<TopicDetails>,
  count: number
}

export interface AttachmentImage {
  file: {
    alt_text?: string,
    container?: string,
    filename: string,
    handle: string,
    key?: string,
    mimetype: string,
    originalPath?: string,
    size: number,
    source?: string,
    status?: string,
    uploadId?: string,
    url: string,
    upload_source: string
    content_type: string
  },
  securedUrl: string
}

export type DefaultImage = {
  url: string | null;
  mimetype: string | null;
  key: string | null;
};
