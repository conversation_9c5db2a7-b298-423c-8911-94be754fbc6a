import React, { ReactElement, useState } from "react";
import { useSelector } from "react-redux";
import FocusLock from "react-focus-lock";
import { getUsersMLP } from "edc-web-sdk/requests/users.v2";
import { translatr } from "centralized-design-system/src/Translatr";
import { CheckboxListWithSearchFilter } from "centralized-design-system/src/OptionListWithSearchFilter";
import Modal, { <PERSON>dalContent, ModalHeader } from "centralized-design-system/src/Modals";
import Tooltip from "centralized-design-system/src/Tooltip";
import Checkbox from "centralized-design-system/src/Checkbox";
import { Select } from "centralized-design-system/src/Inputs";
import { truncateText } from "@utils/utils";
import LD from "../../../../containers/LDStore";
import DateRange from "../../components/DateRange";
import { DateRangeType, SelectItem } from "../../MyLearningTypes";
import { formatDateISOString, setDate, yearOptions, formatDate } from "../../utils";
import "./MyLearningFiltersModal.scss";

const buildAssignmentPriorities = ({
  "web/assignmentPriority/mandatory": mandatory,
  "web/assignmentPriority/recommended": recommended,
  "web/assignmentPriority/optional": optional,
}: any) =>
  !LD.assignmentPriorityEnabled() ? [] : [
    mandatory.visible ? {
      label: mandatory.label || translatr("web.mylearningplan.main", mandatory.defaultLabel),
      value: "mandatory",
      checked: false
    } : undefined,
    recommended.visible ? {
      label: recommended.label || translatr("web.mylearningplan.main", recommended.defaultLabel),
      value: "recommended",
      checked: false
    } : undefined,
    optional.visible ? {
      label: optional.label || translatr("web.mylearningplan.main", optional.defaultLabel),
      value: "optional",
      checked: false
    } : undefined,
  ].filter(ap => ap);

const filterFields = (filters: ReturnMyLearningFilters, fields: MyLearningFiltersModalProps["fields"]): ReturnMyLearningFilters =>
  fields.reduce((acc, curr) => ({
    ...acc,
    [curr]: filters[curr],
  }), {
    assignmentPriority: filters.assignmentPriority,
    assignorIds: filters.assignorIds,
    yearFilter: filters.yearFilter,
  });
const mapResult = (filters: InnerMyLearningFilters, fields: MyLearningFiltersModalProps["fields"]): ReturnMyLearningFilters => filterFields({
  assignmentPriority: filters.assignmentPriority,
  assignedDate: formatDate(filters.assignedDate),
  dueDate: formatDateISOString(filters.dueDate),
  completedDate: formatDate(filters.completedDate),
  startedDate: formatDate(filters.startedDate),
  assignorIds: filters.assignors?.map((a: any) => a.id),
  yearFilter: filters.yearFilter,
}, fields);

interface FieldCheckerProps {
  fieldName: OptionalFields;
  fields: Array<OptionalFields>;
  children: ReactElement;
}
const FieldChecker = ({ fieldName, fields, children }: FieldCheckerProps) =>
  fields.includes(fieldName) ? children : null;

interface MyLearningFilters<T> {
  assignedDate?: T;
  dueDate?: T;
  completedDate?: T;
  startedDate?: T;
  assignors?: Array<{
    firstName?: string;
    lastName?: string;
    id: string;
  }>;
  assignmentPriority?: Array<string>;
  yearFilter?: string | null;
}
type InnerMyLearningFilters = MyLearningFilters<DateRangeType>;
type ReturnMyLearningFilters = Omit<MyLearningFilters<{ start: string, end: string }>, "assignors"> & {
  assignorIds?: Array<string>;
};

type OptionalFields = "assignedDate" | "dueDate" | "completedDate" | "startedDate" | "yearFilter";
interface MyLearningFiltersModalProps {
  initialFilters?: ReturnMyLearningFilters;
  fields: Array<OptionalFields>;
  onClose: (filters?: ReturnMyLearningFilters) => void;
}

const MyLearningFiltersModal = ({ initialFilters, fields, onClose }: MyLearningFiltersModalProps) => {
  const currentUserId = useSelector((state: any) => state.currentUser.get("id"));
  const assignmentPriorities = useSelector((state: any) => state.team.get("OrgConfig")?.assignmentPriority) || {};

  const assignmentPrioritiesCheckboxes = buildAssignmentPriorities(assignmentPriorities);

  const [filters, setFilters] = useState<InnerMyLearningFilters>({
    assignedDate: setDate(initialFilters?.assignedDate),
    dueDate: setDate(initialFilters?.dueDate),
    completedDate: setDate(initialFilters?.completedDate),
    startedDate: setDate(initialFilters?.startedDate),
    assignors: initialFilters?.assignorIds?.map(id => ({ id })) || [],
    assignmentPriority: initialFilters?.assignmentPriority || [],
    yearFilter: initialFilters?.yearFilter || null,
  });

  const clearFilters = () => {
    setFilters({
      assignedDate: setDate(),
      dueDate: setDate(),
      completedDate: setDate(),
      startedDate: setDate(),
      assignors: [],
      assignmentPriority: [],
      yearFilter: null
    });
  };

  const updateFilters = (filter: Partial<InnerMyLearningFilters> | ((filters: InnerMyLearningFilters) => Partial<InnerMyLearningFilters>)) => {
    setFilters(f => {
      const incomingUpdate = filter instanceof Function ? filter(f) : filter;
      let finalUpdate = { ...incomingUpdate };

      // If yearFilter is being set (and is truthy), clear date filters
      if (incomingUpdate.yearFilter) {
          finalUpdate = {
              ...finalUpdate,
              dueDate: setDate(),
              completedDate: setDate(),
          };
      }

      // If a date filter is being set (and has value), clear yearFilter
      const dateBeingSet = incomingUpdate.dueDate && (incomingUpdate.dueDate.start || incomingUpdate.dueDate.end)
        || incomingUpdate.completedDate && (incomingUpdate.completedDate.start || incomingUpdate.completedDate.end);

      if (dateBeingSet) {
          finalUpdate = { ...finalUpdate, yearFilter: null };
      }

      // Note: If yearFilter and a date are set in the same call, yearFilter takes precedence,
      // clearing the dates.

      return {
        ...f,
        ...finalUpdate,
      };
    });
  };

  const isDateFieldDisabled = () => !!filters.yearFilter;
  const isYearFilterDisabled = () => !!(
    filters.dueDate?.start || 
    filters.dueDate?.end || 
    filters.completedDate?.start || 
    filters.completedDate?.end
  );

  const formatAssignedByUsers = (data: { users: any[], total_count: number }) => ({
    options: data?.users
      .filter((user: any) => user.id !== +currentUserId)
      .map((user: any) => ({
        ...user,
        label: `${user.firstName} ${user.lastName}`,
        value: `${user.id}`,
      })),
    totalCount: data.total_count
  });

  return (
    <Modal size="large" className="my-learning-filters-modal">
      <FocusLock>
        <ModalHeader
          title={translatr("web.search.main", "AllFilters")}
          onClose={() => onClose()}
        />
        <ModalContent>
          <div className="my-learning-filters-content">
            <FieldChecker fieldName="assignedDate" fields={fields}>
              <div className="my-learning-filters-content-date-filter">
                <DateRange
                  label={translatr("web.common.main", "AssignedDate2")}
                  dateRange={filters.assignedDate}
                  setDateRange={assignedDate => updateFilters({ assignedDate })}
                />
              </div>
            </FieldChecker>
            <FieldChecker fieldName="dueDate" fields={fields}>
              <div
                className={`my-learning-filters-content-date-filter ${isDateFieldDisabled() ? 'date-range-disabled' : ''}`}
                {...(isDateFieldDisabled() ? { inert: "true" } : {})}
                aria-disabled={isDateFieldDisabled()}
              >
                <DateRange
                  label={translatr("web.common.main", "DueDate2")}
                  dateRange={filters.dueDate}
                  setDateRange={dueDate => updateFilters({ dueDate })}
                />
              </div>
            </FieldChecker>
            <FieldChecker fieldName="completedDate" fields={fields}>
              <div
                className={`my-learning-filters-content-date-filter ${isDateFieldDisabled() ? 'date-range-disabled' : ''}`}
                {...(isDateFieldDisabled() ? { inert: "true" } : {})}
                aria-disabled={isDateFieldDisabled()}
              >
                <DateRange
                  label={translatr("web.common.main", "CompletedDate2")}
                  dateRange={filters.completedDate}
                  setDateRange={completedDate => updateFilters({ completedDate })}
                />
              </div>
            </FieldChecker>
            <FieldChecker fieldName="startedDate" fields={fields}>
              <div className="my-learning-filters-content-date-filter">
                <DateRange
                  label={translatr("web.common.main", "StartedDate2")}
                  dateRange={filters.startedDate}
                  setDateRange={startedDate => updateFilters({ startedDate })}
                />
              </div>
            </FieldChecker>
            <FieldChecker fieldName="yearFilter" fields={fields}>
              <div className="my-learning-filters-content-year-filter">
                <Select
                    title={translatr("web.common.main", "Year")}
                    items={yearOptions}
                    defaultValue={filters.yearFilter}
                    onChange={(item: SelectItem) => updateFilters({ yearFilter: item.value })}
                    isTranslated={true}
                    disabled={isYearFilterDisabled()}
                  />
              </div>
            </FieldChecker>
            <div className="my-learning-filters-content-selector-filters">
              {!!assignmentPrioritiesCheckboxes.length && (
                <div className="my-learning-filters-content-selector-filters-filter">
                  <label>{translatr("web.common.main", "AssignmentType")}</label>
                  {assignmentPrioritiesCheckboxes.map((priority) => (
                    <Tooltip
                      key={`assignment-priority-${priority.value}`}
                      message={priority.label}
                      isTranslated
                    >
                      <Checkbox
                        id={priority.value}
                        label={truncateText(priority.label, 20)}
                        checked={filters.assignmentPriority.includes(priority.value)}
                        onChange={(e: any) => {
                          updateFilters(filters => ({
                            assignmentPriority: e.target.checked ?
                              [ ...filters.assignmentPriority, priority.value ]
                              :
                              filters.assignmentPriority.filter(ap => ap !== priority.value)
                          }));
                        }}
                        isTranslated
                      />
                    </Tooltip>
                  ))}
                </div>
              )}
              <div className="my-learning-filters-content-selector-filters-filter">
                <CheckboxListWithSearchFilter
                  selected={filters.assignors}
                  resetCheckboxes={filters.assignors.length === 0}
                  onSelectionChange={(assignors: any) => updateFilters({ assignors })}
                  heading={translatr("web.mylearningplan.main", "AssignedBy")}
                  getListData={getUsersMLP}
                  otherPayload={{ type: "assignor" }}
                  initialList={[{
                    id: currentUserId,
                    value: currentUserId,
                    label: translatr("web.mylearningplan.main", "ByYou"),
                  }]}
                  generateFormattedObj={formatAssignedByUsers}
                  isTranslated
                />
              </div>
            </div>
          </div>
          <div className="my-learning-filters-buttons">
            <button
              className="ed-btn"
              onClick={() => onClose()}
            >
              {translatr("web.team.main", "Cancel")}
            </button>
            <button
              className="ed-btn"
              onClick={() => clearFilters()}
            >
              {translatr("web.mylearningplan.main", "Clear")}
            </button>
            <button
              className="ed-btn ed-btn-primary"
              onClick={() => onClose(mapResult(filters, fields))}
            >
              {translatr("web.team.main", "Apply")}
            </button>
          </div>
        </ModalContent>
      </FocusLock>
    </Modal>
  );
};

export default MyLearningFiltersModal;
