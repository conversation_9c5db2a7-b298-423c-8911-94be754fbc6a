import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { translatr } from "centralized-design-system/src/Translatr";
import Switch from "centralized-design-system/src/Switch";
import { getUserInfo, updateUser } from "edc-web-sdk/requests/users";
import "./PrivateToMeSwitch.scss";

const PRIVATE_TO_MANAGER_KEY = "assignments_private_to_manager";

interface UserInfo {
  dashboardInfo?: Array<{
    name: string;
    visible: boolean;
  }>;
}
interface PrivateToMeSwitchProps {
  title?: string;
  info?: string;
}
const PrivateToMeSwitch = ({ title, info }: PrivateToMeSwitchProps) => {
  const currentUserId = useSelector((state: any) => state.currentUser.get("id"));

  const [selected, setSelected] = useState<boolean>(null);
  const [userInfo, setUserInfo] = useState<UserInfo>();

  const setData = (data: UserInfo) => {
    const alreadySelected = data?.dashboardInfo
      ?.find(item => item.name === PRIVATE_TO_MANAGER_KEY)
      ?.visible;
    setSelected(alreadySelected);
    setUserInfo(data);
  };

  useEffect(() => {
    getUserInfo({ fields: "dashboard_info" })
      .then(setData);
  }, []);

  const onChange = (checked: boolean) => {
    const newUserInfo = {
      profile_attributes: {
        dashboard_info: [
          ...userInfo?.dashboardInfo.filter(item => item.name !== PRIVATE_TO_MANAGER_KEY),
          { name: PRIVATE_TO_MANAGER_KEY, visible: checked },
        ],
      }
    };
    updateUser(currentUserId, newUserInfo)
      .then(setData);
  };

  return (
    <div className="private-to-me-switch">
      {title && (<span className="private-to-me-switch-title supporting-text">{title}</span>)}
      <Switch
        name={translatr("web.mylearningplan.main", "PrivateToMe")}
        defaultChecked={selected}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.checked)}
        role="switch"
      />
      {info && (<span className="private-to-me-switch-info">{info}</span>)}
    </div>
  );
}

export default PrivateToMeSwitch;
