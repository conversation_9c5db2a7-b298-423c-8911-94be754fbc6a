import { useContext, useEffect } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Avatar, { AVATAR_SIZES } from 'centralized-design-system/src/Avatar';
import { Button } from 'centralized-design-system/src/Buttons';
import { useNavigate } from 'react-router-dom';
import TextClamp from '@components/TextClamp';
import './styles.scss';
import Books from '../../../../../../icons/Books';
import Lightbulb from '../../../../../../icons/Lightbulb';
import Certificate from '../../../../../../icons/Certificate';
import FileCertificate from '../../../../../../icons/FileCertificate';
import VerticalDivider from '../../../../../../icons/VerticalDivider';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';

const Header = () => {
  const navigate = useNavigate();
  const mdContext = useContext(MDContext);
  const { selectedReportee, selectedReporteeStats, navigateToOverview } = mdContext;

  useEffect(() => {
    if (!selectedReportee) {
      navigate('/manager-dashboard');
    }
  }, [selectedReportee, navigate]);

  const { skills_passport_stats } = selectedReporteeStats || {};
  return (
    <div className="mdblocks-individual-header">
      <div className="title-section">
        <h1 className="title">{translatr('web.common.main', 'ManagerDashboard')}</h1>
        <Button
          color="primary"
          variant="borderless"
          onClick={() => {
            navigateToOverview();
          }}
        >
          {translatr('web.manager-dashboard-v2.main', 'GoBackToTeamView') || 'Go back to team view'}
        </Button>
      </div>
      <div className="stats-section">
        <div className="header-user-info">
          <div className="header-user-info__avatar">
            <Avatar
              user={{ imgUrl: selectedReportee?.imageUrl, name: selectedReportee?.name }}
              size={AVATAR_SIZES['5XLARGE']}
              alt={selectedReportee?.name}
            />
          </div>
          <div className="header-user-info__details">
            <button
              className="header-user-info__name"
              onClick={() => {
                navigate(`/${selectedReportee?.handle}`);
              }}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  navigate(`/${selectedReportee?.handle}`);
                }
              }}
            >
              <h2 className="header-user-info__name">{selectedReportee?.name}</h2>
            </button>
            <TextClamp line={2} className="header-user-info__position">
              {selectedReportee?.job}
            </TextClamp>
          </div>
        </div>
        <div className="header__stats">
          <div className="header__stats-item">
            <div className="header__stats-title">
              {translatr('web.manager-dashboard-v2.main', 'DeclaredSkillsLabel') ||
                'Declared Skills'}
            </div>
            <div className="header__stats-content">
              <span className="icon">
                <Lightbulb />
              </span>
              <h2>{skills_passport_stats?.skill || 0}</h2>
            </div>
          </div>
          <VerticalDivider />
          <div className="header__stats-item">
            <div className="header__stats-title">
              {translatr('web.manager-dashboard-v2.main', 'DevelopingSkillsLabel') ||
                'Developing Skills'}
            </div>
            <div className="header__stats-content">
              <span className="icon">
                <Books />
              </span>
              <h2>{skills_passport_stats?.developing_skill || 0}</h2>
            </div>
          </div>
          <VerticalDivider />
          <div className="header__stats-item">
            <div className="header__stats-title">
              {`${translatr('web.common.main', 'Certifications')}...`}
            </div>
            <div className="header__stats-content">
              <span className="icon">
                <FileCertificate />
              </span>
              <h2>{skills_passport_stats?.certificate || 0}</h2>
            </div>
          </div>
          <VerticalDivider />
          <div className="header__stats-item">
            <div className="header__stats-title">{translatr('web.common.main', 'Badges')}</div>
            <div className="header__stats-content">
              <span className="icon">
                <Certificate />
              </span>
              <h2>{skills_passport_stats?.badge || 0}</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Header;
