@import '~centralized-design-system/src/Styles/_variables.scss';

// Define a mixin for shared text styles
@mixin shared-text-style($font-weight, $font-size, $line-height) {
  font-weight: $font-weight;
  font-size: $font-size;
  line-height: $line-height;
  letter-spacing: 0%;
  vertical-align: middle;
}

.mdblocks-individual-header {
  .title-section {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: var(--ed-spacing-3xl);
    margin-top: var(--ed-spacing-3xl);

    h1 {
      margin-bottom: 0;
    }

    .title {
      min-height: 40px;
    }

    .link-button {
      @include shared-text-style(var(--ed-font-weight-bold), 15px, 24px);
      color: var(--ed-primary-base);

      &:hover {
        cursor: pointer;
      }
    }
  }

  .stats-section {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: var(--ed-spacing-base);
    margin-bottom: var(--ed-spacing-base);
    max-width: rem-calc(1200);
    min-height: calc(144);
    background: var(--ed-white);
    box-shadow: var(--ed-shadow-xs), 0px 2px 2px rgba(33, 34, 38, 0.24);
    border-radius: var(--ed-border-radius-lg);
    padding: var(--ed-spacing-3xl) var(--ed-spacing-base) var(--ed-spacing-base)
      var(--ed-spacing-base);

    .header-user-info {
      display: flex;
      align-items: center;
      margin-bottom: var(--ed-spacing-base);

      .header-user-info__avatar {
        display: flex;
        margin-right: var(--ed-spacing-base);
      }

      .header-user-info__details {
        display: flex;
        flex-direction: column;

        .header-user-info__name {
          @include shared-text-style(var(--ed-font-weight-black), 20px, 27px);
          color: var(--ed-text-color-primary);
          text-align: left;
          &:hover {
            cursor: pointer;
          }
        }

        .header-user-info__position {
          display: flex;
        }
      }
    }

    .header__stats {
      display: flex;
      flex-wrap: nowrap;
      flex-direction: row;
      align-items: center;
      margin: 0 var(--ed-spacing-base) var(--ed-spacing-base) var(--ed-spacing-base);

      .header__stats-item {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .header__stats-title {
          @include shared-text-style(400, 14px, 21px);
        }

        .header__stats-content {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          align-items: center;

          .icon {
            display: flex;
            align-items: center;
            margin-right: var(--ed-spacing-3xs);
          }

          h2 {
            @include shared-text-style(var(--ed-font-weight-black), 24px, 36px);
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
