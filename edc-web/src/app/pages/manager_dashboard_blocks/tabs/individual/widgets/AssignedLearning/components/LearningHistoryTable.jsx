import { useState, useEffect, useRef, useCallback } from 'react';
import { string } from 'prop-types';
import _, { debounce } from 'lodash';
import unescape from 'lodash/unescape';
import moment from 'moment';
import DatePicker from 'centralized-design-system/src/DatePickers';
import Loading from 'centralized-design-system/src/Loading';
import Pagination from 'centralized-design-system/src/Pagination';
import { Select, SearchInput } from 'centralized-design-system/src/Inputs';
import Table from 'centralized-design-system/src/Table';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getUserLearnings } from 'edc-web-sdk/requests/managerDashboard.v2';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { truncateText } from '@utils/utils';
import { DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT, MONTH_DAY_YEAR_FORMAT } from '@utils/constants';
import { getMMDDYYY, getMMDDYYYStr } from '../utils/utils';
import { sortData } from '../utils/tableUtil';
import FilterFlyout from './FilterFlyout';
import NoResultFoundInSearch from './NoResultFoundInSearch';
import NoAssignedTraining from './NoAssignedTraining';
import { PAGE_ROW_OPTIONS } from '@pages/manager_dashboard_blocks/tabs/learning/helpers';

const ASSIGNOR_OFFSET_AMOUNT = 8;
const ALL_TIME = translatr('web.common.main', 'AllTime');

const LearningHistoryTable = ({ activeUser }) => {
  const [allData, setAllData] = useState({});
  const [assignamentData, setAssignamentData] = useState({});
  const [pageSize, setPageSize] = useState(PAGE_ROW_OPTIONS[0]);
  const [assignedDate, setAssignedDate] = useState({
    fromDate: null,
    toDate: null
  });
  const [completeDate, setCompleteDate] = useState({
    fromDate: null,
    toDate: null
  });
  const [currentPage, setCurrentPage] = useState(0);
  const [isFirst, setFirst] = useState(true);
  const [loading, setLoading] = useState(true);
  const [placeholderAssignedDate, setPlaceholderAssignedDate] = useState(ALL_TIME);
  const [placeholderCompleteDate, setPlaceholderCompleteDate] = useState(ALL_TIME);
  const [selected, setSelected] = useState('All Content');
  const [showClearCompleteDate, setShowClearCompleteDate] = useState(false);
  const [showClearAssignedDate, setShowClearAssignedDate] = useState(false);
  const [openFiltersFlyout, setOpenFiltersFlyout] = useState(false);
  const [availableFilters, setAvailableFilters] = useState(undefined);
  const [sortDir, setSortDir] = useState('desc');
  const [tableData, setTableData] = useState([]);
  const [totalCount, setTotalCount] = useState();
  const [shouldUpdate, setShouldUpdate] = useState(true);
  const [isFilterApplied, setFilterApplied] = useState(false);
  const SelectOption = [
    { id: 'All Content', value: translatr('web.manager-dashboard-v2.main', 'AllContent') },
    { id: 'Assignments', value: translatr('web.manager-dashboard-v2.main', 'Assignments') }
  ];
  const Headers = {
    card_title: (() => {
      const assignmentText =
        translatr('web.manager-dashboard-v2.main', 'Assignment') || 'Assignment';
      const totalCountText = Number.isInteger(totalCount) ? `(${totalCount})` : '';
      return `${assignmentText} ${totalCountText}`;
    })(),
    assigned_by: translatr('web.common.main', 'AssignedBy'),
    assigned_date: translatr('web.manager-dashboard-v2.main', 'AssignedOn'),
    due_at: translatr('web.manager-dashboard-v2.main', 'DueDate'),
    started_at: translatr('web.common.main', 'StartedOn'),
    completed_at: translatr('web.common.main', 'CompletedOn')
  };
  const TableHeaders = Object.keys(Headers).map(header => {
    return {
      label: Headers[header],
      id: header,
      align: 'text-left',
      sortable: true,
      onClick: getSortedData
    };
  });
  let searchRef = useRef();
  let assignorDataRef = useRef();
  let dataRef = useRef();

  useEffect(() => {
    const updatedFilters = { assignors: assignorDataRef.current };
    if (assignorDataRef.current?.length < 1) {
      setAvailableFilters(undefined);
      return;
    }
    setAvailableFilters(updatedFilters);
  }, [assignorDataRef.current]);

  useEffect(() => {
    const myPage = !currentPage ? 1 : currentPage;
    if (selected === 'Assignments') {
      fetchUserAssignments(myPage);
    } else {
      fetchUserAllContent(myPage);
    }
    setFirst(false);
  }, [shouldUpdate]);

  useEffect(() => {
    if (isFirst) {
      return;
    }
    const myPage = !currentPage ? 1 : currentPage;
    if (selected === 'Assignments') {
      assignorDataRef.current = assignamentData?.assignors?.data || [];
      fetchUserAssignments(myPage);
    } else {
      assignorDataRef.current = allData?.assignors?.data || [];
      fetchUserAllContent(myPage);
    }
  }, [selected]);

  useEffect(() => {
    if (selected === 'Assignments') {
      getTableData(assignamentData.data);
    }
  }, [assignamentData]);

  useEffect(() => {
    if (selected !== 'Assignments') {
      getTableData(allData.data);
    }
  }, [allData]);

  const fetchUserAssignments = async (page = 1) => {
    if (!shouldUpdate && assignamentData.updated) {
      setAssignamentData({ ...assignamentData });
      setTotalCount(assignamentData.totalCount || 0);
      setCurrentPage(assignamentData.page || page);
      dataRef.current = assignamentData;
      assignorDataRef.current = assignamentData.assignors.data.slice(
        0,
        assignamentData.assignors.page * ASSIGNOR_OFFSET_AMOUNT
      );
      return;
    }

    let payload = getPayload(page);
    payload.learning_source = 'assignments';
    setLoading(true);
    const resp = await getUserLearnings(activeUser, payload).catch(err => {
      console.error(`Error on Learning history getUserLearning.func: ${err}`);
      const theAssignors = { data: [], totalCount: 0, page: 1 };
      const theData = {
        data: [],
        assignors: theAssignors,
        totalCount: 0,
        page: 0,
        updated: true
      };
      assignorDataRef.current = [];
      dataRef.current = theData;

      setAssignamentData(theData);
      setTotalCount(0);
      setCurrentPage(page);
      shouldUpdate && setAllData({ ...allData, updated: false });
      setShouldUpdate(false);
      setLoading(false);
    });
    if (resp) {
      const countByAssignor = initAssignors(resp.count_by_assignor || []);
      const theAssignors = {
        data: countByAssignor,
        totalCount: countByAssignor.length,
        page: 1
      };
      const theData = {
        data: resp.data?.filter(item => !!item.card_title?.length) || [],
        assignors: theAssignors,
        totalCount: resp.total_count || 0,
        page,
        updated: true
      };

      dataRef.current = theData;
      assignorDataRef.current = countByAssignor.slice(0, ASSIGNOR_OFFSET_AMOUNT);
      setAssignamentData(theData);
      setTotalCount(resp.total_count || 0);
      setCurrentPage(page);
      shouldUpdate && setAllData({ ...allData, updated: false });
      setShouldUpdate(false);
      countByAssignor.length > ASSIGNOR_OFFSET_AMOUNT;
      setLoading(false);
    }
  };

  const fetchUserAllContent = async (page = 1) => {
    if (!shouldUpdate && allData.updated) {
      setAllData({ ...allData });
      setCurrentPage(allData.page || page);
      setTotalCount(allData.totalCount || 0);
      dataRef.current = allData;
      assignorDataRef.current = allData.assignors.data.slice(
        0,
        allData.assignors.page * ASSIGNOR_OFFSET_AMOUNT
      );
      return;
    }
    let payload = getPayload(page);
    payload.learning_source = 'all';
    setLoading(true);
    const resp = await getUserLearnings(activeUser, payload).catch(err => {
      console.error('Individual Learning history getUserAllLearnings.func: ', err);
      const theAssignors = { data: [], totalCount: 0, page: 1 };
      const theData = {
        data: [],
        assignors: theAssignors,
        totalCount: 0,
        updated: true
      };

      dataRef.current = theData;
      assignorDataRef.current = [];
      setAllData(theData);
      setTotalCount(0);
      setCurrentPage(page);
      shouldUpdate && setAssignamentData({ ...assignamentData, updated: false });
      setShouldUpdate(false);
      setLoading(false);
    });

    if (resp) {
      const countByAssignor = initAssignors(resp.count_by_assignor || []);
      const theAssinors = {
        data: countByAssignor,
        totalCount: countByAssignor.length,
        page: 1
      };
      const theData = {
        data: resp.data?.filter(item => !!item.card_title?.length) || [],
        totalCount: resp.total_count || 0,
        assignors: theAssinors,
        page,
        updated: true
      };

      dataRef.current = theData;
      assignorDataRef.current = countByAssignor.slice(0, ASSIGNOR_OFFSET_AMOUNT);
      setAllData(theData);
      setTotalCount(resp.total_count || 0);
      setCurrentPage(page);
      shouldUpdate && setAssignamentData({ ...assignamentData, updated: false });
      setShouldUpdate(false);
      countByAssignor.length > ASSIGNOR_OFFSET_AMOUNT;
      setLoading(false);
    }
  };

  const getPayload = (page = 1) => {
    const pageToRequest = page - 1;
    let payload = {
      offset: pageToRequest * pageSize.value,
      limit: pageSize.value,
      sort_field: 'card_title',
      sort_order: 'desc'
    };

    if (searchRef.current) {
      payload.q = searchRef.current;
    }
    if (completeDate?.fromDate && completeDate?.toDate) {
      payload['completed_at[]'] = [
        getMMDDYYYStr(completeDate.fromDate),
        getMMDDYYYStr(completeDate.toDate)
      ];
    }
    if (selected === 'Assignments') {
      if (assignedDate?.fromDate && assignedDate?.toDate) {
        payload['assigned_date[]'] = [
          getMMDDYYYStr(assignedDate.fromDate),
          getMMDDYYYStr(assignedDate.toDate)
        ];
      }

      if (availableFilters?.assignors) {
        const ids = [...availableFilters?.assignors]
          .filter(item => item.checked)
          .map(item => item.value);
        payload['assignor_id[]'] = ids;
      }
    }

    return payload;
  };

  const initAssignors = data => {
    if (!data?.length) {
      return [];
    }
    const theList = [...(assignorDataRef.current || [])];
    const theAssignorList = data
      .filter(item => item?.length > 2)
      .map(item => {
        const ischecked = theList.find(d => d.value === item[1])?.checked || false;
        return {
          value: item[1],
          label: `${item[0]} (${item[2]})`,
          checked: ischecked,
          onChange: onAssinedByCheckboxChange
        };
      });

    let prevData = {};
    theAssignorList.forEach(item => {
      prevData[item.value] = item.checked;
    });
    return theAssignorList;
  };

  const getTableData = data => {
    if (!Array.isArray(data)) {
      setTableData([]);
      return;
    }
    const date_keys = ['assigned_date', 'due_at', 'started_at', 'completed_at'];
    const TableData = data.map((item, i) => {
      let row = [];
      Object.keys(Headers).forEach((k, j) => {
        const obj = { ...item };
        if (date_keys.includes(k)) {
          obj[k] = !item[k]
            ? '-'
            : moment(getMMDDYYY(item[k]), MONTH_DAY_YEAR_FORMAT).format(
                DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
              );
        }
        row.push({
          children: rowChildrenSelector(obj, k),
          id: `${k}-${i}-${j}`,
          align: 'text-left'
        });
      });
      return row;
    });
    setTableData(TableData);
  };

  function rowChildrenSelector(obj, key) {
    if (key === 'card_title') {
      return (
        <Tooltip isHtmlIncluded message={obj[key]} hide={obj[key]?.length < 32} pos="right">
          <div
            dangerouslySetInnerHTML={{
              __html: safeRender(
                obj[key]?.length > 32 ? truncateText(unescape(obj[key]), 32, '...') : obj[key]
              )
            }}
          />
        </Tooltip>
      );
    } else {
      return <span className="short-text ed-text-color">{obj[key] || '-'}</span>;
    }
  }

  function getSortedData(header) {
    const sd = sortDir === 'asc' ? 'desc' : 'asc';
    const data = selected === 'Assignments' ? assignamentData.data : allData.data;
    setSortDir(sd);
    getTableData(sortData({ sortDir: sd, sortKey: header.id, data }));
  }

  function doSearch(query) {
    setFilterApplied(query === '' ? false : true);
    searchRef.current = query;
    setCurrentPage(1);
    setShouldUpdate(true);
  }
  const debouncedSearch = debounce(doSearch, 500);

  const onChange = value => {
    if (selected === value) {
      return;
    }

    let myPage = 1;
    if (value === 'Assignments') {
      myPage = assignamentData.page || 1;
      setCurrentPage(myPage);
      setFilterApplied(true);
      setSelected('Assignments');
    } else {
      myPage = allData.page || 1;
      setCurrentPage(myPage);
      setFilterApplied(false);
      setSelected('All Content');
    }
  };

  const paginate = resp => {
    if (resp.event === 'next') {
      setCurrentPage(currentPage + 1);
      setShouldUpdate(true);
    } else if (resp.event === 'prev') {
      setCurrentPage(currentPage - 1);
      setShouldUpdate(true);
    } else {
      if (Number.isInteger(resp.event)) {
        setCurrentPage(resp.event);
        setShouldUpdate(true);
      }
    }
  };

  function onCompletionDateChange(newDate1, newDate2) {
    const fromDate = newDate1?.toLocalISOString() || null;
    const toDate = newDate2?.toLocalISOString() || null;
    setCompleteDate({ fromDate, toDate });
    setPlaceholderCompleteDate(null);
    setShowClearCompleteDate(true);
    if (fromDate && toDate) {
      setFilterApplied(true);
      setShouldUpdate(true);
    }
  }

  function onAssignedDateChange(newDate1, newDate2) {
    const fromDate = newDate1?.toLocalISOString() || null;
    const toDate = newDate2?.toLocalISOString() || null;
    setAssignedDate({ fromDate, toDate });
    setPlaceholderAssignedDate(null);
    setShowClearAssignedDate(true);
    if (fromDate && toDate) {
      setFilterApplied(true);
      setShouldUpdate(true);
    }
  }

  function onClearAssignedDate() {
    setPlaceholderAssignedDate(ALL_TIME);
    setShowClearAssignedDate(false);
    setAssignedDate({ fromDate: null, toDate: null });
    setFilterApplied(false);
    setShouldUpdate(true);
  }

  function onClearCompleteDate() {
    setPlaceholderCompleteDate(ALL_TIME);
    setShowClearCompleteDate(false);
    setCompleteDate({ fromDate: null, toDate: null });
    setFilterApplied(false);
    setShouldUpdate(true);
  }

  const onAssinedByCheckboxChange = e => {
    const { value, checked } = e.target;
    let theAssignors = [...assignorDataRef.current];
    let findMe = theAssignors?.find(item => item.value == value);
    if (findMe) {
      findMe.checked = checked;
    }
    assignorDataRef.current = theAssignors;

    const myData = { ...dataRef.current };
    const isMore = myData.assignors.totalCount > theAssignors.length;
    const theData = {
      ...myData,
      assignors: {
        ...myData.assignors,
        data: isMore
          ? [...theAssignors, ...myData.assignors.data.slice(theAssignors.length)]
          : [...theAssignors]
      }
    };
    dataRef.current = theData;

    if (selected === 'Assignments') {
      setAssignamentData(theData);
    } else {
      setAllData(theData);
    }
  };

  function handleFilterClear() {
    const updatedFilters = { assignors: assignorDataRef.current };
    setAvailableFilters(updatedFilters);
    setFilterApplied(false);
    if (!availableFilters || availableFilters == []) {
      setShouldUpdate(true);
      setOpenFiltersFlyout(false);
    }
  }

  function handleFilterApply() {
    setFilterApplied(true);
    setShouldUpdate(true);
    setOpenFiltersFlyout(false);
  }

  function handleCheck(e, listId, label) {
    const updatedFilters = availableFilters[listId].map(item => {
      if (item.label === label) {
        return { ...item, checked: e.target.checked };
      }
      return item;
    });
    setAvailableFilters(prev => ({
      ...prev,
      [listId]: updatedFilters
    }));
  }

  const handlePageSizeChange = useCallback(data => {
    if (!data || !data.value) {
      console.warn('Invalid page size data received:', data);
      return;
    }

    // When changing page size, reset to first page and trigger data refresh
    setPageSize(data);
    setCurrentPage(1);
    setShouldUpdate(true);
  }, []);

  const showTable = tableData.length > 0 && !loading;
  const showNoAssignedTraining = !loading && !isFilterApplied && tableData.length < 1;
  const showNoResultFoundInSearch = !loading && isFilterApplied && tableData.length < 1;

  return (
    <>
      <div className="assigned-learning-table__container">
        <div className="assigned-learning-table__header">
          <div className="search-container mr-base mt-2xs">
            <SearchInput
              placeholder={
                translatr('web.manager-dashboard-v2.main', 'SearchAssignments') ||
                'Search assignments'
              }
              onSearch={debouncedSearch}
              searchOnTextChange={true}
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SearchAssignments')}
            />
          </div>
          <div className="mr-base mt-2xs">
            <Select
              title={translatr('web.manager-dashboard-v2.main', 'ContentType') || 'Content type'}
              items={SelectOption}
              onChange={data => onChange(data.value)}
              defaultValue={SelectOption[selected]}
              translateDropDownOptions={false}
            />
          </div>

          <div className="date-picker-container mr-base mt-2xs">
            <div className="date-picker-label-container">
              {_.capitalize(translatr('web.manager-dashboard-v2.main', 'CompletionRange'))}
            </div>
            <DatePicker
              singleDatePicker={false}
              opens="left"
              placeHolder={placeholderCompleteDate}
              onChange={onCompletionDateChange}
              startDate={completeDate.fromDate ? new Date(completeDate.fromDate) : null}
              endDate={completeDate.toDate ? new Date(completeDate.toDate) : null}
              onClear={showClearCompleteDate ? onClearCompleteDate : null}
            />
          </div>

          {selected === 'Assignments' && (
            <div className="date-picker-container mr-base mt-2xs">
              <div className="date-picker-label-container">
                {_.capitalize(translatr('web.manager-dashboard-v2.main', 'DueDateRange'))}
              </div>
              <DatePicker
                singleDatePicker={false}
                opens="left"
                placeHolder={placeholderAssignedDate}
                onChange={onAssignedDateChange}
                startDate={assignedDate.fromDate ? new Date(assignedDate.fromDate) : null}
                endDate={assignedDate.toDate ? new Date(assignedDate.toDate) : null}
                onClear={showClearAssignedDate ? onClearAssignedDate : null}
              />
            </div>
          )}

          {selected === 'Assignments' && (
            <div className="filter-container mr-base mt-2xs">
              {
                <div>
                  <FilterFlyout
                    closeModal={() => setOpenFiltersFlyout(false)}
                    isOpen={openFiltersFlyout}
                    handleFilterClear={handleFilterClear}
                    handleFilterApply={handleFilterApply}
                    availableFilters={availableFilters}
                    handleCheck={handleCheck}
                  />
                </div>
              }
              <button
                className="ed-btn ed-btn-neutral container-icon-download"
                onClick={() => setOpenFiltersFlyout(true)}
                aria-label={translatr('web.manager-dashboard-v2.main', 'OpenFilters')}
              >
                <b>
                  <i className="icon-filter" />
                </b>
                <span>
                  <b className="filter-name">
                    {translatr('web.manager-dashboard-v2.main', 'Filters') || 'Filters'}
                  </b>
                </span>
              </button>
            </div>
          )}
        </div>

        {showTable && (
          <Table
            className={`none-vertical-border ${loading ? 'hide-rows' : ''}`}
            headers={TableHeaders}
            rows={tableData}
          />
        )}
        {showNoResultFoundInSearch && <NoResultFoundInSearch />}
        {showNoAssignedTraining && <NoAssignedTraining />}

        {loading && (
          <div className="ledger-loader text-center">
            <Loading />
          </div>
        )}

        {totalCount > 0 && (
          <div className="pagination-container">
            <div className="supporting-text">
              {translatr('web.manager-dashboard-v2.main', 'RowsPerPage') || 'Rows per page'}
            </div>
            <Select
              id="pagination-select"
              defaultValue={pageSize.value}
              onChange={handlePageSizeChange}
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectLimitOption')}
              items={PAGE_ROW_OPTIONS}
            />
            <Pagination
              key={`pagination-${pageSize.value}`}
              postPerPage={pageSize.value}
              totalPosts={totalCount}
              paginate={paginate}
              activePage={currentPage}
              iconType={true}
            />
          </div>
        )}
      </div>
    </>
  );
};

LearningHistoryTable.propTypes = {
  activeUser: string.isRequired
};

export default LearningHistoryTable;
