@import '~centralized-design-system/src/Styles/_variables.scss';

.flx-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}
.supporting-text {
  font-size: var(--ed-font-size-supporting);
  color: var(--ed-gray-6);
  font-weight: var(--ed-font-weight-normal);
  line-height: var(--ed-line-height-xs);
}
.assigned-learning-widget {
  padding: var(--ed-spacing-base);
  .assigned-learning-widget__title {
    h3 {
      font-weight: 900;
      font-size: var(--ed-font-size-lg) !important;
      letter-spacing: 0%;
      vertical-align: middle;
    }
  }
  .assigned-learning-widget__stats {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: var(--ed-spacing-base);
    margin-bottom: var(--ed-spacing-base);
    border: 1px solid var(--ed-gray-2);
    border-radius: var(--ed-border-radius-md);
    padding: var(--ed-spacing-base);
    .assigned-learning-widget__stats__item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      padding: var(--ed-spacing-2xs);
      width: -webkit-fill-available;
      .assigned-learning-widget__stats__item__title {
        font-weight: 400;
        font-size: var(--ed-font-size-sm) !important;
        color: var(--ed-gray-5);
        letter-spacing: 0%;
        vertical-align: middle;
        margin-bottom: var(--ed-spacing-2xs);
      }
      .assigned-learning-widget__stats__item__content {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: var(--ed-font-size-sm) !important;
        letter-spacing: 0%;
        vertical-align: middle;

        .icon-oval-fill::before {
          display: flex;
          align-items: center;
          justify-content: center;
          padding-right: var(--ed-spacing-xs);
          font-size: var(--ed-font-size-2xs);
        }
        h3 {
          font-weight: 900;
          font-size: var(--ed-font-size-lg) !important;
          letter-spacing: 0%;
          vertical-align: middle;
          margin-bottom: 0;
        }
      }
    }
  }
  .assigned-learning-tabs__wrapper {
    .tab-bar.block {
      box-shadow: none !important;
      border-radius: unset !important;
      .tabs {
        margin-left: unset !important;
      }
    }
  }
  .assigned-learning-table__container {
    margin-top: var(--ed-spacing-base);
    .assigned-learning-table__header {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: end;
      margin-bottom: var(--ed-spacing-xl);
      .search-container {
        margin-right: var(--ed-spacing-base);
        .ed-input-container .input-group .input-field {
          border: 1px solid var(--ed-gray-5);
          border-radius: var(--ed-border-radius-md);
          min-height: rem-calc(40);
        }
      }
      .ed-btn-neutral,
      .ed-select {
        border: 1px solid var(--ed-gray-5);
        border-radius: var(--ed-border-radius-md);
        min-height: rem-calc(40);
      }
      .mr-base {
        margin-right: var(--ed-spacing-base);
      }
      .mt-2xs {
        margin-top: var(--ed-spacing-2xs);
      }
      .date-picker-container {
        min-height: rem-calc(71);
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        .date-picker-label-container {
          margin-bottom: var(--ed-spacing-2xs);
        }
      }
      .react-datepicker-wrapper {
        min-width: rem-calc(200);
        min-height: rem-calc(40);
        border: 1px solid var(--ed-gray-5);
        border-radius: var(--ed-border-radius-md);
      }
      .download-report_button {
        min-height: rem-calc(40);
        border: 1px solid var(--ed-gray-5);
        border-radius: var(--ed-border-radius-md);
        color: var(--ed-gray-7);
        font-weight: 700;
        font-size: calc(var(--ed-button-font-size) - 1px);
        vertical-align: middle;
      }
    }
    .ed-table-wrapper {
      border-bottom: unset !important;
      border: 1px solid var(--ed-gray-2);
      border-radius: var(--ed-border-radius-md);
      .text-left {
        min-height: rem-calc(40);
      }
      .short-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .half-cell {
        min-height: fit-content;
        height: fit-content;
        padding-left: 0.1875rem;
      }

      .double-cell-wrapper {
        display: block;
        @extend .short-text;
      }
    }
    .ed-table-wrapper td:first-child > div,
    .ed-table-wrapper th:first-child > div {
      border-right: unset !important;
    }
    .ed-table-wrapper td > div,
    .ed-table-wrapper th div {
      border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
    }

    .pagination-container {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-end;
      padding: var(--ed-spacing-2xs) var(--ed-spacing-2xs) var(--ed-spacing-2xs) 0;
      border: var(--ed-input-border-size) solid var(--ed-gray-2);
      border-top: none !important;
      border-bottom-left-radius: var(--ed-border-radius-md);
      border-bottom-right-radius: var(--ed-border-radius-md);
      gap: var(--ed-spacing-sm);
      .pagination ul {
        margin-left: 0 !important;
      }
      .pagination ul li button {
        border: var(--ed-pagination-border-size) solid var(--ed-gray-2) !important;
      }
    }
  }
  .no-result-container {
    padding: rem-calc(40) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
