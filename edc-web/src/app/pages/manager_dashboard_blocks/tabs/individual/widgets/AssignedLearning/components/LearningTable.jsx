import { useState, useEffect, useRef, useMemo } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  downloadIndividualReport,
  getUserLearning
} from 'edc-web-sdk/requests/managerDashboard.v2';
import DatePicker from 'centralized-design-system/src/DatePickers';
import { Button } from 'centralized-design-system/src/Buttons';
import Table from 'centralized-design-system/src/Table';
import { Select, SearchInput } from 'centralized-design-system/src/Inputs';
import Pagination from 'centralized-design-system/src/Pagination';
import Loading from 'centralized-design-system/src/Loading';
import Tooltip from 'centralized-design-system/src/Tooltip';
import _, { debounce } from 'lodash';
import unescape from 'lodash/unescape';
import moment from 'moment';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { truncateText } from '@utils/utils';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { processIndividualAssignments } from '../utils/tableUtil';
import { getMMDDYYYStr } from '../utils/utils';
import { DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT, MONTH_DAY_YEAR_FORMAT } from '@utils/constants';
import FilterFlyout from './FilterFlyout';
import NoResultFoundInSearch from './NoResultFoundInSearch';
import NoAssignedTraining from './NoAssignedTraining';
import { connect } from 'react-redux';
import { PAGE_ROW_OPTIONS } from '../../../../learning/helpers';
const ASSIGNOR_OFFSET_AMOUNT = 8;
const ALL_TIME = translatr('web.common.main', 'AllTime');
const STATUS_KEY = translatr('web.manager-dashboard-v2.main', 'Status1');
const ASSIGNED_BY_KEY = translatr('web.common.main', 'AssignedBy');

const LearningTable = ({ activeUser, sendReminderModalHandler, toast }) => {
  const [assignedDate, _setAssignedDate] = useState({});
  const [assignments, setAssignments] = useState();
  const [assignmentStatus, setAssignmentStatus] = useState([]);
  const [pageSize, setPageSize] = useState(PAGE_ROW_OPTIONS[0]);
  const [currentPage, setCurrentPage] = useState(0);
  const [dueDate, _setDueDate] = useState({});
  const [loading, setLoading] = useState(true);
  const [openFiltersFlyout, setOpenFiltersFlyout] = useState(false);
  const [availableFilters, setAvailableFilters] = useState(undefined);
  const [placeholderAssignedDate, setPlaceholderAssignedDate] = useState(ALL_TIME);
  const [placeholderDueDate, setPlaceholderDueDate] = useState(ALL_TIME);
  const [showClearDueDate, setShowClearDueDate] = useState(false);
  const [showClearAssignedDate, setShowClearAssignedDate] = useState(false);
  const [sortDir, setSortDir] = useState('desc');
  const [sortKey, setSortKey] = useState('card_title');
  const [tableData, setTableData] = useState([]);
  const [totalCount, setTotalCount] = useState();
  const [shouldUpdate, setShouldUpdate] = useState(false);
  const [isFilterApplied, setFilterApplied] = useState(false);

  const searchRef = useRef();
  const assignmentStatusRef = useRef();
  const assignorsListRef = useRef();
  const assignorsDataRef = useRef();
  const dueDateRef = useRef(dueDate);
  const assignedDateRef = useRef(assignedDate);

  const setAssignedDate = data => {
    assignedDateRef.current = { ...data };
    _setAssignedDate(data);
  };
  const setDueDate = data => {
    dueDateRef.current = { ...data };
    _setDueDate(data);
  };

  const SortMap = {
    card_title: 'card_title',
    assigned_by: 'assigned_by',
    assigned_on: 'assigned_date',
    due_at: 'due_at',
    completion_status: 'status'
  };

  const SortKeyMap = {
    card_title: 'card_title',
    assigned_by: 'assigned_by',
    assigned_date: 'assigned_on',
    due_at: 'due_at',
    status: 'completion_status'
  };

  const Headers = {
    card_title: (() => {
      const assignmentText =
        translatr('web.manager-dashboard-v2.main', 'Assignment') || 'Assignment';
      const totalCountText = Number.isInteger(totalCount) ? `(${totalCount})` : '';
      return `${assignmentText} ${totalCountText}`;
    })(),
    assigned_by: translatr('web.common.main', 'AssignedBy'),
    assigned_on: translatr('web.manager-dashboard-v2.main', 'AssignedOn'),
    due_at: translatr('web.manager-dashboard-v2.main', 'DueDate'),
    completion_status: translatr('web.common.main', 'StatusSkill'),
    send_reminder: translatr('web.manager-dashboard-v2.main', 'SendReminder')
  };
  const StatusDropdownItems = {
    completed: translatr('web.manager-dashboard-v2.main', 'Completed'),
    started: translatr('web.manager-dashboard-v2.main', 'InProgress'),
    assigned: translatr('web.manager-dashboard-v2.main', 'NotStarted')
  };
  const TableHeaders = Object.keys(Headers).map(header => {
    if (['completion_status', 'send_reminder'].includes(header)) {
      return {
        label: header === 'send_reminder' ? ' ' : Headers[header],
        id: header,
        align: 'text-left',
        sortable: false
      };
    }
    return {
      label: Headers[header] || '-',
      id: header,
      align: 'text-left',
      sortable: true,
      onClick: getSortedData
    };
  });

  function getSortedData(header) {
    const sd = header.id !== SortKeyMap[sortKey] ? 'desc' : sortDir === 'asc' ? 'desc' : 'asc';
    setSortDir(sd);
    setSortKey(SortMap[header.id]);
    getAssignments(1, activeUser, SortMap[header.id], sd);
  }

  function doSearch(query) {
    setFilterApplied(query === '' ? false : true);
    searchRef.current = query;
    getAssignments(1, activeUser);
  }

  const debouncedSearch = useMemo(() => debounce(doSearch, 500), []);

  const getPayload = (page, activeUserId = activeUser, sort_field, sort_direction) => {
    let payload = {
      user_id: activeUserId,
      sort_field,
      sort_direction
    };
    const theDueDate = dueDateRef.current || dueDate;
    const theAssignedDate = assignedDateRef.current || assignedDate;
    if (theDueDate?.fromDate && theDueDate?.toDate) {
      payload['due_at[]'] = [getMMDDYYYStr(theDueDate.fromDate), getMMDDYYYStr(theDueDate.toDate)];
    }

    if (theAssignedDate?.fromDate && theAssignedDate?.toDate) {
      payload['assigned_date[]'] = [
        getMMDDYYYStr(theAssignedDate.fromDate),
        getMMDDYYYStr(theAssignedDate.toDate)
      ];
    }

    if (page) {
      payload.offset = (page - 1) * pageSize.value;
      payload.limit = pageSize.value;
    }

    if (searchRef.current) {
      payload.q = searchRef.current;
    }

    if (availableFilters?.assigment_status) {
      const theStatus = [...(availableFilters?.assigment_status || [])]
        .filter(item => item.checked)
        .map(item => item.value);
      payload['status[]'] = theStatus;
    }

    if (availableFilters?.assignors) {
      const theIds = [...(availableFilters?.assignors || [])]
        .filter(item => item.checked)
        .map(item => item.value);
      if (theIds.length > 0) {
        payload['assignor_id[]'] = theIds;
      }
    }
    return payload;
  };

  async function getAssignments(
    page = 1,
    activeUserId = activeUser,
    sort_field = sortKey,
    sort_direction = sortDir
  ) {
    setLoading(true);
    const payload = getPayload(page, activeUserId, sort_field, sort_direction);
    let userAssignments = await getUserLearning(payload).catch(err => {
      const theAssignors = { data: [], totalCount: 0, page: 1 };
      assignorsListRef.current = [];
      assignorsDataRef.current = theAssignors;
      assignmentStatusRef.current = [];
      setAssignmentStatus([]);
      setTotalCount(0);
      setShouldUpdate(false);
      setLoading(false);
      console.error('Individual overview init.getUserLearningMatrix.func', err);
    });

    if (userAssignments) {
      initStatus(userAssignments.data);
      const countByAssignor = initAssignorsOpts(userAssignments.count_by_assignor || []);
      const theAssignors = {
        data: countByAssignor,
        totalCount: countByAssignor.length,
        page: 1
      };
      assignorsListRef.current = [...countByAssignor].slice(0, ASSIGNOR_OFFSET_AMOUNT);
      assignorsDataRef.current = theAssignors;
      countByAssignor?.length > ASSIGNOR_OFFSET_AMOUNT;

      setAssignments(processIndividualAssignments(userAssignments.data || []));
      setTotalCount(userAssignments.total_count);
      setCurrentPage(page);
      setLoading(false);
    }
  }

  function renderStatus(obj) {
    if (obj['completion_percentage'] === 100 || obj['state'] === 'completed')
      return (
        <div className="double-cell-wrapper">
          <div className="ed-primary-color half-cell">
            {translatr('web.manager-dashboard-v2.main', 'Completed')}
          </div>
          <div className="ed-supporting-text ed-text-size-14 half-cell">{`${translatr(
            'web.manager-dashboard-v2.main',
            'On'
          )} ${moment(obj.completed_on, MONTH_DAY_YEAR_FORMAT).format(
            DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
          )}`}</div>
        </div>
      );

    if (obj['completion_percentage'] < 100 && obj['completion_percentage'] > 0) {
      return <div className="ed-text-color">{`${obj['completion_percentage']}%`}</div>;
    }

    if (obj['state'] === 'started') {
      return (
        <div className="ed-text-color">{translatr('web.manager-dashboard-v2.main', 'Started')}</div>
      );
    }

    return (
      <div className="ed-supporting-text">
        {translatr('web.manager-dashboard-v2.main', 'NotStarted')}
      </div>
    );
  }

  function renderDueAt(obj) {
    const nowDate = moment();
    const theDueDate = !!obj['due_at'] ? moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT) : null;
    const diff = !!theDueDate ? theDueDate.diff(nowDate, 'days') : null;

    if (Number.isInteger(diff)) {
      if (diff <= 14 && diff >= 0 && obj['completion_percentage'] != 100) {
        return (
          <span className="ed-text-color-warning">
            <i className="icon-hourglass-half" />
            <span>
              {moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT).format(
                DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
              )}
            </span>
          </span>
        );
      }
      if (diff < 0 && obj['completion_percentage'] != 100) {
        return (
          <span className="ed-text-color-error">
            <i className="icon-hourglass-end" />
            <span>
              {moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT).format(
                DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
              )}
            </span>
          </span>
        );
      }
      return (
        <span className="ed-text-color">
          <i className="icon-hourglass-start" />
          <span>
            {moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT).format(
              DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
            )}
          </span>
        </span>
      );
    }
    return <div className="ed-supporting-text ed-text-size-14">-</div>;
  }

  function rowChildrenSelector(obj, key) {
    switch (key) {
      case 'card_title':
        return (
          <Tooltip isHtmlIncluded message={obj[key]} hide={obj[key]?.length < 32} pos="right">
            <div
              dangerouslySetInnerHTML={{
                __html: safeRender(
                  obj[key]?.length > 32 ? truncateText(unescape(obj[key]), 32, '...') : obj[key]
                )
              }}
            />
          </Tooltip>
        );
      case 'completion_status':
        return renderStatus(obj);
      case 'due_at':
        return renderDueAt(obj);
      case 'send_reminder':
        return obj.last_reminder_date &&
          obj['completion_percentage'] != 100 &&
          obj['state'] != 'completed' ? (
          <div className="double-cell-wrapper">
            <Button
              color="primary"
              variant="borderless"
              size="medium"
              padding="xsmall"
              onClick={e => sendReminderModalHandler(e, obj)}
            >
              {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
            </Button>
            <div className="ed-supporting-text ed-text-size-14 half-cell">{`${translatr(
              'web.manager-dashboard-v2.main',
              'LastSent'
            )}: ${moment(obj.last_reminder_date, MONTH_DAY_YEAR_FORMAT).format(
              DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
            )}`}</div>
          </div>
        ) : obj['completion_percentage'] === 100 || obj['state'] === 'completed' ? (
          <Tooltip
            customClass="pmb-unset"
            message={translatr('web.manager-dashboard-v2.main', 'Completed')}
            isHtmlIncluded
            pos="left"
          >
            <Button color="primary" variant="borderless" padding="xsmall" disabled>
              {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
            </Button>
          </Tooltip>
        ) : (
          <Button
            color="primary"
            variant="borderless"
            padding="xsmall"
            onClick={e => sendReminderModalHandler(e, obj)}
          >
            {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
          </Button>
        );
      default:
        return <span className="short-text ed-text-color">{obj[key]}</span>;
    }
  }

  useEffect(() => {
    setDueDate({ fromDate: null, toDate: null });
    setAssignedDate({ fromDate: null, toDate: null });
    getAssignments();
  }, []);

  useEffect(() => {
    const updatedFilters = {
      [STATUS_KEY]: assignmentStatus,
      [ASSIGNED_BY_KEY]: assignorsListRef.current
    };
    if (assignmentStatus?.length < 1 && assignorsListRef.current?.length < 1) {
      setAvailableFilters(undefined);
      return;
    }
    setAvailableFilters(updatedFilters);
  }, [assignmentStatus, assignorsListRef.current]);

  useEffect(() => {
    getTableData(assignments);
  }, [assignments]);

  useEffect(() => {
    if (!shouldUpdate) {
      return;
    }
    getAssignments();
    setShouldUpdate(false);
  }, [shouldUpdate]);

  useEffect(() => {
    setCurrentPage(1);
    getAssignments(1, activeUser);
  }, [pageSize]);

  const initStatus = data => {
    if (!data?.length) {
      assignmentStatusRef.current = [];
      setAssignmentStatus([]);
      return;
    }

    let theKeys = [];
    data.forEach(item => {
      if (!theKeys.includes(item.state)) {
        theKeys.push(item.state);
      }
    });
    let prevData = {};
    const theStatus = theKeys.map(k => {
      const isChecked =
        [...(assignmentStatusRef.current || [])].find(item => item.value === k)?.checked || false;
      prevData[k] = isChecked;

      return {
        value: k,
        label: StatusDropdownItems[k],
        checked: isChecked
      };
    });
    assignmentStatusRef.current = [...theStatus];
    setAssignmentStatus(theStatus);
  };

  const initAssignorsOpts = data => {
    if (!data?.length) {
      return [];
    }
    const retData = data
      .filter(item => item?.length > 2)
      .map(item => {
        const ischecked =
          [...(assignorsListRef?.current || [])].find(d => d.value === item[1])?.checked || false;
        return {
          value: item[1],
          label: `${item[0]} (${item[2]})`,
          checked: ischecked
        };
      });

    let prevData = {};
    retData.forEach(item => {
      prevData[item.value] = item.checked;
    });
    return retData;
  };

  const getTableData = data => {
    if (!Array.isArray(data)) {
      setTableData([]);
      return;
    }

    const translateMonth = str => {
      return str.replace(/\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b/g, match =>
        translatr('web.common.main', match)
      );
    };

    const TableData = assignments.map((assignment, index) => {
      let row = [];
      Object.keys(Headers).forEach((key, i) => {
        let obj = {
          ...assignment
        };
        if (key === 'assigned_on') {
          const assignedOnDate = moment(assignment[key], MONTH_DAY_YEAR_FORMAT).format(
            DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
          );
          obj.assigned_on = !!assignment[key] ? translateMonth(assignedOnDate) : null;
        }
        row.push({
          children: rowChildrenSelector(obj, key),
          id: `${key}-${index}-${i}`,
          align: 'text-left'
        });
      });
      return row;
    });
    setTableData(TableData);
  };

  const paginate = resp => {
    let activePage = currentPage;
    if (resp.event === 'next') {
      getAssignments(activePage + 1, activeUser);
    } else if (resp.event === 'prev') {
      getAssignments(activePage - 1, activeUser);
    } else {
      if (Number.isInteger(resp.event)) {
        getAssignments(resp.event, activeUser);
      }
    }
  };

  async function downloadReport(e) {
    let el = e.target;
    el.disabled = true;
    const payload = getPayload();
    const resp = await downloadIndividualReport(activeUser, payload).catch(err => {
      console.error(`Fail to download report downloadIndividualReport:func ${err.message || err}`);
    });

    toast(resp?.message);

    el.removeAttribute('disabled');
    if (resp?.file_url) {
      let a = document.createElement('a');
      a.href = resp.file_url;
      a.setAttribute('download', `Individual-Report-${activeUser}.csv`);
      a.click();
    }
  }

  function onAssignedDateChange(newDate1, newDate2) {
    const fromDate = newDate1?.toLocalISOString() || null;
    const toDate = newDate2?.toLocalISOString() || null;
    setAssignedDate({ fromDate, toDate });
    setPlaceholderAssignedDate(null);
    setShowClearAssignedDate(true);
    if (fromDate && toDate) {
      setFilterApplied(true);
      setShouldUpdate(true);
    }
  }

  function onDueDateChange(newDate1, newDate2) {
    const fromDate = newDate1?.toLocalISOString() || null;
    const toDate = newDate2?.toLocalISOString() || null;
    setDueDate({ fromDate, toDate });
    setPlaceholderDueDate(null);
    setShowClearDueDate(true);
    if (fromDate && toDate) {
      setFilterApplied(true);
      setShouldUpdate(true);
    }
  }

  function onClearAssignedDate() {
    setPlaceholderAssignedDate(ALL_TIME);
    setShowClearAssignedDate(false);
    setAssignedDate({ fromDate: null, toDate: null });
    setFilterApplied(false);
    setShouldUpdate(true);
  }

  function onClearDueDate() {
    setPlaceholderDueDate(ALL_TIME);
    setShowClearDueDate(false);
    setDueDate({ fromDate: null, toDate: null });
    setFilterApplied(false);
    setShouldUpdate(true);
  }

  function handleFilterClear(key = 'all') {
    let updatedFilters = {};
    if (key === 'all') {
      updatedFilters = { assigment_status: assignmentStatus, assignors: assignorsListRef.current };
    } else {
      updatedFilters = {
        ...availableFilters,
        [key]: availableFilters[key].map(item => ({ ...item, checked: false }))
      };
    }
    setAvailableFilters(updatedFilters);
    setFilterApplied(false);
    if (!availableFilters || availableFilters == []) {
      setShouldUpdate(true);
      setOpenFiltersFlyout(false);
    }
  }

  function handleFilterApply() {
    setFilterApplied(true);
    setShouldUpdate(true);
    setOpenFiltersFlyout(false);
  }

  function handleCheck(e, listId, label) {
    const updatedFilters = availableFilters[listId].map(item => {
      if (item.label === label) {
        return { ...item, checked: e.target.checked };
      }
      return item;
    });
    setAvailableFilters(prev => ({
      ...prev,
      [listId]: updatedFilters
    }));
  }

  const showTable = tableData.length > 0 && !loading;
  const showNoAssignedTraining = !loading && !isFilterApplied && tableData.length < 1;
  const showNoResultFoundInSearch = !loading && isFilterApplied && tableData.length < 1;

  return (
    <>
      <div className="assigned-learning-table__container">
        <div className="assigned-learning-table__header">
          <div className="search-container mt-2xs">
            <SearchInput
              placeholder={
                translatr('web.manager-dashboard-v2.main', 'SearchAssignments') ||
                'Search assignments'
              }
              onSearch={debouncedSearch}
              searchOnTextChange={true}
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SearchAssignments')}
            />
          </div>
          <div className="filter-container mr-base mt-2xs">
            {
              <div>
                <FilterFlyout
                  closeModal={() => setOpenFiltersFlyout(false)}
                  isOpen={openFiltersFlyout}
                  handleFilterClear={handleFilterClear}
                  handleFilterApply={handleFilterApply}
                  availableFilters={availableFilters}
                  handleCheck={handleCheck}
                />
              </div>
            }
            <button
              className="ed-btn ed-btn-neutral container-icon-download"
              onClick={() => setOpenFiltersFlyout(true)}
              aria-label={translatr('web.manager-dashboard-v2.main', 'OpenFilters')}
            >
              <b>
                <i className="icon-filter" />
              </b>
              <span>
                <b className="filter-name">
                  {translatr('web.manager-dashboard-v2.main', 'Filters') || 'Filters'}
                </b>
              </span>
            </button>
          </div>
          <div className="date-picker-container mr-base mt-2xs">
            <div className="date-picker-label-container">
              {_.capitalize(translatr('web.manager-dashboard-v2.main', 'DueDateRange'))}
            </div>
            <DatePicker
              singleDatePicker={false}
              opens="right"
              placeHolder={placeholderDueDate}
              onChange={onDueDateChange}
              startDate={dueDate.fromDate ? new Date(dueDate.fromDate) : null}
              endDate={dueDate.toDate ? new Date(dueDate.toDate) : null}
              onClear={showClearDueDate ? onClearDueDate : null}
            />
          </div>
          <div className="date-picker-container mr-base mt-2xs">
            <div className="date-picker-label-container">
              {translatr('web.manager-dashboard-v2.main', 'AssignedOnDateRange') ||
                'Assigned on date range'}
            </div>
            <DatePicker
              singleDatePicker={false}
              opens="left"
              placeHolder={placeholderAssignedDate}
              onChange={onAssignedDateChange}
              startDate={assignedDate.fromDate ? new Date(assignedDate.fromDate) : null}
              endDate={assignedDate.toDate ? new Date(assignedDate.toDate) : null}
              onClear={showClearAssignedDate ? onClearAssignedDate : null}
            />
          </div>
          <Button
            color="secondary"
            variant="ghost"
            onClick={downloadReport}
            disabled={totalCount < 1}
          >
            {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
          </Button>
        </div>

        {showTable && (
          <Table
            className={`none-vertical-border assigned-learning-table__content ${
              loading ? 'hide-rows' : ''
            }`}
            headers={TableHeaders}
            rows={tableData}
          />
        )}

        {showNoResultFoundInSearch && <NoResultFoundInSearch />}
        {showNoAssignedTraining && <NoAssignedTraining />}

        {loading && (
          <div className="table-loader">
            <Loading />
          </div>
        )}

        {totalCount > 0 && (
          <div className="pagination-container">
            <div className="supporting-text">
              {translatr('web.manager-dashboard-v2.main', 'RowsPerPage') || 'Rows per page'}
            </div>
            <Select
              id="pagination-select"
              defaultValue={pageSize.value}
              onChange={setPageSize}
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectLimitOption')}
              items={PAGE_ROW_OPTIONS}
            />
            <Pagination
              key={`pagination-${pageSize.value}`}
              postPerPage={pageSize.value}
              totalPosts={totalCount}
              paginate={paginate}
              activePage={currentPage}
              iconType={true}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default connect(null, dispatch => ({
  toast: (message, type = 'success') => dispatch(openSnackBar(message, type))
}))(LearningTable);

LearningTable.propTypes = {
  activeUser: PropTypes.string,
  sendReminderModalHandler: PropTypes.func,
  toast: PropTypes.func
};
