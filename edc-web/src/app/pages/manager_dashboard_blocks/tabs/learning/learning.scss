@import '~centralized-design-system/src/Styles/_variables.scss';
.learning-container {
  display: flex;
  flex-direction: column;
  min-width: rem-calc(1200);
  min-height: rem-calc(250);
  overflow-x: auto;
  padding: var(--ed-spacing-base);
  background-color: var(--ed-card-bg-color);
  border-radius: var(--ed-card-border-radius);
  box-sizing: border-box;
  box-shadow: var(--ed-shadow-sm);
  gap: var(--ed-spacing-base);

  .md-learning-empty-container {
    display: flex;
    align-items: center;
    gap: var(--ed-spacing-xl);
    flex-direction: column;
    width: 100%;
    height: rem-calc(101);
    > label {
      margin-bottom: 0 !important;
      &:first-of-type {
        font-weight: var(--ed-font-weight-bold);
        font-size: var(--ed-font-size-base);
      }
    }
  }

  .md-team-learning-container {
    display: flex;
    gap: var(--ed-spacing-base);
    flex-direction: column;
    width: 100%;

    .md-team-learning-actions {
      display: flex;
      gap: var(--ed-spacing-base);
      justify-content: start;

      > div {
        &:first-of-type {
          display: flex;
          flex-direction: column;
          .ed-input-container {
            margin-top: auto;
            width: rem-calc(288);
          }
        }
        &:last-of-type {
          display: flex;
          flex-direction: column;

          > button {
            margin-top: auto;
          }
        }
      }
    }

    .md-team-learning-table {
      border: var(--ed-border-size-sm) solid var(--ed-border-color-light);
      border-bottom: none;
      border-top-left-radius: var(--ed-border-radius-md);
      border-top-right-radius: var(--ed-border-radius-md);
      .md-learning-headers {
        display: flex;
        width: 100%;
        height: rem-calc(58);
        align-items: center;
        border-bottom: var(--ed-border-size-sm) solid var(--ed-border-color-light);
        > div {
          font-size: var(--ed-font-size-supporting);
          font-weight: var(--ed-font-weight-black);
          color: var(--ed-gray-7);
          flex: 1;
          &:nth-last-of-type(2) {
            flex: 0 0 rem-calc(58);
            text-align: right;
            margin-right: var(--ed-spacing-2xs);
            padding-right: var(--ed-spacing-2xs);
          }
          &:last-of-type {
            flex: 0 0 rem-calc(30);
            padding-right: var(--ed-spacing-base);
          }
        }
      }
      .md-learning-list {
        width: 100%;
        .md-learning-list-item {
          display: flex;
          width: 100%;
          padding: var(--ed-spacing-base) 0;
          align-items: center;
        }
        .md-learning-list-item-name {
          gap: var(--ed-spacing-xs);
          .avatar-box {
            flex: 0 0 auto;
            margin-right: var(--ed-spacing-xs);
          }
          .user-info {
            display: flex;
            flex-direction: column;
            padding-right: var(--ed-spacing-2xs);
            > span {
              font-size: var(--ed-font-size-supporting);
              color: var(--ed-gray-6);
              &:first-of-type {
                font-weight: var(--ed-font-weight-black);
              }
            }
          }
        }
        .md-learning-list-item-hours {
          position: relative;
          flex: 1;
          padding-right: 4rem;
          text-align: right;
          --progress-value: 0%;

          &:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -40px;
            left: calc(var(--progress-value) / 2);
            transform: translateX(-50%);
            background: var(--ed-text-color-primary-light);
            color: var(--ed-gray-6);
            padding: var(--ed-spacing-2xs) var(--ed-spacing-sm);
            border-radius: var(--ed-progress-bar-border-radius);
            white-space: nowrap;
            font-size: var(--ed-font-size-supporting);
            box-shadow: var(--ed-shadow-sm);
            z-index: 10;

            /* Add a constraint to keep the tooltip within the container */
            max-width: calc(100% - 4rem);
          }
          // Add this to align the progress bar to the right
          .progress-container {
            margin-left: auto;
          }
          progress {
            background-color: var(
              --ed-progress-bar-bg-color,
              var(--original-progress-bar-bg-color)
            );
          }

          progress::-webkit-progress-bar {
            background-color: var(
              --ed-progress-bar-bg-color,
              var(--original-progress-bar-bg-color)
            );
          }
        }
      }
      .md-learning-headers > div:first-of-type,
      .md-learning-list-item-name {
        width: rem-calc(288);
        box-sizing: border-box;
        display: flex;
        flex: 0 0 rem-calc(288);
        padding-left: var(--ed-spacing-base) !important;
        padding-right: var(--ed-spacing-base) !important;
      }
    }
    .md-team-learning-footer {
      height: rem-calc(64);
      display: flex;
      justify-content: end;
      align-items: center;
      gap: var(--ed-spacing-sm);
      border: var(--ed-border-size-sm) solid var(--ed-border-color-light);
      border-bottom-left-radius: var(--ed-border-radius-md);
      border-bottom-right-radius: var(--ed-border-radius-md);
      padding-left: var(--ed-spacing-2xs);
      padding-right: var(--ed-spacing-2xs);
      margin-top: calc(-1 * var(--ed-spacing-base));
      .pagination {
        > ul {
          margin-left: 0 !important;
        }
      }
      .md-team-learning-footer-limit {
        display: block;
      }
    }
    .no-border {
      border: none !important;
    }
  }
}
.header {
  font-size: var(--ed-font-size-lg) !important;
  color: var(--ed-text-color-primary);
  font-weight: var(--ed-font-weight-black);
  margin: 0;
}
.flex-between {
  justify-content: space-between;
  gap: 0 !important;
  height: rem-calc(250);
}
.flex-start {
  display: flex;
  align-items: start;
  justify-content: start;
  gap: var(--ed-spacing-base);
}
.supporting-text {
  font-size: var(--ed-font-size-supporting);
  color: var(--ed-gray-6);
  font-weight: var(--ed-font-weight-normal);
  line-height: var(--ed-line-height-xs);
}
.width-200 {
  width: rem-calc(200);
}
.width-147 {
  width: rem-calc(147);
}
.text-overflow-ellipsis {
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
