import React, { useCallback, useContext, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { translatr } from 'centralized-design-system/src/Translatr';
import Tab from 'centralized-design-system/src/TabBar/SimpleTabs';
import Loading from 'centralized-design-system/src/Loading';
import { getTeamOverviewStats } from 'edc-web-sdk/requests/managerDashboard.v2';
import SendReminderModal from '@components/modals/SendReminderModal';
import VerticalDivider from '../../../../../../icons/VerticalDivider';
import './styles.scss';
import ContentLearningsTable from './components/ContentLearningsTable';
import TeamLearningsTable from './components/TeamLearningsTable';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';
import { getUserInfo } from 'edc-web-sdk/requests/users';
import { getMembersInfo } from '../../../../../manager_dashboard/utils';

export default connect(() => ({}))(() => {
  // State variables
  const [openSendReminderModal, setOpenSendReminderModal] = useState(false);
  const [activeTab, setActiveTab] = useState('ContentView');
  const [selectedAssignment, setSelectedAssignment] = useState({});
  const [loading, setLoading] = useState(true);
  const [teamStats, setTeamStats] = useState({});
  const [teamIndex, setTeamIndex] = useState({});

  const mdContext = useContext(MDContext);
  const { reporteesFilter } = mdContext;

  const subTabs = ['ContentView', 'IndividualView'];

  // Fetch user data on reportee change
  useEffect(() => {
    setLoading(true);
    getStats();
    getReporters();
  }, [reporteesFilter]);

  function getReporters() {
    getUserInfo({ fields: 'reporters' })
      .then(resp => {
        const teamMembers = getMembersInfo(resp.reporters);
        let index = {};
        teamMembers?.forEach(element => {
          index = { ...index, [element.id]: element };
        });
        setTeamIndex(index);
      })
      .catch(err => {
        console.error('Error in Manager.UserInfo:', err);
        setTeamIndex({});
      });
  }

  const getStats = useCallback(async () => {
    try {
      const stats = await getTeamOverviewStats({ reporting_type: reporteesFilter.toLowerCase() });
      setTeamStats(stats);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }, [reporteesFilter]);

  // Tab management
  const toggleTab = label => {
    if (activeTab !== label) {
      setActiveTab(label);
    }
  };

  const renderSubTabs = () => (
    <div className="individual-sub-tabs">
      <Tab OnTabClickCB={toggleTab} active={activeTab === 'ContentView' ? 0 : 1}>
        {subTabs.map((name, i) => (
          <Tab.TabPane
            key={i}
            tab={name}
            label={
              name === 'ContentView'
                ? translatr('web.manager-dashboard-v2.main', 'ContentView') || 'Content view'
                : translatr('web.manager-dashboard-v2.main', 'IndividualView') || 'Individual view'
            }
          />
        ))}
      </Tab>
    </div>
  );

  const toggleSendReminderModal = (e, assignment = undefined) => {
    setSelectedAssignment(assignment || {});
    setOpenSendReminderModal(!openSendReminderModal);
  };

  return (
    <>
      <div id="assigned-learning-widget" className="assigned-learning-widget block">
        {/* Widget Title */}
        <div className="assigned-learning-widget__title">
          <h3>
            {translatr('web.manager-dashboard-v2.main', 'AssignedLearning') || 'Assigned learning'}
          </h3>
        </div>

        {/* Loading Indicator */}
        {loading ? (
          <div className="table-loader">
            <Loading />
          </div>
        ) : (
          <div className="assigned-learning-widget__stats">
            {[
              { label: 'Total', count: teamStats?.team_assignments_count },
              {
                label: 'Overdue',
                count: teamStats?.overdue_assignments_count,
                color: '#DE350B'
              },
              {
                label: 'DueIn2Weeks',
                count: teamStats?.due_in_two_weeks_count,
                color: '#FFAB00'
              },
              {
                label: 'InProgress',
                count: teamStats?.in_progress_count,
                color: '#005BF0'
              },
              {
                label: 'Completed',
                count: teamStats?.completed_assignments_count,
                color: '#14B872'
              }
            ].map((stat, index) => (
              <React.Fragment key={index}>
                <div className="assigned-learning-widget__stats__item">
                  <div className="assigned-learning-widget__stats__item__title">
                    {translatr('web.manager-dashboard-v2.main', stat.label) || stat.label}
                  </div>
                  <div className="assigned-learning-widget__stats__item__content">
                    {stat.color && (
                      <span className="icon-oval-fill" style={{ color: stat.color }} />
                    )}
                    <h3>{stat.count || 0}</h3>
                  </div>
                </div>
                {index < 4 && (
                  <div className="flx-centered">
                    <VerticalDivider height={72} />
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        )}

        {/* Tabs Section */}
        <div className="assigned-learning-tabs__wrapper">{renderSubTabs()}</div>

        {/* Table Section */}
        <div id="assigned-learning-table" className="assigned-learning-table__container">
          {activeTab === 'ContentView' ? (
            <ContentLearningsTable sendReminderModalHandler={toggleSendReminderModal} />
          ) : (
            <TeamLearningsTable sendReminderModalHandler={toggleSendReminderModal} />
          )}
        </div>
      </div>

      {openSendReminderModal && (
        <SendReminderModal
          onClose={toggleSendReminderModal}
          assignments={selectedAssignment}
          isMultiUser={activeTab === 'ContentView'}
          indexedTeam={activeTab === 'ContentView' ? teamIndex : null}
        />
      )}
    </>
  );
});
