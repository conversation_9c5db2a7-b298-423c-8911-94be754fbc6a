import { useState, useEffect, useRef, useContext } from 'react';
import {
  getTeamLearningMatrix,
  getTeamUsersLearningMatrix
} from 'edc-web-sdk/requests/managerDashboard.v2';
import moment from 'moment';
import { translatr } from 'centralized-design-system/src/Translatr';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';
import { PAGE_ROW_OPTIONS } from '../../../helpers';
import { ASC, DESC } from '../utils/utils';

// Create a base hook factory function that accepts the API fetch function and additional options
const createLearningsHook = (fetchFunction, options = {}) => () => {
  const ALL_TIME = translatr('web.common.main', 'AllTime');

  const [learnings, setLearnings] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limitOption, setLimitOption] = useState(PAGE_ROW_OPTIONS[0]);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [shouldUpdate, setShouldUpdate] = useState(false);
  const [isFilterApplied, setFilterApplied] = useState(false);
  const [selectedOption, setSelectedFilterOption] = useState('All');
  const [sortField, setSortField] = useState(options.initQueryParams?.sort_field || null);
  const [sortDir, setSortDir] = useState(options.initQueryParams?.sort_direction || null);

  // Date state management
  const [assignedDate, _setAssignedDate] = useState({ fromDate: null, toDate: null });
  const [dueDate, _setDueDate] = useState({ fromDate: null, toDate: null });
  const [placeholderAssignedDate, setPlaceholderAssignedDate] = useState(ALL_TIME);
  const [placeholderDueDate, setPlaceholderDueDate] = useState(ALL_TIME);
  const [showClearDueDate, setShowClearDueDate] = useState(false);
  const [showClearAssignedDate, setShowClearAssignedDate] = useState(false);

  const mdContext = useContext(MDContext);
  const { reporteesFilter } = mdContext;

  const searchRef = useRef('');
  const assignedDateRef = useRef(assignedDate);
  const dueDateRef = useRef(dueDate);
  const sortDirRef = useRef(sortDir);
  const { value: LIMIT } = limitOption || {};

  const setAssignedDate = data => {
    assignedDateRef.current = { ...data };
    _setAssignedDate(data);
  };

  const setDueDate = data => {
    dueDateRef.current = { ...data };
    _setDueDate(data);
  };

  const getMMDDYYYStr = dateStr => {
    if (!dateStr) return null;
    return moment(new Date(dateStr)).format('MM/DD/YYYY');
  };

  const getLearnings = async (page = 1, limit = LIMIT) => {
    setLoading(true);
    setError(null);

    try {
      const pageToRequest = page - 1;
      let queryParams = {
        reporting_type: reporteesFilter.toLowerCase(),
        offset: pageToRequest * limit,
        limit: limit
      };

      if (sortField && sortDir && sortDirRef.current) {
        queryParams.sort_field = sortField;
        queryParams.sort_direction = sortDirRef.current;
      }

      if (searchRef.current) {
        queryParams.q = searchRef.current;
      }

      // Add filter by selected option if it's not 'All'
      if (selectedOption && selectedOption !== 'All') {
        queryParams.assignment_type = selectedOption;
      }

      // Add date filters
      const theDueDate = dueDateRef.current;
      if (theDueDate?.fromDate && theDueDate?.toDate) {
        queryParams['due_at[]'] = [
          getMMDDYYYStr(theDueDate.fromDate),
          getMMDDYYYStr(theDueDate.toDate)
        ];
      }

      const theAssignedDate = assignedDateRef.current;
      if (theAssignedDate?.fromDate && theAssignedDate?.toDate) {
        queryParams['assigned_date[]'] = [
          getMMDDYYYStr(theAssignedDate.fromDate),
          getMMDDYYYStr(theAssignedDate.toDate)
        ];
      }

      const teamLearnings = await fetchFunction(queryParams);

      setLearnings(teamLearnings.data);
      setTotalCount(
        Object.keys(teamLearnings).includes('total_count')
          ? teamLearnings.total_count
          : teamLearnings.total_users
      );
      setCurrentPage(page);
    } catch (err) {
      console.error('Failed to fetch team learnings:', err);
      setError('Failed to load team learnings. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const setSearchQuery = query => {
    searchRef.current = query;
    getLearnings(1); // Reset to first page on search
  };

  const updateSort = () => {
    // Update the ref first, then the state
    sortDirRef.current = sortDirRef.current === ASC ? DESC : ASC;
    setSortDir(sortDirRef.current);
    getLearnings(1); // Reset to first page on sort
  };

  const onDueDateChange = (newDate1, newDate2) => {
    try {
      const fromDate = newDate1?.toLocalISOString() || null;
      const toDate = newDate2?.toLocalISOString() || null;

      // Only validate date order if both dates are present
      if (fromDate && toDate && new Date(fromDate) > new Date(toDate)) {
        console.warn('Invalid date range: start date is after end date');
        return;
      }

      setDueDate({ fromDate, toDate });

      // Only update placeholder and show clear button if at least one date is selected
      if (fromDate || toDate) {
        setPlaceholderDueDate(null);
        setShowClearDueDate(true);
      }

      // Only trigger filter and fetch if both dates are selected
      if (fromDate && toDate) {
        setFilterApplied(true);
        getLearnings(1); // Reset to first page when filter changes
      }
    } catch (err) {
      console.error('Error processing due date change:', err);
    }
  };

  const onAssignedDateChange = (newDate1, newDate2) => {
    try {
      const fromDate = newDate1?.toLocalISOString() || null;
      const toDate = newDate2?.toLocalISOString() || null;

      // Only validate date order if both dates are present
      if (fromDate && toDate && new Date(fromDate) > new Date(toDate)) {
        console.warn('Invalid date range: start date is after end date');
        return;
      }

      setAssignedDate({ fromDate, toDate });

      // Only update placeholder and show clear button if at least one date is selected
      if (fromDate || toDate) {
        setPlaceholderAssignedDate(null);
        setShowClearAssignedDate(true);
      }

      // Only trigger filter and fetch if both dates are selected
      if (fromDate && toDate) {
        setFilterApplied(true);
        getLearnings(1); // Reset to first page when filter changes
      }
    } catch (err) {
      console.error('Error processing assigned date change:', err);
    }
  };
  const onClearDueDate = () => {
    setPlaceholderDueDate(ALL_TIME);
    setShowClearDueDate(false);
    setDueDate({ fromDate: null, toDate: null });
    setFilterApplied(false);
    getLearnings(1);
  };

  const onClearAssignedDate = () => {
    setPlaceholderAssignedDate(ALL_TIME);
    setShowClearAssignedDate(false);
    setAssignedDate({ fromDate: null, toDate: null });
    setFilterApplied(false);
    getLearnings(1);
  };

  // Use a ref to track if this is the first render
  const isFirstRender = useRef(true);

  // Update getLearnings when selectedOption changes, but not on first render
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    if (selectedOption && selectedOption !== 'All') {
      setFilterApplied(true);
    } else {
      setFilterApplied(false);
    }
    getLearnings(1); // Reset to first page when filter changes
  }, [selectedOption, reporteesFilter]);

  useEffect(() => {
    if (shouldUpdate) {
      getLearnings(1);
      setShouldUpdate(false);
    }
  }, [shouldUpdate]);

  // Initial load
  useEffect(() => {
    getLearnings();
  }, []);

  // Pagination
  const onSelectLimitOption = opt => {
    setLimitOption(opt);
    getLearnings(1, opt.value);
  };

  return {
    learnings,
    totalCount,
    currentPage,
    limitOption,
    onSelectLimitOption,
    loading,
    error,
    getLearnings,
    setSearchQuery,
    shouldUpdate,
    setShouldUpdate,
    isFilterApplied,
    setFilterApplied,
    selectedOption,
    setSelectedFilterOption,
    sortDir,
    setSortDir,
    sortField,
    setSortField,
    updateSort,
    // Date related states and functions
    dueDate,
    assignedDate,
    placeholderDueDate,
    placeholderAssignedDate,
    showClearDueDate,
    showClearAssignedDate,
    onDueDateChange,
    onAssignedDateChange,
    onClearDueDate,
    onClearAssignedDate
  };
};

// Create the specific hooks using the factory function with appropriate options
export const useContentLearnings = createLearningsHook(getTeamLearningMatrix, {
  initQueryParams: {
    sort_field: 'card_title',
    sort_direction: DESC
  }
});

export const useTeamLearnings = createLearningsHook(getTeamUsersLearningMatrix, {
  initQueryParams: {} // No additional query parameters needed
});
