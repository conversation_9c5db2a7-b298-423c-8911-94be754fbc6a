import { useState, useContext, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { array, string, bool, func } from 'prop-types';
import { debounce } from 'lodash';
import { Button } from 'centralized-design-system/src/Buttons';
import { Select, SearchInput, LinearProgressBar } from 'centralized-design-system/src/Inputs';
import Pagination from 'centralized-design-system/src/Pagination';
import { translatr } from 'centralized-design-system/src/Translatr';
import Avatar, { AVATAR_SIZES } from 'centralized-design-system/src/Avatar';
import { getTeamActivity, downloadTeamActivity } from 'edc-web-sdk/requests/managerDashboard.v2';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';
import { getErrorMessage } from '@pages/manager_dashboard_blocks/context/helper';
import { OPEN_SNACKBAR_V2 } from '../../../../../../constants/actionTypes';
import Loading from '../../../overview/components/Loadingwidget';
import { ASSIGNMENT_TYPE_OPTIONS, DATE_RANGE_OPTIONS } from '../../../overview/helpers';
import { SORT_OPTIONS, PAGE_ROW_OPTIONS, processStats } from '../../helpers';
import '../../learning.scss';

const LearningActivity = ({ dispatch }) => {
  const [filters, setFilters] = useState({
    activeQuery: '',
    currentPage: 0,
    selectedAssignmentType: ASSIGNMENT_TYPE_OPTIONS[0].value,
    selectedDateRange: DATE_RANGE_OPTIONS[1],
    selectedSortOption: SORT_OPTIONS[0],
    limitOption: PAGE_ROW_OPTIONS[0]
  });
  const [dateRangeOptions, setDateRangeOptions] = useState([]);
  const [isEmpty, setIsEmpty] = useState(true);
  const [isFirst, setIsFirst] = useState(true);
  const [loading, setLoading] = useState(false);
  const [headers, setHeaders] = useState([]);
  const [learningHours, setLearningHours] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const mdContext = useContext(MDContext);
  const { reporteesFilter, clcOptions, clcLoading } = mdContext;

  const getTeamLearningHours = (initUpdate = false) => {
    setLoading(true);
    getTeamActivity(getPayload)
      .then(res => {
        const hasError = res.data?.error;
        if (!hasError) {
          const { stats, headers: _headers } = processStats(res || {});
          if (!_headers) {
            updateStates({
              _initUpdate: initUpdate,
              _isEmpty: false
            });
          } else {
            updateStates({
              _initUpdate: initUpdate,
              _isEmpty: false,
              _count: res.total_users || 0,
              _headers,
              _learningHours: stats
            });
          }
          if (isFirst) {
            setIsFirst(false);
          }
          setLoading(false);
        } else {
          updateStates({
            _initUpdate: initUpdate,
            _isEmpty: true,
            _count: 0
          });
          setLoading(false);
        }
      })
      .catch(err => {
        updateStates({
          _initUpdate: initUpdate,
          _isEmpty: true
        });
        setLoading(false);
        console.error('Error in MD Learning.getTeamActivity: ', getErrorMessage(err));
      });
  };

  const updateStates = opt => {
    const { _initUpdate, _isEmpty, _count, _headers, _learningHours } = opt || {};

    if (_initUpdate) {
      setIsEmpty(_isEmpty);
    }

    if (_isEmpty) {
      setTotalCount(0);
      setHeaders([]);
      setLearningHours([]);
    } else {
      const team_header = translatr('web.manager-dashboard-v2.main', 'TeamMember') || 'Team member';

      setTotalCount(_count || 0);
      setHeaders([`${team_header} (${_count})`, ..._headers]);
      setLearningHours(_learningHours || []);
    }
  };

  const getPayload = useMemo(() => {
    const {
      activeQuery,
      currentPage,
      selectedAssignmentType,
      selectedDateRange,
      selectedSortOption,
      limitOption
    } = filters;

    const { value: sub_type, type } = selectedDateRange || {};
    const { value: limit } = limitOption || {};
    const { value: order } = selectedSortOption || {};
    const offset = currentPage * limit;
    return {
      type,
      sub_type,
      assigment_type: selectedAssignmentType,
      limit,
      offset,
      search_term: activeQuery || '',
      order,
      reporting_type: reporteesFilter.toLowerCase()
    };
  }, [filters, reporteesFilter]);

  useEffect(() => {
    setDateRangeOptions(DATE_RANGE_OPTIONS.concat(clcOptions));
  }, [clcOptions]);

  useEffect(() => {
    setIsFirst(true);
    setFilters({
      ...filters,
      currentPage: 0
    });
    getTeamLearningHours(true);
  }, [reporteesFilter]);

  useEffect(() => {
    if (isFirst) {
      return;
    }
    getTeamLearningHours();
  }, [filters]);

  const onDownload = () => {
    setLoading(true);
    downloadTeamActivity(getPayload)
      .then(resp => {
        setLoading(false);
        dispatch({
          type: OPEN_SNACKBAR_V2,
          message: resp.message,
          snackbarType: 'success',
          autoClose: true
        });
      })
      .catch(err => {
        setLoading(false);
        const errStr = getErrorMessage(err);
        console.error('Error in MD Learning.downloadTeamActivity: ', errStr);
        dispatch({
          type: OPEN_SNACKBAR_V2,
          message: errStr,
          snackbarType: 'error',
          autoClose: true
        });
      });
  };

  const paginate = resp => {
    const { limitOption, currentPage } = filters;
    if (resp.event === 'next') {
      if (currentPage < totalCount / limitOption.value) {
        setFilters({
          ...filters,
          currentPage: currentPage + 1
        });
      }
    } else if (resp.event === 'prev' && currentPage > 0) {
      setFilters({
        ...filters,
        currentPage: currentPage - 1
      });
    } else {
      if (Number.isInteger(resp.event) && resp.event > 0) {
        setFilters({
          ...filters,
          currentPage: resp.event - 1
        });
      }
    }
  };

  function doSearch(query) {
    const _filters = {
      ...filters,
      activeQuery: query,
      currentPage: 0
    };
    setFilters(_filters);
  }
  const debouncedSearch = debounce(doSearch, 500);
  const onSelectSortOption = opt => {
    setFilters({
      ...filters,
      selectedSortOption: opt,
      currentPage: 0
    });
  };
  const onSelectLimitOption = opt => {
    setFilters({
      ...filters,
      limitOption: opt,
      currentPage: 0
    });
  };
  const onSelectAssignmentType = opt => {
    setFilters({
      ...filters,
      selectedAssignmentType: opt.value,
      currentPage: 0
    });
  };
  const onSelectDateRange = opt => {
    setFilters({
      ...filters,
      selectedDateRange: opt,
      currentPage: 0
    });
  };

  const renderEmpty = check => {
    return (
      <div className="md-learning-empty-container">
        {check && (
          <>
            <label>
              {translatr('web.manager-dashboard-v2.main', 'NoLearningActivity') ||
                'No learning activity'}
            </label>
            <label>
              {translatr(
                'web.manager-dashboard-v2.main',
                'CheckBackLaterOrMotivateYourTeamtoEngageInLearning'
              ) || 'Check back later or motivate your team to engage in learning'}
            </label>
          </>
        )}
        {!check && (
          <>
            <label>{translatr('web.common.main', 'NoResultsFound2')}</label>
            <label>
              {translatr(
                'web.manager-dashboard-v2.main',
                'TryAdjustingYourSearchOrFiltertoFindwhatYouAreLookingfor'
              ) || 'Try adjusting your search or filter to find what you are looking for'}
            </label>
          </>
        )}
      </div>
    );
  };

  const renderUser = data => {
    return (
      <>
        <div className="avatar-box">
          <Avatar user={data} size={AVATAR_SIZES.LARGE} />
        </div>
        <div className="user-info">
          <span className="text-overflow-ellipsis"> {data.name} </span>
          <span className="text-overflow-ellipsis"> {data.job_title} </span>
        </div>
      </>
    );
  };

  const renderUsersLearningActivity = () => {
    const maxInterval = [...headers].pop();
    return (
      <>
        <div className="md-learning-headers">
          {headers.map((header, i) => {
            return <div key={i}>{header}</div>;
          })}
          <div key={'last-header'}>[h]</div>
        </div>
        <div className="md-learning-list">
          {learningHours.map((item, i) => {
            const { user_name, user_id, job_title, avatar, learning_in_hours, hrString } =
              item || {};
            const user = {
              name: user_name,
              imgUrl: avatar?.medium,
              id: user_id,
              job_title
            };
            const progress_num = maxInterval > 0 ? (learning_in_hours / maxInterval) * 100 : 0;
            return (
              <div key={i} className="md-learning-list-item">
                <div key={`user-${user_id}`} className="md-learning-list-item-name">
                  {renderUser(user)}
                </div>
                <div
                  data-tooltip={hrString}
                  key={`progress-${user_id}`}
                  className="md-learning-list-item-hours"
                  style={{
                    '--progress-value': `${progress_num}%`,
                    '--ed-progress-bar-bg-color': '#fff'
                  }}
                >
                  <LinearProgressBar value={progress_num} hideLabel={true} />
                </div>
              </div>
            );
          })}
        </div>
      </>
    );
  };

  const renderTeamStats = () => {
    const {
      activeQuery,
      limitOption,
      selectedDateRange,
      selectedSortOption,
      currentPage,
      selectedAssignmentType
    } = filters || {};

    const { value: offset_amount } = limitOption || {};
    const showPagination = totalCount > parseInt(offset_amount);
    const _page = currentPage + 1;
    const _offset_amount = currentPage * offset_amount + 1;
    const _count = totalCount > _page * offset_amount ? _page * offset_amount : totalCount;
    const page_desc = `${_offset_amount}-${_count} ${translatr('web.common.main', 'Of') ||
      'of'} ${totalCount}`;

    return (
      <div
        id="md-learning-activity-widget"
        className={`md-team-learning-container ${!totalCount ? 'flex-between' : ''}`}
      >
        <div className="md-team-learning-actions">
          <div>
            <SearchInput
              value={activeQuery}
              placeholder={translatr('web.manager-dashboard-v2.main', 'SearchMembers')}
              onSearch={debouncedSearch}
              searchOnTextChange={true}
              id="md-team-learning-search"
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SearchTeamMembers')}
            />
          </div>
          <div className="flex-start">
            <div className="width-200" id="assignmentType">
              <Select
                id="assignmentType"
                title={translatr('web.manager-dashboard-v2.main', 'AssignmentType')}
                defaultValue={selectedAssignmentType}
                onChange={onSelectAssignmentType}
                ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectAssignmentType')}
                items={ASSIGNMENT_TYPE_OPTIONS}
              />
            </div>
            <div className="width-200" id="dateRange">
              <Select
                id="dateRange"
                title={translatr('web.manager-dashboard-v2.main', 'DateRange')}
                defaultValue={selectedDateRange.value}
                onChange={onSelectDateRange}
                ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectDateRange')}
                items={dateRangeOptions}
              />
            </div>
            <div className="width-200" id="sort">
              <Select
                id="sort"
                title={translatr('web.manager-dashboard-v2.main', 'Sort')}
                defaultValue={selectedSortOption.value}
                onChange={onSelectSortOption}
                ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectSortOption')}
                items={SORT_OPTIONS}
              />
            </div>
          </div>
          <div>
            <Button
              color="secondary"
              variant="ghost"
              onClick={() => onDownload()}
              disabled={!totalCount}
            >
              {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
            </Button>
          </div>
        </div>
        <div className={`md-team-learning-table ${!totalCount ? 'no-border' : ''}`}>
          {!totalCount && renderEmpty(!!totalCount)}
          {!!totalCount && renderUsersLearningActivity()}
        </div>
        {!!totalCount && (
          <div className="md-team-learning-footer">
            <div className="supporting-text">
              {translatr('web.manager-dashboard-v2.main', 'RowsPerPage') || 'Rows per page'}
            </div>
            <div className="md-team-learning-footer-limit" id="page-num" key="page-num">
              <Select
                id="page"
                defaultValue={limitOption.value}
                onChange={onSelectLimitOption}
                ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectLimitOption')}
                items={PAGE_ROW_OPTIONS}
              />
            </div>
            <div className="supporting-text">{page_desc}</div>
            {showPagination && (
              <div>
                <Pagination
                  postPerPage={offset_amount}
                  totalPosts={totalCount}
                  paginate={paginate}
                  activePage={currentPage + 1}
                  iconType={true}
                />
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`learning-container ${isEmpty ? 'flex-between' : ''}`}>
      <h3 className="header">
        {translatr('web.manager-dashboard-v2.main', 'LearningActivity') || 'Learning activity'}
      </h3>
      {(loading || clcLoading) && <Loading />}
      {!loading && !clcLoading && isEmpty && renderEmpty(isEmpty)}
      {!loading && !clcLoading && !isEmpty && renderTeamStats()}
    </div>
  );
};
LearningActivity.propTypes = {
  reporteesFilter: string.isRequired,
  clcOptions: array.isRequired,
  clcLoading: bool.isRequired,
  dispatch: func.isRequired
};
export default connect()(LearningActivity);
