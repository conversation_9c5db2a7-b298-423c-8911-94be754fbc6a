import React, { useMemo, useCallback, useContext } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';

// UI Components
import Table from 'centralized-design-system/src/Table';
import { Select, SearchInput } from 'centralized-design-system/src/Inputs';
import Pagination from 'centralized-design-system/src/Pagination';
import Loading from 'centralized-design-system/src/Loading';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { Button } from 'centralized-design-system/src/Buttons';
import DatePicker from 'centralized-design-system/src/DatePickers';

// Utilities
import { translatr } from 'centralized-design-system/src/Translatr';
import { downloadTeamsReport } from 'edc-web-sdk/requests/managerDashboard.v2';
import unescape from 'lodash/unescape';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { truncateText } from '@utils/utils';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import _ from 'lodash';

// Custom components and hooks
import { useContentLearnings } from '../hooks';
import ErrorMessage from './ErrorMessage';
import NoResultFoundInSearch from './NoResultFoundInSearch';
import NoAssignedTraining from './NoAssignedTraining';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';

// Shared utilities
import {
  getSelectOptions,
  createDebouncedSearch,
  handlePagination as handlePaginationUtil,
  createReminderCell as createReminderCellUtil,
  createTableHeaders,
  createReportPayload,
  handleDownload
} from '../utils/utils';
import { PAGE_ROW_OPTIONS } from '../../../helpers';

const ContentLearningsTable = ({
  contentTableViewHandler,
  sendReminderModalHandler,
  user_info_disabled,
  toast
}) => {
  const {
    learnings,
    totalCount,
    currentPage,
    loading,
    error,
    getLearnings,
    setSearchQuery,
    isFilterApplied,
    selectedOption,
    setSelectedFilterOption,
    updateSort,
    dueDate,
    assignedDate,
    placeholderDueDate,
    placeholderAssignedDate,
    showClearDueDate,
    showClearAssignedDate,
    onDueDateChange,
    onAssignedDateChange,
    onClearDueDate,
    onClearAssignedDate,
    limitOption,
    onSelectLimitOption
  } = useContentLearnings();
  const navigate = useNavigate();
  const SelectOption = getSelectOptions();
  const mdContext = useContext(MDContext);
  const { reporteesFilter } = mdContext;

  // ===== Table Headers Configuration =====
  const formattedTableHeaders = useMemo(
    () => createTableHeaders('content', user_info_disabled, totalCount, updateSort),
    [user_info_disabled, totalCount, updateSort]
  );

  // ===== Search Functionality =====
  const debouncedSearch = useMemo(() => createDebouncedSearch(setSearchQuery), [setSearchQuery]);

  const onContentClick = item => {
    navigate('/manager-dashboard/learning/content', { state: { content: item } });
  };

  // ===== Table Data Transformation =====
  const tableData = useMemo(() => {
    if (!Array.isArray(learnings)) return [];

    return learnings.map((learning, index) => {
      const rows = [];
      const learningWithOverdue = {
        ...learning,
        overdue: learning.overdue_count
      };

      // Get all header keys from the first header of each column
      const headerKeys = formattedTableHeaders.map(header => header.id);

      headerKeys.forEach((key, i) => {
        if (key === 'send_reminder') {
          if (!user_info_disabled) {
            rows.push(
              createReminderCellUtil(
                learning,
                index,
                i,
                learningWithOverdue,
                sendReminderModalHandler,
                'card_id'
              )
            );
          }
        } else {
          rows.push(createDataCell(key, learningWithOverdue, index, i));
        }
      });

      return rows;
    });
  }, [learnings, formattedTableHeaders, user_info_disabled, sendReminderModalHandler]);

  /**
   * Creates a data cell for the table
   */
  function createDataCell(key, learningData, rowIndex, colIndex) {
    const isTitleCell = key === 'card_title';
    const cellValue = learningData[key];

    return {
      className: isTitleCell ? 'card-title' : '',
      children: isTitleCell ? (
        <Tooltip
          isHtmlIncluded
          message={cellValue}
          hide={cellValue?.length < 32}
          customClass={!user_info_disabled && 'learning-table-tooltip'}
        >
          <Button
            variant="borderless"
            color="primary"
            size="medium"
            padding="xsmall"
            id={`${key}-${rowIndex}-${colIndex}`}
            onClick={() => onContentClick(learningData)}
          >
            {safeRender(
              cellValue?.length > 32 ? truncateText(unescape(cellValue), 32, '...') : cellValue
            )}
          </Button>
        </Tooltip>
      ) : (
        <span className="short-text">{cellValue}</span>
      ),
      id: `${key}-${rowIndex}-${colIndex}`,
      align: isTitleCell ? 'text-left' : 'text-right',
      card_id: learningData.card_id,
      card_title: learningData.card_title,
      onClick: user_info_disabled ? () => {} : contentTableViewHandler
    };
  }

  const onChange = value => {
    setSelectedFilterOption(value);
  };

  // ===== Pagination Handling =====
  const handlePagination = useCallback(
    paginationEvent => handlePaginationUtil(paginationEvent, currentPage, getLearnings),
    [currentPage, getLearnings]
  );

  // ===== Report Download =====
  const handleDownloadReport = useCallback(
    async e => {
      const payload = createReportPayload({
        reporteesFilter,
        searchQuery: setSearchQuery._value,
        selectedOption,
        dueDate,
        assignedDate
      });

      await handleDownload(e, downloadTeamsReport, payload, toast);
    },
    [reporteesFilter, selectedOption, dueDate, assignedDate, setSearchQuery]
  );

  const showTable = tableData.length > 0 && !loading;
  const showNoAssignedTraining = !loading && !isFilterApplied && tableData.length < 1;
  const showNoResultFoundInSearch = !loading && isFilterApplied && tableData.length < 1;

  const { value: offset_amount } = limitOption || {};
  const showPagination = totalCount > parseInt(offset_amount);
  const startItem = (currentPage - 1) * parseInt(offset_amount) + 1;
  const endItem = Math.min(startItem + parseInt(offset_amount) - 1, totalCount);
  const page_desc = `${startItem}-${endItem} ${translatr('web.common.main', 'Of') ||
    'of'} ${totalCount}`;

  // ===== Render Component =====
  return (
    <React.Fragment>
      {/* Search and Download Controls */}
      <div className="assigned-learning-table__header">
        <div className="search-container">
          <SearchInput
            placeholder={translatr('web.manager-dashboard-v2.main', 'SearchContent')}
            onSearch={debouncedSearch}
            searchOnTextChange={true}
          />
        </div>
        <div className="mr-base mt-2xs width-200">
          <Select
            id="assigned-learning-type-select"
            title={translatr('web.manager-dashboard-v2.main', 'AssignmentType')}
            items={SelectOption}
            onChange={data => onChange(data.id)}
            defaultValue={SelectOption.find(option => option.id === selectedOption)}
            translateDropDownOptions={false}
          />
        </div>
        <div className="date-picker-container mr-base mt-2xs">
          <div className="date-picker-label-container">
            {_.capitalize(translatr('web.manager-dashboard-v2.main', 'DueDateRange'))}
          </div>
          <DatePicker
            singleDatePicker={false}
            opens="right"
            placeHolder={placeholderDueDate}
            onChange={onDueDateChange}
            startDate={dueDate.fromDate ? new Date(dueDate.fromDate) : null}
            endDate={dueDate.toDate ? new Date(dueDate.toDate) : null}
            onClear={showClearDueDate ? onClearDueDate : null}
          />
        </div>
        <div className="date-picker-container mr-base mt-2xs">
          <div className="date-picker-label-container">
            {translatr('web.manager-dashboard-v2.main', 'AssignedOnDateRange') ||
              'Assigned on date range'}
          </div>
          <DatePicker
            singleDatePicker={false}
            opens="left"
            placeHolder={placeholderAssignedDate}
            onChange={onAssignedDateChange}
            startDate={assignedDate.fromDate ? new Date(assignedDate.fromDate) : null}
            endDate={assignedDate.toDate ? new Date(assignedDate.toDate) : null}
            onClear={showClearAssignedDate ? onClearAssignedDate : null}
          />
        </div>
        <Button
          color="secondary"
          variant="ghost"
          disabled={tableData.length < 1}
          onClick={handleDownloadReport}
        >
          {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
        </Button>
      </div>

      {/* Error Message */}
      {error && <ErrorMessage message={error} />}

      {/* Data Table */}
      {showTable && (
        <Table
          className={`none-vertical-border ${loading ? 'hide-rows' : ''}`}
          headers={formattedTableHeaders}
          rows={tableData}
        />
      )}

      {showNoResultFoundInSearch && <NoResultFoundInSearch />}
      {showNoAssignedTraining && <NoAssignedTraining />}

      {/* Loading State */}
      {loading && (
        <div className="table-loader">
          <Loading />
        </div>
      )}

      {/* Pagination */}
      {!!totalCount && !loading && (
        <div className="md-team-learning-footer">
          <div className="supporting-text">
            {translatr('web.manager-dashboard-v2.main', 'RowsPerPage') || 'Rows per page'}
          </div>
          <div className="md-team-learning-footer-limit" id="page-num" key="page-num">
            <Select
              id="page"
              defaultValue={limitOption.value}
              onChange={onSelectLimitOption}
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectLimitOption')}
              items={PAGE_ROW_OPTIONS}
            />
          </div>
          <div className="supporting-text">{page_desc}</div>
          {showPagination && (
            <div>
              <Pagination
                postPerPage={parseInt(offset_amount)}
                totalPosts={totalCount}
                paginate={handlePagination}
                activePage={currentPage}
                iconType={true}
              />
            </div>
          )}
        </div>
      )}
    </React.Fragment>
  );
};

export default connect(null, dispatch => ({
  toast: (message, type = 'success') => dispatch(openSnackBar(message, type))
}))(ContentLearningsTable);

ContentLearningsTable.propTypes = {
  contentTableViewHandler: PropTypes.func,
  sendReminderModalHandler: PropTypes.func,
  user_info_disabled: PropTypes.bool,
  toast: PropTypes.func
};
