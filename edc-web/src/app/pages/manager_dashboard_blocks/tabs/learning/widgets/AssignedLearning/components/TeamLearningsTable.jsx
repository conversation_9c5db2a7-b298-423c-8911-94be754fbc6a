import React, { useMemo, useCallback, useContext } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

// UI Components
import Table from 'centralized-design-system/src/Table';
import { Select, SearchInput } from 'centralized-design-system/src/Inputs';
import Pagination from 'centralized-design-system/src/Pagination';
import Loading from 'centralized-design-system/src/Loading';
import { Button } from 'centralized-design-system/src/Buttons';
import DatePicker from 'centralized-design-system/src/DatePickers';

// Utilities
import { translatr } from 'centralized-design-system/src/Translatr';
import { downloadTeamsIndividualReport } from 'edc-web-sdk/requests/managerDashboard.v2';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import _ from 'lodash';

// Custom components and hooks
import { useTeamLearnings } from '../hooks';
import ErrorMessage from './ErrorMessage';
import NoResultFoundInSearch from './NoResultFoundInSearch';
import NoAssignedTraining from './NoAssignedTraining';
import Avatar, { AVATAR_SIZES } from 'centralized-design-system/src/Avatar';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';

// Shared utilities
import {
  getSelectOptions,
  createDebouncedSearch,
  handlePagination as handlePaginationUtil,
  createReminderCell as createReminderCellUtil,
  createTableHeaders,
  handleDownload
} from '../utils/utils';
import { PAGE_ROW_OPTIONS } from '../../../helpers';

const TeamLearningsTable = ({ sendReminderModalHandler, user_info_disabled, toast }) => {
  const {
    learnings,
    totalCount,
    currentPage,
    loading,
    error,
    getLearnings,
    setSearchQuery,
    isFilterApplied,
    selectedOption,
    setSelectedFilterOption,
    dueDate,
    assignedDate,
    placeholderDueDate,
    placeholderAssignedDate,
    showClearDueDate,
    showClearAssignedDate,
    onDueDateChange,
    onAssignedDateChange,
    onClearDueDate,
    onClearAssignedDate,
    limitOption,
    onSelectLimitOption
  } = useTeamLearnings();

  const SelectOption = getSelectOptions();
  const mdContext = useContext(MDContext);
  const { reporteesFilter } = mdContext;

  // ===== Table Headers Configuration =====
  const formattedTableHeaders = useMemo(
    () => createTableHeaders('team', user_info_disabled, totalCount),
    [user_info_disabled, totalCount]
  );

  // ===== Search Functionality =====
  const debouncedSearch = useMemo(() => createDebouncedSearch(setSearchQuery), [setSearchQuery]);

  // ===== Table Data Transformation =====
  const tableData = useMemo(() => {
    if (!Array.isArray(learnings)) return [];

    return learnings.map((learning, index) => {
      const rows = [];
      const learningWithOverdue = {
        ...learning,
        overdue: learning.overdue
      };

      // Get all header keys from the first header of each column
      const headerKeys = formattedTableHeaders.map(header => header.id);

      headerKeys.forEach((key, i) => {
        if (key === 'send_reminder') {
          if (!user_info_disabled) {
            rows.push(
              createReminderCellUtil(
                learning,
                index,
                i,
                learningWithOverdue,
                sendReminderModalHandler,
                'user_details',
                'completed',
                'total_assigned'
              )
            );
          }
        } else {
          rows.push(createDataCell(key, learningWithOverdue, index, i));
        }
      });

      return rows;
    });
  }, [learnings, formattedTableHeaders, user_info_disabled, sendReminderModalHandler]);

  /**
   * Creates a data cell for the table
   */
  function createDataCell(key, learningData, rowIndex, colIndex) {
    const isUserCell = key === 'user_details';
    const cellValue = learningData[key];

    return {
      className: 'cell',
      children: isUserCell ? (
        <div key={`user-${cellValue?.user_id}-${rowIndex}`} className="md-learning-list-item-name">
          <>
            <div className="avatar-box" key={`avatar-${cellValue?.user_id}-${rowIndex}`}>
              <Avatar user={cellValue} size={AVATAR_SIZES.LARGE} />
            </div>
            <div className="user-info" key={`info-${cellValue?.user_id}-${rowIndex}`}>
              <span
                className="text-overflow-ellipsis"
                key={`name-${cellValue?.user_id}-${rowIndex}`}
              >
                {' '}
                {cellValue?.user_name}{' '}
              </span>
              <span
                className="text-overflow-ellipsis"
                key={`job-${cellValue?.user_id}-${rowIndex}`}
              >
                {' '}
                {cellValue?.jobTitle}{' '}
              </span>
            </div>
          </>
        </div>
      ) : (
        <span className="short-text" key={`${key}-${rowIndex}-${colIndex}`}>
          {cellValue}
        </span>
      ),
      id: `${key}-${rowIndex}-${colIndex}`,
      align: isUserCell ? 'text-left' : 'text-right',
      user_details: learningData.user_details
    };
  }
  const onChange = value => {
    setSelectedFilterOption(value);
  };

  // ===== Pagination Handling =====
  const handlePagination = useCallback(
    paginationEvent => handlePaginationUtil(paginationEvent, currentPage, getLearnings),
    [currentPage, getLearnings]
  );

  // ===== Report Download =====
  const handleDownloadReport = useCallback(
    async e => {
      const payload = {
        reporting_type: reporteesFilter.toLowerCase(),
        search_query: setSearchQuery._value,
        assignment_type: selectedOption,
        due_date:
          dueDate.fromDate || dueDate.toDate
            ? {
                from_date: dueDate.fromDate,
                to_date: dueDate.toDate
              }
            : null,
        assigned_date:
          assignedDate.fromDate || assignedDate.toDate
            ? {
                from_date: assignedDate.fromDate,
                to_date: assignedDate.toDate
              }
            : null
      };

      await handleDownload(e, downloadTeamsIndividualReport, payload, toast);
    },
    [reporteesFilter, selectedOption, dueDate, assignedDate, setSearchQuery]
  );

  const showTable = tableData.length > 0 && !loading;
  const showNoAssignedTraining = !loading && !isFilterApplied && tableData.length < 1;
  const showNoResultFoundInSearch = !loading && isFilterApplied && tableData.length < 1;

  const { value: offset_amount } = limitOption || {};
  const showPagination = totalCount > parseInt(offset_amount);
  const startItem = (currentPage - 1) * parseInt(offset_amount) + 1;
  const endItem = Math.min(startItem + parseInt(offset_amount) - 1, totalCount);
  const page_desc = `${startItem}-${endItem} ${translatr('web.common.main', 'Of') ||
    'of'} ${totalCount}`;

  // ===== Render Component =====
  return (
    <React.Fragment>
      {/* Search and Download Controls */}
      <div className="assigned-learning-table__header">
        <div className="search-container">
          <SearchInput
            placeholder={translatr('web.manager-dashboard-v2.main', 'SearchMembers')}
            onSearch={debouncedSearch}
            searchOnTextChange={true}
          />
        </div>
        <div className="mr-base mt-2xs width-200">
          <Select
            title={translatr('web.manager-dashboard-v2.main', 'AssignmentType')}
            items={SelectOption}
            onChange={data => onChange(data.id)}
            defaultValue={SelectOption.find(option => option.id === selectedOption)}
            translateDropDownOptions={false}
          />
        </div>
        <div className="date-picker-container mr-base mt-2xs">
          <div className="date-picker-label-container">
            {_.capitalize(translatr('web.manager-dashboard-v2.main', 'DueDateRange'))}
          </div>
          <DatePicker
            singleDatePicker={false}
            opens="right"
            placeHolder={placeholderDueDate}
            onChange={onDueDateChange}
            startDate={dueDate.fromDate ? new Date(dueDate.fromDate) : null}
            endDate={dueDate.toDate ? new Date(dueDate.toDate) : null}
            onClear={showClearDueDate ? onClearDueDate : null}
          />
        </div>
        <div className="date-picker-container mr-base mt-2xs">
          <div className="date-picker-label-container">
            {translatr('web.manager-dashboard-v2.main', 'AssignedOnDateRange') ||
              'Assigned on date range'}
          </div>
          <DatePicker
            singleDatePicker={false}
            opens="left"
            placeHolder={placeholderAssignedDate}
            onChange={onAssignedDateChange}
            startDate={assignedDate.fromDate ? new Date(assignedDate.fromDate) : null}
            endDate={assignedDate.toDate ? new Date(assignedDate.toDate) : null}
            onClear={showClearAssignedDate ? onClearAssignedDate : null}
          />
        </div>
        <Button
          color="secondary"
          variant="ghost"
          disabled={tableData.length < 1 || loading}
          onClick={handleDownloadReport}
        >
          {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
        </Button>
      </div>

      {/* Error Message */}
      {error && <ErrorMessage message={error} />}

      {/* Data Table */}
      {showTable && (
        <Table
          className={`none-vertical-border ${loading ? 'hide-rows' : ''}`}
          headers={formattedTableHeaders}
          rows={tableData}
        />
      )}
      {showNoResultFoundInSearch && <NoResultFoundInSearch />}
      {showNoAssignedTraining && <NoAssignedTraining />}

      {/* Loading State */}
      {loading && (
        <div className="table-loader">
          <Loading />
        </div>
      )}

      {/* Pagination */}
      {!!totalCount && !loading && (
        <div className="md-team-learning-footer">
          <div className="supporting-text">
            {translatr('web.manager-dashboard-v2.main', 'RowsPerPage') || 'Rows per page'}
          </div>
          <div className="md-team-learning-footer-limit" id="page-num" key="page-num">
            <Select
              id="page"
              defaultValue={limitOption.value}
              onChange={onSelectLimitOption}
              ariaLabel={translatr('web.manager-dashboard-v2.main', 'SelectLimitOption')}
              items={PAGE_ROW_OPTIONS}
            />
          </div>
          <div className="supporting-text">{page_desc}</div>
          {showPagination && (
            <div>
              <Pagination
                postPerPage={parseInt(offset_amount)}
                totalPosts={totalCount}
                paginate={handlePagination}
                activePage={currentPage}
                iconType={true}
              />
            </div>
          )}
        </div>
      )}
    </React.Fragment>
  );
};

export default connect(null, dispatch => ({
  toast: (message, type = 'success') => dispatch(openSnackBar(message, type))
}))(TeamLearningsTable);

TeamLearningsTable.propTypes = {
  sendReminderModalHandler: PropTypes.func,
  user_info_disabled: PropTypes.bool,
  toast: PropTypes.func
};
