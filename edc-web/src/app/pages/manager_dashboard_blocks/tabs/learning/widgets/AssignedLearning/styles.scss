@import '~centralized-design-system/src/Styles/_variables.scss';

.flx-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}
.assigned-learning-widget {
  padding: var(--ed-spacing-base);
  .assigned-learning-widget__title {
    h3 {
      font-weight: 900;
      font-size: var(--ed-font-size-lg) !important;
      letter-spacing: 0%;
      vertical-align: middle;
    }
  }
  .assigned-learning-widget__stats {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: var(--ed-spacing-base);
    margin-bottom: var(--ed-spacing-base);
    border: 1px solid var(--ed-gray-2);
    border-radius: var(--ed-border-radius-md);
    padding: var(--ed-spacing-base);
    .assigned-learning-widget__stats__item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      padding: var(--ed-spacing-2xs);
      width: -webkit-fill-available;
      .assigned-learning-widget__stats__item__title {
        font-weight: 400;
        font-size: var(--ed-font-size-sm) !important;
        color: var(--ed-gray-5);
        letter-spacing: 0%;
        vertical-align: middle;
        margin-bottom: var(--ed-spacing-2xs);
      }
      .assigned-learning-widget__stats__item__content {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: var(--ed-font-size-sm) !important;
        letter-spacing: 0%;
        vertical-align: middle;

        .icon-oval-fill::before {
          display: flex;
          align-items: center;
          justify-content: center;
          padding-right: var(--ed-spacing-xs);
          font-size: var(--ed-font-size-2xs);
        }
        h3 {
          font-weight: 900;
          font-size: var(--ed-font-size-lg) !important;
          letter-spacing: 0%;
          vertical-align: middle;
          margin-bottom: 0;
        }
      }
    }
  }
  .assigned-learning-tabs__wrapper {
    .tab-bar.block {
      box-shadow: none !important;
      border-radius: unset !important;
      .tabs {
        margin-left: unset !important;
        .tab button.nav-link {
          text-transform: none !important;
        }
      }
    }
  }
  .assigned-learning-table__container {
    margin-top: var(--ed-spacing-base);
    .assigned-learning-table__header {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: end;
      margin-bottom: var(--ed-spacing-xl);
      .search-container {
        margin-right: var(--ed-spacing-base);
        .ed-input-container .input-group .input-field {
          border: 1px solid var(--ed-gray-5);
          border-radius: var(--ed-border-radius-md);
          min-height: rem-calc(40);
        }
        .ed-input-container {
          width: rem-calc(288);
        }
      }
      .ed-select {
        border: 1px solid var(--ed-gray-5);
        border-radius: var(--ed-border-radius-md);
        min-height: rem-calc(40);
      }
      .mr-base {
        margin-right: var(--ed-spacing-base);
      }
      .mt-2xs {
        margin-top: var(--ed-spacing-2xs);
      }
      .width-200 {
        width: rem-calc(200);
      }
      .date-picker-container {
        min-height: rem-calc(71);
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        .date-picker-label-container {
          margin-bottom: var(--ed-spacing-xs) !important;
          font-size: var(--ed-input-font-size);
          font-weight: var(--ed-input-label-font-weight);
          color: var(--ed-input-label-font-color);
        }
      }
      .react-datepicker-wrapper {
        min-width: rem-calc(200);
        min-height: rem-calc(40);
        border: 1px solid var(--ed-gray-5);
        border-radius: var(--ed-border-radius-md);
      }
    }
    .ed-table-wrapper {
      border-bottom: unset !important;
      border: 1px solid var(--ed-gray-2);
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
      border-top-left-radius: var(--ed-border-radius-md) !important;
      border-top-right-radius: var(--ed-border-radius-md) !important;
      td > div {
        height: rem-calc(76);
        max-height: rem-calc(76);
      }
      th > div {
        height: rem-calc(46);
        max-height: rem-calc(46);
      }
      .md-learning-list-item-name {
        gap: var(--ed-spacing-xs);
        .avatar-box {
          flex: 0 0 auto;
          height: rem-calc(40) !important;
          width: rem-calc(40) !important;
          min-height: unset !important;
        }
        .user-info {
          display: flex;
          flex-direction: column;
          flex-wrap: nowrap;
          align-items: flex-start;
          justify-content: center;
          padding-right: var(--ed-spacing-2xs);
          height: rem-calc(40) !important;
          min-height: unset !important;
          > span {
            font-size: var(--ed-font-size-supporting);
            color: var(--ed-gray-6);
            &:first-of-type {
              font-weight: var(--ed-font-weight-black);
            }
          }
        }
      }
      .text-left {
        min-height: rem-calc(40);
      }
      .short-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .half-cell {
        min-height: fit-content;
        height: fit-content;
        padding-left: 0.1875rem;
      }

      .double-cell-wrapper {
        display: block;
        @extend .short-text;
      }
    }
    .ed-table-wrapper td:first-child > div,
    .ed-table-wrapper th:first-child > div {
      border-right: unset !important;
    }
    .ed-table-wrapper th div {
      border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
    }
    .ed-table-wrapper td > div {
      border-bottom: unset !important;
    }
    .ed-table-wrapper tr:last-child td > div {
      border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2) !important;
    }

    .md-team-learning-footer {
      height: rem-calc(64);
      display: flex;
      justify-content: end;
      align-items: center;
      gap: var(--ed-spacing-sm);
      border: var(--ed-border-size-sm) solid var(--ed-border-color-light);
      border-top: none !important;
      border-bottom-left-radius: var(--ed-border-radius-md);
      border-bottom-right-radius: var(--ed-border-radius-md);
      padding-right: var(--ed-spacing-2xs);
      .pagination {
        > ul {
          margin-left: 0 !important;
        }
      }
      .md-team-learning-footer-limit {
        display: block;
      }
    }
  }
  .no-result-container {
    padding: rem-calc(40) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.error-message-container {
  background-color: #fff8f8;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #d32f2f;
  margin: 16px 0;
  padding: 12px 16px;
}

.error-message-content {
  display: flex;
  align-items: center;
}

.error-message-content i {
  margin-right: 8px;
  font-size: 18px;
}
