@import '~centralized-design-system/src/Styles/_variables.scss';

.md-content-drilldown-widget {
  display: flex;
  width: rem-calc(1140);
  max-width: 100%;
  padding: var(--ed-spacing-base);
  background-color: var(--ed-card-bg-color);
  border-radius: var(--ed-spacing-4xs);
  box-sizing: border-box;
  flex-direction: column;
  box-shadow: var(--ed-shadow-sm);
  overflow-y: visible;

  .capitalize {
    text-transform: capitalize;
  }
  .short-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .double-cell-wrapper {
    display: block;
    height: rem-calc(76);
    @extend .short-text;
  }
  .half-cell {
    min-height: fit-content;
    height: fit-content;
    padding-left: var(--ed-button-large-padding-xsmall-x);
  }
  .md-content-drilldown-widget__top {
    display: flex;
    flex-direction: column;
    gap: var(--ed-spacing-2xs);
    margin-bottom: var(--ed-spacing-base);
    > div {
      &:first-of-type {
        display: flex;
        align-items: center;
        gap: var(--ed-spacing-2xs);
        .md-content-drilldown-widget__title {
          font-size: var(--ed-font-size-lg);
          font-weight: var(--ed-font-weight-black);
          color: var(--ed-gray-7);
        }
        .ed-btn-v2.ed-btn-primary-borderless-v2 {
          color: var(--ed-gray-6);
          padding: 0;
        }
      }
    }
  }

  .md-content-drilldown-widget__footer {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: var(--ed-spacing-sm);
    padding: var(--ed-spacing-sm) var(--ed-spacing-sm) var(--ed-spacing-sm) 0;
    border: var(--ed-input-border-size) solid var(--ed-gray-2);
    border-top: none !important;
    border-bottom-left-radius: var(--ed-border-radius-md);
    border-bottom-right-radius: var(--ed-border-radius-md);
    .pagination ul {
      margin-left: 0;
    }
  }
  .md-content-drilldown-widget__table {
    min-width: 0;
    width: 100%;
    overflow-x: visible;
    .ed-table-wrapper {
      border: var(--ed-input-border-size) solid var(--ed-gray-2);
      border-top-left-radius: var(--ed-border-radius-md);
      border-top-right-radius: var(--ed-border-radius-md);
      overflow-x: visible;
      .hide-rows {
        opacity: 0;
      }
    }
    .ed-table-wrapper td > div {
      border-bottom: none !important;
    }
    .ed-table-wrapper td {
      height: rem-calc(76);
    }
    .ed-table-wrapper th > div {
      height: rem-calc(46);
    }
    .ed-table-wrapper thead th > div {
      .label {
        color: var(--ed-text-color-primary);
        font-weight: var(--ed-font-weight-semibold) !important;
      }
    }
    .ed-btn-v2.ed-btn-size-large-v2 {
      font-size: var(--ed-font-size-sm) !important;
      font-weight: var(--ed-font-weight-bold) !important;
    }
  }
}

.md-content-drilldown-widget__version-selector {
  .btn-version-selection {
    cursor: pointer;
    &.active {
      pointer-events: none;
      color: var(--ed-primary-base);
    }
  }
  span:last-child i {
    display: none !important;
  }
}
.none-vertical-border {
  &.ed-table-wrapper th:first-child > div {
    border-right: 0px !important;
    border-left: 0px !important;
  }
  &.ed-table-wrapper td:first-child > div {
    border-right: 0px !important;
    border-left: 0px !important;
  }
}
.ed-supporting-text {
  font-size: var(--ed-font-size-sm);
  color: var(--ed-gray-6);
}
.ed-font-weight-bold {
  font-weight: var(--ed-font-weight-black);
}
.ed-text-color-warning {
  color: var(--ed-warning-2) !important;
}
.ed-text-color-error {
  color: var(--ed-negative-2) !important;
}
