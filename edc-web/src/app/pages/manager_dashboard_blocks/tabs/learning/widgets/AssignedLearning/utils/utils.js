import { translatr } from 'centralized-design-system/src/Translatr';
import _, { debounce } from 'lodash';
import { Button } from 'centralized-design-system/src/Buttons';
import { Select, SearchInput } from 'centralized-design-system/src/Inputs';
import DatePicker from 'centralized-design-system/src/DatePickers';

export const ASC = 'asc';
export const DESC = 'desc';

/**
 * Creates common select options for filter dropdown
 * @returns {Array} Array of select options
 */
export const getSelectOptions = () => [
  { id: 'all', value: translatr('web.common.main', 'All') },
  { id: 'assigned', value: translatr('web.common.main', 'Assigned') },
  { id: 'self_requested', value: translatr('web.manager-dashboard-v2.main', 'SelfRequested') }
];

/**
 * Creates a debounced search function
 * @param {Function} setSearchQuery - Function to set search query
 * @returns {Function} Debounced search function
 */
export const createDebouncedSearch = setSearchQuery => {
  return debounce(query => setSearchQuery(query), 500);
};

/**
 * Handles pagination events
 * @param {Object} params - Pagination parameters
 * @param {string|number} params.event - Pagination event
 * @param {number} currentPage - Current page number
 * @param {Function} getLearnings - Function to get learnings
 */
export const handlePagination = ({ event }, currentPage, getLearnings) => {
  if (event === 'next') {
    getLearnings(currentPage + 1);
  } else if (event === 'prev') {
    getLearnings(currentPage - 1);
  } else if (Number.isInteger(event)) {
    getLearnings(event);
  }
};

/**
 * Creates a reminder button cell for the table
 * @param {Object} learning - Learning data
 * @param {number} rowIndex - Row index
 * @param {number} colIndex - Column index
 * @param {Object} learningData - Learning data with additional properties
 * @param {Function} sendReminderModalHandler - Handler for send reminder modal
 * @param {string} idField - Field to use as ID
 * @param {string} completedField - Field to check if all completed
 * @param {string} totalField - Field to check total assignments
 * @returns {Object} Cell configuration
 */
export const createReminderCell = (
  learning,
  rowIndex,
  colIndex,
  learningData,
  sendReminderModalHandler,
  idField = 'card_id',
  completedField = 'completed',
  totalField = 'assigned_total'
) => {
  const isAllCompleted = learning[totalField] === learning[completedField];
  const btnLabel = isAllCompleted
    ? translatr('web.manager-dashboard-v2.main', 'AllCompleted')
    : translatr('web.manager-dashboard-v2.main', 'SendReminder');
  return {
    children: (
      <Button
        disabled={isAllCompleted ? true : false}
        color="primary"
        variant="borderless"
        size="medium"
        padding="xsmall"
        id={`send_reminder-${rowIndex}-${colIndex}`}
        aria-labelledby={`send_reminder-${rowIndex}-${colIndex} ${idField}-${rowIndex}-0`}
        onClick={e => sendReminderModalHandler(e, learningData)}
      >
        {btnLabel}
      </Button>
    ),
    id: `send_reminder-${rowIndex}-${colIndex}`,
    align: 'text-right',
    [idField]: learningData[idField]
  };
};

/**
 * Creates table headers based on view type and user info status
 * @param {string} viewType - 'content' or 'team'
 * @param {boolean} user_info_disabled - Whether user info is disabled
 * @param {number} totalCount - Total count of items
 * @returns {Array} Formatted table headers
 */
export const createTableHeaders = (viewType, user_info_disabled, totalCount, updateSortDir) => {
  let baseHeaders = {};

  if (viewType === 'content') {
    baseHeaders = {
      card_title: translatr('web.common.main', 'Assignment'),
      assigned_total: translatr('web.manager-dashboard-v2.main', 'AssignedTo'),
      not_started: translatr('web.manager-dashboard-v2.main', 'NotStarted'),
      due_in_two_weeks_count: translatr('web.manager-dashboard-v2.main', 'DueIn2Weeks'),
      overdue: translatr('web.common.main', 'Overdue'),
      completed: translatr('web.manager-dashboard-v2.main', 'Completed')
    };
  } else {
    baseHeaders = {
      user_details: translatr('web.common.main', 'TeamMember'),
      total_assignments: translatr('web.common.main', 'Assignments'),
      not_started: translatr('web.manager-dashboard-v2.main', 'NotStarted'),
      overdue: translatr('web.common.main', 'Overdue'),
      due_in_two_weeks: translatr('web.manager-dashboard-v2.main', 'DueIn2Weeks'),
      completed: translatr('web.manager-dashboard-v2.main', 'Completed')
    };
  }

  // Add reminder header if user info is enabled
  if (!user_info_disabled) {
    baseHeaders.send_reminder = translatr('web.manager-dashboard-v2.main', 'SendReminder');
  }

  // Format headers for the Table component
  return Object.keys(baseHeaders).map(header => {
    const firstColumnField = viewType === 'content' ? 'card_title' : 'user_details';
    const isFirstColumn = header === firstColumnField;
    const shouldSort = header === 'card_title';

    const label = isFirstColumn
      ? `${baseHeaders[header]} (${totalCount || 0})`
      : header === 'send_reminder'
      ? ' '
      : baseHeaders[header];

    return shouldSort
      ? {
          className: '',
          label,
          id: header,
          align: 'text-left',
          sortable: true,
          onClick: () => updateSortDir()
        }
      : {
          className: '',
          label,
          id: header,
          align: isFirstColumn ? 'text-left' : 'text-right',
          sortable: false
        };
  });
};

/**
 * Renders the table header section with search, filters, and download button
 * @param {Object} props - Component props
 * @returns {JSX.Element} Table header JSX
 */
export const renderTableHeader = ({
  debouncedSearch,
  SelectOption,
  selectedOption,
  onChange,
  dueDate,
  assignedDate,
  placeholderDueDate,
  placeholderAssignedDate,
  showClearDueDate,
  showClearAssignedDate,
  onDueDateChange,
  onAssignedDateChange,
  onClearDueDate,
  onClearAssignedDate,
  tableData,
  handleDownloadReport
}) => (
  <div className="assigned-learning-table__header">
    <div className="search-container">
      <SearchInput
        placeholder={translatr('web.manager-dashboard-v2.main', 'SearchContent')}
        onSearch={debouncedSearch}
        searchOnTextChange={true}
      />
    </div>
    <div className="mr-base mt-2xs">
      <Select
        items={SelectOption}
        onChange={data => onChange(data.value)}
        defaultValue={SelectOption.find(option => option.id === selectedOption)}
        translateDropDownOptions={false}
      />
    </div>
    <div className="date-picker-container mr-base mt-2xs">
      <div className="date-picker-label-container">
        {_.capitalize(translatr('web.manager-dashboard-v2.main', 'DueDateRange'))}
      </div>
      <DatePicker
        singleDatePicker={false}
        opens="right"
        placeHolder={placeholderDueDate}
        onChange={onDueDateChange}
        startDate={dueDate.fromDate ? new Date(dueDate.fromDate) : null}
        endDate={dueDate.toDate ? new Date(dueDate.toDate) : null}
        onClear={showClearDueDate ? onClearDueDate : null}
      />
    </div>
    <div className="date-picker-container mr-base mt-2xs">
      <div className="date-picker-label-container">
        {translatr('web.manager-dashboard-v2.main', 'AssignedOnDateRange') ||
          'Assigned on date range'}
      </div>
      <DatePicker
        singleDatePicker={false}
        opens="left"
        placeHolder={placeholderAssignedDate}
        onChange={onAssignedDateChange}
        startDate={assignedDate.fromDate ? new Date(assignedDate.fromDate) : null}
        endDate={assignedDate.toDate ? new Date(assignedDate.toDate) : null}
        onClear={showClearAssignedDate ? onClearAssignedDate : null}
      />
    </div>
    <Button
      color="secondary"
      variant="ghost"
      className="ed-btn ed-btn-neutral"
      disabled={tableData.length < 1}
      onClick={handleDownloadReport}
    >
      <i className="icon-download" />
      {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
    </Button>
  </div>
);

/**
 * Creates a download report payload based on filters
 * @param {Object} params - Parameters for report
 * @returns {Object} Report payload
 */
export const createReportPayload = ({
  reporteesFilter,
  searchQuery,
  selectedOption,
  dueDate,
  assignedDate
}) => ({
  reporting_type: reporteesFilter.toLowerCase(),
  search_query: searchQuery,
  assignment_type: selectedOption,
  due_date_from: dueDate.fromDate || null,
  due_date_to: dueDate.toDate || null,
  assigned_date_from: assignedDate.fromDate || null,
  assigned_date_to: assignedDate.toDate || null
});

/**
 * Handles the download report process
 * @param {Event} e - Click event
 * @param {Function} downloadFunction - API function to download report
 * @param {Object} payload - Report payload
 * @param {Function} toast - Toast function to display messages
 */
export const handleDownload = async (e, downloadFunction, payload, toast) => {
  const button = e.target;
  button.disabled = true;

  try {
    const response = await downloadFunction(payload);

    // Display response message using toast if available
    if (toast && response?.message) {
      toast(response.message, 'success'); // Added 'success' type parameter
    }

    // If there's a file URL, trigger download
    if (response?.file_url) {
      let a = document.createElement('a');
      a.href = response.file_url;
      a.setAttribute('download', `Report.csv`);
      a.click();
    }
  } catch (err) {
    console.error('Failed to download report:', err);
    if (toast) {
      toast('Failed to download report. Please try again later.', 'error');
    } else {
      alert('Failed to download report. Please try again later.');
    }
  } finally {
    button.disabled = false;
  }
};
