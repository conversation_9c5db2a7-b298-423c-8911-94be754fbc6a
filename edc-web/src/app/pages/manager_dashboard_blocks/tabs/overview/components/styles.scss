@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-dialog-modal-content {
  .reporters-container {
    display: flex;
    flex-direction: column;
    gap: var(--ed-spacing-2xs);
    margin: var(--ed-spacing-base) 0;
    max-height: 31.25rem;
    overflow-y: auto;

    .reporter-list-loading {
      min-height: 3rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    > li {
      display: flex;

      > div {
        display: flex;
        gap: var(--ed-spacing-base);
      }

      .dashed {
        border-radius: 50%;
        border: var(--ed-spacing-5xs) dotted var(--ed-gray-3);
      }

      .reporter-type {
        font-weight: 400;
      }
    }
  }
}
