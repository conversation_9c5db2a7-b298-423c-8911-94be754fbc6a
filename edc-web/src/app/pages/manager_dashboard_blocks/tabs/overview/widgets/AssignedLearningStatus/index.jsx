import { useState, useEffect, useContext, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Button } from 'centralized-design-system/src/Buttons';
import { Select } from 'centralized-design-system/src/Inputs';
import remCalc from 'centralized-design-system/src/Utils/remCalc';
import Switch from 'centralized-design-system/src/Switch';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { getTeamOverviewStats } from 'edc-web-sdk/requests/managerDashboard.v2';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';
import { getErrorMessage } from '@pages/manager_dashboard_blocks/context/helper';
import EmptyStateWidget from '../../components/EmptyStateWidget';
import Loading from '../../components/Loadingwidget';
import { ASSIGNMENT_TYPE_OPTIONS, DATE_RANGE_OPTIONS, getTeamStatsInfo } from '../../helpers';
import '../../overview.scss';

const AssignedLearningStatus = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState({
    dateRangeOptions: [],
    showCompletedAssignments: true,
    selectedAssignmentType: ASSIGNMENT_TYPE_OPTIONS[0].value,
    selectedDateRange: DATE_RANGE_OPTIONS[2]
  });
  const [isEmpty, setIsEmpty] = useState(true);
  const [loading, setLoading] = useState(false);
  const [teamStats, setTeamStats] = useState({});
  const [teamStatsInfo, setTeamStatsInfo] = useState({});

  const mdContext = useContext(MDContext);
  const { reporteesFilter, members, membersLoading, clcOptions, clcLoading } = mdContext;

  const isLoading = useMemo(() => {
    return loading || membersLoading || clcLoading;
  }, [loading, membersLoading, clcLoading]);

  const getOverviewStats = () => {
    setLoading(true);
    getTeamOverviewStats(getPayload)
      .then(resp => {
        updateStates({
          _teamStats: resp || {},
          _teamStatsInfo: getTeamStatsInfo(resp || {})
        });
        setLoading(false);
      })
      .catch(err => {
        updateStates({
          _teamStats: {},
          _teamStatsInfo: {}
        });
        console.error('Error in Manager.getBulkLocationById:', getErrorMessage(err));
        setLoading(false);
      });
  };

  const updateStates = item => {
    const { _teamStats, _teamStatsInfo } = item;
    setTeamStats(_teamStats);
    setTeamStatsInfo(_teamStatsInfo);
  };

  const getPayload = useMemo(() => {
    const { selectedAssignmentType, selectedDateRange, showCompletedAssignments } = filters;
    let payload = {
      type: selectedAssignmentType,
      reporting_type: reporteesFilter.toLowerCase()
    };
    if (showCompletedAssignments) {
      const { value, type } = selectedDateRange || {};
      payload['learning_hr_type'] = type;
      payload['learning_hr_sub_type'] = value;
    }
    return payload;
  }, [filters, reporteesFilter]);

  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      dateRangeOptions: [...DATE_RANGE_OPTIONS, ...clcOptions]
    }));
  }, [clcOptions]);

  useEffect(() => {
    setIsEmpty(!members?.total_count);
  }, [members]);

  useEffect(() => {
    getOverviewStats();
  }, [filters, reporteesFilter]);

  const onSelectAssignmentType = item => {
    setFilters({
      ...filters,
      selectedAssignmentType: item.value
    });
  };

  const onSelectDateRange = item => {
    setFilters({
      ...filters,
      selectedDateRange: item
    });
  };

  const onChangeShowCompletedAssignments = e => {
    setFilters({
      ...filters,
      showCompletedAssignments: e.target.checked
    });
  };

  const renderTeamStats = () => {
    const {
      selectedAssignmentType,
      selectedDateRange,
      showCompletedAssignments,
      dateRangeOptions
    } = filters;
    const {
      overdue_assignments_count,
      due_in_two_weeks_count,
      on_track_count,
      completed_assignments_count
    } = teamStats;

    const isTeamStatsEmpty = Object.keys(teamStats).every(key => !teamStats[key]);
    const the_team_stats = !isTeamStatsEmpty
      ? showCompletedAssignments
        ? teamStatsInfo['completed']
        : teamStatsInfo['notCompleted']
      : { total: 0 };

    const style = isTeamStatsEmpty
      ? {
          '--completed': '0deg',
          '--overdue': '0deg',
          '--duein2wk': '0deg',
          '--ontrack': '0deg',
          '--empty-state': '360deg'
        }
      : showCompletedAssignments
      ? {
          '--completed': `${the_team_stats['completed']}deg`,
          '--overdue': `${the_team_stats['overdue']}deg`,
          '--duein2wk': `${the_team_stats['duein2wk']}deg`
        }
      : {
          '--duein2wk': `${the_team_stats['duein2wk']}deg`,
          '--ontrack': `${the_team_stats['ontrack']}deg`
        };

    return (
      <div className="flex-start team-stats">
        <div className="flex-center top-section">
          <h3 id="teamAssignments" className="header">
            {translatr('web.manager-dashboard-v2.main', 'AssignedLearningStatus') ||
              'Assigned learning status'}
          </h3>
          <Button
            id="teamAsignSeeDetails"
            aria-labelledby="teamAssignments teamAsignSeeDetails"
            color="primary"
            variant="borderless"
            padding="small"
            onClick={() => navigate('/manager-dashboard/learning')}
          >
            {translatr('web.manager-dashboard-v2.main', 'ViewDetail') || 'View detail'}
          </Button>
        </div>
        <div className="flex-center stats-section">
          <div
            className={`flex-center donut-chart-container ${
              isTeamStatsEmpty
                ? 'empty-stats'
                : showCompletedAssignments
                ? 'show_completed'
                : 'hide_completed'
            }`}
            style={style}
          >
            <div className="flex-center donut-inner-circle">
              <span>{the_team_stats['total']}</span>
              <span>{translatr('web.manager-dashboard-v2.main', 'Total') || 'Total'}</span>
            </div>
          </div>
          <div className="flex-start stats-section-actions">
            <div className="stats-select">
              <div id="md-overview-assignment-type">
                <Select
                  title={
                    translatr('web.manager-dashboard-v2.main', 'AssignmentType') ||
                    'Assignment type'
                  }
                  id="assignment-type"
                  ariaLabel={
                    translatr('web.manager-dashboard-v2.main', 'SelectAssignedType') ||
                    'SELECT ASSIGNED TYPE'
                  }
                  defaultValue={selectedAssignmentType}
                  onChange={onSelectAssignmentType}
                  items={ASSIGNMENT_TYPE_OPTIONS}
                />
              </div>
              {showCompletedAssignments && (
                <div id="md-overview-date-range">
                  <Select
                    title={translatr('web.manager-dashboard-v2.main', 'DateRange') || 'Date range'}
                    id="date-range"
                    ariaLabel={
                      translatr('web.manager-dashboard-v2.main', 'SelectDataRange') ||
                      'SELECT DATA RANGE'
                    }
                    defaultValue={selectedDateRange.value}
                    onChange={onSelectDateRange}
                    items={dateRangeOptions}
                  />
                </div>
              )}
            </div>
            <div className="flex-start stats-toggle-switch">
              <Switch
                ariaHidden={false}
                ariaLabel={
                  translatr('web.manager-dashboard-v2.main', 'ShowCompletedAssignments') ||
                  'Show completed assignments'
                }
                role={'switch'}
                ariaChecked={true}
                defaultChecked={showCompletedAssignments}
                onChange={e => onChangeShowCompletedAssignments(e)}
              />
              <div className="toggle-switch-label">
                {translatr('web.manager-dashboard-v2.main', 'ShowCompletedAssignments') ||
                  'Show completed assignments'}
              </div>
            </div>
            <div className="flex-start stats-summay-view">
              <div>
                <span className="icon-color-swatch" style={{ color: '#DE350B' }} />
                <span className="supporting-text">
                  {`${translatr('web.manager-dashboard-v2.main', 'Overdue') || 'Overdue'}`}
                </span>
                <span className="supporting-bold-text">
                  {' '}
                  {`(${overdue_assignments_count || 0})`}
                </span>
              </div>
              <div>
                <span className="icon-color-swatch" style={{ color: '#FFAB00' }} />
                <span className="supporting-text">
                  {`${translatr('web.manager-dashboard-v2.main', 'DueIn2Weeks') ||
                    'Due in 2 weeks'}`}
                </span>
                <span className="supporting-bold-text"> {`(${due_in_two_weeks_count || 0})`}</span>
              </div>
              <Tooltip
                message={
                  translatr('web.manager-dashboard-v2.main', 'OnTrackTooltip') ||
                  'On Track is calculated after removing Total Completed, Overdue and Due in 2 Weeks from Total Assigned Content'
                }
                tooltipCardInlineCss={{ 'max-width': remCalc(300) }}
              >
                <div>
                  <span className="icon-color-swatch" style={{ color: '#005BF0' }} />
                  <span className="supporting-text">{`${translatr(
                    'web.manager-dashboard-v2.main',
                    'OnTrack'
                  ) || 'On track'}`}</span>
                  <span className="supporting-bold-text"> {`(${on_track_count || 0})`}</span>
                </div>
              </Tooltip>
              {showCompletedAssignments && (
                <div>
                  <span className="icon-color-swatch" style={{ color: '#14B872' }} />
                  <span className="supporting-text">{`${translatr(
                    'web.manager-dashboard-v2.main',
                    'Completed'
                  ) || 'On track'}`}</span>
                  <span className="supporting-bold-text">
                    {' '}
                    {`(${completed_assignments_count || 0})`}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="overview-block-container">
      {isLoading && <Loading />}
      {!isLoading && isEmpty && (
        <>
          <h3 id="teamAssignments" className="header">
            {translatr('web.manager-dashboard-v2.main', 'AssignedLearningStatus') ||
              'Assigned learning status'}
          </h3>
          <EmptyStateWidget
            icon="icon-file"
            headline={`${translatr('web.manager-dashboard-v2.main', 'NoAssignedLearning') ||
              'No assigned learning'}`}
            subtext={`${translatr(
              'web.manager-dashboard-v2.main',
              'ryAssigningLearningToYourTeamToTrackStatusHere'
            ) || 'Try assigning learning to your team to track status here'}`}
          />
        </>
      )}
      {!isLoading && !isEmpty && renderTeamStats()}
    </div>
  );
};

export default AssignedLearningStatus;
