import { useState, useEffect, useContext, useMemo } from 'react';
import { debounce } from 'lodash';
import { translatr } from 'centralized-design-system/src/Translatr';
import Avatar from 'centralized-design-system/src/MUIComponents/Avatar';
import { Button, ButtonLink } from 'centralized-design-system/src/Buttons';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { getBulkLocationById, getAllCountries } from 'edc-web-sdk/requests/hrData.v2';
import { getMDConfig } from 'edc-web-sdk/requests/managerDashboard.v2';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { getReportersInfo } from '../../../../../manager_dashboard/utils';
import TeamListModal from '../../components/TeamListModal';
import EmptyStateWidget from '../../components/EmptyStateWidget';
import Loading from '../../components/Loadingwidget';
import { MAX_AVATAR_NUMBER, MIN_AVATAR_NUMBER, MAX_TEAMVIEW_WIDTH } from '../../helpers';
import '../../overview.scss';
import { MDContext } from '@pages/manager_dashboard_blocks/context/MDContext';
import { getErrorMessage } from '@pages/manager_dashboard_blocks/context/helper';

const MyTeam = () => {
  let initAvatarNum =
    window?.innerWidth >= MAX_TEAMVIEW_WIDTH ? MAX_AVATAR_NUMBER : MIN_AVATAR_NUMBER;
  const [countries, setCountries] = useState({});
  const [initLoading, setInitLoading] = useState(true);
  const [mdConfig, setMDConfig] = useState({});
  const [teamMembers, setTeamMembers] = useState([]);
  const [numAvatarsToShow, setNumAvatarsToShow] = useState(initAvatarNum);
  const [openReportersModal, setOpenReportersModal] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [wkLocations, setWKLocations] = useState([]);
  const mdContext = useContext(MDContext);
  const {
    setSelectedReportee,
    members,
    membersLoading,
    setSearchReporterTerm,
    setShouldFetchMembers
  } = mdContext;
  const currentUserLang = window.__ED__.profile?.language || 'en';
  const resizeHandler = debounce(() => {
    setNumAvatarsToShow(
      window?.innerWidth > MAX_TEAMVIEW_WIDTH ? MAX_AVATAR_NUMBER : MIN_AVATAR_NUMBER
    );
  }, 300);
  const isLoading = useMemo(() => {
    return membersLoading || initLoading;
  }, [membersLoading, initLoading]);

  async function init() {
    setInitLoading(true);
    const resps = await Promise.allSettled([getMDConfig(), getAllCountries(currentUserLang)]);
    setInitLoading(false);
    resps.forEach((resp, i) => {
      switch (i) {
        case 0:
          if (resp.status === 'fulfilled') {
            setMDConfig(resp?.value || {});
          } else {
            console.error('Error in Manager.getMDConfig: ', getErrorMessage(resp));
          }
          break;
        case 1:
          if (resp.status === 'fulfilled') {
            const all_countries = {};
            (resp?.value?.data || [])
              .filter(item => item && item.enable && item.id && item.label)
              .forEach(item => {
                const { id, label } = item;
                all_countries[id] = label;
              });
            setCountries(all_countries);
          } else {
            console.error('Error in Manager.getAllCountries: ', getErrorMessage(resp));
          }
          break;
        default:
          break;
      }
    });
  }

  useEffect(() => {
    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  }, []);

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    const reporters = getReportersInfo(members?.data || [], mdConfig || {});
    const the_count = members?.total_count || 0;
    const locationIds = reporters
      .map(reporter => {
        return reporter.workLocationId;
      })
      .filter(id => id?.length);

    setTeamMembers(reporters);
    setTotalCount(the_count);
    if (!!locationIds.length) {
      getBulkLocationById({ ids: locationIds })
        .then(locations => {
          const the_locations = {};
          (locations?.data || []).forEach(loc => {
            const { id, country } = loc;
            the_locations[id] = country;
          });
          setWKLocations(the_locations || []);
        })
        .catch(err1 => {
          console.error('Error in Manager.getBulkLocationById:', getErrorMessage(err1));
        });
    }
  }, [members]);

  // Memoize the subset of team members to display to prevent unnecessary re-renders
  const avatarsToShow = useMemo(() => [...teamMembers].slice(0, numAvatarsToShow), [
    teamMembers,
    numAvatarsToShow
  ]);

  const searchHandler = () => {
    setSearchReporterTerm('');
    setShouldFetchMembers(true);
    setOpenReportersModal(true);
  };

  function renderMemebers() {
    const showMore = totalCount > numAvatarsToShow;
    const x_more = showMore ? totalCount - numAvatarsToShow : 0;

    return (
      <div className="team_members-container">
        <ul className="team_members-list">
          {avatarsToShow.map((member, index) => {
            const { id, tiny, name, job, isClickable, workLocationId, reportingType } = member;
            const msg1 =
              reportingType === 'in_direct'
                ? `${name} | ${translatr('web.manager-dashboard-v2.main', 'IndirectReport') ||
                    'Indirect report'}`
                : name;
            const msg2 =
              workLocationId && !!wkLocations[workLocationId]
                ? job
                  ? `${job} - ${countries[wkLocations[workLocationId]]}`
                  : countries[wkLocations[workLocationId]]
                : job;

            return (
              <li
                key={index}
                className={`team_members-item ${
                  (reportingType || '') === 'in_direct' ? 'dashed' : ''
                }`}
                style={{ '--item-index': index }}
              >
                <Tooltip
                  message={
                    <>
                      {[msg1, msg2].map((msg, index1) => {
                        return (
                          <div
                            key={index1}
                            dangerouslySetInnerHTML={{ __html: safeRender(msg) }}
                          ></div>
                        );
                      })}
                    </>
                  }
                  disabled={!isClickable}
                >
                  <ButtonLink
                    color="secondary"
                    variant="borderless"
                    size="medium"
                    padding="0"
                    disabled={!isClickable}
                    handleClick={() => setSelectedReportee(member)}
                  >
                    <Avatar className={`avatar-${id}`} user={{ avatar: tiny, name: name }} />
                  </ButtonLink>
                </Tooltip>
              </li>
            );
          })}
          {showMore && (
            <Tooltip message={translatr('web.common.main', 'ViewMore')}>
              <div className="more-user">
                <Button
                  color="secondary"
                  variant="borderless"
                  size="medium"
                  padding="0"
                  aria-label={translatr('web.manager-dashboard-v2.main', 'SearchTeamMembers')}
                  onClick={() => setOpenReportersModal(true)}
                >
                  +{x_more}
                </Button>
              </div>
            </Tooltip>
          )}
        </ul>
        <div>
          <Button
            color="secondary"
            variant="ghost"
            size="medium"
            padding="small"
            aria-label={translatr('web.manager-dashboard-v2.main', 'SearchTeamMembers')}
            onClick={searchHandler}
          >
            <i className="icon-search" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`overview-block-container overview-container ${
        isLoading || !!totalCount ? '' : 'empty-widge'
      }`}
    >
      <h3 className="header">
        {`${translatr('web.manager-dashboard-v2.main', 'MyTeam') || 'My team'}`}
      </h3>
      {isLoading && <Loading />}
      {!isLoading && totalCount === 0 && (
        <EmptyStateWidget
          icon="icon-users-fill"
          headline={`${translatr('web.manager-dashboard-v2.main', 'NoTeamMembersFound') ||
            'No team members found'}`}
          subtext={`${translatr(
            'web.manager-dashboard-v2.main',
            'TryReachingOutToYourplatformAdminToSetUpYourTeam'
          ) || 'Try reaching out to your platform admin to set up your team.'}`}
        />
      )}
      {!isLoading && totalCount > 0 && renderMemebers()}
      {openReportersModal && (
        <TeamListModal
          config={mdConfig}
          locations={wkLocations}
          countriesMap={countries}
          handleOpen={setOpenReportersModal}
        />
      )}
    </div>
  );
};

export default MyTeam;
