@import '~centralized-design-system/src/Styles/_variables.scss';
.overview-block-container {
  display: flex;
  align-items: start;
  justify-content: start;
  width: 100%;
  padding: var(--ed-card-padding-x) !important;
  background-color: var(--ed-card-bg-color);
  border-radius: var(--ed-card-border-radius);
  box-sizing: border-box;
  flex-direction: column;
  gap: var(--ed-spacing-base);
  box-shadow: var(--ed-shadow-sm);
}

.overview-container {
  min-height: rem-calc(120);

  .team_members-container {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    .team_members-list {
      display: flex;
      gap: 0;
      margin-left: 0 !important;
      align-items: center;
      height: 2.5rem;

      .team_members-item {
        position: relative;
        z-index: var(--item-index, 0);
        &:not(:first-of-type) {
          margin-left: -0.5rem;
        }
        .ed-btn-v2 {
          border: none !important;
        }
      }
      .dashed {
        border-radius: 50%;
        border: var(--ed-spacing-5xs) dotted var(--ed-gray-3);
      }
      .more-user {
        position: relative;
        height: 2.5rem;
        width: 2.5rem;
      }
      button {
        display: block;
        position: absolute;
        height: 2.5rem;
        width: 2.5rem;
        border: var(--ed-button-border-size) solid var(--ed-gray-4);
        border-radius: var(--ed-border-radius-circle);
        z-index: 100;
        right: 0.5rem;
        color: var(--ed-white);
        background: var(--ed-black);
      }
    }
  }
}

.team-stats {
  flex-direction: column;
  gap: var(--ed-spacing-base);
  width: 100%;

  .top-section {
    justify-content: space-between;
    width: 100%;
  }

  .stats-section {
    justify-content: start;
    gap: var(--ed-spacing-base);
    width: 100%;
    min-height: rem-calc(265);

    .donut-chart-container {
      width: rem-calc(240);
      height: rem-calc(240);
      border-radius: 50%;
      justify-content: center;
      flex-shrink: 0;

      .donut-inner-circle {
        width: 70%;
        height: 70%;
        border-radius: inherit;
        background: var(--ed-card-bg-color);
        font-size: var(--ed-font-size-2xs);
        color: var(--ed-gray-5);
        justify-content: center;
        flex-direction: column;
        > span:first-of-type {
          font-weight: var(--ed-font-weight-black);
          font-size: var(--ed-font-size-xl);
          color: var(--ed-text-color-primary);
        }
      }
    }

    .donut-chart-container.empty-stats {
      background: conic-gradient(#e6e6e6 var(--empty-state, 360deg));
    }

    .donut-chart-container.empty-stats .donut-inner-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: var(--ed-white);
      border-radius: 50%;
      width: 70%;
      height: 70%;
    }

    .show_completed {
      background: conic-gradient(
        #14b872 var(--completed),
        #de350b var(--completed) var(--overdue),
        #ffab00 var(--overdue) var(--duein2wk),
        #005bf0 var(--duein2wk)
      );
    }
    .hide_completed {
      background: conic-gradient(
        #ffab00 var(--duein2wk),
        #005bf0 var(--duein2wk) var(--ontrack),
        #de350b var(--ontrack)
      );
    }
    .stats-section-actions {
      width: rem-calc(463);
      height: 100%;
      flex-direction: column;
      gap: var(--ed-spacing-xl);
      padding-top: var(--ed-spacing-xl);
      padding-bottom: var(--ed-spacing-xl);
      padding-right: 1rem;

      .stats-select {
        display: flex;
        align-items: start;
        justify-content: start;
        gap: var(--ed-spacing-base);
        max-height: 3.8rem;
        padding-right: 1rem;
        @include max-screen-width(710px) {
          max-height: 8.55rem;
          flex-direction: column;
        }
        .ed-input-container {
          margin-top: 0 !important;
          width: rem-calc(200) !important;
          .ed-input-title {
            font-size: var(--ed-font-size-supporting) !important;
            font-weight: var(--ed-font-weight-bold) !important;
            color: var(--ed-gray-5) !important;
          }
          > div {
            > span:last-of-type {
              font-size: var(--ed-font-size-base) !important;
              font-weight: var(--ed-font-weight-normal);
              color: var(--ed-text-color-primary);
            }
          }
        }
      }
      .stats-toggle-switch {
        gap: var(--ed-spacing-sm);
        .switch input[type='checkbox']:checked ~ span:after {
          left: calc(100% + 3.175rem);
        }
        .switch input[type='checkbox']:checked ~ span:before {
          background-color: #005bf0;
        }
        .switch span {
          margin-left: -6.5rem;
          &::before {
            width: 3rem;
            height: 1.5rem;
            background-color: var(--ed-gray-4);
          }
          &::after {
            width: var(--ed-font-size-base);
            height: var(--ed-font-size-base);
            border-radius: rem-calc(100);
            background-color: var(--ed-switch-on-indicator-color);
          }
        }
        .toggle-switch-label {
          font-size: var(--ed-font-size-supporting);
          font-weight: var(--ed-font-weight-bold);
          color: var(--ed-gray-5);
          padding-left: var(--ed-spacing-xs);
          height: 1.5rem;
          display: flex;
          align-items: center;
        }
      }
      .stats-summay-view {
        max-width: rem-calc(463);
        height: rem-calc(21);
        flex-wrap: wrap;
        gap: var(--ed-spacing-sm);

        .icon-color-swatch::before {
          padding-right: var(--ed-spacing-2xs);
        }
      }
    }
  }
}

@include max-screen-width(320px) {
  .team-stats {
    width: 100%;

    .stats-section {
      flex-direction: column;
      align-items: center;
      max-height: none;
      height: auto;

      .donut-chart-container {
        width: rem-calc(180);
        height: rem-calc(180);
      }

      .stats-section-actions {
        width: 100% !important;
        padding: var(--ed-spacing-xs) 0;
        height: auto;
        max-height: none;
        overflow-y: visible;

        .stats-select {
          display: flex !important;
          flex-direction: column !important;
          width: 100% !important;
          max-height: none !important;
          overflow: visible !important;
          max-height: none;
          overflow-y: visible;
          height: auto;

          #md-overview-assignment-type,
          #md-overview-date-range {
            width: 100% !important;
            margin-bottom: var(--ed-spacing-sm);
          }
          .ed-input-container {
            display: block !important;
            width: 100% !important;
            min-width: 100% !important;
            max-width: 100% !important;
          }
        }

        .stats-toggle-switch {
          margin-top: var(--ed-spacing-xs);
          width: 100%;

          .switch {
            min-width: 3rem;
            padding-left: var(--ed-spacing-base);
          }
          .toggle-switch-label {
            font-size: var(--ed-font-size-2xs);
          }
        }

        .stats-summay-view {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          height: auto;
          width: 100%;
          gap: var(--ed-spacing-xs);
          justify-content: flex-start;
          align-items: flex-start;

          div {
            display: flex;
            align-items: center;
            width: auto;
            margin-bottom: var(--ed-spacing-xs);
            justify-content: flex-start;
            margin-right: auto;
            text-align: left;

            .icon-color-swatch {
              flex-shrink: 0;
              text-align: left;
              &::before {
                padding-right: var(--ed-spacing-4xs);
              }
            }
            .supporting-text,
            .supporting-bold-text {
              white-space: nowrap;
            }
          }
        }
      }
    }

    .top-section {
      align-items: center;
      justify-content: space-between;
      .header {
        font-size: var(--ed-font-size-sm) !important;
      }
      #teamAsignSeeDetails {
        font-size: var(--ed-font-size-xs);
      }
    }
  }
}

.flex-start {
  display: flex;
  align-items: start;
  justify-content: start;
}
.flex-center {
  display: flex;
  align-items: center;
}
.header {
  font-size: var(--ed-font-size-lg) !important;
  color: var(--ed-text-color-primary);
  font-weight: var(--ed-font-weight-black);
  margin: 0;
}
.empty-widge {
  min-height: 300px !important;
  margin-bottom: var(--ed-card-padding-x);
}
.loading-container {
  align-self: center;
}
.supporting-text {
  font-size: var(--ed-font-size-supporting);
  color: var(--ed-gray-6);
  font-weight: var(--ed-font-weight-normal);
  line-height: var(--ed-line-height-xs);
}
.supporting-bold-text {
  font-size: var(--ed-font-size-supporting);
  color: var(--ed-body-color);
  font-weight: var(--ed-font-weight-black);
  line-height: rem-calc(19);
}
