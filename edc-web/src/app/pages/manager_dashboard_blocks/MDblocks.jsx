import { useEffect, useState, useContext } from 'react';
import { Route } from 'react-router-dom';

// Components
import { translatr } from 'centralized-design-system/src/Translatr';
import { Select } from 'centralized-design-system/src/Inputs';
import TabBar from 'centralized-design-system/src/TabBar';
import { TABS } from '@components/modals/MultiActionModal/constant';

import { RootRoutes } from '../../Router';
import Overview from './tabs/overview';
import Skills from './tabs/skills';
import Learning from './tabs/learning';
import Individual from './tabs/individual';
import { MDContext } from './context/MDContext';
import './styles.scss';

const defaultTabs = [
  {
    name: 'Overview',
    link: '/manager-dashboard'
  },
  {
    name: 'Learning',
    link: '/manager-dashboard/learning'
  }
];

const reporteesTypeOptions = [
  { label: translatr('web.common.main', 'All'), value: 'All' },
  { label: TABS().DIRECT_REPORTS, value: 'Direct' },
  {
    label: TABS().INDIRECT_REPORTS,
    value: 'Indirect'
  }
];

// Helper function to get configuration value by name
function getConfigValue(name) {
  return global?.__edOrgData?.configs?.find(f => f.name === name)?.value;
}

const HR_DATA_SERVICE_ENABLEMENT = getConfigValue('hr_data_service_enablement');
const SKILLS_ASSESSMENT_ENABLEMENT = getConfigValue('skills_assessment_enablement');
const MD_TEAM_SKILLS_ASSESSMENT = getConfigValue('md_configuration')?.show_skill_assessment;

export default function ManagerDashboardBlocks() {
  const context = useContext(MDContext);
  const [myTabs, setMyTabs] = useState(defaultTabs);
  const [reporteesFilter, setReporteesFilter] = useState(context.reporteesFilter || 'All');

  useEffect(() => {
    context.setReporteesFilter(reporteesFilter);
    context.setSearchReporterTerm('');
    context.setShouldFetchMembers(true);
  }, [reporteesFilter]);

  const showSecondaryManager =
    global?.__edOrgData?.configs?.find(f => f.name === 'md_configuration')?.value
      ?.show_secondary_manager && window?.__ED__?.isSecondaryManager;

  const pathname = window.location.pathname;

  const { mfeSkillsEnabled } = context;

  const SHOW_TEAM_MATRIX =
    HR_DATA_SERVICE_ENABLEMENT && SKILLS_ASSESSMENT_ENABLEMENT && MD_TEAM_SKILLS_ASSESSMENT;

  useEffect(() => {
    if (SHOW_TEAM_MATRIX) {
      setMyTabs([
        ...myTabs,
        {
          name: 'Skills',
          link: '/manager-dashboard/skills'
        }
      ]);
    }
  }, []);

  const showReporteeDD =
    (showSecondaryManager &&
      (pathname === '/manager-dashboard' || pathname === '/manager-dashboard/learning')) ||
    (showSecondaryManager && pathname === '/manager-dashboard/skills' && !mfeSkillsEnabled);

  const notOnIndividualPage = !pathname.includes('/manager-dashboard/individual/');

  const onReporteesFilter = item => {
    setReporteesFilter(item?.value || 'All');
  };

  const renderHeader = () => (
    <div className="mt-32 flex-space-between">
      <h1 className="title">{translatr('web.common.main', 'ManagerDashboard')}</h1>
      {showReporteeDD && renderSelect()}
    </div>
  );

  const renderSelect = () => (
    <div className="report-type-container">
      <Select
        title={translatr('web.common.main', 'ReportType') || 'Report type'}
        defaultValue={reporteesFilter}
        onChange={onReporteesFilter}
        items={reporteesTypeOptions}
        aria-label={translatr('web.common.main', 'Select')}
      />
    </div>
  );

  const renderRoutes = () => (
    <RootRoutes>
      <Route path="/manager-dashboard" element={<Overview reporteesFilter={reporteesFilter} />} />
      <Route path="/manager-dashboard/learning" element={<Learning />} />
      <Route path="/manager-dashboard/skills" element={<Skills />} />
      <Route path="/manager-dashboard/individual/:handle" element={<Individual />} />
    </RootRoutes>
  );

  return (
    <div className="ed-ui wrapper manager-dashboard-blocks">
      {notOnIndividualPage && renderHeader()}
      {notOnIndividualPage && <TabBar tabs={myTabs} className="mb-16" />}
      {renderRoutes()}
    </div>
  );
}
