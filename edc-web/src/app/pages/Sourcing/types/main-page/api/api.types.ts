import { JobFamilyFilter, JobVacancy } from '../../manage-page/api/api.types';
import { Capability } from '../../manage-page/api/common.types';

export interface JobVacancyData {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalPages: number
  values: JobVacancy[],
  capabilities: Capability[]
  jobFamilyId: string
}

export interface Country {
  id: string
  label: string
  enable: boolean
  index: number
}

export interface NumberObject {
    [key: number]: number; 
}
export interface OpportunityAvailableFilters {
  organizations: Division[]
  countryCodes: {
    [key: string]: number
  }
  jobStartDateRange: NumberObject
  jobEndDateRange: NumberObject,
  linkedRoleStatus: NumberObject,
  jobFamily: JobFamilyFilter[],
  locations: NumberObject
}

export interface Division {
  id: string
  parentId: string
  externalId: string
  costCenterId: string
  title: string
  orgType: string
  hasChild: boolean
  path: string
  status: string
  level: number
  effectiveDate: string
  createdOn: string
  updatedOn: string
  approvalTeam1: any[]
  approvalTeam2: any[]
  keyValues?: {
    index: number
    enable: string
    default: string
  }
}

export type OrgType = Country;

export interface JobFamilyAndFunction {
  jobFamilyId?: string
  jobFamilyName?: string
  jobFunctionId?: string
  jobFunctionName?: string
}
export interface SourcingFilterData {
  countryCodes: Record<string, number>
  organizations: Division[]
  jobStartDateRange: NumberObject
  jobEndDateRange: NumberObject
  linkedRoleStatus: Record<string, number>
  jobFamily: JobFamilyAndFunction
}

type JobStatus = "OPEN" | "MY_PUBLISHED_VACANCIES";

export interface JobVacancyRequestBody {
  pageSize: number
  sortType: string
  sortOrder: string
  jobStatus: JobStatus
  language: string
  type: string
  keyword: string
  pageNumber?: number
}
