import { Item } from "../api/common.types"

export enum FILTER_TYPE {
  LIST = 'LIST',
  LIST_WITH_ACCORDION = 'LIST_WITH_ACCORDION',
  DATE_PICKER = 'DATE_PICKER'
}

export type SearchType = 'INTERNAL' | 'EXTERNAL_SKILLS' | 'EXTERNAL_JOB_ROLE' | 'NONE';

export enum FILTER_GROUPS {
  ORG_UNIT = 'org_unit'
}

export enum FilterSection {
  MainMenu = 'MainMenu',
  Default = 'default',
  MenuList = 'MenuList'
}

export interface CandidateFilterItem {
  filterId: string;
  filterName: string;
  smallLabel?: string;
  filterItems: FilterItem[];
  filterType: FILTER_TYPE;
  filterGroup?: FILTER_GROUPS;
  searchType: string;
}

export interface PreviewFilterItem {
  filterName: string,
  tagInfo: TagInfo[],
  filterId: string,
  filterType: FILTER_TYPE,
  filterGroup: FILTER_GROUPS
}

export interface TagInfo {
  id: string,
  label: string,
  filterTagText: string,
  selectedSubFilterDetails: SubFilter,
  additionalInfo: any
};

export interface SubFilter {
  items?: Item[]
  options?: Item[]
}
export interface FilterItem {
  id: string
  label: string
  isSelected: boolean
  subFilters?: {
    items?: Item[]
    options?: Item[]
  }
  dateRange?: {
    fromDate?: Date
    toDate?: Date
    maxDate?: Date | object,
    minDate?: Date | object
  }
  additionalInfo?: {
    isDisabled?: boolean
    disabledMessage?: string
    [key: string]: string | number | boolean | object
  }
}

export interface FilterItemError {
  filterId: string
  filterItems: [{
    filterItemId: string
    message: string
    type?: 'warning'
  }]
}

export interface JobFamilyAndFunction {
  jobFamilyId?: string
  jobFamilyName?: string
  jobFunctionId?: string
  jobFunctionName?: string
}

export interface SavedFilters {
  id: string
  filterName: string
  filterType: string
  filterCriteria: PreviewFilterItem[]
  permissions: Permissions
  filterOwner: string
  filterStatus: string,
  filterConfiguration?: {
    isShowPartialMatchEnabled: boolean
  }
}
interface Permissions {
  isPublic: boolean
  users?: string[]
}

export interface PositionDimensions {
  top: string,
  left: string
}

export interface SelectItem {
  id: string,
  value: string
}

export interface MasterSwitch {
  label: string,
  value: boolean, 
  requestBodyKey: string
}

export interface CurrentFilterSettings {
  filterCriteria: PreviewFilterItem[],
  masterSwitchValue: boolean
}