import { CandidateFilterItem, CurrentFilterSettings, FilterItemError, MasterSwitch, PositionDimensions, PreviewFilterItem, SavedFilters, SelectItem } from "../candidate-filter/filter.types";

export interface State {
    candidateFilterList: CandidateFilterItem[],
    interestsLabel: string,
    selectedFilterInfo: CandidateFilterItem,
    isFilterMenuOpen: boolean,
    showFilterListOnPlus: boolean,
    previewFilterItems: PreviewFilterItem[],
    candidateFilterListCopy: CandidateFilterItem[],
    expandedFilter: boolean,
    showFilterPreview: boolean,
    isFilterListLoading: boolean,
    showMenuDetails: boolean,
    filterErrors: FilterItemError[],
    savedFilters: SavedFilters[],
    savedFiltersDefaultValue: SelectItem,
    filterMenuOpeningPosition: PositionDimensions,
    savedFiltersDropItems: SelectItem[],
    savedSearchFilterType: string,
    currentFilterSettings: CurrentFilterSettings,
    masterSwitch: MasterSwitch,
    masterSwitchInitialState: MasterSwitch
}

export enum CANDIDATE_FILTER_ACTION_TYPES {
    SET_CANDIDATE_FILTERS_LIST = 'SET_CANDIDATE_FILTERS_LIST',
    SET_SELECTED_FILTER_INFO = 'SET_SELECTED_FILTER_INFO',
    SET_FILTER_MENU_OPEN = 'SET_FILTER_MENU_OPEN',
    SET_SHOW_FILTER_LIST_ON_PLUS = 'SET_SHOW_FILTER_LIST_ON_PLUS',
    SET_PREVIEW_FILTER_ITEMS = 'SET_PREVIEW_FILTER_ITEMS',
    SET_CANDIDATE_FILTERS_LIST_COPY = 'SET_CANDIDATE_FILTERS_LIST_COPY',
    RESET_CANDIDATE_FILTERS_LIST = 'RESET_CANDIDATE_FILTERS_LIST',
    SET_EXPANDED_FILTER = 'SET_EXPANDED_FILTER',
    SET_SHOW_FILTER_PREVIEW = 'SET_SHOW_FILTER_PREVIEW',
    SET_IS_FILTER_LIST_LOADING = 'SET_IS_FILTER_LIST_LOADING',
    SET_SHOW_MENU_DETAILS = 'SET_SHOW_MENU_DETAILS',
    SET_FILTER_ERRORS = 'SET_FILTER_ERRORS',
    SET_SAVED_FILTERS = 'SET_SAVED_FILTERS',
    SET_SAVED_FILTERS_DEFAULT_VALUE = 'SET_SAVED_FILTERS_DEFAULT_VALUE',
    SET_FILTER_MENU_OPENING_POSITION = 'SET_FILTER_MENU_OPENING_POSITION',
    SET_SAVED_FILTERS_DROPDOWN_ITEMS = 'SET_SAVED_FILTERS_DROPDOWN_ITEMS',
    SET_SAVED_SEARCH_FILTER_TYPE = 'SET_SAVED_SEARCH_FILTER_TYPE',
    SET_CURRENT_FILTER_SETTINGS = 'SET_CURRENT_FILTER_SETTINGS',
    SET_MASTER_SWITCH_DETAILS = 'SET_MASTER_SWITCH_DETAILS',
    SET_MASTER_SWITCH_INITIAL_STATE = 'SET_MASTER_SWITCH_INITIAL_STATE'
}

export interface SetCandidateFilterList {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST,
    candidateFilterList: CandidateFilterItem[]
}

export interface SetCandidateFilterListDeepCopy {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST_COPY,
    candidateFilterListCopy: CandidateFilterItem[]
}

export interface SetIsFilterListLoading {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_IS_FILTER_LIST_LOADING,
    isFilterListLoading: boolean
}

export interface SetSelectedFilterState {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SELECTED_FILTER_INFO,
    selectedFilterInfo: CandidateFilterItem
}

export interface SetFilterMenuOpen {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_MENU_OPEN,
    isFilterMenuOpen: boolean
}

export interface SetFilterMenuDetailsPanel {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_MENU_DETAILS,
    showMenuDetails: any
}

export interface SetFilterListStateOnPlusButton {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_FILTER_LIST_ON_PLUS,
    showFilterListOnPlus: boolean
}

export interface SetExpandedFilter {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_EXPANDED_FILTER,
    expandedFilter: boolean
}

export interface ResetCandidateFilterList {
    type: CANDIDATE_FILTER_ACTION_TYPES.RESET_CANDIDATE_FILTERS_LIST
}

export interface SetPreviewFilterItems {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_PREVIEW_FILTER_ITEMS,
    previewFilterItems: PreviewFilterItem[]
}

export interface SetShowPreviewFilter {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_FILTER_PREVIEW,
    showFilterPreview: boolean
}

export interface SetFilterErrors {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_ERRORS,
    filterErrors: FilterItemError[]
}

export interface SetSavedFilters {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS,
    savedFilters: SavedFilters[]
}

export interface SetSavedFiltersDefaultValue {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS_DEFAULT_VALUE,
    savedFiltersDefaultValue: SelectItem
}

export interface SetFilterMenuOpeningPosition {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_MENU_OPENING_POSITION,
    filterMenuOpeningPosition: PositionDimensions
}

export interface SetSavedFiltersDropItems {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS_DROPDOWN_ITEMS,
    savedFiltersDropItems: SelectItem[]
}

export interface SetSavedSearchFilterType {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_SEARCH_FILTER_TYPE,
    savedSearchFilterType: string
}

export interface setCurrentFilterSettings {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_CURRENT_FILTER_SETTINGS,
    currentFilterSettings: CurrentFilterSettings
}

export interface SetMasterSwitchDetails {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_DETAILS,
    masterSwitch: MasterSwitch
}

export interface SetMasterSwitchInitialState {
    type: CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_INITIAL_STATE,
    masterSwitchInitialState: MasterSwitch
}

export type Action = SetCandidateFilterList | SetCandidateFilterListDeepCopy | SetIsFilterListLoading | SetSelectedFilterState
    | SetFilterMenuOpen | SetFilterMenuDetailsPanel | SetFilterListStateOnPlusButton | SetExpandedFilter | ResetCandidateFilterList
    | SetPreviewFilterItems | SetShowPreviewFilter | SetFilterErrors | SetSavedFilters | SetSavedFiltersDefaultValue
    | SetFilterMenuOpeningPosition | SetSavedFiltersDropItems | SetSavedSearchFilterType | setCurrentFilterSettings
    | SetMasterSwitchDetails | SetMasterSwitchInitialState;
