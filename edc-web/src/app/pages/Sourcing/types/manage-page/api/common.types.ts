export interface Location {
  id?: string,
  locationId: string | null
  city: string
  country: string
  countryCode: string
  primary: boolean,
  location_name: string,
  externalLocationId: string
}

export interface LocationConfig {
  enable: boolean,
  association: string
}

export interface Description {
  title: string
  language: string
  isDefault: boolean
}

export interface Capability {
  id: string
  type: string
  name: string
  skillSourceId: string
  status: string
  label: string
  levelProficiency: number
}

export interface ProfileAttribute {
  language: string
  jobTitle: string
}

export interface Language {
  code: string
  name: string
  description: string
}

export interface Range {
  from: string
  to: string
}

export interface Item {
  id: number | string
  value?: string
  label: string
  range?: Range
  level?: string,
  isSelected?: boolean,
  isMissingFromConfig?: boolean
}

export interface ConfigValue {
  key: string
  defaultLabel: string
  label?: string
  rank: {
    label: string
  }
}

export enum CANDIDATE_FILTER_SECTION_KEY {
  MATCHING_PROFILES = 'MATCHING_PROFILES',
  SHORTLISTED_PROFILES = 'SHORTLISTED_PROFILES',
  ALL_PROFILES = 'ALL_PROFILES'
}