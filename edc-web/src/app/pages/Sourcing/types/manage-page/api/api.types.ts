import {
  Location,
  Capability,
  Description,
  ProfileAttribute,
  Language,
  Range
} from './common.types';

export interface JobVacancy {
  id: string
  referenceNumber: string
  title: string
  locations: Location[]
  remote: string
  endDate: string
  startDate: string
  appliedCount: number
  bookmarkedCount: number
  uniqueViews: number
  isBookmarked: boolean
  linkedRoleStatusDetails: LinkedRoleStatusDetail[]
  capabilities: Capability[]
  descriptions: Description[]
  jobStatus: string,
  linkedRoleStatus: any[],
  jobFamilyId: string,
  jobFamilyName?: string,
  jobFunctionName?: string,
  organizations?: Organization[]
  shortlistedCount: number
}

export interface Candidate {
  id: number
  firstName: string
  lastName: string
  handle: string
  profileAttribute: ProfileAttribute
  matchScore: number
  name: string
  picture: string
  location: any
  shortlisted: boolean
  shortlistedBy: any
  organizationUnits: any
}

export interface CandidateData {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalPages: number
  values: Candidate[]
}

export interface LinkedRoleStatusDetail {
  id: string
  descriptions: Description[],
  topics: Topics[]
}

export interface Topics {
  id: string;
  label: string;
  status: string;
  parent: {
    id: string;
    label: string;
  };
  skills: Skill[];
  visibility: boolean;
}
export interface SkillsLevelGroup {
  id: number
  name: string
  descriptions: string
  level: string
  languages: Language[]
  range: Range
  label: string
  translatedLevelDescription: string
  value: string
  skills: Array<any>
}

export interface Skill {
  id: string
  type: string
  name: string
  skillSourceId: string
  status: string
  level: string
  label: string
  skillSourceLabel: any
  levelProficiency: number
}

export interface OrgUnits {
  columnType: string;
  columnLabel: string;
  orgDivisionsMap: Record<string, string>;
  orgUnitsMap: {
    orgUserAssociation: any[];
    orgCareerPrefAssociation: any[];
    orgUserCareerPrefAssociation: any[];
    orgTypes: any;
    orgUnitsInFilter: any[]
  }
}
export interface CandidatesPayload {
  profilesFilterSection: string;
  filters: object;
  showPartialMatchEnabled?: boolean;
  sortOrder: string;
  [key: string]: any;
}

export interface Organization {
  internalId: string,
  externalId: string,
  organizationTypeId: string,
  title?: string
}

export interface JobFamilyFilter {
  jobFamilyId: string
  jobFamilyName: string
  jobFunctionId: string
  jobFunctionName: string
}

export interface CandidateFiltersResponsePayload {
  filters: object,
  showPartialMatchEnabled?: boolean
}