import { OPTIONS } from '../../constants/filter/general-constants';
import { FILTERS, SUB_FILTERS } from '../../constants/filter/filter-labels';
import { getOrgUnitsPayload } from './utils/payload/org_units';
import { getLevelsPayload } from './utils/payload/levels';
import { FILTER_TYPE } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import moment from 'moment';

export const SEARCH_TYPE = {
  INTERNAL: 'INTERNAL',
  EXTERNAL_SKILLS: 'EXTERNAL_SKILLS',
  EXTERNAL_JOB_ROLE: 'EXTERNAL_JOB_ROLE',
  NONE: 'NONE'
};

export const changeStatusOfFilterItems = (filterItems, status) => {
  return filterItems.map(innerFilter => {
    const updatedFilter = {
      ...innerFilter,
      isSelected: status,
      subFilters: {
        ...innerFilter.subFilters
      }
    };
    if (innerFilter.subFilters && Object.keys(innerFilter.subFilters).length > 0) {
      updatedFilter.subFilters = {};
      for (const subFilterKey in innerFilter.subFilters) {
        if (Object.hasOwnProperty.call(innerFilter.subFilters, subFilterKey)) {
          updatedFilter.subFilters[subFilterKey] = innerFilter.subFilters[subFilterKey].map(
            subFilterItem => ({
              ...subFilterItem,
              isSelected: status
            })
          );
        }
      }
    }
    return updatedFilter;
  });
};

export const changeStatusOfSubFilters = (
  filterName,
  filterPropertyId,
  subFilters,
  previewItems,
  status
) => {
  if (subFilters && Object.keys(subFilters).length) {
    const selectedSubFilters = getSelectedFiltersOfSubFilters(
      previewItems,
      filterPropertyId,
      filterName
    );
    return {
      ...subFilters,
      items: subFilters.items.map(item => ({
        ...item,
        isSelected:
          status === undefined
            ? selectedSubFilters.selectedSubFilterDetails.items.find(
                filter => filter.id === item.id
              ).isSelected
            : status
      })),
      options: subFilters.options
        ? subFilters.options.map(option => ({
            ...option,
            isSelected:
              status === undefined
                ? selectedSubFilters.selectedSubFilterDetails?.options?.find(
                    filter => filter.id === option.id
                  ).isSelected
                : status
          }))
        : []
    };
  } else return {};
};

const getSelectedFiltersOfSubFilters = (previewItems, filterPropertyId, filterName) =>
  previewItems
    .find(filter => filter.filterId === filterName)
    .tagInfo.find(row => row.id === filterPropertyId);

export const getFilterTagTextFromPreviewList = (filterId, filterItemsId, previewItemList) => {
  let tagValue = '';
  previewItemList.forEach(item => {
    if (item.filterId === filterId) {
      const matchingFilterItem = item.tagInfo.find(row => row.id === filterItemsId);
      tagValue = matchingFilterItem ? matchingFilterItem.filterTagText : '';
    }
  });
  return tagValue;
};

export const formulateApiRequest = selectedFilterList => {
  const getFilterById = id => selectedFilterList.find(filter => filter.filterId === id);

  const skillsFilter = getFilterById(FILTERS.SKILLS.id);
  const schedule = getFilterById(FILTERS.SCHEDULE.id);
  const jobType = getFilterById(FILTERS.JOB_TYPE.id);
  const workplaceModel = getFilterById(FILTERS.WORKPLACE_MODEL.id);
  const careerTrack = getFilterById(FILTERS.CAREER_TRACK.id);
  const locations = getFilterById(FILTERS.LOCATION.id);
  const openToOfferFilter = getFilterById(FILTERS.OPEN_TO_OFFERS.id);
  const levels = getLevelsPayload(selectedFilterList);
  const jobRole = getFilterById(FILTERS.JOB_ROLE.id);
  const organizationUnits = getOrgUnitsPayload(selectedFilterList);
  const countryCode = getFilterById(FILTERS.COUNTRY.id);
  const jobFamily = getFilterById(FILTERS.JOB_FAMILY.id);
  const jobFunction = getFilterById(FILTERS.JOB_FUNCTION.id);

  const payload = {
    capabilityFilters: skillsFilter
      ? skillsFilter.tagInfo.map(data => ({
          capabilityId: data.id,
          levelProficiencyRange: data.selectedSubFilterDetails.items
            .filter(filter => filter.isSelected)
            .map(item => item.value.range),
          capabilityTypes: data.selectedSubFilterDetails?.options
            ?.filter(filter => filter.isSelected)
            .map(option => option.id)
        }))
      : [],
    schedules: schedule ? schedule.tagInfo.map(data => data.id) : [],
    jobTypes: jobType ? jobType.tagInfo.map(data => data.id) : [],
    workplaceModelFilter: workplaceModel ? workplaceModel.tagInfo.map(data => data.id) : [],
    careerTrackFilter: careerTrack ? careerTrack.tagInfo.map(data => data.id) : [],
    locations: locations
      ? locations.tagInfo.map(data => {
          if (data.selectedSubFilterDetails?.items && data.selectedSubFilterDetails.items.length) {
            return {
              id: data.id,
              type: data.selectedSubFilterDetails.items
                .filter(filter => filter.isSelected)
                .map(item => item.value)
            };
          } else {
            return data.id;
          }
        })
      : [],
    openToOffers: openToOfferFilter ? openToOfferFilter.tagInfo.map(data => data.id) : [],
    levels,
    jobRole:
      jobRole && jobRole.filterType === FILTER_TYPE.LIST_WITH_ACCORDION
        ? jobRole.tagInfo.map(data => ({
            id: data.id,
            type: data.selectedSubFilterDetails.items
              .filter(filter => filter.isSelected)
              .map(item => item.value)
          }))
        : [],
    jobRoles:
      jobRole && jobRole.filterType === FILTER_TYPE.LIST
        ? jobRole.tagInfo.map(data => data.id)
        : [],
    countryCode: countryCode
      ? countryCode.tagInfo.map(countryData => countryData.id.toLowerCase())
      : [],
    organizationUnits,
    startDateRanges: getStartAndDateRange(getFilterById(FILTERS.JOB_START_DATE.id)),
    endDateRanges: getStartAndDateRange(getFilterById(FILTERS.JOB_END_DATE.id)),
    jobFamilies: jobFamily ? jobFamily.tagInfo.map(data => data.id) : [],
    jobFunctions: jobFunction ? jobFunction.tagInfo.map(data => data.id) : []
  };

  return Object.fromEntries(
    Object.entries(payload).filter(([_key, value]) => !(Array.isArray(value) && value.length === 0))
  );
};

export const getStartAndDateRange = dateRange => {
  if (dateRange && dateRange.tagInfo && dateRange.tagInfo.length > 0) {
    const getDate = date => moment(date).format('YYYY-MM-DD');
    return [
      {
        fromDate: getDate(dateRange.tagInfo[0].additionalInfo.fromDate),
        toDate: getDate(dateRange.tagInfo[0].additionalInfo.toDate)
      }
    ];
  }
  return [];
};

export const updateSubFiltersAndDependentSections = (
  updatedSubFiltersAll,
  isParentSelected,
  sectionName,
  value
) => {
  if (isParentSelected && sectionName === OPTIONS) {
    const isAnyLevelsChecked = updatedSubFiltersAll.items.some(item => item.isSelected);
    if (!isAnyLevelsChecked) {
      updatedSubFiltersAll.items.forEach(item => {
        item.isSelected = true;
      });
    }
  }

  if (isParentSelected && sectionName === 'items' && value && updatedSubFiltersAll.options) {
    const isAnyLevelsChecked = updatedSubFiltersAll.options.some(item => item.isSelected);
    if (!isAnyLevelsChecked) {
      const levelToUpdate = updatedSubFiltersAll.options.find(
        item => item.id === SUB_FILTERS.CURRENT.id
      );
      if (levelToUpdate) {
        levelToUpdate.isSelected = true;
      }
    }
  }

  if (!isParentSelected && !value) {
    setAllSubFiltersToFalse(updatedSubFiltersAll);
  }

  return updatedSubFiltersAll;
};

export const setAllSubFiltersToFalse = updatedSubFiltersAll => {
  Object.entries(updatedSubFiltersAll).forEach(([_key, filter]) => {
    if (Array.isArray(filter)) {
      filter.forEach(obj => {
        if (obj.hasOwnProperty('isSelected')) {
          obj.isSelected = false;
        }
      });
    }
  });
};

export const generateFilterPreviewTag = (fromDate, toDate) => {
  const startDate = moment(fromDate);
  const endDate = moment(toDate);
  return `${startDate.format('DD/MM/YYYY')} - ${endDate.format('DD/MM/YYYY')}`;
};

export const getSelectedSubFilters = subFilters => {
  if (!subFilters || !Object.keys(subFilters).length) {
    return {};
  }

  const selectedItems = subFilters.items?.filter(item => item.isSelected) || [];
  const selectedOptions = subFilters.options?.filter(option => option.isSelected) || [];

  if (!selectedItems.length && !selectedOptions.length) {
    return {};
  }

  const result = {};

  if (selectedItems.length) {
    result.items = selectedItems;
  }

  if (selectedOptions.length) {
    result.options = selectedOptions;
  }

  return result;
};

export const undoUnsavedFilters = (filterItemsToIterate, previewItems) => {
  return filterItemsToIterate.map(filterData => {
    if (previewItems.some(filter => filter.filterId === filterData.filterId)) {
      const extractedIds = previewItems.flatMap(item => item.tagInfo.map(tag => tag.id));
      return {
        ...filterData,
        filterItems: filterData.filterItems.map(filterProperties => {
          if (extractedIds.includes(filterProperties.id)) {
            return {
              ...filterProperties,
              isSelected: true,
              subFilters: changeStatusOfSubFilters(
                filterData.filterId,
                filterProperties.id,
                filterProperties.subFilters,
                previewItems
              )
            };
          } else {
            return {
              ...filterProperties,
              isSelected: false,
              subFilters: changeStatusOfSubFilters(
                filterData.filterId,
                filterProperties.id,
                filterProperties.subFilters,
                previewItems,
                false
              )
            };
          }
        })
      };
    } else {
      return {
        ...filterData,
        filterItems: changeStatusOfFilterItems(filterData.filterItems, false)
      };
    }
  });
};

export const prepareFilteredDataForCopy = (
  candidateFilterList,
  candidateFilterListCopy,
  filterId
) => {
  const copiedData = [...candidateFilterListCopy];
  const locationIndex = copiedData.findIndex(filter => filter.filterId === filterId);
  const updatedCopiedFilterItems = getDataFromValues(
    candidateFilterList[locationIndex].filterItems,
    copiedData[locationIndex].filterItems
  );
  copiedData[locationIndex] = {
    ...copiedData[locationIndex],
    filterItems: updatedCopiedFilterItems
  };
  return copiedData;
};

const getDataFromValues = (filterItems, copyFilterItems) => {
  const filterItemsToReturn = [];
  copyFilterItems.forEach(filter => {
    const isPresent = filterItems.find(filterInner => filterInner.id === filter.id);
    if (isPresent) {
      filterItemsToReturn.push(isPresent);
    } else {
      filterItemsToReturn.push(filter);
    }
  });
  return filterItemsToReturn;
};

export const addNewValuesWithExistingList = (currentFilterInfo, filterItemsToUpdate) => {
  const newSelectedValues = filterItemsToUpdate.filterItems.filter(item => item.isSelected);
  const updatedValues = currentFilterInfo.filterItems.map(item => {
    const newValue = filterItemsToUpdate.filterItems.find(val => val.id === item.id);
    return newValue ? newValue : item;
  });
  const newSelectedValuesToAdd = newSelectedValues.filter(
    item => !updatedValues.find(val => val.id === item.id)
  );
  const updatedFilterItems = [...updatedValues, ...newSelectedValuesToAdd];
  return { ...filterItemsToUpdate, filterItems: updatedFilterItems };
};

export const preSelectForSearchResults = (jobRoleData, currentFilter) =>
  jobRoleData.map(filterItem => {
    const currentItemData = currentFilter.filterItems.find(filter => filter.id === filterItem.id);
    if (currentItemData) {
      return currentItemData;
    } else {
      return filterItem;
    }
  });
