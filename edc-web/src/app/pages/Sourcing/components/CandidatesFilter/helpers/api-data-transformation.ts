import { CandidateFilterItem, FILTER_GROUPS, FILTER_TYPE, FilterItem, PreviewFilterItem, SubFilter, TagInfo } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import { changeStatusOfFilterItems } from "../helpers"
import { Item } from "@pages/Sourcing/types/manage-page/api/common.types";
import { isEmpty } from 'lodash';

export const prepareFilterRequestBody = (previewFilterItems: PreviewFilterItem[]) => {
  // Removing isSelected from the filter items to avoid sending it to the backend
  return previewFilterItems.map((filter: PreviewFilterItem) => {
    if (filter.tagInfo) {
      filter.tagInfo = filter.tagInfo.map((tag: TagInfo) => {
        if (tag.selectedSubFilterDetails) {
          if (tag.selectedSubFilterDetails.items) {
            tag.selectedSubFilterDetails.items = tag.selectedSubFilterDetails.items.map((item: Item) => {
              const { isSelected, ...rest } = item;
              return rest;
            });
          }
          
          if (tag.selectedSubFilterDetails.options) {
            tag.selectedSubFilterDetails.options = tag.selectedSubFilterDetails.options.map((option: Item) => {
              const { isSelected, ...rest } = option;
              return rest;
            });
          }
        }
        return tag;
      });
    }
    return filter;
  })
};

// Setting the filter name based on current configuration (filter data). to saved filter.
// Ex: if the filter name was saved with location and in configuration the location is renamed then the filter name will be updated to the new name.
export const transformSavedFilterDataToCandidateFilterList = (previewFilterItems: PreviewFilterItem[], candidateFilterList: CandidateFilterItem[]) =>
  candidateFilterList.map((candidateFilter: CandidateFilterItem) => {
    const filterPresentInPreviewList = previewFilterItems.find((previewFilterItem: PreviewFilterItem) => previewFilterItem.filterId === candidateFilter.filterId);
    if (filterPresentInPreviewList) {
      const filterItems = processFilterItems(candidateFilter.filterItems, filterPresentInPreviewList.tagInfo);
      return {
        ...candidateFilter,
        filterItems: filterItems
      }
    } else return {
      ...candidateFilter,
      filterItems: changeStatusOfFilterItems(candidateFilter.filterItems, false)
    };
  });

const processFilterItems = (currentFilterItems: FilterItem[], savedFilterTagInfo: TagInfo[]) => {
  return currentFilterItems.map((filterItem: FilterItem) => {
    // check is filter was already selected in saved filter.
    const savedFiltersFilterItemInfo: TagInfo = savedFilterTagInfo.find((tagInfo: TagInfo) => tagInfo.id === filterItem.id);
    if (savedFiltersFilterItemInfo) {
      // checking if any of the sub filter value mach with current config then making the parent filter selected.
      const isValuePresentInConfig = isConfigSameAsSaved(filterItem, savedFiltersFilterItemInfo);
      return {
        ...filterItem,
        isSelected: isValuePresentInConfig,
        subFilters: processSubFilters(filterItem, savedFiltersFilterItemInfo)
      }
    } else return {
      ...filterItem, isSelected: false,
      subFilters: !isEmpty(filterItem.subFilters) ? {
        items: filterItem.subFilters.items?.map(item => ({ ...item, isSelected: false })),
        options: filterItem.subFilters.options?.map(item => ({ ...item, isSelected: false }))
      } : {}
    };
  });
};

// Checking if any of the saved sub filter value like: current/preferred etc is present in current filter.
const isConfigSameAsSaved = (currentFilterItems: FilterItem, savedSubFilter: TagInfo) => {
  const subFilters = currentFilterItems.subFilters;
  const selectedSubFilterDetails = savedSubFilter.selectedSubFilterDetails;

  if (isValueDisabledForCurrentConfig(currentFilterItems)) {
    return false;
  }
  if (isEmpty(subFilters) && isEmpty(selectedSubFilterDetails)) {
    return true;
  }
  
  const currentSubFilterItems = subFilters?.items ? subFilters.items.map((item: Item) => item.id) : [];
  const savedSubFilterItems = selectedSubFilterDetails?.items ? selectedSubFilterDetails.items.map((item: Item) => item.id) : [];
  return savedSubFilterItems.some((item: string) => currentSubFilterItems.includes(item));
}

const isValueDisabledForCurrentConfig = (currentFilterItems: FilterItem) => currentFilterItems?.additionalInfo?.isDisabled;

const processSubFilters = (currentConfigFilterItem: FilterItem, savedFiltersFilterItemInfo: TagInfo) => {
  
  if (isEmpty(currentConfigFilterItem.subFilters) || isEmpty(savedFiltersFilterItemInfo.selectedSubFilterDetails)) {
    return currentConfigFilterItem.subFilters || {};
  }

  const isSubFilterItemSelected = (key: keyof SubFilter, itemId: string | number) => 
    savedFiltersFilterItemInfo.selectedSubFilterDetails?.[key]
      .find((item: Item) => item.id === itemId)
      ?.isSelected ?? false;

  const currentSubFilters = currentConfigFilterItem.subFilters?.items?.map((item: Item) => ({
    ...item,
    isSelected: isSubFilterItemSelected('items', item.id)
  })) || [];

  const missingSubFilters = savedFiltersFilterItemInfo.selectedSubFilterDetails?.items
    ?.filter(savedItem => !currentConfigFilterItem.subFilters?.items?.some(item => item.id === savedItem.id))
    .map(item => ({
      ...item,
      isSelected: true,
      isMissingFromConfig: true
    })) || [];

  return {
    ...currentConfigFilterItem.subFilters,
    items: [...currentSubFilters, ...missingSubFilters],
    options: currentConfigFilterItem.subFilters?.options?.map(item => ({ 
      ...item, 
      isSelected: isSubFilterItemSelected('options', item.id)
    }))
  };
};

export const normalizePreviewFilterList = (previewList: PreviewFilterItem[]): PreviewFilterItem[] => {
  //Adding isSelected: true to all the filter items for saved filter data in preview list.
  return previewList.map(item => {
    return {
      ...item,
      tagInfo: item.tagInfo?.map((tag: TagInfo) => ({
        ...tag,
        selectedSubFilterDetails: isEmpty(tag.selectedSubFilterDetails) ? {} : {
          items: tag.selectedSubFilterDetails?.items?.map(item => ({ ...item, isSelected: true })) || [],
          options: tag.selectedSubFilterDetails?.options?.map(item => ({ ...item, isSelected: true })) || []
        }
      }))
    };
  });
};