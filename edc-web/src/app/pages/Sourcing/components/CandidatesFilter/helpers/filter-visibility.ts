import { SAVED_FILTERS } from "@pages/Sourcing/constants/filter/filter-labels";
import { State } from "@pages/Sourcing/types/manage-page/context/candidate-filter";
//@ts-ignore
import isEqual from 'lodash/isEqual';
import { formulateApiRequest } from "../helpers";

export const getSaveFiltersButtonVisibility = (state: State) => {
    const { savedFiltersDefaultValue, previewFilterItems, currentFilterSettings, masterSwitchInitialState } = state;
    const { id } = savedFiltersDefaultValue;
    if (!id || id !== SAVED_FILTERS.SELECT_FILTER.id) {
        const currentApiRequestBody = formulateApiRequest(previewFilterItems);
         // Compare the selected filter request body with the latest request body and master switch value
        if (!isEqual(currentFilterSettings.filterCriteria, currentApiRequestBody) ||
            (masterSwitchInitialState?.value !== currentFilterSettings?.masterSwitchValue)) {
            return true;
        }
    }
    return false;
};