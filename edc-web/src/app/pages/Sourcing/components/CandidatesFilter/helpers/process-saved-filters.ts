import { CandidateFilterItem, PreviewFilterItem, SavedFilters } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import { normalizePreviewFilterList, transformSavedFilterDataToCandidateFilterList } from "./api-data-transformation";
import { transformValuesForPreview } from "./filter-preview";
import { Action, State } from "@pages/Sourcing/types/manage-page/context/candidate-filter";
import { useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import { isEmpty } from 'lodash';

const processSavedFilters = (
    selectedFilterItem: SavedFilters,
    state: State,
    dispatch: React.Dispatch<Action>
) => {
    const {
        setCandidateFilterListDeepCopy,
        setPreviewFilterItems,
        setMasterSwitchWithInitial
    } = useCandidateFilterActions(dispatch);
    const { candidateFilterList, masterSwitchInitialState } = state;

    // Adding isSelected to the saved filter items.
    const normalizedData = normalizePreviewFilterList(selectedFilterItem.filterCriteria);

    // Step 1. Transforming the saved filter data to candidate filter list.
    const transformedDataFromResponse = transformSavedFilterDataToCandidateFilterList(
        normalizedData, candidateFilterList);

    // Step 2. Saving the transformed data to candidate filter list.
    setCandidateFilterListDeepCopy(transformedDataFromResponse);

    //Step 3. Transform to preview filter list. 
    const previewFilterList = transformValuesForPreview(transformedDataFromResponse, normalizedData);

    // Step 4. Set the preview filter list.
    setPreviewFilterItems(previewFilterList);

    // Step 5: Set Master Switch value. 
    if (!isEmpty(masterSwitchInitialState)) setMasterSwitchWithInitial({
        ...masterSwitchInitialState,
        value: selectedFilterItem?.filterConfiguration?.isShowPartialMatchEnabled
    });

    return previewFilterList;
}

export default processSavedFilters;