import { CandidateFilterItem, FILTER_TYPE, FilterItem, PreviewFilterItem, SubFilter, TagInfo } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import { isEndDateGreater } from "../ts-helper";
import { generateFilterPreviewTag, getSelectedSubFilters } from "../helpers";
import { Item } from "@pages/Sourcing/types/manage-page/api/common.types";
import { sortListByAscendingOrder } from "@pages/Sourcing/routes/manage-page/job-vacancy/utils/filter/common";

export const transformValuesForPreview = (candidateFilterList: CandidateFilterItem[], previewFilterItems: PreviewFilterItem[]) => {
  const previewFilterList = candidateFilterList.map(row => {
    const tagInfo = [];
    if (row.filterType === FILTER_TYPE.DATE_PICKER) {
      const datePickerResult = handleDatePickerFilterItemsForTagInfo(row);
      if (datePickerResult) {
        tagInfo.push(datePickerResult);
      }
    } else {
      tagInfo.push(...processFilterItemsForTagInfo(row.filterItems));
    }

    // Adding the missing values which are not present in the candidate list but are present in the saved filters
    tagInfo.push(...addMissingTagsFromPreview(row, previewFilterItems));

    return tagInfo.length ? {
      filterName: row.filterName,
      tagInfo,
      filterId: row.filterId,
      filterType: row.filterType,
      filterGroup: row.filterGroup
    } : null;
  }).filter(Boolean);

  // Considering the case where the filter is not present in the candidate list but is present
  // in saved filters, hence we are adding it to the preview list
  const missingFilters = previewFilterItems.filter(previewFilter => {
    const existsInCandidateList = candidateFilterList.some(
      candidateFilter => candidateFilter.filterId === previewFilter.filterId
    );
    return !existsInCandidateList;
  });
  
  const allFiltersForPreview = [...previewFilterList, ...missingFilters];
  return sortListByAscendingOrder(allFiltersForPreview, 'filterName');
};

const handleDatePickerFilterItemsForTagInfo = (row: CandidateFilterItem) => {
  const firstFilterItem = row.filterItems[0];
  const { id, label, dateRange, subFilters } = firstFilterItem;
  if (
    dateRange.fromDate &&
    dateRange.toDate &&
    isEndDateGreater(dateRange.fromDate, dateRange.toDate) === false
  ) {
    return createTagInfo(id,
      label,
      generateFilterPreviewTag(dateRange.fromDate, dateRange.toDate),
      getSelectedSubFilters(subFilters), {
      fromDate: dateRange.fromDate,
      toDate: dateRange.toDate
    });
  }
};

const createSimpleTag = (filterItem: FilterItem): TagInfo => {
  const { id, label, additionalInfo } = filterItem;
  return createTagInfo(
    id,
    label,
    generatePreviewTagLabel([], {}),
    {},
    additionalInfo ?? {}
  );
};

const createSubFilterTag = (filterItem: FilterItem, selectedItems: Item[]): TagInfo => {
  const { id, label, subFilters, additionalInfo } = filterItem;
  return createTagInfo(
    id,
    label,
    generatePreviewTagLabel(selectedItems, subFilters),
    getSelectedSubFilters(subFilters),
    additionalInfo ?? {}
  );
};

const processFilterItemsForTagInfo = (filterItems: FilterItem[]): TagInfo[] => {
  return filterItems.reduce((tagInfo: TagInfo[], filterItem: FilterItem) => {
    const { subFilters, isSelected } = filterItem;
    const hasSubFilters = Object.keys(subFilters || {}).length > 0;
    
    if (!isSelected) return tagInfo;

    if (isSelected && !hasSubFilters) {
      tagInfo.push(createSimpleTag(filterItem));
      return tagInfo;
    }
    
    if (hasSubFilters) {
      const selectedItems = subFilters.items?.filter(item => item.isSelected);
      if (selectedItems?.length) {
        tagInfo.push(createSubFilterTag(filterItem, selectedItems));
      }
    }
    
    return tagInfo;
  }, []);
};

const addMissingTagsFromPreview = (row: CandidateFilterItem, previewFilterItems: PreviewFilterItem[]) => {
  const matchingFilter = previewFilterItems.find(filter => filter.filterId === row.filterId);
  
  return matchingFilter?.tagInfo.reduce<TagInfo[]>((acc, tagData) => {
    const isTagMissing = !row.filterItems.find(filterItem => filterItem.id === tagData.id);
    
    if (isTagMissing) {
      const { id, label, selectedSubFilterDetails, additionalInfo } = tagData;
      const items = Object.keys(selectedSubFilterDetails).length ? selectedSubFilterDetails.items : [];
      const filterTagText = generatePreviewTagLabel(items, selectedSubFilterDetails);
      
      acc.push(createTagInfo(
        id,
        label,
        filterTagText,
        getSelectedSubFilters(selectedSubFilterDetails),
        additionalInfo ?? {}
      ));
    }
    
    return acc;
  }, []) ?? [];
};

const createTagInfo = (id: string, label: string, filterTagText: string, subFilters: SubFilter, additionalInfo: object) => ({
  id,
  label,
  filterTagText,
  selectedSubFilterDetails: subFilters,
  additionalInfo
});

const generatePreviewTagLabel = (items: Item[], subFilters: SubFilter) => {
  const levelLabel = items.map(data => data.label).join(', ');
  if (!subFilters || !Object.keys(subFilters).length) {
    return levelLabel;
  }

  const selectedOptions = subFilters.options
    ? subFilters.options.filter(option => option.isSelected)
    : [];
  if (items.length && selectedOptions.length) {
    return `${items.map(data => data.label).join(', ')} / ${subFilters.options
      .filter(option => option.isSelected)
      .map(option => option.label)
      .join(', ')}`;
  } else if (items.length) {
    return levelLabel;
  } else {
    return '';
  }
};