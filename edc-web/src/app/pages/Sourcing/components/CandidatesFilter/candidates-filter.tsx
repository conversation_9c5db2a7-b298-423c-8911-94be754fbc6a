import React, { useRef, useContext, useEffect, useState } from 'react';
import './candidates-filter.scss';
import useClickOutside from '../../hooks/useClickOutside';
import { CandidateFilterContext } from './candidate-filter-provider';
import ActionPanel from './FilterComponents/action-bar/action-panel';
import {
  fetchAndSetSavedSearches,
  fetchFilterDataAndSetCandidateFilterList,
  manageMenuOpenState,
  useCandidateFilterActions
} from '@pages/Sourcing/actions/manage-job-vacancy/candidate-filter';
import FilterWindow from './FilterComponents/filter-window/filter-window';
import FiltersPreviews from './FilterComponents/filter-previews/filters-previews';
import Spinner from 'centralized-design-system/src/MUIComponents/common/Spinner';
import FocusLock from 'react-focus-lock';
import { FilterSection, MasterSwitch } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';

interface CandidatesFilterProps {
  getFilterData: (setFilterList: (result: object) => void) => {} | Promise<any>;
  onChange: (value: object) => void;
  savedSearchFilterType: string;
  masterSwitch?: MasterSwitch | {};
}

const CandidatesFilter: React.FC<CandidatesFilterProps> = ({ getFilterData, onChange, savedSearchFilterType = '', masterSwitch }) => {

  const { state, dispatch } = useContext(CandidateFilterContext);
  const [filterSectionLoading, setFilterSectionLoading] = useState(true);

  const {
    isFilterMenuOpen,
    expandedFilter,
    showFilterPreview,
    showMenuDetails,
    isFilterListLoading,
    candidateFilterListCopy,
    masterSwitchInitialState,
    masterSwitch: stateMasterSwitch
  } = state;

  const {
    setFilterMenuOpen,
    setSavedFilterList,
    setSavedSearchFilterType,
    setMasterSwitchWithInitial,
    setCandidateFilterList,
    setSelectedFilterState,
    setFilterErrors,
    setMasterSwitchDetails
  } = useCandidateFilterActions(dispatch);

  const filterMenuListRef = useRef(null);
  const addFilterButtonRef = useRef(null);

   const onCancel = () => {
      setCandidateFilterList(candidateFilterListCopy);
      manageMenuOpenState(false, dispatch);
      setSelectedFilterState({});
      setFilterErrors([]);
      setMasterSwitchDetails(masterSwitchInitialState)
    };

  useClickOutside(
    filterMenuListRef,
    () => showMenuDetails && !isFilterMenuOpen,
    () => { if (!showMenuDetails) setFilterMenuOpen(false); },
    addFilterButtonRef,
    onCancel
  );

  useEffect(() => {
    (async () => {
      if (savedSearchFilterType) {
        await fetchAndSetSavedSearches(setSavedSearchFilterType, setSavedFilterList, savedSearchFilterType);
        await fetchFilterDataAndSetCandidateFilterList(getFilterData, dispatch);
        setMasterSwitchWithInitial(masterSwitch);
      }
      setFilterSectionLoading(false);
    })();
  }, []);

  /**
   * Handler for keyboard events within the filter window
   * @param event - The keyboard event object
   */
  const handleKeyDown = (event: React.KeyboardEvent<HTMLElement>) => {
    if (event.key === 'Escape' && isFilterMenuOpen) {
      manageMenuOpenState(false, dispatch, FilterSection.MainMenu);
      requestAnimationFrame(() => { addFilterButtonRef.current?.focus(); });
    }
  };

  const onFilterChanges = (requestBody: Object, masterSwitchValue?: boolean) => {
    if (Object.keys(stateMasterSwitch).length) {
      onChange({ filters: requestBody, [stateMasterSwitch.requestBodyKey]: masterSwitchValue });
    } else {
      onChange({ filters: requestBody });
    }
  };

  return (
    <div className="filter-header">
      {filterSectionLoading ?
        <div className="make-center width-100">
          <Spinner />
        </div>
        :
        <>
          <ActionPanel
            onChange={onFilterChanges}
            addFilterButtonRef={addFilterButtonRef}
            getFilterData={getFilterData}
            handleKeyDown={handleKeyDown}
          />
          {(isFilterMenuOpen || showMenuDetails || isFilterListLoading) && (
            <FocusLock>
              <FilterWindow
                filterMenuListRef={filterMenuListRef}
                onChange={onFilterChanges}
                addFilterButtonRef={addFilterButtonRef}
                handleKeyDown={handleKeyDown}
              />
            </FocusLock>
          )}
          {expandedFilter && showFilterPreview && (
            <FiltersPreviews
              onChange={onFilterChanges}
            />
          )}
        </>
      }
    </div>
  );
};

export default CandidatesFilter;
