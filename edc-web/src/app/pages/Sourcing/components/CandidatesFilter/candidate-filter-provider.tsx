import React from 'react';
//@ts-ignore
import cloneDeep from 'lodash/cloneDeep';
import { Action, CANDIDATE_FILTER_ACTION_TYPES, State } from '@pages/Sourcing/types/manage-page/context/candidate-filter';
import { CandidateFilterItem, CurrentFilterSettings, MasterSwitch } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import { SAVED_FILTERS } from '@pages/Sourcing/constants/filter/filter-labels';

const initState: State = {
  candidateFilterList: [],
  interestsLabel: '',
  selectedFilterInfo: {} as CandidateFilterItem,
  isFilterMenuOpen: false,
  showFilterListOnPlus: false,
  previewFilterItems: [],
  candidateFilterListCopy: [],
  expandedFilter: false,
  showFilterPreview: false,
  isFilterListLoading: false,
  showMenuDetails: false,
  filterErrors: [],
  savedFilters: [],
  savedFiltersDefaultValue: SAVED_FILTERS.SELECT_FILTER,
  filterMenuOpeningPosition: {
    top: '0rem',
    left: '0rem'
  },
  savedFiltersDropItems: [],
  savedSearchFilterType: '',
  currentFilterSettings: {} as CurrentFilterSettings,
  masterSwitch: {} as MasterSwitch,
  masterSwitchInitialState: {} as MasterSwitch
};

export const CandidateFilterContext = React.createContext<{ state: State, dispatch: React.Dispatch<Action> }>({
  state: initState,
  dispatch: () => undefined
});

const filterContextReducer = (state: State, action: Action ): State => {
  switch (action.type) {
    case CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST: {
      return {
        ...state,
        candidateFilterList: cloneDeep(action.candidateFilterList)
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SELECTED_FILTER_INFO: {
      return {
        ...state,
        selectedFilterInfo: action.selectedFilterInfo
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_MENU_OPEN: {
      return {
        ...state,
        isFilterMenuOpen: action.isFilterMenuOpen
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_FILTER_LIST_ON_PLUS: {
      return {
        ...state,
        showFilterListOnPlus: action.showFilterListOnPlus
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_PREVIEW_FILTER_ITEMS: {
      return {
        ...state,
        previewFilterItems: action.previewFilterItems
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST_COPY: {
      return {
        ...state,
        candidateFilterListCopy: action.candidateFilterListCopy
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.RESET_CANDIDATE_FILTERS_LIST: {
      return {
        ...state,
        previewFilterItems: [],
        candidateFilterList: [],
        candidateFilterListCopy: [],
        savedFiltersDefaultValue: SAVED_FILTERS.SELECT_FILTER
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_EXPANDED_FILTER: {
      return {
        ...state,
        expandedFilter: action.expandedFilter
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_FILTER_PREVIEW: {
      return {
        ...state,
        showFilterPreview: action.showFilterPreview
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_IS_FILTER_LIST_LOADING: {
      return {
        ...state,
        isFilterListLoading: action.isFilterListLoading
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_MENU_DETAILS: {
      return {
        ...state,
        showMenuDetails: action.showMenuDetails
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_ERRORS: {
      return {
        ...state,
        filterErrors: action.filterErrors
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS: {
      return {
        ...state,
        savedFilters: action.savedFilters
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS_DEFAULT_VALUE: {
      return {
        ...state,
        savedFiltersDefaultValue: action.savedFiltersDefaultValue
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_MENU_OPENING_POSITION: {
      return {
        ...state,
        filterMenuOpeningPosition: action.filterMenuOpeningPosition
      };
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS_DROPDOWN_ITEMS: {
      return {
        ...state,
        savedFiltersDropItems: action.savedFiltersDropItems
      }
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_SEARCH_FILTER_TYPE: {
      return {
        ...state,
        savedSearchFilterType: action.savedSearchFilterType
      }
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_CURRENT_FILTER_SETTINGS: {
      return {
        ...state,
        currentFilterSettings: action.currentFilterSettings
      }
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_DETAILS: {
      return {
        ...state,
        masterSwitch: action.masterSwitch
      }
    }
    case CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_INITIAL_STATE: {
      return {
        ...state,
        masterSwitchInitialState: action.masterSwitchInitialState
      }
    }
    default: {
      return state;
    }
  }
};

const CandidateFilterProvider = ({ children }: { children: React.ReactNode }) => {
  
  const [state, dispatch] = React.useReducer(filterContextReducer, initState);

  return (
    <CandidateFilterContext.Provider value={{ state, dispatch }}>
      {children}
    </CandidateFilterContext.Provider>
  );
};

export default CandidateFilterProvider;