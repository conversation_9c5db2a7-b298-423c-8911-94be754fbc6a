import React, {
  LegacyRef,
  useContext, useRef, useEffect
} from 'react';
import { CandidateFilterContext } from "../../candidate-filter-provider";
import FilterMenuList from "../filter-menu-list";
import ConfigurableLoadingCard from 'centralized-design-system/src/SkeletonAnimations/ConfigurableLoadingCard/ConfigurableLoadingCard';
import { manageMenuOpenState, useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import { getFilterDetailsById } from "../../utils/common";
import { FilterSection } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import FilterTypeRenderer from "../filter-type-renderer";
import { connect } from "react-redux";
import { ThemeId } from "centralized-design-system/src/Theme/ThemeInterface";
import { translatr } from 'centralized-design-system/src/Translatr';
import Switch from 'centralized-design-system/src/Switch';
import LayoutDivider from '@pages/TalentMarketplace/shared/CandidateListing/LayoutDivider/LayoutDivider';

interface FilterWindowProps {
  filterMenuListRef: LegacyRef<HTMLDivElement>,
  onChange: (value: object, masterSwitchValue: boolean) => void,
  theme: string
  addFilterButtonRef: React.RefObject<HTMLButtonElement>
  handleKeyDown: React.KeyboardEventHandler<HTMLElement>;
}

const FilterWindow: React.FC<FilterWindowProps> = ({ filterMenuListRef, onChange, theme, addFilterButtonRef, handleKeyDown }) => {

  const { state, dispatch } = useContext(CandidateFilterContext);
  const { candidateFilterList,
    isFilterMenuOpen,
    isFilterListLoading,
    showMenuDetails,
    filterMenuOpeningPosition,
    masterSwitch
  } = state;

  const { setSelectedFilterState, setMasterSwitchDetails } = useCandidateFilterActions(dispatch);
  
  const boxItemCommonConfig = {
    height: '2.188rem',
    marginBottom: '0.063rem',
    width: '17.6rem'
  };
  const isNewDesignEnabled = theme == ThemeId.PLARE;
  const boxItemsConfig = Array(6).fill(boxItemCommonConfig);

  const filterHandler = (filterId: string) => {
    const selectFilterDetails = getFilterDetailsById(filterId, candidateFilterList);
    manageMenuOpenState(true, dispatch, FilterSection.MenuList);
    setSelectedFilterState(selectFilterDetails);
  };

  return <div
    className={'filter-list-container'}
    style={{ left: filterMenuOpeningPosition?.left, top: filterMenuOpeningPosition?.top }}
    role="dialog"
    aria-modal="true"
    aria-label={translatr('web.common.main', 'AddFilter')}>
    {candidateFilterList.length > 0 && isFilterMenuOpen && (
      <div
        ref={filterMenuListRef}
        className={isNewDesignEnabled ? 'filter-list-container-padding' : ''}
        onKeyDown={handleKeyDown}
        autoFocus
      >
        {Object.keys(masterSwitch).length > 0 &&
          <>
            <div className='master-switch-container'>
              <div className='master-switch'>
                <Switch
                  defaultChecked={masterSwitch.value}
                  onChange={(value) => setMasterSwitchDetails({ ...masterSwitch, value: value.target.checked })}
                  ariaHidden={false}
                  ariaLabel={masterSwitch.label}
                  role="switch"
                  ariaChecked={masterSwitch.value}
                />
              </div>
              <div className='master-switch-label'>
                <span>{masterSwitch.label}</span>
              </div>
            </div>
            <LayoutDivider />
          </>}
        <FilterMenuList filterList={candidateFilterList} filterHandler={filterHandler} />
      </div>
    )}
      {isFilterListLoading && (
        <div className='loading-card'>
          <ConfigurableLoadingCard
            key={'loading-card'}
            containerStyles={{ padding: '0.313rem' }}
            boxItemsConfig={boxItemsConfig}
          />
        </div>
      )}
      {showMenuDetails && (
        <div
          className={'filter-list-container-padding'}
          ref={filterMenuListRef}
        >
          <FilterTypeRenderer
            onChange={onChange}
            addFilterButtonRef={addFilterButtonRef}
          />
        </div>
      )}
    </div>
};

export default connect((state: any) => ({ theme: state.theme?.get('themeId') }))(FilterWindow);
