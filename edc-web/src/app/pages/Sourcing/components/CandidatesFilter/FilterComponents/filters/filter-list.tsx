import NoResultsFound from "../common/no-results-found";
import Checkbox from 'centralized-design-system/src/Checkbox';
import SearchFilterPanel from "../search-filter-panel";
import Tooltip from 'centralized-design-system/src/Tooltip';
import { CandidateFilterContext } from "../../candidate-filter-provider";
import { useContext, useState } from "react";
import { formatFilterLabel } from "../../utils/common";
import { truncateText } from "@utils/utils";
import { useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import { handleMainAccordionCheckBox } from "../../ts-helper";
import classNames from "classnames";
import { connect } from "react-redux";
import { ThemeId } from "centralized-design-system/src/Theme/ThemeInterface";
import { FilterItem } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";

interface FilterListProps {
  theme: string
}
const FilterList: React.FC<FilterListProps> = ({ theme }) => {

  const { state, dispatch } = useContext(CandidateFilterContext);
  const { selectedFilterInfo, candidateFilterList } = state;
  const { setSelectedFilterState, setCandidateFilterList } = useCandidateFilterActions(dispatch);
  const [internalSearchQuery, setInternalSearchQuery] = useState('');

  const handleCheckBox = (value: boolean, filterItemId: string) => {
    handleMainAccordionCheckBox(value, filterItemId, selectedFilterInfo, candidateFilterList, setCandidateFilterList, setSelectedFilterState)
  };
  const searchForFilter = async (value: string) => {
    setInternalSearchQuery(value.toLowerCase());
  };

  const filterItems = selectedFilterInfo.filterItems.filter(item =>
    item.label.toLowerCase().includes(internalSearchQuery.toLowerCase())
  );

  const isNewDesignEnabled = theme == ThemeId.PLARE;

  const renderFilterItem = (filter: FilterItem) => {
    const isDisabled = filter?.additionalInfo?.isDisabled;
    const label = formatFilterLabel(filter);
    const tooltipMessage = isDisabled ? filter.additionalInfo?.disabledMessage : label;
    const needsTooltip = label.length > 27 || isDisabled;
    
    const checkboxProps = {
      id: filter.id,
      key: filter.id,
      checked: filter.isSelected,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleCheckBox(e.target.checked, filter.id),
      ariaLabel: tooltipMessage,
      disabled: isDisabled
    };

    const checkbox = label.length > 27 ? (
      <label htmlFor={filter.id} className="checkbox-label-wrapper">
        <Checkbox {...checkboxProps} />
        <span>{truncateText(label, 27, '...')}</span>
      </label>
    ) : (
      <Checkbox
        {...checkboxProps}
        label={label}
        ariaLabelHidden={!isDisabled}
      />
    );

    if (needsTooltip) {
      return (
        <Tooltip
          message={<h2 className="font-size-l font-normal" aria-hidden="true">{tooltipMessage}</h2>}
          customClass="width-300"
          id={filter.id}
          tabIndex={isDisabled ? '0' : '-1'}
        >
          {checkbox}
        </Tooltip>
      );
    }

    return checkbox;
  };
  
  return <>
      <SearchFilterPanel
        searchType={selectedFilterInfo.searchType}
        onSearch={searchForFilter}
        filterName={selectedFilterInfo.filterName}
      />
      {internalSearchQuery && filterItems.length === 0 && <NoResultsFound />}
        <ul role='list' className="filter-options-list">
          {filterItems.map((filter) => (
            <li className={classNames('accordion-item', {
              'ed-ui': !isNewDesignEnabled,
              'plare': isNewDesignEnabled
            })} key={filter.id}>
              {renderFilterItem(filter)}
            </li>
          ))}
        </ul>
      </>;
}

export default connect((state: any) => ({ theme: state.theme?.get('themeId') }))(FilterList);