import { useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import removeTheFilterCategoryFromPreview, { clearAllFiltersFromCategory } from "../../utils/common";
import { CandidateFilterContext } from "../../candidate-filter-provider";
import { useContext } from "react";
import { formulateApiRequest } from "../../helpers";
import { handleEmptyFilters } from "../../ts-helper";
import { translatr } from "centralized-design-system/src/Translatr";

interface FilterCategoryProps {
    filterId: string;
    filterName: string;
    onChange: (value: object, masterSwitchValue: boolean) => void;
}
const FilterCategory: React.FC<FilterCategoryProps> = ({ filterId, filterName, onChange }) => {

    const { state, dispatch } = useContext(CandidateFilterContext);
    const { candidateFilterList, previewFilterItems, masterSwitch } = state;

    const {
        setCandidateFilterListDeepCopy,
        setPreviewFilterItems,
        setMasterSwitchWithInitial
    } = useCandidateFilterActions(dispatch);

    const clearFilterCategory = () => {
        const updatedFilterList = removeTheFilterCategoryFromPreview(filterId, previewFilterItems);
        setCandidateFilterListDeepCopy(clearAllFiltersFromCategory(filterId, candidateFilterList));
        setPreviewFilterItems(updatedFilterList);
        if (updatedFilterList.length === 0) {
            setMasterSwitchWithInitial(Object.keys(masterSwitch).length ? { ...masterSwitch, value: false  } : {});
        }
        const requestBody = formulateApiRequest(updatedFilterList);
        handleEmptyFilters(requestBody, dispatch);
        onChange(requestBody, masterSwitch?.value);
    };

    return <div className="title-container">
        <button onClick={() => clearFilterCategory()} aria-label={`${translatr('web.projects.main', 'Clear')} ${filterName}`}>
            <i className="icon-clear-filter reset-button-icon pointer" />
        </button>
        <span className="selected-filter-title" role="heading" aria-level="3">{filterName}: </span>
    </div>;
}

export default FilterCategory;
