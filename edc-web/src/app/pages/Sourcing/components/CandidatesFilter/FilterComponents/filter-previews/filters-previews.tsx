import { useContext, useRef } from 'react';
import PropTypes from 'prop-types';
import { CandidateFilterContext } from '../../candidate-filter-provider';
import { formulateApiRequest } from '../../helpers';
import {
  useCandidateFilterActions
} from '@pages/Sourcing/actions/manage-job-vacancy/candidate-filter';
import { removeTagFromCategoryPreviewList, removeTagFromCategoryFilterList, handleEmptyFilters, checkFilterIdPresentInCandidateList } from '../../ts-helper';
import PlusFilterButton from './plus-filter-button';
import FilterCategory from './filter-category';
import { FILTER_TYPE, TagInfo } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import CommonFilterTag from './filter-tag/common-filter-tag';
import DateRangeFilterTag from './filter-tag/date-range-filter-tag';
import { isDateFilterType } from '../../utils/common';

interface FiltersPreviewsProps {
  onChange: (value: object, masterSwitchValue: boolean) => void;
}

const FiltersPreviews: React.FC<FiltersPreviewsProps> = ({ onChange }) => {
  const parentDivRefs = useRef([]);
  const { state, dispatch } = useContext(CandidateFilterContext);
  const {
    previewFilterItems,
    candidateFilterList,
    masterSwitch
  } = state;

  const {
    setCandidateFilterListDeepCopy,
    setPreviewFilterItems
  } = useCandidateFilterActions(dispatch);

  const removeFilter = (tagId: string, filterId: string) => {
    const updatedList = removeTagFromCategoryFilterList(tagId, filterId, candidateFilterList);
    setCandidateFilterListDeepCopy(updatedList);
    const remainingPreviewFilterItems = removeTagFromCategoryPreviewList(
      tagId,
      filterId,
      previewFilterItems
    );
    const requestBody = formulateApiRequest(remainingPreviewFilterItems);
    setPreviewFilterItems(remainingPreviewFilterItems);
    handleEmptyFilters(requestBody, dispatch);
    onChange(requestBody, masterSwitch?.value);
  };

  return previewFilterItems.map(
    (row, index) =>
      row.tagInfo.length > 0 && (
        <div className="preview-container" key={row.filterName}>
          <FilterCategory filterId={row.filterId} filterName={row.filterName} onChange={onChange} />
          <div
            className="tag-container"
            id={`${row.filterName}`}
            ref={ref => (parentDivRefs.current[index] = ref)}
          >
            {row.tagInfo.map((tagData: TagInfo) => (
              row.filterType === FILTER_TYPE.DATE_PICKER ?
              <DateRangeFilterTag 
                filterId={row.filterId}
                tagId={tagData.id}
                filterTagText={tagData.filterTagText}
                removeFilter={removeFilter}
              /> :
              <CommonFilterTag
                filterId={row.filterId}
                tagId={tagData.id}
                label={tagData.label}
                filterTagText={tagData.filterTagText}
                removeFilter={removeFilter}
              />
            ))}
            {!isDateFilterType(row.filterId) && checkFilterIdPresentInCandidateList(row.filterId, candidateFilterList) &&
              <PlusFilterButton parentDivRefs={parentDivRefs} index={index} row={row} onChange={onChange} />
            }
          </div>
        </div>
      )
  );
};

FiltersPreviews.propTypes = {
  onChange: PropTypes.func
};

export default FiltersPreviews;
