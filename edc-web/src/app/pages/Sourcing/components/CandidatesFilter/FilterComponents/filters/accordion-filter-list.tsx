import Checkbox from 'centralized-design-system/src/Checkbox';
import { useCallback, useContext, useState } from 'react';
import { CandidateFilterContext } from '../../candidate-filter-provider';
import { useCandidateFilterActions } from '@pages/Sourcing/actions/manage-job-vacancy/candidate-filter';
import { preSelectForSearchResults, updateSubFiltersAndDependentSections } from '../../helpers';
import { translatr } from 'centralized-design-system/src/Translatr';
import { formatFilterLabel, getFilterDetailsById } from '../../utils/common';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { truncateText } from '@utils/utils';
import { CandidateFilterItem, FilterItem, SubFilter } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import { Item } from '@pages/Sourcing/types/manage-page/api/common.types';
import { OPTIONS } from '@pages/Sourcing/constants/filter/general-constants';
import { SUB_FILTERS } from '@pages/Sourcing/constants/filter/filter-labels';
import { CANDIDATE_FILTER_ACTION_TYPES } from '@pages/Sourcing/types/manage-page/context/candidate-filter';
import { transformSkillFilterItems } from '@pages/Sourcing/routes/manage-page/job-vacancy/utils/filter/filter-transformations';
import { formatJobRoleResponseForFilter } from '@pages/Sourcing/routes/manage-page/job-vacancy/utils/filter/common';
import SearchFilterPanel from '../search-filter-panel';
import ConfigurableLoadingCard from 'centralized-design-system/src/SkeletonAnimations/ConfigurableLoadingCard/ConfigurableLoadingCard';
import { handleMainAccordionCheckBox, updateFilterList } from '../../ts-helper';
import { connect } from 'react-redux';
import { getOmpRoles } from 'edc-web-sdk/requests/skills';
import _ from 'lodash';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import classNames from 'classnames';

interface AccordionFilterListProps {
  currentUser: {
    currentAppLanguage: string
  },
  theme: string
}

const AccordionFilterList: React.FC<AccordionFilterListProps> = ({ currentUser, theme }) => {

  const { state, dispatch } = useContext(CandidateFilterContext);
  const { selectedFilterInfo, candidateFilterList, interestsLabel } = state;
  const { setSelectedFilterState, setCandidateFilterList } = useCandidateFilterActions(dispatch);
  const [openIndex, setOpenIndex] = useState([]);
  const [internalSearchQuery, setInternalSearchQuery] = useState('');
  const [loadingFilterList, setLoadingFilterList] = useState(false);

  const accordionToggleHandler = (index: number) => {
    const isAlreadyPresent = openIndex.includes(index);
    if (isAlreadyPresent) {
      setOpenIndex(openIndex.filter(i => i !== index));
    } else {
      setOpenIndex([...openIndex, index]);
    }
  };

  const handleSubFilterValues = (sectionName: string, subFilterId: number, value: boolean, filterItemId: string) => {
    const updatedFilterValue = selectedFilterInfo.filterItems.map(item => {
      if (item.id === filterItemId) {
        const updatedSubFilters = updateSubFilters(
          item.subFilters,
          sectionName,
          subFilterId,
          value
        );

        const updatedSubFiltersAll = { ...item.subFilters, [sectionName]: updatedSubFilters };
        const isSelected =
          sectionName === OPTIONS && checkOtherSubFiltersAreSelected(updatedSubFiltersAll)
            ? true
            : updatedSubFilters.every((filter: Item) => !filter.isSelected)
              ? false
              : true;

        const updatedAllSubFilters = updateSubFiltersAndDependentSections(
          updatedSubFiltersAll,
          isSelected,
          sectionName,
          value
        );

        return {
          ...item,
          isSelected,
          subFilters: updatedAllSubFilters
        };
      }
      return item;
    });
    setSelectedFilterState({ ...selectedFilterInfo, filterItems: updatedFilterValue });
    updateFilterList({ ...selectedFilterInfo, filterItems: updatedFilterValue }, candidateFilterList, selectedFilterInfo, setCandidateFilterList);
  };

  const updateSubFilters = (subFilters: SubFilter, sectionName: string, id: number, value: boolean) =>
    //@ts-ignore
    subFilters[sectionName].map(filter =>
      filter.id === id ? { ...filter, isSelected: value } : filter
    );

  const checkOtherSubFiltersAreSelected = (updatedSubFiltersAll: object) => {
    return Object.values(updatedSubFiltersAll).some(
      filters => Array.isArray(filters) && filters.some(filter => filter.isSelected)
    );
  };
  const searchForFilter = async (value: string) => {
      setInternalSearchQuery(value.toLowerCase());
  };

  const searchForSkills = (value: FilterItem) => {
    const isValueAlreadyExists = selectedFilterInfo.filterItems.find(
      filter => filter.id === value.id
    );
    if (!isValueAlreadyExists) {
      const transformedSelectedSkills = transformSkillFilterItems(value, true, interestsLabel);
      const selectedSearchedSkill = {
        id: value.id,
        label: value.label,
        isSelected: true,
        subFilters: {
          ...transformedSelectedSkills.subFilters,
          items: transformedSelectedSkills.subFilters.items.map((item: Item) => ({
            ...item,
            isSelected: true
          })),
          options: transformedSelectedSkills.subFilters.options.map((option: Item) => ({
            ...option,
            isSelected: option.id === SUB_FILTERS.CURRENT.id ? true : false
          }))
        }
      };

      const updatedFilterValues = {
        ...selectedFilterInfo,
        filterItems: [selectedSearchedSkill, ...selectedFilterInfo.filterItems]
      };
      setSelectedFilterState(updatedFilterValues);
      updateFilterList(updatedFilterValues, candidateFilterList, selectedFilterInfo, setCandidateFilterList);
    } else {
      const filterItems = [];
      filterItems.push({
        ...isValueAlreadyExists,
        isSelected: true,
        subFilters: {
          ...transformSkillFilterItems(isValueAlreadyExists, true, interestsLabel).subFilters,
          items: transformSkillFilterItems(
            isValueAlreadyExists,
            true,
            interestsLabel
          ).subFilters.items.map((item: Item) => ({
            ...item,
            isSelected: true
          }))
        }
      });
      selectedFilterInfo.filterItems.forEach(filterItem => {
        if (isValueAlreadyExists.id !== filterItem.id) {
          filterItems.push(filterItem);
        }
      });
      const updatedFilterValues = { ...selectedFilterInfo, filterItems };
      updateFilterList(updatedFilterValues, candidateFilterList, selectedFilterInfo, setCandidateFilterList);
    }
  };

  const searchJobRole = useCallback(
    _.debounce(async (value: string, selectedFilterCategoryInfo: CandidateFilterItem) => {
      if (value) {
        const jobRole = await getOmpRoles({
          language: currentUser.currentAppLanguage,
          pageSize: 10,
          query: value
        });
        const formattedJobRoleData = formatJobRoleResponseForFilter(jobRole.topics);
        const updatedFilterItems = preSelectForSearchResults(
          formattedJobRoleData,
          selectedFilterCategoryInfo
        );
        dispatch({
          type: CANDIDATE_FILTER_ACTION_TYPES.SET_SELECTED_FILTER_INFO,
          selectedFilterInfo: { ...selectedFilterInfo, filterItems: updatedFilterItems }
        });
      } else {
        dispatch({
          type: CANDIDATE_FILTER_ACTION_TYPES.SET_SELECTED_FILTER_INFO,
          selectedFilterInfo: selectedFilterCategoryInfo
        });
      }
      setLoadingFilterList(false);
    }, 500),
    []
  );

  const loadingCardStyle = { padding: '5px 0px', overflow: 'hidden', display: 'grid', gap: 7 };

  const boxItemCommonConfig = {
    height: '2.188rem',
    marginBottom: '0.063rem',
    width: '17.6rem'
  };

  const boxItemsConfig = Array(6).fill(boxItemCommonConfig);

  const filterItems = selectedFilterInfo.filterItems.filter(item =>
    item.label.toLowerCase().includes(internalSearchQuery.toLowerCase())
  );

  const handleMainCheckBox = (value: boolean, filterItemId: string) => {
    handleMainAccordionCheckBox(value, filterItemId, selectedFilterInfo, candidateFilterList,
      setCandidateFilterList, setSelectedFilterState);
  };

  const searchForJobRole = (value: string) => {
    setLoadingFilterList(true);
    setInternalSearchQuery(value.toLowerCase());
    const selectedFilterCategoryInfo = getFilterDetailsById(
      selectedFilterInfo.filterId,
      candidateFilterList
    );
    searchJobRole(value.toLowerCase(), selectedFilterCategoryInfo);
  };

  const isNewDesignEnabled = theme == ThemeId.PLARE;

  return <>
    <SearchFilterPanel
      searchType={selectedFilterInfo.searchType}
      onSearch={searchForFilter}
      searchForSkills={searchForSkills}
      searchForJobRole={searchForJobRole}
      filterName={selectedFilterInfo.filterName}
    />
    {internalSearchQuery && !loadingFilterList && filterItems.length === 0 && (
      <div className="no-results" aria-live="polite">{translatr('cds.common.main', 'NoResultsFound')}</div>
    )}
    {
      <ul role='list' className="filter-options-list">
        {filterItems.map((filter, index) => (
          <li key={filter.id}>
            {loadingFilterList ? (
              <ConfigurableLoadingCard
                key={'candidate-filter-list-locading-card'}
                containerStyles={loadingCardStyle}
                boxItemsConfig={boxItemsConfig}
              />
            ) :
              <div className={classNames('accordion-item', {
                'ed-ui': !isNewDesignEnabled,
                'plare': isNewDesignEnabled
              })}
                key={filter.id}
              >
                <div className={isNewDesignEnabled ? 'filter-item-container' : ''}>
                  <Checkbox
                    label=''
                    className="check-box"
                    id={filter.id}
                    key={filter.id}
                    checked={filter.isSelected}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleMainCheckBox(e.target.checked, filter.id)}
                    ariaLabel={translatr('web.sourcing.candidate-profile', 'SelectAllLevelsOfSkill', { skill: filter.label})}
                  />
                  <button
                    className="accordion-title justflex"
                    onClick={() => accordionToggleHandler(index)}
                  >
                    <button
                      className={'down-arrow-button-icon'}
                      tabIndex={-1}
                    >
                      {openIndex.includes(index) ? (
                        <i className="icon-angle-down-arrow" />
                      ) : (
                        <i className="icon-angle-right-arrow" />
                      )}
                    </button>
                    {formatFilterLabel(filter).length > 30 ? (
                      <Tooltip
                        message={
                          <h2 className="font-size-l font-normal" aria-hidden="true">
                            {formatFilterLabel(filter)}
                          </h2>
                        }
                        ariaLabel={formatFilterLabel(filter)}
                        customClass={'width-300'}
                      >
                        <h2 className="font-size-l font-normal" aria-expanded={openIndex.includes(index)}>
                          {truncateText(formatFilterLabel(filter), 30, '...')}
                        </h2>
                      </Tooltip>
                    ) : (
                      <h2 className="font-size-l font-normal" aria-expanded={openIndex.includes(index)}>
                        {truncateText(formatFilterLabel(filter), 30, '...')}
                      </h2>
                    )}
                  </button>
                </div>
                {openIndex.includes(index) && (
                  <>
                    {filter.subFilters.items.map((subFilter: Item) => (
                      !subFilter.isMissingFromConfig ?
                      <div className="accordion-content" key={`${filter.id}-${subFilter.id}`}>
                        <Checkbox
                          label={subFilter.label}
                          id={`${filter.id}-${subFilter.id}`}
                          key={`${filter.label}-${subFilter.label}`}
                          className="check-box-levels"
                          checked={subFilter.isSelected}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            handleSubFilterValues(
                              'items',
                              subFilter.id,
                              e.target.checked,
                              filter.id
                            )
                          }
                        />
                      </div> : null
                    ))}
                    {filter.subFilters.options && filter.subFilters.options.length > 0 && (
                      <>
                        <hr className="center-hr-text" />
                        {filter.subFilters.options.map((subFilter: Item) => (
                          !subFilter.isMissingFromConfig ?
                          <div className="accordion-content" key={`${filter.id}-${subFilter.id}`}>
                            <Checkbox
                              label={subFilter.label}
                              id={`${filter.id}-${subFilter.id}`}
                              key={`${filter.label}-${subFilter.label}`}
                              className="check-box-levels"
                              checked={subFilter.isSelected}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                handleSubFilterValues(
                                  OPTIONS,
                                  subFilter.id,
                                  e.target.checked,
                                  filter.id
                                )
                              }
                            />
                          </div> : null
                        ))}
                      </>
                    )}
                  </>
                )}
              </div>}
          </li>
      ))}
      </ul>
    }
  </>
};

export default connect(({ currentUser, theme }: any) => ({ currentUser: currentUser.toJS(), theme: theme?.get('themeId') }))(AccordionFilterList);
