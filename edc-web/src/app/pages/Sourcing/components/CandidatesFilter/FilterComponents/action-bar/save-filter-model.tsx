import TextField from 'centralized-design-system/src/Inputs/TextField';
import <PERSON><PERSON>, {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>eader,
    ModalContent
} from 'centralized-design-system/src/Modals';
import { translatr } from "centralized-design-system/src/Translatr";
import { saveSavedSearches, updateSavedSearches } from 'edc-web-sdk/requests/sourcing';
import { useContext, useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
//@ts-ignore
import { open_v2 as openSnackBar } from 'actions/snackBarActions';
import Spinner from '@components/common/spinner';
import FocusLock from 'react-focus-lock';
import { CandidateFilterContext } from '../../candidate-filter-provider';
import { useCandidateFilterActions } from '@pages/Sourcing/actions/manage-job-vacancy/candidate-filter';
import { SAVED_FILTERS } from '@pages/Sourcing/constants/filter/filter-labels';
import { SavedFilters } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import { prepareFilterRequestBody } from '../../helpers/api-data-transformation';
import { FieldFooter } from '@pages/Projects/ProjectForm/Form';
import { formulateApiRequest } from '../../helpers';
import { useEscapeKey } from '@pages/Sourcing/hooks/useEscapeKey';
import { Button } from 'centralized-design-system/src/Buttons';

interface SaveFilterModelProps {
    handleCloseModal: () => void;
    toast: (message: string, type: string) => void;
}

const SaveFilterModel: React.FC<SaveFilterModelProps> = ({ handleCloseModal, toast }) => {

    const [hasError, setHasError] = useState({ uniqueFilter: '', lengthTooLong: false });
    const [isLoading, setIsLoading] = useState(false);
    const { state, dispatch } = useContext(CandidateFilterContext);
    const { savedFilters, savedFiltersDefaultValue, previewFilterItems, savedSearchFilterType, masterSwitch, masterSwitchInitialState } = state;
    const { setFiltersDefaultValue, setSavedFilterList, setCurrentFilterSettings } = useCandidateFilterActions(dispatch);
    const { SELECT_FILTER } = SAVED_FILTERS;
    const [filterName, setFilterName] = useState(savedFiltersDefaultValue.id === SELECT_FILTER.id ? '' : savedFiltersDefaultValue.value);
    useEscapeKey(() => handleCloseModal());
    const filterNameInputRef = useRef<HTMLInputElement>(null);

    const isCreateMode = savedFiltersDefaultValue.id === SELECT_FILTER.id;

    const handleSaveFilter = async (saveAsNew = false) => {
        setIsLoading(true); 
        const trimmedFilterName = filterName.trim();
        const previewFilterItemsDeepCopy = JSON.parse(JSON.stringify(previewFilterItems));
         // This will remove all isSelected and remove the unnecessary empty values key
        const prepareFilterCriteria = prepareFilterRequestBody(previewFilterItemsDeepCopy);
        try {
            const requestBody = {
                filterName: trimmedFilterName,
                filterType: savedSearchFilterType,
                filterCriteria: prepareFilterCriteria,
                filterConfiguration: {
                    isShowPartialMatchEnabled: masterSwitch?.value || false
                },
                ...(saveAsNew ? {
                    permissions: { isPublic: false }
                } : {
                    id: savedFiltersDefaultValue.id
                })
            };
    
            const savedResponse = await (saveAsNew ? saveSavedSearches(requestBody) : updateSavedSearches(requestBody));
    
            if (savedResponse) {
                if (saveAsNew) {
                    const allSavedFilters = [...savedFilters];
                    allSavedFilters.push({ ...savedResponse, filterCriteria: prepareFilterCriteria });
                    setSavedFilterList(allSavedFilters);
                } else {
                    const updatedSavedFilterData = savedFilters.map((savedFilter: SavedFilters) => {
                        if (savedFilter.id === savedFiltersDefaultValue.id) {
                            return { ...savedFilter, ...requestBody };
                        }
                        return savedFilter;
                    });
                    setSavedFilterList(updatedSavedFilterData);
                }   

                setFiltersDefaultValue({
                    id: savedResponse.id || savedFiltersDefaultValue.id,
                    value: trimmedFilterName
                });
                
                toast(
                    translatr('web.sourcing.candidate-profile',
                        saveAsNew ? 'SaveFilterSuccessMessage' : 'FilterUpdatedSuccessMessage'
                    ),
                    'success'
                );
                // Updating the last api request body to be used to hide the save filter button
                setCurrentFilterSettings({
                    filterCriteria: formulateApiRequest(previewFilterItems),
                    masterSwitchValue: masterSwitchInitialState?.value || false
                });
                handleCloseModal();
            }
        } catch (error) {
            const isNameConflict = error?.response?.status === 409;
            if (isNameConflict) {
                setHasError({
                    lengthTooLong: false,
                    uniqueFilter: trimmedFilterName
                });
                setTimeout(() => {
                    if (filterNameInputRef?.current)
                        filterNameInputRef.current.focus();
                }, 0);
            } else {
                toast(
                    translatr('web.sourcing.candidate-profile',
                        saveAsNew ? 'FilterUpdatedErrorMessage' : 'SaveFilterErrorMessage'
                    ),
                    'error'
                );
            }
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        const isFilterNameTooLong = filterName?.length > 100;
        if (isFilterNameTooLong !== hasError.lengthTooLong) {
            setHasError({
                uniqueFilter: '',
                lengthTooLong: isFilterNameTooLong
            });
        }
    }, [filterName]);

    const isActionButtonDisabled = !filterName.trim() || hasError.lengthTooLong;
    const showFilterNameAlreadyExistError = hasError.uniqueFilter === filterName && filterName !== '';

    return (
        <FocusLock>
            <Modal size="small">
                <ModalHeader title={translatr('web.sourcing.candidate-profile', 'SaveFilter')} onClose={handleCloseModal} />
                <ModalContent>
                    <div>
                        {isLoading ? <div
                            className='make-center width-100 mt-24 mb-24'
                            id="loading-timer"
                            role="progressbar"
                            aria-live="polite"
                        >
                            <span className="visually-hidden">
                                <Spinner />
                            </span>
                        </div>
                            :
                            <>
                                <div role="alert" aria-live="polite">
                                    <div className={hasError.lengthTooLong ? 'filter-name-container' : ''}>
                                        <TextField
                                            required
                                            id={'filter-name'}
                                            title={translatr('web.sourcing.candidate-profile', 'FilterName')}
                                            placeholder={translatr('web.sourcing.candidate-profile', 'EnterFilterNameHere')}
                                            setValue={(value: string) => setFilterName(value)}
                                            defaultValue={filterName}
                                            error={showFilterNameAlreadyExistError && translatr('web.sourcing.candidate-profile', 'FilterNameValidationMessage')}
                                            ariaDescribedby={translatr('web.sourcing.candidate-profile', 'FilterName')}
                                            ref={filterNameInputRef}
                                        />
                                    </div>
                                    {!showFilterNameAlreadyExistError &&
                                        <FieldFooter
                                            error={hasError.lengthTooLong && translatr('web.talentmarketplace.main', 'MaximumCharacterLimitReached')}
                                            length={filterName?.length}
                                            maxLength={100}
                                            suffix="left"
                                            type="text"
                                        />}
                                </div>
                            </>
                        }
                    </div>
                </ModalContent>
                <ModalFooter>
                    <Button
                        color="secondary"
                        onClick={handleCloseModal}
                        variant="ghost"
                        >
                        {translatr('web.common.main', 'Cancel')}
                    </Button>
                    {isCreateMode ?
                        <Button
                            color="primary"
                            onClick={() => handleSaveFilter(true)}
                            disabled={isActionButtonDisabled}
                        >
                          {translatr('web.common.main', 'Save')}
                        </Button>
                        :
                        <>
                            <Button
                                color="secondary"
                                disabled={isActionButtonDisabled}
                                onClick={() => handleSaveFilter(false)}
                                
                            >
                                {translatr('web.sourcing.candidate-profile', 'UpdateFilter')}
                            </Button>
                            <Button
                                color="primary"
                                disabled={isActionButtonDisabled}
                                onClick={() => handleSaveFilter(true)}
                            >
                                {translatr('web.sourcing.candidate-profile', 'SaveAsNew')}
                            </Button>
                        </>}
                </ModalFooter>
            </Modal>
        </FocusLock>
    );
};

const mapDispatchToProps = (dispatch: any) => ({
    toast: (message: string, type: string) => dispatch(openSnackBar(message, type))
});

export default connect(null, mapDispatchToProps)(SaveFilterModel);