import { useState, useRef, useContext } from "react";
import { CandidateFilterContext } from "../../candidate-filter-provider";
import { manageMenuOpenState, useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import classNames from "classnames";
import { getFilterDetailsById } from "../../utils/common";
import FilterTypeRenderer from "../filter-type-renderer";
import { translatr } from 'centralized-design-system/src/Translatr';


interface PlusFilterButtonProps {
    parentDivRefs: any;
    index: number;
    row: any;
    onChange: (value: object, masterSwitchValue: boolean) => void;
}
const PlusFilterButton: React.FC<PlusFilterButtonProps> = ({ parentDivRefs, index, row, onChange }) => {
    const buttonRefs = useRef([]);
    const [height, setHeight] = useState(0);
    const [width, setWidth] = useState(0);

    const { state, dispatch } = useContext(CandidateFilterContext);
    const {
        selectedFilterInfo,
        showFilterListOnPlus,
        candidateFilterList
    } = state;

    const {
        setFilterMenuOpen,
        setSelectedFilterState
    } = useCandidateFilterActions(dispatch);

    const handleAddMorePlusButton = (index: number) => {
        const parentDivRect = parentDivRefs.current[index]
            ? parentDivRefs.current[index].getBoundingClientRect()
            : null;
        const buttonRect = buttonRefs.current[index]
            ? buttonRefs.current[index].getBoundingClientRect()
            : null;

        const getMenuLeft = () => {
            if (parentDivRect && buttonRect) {
                if (parentDivRect.width - (buttonRect.left - parentDivRect.left) < 300) {
                    return buttonRect.left - parentDivRect.left - 300;
                } else return buttonRect.left - parentDivRect.left;
            }
            return 0;
        };

        setWidth(buttonRect ? getMenuLeft() : 0);
        setHeight(buttonRect ? buttonRect.bottom - parentDivRect.top : 0);
    };

    const handlePlusBtn = (status: boolean, filterId: string) => {
        const selectFilterDetails = getFilterDetailsById(filterId, candidateFilterList);
        manageMenuOpenState(status, dispatch);
        setSelectedFilterState(selectFilterDetails);
    };

    return <>
        <button
            className="specific-filter-add-button"
            ref={ref => (buttonRefs.current[index] = ref)}
            onClick={() => {
                handleAddMorePlusButton(index);
                setFilterMenuOpen(false);
                handlePlusBtn(!showFilterListOnPlus, row.filterId);
            }}
            aria-label={translatr('web.sourcing.candidate-profile', 'AddMoreCriteriaForType', { type: row.filterName })}
        >
            <span className="icon-plus"/>
        </button>
        {showFilterListOnPlus && row.filterId === selectedFilterInfo.filterId && (
            <div
                className={classNames('filter-list-container', {
                    'filter-list-container-padding': showFilterListOnPlus,
                    'filter-menu-container': showFilterListOnPlus
                })}
                style={{ left: width, top: height }}
            >
                <FilterTypeRenderer onChange={onChange} />
            </div>
        )}
    </>
}

export default PlusFilterButton;
