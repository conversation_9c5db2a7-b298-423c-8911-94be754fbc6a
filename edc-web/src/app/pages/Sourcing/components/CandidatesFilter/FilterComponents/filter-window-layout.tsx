import React, { useContext } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import '../candidates-filter.scss';
import { CandidateFilterContext } from '../candidate-filter-provider';
import {
  manageMenuOpenState,
  useCandidateFilterActions
} from '@pages/Sourcing/actions/manage-job-vacancy/candidate-filter';
import { formulateApiRequest } from '../helpers';
import { CandidateFilterItem } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import { restoreErroredFiltersToDefault } from '../ts-helper';
import { transformValuesForPreview } from '../helpers/filter-preview';
import { Button } from 'centralized-design-system/src/Buttons';

interface FilterWindowLayoutProps {
  children: React.ReactNode;
  selectedFilterInfo: CandidateFilterItem;
  showFilterListOnPlus: boolean;
  onChange: (value: object, masterSwitchValue: boolean) => void;
  addFilterButtonRef?: React.RefObject<HTMLButtonElement>;
}

const FilterWindowLayout: React.FC<FilterWindowLayoutProps> = ({ children, selectedFilterInfo, showFilterListOnPlus, onChange, addFilterButtonRef }) => {

  const { state, dispatch } = useContext(CandidateFilterContext);
  const { previewFilterItems, candidateFilterList, candidateFilterListCopy, filterErrors, masterSwitchInitialState, masterSwitch } = state;

  const {
    setCandidateFilterListCopy,
    setSelectedFilterState,
    setExpandedFilter,
    setPreviewFilterItems,
    setShowPreviewFilter,
    setCandidateFilterList,
    setFilterMenuDetailsPanel,
    setFilterMenuOpen,
    setFilterErrors,
    setMasterSwitchDetails,
    setMasterSwitchWithInitial
  } = useCandidateFilterActions(dispatch);
  /**
   * Manages focus by returning it to the add filter button after an operation
   * Uses setTimeout to ensure DOM updates complete before shifting focus
   */
  const manageAddFilterButtonFocus = () => {
    if (addFilterButtonRef && addFilterButtonRef.current) {
      setTimeout(() => {
        addFilterButtonRef.current?.focus();
      }, 0);
    }
  };

  const onCancel = () => {
    setCandidateFilterList(candidateFilterListCopy);
    setMasterSwitchDetails(masterSwitchInitialState);
    cleanupFilterState();
    manageAddFilterButtonFocus();
  };

  const saveButtonHandler = () => {
    const previewFilterList = transformValuesForPreview(candidateFilterList, previewFilterItems);
    setPreviewFilterItems(previewFilterList);
    if (previewFilterList.length > 0) setExpandedFilter(true);
    setShowPreviewFilter(true);
    const payloadRequest = formulateApiRequest(previewFilterList);
    onChange(payloadRequest, masterSwitch?.value);
    const restoredFilterList = restoreErroredFiltersToDefault(candidateFilterList, candidateFilterListCopy, filterErrors);
    setCandidateFilterListCopy(restoredFilterList);
    setCandidateFilterList(restoredFilterList);
    setMasterSwitchWithInitial(Object.keys(payloadRequest).length ? masterSwitch : masterSwitchInitialState);
    cleanupFilterState();
    manageAddFilterButtonFocus();
  };

  const cleanupFilterState = () => {
    manageMenuOpenState(false, dispatch);
    setSelectedFilterState({});
    setFilterErrors([]);
  }

  const handleBackButton = (status: boolean) => {
    setFilterMenuDetailsPanel(status);
    setFilterMenuOpen(true);
  };
  return (
    <>
      <div className="header-title-container">
        {!showFilterListOnPlus && (
          <button
            onClick={() => handleBackButton(false)}
            className="back-arrow-button-icon"
            aria-label={`${translatr('web.common.main', 'Back')} ${selectedFilterInfo.filterName}`}
            autoFocus
          >
            <i className="icon-path" />
          </button>
        )}
        <span className="specific-filter-title" role="heading" aria-level={2}>{selectedFilterInfo.filterName}</span>
        {selectedFilterInfo.smallLabel && (
          <span className="universal-filter-sub-lable sub-label-text">
            {selectedFilterInfo.smallLabel}
          </span>
        )}
      </div>
      {children}
      <div className="button-container justflex justify-center">
        <Button
            color="secondary"
            onClick={onCancel}
            variant="ghost"
            size="medium"
            aria-label={translatr('web.common.main', 'Cancel')}
            >
              {translatr('web.common.main', 'Cancel')}
            </Button>
        <Button
            color="primary"
            onClick={() => saveButtonHandler()}
            size="medium"
            aria-label={translatr('web.common.main', 'Save')}
            >
              {translatr('web.common.main', 'Save')}
            </Button>
      </div>
    </>
  );
};

export default FilterWindowLayout;
