import React, { useContext, useEffect, useRef } from 'react';
import { CandidateFilterItem } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import { connect } from 'react-redux';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import classNames from 'classnames';
import {
  CandidateFilterContext
} from '@pages/Sourcing/components/CandidatesFilter/candidate-filter-provider';

interface FilterMenuListProps {
  filterList: CandidateFilterItem[];
  filterHandler: (filterId: string) => void;
  theme: string;
}

const FilterMenuList: React.FC<FilterMenuListProps> = ({ filterList, filterHandler, theme }) => {
  const { state } = useContext(CandidateFilterContext);
  const { selectedFilterInfo, showMenuDetails } = state;
  const isNewDesignEnabled = theme == ThemeId.PLARE;

  const itemRefs = useRef<{ [key: string]: HTMLButtonElement }>({});

  useEffect(() => {
    if (!showMenuDetails && selectedFilterInfo && selectedFilterInfo.filterId) {
      const itemToFocus = itemRefs.current[selectedFilterInfo.filterId];
      if (itemToFocus) {
        itemToFocus.focus();
      }
    }
  }, [showMenuDetails, selectedFilterInfo]);

  const renderMenuItem = ({ filterId, filterName, smallLabel }: CandidateFilterItem) => (
    <li key={filterName}>
      <button
        className={classNames('filter-menu-item', { 'plare-theme': isNewDesignEnabled, 'ed-ui': !isNewDesignEnabled })}
        onClick={() => filterHandler(filterId)}
        type="button"
        ref={(btnRef) => btnRef && (itemRefs.current[filterId] = btnRef)}
        role="menuitem"
      >
        {filterName}
        {smallLabel && (
          <span className="sub-label-text">
            {smallLabel}
          </span>
        )}
      </button>
    </li>
  );

  return (
    <ul className="filter-menu-list" role="menu">
      {filterList.map(renderMenuItem)}
    </ul>
  );
}

export default connect((state: any) => ({ theme: state.theme?.get('themeId') }))(FilterMenuList);
