import { useContext } from 'react';
import FilterWindowLayout from './filter-window-layout';
import classNames from 'classnames';
import '../candidates-filter.scss';
import { CandidateFilterContext } from '../candidate-filter-provider';
import FilterList from './filters/filter-list';
import AccordionFilterList from './filters/accordion-filter-list';
import { FILTER_TYPE } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import DateFilter from './filters/date-filter';

interface FilterTypeRendererProps {
  onChange: (value: object, masterSwitchValue: boolean) => void;
  addFilterButtonRef?: React.RefObject<HTMLButtonElement>;
}
const FilterTypeRenderer: React.FC<FilterTypeRendererProps> = ({ onChange, addFilterButtonRef }) => {

  const { state } = useContext(CandidateFilterContext);
  const { selectedFilterInfo, showFilterListOnPlus } = state;

  const isAccordionType = selectedFilterInfo.filterType === FILTER_TYPE.LIST_WITH_ACCORDION;

  const containerClassNames = selectedFilterInfo.filterType === FILTER_TYPE.DATE_PICKER 
  ? ''
  : classNames('specific-filters-list-container', {
      'levels-container': isAccordionType,
      'container': selectedFilterInfo.filterType === FILTER_TYPE.LIST
    });

  const getFilterComponent = () => {
    switch (selectedFilterInfo.filterType) {
      case FILTER_TYPE.LIST_WITH_ACCORDION:
        return <AccordionFilterList />;
      case FILTER_TYPE.DATE_PICKER:
        return <DateFilter />;
      default:
        return <FilterList />;
    }
  };
  
  return (
    <FilterWindowLayout
      selectedFilterInfo={selectedFilterInfo}
      showFilterListOnPlus={showFilterListOnPlus}
      key={selectedFilterInfo.filterId}
      onChange={onChange}
      addFilterButtonRef={addFilterButtonRef}
    >
       <div className={containerClassNames}>
        {getFilterComponent()}
      </div>
    </FilterWindowLayout>
  );
};

export default FilterTypeRenderer;
