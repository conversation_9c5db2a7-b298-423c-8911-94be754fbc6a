import React, { LegacyRef, useContext, useRef, useState } from 'react';
import "../../candidates-filter.scss";
import { translatr } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { CandidateFilterContext } from '../../candidate-filter-provider';
import { manageMenuOpenState, useCandidateFilterActions } from '@pages/Sourcing/actions/manage-job-vacancy/candidate-filter';
//@ts-ignore
import cloneDeep from 'lodash/cloneDeep';
import { CandidateFilterItem, FilterSection } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import FilterCountDisplay from './filter-count-display';
import SaveFilterModel from './save-filter-model';
import ActionButton from './action-button';
import SavedFiltersDropdown from './saved-filters-dropdown';
import { SAVED_FILTERS } from '@pages/Sourcing/constants/filter/filter-labels';
import { getSaveFiltersButtonVisibility } from '../../helpers/filter-visibility';
import { State } from '@pages/Sourcing/types/manage-page/context/candidate-filter';
import { Button } from 'centralized-design-system/src/Buttons';

interface ActionPanelProps {
  getFilterData: (setFilterList: (result: object) => void) => {} | Promise<any>;
  onChange: (value: object, masterSwitchValue: boolean) => void;
  addFilterButtonRef: React.Ref<HTMLButtonElement>
  handleKeyDown: React.KeyboardEventHandler<HTMLButtonElement>;
}

interface AddFilterButtonProps {
  onClick: (event: React.MouseEvent) => void
  buttonRef: React.Ref<HTMLButtonElement>
  isFilterMenuOpen: boolean
  handleKeyDown: React.KeyboardEventHandler<HTMLElement>;
}

interface FilterActionsProps {
  onSave: () => void;
  onClear: () => void;
  state: State;
  actionButtonRef?: React.RefObject<HTMLButtonElement>;
}

interface ExpandCollapseButtonProps {
  expandedFilter: boolean;
  setExpandedFilter: (value: boolean) => void;
}

const ActionPanel: React.FC<ActionPanelProps> = ({ onChange, addFilterButtonRef, getFilterData, handleKeyDown }) => {

  const [showSaveFilterModel, setShowSaveFilterModel] = useState(false);

  const { state, dispatch } = useContext(CandidateFilterContext);
  const {
    previewFilterItems,
    expandedFilter,
    candidateFilterList,
    isFilterMenuOpen,
    savedFilters,
    savedSearchFilterType,
    masterSwitch
  } = state;

  const {
    setExpandedFilter,
    resetCandidateFilterList,
    setSelectedFilterState,
    setIsFilterListLoading,
    setCandidateFilterListDeepCopy,
    setShowPreviewFilter,
    setPreviewFilterItems,
    setFilterMenuOpeningPosition,
    setFiltersDefaultValue,
    setCurrentFilterSettings,
    setMasterSwitchWithInitial
  } = useCandidateFilterActions(dispatch);

  const saveFilterButtonRef = useRef<HTMLButtonElement>(null);

  const handleClearFilterButton = async () => {
    onChange({}, false);
    resetCandidateFilterList();
    setShowPreviewFilter(false);
    setPreviewFilterItems([]);
    setFiltersDefaultValue(SAVED_FILTERS.SELECT_FILTER);
    setCurrentFilterSettings({});
    // Reset the master switch to ensure the master filtering value is disabled when clearing filters.
    setMasterSwitchWithInitial(Object.keys(masterSwitch).length ? { ...masterSwitch, value: false  } : {});
    await getFilterData((result: CandidateFilterItem) => {
      setIsFilterListLoading(false);
      setCandidateFilterListDeepCopy(cloneDeep(result));
      manageMenuOpenState(false, dispatch, FilterSection.MainMenu);
      setSelectedFilterState({});
    });
  };

  const handleAddFilterButton = (event: React.MouseEvent) => {
    setMenuOpeningPosition(event);
    if (candidateFilterList.length) {
      manageMenuOpenState(!isFilterMenuOpen, dispatch, FilterSection.MainMenu);
      setSelectedFilterState({});
    } else {
      setIsFilterListLoading(true);
      getFilterData((result: CandidateFilterItem) => {
        setIsFilterListLoading(false);
        setCandidateFilterListDeepCopy(cloneDeep(result));
        manageMenuOpenState(!isFilterMenuOpen, dispatch, FilterSection.MainMenu);
        setSelectedFilterState({});
      });
    }
  };

  const setMenuOpeningPosition = (event: React.MouseEvent) => {
    const { offsetLeft, offsetTop, offsetHeight } = event.currentTarget as HTMLButtonElement;
    setFilterMenuOpeningPosition({
      left: `${offsetLeft / 16}rem`,
      top: `${(offsetTop + offsetHeight) / 16}rem`
    });
  };

  const handleCloseModal = () => {
    setShowSaveFilterModel(false);
    setTimeout(() => {
      saveFilterButtonRef.current?.focus();
    }, 0);
  };

  return (<div className="filter-section-header">
    {showSaveFilterModel && <SaveFilterModel handleCloseModal={handleCloseModal} />}
    <div className="add-button-section">
      {savedSearchFilterType && savedFilters.length > 0 &&
        <SavedFiltersDropdown
          onChange={onChange}
          handleClearFilterButton={handleClearFilterButton}
        />}
     <AddFilterButton onClick={handleAddFilterButton} buttonRef={addFilterButtonRef}
                      isFilterMenuOpen={isFilterMenuOpen} handleKeyDown={handleKeyDown} />
      {previewFilterItems.length > 0 && (
          <FilterActions
            state={state}
            onSave={() => setShowSaveFilterModel(true)}
            onClear={handleClearFilterButton}
            actionButtonRef={saveFilterButtonRef}
          />
        )}
    </div>
    {previewFilterItems.length > 0 && (
        <ExpandCollapseButton
          expandedFilter={expandedFilter}
          setExpandedFilter={setExpandedFilter}
        />
      )}
  </div>)
};

const AddFilterButton: React.FC<AddFilterButtonProps> = ({ onClick, buttonRef, isFilterMenuOpen, handleKeyDown }) => {
  return (
    <div className='add-filter-button'>
      <Button
        data-testid="add-filter-button"
        color="secondary" variant="ghost"
        onClick={onClick}
        onKeyDown={handleKeyDown}
        ref={buttonRef}
        aria-expanded={isFilterMenuOpen}
        aria-haspopup="true"
        aria-label={translatr('web.common.main', 'AddFilter')}
        tabIndex={0}>
        <span className="icon-plus"/> {translatr('web.common.main', 'AddFilter')}
      </Button>
    </div>
  );
};

const FilterActions: React.FC<FilterActionsProps> = ({ state, onSave, onClear, actionButtonRef }) => {
  const { savedSearchFilterType, savedFiltersDefaultValue, previewFilterItems, currentFilterSettings, masterSwitchInitialState } = state;
  const saveFiltersButtonVisible = React.useMemo(() =>
    savedSearchFilterType && getSaveFiltersButtonVisibility(state),
    [savedFiltersDefaultValue, previewFilterItems, currentFilterSettings, masterSwitchInitialState]
  );

  return <>
    {saveFiltersButtonVisible && (
      <ActionButton
        title={translatr('web.sourcing.candidate-profile', 'SaveFilters')}
        onClick={onSave}
        additionalClass='save-filter-button'
        actionButtonRef={actionButtonRef}
      />
    )}
    <div className="divider-container">
      <hr className="center-hr-text clear-filter-divider" role="presentation" />
    </div>
    {state.savedSearchFilterType && <FilterCountDisplay />}
    <ActionButton
      title={translatr('web.sourcing.candidate-profile', 'ClearFilters')}
      onClick={onClear}
    />
  </>
};

const ExpandCollapseButton: React.FC<ExpandCollapseButtonProps> = ({ expandedFilter, setExpandedFilter }) => {

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setExpandedFilter(!expandedFilter);
    }
  };

  return (
      <div onKeyDown={handleKeyDown}>
      <Tooltip
        message={
          expandedFilter
            ? translatr('web.sourcing.candidate-profile', 'Collapse')
            : translatr('web.sourcing.candidate-profile', 'Expand')
        }
      >
        <button
          className={`icon-angle-down-arrow cursor-pointer transition-expand-collapse expand-collapse-btn ${expandedFilter &&
            'rotate-icon'}`}
          aria-label={translatr('web.common.main', 'Filters')}
          aria-expanded={expandedFilter}
          onClick={() => setExpandedFilter(!expandedFilter)}
        ></button>
      </Tooltip>
      </div>
  );
}

export default ActionPanel;
