import { Select } from "centralized-design-system/src/Inputs";
import { translatr } from "centralized-design-system/src/Translatr";
import React, { useContext, useEffect } from "react";
import { CandidateFilterContext } from "../../candidate-filter-provider";
import { SavedFilters, SelectItem } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import { useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import { formulateApiRequest } from "../../helpers";
import { SAVED_FILTERS } from "@pages/Sourcing/constants/filter/filter-labels";
import processSavedFilters from "../../helpers/process-saved-filters";
interface SavedFiltersDropdownProps {
    onChange: (value: object, masterSwitchValue: boolean) => void;
    handleClearFilterButton: () => void;
}
const SavedFiltersDropdown: React.FC<SavedFiltersDropdownProps> = ({ onChange, handleClearFilterButton }) => {

    const { state, dispatch } = useContext(CandidateFilterContext);
    const { savedFilters, savedFiltersDefaultValue, savedFiltersDropItems } = state;
    const {
        setShowPreviewFilter,
        setExpandedFilter,
        setSavedFiltersDropItems,
        setFiltersDefaultValue,
        setCurrentFilterSettings,
    } = useCandidateFilterActions(dispatch);

    useEffect(() => {
        const filterList: any[] = savedFilters.map((filter: SavedFilters) => ({
            id: filter.id,
            value: filter.filterName
        }));
        filterList.unshift(SAVED_FILTERS.SELECT_FILTER);
        setSavedFiltersDropItems(filterList);
    }, [savedFilters]);

    const setFilter = (option: SelectItem) => {
        if (option.id) {
            const selectedFilterItem = savedFilters.find((filter: SavedFilters) => filter.id === option.id);
            setFiltersDefaultValue(option);
            // This will save preview filters from this, and add the data to candidate list and isSelected to each row. 
            const previewFilterList = processSavedFilters(selectedFilterItem, state, dispatch);
            const payloadRequest = formulateApiRequest(previewFilterList);
            setCurrentFilterSettings({
                filterCriteria: payloadRequest,
                masterSwitchValue: selectedFilterItem?.filterConfiguration?.isShowPartialMatchEnabled || false
            });
            setShowPreviewFilter(true);
            setExpandedFilter(true);
            onChange(payloadRequest, selectedFilterItem?.filterConfiguration?.isShowPartialMatchEnabled);
        } else {
            handleClearFilterButton();
            setCurrentFilterSettings({});
        }
    };

    return (
        <>
            <span id="saved-filters-dropdown-label" className='normal-text saved-filters-label'>
                {translatr('web.sourcing.candidate-profile', 'SavedFilters')}:
            </span>
            <div className='saved-filters-dropdown'>
                <Select
                    id='saved-filters-dropdown'
                    items={savedFiltersDropItems}
                    onChange={(e: SelectItem) => setFilter(e)}
                    defaultValue={savedFiltersDefaultValue.value}
                    ariaLabelledBy="saved-filters-dropdown-label"
                />
            </div>
        </>
    )
}

export default SavedFiltersDropdown;