@import '~centralized-design-system/src/Styles/_variables.scss';

.filter-header {
  background: var(--ed-white);
  color: var(--ed-text-color-primary);
  padding: var(--ed-spacing-base);
  border-radius: var(--ed-spacing-4xs);
  box-shadow: var(--ed-shadow-sm);
  font-size: var(--ed-font-size-lg);
  margin-top: 1.063rem;
  position: relative;
}

.trash-icon {
  font-size: 0.875;
  padding: 0;
  &:hover {
    color: var(--ed-negative-1);
    border-color: var(--ed-negative-1);
  }
}

.down-arrow-button-icon {
  width: 1.5rem;
  font-size: 1.188rem;
}

.filter-list-container {
  position: absolute;
  white-space: nowrap;
  font-size: var(--ed-font-size-sm);
  z-index: 100;
  margin: 0rem !important;
  padding: 0rem !important;
  background-color: var(--ed-white) !important;
  border-radius: var(--ed-border-radius-md);
  box-shadow: 0rem 0.125rem 0.375rem rgba(38, 39, 59, 0.14);
  min-width: 18.625rem;
  .loading-card {
    overflow: hidden;
  }

  .switch.cursor-pointer {
    padding-left: 0rem !important;
    margin-left: -1.188rem !important;
  }

  .solid-line {
    margin: var(--ed-spacing-2xs) 0rem;
  }

  .master-switch-container {
    display: flex;
    flex-wrap: wrap;

    .master-switch {
      width: 2.938rem;
      margin-right: var(--ed-spacing-xs);
    }

    .master-switch-label {
      width: 5.625rem;
      word-break: break-word;
      flex-grow: 1;
      overflow: hidden;
      white-space: normal;
      font-size: var(--ed-font-size-sm);
      font-weight: var(--ed-font-weight-bold);
      color: var(--ed-neutral-4);
    }
  }
}

.add-filter-button {
  height: 2.5rem;
}

.filter-list-container-padding {
  padding: var(--ed-spacing-2xs) !important;
}

.filter-menu-container {
  top: 100%;
}

.filter-menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.filter-menu-list li {
  margin: 0;
  padding: 0;
}

.filter-menu-item {
  font-family: Open Sans;
  font-size: var(--ed-font-size-sm);
  color: var(--ed-text-color-primary);
  display: flex;
  width: 100%;
  height: auto;
  &.plare-theme {
    &:hover {
      background-color: var(--ed-blue-5);
    }
    padding: var(--ed-spacing-xs) var(--ed-spacing-2xs);
    border-radius: var(--ed-border-radius-md);
  }
  &.ed-ui {
    &:hover {
      background-color: var(--ed-input-hover-bg-color);
    }
    padding: var(--ed-spacing-xs) var(--ed-spacing-base);
  }
}

.specific-filters-list-container {
  max-height: 18.75rem;
  overflow-y: auto;
  .filter-options-list {
    margin: 0rem;
  }
}

.levels-container {
  height: 18.75rem;
}

.accordion-item {
  margin-top: 0.625rem;

  .accordion-title {
    display: inline-flex !important;
    align-items: center;
    grid-gap: 0.188rem;
    width: 90% !important;
    padding: 0px !important;
    h2 {
      margin-bottom: 0;
    }
  }

  &.ed-ui {
    border-radius: var(--ed-border-radius-lg);
    background-color: var(--ed-neutral-7);
    padding: 0.65rem 0.5rem 0.5rem;
    border: none !important;
  }

  &.plare {
    padding: 0.313rem 0.5rem;
    border: none !important;
    .filter-item-container {
      display: flex;
      align-items: center;
      height: 1.563rem;
    }
  }
}

.accordion-content {
  padding: var(--ed-spacing-3xs) 0rem 0rem 1.563rem !important;
  border: none !important;
}

.check-box {
  display: contents;
  &.checkbox {
    display: inline !important;
    margin-right: -0.125rem !important;
    font-size: var(--ed-font-size-sm);
  }
}

.check-box-levels {
  &.checkbox .ed-checkbox-label {
    font-size: var(--ed-font-size-sm);
  }
}

.button-container {
  border-top: var(--ed-border-size-sm) solid var(--ed-gray-2);
  padding: var(--ed-spacing-xl) 0rem var(--ed-spacing-base);
  margin-top: var(--ed-spacing-base);
}

.specific-filter-add-button {
  cursor: pointer;
  font-size: var(--ed-font-size-lg) !important;
  color: var(--ed-neutral-3) !important;
  margin-left: var(--ed-spacing-sm);
  position: absolute;
  margin-top: var(--ed-spacing-3xs);
}

.selected-filter-title {
  font-size: var(--ed-font-size-sm);
  font-weight: var(--ed-font-weight-bold);
  margin-right: var(--ed-spacing-base);
}

.show-filter-container {
  padding-top: var(--ed-spacing-base) !important;
}

.icon-clear-filter {
  margin: 0rem !important;
  margin: 0 var(--ed-spacing-base);
  margin-right: var(--ed-spacing-base) !important;
  display: inline-flex !important;
  font-size: 1.125rem;
}

.tag-container {
  display: inline-table;
  position: relative;
}

.preview-container {
  padding-top: var(--ed-spacing-xs) !important;
  display: grid;
  grid-template-columns: auto 1fr;
}

.topics-chips {
  margin: 0.25rem;
  border: var(--ed-border-size-sm) solid var(--ed-border-color);
  padding: var(--ed-spacing-5xs) var(--ed-spacing-sm);
  border-radius: 3.125rem;
  color: var(--ed-text-color-primary);
  line-height: inherit;
  .filter-title-tag {
    font-weight: var(--ed-font-weight-bold);
    font-size: var(--ed-font-size-sm);
  }
  &.plare {
    border: none;
    background-color: var(--ed-gray-1);
  }
}

.back-arrow-button-icon {
  transform: rotateZ(180deg);
  color: var(--ed-primary-base);
  cursor: pointer;
  .icon-path {
    font-size: var(--ed-font-size-xs);
    font-weight: var(--ed-font-weight-bold) !important;
  }
}

.specific-filter-title {
  font-size: var(--ed-font-size-xs);
  color: var(--ed-primary-base);
  font-weight: var(--ed-font-weight-bold);
}

.title-container {
  .icon-clear-filter {
    &:hover {
      color: var(--ed-negative-1);
      border-color: var(--ed-negative-1);
    }
  }
}

.header-title-container {
  display: flex;
  padding: var(--ed-spacing-xs);
}

.sub-label-text {
  align-items: center;
  justify-content: center;
  display: flex;
  width: 4.125rem;
  height: 1.063rem;
  font-family: Open Sans;
  font-size: var(--ed-font-size-xs) !important;
  color: var(--ed-text-color-supporting);
}

.universal-filter-sub-lable {
  margin-top: var(--ed-spacing-5xs);
}

.center-hr-text {
  margin: 0rem;
  margin-left: 1.5rem;
}

.no-results {
  height: 3.125rem;
  align-items: center;
  justify-content: center;
  display: flex;
  font-size: var(--ed-font-size-base);
}

.filter-section-header {
  display: flex;
}

.divider-container {
  margin: 0rem 0.9rem 0rem 1rem;
}
.add-button-section {
  flex-grow: 1;
  display: flex;
  .icon-plus {
    margin-right: var(--ed-spacing-4xs);
  }
}

.expand-btn {
  margin-left: 0.625rem;
  cursor: pointer;
}

.clear-filter-divider {
  margin: var(--ed-spacing-xl) 0;
  position: relative;
  display: inline-block;
  transform: rotate(90deg);
  margin-bottom: 0rem;
  margin-top: 0.438rem !important;
  color: var(--ed-gray-6);
}

.rotate-icon {
  rotate: 180deg;
}

.expand-collapse-btn {
  margin-right: var(--ed-spacing-xs);
  color: var(--ed-gray-6);
  font-size: var(--ed-font-size-2xl);
}

.date-filter-container {
  display: flex;
  gap: var(--ed-spacing-2xs);
  .center-hr-text {
    margin: 0rem !important;
    width: 0.625rem;
  }

  .supporting-text {
    margin-bottom: var(--ed-spacing-3xs);
  }
}

.date-picker-divider-container {
  margin-top: 3.313rem;
  .center-hr-text::before {
    background-color: var(--ed-black);
  }
}

.date-filter-item {
  margin-top: 1.25rem;
}

.date-filter-component {
  &.has-error {
    .react-datepicker-wrapper {
      border-color: var(--ed-negative-darken-1);
    }
  }
  .react-datepicker-wrapper {
    width: 10.375rem !important;
  }
}

.error-message {
  margin-top: 0.313rem;
}

.error-message-color {
  color: var(--ed-negative-1);
}

.field-label-weight {
  font-weight: var(--ed-font-weight-semibold);
}

.save-filter-button {
  margin-left: var(--ed-spacing-3xl);
}

.saved-filters-label {
  margin-right: var(--ed-spacing-xs);
  margin-top: 0.375rem;
}

.normal-text {
  font-size: var(--ed-font-size-base);
  font-weight: var(--ed-font-weight-normal);
}

.active-primary-text-color {
  color: var(--ed-state-active-color);
}

.saved-filters-dropdown {
  width: 12.5rem;
  margin-right: var(--ed-spacing-lg);
}

.action-button {
  height: 2.375rem;
  cursor: pointer;
}

.filter-count-display-container {
  display: flex;
  padding-top: var(--ed-spacing-2xs);
  .title-filters {
    margin-right: var(--ed-spacing-2xs);
    font-weight: var(--ed-font-weight-bold);
  }

  .filter-count-display {
    max-width: 41.125rem;
    margin-right: var(--ed-spacing-3xl);
    &.has-saved-filters {
      max-width: 20rem;
    }
    &.no-saved-filters {
      max-width: 41.125rem;
    }
  }
}

.text-field-meta-group {
  display: flex;
  justify-content: space-between;
  font-size: var(--ed-font-size-sm);

  &.input-error {
    * {
      color: var(--ed-negative-1);
    }
  }
}

.filter-name-container {
  .ed-input-container .input-group .input-field {
    border-color: var(--ed-negative-2);

    &:focus {
      border-color: var(--ed-negative-2);
    }
  }
}

.checkbox-label-wrapper {
  font-size: var(--ed-font-size-base);
  font-weight: var(--ed-font-weight-normal);
  display: flex;
  color: var(--ed-text-color-primary);
}
