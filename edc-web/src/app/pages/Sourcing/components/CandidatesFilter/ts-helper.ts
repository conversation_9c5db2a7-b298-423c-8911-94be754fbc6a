import { CandidateFilterItem,
  FILTER_TYPE,
  FilterItem,
  FilterItemError,
  PreviewFilterItem,
  SavedFilters,
  SubFilter,
  TagInfo
} from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import { addNewValuesWithExistingList, SEARCH_TYPE, setAllSubFiltersToFalse } from "./helpers";
import { FILTERS, SUB_FILTERS } from "@pages/Sourcing/constants/filter/filter-labels";
import { useCandidateFilterActions } from "@pages/Sourcing/actions/manage-job-vacancy/candidate-filter";
import { isDateFilterType } from "./utils/common";
import { JobVacancyRequestBody } from "@pages/Sourcing/types/main-page/api/api.types";

export const removeTagFromCategoryPreviewList = (tagId: string, filterId: string, previewFilterItems: PreviewFilterItem[]) =>
  previewFilterItems.map(filter => {
    if (filter.filterId === filterId) {
      const filteredData = filter.tagInfo.filter((data: TagInfo) => data.id !== tagId);
      return { ...filter, tagInfo: filteredData };
    }
    return filter;
  });

export const removeTagFromCategoryFilterList = (filterTagId: string, filterId: string, candidateFilterList: CandidateFilterItem[]) => {
  const candidateFilterData = [...candidateFilterList];
  candidateFilterData.forEach(filter => {
    if (filter.filterId === filterId) {
      const { filterItems } = filter;
      if (filter.filterType === FILTER_TYPE.DATE_PICKER) {
        filterItems.forEach((item: FilterItem) => {
          if (item.id === filterTagId) {
            item.isSelected = false;
            item.dateRange = resetDateRangeIfIncomplete(item.dateRange)
          }
        });
      } else filterItems.forEach((item: FilterItem) => {
        if (item.id === filterTagId) {
          item.isSelected = false;
          setAllSubFiltersToFalse(item.subFilters);
        }
      });
    }
  });
  return candidateFilterData;
};

export const updateFilterList = (values: CandidateFilterItem, candidateFilterList: CandidateFilterItem[], selectedFilterInfo: CandidateFilterItem, setCandidateFilterList: (value: CandidateFilterItem[]) => void) => {
  const filterDataList = candidateFilterList.map((filterRow: CandidateFilterItem) => {
    if (filterRow.filterId === selectedFilterInfo.filterId) {
      // This will be used for external job role search
      if (selectedFilterInfo.searchType === SEARCH_TYPE.EXTERNAL_JOB_ROLE) {
        return addNewValuesWithExistingList(filterRow, values);
      } else return values;
    }
    return filterRow;
  });
  setCandidateFilterList(filterDataList);
};

export const handleMainAccordionCheckBox = (value: boolean, filterItemId: string, selectedFilterInfo: CandidateFilterItem,
  candidateFilterList: CandidateFilterItem[], setCandidateFilterList: (value: CandidateFilterItem[]) => void, setSelectedFilterState: (value: CandidateFilterItem) => void) => {
  const updatedFilterValue = {
    ...selectedFilterInfo,
    filterItems: selectedFilterInfo.filterItems.map(item =>
      item.id === filterItemId
        ? {
          ...item,
          isSelected: value,
          subFilters: Object.keys(item.subFilters).length ? updateSubFilters(selectedFilterInfo.filterId, value, item.subFilters) : {}
        }
        : item
    )
  };
  setSelectedFilterState(updatedFilterValue);
  updateFilterList(updatedFilterValue, candidateFilterList, selectedFilterInfo, setCandidateFilterList);
};

export const updateSubFilters = (filterId: string, value: boolean, subFilters: SubFilter) => ({
  ...subFilters,
  items: subFilters.items?.map(item =>
    ({
      ...item,
      isSelected: item.isMissingFromConfig ? item.isSelected : value
    })),
  options: subFilters.options?.map(option => ({
    ...option,
    isSelected: option?.isMissingFromConfig ? option.isSelected :
    (filterId === FILTERS.SKILLS.id ? option.id === SUB_FILTERS.CURRENT.id && value : value)
  }))
});

export const handleEmptyFilters = (requestBody: { [key: string]: any }, dispatch: (value: any) => void) => {
  const filterActions = useCandidateFilterActions(dispatch);
  const shouldResetFilters = Object.keys(requestBody).length === 0;
  if (shouldResetFilters) {
    const {
      resetCandidateFilterList,
      setShowPreviewFilter,
      setPreviewFilterItems
    } = filterActions;

    resetCandidateFilterList();
    setShowPreviewFilter(false);
    setPreviewFilterItems([]);
  }
};

export const addSelectedFilterInfoInCandidateFilterList = (selectedFilterInfo: CandidateFilterItem, candidateFilterList: CandidateFilterItem[]) => {
  const updatedCandidateFilterList = [...candidateFilterList];
  const filterIndex = updatedCandidateFilterList.findIndex(filter => filter.filterId === selectedFilterInfo.filterId);
  if (filterIndex !== -1) {
    updatedCandidateFilterList[filterIndex] = selectedFilterInfo;
  }
  return updatedCandidateFilterList;
};

export const isEndDateGreater = (fromDate: Date, toDate: Date) => new Date(toDate) < new Date(fromDate);

export const updateFilterErrors = (
  filterId: string,
  filterItemId: string,
  message: string,
  candidateFilterErrors: FilterItemError[],
  type: 'warning' = 'warning'
): FilterItemError[] => {
  const updatedErrors = [...candidateFilterErrors];
  const filterIndex = updatedErrors.findIndex(error => error.filterId === filterId);
  if (filterIndex !== -1) {
    const filterItemIndex = updatedErrors[filterIndex].filterItems.findIndex(item => item.filterItemId === filterItemId);
    if (filterItemIndex !== -1) {
      updatedErrors[filterIndex].filterItems[filterItemIndex] = {
        filterItemId,
        message,
        type
      };
    } else {
      updatedErrors[filterIndex].filterItems.push({
        filterItemId,
        message,
        type
      });
    }
  } else {
    updatedErrors.push({
      filterId,
      filterItems: [{
        filterItemId,
        message,
        type
      }]
    });
  }
  return updatedErrors;
};

export const removeFilterError = (
  currentErrors: FilterItemError[],
  filterIdToRemove: string,
  filterItemIdToRemove: string = ''
) => {
  if (!filterItemIdToRemove) {
    return currentErrors.filter(error => error.filterId !== filterIdToRemove);
  }

  const updatedErrorList = currentErrors.map(error => {
    if (error.filterId === filterIdToRemove) {
      const filteredItems = error.filterItems.filter(
        item => item.filterItemId !== filterItemIdToRemove
      );
      return {
        ...error,
        filterItems: filteredItems.length ? [filteredItems[0]] : []
      };
    }
    return error;
  }).filter(error => error.filterItems.length > 0) as FilterItemError[];
  return updatedErrorList;
};

export const restoreErroredFiltersToDefault = (
  candidateFilterList: CandidateFilterItem[],
  candidateFilterListCopy: CandidateFilterItem[],
  filterErrors: FilterItemError[]
) => candidateFilterList.map(filterRow => {
  const { filterId, filterItems } = filterRow;
  const hasError = filterErrors.some(error => error.filterId === filterId);
  const isDateFilter = isDateFilterType(filterId);

  if (hasError) {
    return restoreToDefaultValue(filterRow, candidateFilterListCopy, isDateFilter);
  }

  if (isDateFilter) {
    return {
      ...filterRow,
      filterItems: filterItems.map((item: FilterItem) =>
        item.dateRange.fromDate && item.dateRange.toDate
          ? item
          : { ...item, dateRange: resetDateRangeIfIncomplete(item.dateRange) }
      )
    };
  }

  return filterRow;
});

const restoreToDefaultValue = (filterRow: CandidateFilterItem, candidateFilterListCopy: CandidateFilterItem[], isDateFilter: boolean) => {
  return isDateFilter
    ? {
      ...filterRow,
      filterItems: filterRow.filterItems.map(item => ({
        ...item,
        dateRange: resetDateRangeIfIncomplete(item.dateRange)
      }))
    }
    : candidateFilterListCopy.find(filter => filter.filterId === filterRow.filterId);
};

export const resetDateRangeIfIncomplete = (dateRange: { fromDate?: Date, toDate?: Date }): { fromDate?: Date, toDate?: Date } => {
  return {
    ...dateRange,
    fromDate: undefined,
    toDate: undefined
  };
};

export const checkFilterIdPresentInCandidateList = (filterId: string, candidateFilterList: CandidateFilterItem[]) =>
  candidateFilterList.find((filter: CandidateFilterItem) => filter.filterId === filterId);

export const extractPrivateFilters = (savedFilterList: SavedFilters[], savedSearchFilterType: string) =>
  savedFilterList.filter((filter: SavedFilters) =>
    !filter?.permissions?.isPublic && filter.filterType === savedSearchFilterType
  );