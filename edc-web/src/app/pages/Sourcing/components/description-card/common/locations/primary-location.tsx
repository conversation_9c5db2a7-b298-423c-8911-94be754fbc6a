import React from 'react';
import { ompLov, translatr } from 'centralized-design-system/src/Translatr';
import { resolveLocationData } from './utils';
import { Location } from '../../../../types/manage-page/api/common.types';
import { selectAvailableLocations } from '@pages/Sourcing/selectors/main-page/selectors';
import { useSelector } from 'react-redux';

interface PrimaryLocationProps {
  remote: string
  locations: Array<Location>
}

const PrimaryLocation: React.FC<PrimaryLocationProps>  = ({ remote, locations }) => {

  const primaryLocation = locations.find(({ primary }) => primary);
  const availableLocations = useSelector(selectAvailableLocations);
  const locationData = resolveLocationData(primaryLocation, availableLocations);

  if (remote && primaryLocation) {
    return `(${ompLov('workplace_model', remote)}), ${locationData}`;
  }
  if (primaryLocation) {
    return locationData;
  } else {
    return translatr('web.common.main', 'NotSpecified');
  }
}

export default PrimaryLocation;
