import { Location } from '@pages/Sourcing/types/manage-page/api/common.types';
import { translatr } from 'centralized-design-system/src/Translatr';

const formatLocation = (city: string, country: string) => {
  if (city || country) {
    return `${city ? city + ', ' : ''}${country ? country : ''}`;
  } else {
    return translatr('web.common.main', 'NotSpecified');
  }
};

const resolveLocationData = (location: Location, availableLocations: Array<Location>) => {
  // Returns location data for the given locationId if it exists in locations;
  // otherwise, returns data for the primaryLocationId.
  const locationData = availableLocations.find(({ id }) => id === location?.locationId);
  if (locationData) {
    return locationData.location_name || translatr('web.common.main', 'NotSpecified');
  }
  return formatLocation(location.city, location.country)
}

export { resolveLocationData };
