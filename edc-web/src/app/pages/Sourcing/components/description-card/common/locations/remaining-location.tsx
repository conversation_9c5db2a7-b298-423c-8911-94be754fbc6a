import React from 'react';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Location } from '../../../../types/manage-page/api/common.types';
import { resolveLocationData } from './utils';
import { useSelector } from 'react-redux';
import { selectAvailableLocations } from '@pages/Sourcing/selectors/manage-page/selectors';

interface RemainingLocationProps {
  locations: Array<Location>
}

const RemainingLocation: React.FC<RemainingLocationProps> = ({ locations }) => {
  const remainingLocations = locations.filter(({ primary }) => !primary);
  const availableLocations = useSelector(selectAvailableLocations);

  if (remainingLocations.length === 0) {
    return null;
  }
  
  return (
    <Tooltip
      message={
        <ul>
          {remainingLocations.map((location) => (
            <li key={`${location.locationId || location.externalLocationId || ''}-${location.city || ''}-${location.country || ''}`}>
              {resolveLocationData(location, availableLocations)}
            </li>
          ))}
        </ul>
      }
    >
      <span className="locations-view-more">
        +{remainingLocations.length} {translatr('web.sourcing.candidate-profile', 'More')}
      </span>
    </Tooltip>
  );
}

export default RemainingLocation;
