import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import PrimaryLocation from '../../common/locations/primary-location';
import { Location } from '../../../../types/manage-page/api/common.types';
import { Organization } from '@pages/Sourcing/types/manage-page/api/api.types';
import DateDetails from '../../common/date-details/date-details';
import { formatJobFamily, formatOrganizations } from '../utils/common';
import OrgUnitInfo from '../../common/org-unit-info/org-unit-info';
import VerticalDivider from '../../common/vertical-divider/vertical-divider';
import { useSelector } from 'react-redux';
import { selectOrgTypes } from '@pages/Sourcing/selectors/manage-page/selectors';
import { toMap } from '@pages/TalentMarketplace/shared/CandidateListing/utils/org_units';
interface InfoSectionProps {
  id: string
  title: string
  referenceNumber: string
  locations: Array<Location>
  remote: string
  endDate: string
  startDate: string
  jobFunctionName: string
  jobFamilyName: string
  organizations: Organization[]
}

const InfoSection: React.FC<InfoSectionProps> = ({ id, title, referenceNumber, locations, remote, startDate, endDate, jobFunctionName, jobFamilyName, organizations }) => {
  const location = useLocation();
  const path = location.pathname;
  const orgTypes = toMap(useSelector(selectOrgTypes));
  const remainingLocations = locations.filter(({ primary }) => !primary);
  const jobFamily = formatJobFamily(jobFunctionName, jobFamilyName);
  const organizationName = formatOrganizations(organizations, orgTypes);

  return (
    <div className="desc-col-1 flex flex-column">
      <Link to={`${path}/manage/job_vacancy/${encodeURIComponent(id)}`} className="link-title">
        <div className="title-txt-normal mb-0 job-title-base-size" role="heading" aria-level={2}>{title}</div>
      </Link>
      <div className="info-description">
        <p className="vacancy-description title-info-container">
          <div className="info-section-flex-container">
            {referenceNumber &&
              <div className='info-section-divider-container'>
                {`# ${referenceNumber}`}
                <VerticalDivider />
              </div>}
            {jobFamily &&
              <div className='info-section-divider-container'>
                {jobFamily}
                <VerticalDivider />
              </div>}
            {organizationName &&
              <div className='info-section-divider-container'>
                <OrgUnitInfo orgInfo={organizationName} />
                <VerticalDivider />
              </div>}
            <div className="info-section-divider-container">
              <div className="icon-map-marker icon-style" aria-label={translatr('web.talentmarketplace.main', 'Locations')}
                   role="img"></div>{' '}
              {remainingLocations.length ?
                translatr('web.common.main', 'MultipleLocations') :
                <PrimaryLocation remote={remote} locations={locations} />
              }
            </div>
          </div>
          <DateDetails endDate={endDate} startDate={startDate} />
        </p>
      </div>
    </div>
  );
};

export default InfoSection;
