import React from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import PrimaryLocation from '../../common/locations/primary-location';
import RemainingLocation from '../../common/locations/remaining-location';
import { Location } from '../../../../types/manage-page/api/common.types';
import DateDetails from '../../common/date-details/date-details';
import { Organization } from '@pages/Sourcing/types/manage-page/api/api.types';
import { formatJobFamily, formatOrganizations } from '../utils/common';
import OrgUnitInfo from '../../common/org-unit-info/org-unit-info';
import VerticalDivider from '../../common/vertical-divider/vertical-divider';
import { toMap } from '@pages/TalentMarketplace/shared/CandidateListing/utils/org_units';
import { useSelector } from 'react-redux';
import { selectOrgTypes } from '@pages/Sourcing/selectors/manage-page/selectors';
interface InfoSectionProps {
  id: string
  title: string
  referenceNumber: string
  locations: Array<Location>
  remote: string
  endDate: string
  startDate: string
  jobFunctionName: string
  jobFamilyName: string
  organizations: Organization[]
}

const InfoSection: React.FC<InfoSectionProps> = ({ title, referenceNumber, remote, locations, endDate, startDate, jobFunctionName, jobFamilyName, organizations }) => {

  const orgTypes = toMap(useSelector(selectOrgTypes));
  const jobFamily = formatJobFamily(jobFunctionName, jobFamilyName);
  const organizationName = formatOrganizations(organizations, orgTypes);

  return (
    <div className="desc-col-1 flex flex-column">
      <p className="desc-card-tag">
        {translatr('web.sourcing.candidate-profile', 'JobVacancy')}
      </p>
      <h1 className="desc-card-vacancy-title">{title}</h1>
      <div className="info-description">
        <p className="vacancy-description title-info-container">
          <div className="info-section-flex-container">
            {referenceNumber &&
              <div className="info-section-divider-container">
                {`# ${referenceNumber}`}
                <VerticalDivider />
              </div>}
            {jobFamily &&
              <div className="info-section-divider-container">
                {jobFamily}
                <VerticalDivider />
              </div>}
            {organizationName &&
              <div className="info-section-divider-container">
                <OrgUnitInfo orgInfo={organizationName} />
                <VerticalDivider />
              </div>}
              <div className="icon-map-marker icon-style"
                   aria-label={translatr('web.talentmarketplace.main', 'Locations')}
                   role={'img'}></div>{' '}
            <PrimaryLocation remote={remote} locations={locations} />
            {locations.length > 1 && (
              <>
                {' '}
                <RemainingLocation locations={locations} />
              </>
            )}
          </div>
          <DateDetails startDate={startDate} endDate={endDate}/>
        </p>
      </div>
    </div>
  )
}

export default InfoSection;
