@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.desc-card {
  display: flex;
  background: var(--ed-white);
  padding: var(--ed-spacing-base);

  &.desc-card-with-margin {
    box-shadow: var(--ed-shadow-sm);
    border-radius: var(-ed-border-radius-md);
    margin-top: var(--ed-spacing-base);
  }
}

.ts-desc-card-container {
  min-height: 6.625rem;
  &.edcast-ui {
    box-shadow: var(--ed-shadow-base);
    border-radius: var(--ed-border-radius-lg);
    margin-top: var(--ed-spacing-base);
  }
}

.desc-col-1 {
  div:nth-child(3) {
    display: flex;
    gap: 5px;
  }
  padding-right: var(--ed-spacing-xs);
}

.desc-col-2 {
  display: flex;
  gap: 5px;
  .ed-dropdown {
    margin-left: var(--ed-spacing-lg);
    margin-right: 0;
    margin-top: var(--ed-spacing-xs);
    color: var(--ed-text-color-primary);
    i {
      font-size: 1.6875rem;
      line-height: 1rem;
      font-weight: var(--ed-font-weight-black);
    }
  }
}

.desc-stats-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .stats-heading {
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 0.5rem;
    line-height: 1.4;
  }
}

.job-title-base-size {
  font-size: var(--ed-font-size-base);
}

.menu-color {
  color: var(--ed-black) !important;
}

.desc-card-tag {
  color: var(--ed-gray-6) !important;
  font-size: var(--ed-font-size-sm) !important;
}

.dash-line-container {
  width: 1.5rem;
  margin-top: 0.313rem;
  &.ed-ui {
    height: 80%;
  }
  &.plare {
    height: 95%;
  }
}

.title-info-container {
  line-height: 1.2rem;
}

.card-options {
  .dropdown-content {
    box-shadow: var(--ed-shadow-sm) !important;
    border-radius: var(--ed-spacing-4xs) !important;
  }

  .sourcing-dropdown-list {
    font-size: var(--ed-font-size-base);
    cursor: pointer;

    &:not(.plate-theme) {
      li {
        padding: 0 !important;

        button {
          padding: rem-calc(6) rem-calc(14);
          display: flex;
          justify-content: space-between;
          width: 100%;
        }
      }

      li:hover {
        background-color: var(--ed-input-hover-bg-color);
      }
    }

    &.plate-theme {
      padding: 0rem var(--ed-spacing-xs);

      li {
        padding: 0 !important;
        padding: var(--ed-spacing-xs) var(--ed-spacing-2xs);
        border-radius: var(--ed-border-radius-md);

        button {
          padding: var(--ed-spacing-3xs) var(--ed-spacing-xs);
          display: flex;
          justify-content: space-between;
          width: 100%;
        }
      }

      li:hover {
        background-color: var(--ed-blue-5);
      }
    }
  }
}

.text-underline {
  text-decoration: underline;
}

.locations-view-more {
  color: var(--ed-text-color-supporting);
  text-decoration: underline;
}

.desc-count {
  margin: 0;
  font-size: var(--ed-font-size-lg);
  font-weight: var(--ed-font-weight-black);
}

.info-description {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex-grow: 1;
  .vacancy-description {
    color: var(--ed-gray-6) !important;
    font-weight: var(--ed-font-weight-normal) !important;
    font-size: var(--ed-font-size-sm) !important;
  }
}

.link-title {
  text-decoration: none;
  font-weight: var(--ed-font-weight-black);
  color: var(--ed-gray-6);
}

.link-title:hover {
  text-decoration: underline;
  font-weight: var(--ed-font-weight-black);
  color: var(--ed-gray-6);
}

.link-title:focus,
.link-title.active {
  text-decoration: underline;
}

.org-unit-ino-tooltip {
  display: inline-flex !important;
}

.vertical-divider {
  padding: var(--ed-spacing-2xs);
  border-left: var(--ed-border-size-sm) solid var(--ed-gray-2);
  height: inherit;
  display: inline-flex;
  margin-left: var(--ed-spacing-xs);
}

.info-section-divider-container {
  display: inline-flex;
}

.desc-card-vacancy-title {
  color: var(--ed-text-color-primary) !important;
  font-weight: var(--ed-font-weight-bold);
  font-size: var(--ed-font-size-lg) !important;
}

.info-section-flex-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: var(--ed-spacing-4xs);
}

.icon-style {
  margin-right: var(--ed-spacing-4xs);
  margin-top: var(--ed-spacing-4xs);
}
