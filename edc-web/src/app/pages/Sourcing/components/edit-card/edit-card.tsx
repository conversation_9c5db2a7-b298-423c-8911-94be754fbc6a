import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import TagsWithExpand from 'opportunity-marketplace/shared/CandidateListing/TagsWithExpand/TagsWithExpand';
import { getAllProficiencyLevels } from 'centralized-design-system/src/Utils/proficiencyLevels';
import { translatr, ompLov } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { selectCurrentLanguage } from '../../selectors/manage-page/selectors';
import {
  SkillsLevelGroup,
  Skill,
  LinkedRoleStatusDetail
} from '../../types/manage-page/api/api.types';
import './styles.scss';
import { Button } from 'centralized-design-system/src/Buttons';
import { useNavigate } from 'react-router-dom';

interface EditCardProps {
  view: string
  data: Array<any>
}

const EditCard: React.FC<EditCardProps> = ({ view, data }) => {
  const location = useLocation();
  const language = useSelector(selectCurrentLanguage);
  const navigate = useNavigate();
  const maxTagsToDisplay = 5;

  const LinkedRoles = () => {
    const linkedJobsData = data.reduce((acc, curr) => {
      const { descriptions } = curr as LinkedRoleStatusDetail;
      const isFound = descriptions.find(({ isDefault }) => isDefault);
      if (!isFound) return acc;

      acc.push(isFound.title);
      return acc;
    }, []);

    return (
      <TagsWithExpand
        tagsToDisplay={linkedJobsData}
        initialMaxDisplayTags={maxTagsToDisplay}
        displayClass="tag-display-edit-card"
        ariaLabelAttr={{
          number: (linkedJobsData.length) - maxTagsToDisplay,
          labelName: '',
          sectionName: `${translatr('web.sourcing.candidate-profile', 'Linked', { labelName: ompLov('tm_job_roles') })}`
        }}
      />
    );
  };

  const RelatedSkills = () => {
    const groupBySkillsLevel: Array<SkillsLevelGroup> = getAllProficiencyLevels(
      window.__edOrgData.proficiencyLevels,
      language
    );

    data.forEach((skill: Skill) => {
      if (skill.levelProficiency !== null) {
        const levelIdx = groupBySkillsLevel.findIndex(
          level =>
            skill.levelProficiency >= parseFloat(level.range.from) &&
            skill.levelProficiency <= parseFloat(level.range.to)
        );

        groupBySkillsLevel[levelIdx].skills = groupBySkillsLevel[levelIdx].skills
          ? [...groupBySkillsLevel[levelIdx].skills, skill]
          : [skill];
      }
    });

    const getToolTipDescription = (translatedLevelDescription: string, descriptions: string) => {
      const trimmedTranslatedLevelDescription = translatedLevelDescription
        ? translatedLevelDescription.trim()
        : '';
      const trimmedDescriptions = descriptions ? descriptions.trim() : '';
      return trimmedTranslatedLevelDescription || trimmedDescriptions;
    };

    return (
      <div className="skills-section">
        {groupBySkillsLevel.map(
          ({ label, descriptions, translatedLevelDescription, skills = [] }) => {
            if (!skills.length) return null;

            const skillsList = skills.map(skill => skill.label || skill.name || skill.id);
            return (
              <div key={label}>
                {view !== 'Linked Roles' &&
                getToolTipDescription(translatedLevelDescription, descriptions) ? (
                  <Tooltip
                    message={getToolTipDescription(translatedLevelDescription, descriptions)}
                    aria-label={getToolTipDescription(translatedLevelDescription, descriptions)}
                    tabIndex="0"
                    tooltipParentRole="button"
                  >
                    <div className="skills-level-label" role="heading" aria-level={3}>{label}</div>
                  </Tooltip>
                ) : (
                  <div className="skills-level-label" role="heading" aria-level={3}>{label}</div>
                )}
                <TagsWithExpand
                  tagsToDisplay={skillsList}
                  initialMaxDisplayTags={maxTagsToDisplay}
                  displayClass="tag-display-edit-card"
                  ariaLabelAttr={{
                    number: (skillsList.length) - maxTagsToDisplay,
                    labelName: '',
                    sectionName: `${label} ${translatr('web.sourcing.candidate-profile', 'RelatedSkills')}`
                  }}
                />
              </div>
            );
          }
        )}
      </div>
    );
  };

  const getUrl = () => {
    const slug = view === 'Linked Roles' ? 'roles' : 'skills';
    return `${location.pathname}/${slug}/edit`;
  };

  return (
    <div className="edit-card">
      <div className="edit-col-1">
        <h2 className="desc-card-vacancy-title">
          {view === 'Linked Roles'
            ? `${translatr('web.sourcing.candidate-profile', 'Linked', { labelName: ompLov('tm_job_roles') })}`
            : translatr('web.sourcing.candidate-profile', 'RelatedSkills')}
        </h2>
        <Button
          color="secondary"
          variant="borderless"
          padding="xsmall"
          size="medium"
          onClick={() => navigate(getUrl())}
          aria-label={view === 'Linked Roles'
            ? `${translatr('web.sourcing.candidate-profile', 'Edit')} ${translatr('web.sourcing.candidate-profile', 'Linked', { labelName: ompLov('tm_job_roles') })}`
            : translatr('web.sourcing.candidate-profile', 'EditRelatedSkills')}
        >
          {translatr('web.sourcing.candidate-profile', 'Edit')}
        </Button>
      </div>
      <div className="edit-col-2">
        {view === 'Linked Roles' ? <LinkedRoles /> : <RelatedSkills />}
      </div>
    </div>
  );
};

export default EditCard;
