import { extractPrivateFilters } from '@pages/Sourcing/components/CandidatesFilter/ts-helper';
import { JobVacancyRequestBody } from '@pages/Sourcing/types/main-page/api/api.types';
import { CandidateFilterItem, CurrentFilterSettings, FilterItemError, FilterSection, MasterSwitch, PositionDimensions, PreviewFilterItem, SavedFilters, SelectItem } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';
import { Action, CANDIDATE_FILTER_ACTION_TYPES } from '@pages/Sourcing/types/manage-page/context/candidate-filter';
import { getSavedSearches } from 'edc-web-sdk/requests/sourcing';
//@ts-ignore
import cloneDeep from 'lodash/cloneDeep';

export const useCandidateFilterActions = (dispatch: any) => {
    return {
        setIsFilterListLoading: (status: boolean) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_IS_FILTER_LIST_LOADING,
                isFilterListLoading: status
            });
        },
        setCandidateFilterListDeepCopy: (result: CandidateFilterItem[]) => {
            const deepCopiedFilterData = cloneDeep(result);
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST,
                candidateFilterList: deepCopiedFilterData
            });
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST_COPY,
                candidateFilterListCopy: deepCopiedFilterData
            });
        },
        setCandidateFilterList: (result: CandidateFilterItem[]) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST,
                candidateFilterList: result
            });
        },
        setCandidateFilterListCopy: (result: CandidateFilterItem[]) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_CANDIDATE_FILTERS_LIST_COPY,
                candidateFilterListCopy: cloneDeep(result)
            });
        },
        setSelectedFilterState: (selectedFilterDetails: CandidateFilterItem | {}) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_SELECTED_FILTER_INFO,
                selectedFilterInfo: selectedFilterDetails
            });
        },
        setFilterMenuOpen: (menuState: boolean) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_MENU_OPEN,
                isFilterMenuOpen: menuState
            });
        },
        setFilterMenuDetailsPanel: (menuDetailsStatus: boolean) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_MENU_DETAILS,
                showMenuDetails: menuDetailsStatus
            });
        },
        setFilterListStateOnPlusButton: (status: boolean) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_FILTER_LIST_ON_PLUS,
                showFilterListOnPlus: status
            });
        },
        setExpandedFilter: (status: boolean) =>
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_EXPANDED_FILTER,
                expandedFilter: status
            }),
        resetCandidateFilterList: () => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.RESET_CANDIDATE_FILTERS_LIST
        }),
        setPreviewFilterItems: (previewFilterItems: PreviewFilterItem[]) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_PREVIEW_FILTER_ITEMS,
            previewFilterItems: previewFilterItems
        }),
        setShowPreviewFilter: (status: boolean) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_SHOW_FILTER_PREVIEW,
            showFilterPreview: status
        }),
        setFilterErrors: (filterErrors: FilterItemError[]) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_ERRORS,
            filterErrors: filterErrors
        }),
        setSavedFilterList: (savedFilterList: SavedFilters[]) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS,
            savedFilters: savedFilterList
        }),
        setFiltersDefaultValue: (savedFiltersDefaultValue: SelectItem) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS_DEFAULT_VALUE,
            savedFiltersDefaultValue
        }),
        setFilterMenuOpeningPosition: (position: PositionDimensions) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_FILTER_MENU_OPENING_POSITION,
            filterMenuOpeningPosition: position
        }),
        setSavedFiltersDropItems: (savedFiltersDropItems: SelectItem[]) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_FILTERS_DROPDOWN_ITEMS,
            savedFiltersDropItems
        }),
        setSavedSearchFilterType: (savedSearchFilterType: string) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_SAVED_SEARCH_FILTER_TYPE,
            savedSearchFilterType
        }),
        setCurrentFilterSettings: (currentFilterSettings: CurrentFilterSettings | {}) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_CURRENT_FILTER_SETTINGS,
            currentFilterSettings
        }),
        setMasterSwitchDetails: (masterSwitch: MasterSwitch | {}) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_DETAILS,
            masterSwitch
        }),
        setMasterSwitchInitialState: (masterSwitchInitialState: MasterSwitch | {}) => dispatch({
            type: CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_INITIAL_STATE,
            masterSwitchInitialState
        }),
        setMasterSwitchWithInitial: (masterSwitch: MasterSwitch | {}) => {
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_DETAILS,
                masterSwitch
            })
            dispatch({
                type: CANDIDATE_FILTER_ACTION_TYPES.SET_MASTER_SWITCH_INITIAL_STATE,
                masterSwitchInitialState: masterSwitch
            })
        }
    };
};

export const manageMenuOpenState = (status: boolean, dispatch: React.Dispatch<Action>, key: FilterSection = FilterSection.Default) => {

    const { setFilterMenuOpen, setFilterMenuDetailsPanel, setFilterListStateOnPlusButton } = useCandidateFilterActions(dispatch);

    const updateStates: Record<FilterSection, any> = {
        MainMenu: {
            isFilterMenuOpen: status,
            showMenuDetails: false,
            showFilterListOnPlus: false
        },
        MenuList: {
            isFilterMenuOpen: false,
            showMenuDetails: status,
            showFilterListOnPlus: false
        },
        default: {
            isFilterMenuOpen: false,
            showMenuDetails: false,
            showFilterListOnPlus: status
        }
    };

    const currentState  = updateStates[key] || updateStates.default;
    setFilterMenuOpen(currentState.isFilterMenuOpen,);
    setFilterMenuDetailsPanel(currentState.showMenuDetails);
    setFilterListStateOnPlusButton(currentState.showFilterListOnPlus);
};

export const fetchAndSetSavedSearches = async (
    setSavedSearchFilterType: React.Dispatch<React.SetStateAction<string>>,
    setSavedFilterList: React.Dispatch<React.SetStateAction<SavedFilters[]>>,
    savedSearchFilterType: string
) => {
    setSavedSearchFilterType(savedSearchFilterType);
    const savedFilters = await getSavedSearches();
    if (savedFilters && savedFilters.values?.length) {
        const privateFilters = extractPrivateFilters(savedFilters.values, savedSearchFilterType);
        setSavedFilterList(privateFilters);
    }
};

export const fetchFilterDataAndSetCandidateFilterList = async (
    getFilterData: (setFilterList: (result: object) => void) => {} | Promise<any>,
    dispatch: React.Dispatch<any>
) => {
    const { setCandidateFilterListDeepCopy, setSelectedFilterState } = useCandidateFilterActions(dispatch);
    await getFilterData((result: CandidateFilterItem) => {
        setCandidateFilterListDeepCopy(cloneDeep(result));
        manageMenuOpenState(false, dispatch, FilterSection.MainMenu);
        setSelectedFilterState({});
    });
}