import React from 'react';
import { searchJobVacancies } from 'edc-web-sdk/requests/sourcing';
import {
  Action,
  MAIN_PAGE_ACTION_TYPES
} from '../../types/main-page/context/main-page';
import { JobVacancyData, JobVacancyRequestBody, SourcingFilterData } from '../../types/main-page/api/api.types';
import { SOMETHING_WENT_WRONG } from '../../constants/errors';
import { getOrgUnitsById, getFamliyIds, getJobFunctionByIds } from 'edc-web-sdk/requests/hrData.v2';
import { JobVacancy, Organization } from '@pages/Sourcing/types/manage-page/api/api.types';
import { getAvailableFiltersData } from '@pages/Sourcing/routes/main-page/utils/filter/setup-vacancy-filters';
import { saveSourcingFiltersData } from '@actions/sourcingActions';

const fetchOpportunitiesDataAndDetails = (language: string) => {
  return getAvailableFiltersData(language)
  .then((response) => {
    const organizationsById: string[] | Promise<any> =
      response.organizations ? getOrganizationDetails(Object.keys(response.organizations), language) : [];
    const jobFamilyData: string[] | Promise<any> = response.jobFamily ? generateJobFamilyAndFunctionName(Object.keys(response.jobFamily)) : [];
    // Extracting the data for organization and job family and function by ids
    return Promise.allSettled([organizationsById, jobFamilyData])
      .then((results) => {
        const [organizationsResult, jobFamilyResult] = results;
        if (organizationsResult.status === "fulfilled" && jobFamilyResult.status === "fulfilled") {
          return {
            ...response,
            organizations: organizationsResult.value,
            jobFamily: jobFamilyResult.value
          };
        } else {
          throw new Error('Error getting data for organization, job family and job functions.');
        }
      })
      .catch(() => {
        return response;
      });
  });
}

export const fetchJobVacancyData = (
  requestBody: JobVacancyRequestBody, 
  contextDispatch: React.Dispatch<Action>,
  sourcingFilterData?: SourcingFilterData,
  dispatch?: React.Dispatch<Action>
) => {
  contextDispatch({ type: MAIN_PAGE_ACTION_TYPES.FETCH_START });
  if (dispatch) {
    // Initial load - fetch both filter data and vacancies
    const listOfOpportunitiesDetails = sourcingFilterData ? sourcingFilterData : fetchOpportunitiesDataAndDetails(requestBody.language);
    const jobVacanciesList = searchJobVacancies(requestBody);
    
    Promise.allSettled([listOfOpportunitiesDetails, jobVacanciesList]).then(async (results: any[]) => {
      const [opportunityDetails, jobVacancies] = results;
      const filterData = sourcingFilterData ? sourcingFilterData : opportunityDetails.value;
      //@ts-ignore
      dispatch(saveSourcingFiltersData(opportunityDetails.value));
      dispatchJobVacancyData(contextDispatch, jobVacancies.value, filterData);
    });
  } else {
    searchJobVacancies(requestBody).then((jobVacanciesList: JobVacancyData) => {
      dispatchJobVacancyData(contextDispatch, jobVacanciesList, sourcingFilterData!);
    });
  }
}

const dispatchJobVacancyData = (contextDispatch: React.Dispatch<Action>, jobVacancies: JobVacancyData, filterData: SourcingFilterData) => {
  try {
    const vacanciesData = insertOrganizationJobFamilyFunctionData(jobVacancies.values, filterData.jobFamily, filterData.organizations);
    contextDispatch({
      type: MAIN_PAGE_ACTION_TYPES.FETCH_SUCCESS,
      jobVacancies: vacanciesData,
      totalPages: jobVacancies.totalPages,
      totalElements: jobVacancies.totalElements,
    })
  } catch (error) {
    contextDispatch({
      type: MAIN_PAGE_ACTION_TYPES.FETCH_ERROR,
      error: SOMETHING_WENT_WRONG
    });
  }
}
export const mergeJobFamilyFunctionAndOrgWithResponse = async (vacancies: JobVacancy[], language: string, sourcingFilterData: SourcingFilterData) => {

  if (sourcingFilterData) {
    return insertOrganizationJobFamilyFunctionData(vacancies, sourcingFilterData.jobFamily, sourcingFilterData.organizations);
  }

  // Extracting all job family id from vacancy list
  const jobFamilyIds = vacancies.filter((jobVacancy: JobVacancy) => jobVacancy.jobFamilyId)
  .map((jobVacancy: JobVacancy) => jobVacancy.jobFamilyId);

  // Extracting all organizations from job vacancy list
  const organizationIds = vacancies.flatMap((jobVacancy: JobVacancy) => 
    jobVacancy.organizations.length ? jobVacancy.organizations.map((org: Organization) => org.internalId) : []
  );

  if (!jobFamilyIds.length && !organizationIds.length) {
    return vacancies;
  }

  const jobInfoPromise = jobFamilyIds.length ? generateJobFamilyAndFunctionName(jobFamilyIds) : Promise.resolve([]);
  const orgDataPromise = organizationIds.length ? getOrganizationDetails(organizationIds, language) : Promise.resolve([]);
  const [jobInfo, orgData] = await Promise.all([jobInfoPromise, orgDataPromise]);
  return insertOrganizationJobFamilyFunctionData(vacancies, jobInfo, orgData);
}

const insertOrganizationJobFamilyFunctionData = (vacancies: JobVacancy[], jobFamilyData: any, OrgData: any) =>
  vacancies.map((vacancy: JobVacancy) => {
    const jobDetails = jobFamilyData.find((data: any) => data.jobFamilyId === vacancy.jobFamilyId) || {};
    const orgs = vacancy.organizations.map((org: Organization) => {
      const orgInfo = OrgData.find((data: any) => data.id === org.internalId)?.title || '';
      return { ...org, title: orgInfo };
    }).filter((org: Organization) => org.title);
    return { ...vacancy, ...jobDetails, organizations: orgs || [] };
  });

export const generateJobFamilyAndFunctionName = async (jobFamilyIds: string[]) => {
  const jobFamilyData = await getFamliyIds({ ids: jobFamilyIds });
  const { data } = jobFamilyData;

  if (data) {
    // Extracting all job functions from the job family response
    const jobFunctionsIds = data.filter((family: any) => family.functionId).map((family: any) => family.functionId);
    if (!jobFunctionsIds.length) {
      return data.map((jobFamily: any) => ({ jobFamilyId: jobFamily.id, jobFamilyName: jobFamily.title, jobFunctionName: '' }));
    }
    const jobFunctionData = await getJobFunctionByIds({ ids: jobFunctionsIds, status: ["ACTIVE"] });
    return data.map((jobFamily: any) => {
      const jobFunction = jobFunctionData?.data.find((jobFunction: any) => jobFamily.functionId === jobFunction.id);
      return {
        jobFamilyId: jobFamily.id,
        jobFamilyName: jobFamily.title,
        jobFunctionId: jobFunction ? jobFunction.id : '',
        jobFunctionName: jobFunction ? jobFunction.title : ''
      };
    });
  } else {
    return [];
  }
}

export const getOrganizationDetails = async (organizationIds: string[], language: string) => {
  const orgDetailsPayload = {
    filterInActive: true,
    ids: organizationIds
  };
  const orgData = await getOrgUnitsById(orgDetailsPayload, language);
  return orgData?.divisions || [];
};
