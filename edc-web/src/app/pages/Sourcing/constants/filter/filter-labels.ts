import { translatr, ompLov } from 'centralized-design-system/src/Translatr';

const FILTERS = {
    SKILLS: {
        id: 'skills',
        label: translatr('web.common.main', 'Skills')
    },
    SCHEDULE: {
        id: 'schedule',
        label: translatr('web.sourcing.candidate-profile', 'Schedule'),
    },
    JOB_TYPE: {
        id: 'job_type',
        label: translatr('web.sourcing.candidate-profile', 'JobType')
    },
    WORKPLACE_MODEL: {
        id: 'workplace_model',
        label: translatr('web.sourcing.candidate-profile', 'WorkplaceModel')
    },
    LOCATION: {
        id: 'location',
        label: translatr('web.sourcing.candidate-profile', 'Location')
    },
    CAREER_TRACK: {
        id: 'job_role_type',
        label: ompLov('job_role_type')
    },
    LEVEL: {
        id: 'level',
        label: translatr('web.sourcing.candidate-profile', 'Level')
    },
    JOB_ROLE: {
        id: 'job_role',
        label: translatr('web.sourcing.candidate-profile', 'Linked', { labelName: ompLov('tm_job_roles') })
    },
    OPEN_TO_OFFERS: {
        id: 'open_to_offers',
        label: ''   // Taking label from config
    },
    COUNTRY: {
        id: 'country',
        label: translatr('web.common.main', 'Country')
    },
    JOB_START_DATE: {
        id: 'job_start_date',
        label: translatr('web.sourcing.main', 'PostingDate')
    },
    JOB_END_DATE: {
        id: 'job_end_date',
        label: translatr('web.sourcing.main', 'ClosingDate')
    },
    JOB_FAMILY: {
        id: 'tm_job_family',
        label: '' // Taking label from config
    }, 
    JOB_FUNCTION: {
        id: 'job_function',
        label: translatr('web.sourcing.main', 'JobFunction')
    }
};

const SUB_FILTERS = {
    PREFERRED: {
        label: translatr('web.sourcing.candidate-profile', 'Preferred'),
        id: 'PREFERRED'
    },
    CURRENT: {
        label: translatr('web.sourcing.candidate-profile', 'Current'),
        id: 'CURRENT'
    },
    ASPIRATIONAL: {
        label: translatr('web.talentmarketplace.main', 'Aspirational'),
        id: 'ASPIRATIONAL'
    }
}

const SAVED_FILTERS = {
    SELECT_FILTER: {
        id: '',
        value: translatr('web.sourcing.candidate-profile', 'SelectFilter')
    }
}

export {
    FILTERS,
    SUB_FILTERS,
    SAVED_FILTERS
}