import { RefObject, useEffect } from 'react';


const useClickOutside = (ref: RefObject<HTMLDivElement>, showMenuDetails: Function, handleMenuClose: Function, addButtonRef: RefObject<HTMLDivElement>, onCancel: Function) => {
  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (
        !showMenuDetails() &&
        ref.current &&
        !ref.current.contains(event.target) &&
        (!addButtonRef || (addButtonRef.current && !addButtonRef.current.contains(event.target)))
      ) {
        handleMenuClose();
        onCancel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, showMenuDetails, handleMenuClose]);
};

export default useClickOutside;
