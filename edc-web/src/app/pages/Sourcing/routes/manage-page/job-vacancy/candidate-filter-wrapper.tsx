import React, { useState } from 'react';
// @ts-ignore
import _ from 'lodash';
import CandidatesFilter from '../../../components/CandidatesFilter/candidates-filter';
import { JobVacancyContext } from '../../../context/manage-page/job-vacancy-context';
import { getOrganizations } from 'edc-web-sdk/requests/organizations';
import { useSelector } from 'react-redux';
import { selectAvailableLocations, selectCurrentLanguage, selectLocationEnabled, selectLocationsAssociation, selectOrgLabel } from '../../../selectors/manage-page/selectors';
import { getOmpRoles } from 'edc-web-sdk/requests/skills';
import { searchJobRolesByIds } from 'edc-web-sdk/requests/tmSearchSvc';
import { generatePayload, retrievePayloadSectionKey } from './utils/people-component/people-component';
import { getCandidatesListByVacancyBasedOnFilters } from 'edc-web-sdk/requests/sourcing';
import { getOrgUnitNameForIds } from 'opportunity-marketplace/shared/CandidateListing/utils/org_units';
import { useJobVacancyActions } from '../../../actions/manage-job-vacancy/manage-job-vacancy';
import { CandidateData } from '../../../types/manage-page/api/api.types';
import { setUpFilters } from './utils/filter/setup-filter';
import { translatr } from 'centralized-design-system/src/Translatr';

const CandidateFilterWrapper = ({ setTabCount }: any) => {

    const [candidateFilterData, setCandidateFilterData] = React.useState([]);
    const { state } = React.useContext(JobVacancyContext) || {};
    const { payload, jobVacancyData, orgUnits, activeTabIndex } = state;
    const language = useSelector(selectCurrentLanguage);
    const locationsEnabled = useSelector(selectLocationEnabled);
    const locationsAssociation = useSelector(selectLocationsAssociation);
    const labels = useSelector(selectOrgLabel);
    const availableLocations = useSelector(selectAvailableLocations);

    const [, setLocalLoadingCount] = useState(0);

    const {
        setCandidateListLoadingState,
        setJobVacancyPayload,
        setIsCandidateFilterApplied,
        setCurrentActiveTabCandidates,
        setOrgUnits
      } = useJobVacancyActions();

  const getFilterData = async (setFilterList: (filterList: object) => void) => {
    if (candidateFilterData.length) {
      setFilterList(candidateFilterData);
      return;
    }
    const { linkedRoleStatus } = jobVacancyData;

    const allOrgTypes = _.union(
      orgUnits.orgUnitsMap.orgUserAssociation,
      orgUnits.orgUnitsMap.orgCareerPrefAssociation
    );
    const orgPromises = allOrgTypes.map(async (org: string) => {
      const orgResponse = await getOrganizations(
        { orgType: org, pageNumber: 1, pageSize: 150, context: '' },
        language
      );
      return { [org]: orgResponse.divisions };
    });

    const [jobRole, vacancyLinkedRoles, ...orgResponse] = await Promise.all([
      getOmpRoles({ language, pageSize: 10 }),
      linkedRoleStatus && linkedRoleStatus.length
        ? searchJobRolesByIds({
            language,
            ids: linkedRoleStatus.map(role => role.internalId)
          })
        : {},
      ...orgPromises
    ]);

    const locationConfig = {
      enable: locationsEnabled,
      association: locationsAssociation
    };

    const orgDivisions = orgResponse.reduce((acc: {}, curr) => ({ ...acc, ...(curr as Record<string, unknown>) }), {});

    const filterList = await setUpFilters(
      jobVacancyData,
      locationConfig,
      vacancyLinkedRoles,
      orgDivisions,
      language,
      availableLocations,
      state,
      labels
    );
    setCandidateFilterData(_.cloneDeep(filterList));
    setFilterList(filterList);
  };

const handleSubmitFilter = async (filters: any) => {
  await setCandidateListLoadingState(true);
  const jobVacancyPayload = generatePayload(payload, activeTabIndex, filters);
  setLocalLoadingCount(prevCount => prevCount + 1);

  let candidateResult: CandidateData;
  let orgData = {};
  const currentTab = retrievePayloadSectionKey(activeTabIndex);
  try {
    candidateResult = await getCandidatesListByVacancyBasedOnFilters(jobVacancyPayload);
    orgData = await getOrgUnitNameForIds(
      candidateResult,
      orgUnits.columnLabel,
      language,
      orgUnits.columnType
    );
  } catch (e) {
    setTabCount(currentTab, 0);
    setCurrentActiveTabCandidates({});
  } finally {
      setLocalLoadingCount(prevCount => {
          const newCount = prevCount - 1;
          if (newCount === 0) {
              setOrgUnits({...orgUnits, orgDivisionsMap: orgData });
              setCurrentActiveTabCandidates(candidateResult);
              setTabCount(currentTab, candidateResult.totalElements);
              setCandidateListLoadingState(false);
              const hasActiveFilters = filters.filters && Object.keys(filters.filters).length > 0
                && Object.values(filters.filters).some((filter: any) => filter.length);
              setIsCandidateFilterApplied(hasActiveFilters);
          }
      return newCount;
    });
  }
    setJobVacancyPayload(jobVacancyPayload);
  };
  return (
    <CandidatesFilter
      key={`filter-tab-${activeTabIndex}`}
      getFilterData={getFilterData}
      onChange={handleSubmitFilter}
      savedSearchFilterType={'sourcing-candidate-filter'}
      masterSwitch={{
        label: translatr('web.sourcing.candidate-profile', 'IncludeProfilesWithCriteriaNotSpecified'),
        value: false,
        requestBodyKey: 'showPartialMatchEnabled'
      }}
    />
  );
};

export default CandidateFilterWrapper;