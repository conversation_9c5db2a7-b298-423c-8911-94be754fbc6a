import { SEARCH_TYPE } from '@pages/Sourcing/components/CandidatesFilter/helpers';
import { configListOfValues } from 'opportunity-marketplace/util';
import { isPreferencesEnabled } from 'opportunity-marketplace/shared/CandidateListing/utils/extract_label';
import { LOCATION_ASSOCIATION } from 'opportunity-marketplace/helpers';
import { SUB_FILTERS } from '../../../../../constants/filter/filter-labels';
import { ACTIVE } from '../../../../../constants/filter/general-constants';
import { FILTER_TYPE } from '@pages/Sourcing/types/manage-page/candidate-filter/filter.types';

const sortListByAscendingOrder = (list, key = 'label') =>
  list.sort((value1, value2) => value1[key].localeCompare(value2[key]));

const createFilterObject = (
  filterId,
  filterName,
  smallLabel,
  filterItems,
  shouldSort = true,
  searchType = SEARCH_TYPE.NONE,
  filterType = FILTER_TYPE.LIST,
  filterGroup
) => {
  return {
    filterId,
    filterName,
    smallLabel,
    searchType,
    filterGroup,
    filterType,
    filterItems: shouldSort ? sortListByAscendingOrder(filterItems) : filterItems
  };
};

const createFilterItem = (label, id, isSelected = false, subFilters = {}, additionalInfo = {}) => ({
  label,
  id,
  isSelected,
  subFilters,
  additionalInfo
});

const createCurrentSubFilter = () => ({
  id: SUB_FILTERS.CURRENT.id,
  label: SUB_FILTERS.CURRENT.label,
  value: SUB_FILTERS.CURRENT.id,
  isSelected: false
});

const createPreferredSubFilter = () => ({
  id: SUB_FILTERS.PREFERRED.id,
  label: SUB_FILTERS.PREFERRED.label,
  value: SUB_FILTERS.PREFERRED.id,
  isSelected: false
});

const createAspirationalSubFilter = () => ({
  id: SUB_FILTERS.ASPIRATIONAL.id,
  label: SUB_FILTERS.ASPIRATIONAL.label,
  value: SUB_FILTERS.ASPIRATIONAL.id,
  isSelected: false
});

const createSubFilterObject = (items = [], options = []) => ({ items, options });

const getConfigData = configType => configListOfValues.find(config => config.key === configType);

const extractConfigData = configData => ({
  configData,
  values: configData.values.filter(({ enable }) => enable)
});

const getConfigDataAndValues = configType => {
  const configData = getConfigData(configType);
  if (!configData || !isPreferencesEnabled(configData)) return null;
  const enabledValues = configData.values?.filter(({ enable }) => enable);
  return enabledValues?.length ? { configData, values: enabledValues } : null;
};

const checkLocFilterConfig = (enable, association) =>
  enable &&
  (association.includes(LOCATION_ASSOCIATION.USER) ||
    association.includes(LOCATION_ASSOCIATION.CAREER_PREFERENCE));

const getLocationOptionsFromConfig = association => {
  const locationOptions = [];
  const currentValue = createCurrentSubFilter();
  const preferredValue = createPreferredSubFilter();
  const { USER, CAREER_PREFERENCE } = LOCATION_ASSOCIATION;
  if (association.includes(USER) && !association.includes(CAREER_PREFERENCE)) {
    locationOptions.push(currentValue);
  } else if (!association.includes(USER) && association.includes(CAREER_PREFERENCE)) {
    locationOptions.push(preferredValue);
  } else {
    locationOptions.push(currentValue);
    locationOptions.push(preferredValue);
  }
  return locationOptions;
};

const formatJobRoleResponseForFilter = jobRole =>
  jobRole
    .filter(({ status }) => status === ACTIVE)
    .map(item => ({
      ...item,
      id: item.id,
      label: item.label,
      isSelected: false,
      subFilters: {
        items: [createCurrentSubFilter(), createAspirationalSubFilter()],
        options: []
      }
    }));

const getLevelOptions = levelLov => {
  const items = [createCurrentSubFilter()];
  if (isPreferencesEnabled(levelLov)) {
    items.push(createPreferredSubFilter());
  }
  return items;
};

const removeDuplicatesJobRole = array => {
  const uniqueJobRoleIds = new Set();
  return array.filter(item => {
    if (!uniqueJobRoleIds.has(item.id)) {
      uniqueJobRoleIds.add(item.id);
      return true;
    }
    return false;
  });
};

export {
  createFilterObject,
  sortListByAscendingOrder,
  createFilterItem,
  createSubFilterObject,
  createCurrentSubFilter,
  createPreferredSubFilter,
  createAspirationalSubFilter,
  getConfigDataAndValues,
  checkLocFilterConfig,
  getLocationOptionsFromConfig,
  formatJobRoleResponseForFilter,
  getLevelOptions,
  getConfigData,
  extractConfigData,
  removeDuplicatesJobRole
};
