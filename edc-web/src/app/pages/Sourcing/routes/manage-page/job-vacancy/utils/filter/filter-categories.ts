import {
    checkLocFilterConfig,
    createFilterObject,
    createPreferredSubFilter,
    formatJobRoleResponseForFilter,
    getConfigDataAndValues,
    getLocationOptionsFromConfig,
    removeDuplicatesJobRole,
    sortListByAscendingOrder
} from "./common";
import { ConfigValue, LocationConfig, Location, Item } from "../../../../../types/manage-page/api/common.types";
import { transformSkillFilterItems } from "./filter-transformations";
import { SEARCH_TYPE } from "../../../../../components/CandidatesFilter/helpers";
import { FILTERS, SUB_FILTERS } from "../../../../../constants/filter/filter-labels";
import { getLabel } from "opportunity-marketplace/shared/CandidateListing/utils/extract_label";
import { FILTER_TYPE, FilterItem } from "../../../../../types/manage-page/candidate-filter/filter.types";
import { ompLov, translatr } from "centralized-design-system/src/Translatr";
import { LinkedRoleStatusDetail } from "../../../../../types/manage-page/api/api.types";
import { configListOfLabels } from "opportunity-marketplace/util";

const skillFilter = (filterItems: FilterItem[], interestsLabel: string) => {
    const filterData = filterItems.length
    ? sortListByAscendingOrder(filterItems).map((filterItem: FilterItem) =>
        transformSkillFilterItems(filterItem, false, interestsLabel)
      )
    : [];

    return createFilterObject(
      FILTERS.SKILLS.id,    
      FILTERS.SKILLS.label,
      '',
      filterData,
      false,
      SEARCH_TYPE.EXTERNAL_SKILLS,
      FILTER_TYPE.LIST_WITH_ACCORDION
    );
  };

const tmStandardFieldFilter = (language: string, configKey: string, label: string) => {
    const config = getConfigDataAndValues(configKey);
    if (!config) {
        return {};
    }

    const filterItems: FilterItem[] = config.values
        .map((configInfo: ConfigValue) => ({
            ...configInfo,
            id: configInfo.key,
            label: getLabel(configInfo, configInfo.defaultLabel, language),
            isSelected: false,
            subFilters: {
                items: [createPreferredSubFilter()],
                options: [] as Item[]
            }
        }));
    return filterItems.length
        ? createFilterObject(
            configKey,
            getLabel(config.configData, label, language),
            SUB_FILTERS.PREFERRED.label,
            filterItems,
            true,
            SEARCH_TYPE.NONE,
            FILTER_TYPE.LIST
        )
        : {};
  };

const openToOfferFilter = (language: string, isMatchingTab: boolean) => {
    const config = getConfigDataAndValues(FILTERS.OPEN_TO_OFFERS.id);
    if (!config) {
        return {};
    }

    const openToJobOffersAPIRequestKeys = ['OPEN_TO_VACANCIES', 'CLOSED_TO_VACANCIES'];
    const filterItems = config.values
        .filter((configInfo: ConfigValue) => openToJobOffersAPIRequestKeys.includes(configInfo.key))
        .map((configInfo: ConfigValue) => ({
            id: configInfo.key,
            label: getLabel(configInfo, configInfo.defaultLabel, language),
            isSelected: false,
            subFilters: {
                items: [createPreferredSubFilter()],
                options: [] as Item[]
            },
            additionalInfo: configInfo.key === 'CLOSED_TO_VACANCIES' && isMatchingTab ? {
                isDisabled: true,
                disabledMessage: translatr('web.sourcing.candidate-profile', 'ValueNotAvailableFromTab', {
                    tabName: translatr('web.sourcing.candidate-profile', 'Matching')
                })
            } : {}
        }));

    filterItems.push({
        id: 'NOT_SPECIFIED_OPEN_TO_VACANCIES',
        label: translatr('web.common.main', 'NotSpecified'),
        isSelected: false,
        subFilters: {
            items: [createPreferredSubFilter()],
            options: [] as Item[]
        }
    });

    return createFilterObject(
        FILTERS.OPEN_TO_OFFERS.id,
        getLabel(config.configData, config.configData.defaultLabel, language),
        SUB_FILTERS.PREFERRED.label,
        filterItems,
        false
    );
};

const jobRoleFilter = (vacancyLinkedRoles: LinkedRoleStatusDetail, jobRole: LinkedRoleStatusDetail, language: string) => {
    const combinedTopics = [...(vacancyLinkedRoles.topics ?? []), ...(jobRole.topics ?? [])];
    const filterItems = formatJobRoleResponseForFilter(removeDuplicatesJobRole(combinedTopics));
    const jobRoleConfig = configListOfLabels.find((label: any) => label.key === FILTERS.JOB_ROLE.id);
    if (!jobRoleConfig) {
        return {};
    }
    return createFilterObject(
        FILTERS.JOB_ROLE.id,
        getLabel(jobRoleConfig, jobRoleConfig.defaultLabel, language),
        '',
        filterItems,
        false,
        SEARCH_TYPE.EXTERNAL_JOB_ROLE,
        FILTER_TYPE.LIST_WITH_ACCORDION
    );
};

const locationFilter = (locations: Location[], locationConfig: LocationConfig) => {
    const { enable, association } = locationConfig;
    if (!checkLocFilterConfig(enable, association)) {
        return {};
    }
    try {
        const filterItems = locations.map((location: Location) => ({
            id: location.id,
            label: location.location_name,
            isSelected: false,
            subFilters: {
                items: getLocationOptionsFromConfig(association),
                options: [] as Item[]
            }
        }));
        return createFilterObject(
            FILTERS.LOCATION.id,
            FILTERS.LOCATION.label,
            '',
            filterItems,
            false,
            SEARCH_TYPE.INTERNAL,
            FILTER_TYPE.LIST_WITH_ACCORDION
        );
    } catch (e) {
        return {};
    }
};

export {
    skillFilter,
    tmStandardFieldFilter,
    openToOfferFilter,
    jobRoleFilter,
    locationFilter
}
