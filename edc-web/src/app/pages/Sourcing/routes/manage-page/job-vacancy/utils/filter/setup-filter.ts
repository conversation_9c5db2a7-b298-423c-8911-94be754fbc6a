import { translateInterestsLabel } from "@components/modals/SkillsModal/helpers";
import { getOmpRoles } from 'edc-web-sdk/requests/skills';
import { setupLevelFilter } from './levels';
import { setupOrgFilter } from "./org_units";
import { FILTERS } from "../../../../../constants/filter/filter-labels";
import { jobRoleFilter, locationFilter, openToOfferFilter, skillFilter, tmStandardFieldFilter } from "./filter-categories";
import { LinkedRoleStatusDetail } from "../../../../../types/manage-page/api/api.types";

export const setUpFilters = async (
  jobVacancyData: any,
  locationConfig: { enable: boolean; association: string },
  vacancyLinkedRoles: LinkedRoleStatusDetail,
  orgDivisions: any,
  language: any,
  availableLocations: any,
  state: any,
  labels: object
) => {
    const { orgUnits } = state;
    const interestsLabel = translateInterestsLabel(labels, language);
    const jobRole = await getOmpRoles({ language, pageSize: 10 });
    const orgFilter = setupOrgFilter(orgUnits.orgUnitsMap, orgDivisions);
    const isMatchingTab = state?.activeTabIndex === 0;

    const filters = [
        skillFilter(jobVacancyData.capabilities, interestsLabel),
        tmStandardFieldFilter(language, FILTERS.SCHEDULE.id, FILTERS.SCHEDULE.label),
        tmStandardFieldFilter(language, FILTERS.JOB_TYPE.id, FILTERS.JOB_TYPE.label),
        tmStandardFieldFilter(language, FILTERS.WORKPLACE_MODEL.id, FILTERS.WORKPLACE_MODEL.label),
        tmStandardFieldFilter(language, FILTERS.CAREER_TRACK.id, FILTERS.CAREER_TRACK.label),
        openToOfferFilter(language, isMatchingTab),
        setupLevelFilter(language),
        jobRoleFilter(vacancyLinkedRoles, jobRole, language),
        locationFilter(availableLocations, locationConfig),
        ...orgFilter
    ];
    return filters.filter(obj => Object.keys(obj).length !== 0)
      .sort((currentFilter, nextFilter) => currentFilter.filterName.localeCompare(nextFilter.filterName));
};
