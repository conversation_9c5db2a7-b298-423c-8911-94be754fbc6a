import { getConfigurations } from 'edc-web-sdk/requests/hrData.v2';
import { extractOrgUnitsMap, } from 'opportunity-marketplace/shared/CandidateListing/utils/org_units';
import { CandidateData, CandidateFiltersResponsePayload, CandidatesPayload, JobVacancy, OrgUnits } from 'src/app/pages/Sourcing/types/manage-page/api/api.types';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { CANDIDATE_FILTER_SECTION_KEY } from '../../../../../types/manage-page/api/common.types';

export const fetchOrgConfig = async () => {
    const response = await getConfigurations({ groupId: 'hr-org-config' });
    return response.data;
};

export const processOrgUnitsMap = (configData: any, orgTypes: any, orgConfig: any) => {
    const orgUnitsMap = extractOrgUnitsMap(configData, orgTypes);
    const columnType = orgConfig['sourcing_table'];
    const columnLabel = columnType ? orgTypes[columnType].label : '';
    return { orgUnitsMap, columnType, columnLabel };
};

export const isCandidatesListEmpty = (candidateFilterApplied: boolean, matchingTabCount: number, shortlistTabCount: number,
    allProfilesTabCount: number, currentActiveTabCandidates: CandidateData, isCandidateListLoading: boolean) => {
    const noFilterApplied = !candidateFilterApplied;
    const allTabsEmpty =
        matchingTabCount === 0 && shortlistTabCount === 0 && allProfilesTabCount === 0;
    const noCandidateData = !currentActiveTabCandidates.values || Object.keys(currentActiveTabCandidates.values).length === 0;
    return !isCandidateListLoading && noFilterApplied && allTabsEmpty && noCandidateData;
};

export const getTabLabel = (index: number, tab: string, matchingTabCount: number, shortlistTabCount: number, allProfilesTabCount: number) => {
  const profileCountsByCategory = [matchingTabCount, shortlistTabCount, allProfilesTabCount];
  return `${tab} (${profileCountsByCategory[index]})`;
};

export const retrievePayloadSectionKey = (index: number) => {
    const requestPayloadValues = [CANDIDATE_FILTER_SECTION_KEY.MATCHING_PROFILES,
        CANDIDATE_FILTER_SECTION_KEY.SHORTLISTED_PROFILES, CANDIDATE_FILTER_SECTION_KEY.ALL_PROFILES];
    return requestPayloadValues[index];
};

export const getTabIndexByLabel = (label: string, peopleTabs: any[]) =>
    peopleTabs.findIndex(tabLabel => tabLabel === label.replace(/\s*\(\d+\)\s*/g, '').trim());

export const generatePayload = (payload: object, index: number, filterData: CandidateFiltersResponsePayload = { filters: {} }): CandidatesPayload => {
    const profilesFilterSection = retrievePayloadSectionKey(index);
    const hasFilters = filterData?.filters && Object.keys(filterData.filters).length > 0;
    
    // sort order based on profile section and filter settings
    const isAllProfilesSection = profilesFilterSection === CANDIDATE_FILTER_SECTION_KEY.ALL_PROFILES;
    const sortOrder = isAllProfilesSection ? (filterData?.showPartialMatchEnabled ? 'DESC' : 'BY_LAST_NAME') : 'DESC';

    return {
        ...payload,
        profilesFilterSection,
        filters: hasFilters ? filterData.filters : {},
        showPartialMatchEnabled: hasFilters ? (filterData.showPartialMatchEnabled || false) : false,
        sortOrder
    };
};

export const createCandidateListingProps = (jobVacancyData: JobVacancy, currentActiveTabCandidates: object, slug: string,
    orgUnits: OrgUnits, language: string, activeTabIndex: number) => ({
    requiredSkills: jobVacancyData.capabilities,
    apiData: currentActiveTabCandidates,
    opportunityId: slug,
    showSkillsMatrix: true,
    opportunityType: JOB_TYPE.VACANCY,
    showShortlist: true,
    fetchDataOnPagination: true,
    orgDivisionsMap: orgUnits.orgDivisionsMap,
    orgUnitsMap: orgUnits.orgUnitsMap,
    orgUnitColumnLabel: orgUnits.columnLabel,
    language,
    showMatchingScore: activeTabIndex !== 2
});