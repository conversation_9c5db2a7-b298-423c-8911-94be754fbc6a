import { generateJobFamilyAndFunctionName, getOrganizationDetails } from "@pages/Sourcing/actions/main-page/main-page-actions";
import { SEARCH_TYPE } from "@pages/Sourcing/components/CandidatesFilter/helpers";
import { FILTERS } from "@pages/Sourcing/constants/filter/filter-labels";
import { ORG_UNIT } from "@pages/Sourcing/constants/filter/general-constants";
import { createFilterItem, createFilterObject } from "@pages/Sourcing/routes/manage-page/job-vacancy/utils/filter/common";
import { Country, Division, NumberObject } from "@pages/Sourcing/types/main-page/api/api.types";
import { JobFamilyFilter, Topics } from "@pages/Sourcing/types/manage-page/api/api.types";
import { CandidateFilterItem, FILTER_TYPE, FilterItem } from "@pages/Sourcing/types/manage-page/candidate-filter/filter.types";
import { searchJobRolesByIds } from 'edc-web-sdk/requests/tmSearchSvc';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import { getJobFamilyFilterItems, getJobFunctionFilterItems } from "./common";
import { Location } from "@pages/Sourcing/types/manage-page/api/common.types";

export const createOrganizationFilters = (organizations: Division[], orgTypes: Record<string, any>) => {
    const groupedData = organizations.reduce((acc: { [key: string]: Division[] }, item: Division) => {
        const { orgType } = item;
        if (!acc[orgType]) {
            acc[orgType] = [];
        }
        acc[orgType].push(item);
        return acc;
    }, {});

    const orgTypesFilter: CandidateFilterItem[] = [];
    Object.entries(groupedData).forEach(([orgType, orgDetails]: [string, Division[]]) => {
        const filterItems: FilterItem[] = [];
        orgDetails.forEach((orgData: Division) => filterItems.push(createFilterItem(orgData.title, orgData.id)));
        const orgTypeTitle = orgTypes[orgType]?.label;
        if (orgTypeTitle && filterItems.length) {
            orgTypesFilter.push(createFilterObject(orgType, orgTypeTitle, '', filterItems, true, SEARCH_TYPE.INTERNAL, FILTER_TYPE.LIST, ORG_UNIT));
        }
    });
    return orgTypesFilter;
}

export const createCountryFilters = (allCountries: Country[], countryCodes: Record<string, number>): CandidateFilterItem | {} => {
    const notSpecifiedCountryItem = createFilterItem(translatr('web.common.main', 'NotSpecified'), 'undefined');
    
    const sortedCountryItems = Object.keys(countryCodes).map((countryCode: string) => {
        const countryDetails = allCountries.find((country: Country) => country.id.toLowerCase() === countryCode);
        if (countryDetails) return createFilterItem(countryDetails.label, countryDetails.id);
        else return null;
    }).filter(country => country).sort((c1, c2) => c1.label.localeCompare(c2.label));

    const filterItems = [notSpecifiedCountryItem, ...sortedCountryItems];
    
    if (filterItems.length > 1) {
        const { id, label } = FILTERS.COUNTRY;
        return createFilterObject(id, label, '', filterItems, false, SEARCH_TYPE.INTERNAL);
    } else {
        return {};
    }
}

export const createDateFilter = (
    type: 'start' | 'end',
    filterId: string,
    label: string
) => {
    const isStartDate = type === 'start';
    const filterItems: FilterItem[] = [
        {
            id: `job-${type}-date`,
            label: `job-${type}-date`,
            isSelected: false,
            dateRange: {
                fromDate: undefined,
                toDate: undefined,
                [isStartDate ? 'maxDate' : 'minDate']: new Date(),
                [isStartDate ? 'minDate' : 'maxDate']: {}
            }
        }
    ];

    return createFilterObject(
        filterId,
        label,
        '',
        filterItems,
        true,
        '',
        FILTER_TYPE.DATE_PICKER
    );
};

export const createLinkedRolesFilters = async (linkedRoleStatus: string[], language: string) => {
    const jobRoles = await searchJobRolesByIds({
        language,
        ids: linkedRoleStatus
      });
    const filterItems = jobRoles.topics.map((jobRole: Topics) => {
        const { id, label } = jobRole;
        return createFilterItem(label, id);
    });
    if (!filterItems.length) {
        return {};
    }
    return createFilterObject(
        FILTERS.JOB_ROLE.id,
        FILTERS.JOB_ROLE.label,
        '',
        filterItems,
        true,
        SEARCH_TYPE.INTERNAL,
        FILTER_TYPE.LIST
    );
}

export const createJobFamilyAndFunctionFilters = (jobFamily: JobFamilyFilter[]) => {
    if (jobFamily) {
        const jobFamilyFilterItems = getJobFamilyFilterItems(jobFamily);
        const jobFunctionFilterItems = getJobFunctionFilterItems(jobFamily);

        const filters = [];

        if (jobFamilyFilterItems.length) {
            filters.push(createFilterObject(
                FILTERS.JOB_FAMILY.id,
                omp(FILTERS.JOB_FAMILY.id),
                '',
                jobFamilyFilterItems,
                true,
                SEARCH_TYPE.INTERNAL,
                FILTER_TYPE.LIST
            ));
        }

        if (jobFunctionFilterItems.length) {
            filters.push(createFilterObject(
                FILTERS.JOB_FUNCTION.id,
                FILTERS.JOB_FUNCTION.label,
                '',
                jobFunctionFilterItems,
                true,
                SEARCH_TYPE.INTERNAL,
                FILTER_TYPE.LIST
            ));
        }

        return filters;
    }
    return [];
}

export const createLocationFilter = (locations: NumberObject, availableLocations: Location[]) => {
    const presentLocations: string[] = Object.keys(locations) ?? [];
    const filterItems = presentLocations.reduce<FilterItem[]>((filterItem, location) => {
        const hasLocationData = availableLocations.find((loc: Location) => loc.id === location);
        if (hasLocationData) {
            filterItem.push(createFilterItem(hasLocationData.location_name, hasLocationData.id));
        }
        return filterItem;
    }, []);
    
    return createFilterObject(
        FILTERS.LOCATION.id,
        FILTERS.LOCATION.label,
        '',
        filterItems,
        true,
        SEARCH_TYPE.INTERNAL,
        FILTER_TYPE.LIST
    );
};