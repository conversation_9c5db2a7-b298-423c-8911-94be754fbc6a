import { search } from 'edc-web-sdk/requests/extOpportunities';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { FILTERS } from '@pages/Sourcing/constants/filter/filter-labels';
import { Country, OpportunityAvailableFilters } from '@pages/Sourcing/types/main-page/api/api.types';

import {
    validateAndCreateOrgFilters,
    validateAndCreateDateRangeFilters,
    validateAndCreateCountryFilter,
    validateAndCreateJobRoleFilters,
    validateAndCreateJobFamilyAndFunctionFilters,
    validateAndCreateLocationFilters
} from './vacancy-filter-validator';
import { getNonZeroEntries } from './common';
import { Location } from '@pages/Sourcing/types/manage-page/api/common.types';

export const getAvailableFiltersData = async (language: string): Promise<any> => {
    const availableVacancyData = await search({
        pageNumber: 1, 
        pageSize: 1, 
        keyword: "", 
        sortType: "MATCH", 
        sortOrder: "DESC", 
        roleId: [], 
        capabilityId: [], 
        location: [], 
        geolocation: {}, 
        jobFamily: [], 
        organizationId: null,
        language: language,
        isDismissedExcluded: false,
        isFilterAggregationEnabled: true,
        type: JOB_TYPE.VACANCY
    });

    const filters = availableVacancyData?.values[0]?.opportunityAvailableFilters;
    
    if (!filters) {
        return {};
    }

    const allFilters = {
        countryCodes: getNonZeroEntries(filters.countryCodes),
        organizations: getNonZeroEntries(filters.organizations),
        jobStartDateRange: getNonZeroEntries(filters.jobStartDateRange),
        jobEndDateRange: getNonZeroEntries(filters.jobEndDateRange),
        linkedRoleStatus: getNonZeroEntries(filters.linkedRoleStatus),
        jobFamily: getNonZeroEntries(filters.jobFamily),
        locations: getNonZeroEntries(filters.locations)
    };
    
    return Object.fromEntries(
        Object.entries(allFilters).filter(([_, value]) => value && Object.keys(value).length > 0)
    );
};

export const setupVacancyFilters = async (
    allCountries: Country[], 
    orgTypes: Record<string, any>, 
    opportunityAvailableFilters: OpportunityAvailableFilters, 
    language: string,
    availableLocations: Array<Location>
): Promise<any> => {
    if (!opportunityAvailableFilters) return [];

    const {
        organizations,
        countryCodes,
        jobStartDateRange,
        jobEndDateRange,
        linkedRoleStatus,
        jobFamily,
        locations
    } = opportunityAvailableFilters;

    const requiredApiCallFilters = await Promise.all([
        validateAndCreateJobRoleFilters(linkedRoleStatus, language)
    ]);

    const nonRequiredApiCallFilters = [
        validateAndCreateOrgFilters(organizations, orgTypes),
        validateAndCreateCountryFilter(allCountries, countryCodes),
        validateAndCreateDateRangeFilters(jobStartDateRange, 'start', FILTERS.JOB_START_DATE.id, FILTERS.JOB_START_DATE.label),
        validateAndCreateDateRangeFilters(jobEndDateRange, 'end', FILTERS.JOB_END_DATE.id, FILTERS.JOB_END_DATE.label),
        validateAndCreateJobFamilyAndFunctionFilters(jobFamily),
        validateAndCreateLocationFilters(locations, availableLocations)
    ];

    const allFilters = [...requiredApiCallFilters, ...nonRequiredApiCallFilters];

    const isNonEmptyFilter = (obj: object) => obj && Object.keys(obj).length !== 0;
    return allFilters
        .flat()
        .filter(isNonEmptyFilter)
        .sort((currentFilter, nextFilter) => currentFilter.filterName.localeCompare(nextFilter.filterName));
};