import { Country, Division, NumberObject } from "@pages/Sourcing/types/main-page/api/api.types";
import { createOrganizationFilters, createDateFilter, createCountryFilters, createLinkedRolesFilters, createJobFamilyAndFunctionFilters, createLocationFilter } from "./vacancy-filter-creator";
import { JobFamilyFilter } from "@pages/Sourcing/types/manage-page/api/api.types";
import { Location } from "@pages/Sourcing/types/manage-page/api/common.types";

export const validateAndCreateOrgFilters = (organizations: Division[], orgTypes: Record<string, any>) => {
    if (organizations && organizations.length) {
        try {
            return createOrganizationFilters(organizations, orgTypes);
        } catch (error) {
            console.error('Error creating organization filters:', error);
        }
    }
    return [];
}

export const validateAndCreateCountryFilter = (allCountries: Country[], countryCodes: Record<string, number>) => {
    if (countryCodes && Object.keys(countryCodes).length) {
        try {
            return createCountryFilters(allCountries, countryCodes)
        } catch (error) {
            console.error('Error creating country filters:', error);
        }
    }
    return {};
}

export const validateAndCreateDateRangeFilters = (jobDateRange: NumberObject, type: 'start' | 'end', id: string, label: string): any => {
    if (jobDateRange && Object.keys(jobDateRange).length) {
        try {
            return createDateFilter(type, id, label);
        } catch (error) {
            console.error('Error creating date filter:', error);
        }
    }
    return {};
}

export const validateAndCreateJobRoleFilters = async (linkedRoleStatus: NumberObject, language: string): Promise<any> => {
    if (linkedRoleStatus && Object.keys(linkedRoleStatus).length) {
        try {
            return await createLinkedRolesFilters(Object.keys(linkedRoleStatus), language);
        } catch (error) {
            console.error('Error creating job role filters:', error);
        }

    }
    return {};
}

export const validateAndCreateJobFamilyAndFunctionFilters = (jobFamily: JobFamilyFilter[]) => {
    if (jobFamily && jobFamily.length) {
        try {
            return createJobFamilyAndFunctionFilters(jobFamily);
        } catch (error) {
            console.error('Error creating job family and function filters:', error);
        }
    }
    return [];
}

export const validateAndCreateLocationFilters = (locations: NumberObject, availableLocations: Location[]) => {
    if (locations && Object.keys(locations).length) {
        try {
            return createLocationFilter(locations, availableLocations);
        } catch (error) {
            console.error('Error creating location filter:', error);
        }
    }
    return {};
}