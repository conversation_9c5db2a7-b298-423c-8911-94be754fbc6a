import React, { useEffect, useRef, useContext, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Tab from 'centralized-design-system/src/TabBar/SimpleTabs';
import JobVacanciesTab from './JobVacanciesTab/JobVacanciesTab.jsx';
import Pagination from 'centralized-design-system/src/Pagination';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import withMainProvider from '../../hoc/main-page/withMainProvider';
import { useSearchParams } from '../../hooks/useSearchParams';
import {
  bookmarkOpportunity,
  unbookmarkOpportunity
} from 'edc-web-sdk/requests/careerOportunities.v2.js';
import { open_v2 as openSnackBar } from '../../../../actions/snackBarActions';
import { selectCurrentLanguage, selectOrgTypes, selectSourcingFilterData } from '../../selectors/manage-page/selectors';
import { MainPageContext } from '../../context/main-page/main-page-context';
import { fetchJobVacancyData } from '../../actions/main-page/main-page-actions';
import {
  SearchParamPayload
} from '../../types/manage-page/hooks/search-params';
import './style.scss';
import { getOrgTypeLovs } from '@actions/organizationsActions.js';
import { getCountries, getAvailableLocations } from '@actions/availableLocationsActions.js';
import CandidateFilterProvider from '@pages/Sourcing/components/CandidatesFilter/candidate-filter-provider';
import { toMap } from '@pages/TalentMarketplace/shared/CandidateListing/utils/org_units/index.js';
import { selectCounties, selectAvailableLocations } from '@pages/Sourcing/selectors/main-page/selectors';

const SourcingContainer = () => {
  const tabs = [translatr('web.sourcing.main', 'JobVacancies')];
  const pageLimit = 5;
  const language = useSelector(selectCurrentLanguage);
  const dispatch = useDispatch();
  const { state, dispatch: contextDispatch } = useContext(MainPageContext);
  const { loading, jobVacancies, totalElements, error } = state;
  const orgTypes = toMap(useSelector(selectOrgTypes));
  const sourcingFilterData = useSelector(selectSourcingFilterData);
  const [searchParams, setSearchParams] = useSearchParams();
  const availableLocations = useSelector(selectAvailableLocations);
  const countries = useSelector(selectCounties);

  const {
    type = 'job',
    sortOrder = 'ASC',
    sortType = 'START_DATE',
    jobStatus = 'OPEN',
    keyword = '',
    page = 1
  } = searchParams;
  const requestBodyRef = useRef({
    pageSize: pageLimit,
    sortType,
    sortOrder,
    jobStatus,
    language,
    type,
    keyword
  });
  const getRequestBody = (pageNumber: number, newParameter = {}) => {
    requestBodyRef.current = {
      ...requestBodyRef.current,
      // @ts-ignore
      pageNumber: parseInt(pageNumber),
      ...newParameter
    };

    return requestBodyRef.current;
  };

  useEffect(() => {
    (async () => {
      try {
        const vacancyDataPromise = fetchJobVacancyData(getRequestBody(page), contextDispatch, sourcingFilterData, dispatch);
        const orgTypeDataPromise = Object.keys(orgTypes).length ? Promise.resolve(orgTypes) 
          //@ts-ignore
          : dispatch(getOrgTypeLovs(language));
        const locationsDataPromise = availableLocations ? Promise.resolve(availableLocations) 
          //@ts-ignore
          : dispatch(getAvailableLocations(language));

        await Promise.all([vacancyDataPromise, orgTypeDataPromise, locationsDataPromise]);

        //@ts-ignore
        if (!countries) dispatch(getCountries(language));
      } catch (error) {
        console.error('Error fetching initial data:', error);
      }
    })();
  }, []);

  const paginate = async (resp: any) => {
    let targetPage = Number(page);

    if (Number.isInteger(resp.event)) {
      targetPage = resp.event;
    } else if (resp.event === 'next') {
      targetPage += 1;
    } else if (resp.event === 'prev') {
      targetPage -= 1;
    }

    await fetchJobVacancyData(getRequestBody(targetPage), contextDispatch, sourcingFilterData);
    setSearchParams({ page: targetPage });
  };

  const handleChange = async (payload: SearchParamPayload) => {
    setSearchParams({ ...payload, page: 1 });
    await fetchJobVacancyData(getRequestBody(1, { ...payload }), contextDispatch, sourcingFilterData);
  };

  const handleBookmark = async (vacancyId: string, bookmarkType: string, isBookmark: boolean) => {
    const message = isBookmark ? 'OpportunityUnbookmarked' : 'OpportunityBookmarked';
    try {
      if (isBookmark) await unbookmarkOpportunity(vacancyId, bookmarkType);
      else await bookmarkOpportunity(vacancyId, bookmarkType);
      dispatch(openSnackBar(
        translatr('web.common.main', message, { opportunity: omp(`tm_job_vacancy`) }),
        'success'
      ));
      await fetchJobVacancyData(getRequestBody(page), contextDispatch, sourcingFilterData);
    } catch (error) {
      dispatch(openSnackBar(translatr('web.common.main', 'SomethingWentWrongPleaseTryAgainLater'), 'error'));
    }
  };

  return (
    <div className='tab-container'>
      <Tab>
        {tabs.map((tab, index) => (
          //@ts-ignore
          <Tab.TabPane key={`tab-${index}`} tab={tab}>
            <>
              {error ? (
                <p className="empty-vacancies-list">{error}</p>
              ) : (
                <>
                  <CandidateFilterProvider>
                    <JobVacanciesTab
                      listOfJobVacancies={jobVacancies}
                      isLoading={loading}
                      sortType={sortType}
                      sortOrder={sortOrder}
                      jobStatus={jobStatus}
                      keyword={keyword}
                      totalElements={totalElements}
                      handleChange={handleChange}
                      handleBookmark={handleBookmark}
                      getRequestBody={getRequestBody}
                      setSearchParam={setSearchParams}
                    />
                  </CandidateFilterProvider>
                  {totalElements > pageLimit && !loading && (
                    <div className="pagination-container">
                      <Pagination
                        postPerPage={pageLimit}
                        totalPosts={totalElements}
                        paginate={paginate}
                        activePage={parseInt(String(page))}
                        iconType={true}
                      />
                    </div>
                  )}
                </>
              )}
            </>
          </Tab.TabPane>
        ))}
      </Tab>
    </div>);
};

export default withMainProvider(SourcingContainer);
