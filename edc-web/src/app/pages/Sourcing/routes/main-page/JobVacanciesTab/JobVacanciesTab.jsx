import React, { useState, useMemo, useEffect, useContext } from 'react';
import { InputWithButton, Select } from 'centralized-design-system/src/Inputs';
import PropTypes from 'prop-types';
import './style.scss';
import DescriptionCard from '../../../components/description-card/job-vacancy/main-page/main-page';
import { translatr } from 'centralized-design-system/src/Translatr';
import Spinner from '@components/common/spinner';
import EmptyState from '@pages/Sourcing/components/empty-state/empty-state';
import CandidatesFilter from '@pages/Sourcing/components/CandidatesFilter/candidates-filter';
import { setupVacancyFilters } from '../utils/filter/setup-vacancy-filters';
import { connect, useSelector } from 'react-redux';
import { MAIN_PAGE_ACTION_TYPES } from '@pages/Sourcing/types/main-page/context/main-page';
import { searchJobVacancies } from 'edc-web-sdk/requests/sourcing';
import {
  fetchJobVacancyData,
  mergeJobFamilyFunctionAndOrgWithResponse
} from '@pages/Sourcing/actions/main-page/main-page-actions';
import { MainPageContext } from '@pages/Sourcing/context/main-page/main-page-context';
import {
  selectCurrentLanguage,
  selectOrgTypes,
  selectSourcingFilterData
} from '@pages/Sourcing/selectors/manage-page/selectors';
import {
  selectAvailableLocations,
  selectCounties
} from '@pages/Sourcing/selectors/main-page/selectors';
import { toMap } from '@pages/TalentMarketplace/shared/CandidateListing/utils/org_units';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';

const sortItems = [
  {
    id: 1,
    option: { sortType: 'BOOKMARKED', sortOrder: 'DESC' },
    value: translatr('web.sourcing.main', 'SortOptionBookmarkedHighToLow')
  },
  {
    id: 2,
    option: { sortType: 'BOOKMARKED', sortOrder: 'ASC' },
    value: translatr('web.sourcing.main', 'SortOptionBookmarkedLowToHigh')
  },
  {
    id: 3,
    option: { sortType: 'END_DATE', sortOrder: 'ASC' },
    value: translatr('web.sourcing.main', 'SortOptionClosingDateNewestToOldest')
  },
  {
    id: 4,
    option: { sortType: 'END_DATE', sortOrder: 'DESC' },
    value: translatr('web.sourcing.main', 'SortOptionClosingDateOldestToNewest')
  },
  {
    id: 5,
    option: { sortType: 'START_DATE', sortOrder: 'DESC' },
    value: translatr('web.sourcing.main', 'SortOptionPostingDateNewestToOldest')
  },
  {
    id: 6,
    option: { sortType: 'START_DATE', sortOrder: 'ASC' },
    value: translatr('web.sourcing.main', 'SortOptionPostingDateOldestToNewest')
  },
  {
    id: 7,
    option: { sortType: 'APPLIED', sortOrder: 'DESC' },
    value: translatr('web.sourcing.main', 'SortOptionNumberOfApplicantsHighToLow')
  },
  {
    id: 8,
    option: { sortType: 'APPLIED', sortOrder: 'ASC' },
    value: translatr('web.sourcing.main', 'SortOptionNumberOfApplicantsLowToHigh')
  },
  {
    id: 9,
    option: { sortType: 'SHORTLISTED', sortOrder: 'DESC' },
    value: translatr('web.sourcing.main', 'SortOptionShortlistedHighToLow')
  },
  {
    id: 10,
    option: { sortType: 'SHORTLISTED', sortOrder: 'ASC' },
    value: translatr('web.sourcing.main', 'SortOptionShortlistedLowToHigh')
  }
];

const displayItems = [
  {
    id: 1,
    option: { jobStatus: 'MY_PUBLISHED_VACANCIES' },
    value: translatr('web.sourcing.main', 'MyPublishedVacancies')
  },
  {
    id: 2,
    option: { jobStatus: 'OPEN' },
    value: translatr('web.sourcing.main', 'AllPublishedVacancies')
  }
];

const JobVacanciesTab = ({
  listOfJobVacancies,
  handleChange,
  sortType,
  sortOrder,
  jobStatus,
  keyword,
  totalElements,
  isLoading,
  handleBookmark,
  getRequestBody,
  setSearchParam,
  theme
}) => {
  const [searchBoxContent, setSearchBoxContent] = useState(keyword);
  const [, setLocalLoadingCount] = useState(0);
  const searchResultsRef = React.useRef(null);
  const { dispatch: contextDispatch } = useContext(MainPageContext);

  const language = useSelector(selectCurrentLanguage);
  const allCountries = useSelector(selectCounties);
  const sourcingFilterData = useSelector(selectSourcingFilterData);
  const orgTypes = toMap(useSelector(selectOrgTypes));
  const availableLocations = useSelector(selectAvailableLocations);

  const defaultSortValue = sortItems.find(
    ({ option }) => option.sortType === sortType && option.sortOrder === sortOrder
  ).value;

  const defaultDisplayValue = displayItems.find(({ option }) => option.jobStatus === jobStatus)
    .value;

  useEffect(() => {
    if (keyword) {
      searchResultsRef.current?.focus();
    }
  }, [totalElements, keyword]);

  const handleClearSearchText = async () => {
    setSearchParam({ keyword: '', page: 1 });
    setSearchBoxContent('');
    await fetchJobVacancyData(
      getRequestBody(1, { keyword: '' }),
      contextDispatch,
      sourcingFilterData
    );
  };

  const renderResultText = useMemo(() => {
    return (
      <>
        {translatr('web.talentmarketplace.main', totalElements === 1 ? 'ResultFor' : 'ResultsFor', {
          counter: totalElements
        })}
        <span> </span>
        <span aria-label={keyword} className="sr-only">
          {keyword}
        </span>
        <span aria-hidden>'{keyword}'</span>
        <span> </span>
        {translatr('web.talentmarketplace.main', 'FromOpportunity', {
          opportunity: translatr('web.sourcing.main', 'JobVacancies')
        }).toLowerCase()}
        <button
          onClick={handleClearSearchText}
          className="ts-filterbar__button--clear"
          title={translatr('web.talentmarketplace.main', 'ClearKeyword')}
          aria-label={translatr('web.talentmarketplace.main', 'ClearKeyword')}
          tabIndex={0}
        >
          <i className="icon-cross-circle" aria-hidden="true" />
        </button>
      </>
    );
  }, [totalElements, keyword, handleClearSearchText]);

  const setSearchData = value => {
    setSearchBoxContent(value);
  };

  const Loader = () => (
    <div className="make-center width-100 mt-24" aria-hidden={!!(isLoading && keyword)}>
      <Spinner />
    </div>
  );

  const getFilterData = async setFilterList => {
    const result = await setupVacancyFilters(
      allCountries,
      orgTypes,
      sourcingFilterData,
      language,
      availableLocations
    );
    setFilterList(result);
  };

  const handleSubmitFilter = async ({ filters }) => {
    await contextDispatch({ type: MAIN_PAGE_ACTION_TYPES.FETCH_START });
    setLocalLoadingCount(prevCount => prevCount + 1);

    let vacancyData = [];
    try {
      vacancyData = await searchJobVacancies(getRequestBody(1, { filters }));
    } catch (e) {
      contextDispatch({
        type: MAIN_PAGE_ACTION_TYPES.FETCH_SUCCESS,
        jobVacancies: [],
        totalPages: 0,
        totalElements: 0
      });
    } finally {
      const values = await mergeJobFamilyFunctionAndOrgWithResponse(
        vacancyData.values,
        language,
        sourcingFilterData
      );
      setLocalLoadingCount(prevCount => {
        const newCount = prevCount - 1;
        if (newCount === 0) {
          if (vacancyData.values && vacancyData.values.length) {
            contextDispatch({
              type: MAIN_PAGE_ACTION_TYPES.FETCH_SUCCESS,
              jobVacancies: values,
              totalPages: vacancyData.totalPages,
              totalElements: vacancyData.totalElements
            });
          } else {
            contextDispatch({
              type: MAIN_PAGE_ACTION_TYPES.FETCH_SUCCESS,
              jobVacancies: [],
              totalPages: vacancyData.totalPages,
              totalElements: vacancyData.totalElements
            });
          }
          setSearchParam({ page: 1 });
        }
        return newCount;
      });
    }
  };

  const isNewDesignEnabled = theme == ThemeId.PLARE;

  return (
    <>
      <div className="sourcing-filters-container">
        <div className="flex align-items-center filter-gap">
          <InputWithButton
            id="sourcing-opportunity-search"
            defaultValue={searchBoxContent}
            placeholder={`${translatr('web.sourcing.main', 'SearchJobVacancies')}...`}
            buttonContent={<i className="icon-search" />}
            onChangeHandler={value => setSearchData(value)}
            onSubmitHandler={() => handleChange({ keyword: searchBoxContent })}
            buttonAriaLabel={translatr('web.common.main', 'Search')}
          />
          <div id="sort-by" className="filter-dropdown-label mb-zero">
            {translatr('web.sourcing.main', 'SortBy')}:
          </div>
          <Select
            defaultValue={defaultSortValue}
            items={sortItems}
            ariaLabelledBy="sort-by"
            onChange={item => handleChange(item.option)}
          />
          <div
            id={translatr('web.sourcing.main', 'Display')}
            className="filter-dropdown-label mb-zero"
          >
            {translatr('web.sourcing.main', 'Display')}:
          </div>
          <Select
            defaultValue={defaultDisplayValue}
            items={displayItems}
            ariaLabelledBy={translatr('web.sourcing.main', 'Display')}
            onChange={item => handleChange(item.option)}
          />
          <label htmlFor="sourcing-opportunity-search" className="visually-hidden">
            {`${translatr('web.sourcing.main', 'SearchJobVacancies')}...`}
          </label>
        </div>
        {keyword && (
          <div className="ts-filterbar-keyword" aria-live="assertive">
            {isLoading
              ? translatr('web.talentmarketplace.main', 'LoadingSearchResults')
              : renderResultText}
          </div>
        )}
      </div>
      {sourcingFilterData && (
        <CandidatesFilter getFilterData={getFilterData} onChange={handleSubmitFilter} />
      )}
      {isLoading && <Loader />}
      <div className={isNewDesignEnabled ? 'sourcing-cards-container' : ''}>
        {!isLoading &&
          listOfJobVacancies.length > 0 &&
          listOfJobVacancies.map((job, index) => (
            <>
              <DescriptionCard key={job.id} {...job} handleBookmark={handleBookmark} />
              {isNewDesignEnabled && index < listOfJobVacancies.length - 1 && (
                <hr className="center-hr-text" />
              )}
            </>
          ))}
      </div>
      {!isLoading && keyword && listOfJobVacancies.length === 0 && (
        <EmptyState type="JobVacancySearch" />
      )}
      {!isLoading && !keyword && listOfJobVacancies.length === 0 && (
        <EmptyState type="JobVacancy" />
      )}
    </>
  );
};

JobVacanciesTab.propTypes = {
  listOfJobVacancies: PropTypes.array,
  keyword: PropTypes.string,
  totalElements: PropTypes.number,
  sortOrder: PropTypes.string,
  sortType: PropTypes.string,
  jobStatus: PropTypes.string,
  handleChange: PropTypes.func,
  isLoading: PropTypes.bool,
  handleBookmark: PropTypes.func,
  getRequestBody: PropTypes.func,
  setSearchParam: PropTypes.func,
  orgTypes: PropTypes.array,
  theme: PropTypes.string
};

export default connect(({ theme }) => ({ theme: theme?.get('themeId') }))(JobVacanciesTab);
