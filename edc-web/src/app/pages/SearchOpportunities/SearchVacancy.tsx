import React, { useEffect, useState, useRef, useContext } from 'react';
import { connect } from 'react-redux';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { search } from 'edc-web-sdk/requests/extOpportunities';
import Results from '@pages/TalentMarketplace/shared/Results';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import SearchHeader from './Components/SearchHeader';
import { mapJobVacancies } from '@pages/TalentMarketplace/Api';
import WithAspirationsContext from '@pages/TalentMarketplace/shared/WithAspirationsContext';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import { ITEM_PER_PAGE } from './util';
import {
  transformFilters,
  prepareLovFiltersForView,
  prepareCustomFiltersForView,
  prepareOrgFiltersForView,
  filterFullySelectedOrganizations
} from '@pages/TalentMarketplace/shared/filters/Filters.utils';
import {
  TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME,
  FILTER_TYPE,
  TM_FILTER_SORT_BY,
  IFilterState,
  ISortingOption
} from '@pages/TalentMarketplace/shared/filters/Filters.constants';
import { tmSaveFiltersConfig } from '@actions/talentmarketplaceActions';
import { loadAllOrganizations, orgVisibility, orgAssociation } from '@actions/organizationsActions';
import { getAvailableFiltersCountersConfig as tmGetAvailableFiltersCountersConfig } from '@actions/availableFilterCountersActions';
import { useCountries, useJobFamilies } from '@pages/TalentMarketplace/shared/hooks';
import SearchFilterPanel from './Components/SearchFilterPanel';
import { LOCATION_USAGE_OPTIONS } from '@pages/TalentMarketplace/helpers';
import {Collection} from 'immutable';
import { getAvailableLocations } from '@actions/availableLocationsActions';

type SearchVacancyStoreProps = {
  currentUser: Collection.Keyed<string, any>,
  organizations: Map<string,any>,
  locationFieldVisibility: object,
  currentUserLang: string,
  availableLocations: Array<any>,
  locationsUsageList: Map<string,any>,
  locationsEnabled: boolean,
  isGeolocationEnabled: boolean,
  countriesLoading: boolean,
  searchFilters: IFilterState,
};

type SearchVacancyDispatchProps = {
  toast: (message: string, type: string) => void,
  getAvailableFiltersCountersConfig: (jobType: string, payload: object) => void,
  saveFiltersConfig: (config: Array<any>) => void,
  loadOrganizations: (lang: string, currentVisibility: string, currentAssociation: string) => void,
  getLocations: (lang: string) => void;
};

type SearchVacancyProps = {
  searchValue: string,
  noDataComponent: () => React.FC,
} & SearchVacancyStoreProps & SearchVacancyDispatchProps;



const SearchVacancy: React.FC<SearchVacancyProps> = ({
  searchValue,
  noDataComponent,
  toast,
  currentUserLang,
  searchFilters,
  getAvailableFiltersCountersConfig,
  organizations,
  availableLocations,
  locationsEnabled,
  locationFieldVisibility,
  locationsUsageList,
  isGeolocationEnabled,
  currentUser,
  saveFiltersConfig,
  loadOrganizations,
  countriesLoading,
  getLocations
}) => {
  const jobType = JOB_TYPE.VACANCY;

  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loadingOrg, setLoadingOrg] = useState(false);
  const [hideMatchingDetails, setHideMatchingDetails] = useState(false);
  const [totalElements, setTotalElements] = useState(0);
  const [defaultSortBy, setDefaultSortBy] = useState(null);
  const [sortBy, setSortBy] = useState<ISortingOption | null>(null);
  const [sortOptions, setSortOptions] = useState<Array<ISortingOption>>(TM_FILTER_SORT_BY);
  const [currentPage, setCurrentPage] = useState(1);
  const [isInitialFetch, setIsInitialFetch] = useState(true);
  const [searchPayload, setSearchPayload] = useState({
    pageNumber: 1,
    keyword: searchValue,
    pageSize: ITEM_PER_PAGE,
    type: jobType,
    language: currentUserLang,
    isDismissedExcluded: false
  });

  const countries = useCountries();
  const { aspirations: aspirationalRoles } = useContext(AspirationsContext);

  const isInitialRender = useRef(true);

  useEffect(() => {
    if (organizations.get('config')?.enable) {
      if (!organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER]) {
        setLoadingOrg(true);
        loadOrganizations(
          currentUserLang,
          orgVisibility.JOB_VACANCY_FILTER,
          orgAssociation.JOB_VACANCY
        );
      } else {
        setLoadingOrg(false);
      }
    }
  }, [
    organizations.get('config')?.enable,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER]
  ]);

  useEffect(() => {
    if (!availableLocations) {
      getLocations(currentUserLang);
      return;
    }

    const orgFilters = prepareOrgFiltersForView({
      organizations,
      orgKeys:
        organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER] || [],
      orgVisibility: orgVisibility.JOB_VACANCY_FILTER
    });
    const lovFilters = prepareLovFiltersForView(jobType);
    const decoratedFilters = prepareCustomFiltersForView({
      currentUser: currentUser.toJS(),
      aspirationalRoles,
      customFilters: searchFilters.config.filter(
        itm => itm.type !== FILTER_TYPE.LOV && itm.type !== FILTER_TYPE.ORG
      ),
      locationsConfig: {
        locations: availableLocations,
        enabled: locationsEnabled,
        visibility: locationFieldVisibility,
        usageContext: LOCATION_USAGE_OPTIONS.JOB_VACANCY_FILTER,
        usageList: locationsUsageList,
        countries: countries
      },
      isGeolocationEnabled,
      jobFamilies: []
    });
    saveFiltersConfig([...lovFilters, ...decoratedFilters, ...orgFilters]);
  }, [
    aspirationalRoles,
    currentUser,
    availableLocations,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER],
    organizations.get('levelForTypeAndVisibility'),
    locationsEnabled,
    locationFieldVisibility,
    locationsUsageList,
    isGeolocationEnabled,
    countries
  ]);

  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    if (searchPayload) {
      if (isInitialRender.current) {
        isInitialRender.current = false;
        return;
      }

      if (isInitialFetch) {
        setIsInitialFetch(false);
        setLoading(true);
        getAvailableFiltersCountersConfig(jobType, {
          ...searchPayload,
          pageNumber: 1,
          isFilterAggregationEnabled: true
        });
      } else {
        setLoadingMore(true);
      }


      const fetchData = async () => {
        try {
          const response = await search(searchPayload, { signal });
          if (!signal.aborted) {
            const { values, totalElements: total, context } = response;
            const { hideMatchingDetails: hideMatch, sortType, sortOrder } = context || {};

            setTotalElements(total || 0);
            setHideMatchingDetails(!!hideMatch);

            if (hideMatch) {
              setSortOptions(prevOptions => prevOptions.filter(f => !f.id.startsWith('MATCH_')));
              setDefaultSortBy(`${sortType}_${sortOrder}`);
            }

            setOpportunities(prev => [...prev, ...mapJobVacancies(values)]);

            setLoading(false);
            setLoadingMore(false);
          }
        } catch (err) {
          if (!signal.aborted) {
            setOpportunities([]);
            setTotalElements(0);
            setLoading(false);
            setLoadingMore(false);
            toast(
              translatr(
                'web.talentmarketplace.main',
                'ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain'
              ),
              'error'
            );
          }
        }
      };
  
      fetchData();
    }

    return () => {
      abortController.abort();
    };
  }, [searchPayload]);

  useEffect(() => {
    setOpportunities([]);
    setCurrentPage(1);
    setIsInitialFetch(true);
    setTotalElements(0);
    setLoading(true);

    const filters = transformFilters(searchFilters);
    const updatedSearchPayload = {
      ...filters,
      organizationId: filterFullySelectedOrganizations(filters.organizationId),
      pageNumber: 1,
      keyword: searchValue,
      pageSize: ITEM_PER_PAGE,
      type: jobType,
      language: currentUserLang,
      isDismissedExcluded: false
    };

    if (sortBy) {
      const [sortType, sortOrder] = sortBy.id.split('_');
      updatedSearchPayload.sortType = sortType;
      updatedSearchPayload.sortOrder = sortOrder;
    }

    setSearchPayload(updatedSearchPayload);
  }, [searchValue, sortBy, searchFilters.filters]);

  useEffect(() => {
    if (!isInitialFetch) {
      const updatedSearchPayload = {
        ...searchPayload,
        type: jobType,
        pageNumber: currentPage
      };
      setSearchPayload(updatedSearchPayload);
    }
  }, [currentPage]);

  const onChangeSort = (value: ISortingOption) => {
    setSortBy(value);
  };

  if (typeof countriesLoading === 'undefined') return null; // wait until redux store is informed that location fetch is started

  return (
    <>
      <SearchFilterPanel
        loading={loading || loadingOrg}
        jobType={jobType}
        filtersState={searchFilters}
      />

      <SearchHeader
        searchValue={searchValue}
        total={totalElements}
        sortOptions={sortOptions}
        sortBy={defaultSortBy}
        onChangeSort={onChangeSort}
        loading={loading}
      />

      {!loading && opportunities.length === 0 ? (
        noDataComponent()
      ) : (
        <div className="all-opportunity-container">
          <h2 className="tab-heading omp-counter-field">
            {translatr('web.talentmarketplace.main', 'AllOpportunities', {
              opportunities: omp('tm_tm_job_vacancies')
            })}
          </h2>

          <Results
            itemsPerPage={ITEM_PER_PAGE}
            loading={loading}
            opportunities={opportunities}
            setOpportunities={setOpportunities}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            jobType={jobType}
            totalElements={totalElements}
            hideMatchingDetails={hideMatchingDetails}
            isLoadMore={true}
            loadMoreLoading={loadingMore}
          />
        </div>
      )}
    </>
  );
};

const mapDispatchToProps = (dispatch: (...args: any[]) => void): SearchVacancyDispatchProps => {
  return {
    toast: (message, type) => dispatch(openSnackBar(message, type)),
    saveFiltersConfig: newConfig =>
      dispatch(tmSaveFiltersConfig(newConfig, TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME)),
    loadOrganizations: (lang, currentVisibility, currentAssociation) =>
      dispatch(loadAllOrganizations(lang, currentVisibility, currentAssociation)),
    getAvailableFiltersCountersConfig: (jobType, payload) =>
      dispatch(tmGetAvailableFiltersCountersConfig(jobType, payload)),
    getLocations: lang => dispatch(getAvailableLocations(lang))
  };
};

const mapStoreStateToProps = ({
  talentmarketplaceReducer,
  currentUser,
  team,
  organizations,
  availableLocations,
  configService,
  locationsConfiguration
}: {
  talentmarketplaceReducer: Map<string, any>,
  currentUser: Collection.Keyed<string, any>,
  team: Map<string, any>,
  organizations: Map<string, any>,
  availableLocations: Map<string, any>,
  configService: Map<string, any>,
  locationsConfiguration: Map<string, any>
}): SearchVacancyStoreProps => {
  return {
    currentUser: currentUser,
    searchFilters: talentmarketplaceReducer.get(TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations,
    availableLocations: availableLocations.get('availableLocations'),
    locationsEnabled: locationsConfiguration.get('enable'),
    locationFieldVisibility: locationsConfiguration.get('visibility'),
    locationsUsageList: locationsConfiguration.get('usageList'),
    isGeolocationEnabled: configService.get('omp')?.['geolocation']?.['enable_geolocation'],
    countriesLoading: availableLocations.get('countriesLoading')
  };
};

export default connect(
  mapStoreStateToProps,
  mapDispatchToProps
)(WithAspirationsContext(SearchVacancy));
