import React, { useEffect } from 'react';
import { string, func, object } from 'prop-types';
import Card from 'centralized-design-system/src/Card/CardContainer';
import EmptyState from 'centralized-design-system/src/EmptyState';
import { translatr } from 'centralized-design-system/src/Translatr';
import { componentMap } from './util';
import './SearchOpportunities.scss';
import { connect } from 'react-redux';
import { getOrgConfiguration } from '@actions/organizationsActions';

const SearchOpportunitiesGlobal = ({
  searchValue,
  activeTab,
  getOrganizationsConfig,
  organizations
}) => {
  const Component = componentMap[activeTab];

  if (!Component) {
    return null;
  }

  useEffect(() => {
    if (!organizations.get('config')) {
      getOrganizationsConfig();
    }
  }, []);

  return (
    // This ID matches the aria-controls attribute on the corresponding tab element
    // to create a relationship between the tab and this panel for screen reader
    <div className="ed-ui search-opportunity_page" id={`results-section-${activeTab}-section`}>
      <div className="search-opportunity_content-container  ">
        <Card>
          <Component
            searchValue={searchValue}
            noDataComponent={() => (
              <EmptyState
                title={translatr('web.people-search.main', 'NoResultsFound')}
                description={translatr(
                  'web.talentmarketplace.main',
                  'SorryNothingMatchesYourCriteriaTryDifferentKeywords'
                )}
                icon="icon-list"
              />
            )}
          />
        </Card>
      </div>
    </div>
  );
};

SearchOpportunitiesGlobal.propTypes = {
  searchValue: string,
  activeTab: string,
  getOrganizationsConfig: func,
  organizations: object
};

export default connect(
  ({ organizations }) => ({
    organizations
  }),
  dispatch => ({
    getOrganizationsConfig: () => dispatch(getOrgConfiguration())
  })
)(SearchOpportunitiesGlobal);
