import React, { useState, useRef, MutableRefObject } from 'react';
import { Button } from 'centralized-design-system/src/Buttons';
import { translatr } from 'centralized-design-system/src/Translatr';
import OpportunitiesFilterModal from '@pages/TalentMarketplace/shared/filters/OpportunitiesFilterModal';
import { FILTERS_DEFAULT_ASSOCIATION_ID, IFilterState } from '@pages/TalentMarketplace/shared/filters/Filters.constants';
import SearchFilterActiveBar from './SearchFilterActiveBar';
import "./SearchFilterPanel.scss";

type SearchFilterPanelProps = {
  jobType: string,
  loading: boolean,
  filtersState: IFilterState
}

const SearchFilterPanel: React.FC<SearchFilterPanelProps> = ({ jobType, loading, filtersState }) => {
  const filtersButtonRef: MutableRefObject<HTMLButtonElement> = useRef();

  const [showFilterModal, setShowFilterModal] = useState(false);
  const [openedFromHtmlElement, setOpenedFromHtmlElement] = useState<undefined | HTMLButtonElement>(undefined);

  const filters = filtersState?.filters?.[FILTERS_DEFAULT_ASSOCIATION_ID];
  const filterCount = Object.values(filters).reduce((count, filter) => count + (Array.isArray(filter) ? filter.length : 0), 0);

  const handleClickClear = () => {
    if(filtersButtonRef?.current) {
      filtersButtonRef.current.focus();
    }
  };

  const openFilterFlyout = (e: React.MouseEvent<HTMLButtonElement>) => {
    setOpenedFromHtmlElement(e.currentTarget);
    setShowFilterModal(true);
  };

  return (
    <>
      <Button
        ref={filtersButtonRef}
        id="search-opportunity_filter-button"
        color="secondary"
        variant="ghost"
        onClick={openFilterFlyout}
        aria-describedby='search-opportunity_filter-button-description'
      >
        <i className="filter-button icon-filter" aria-hidden="true" />
        <span className="xl-font-size text-color">
          {translatr('web.talentmarketplace.main', 'Filters')}{' '}
        </span>
        {filterCount > 0 && <span className="filter-count" aria-hidden="true">
          <span>{filterCount}</span>
        </span> }
      </Button>

      <span className="sr-only" id="search-opportunity_filter-button-description">
        {translatr('web.talentmarketplace.main', 'XFilterApplied', { nmb: filterCount })}
      </span>

      <SearchFilterActiveBar
        filtersState={filtersState}
        onClickMore={openFilterFlyout}
        onClickClear={handleClickClear}
      />

      <OpportunitiesFilterModal
        isOpen={showFilterModal}
        displayAsFlyout={true}
        jobType={jobType}
        loading={loading}
        onClose={(appliedFilters = false) => {
          setShowFilterModal(false);
          setTimeout(()=> {
            if(appliedFilters) {
              filtersButtonRef?.current?.focus();
            } else if(openedFromHtmlElement && document.body.contains(openedFromHtmlElement)) {
              openedFromHtmlElement.focus();
            } else {
              filtersButtonRef?.current?.focus();
            }
          },0)
        }}
        filtersState={filtersState}
      /> 
    </>
  );
};

export default SearchFilterPanel;
