import React from 'react';
import { array, func, number, string, bool } from 'prop-types';
import { Select } from 'centralized-design-system/src/Inputs';
import { TM_FILTER_SORT_BY } from '@pages/TalentMarketplace/shared/filters/Filters.constants';
import { translateWCM } from '@pages/SearchPeopleV2/utils';
import { translatr } from 'centralized-design-system/src/Translatr';

const SearchHeader = ({
  searchValue,
  total,
  sortOptions,
  sortBy,
  onChangeSort,
  loading = false
}) => {
  const sortingOptions = (sortOptions || TM_FILTER_SORT_BY).map(option => ({
    ...option,
    value: translatr(option.i18n[0], option.i18n[1])
  }));

  return (
    searchValue && (
      <div className="search-opportunity-header">
        <span aria-live="assertive">
          {loading ? (
            translatr('web.talentmarketplace.main', 'LoadingSearchResults')
          ) : (
            <>
              <span aria-hidden="true">
                {translateWCM('ResultsFor')}
                <strong>{` ${searchValue} (${total})`}</strong>
              </span>

              <span className="sr-only">
                {translatr('web.talentmarketplace.main', total === 1 ? 'ResultFor' : 'ResultsFor', {
                  counter: total
                })}{' '}
                {searchValue}
              </span>
            </>
          )}
        </span>
        <Select
          id="ed-opportunities-sort-by"
          defaultValue={sortBy}
          onChange={onChangeSort}
          items={sortingOptions}
          title={translatr('web.search.main', 'SortBy')}
          isTranslated={true}
        />
      </div>
    )
  );
};

SearchHeader.propTypes = {
  searchValue: string,
  total: number,
  sortOptions: array,
  sortBy: string,
  onChangeSort: func,
  loading: bool
};

export default SearchHeader;
