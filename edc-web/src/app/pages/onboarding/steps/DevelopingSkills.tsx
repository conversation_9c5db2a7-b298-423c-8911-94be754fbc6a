import React, { ReactNode, useState } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import Info from '../components/Info';
import SkillsToDevelop from '@components/ProfileSteps/SkillsToDevelop';
import { users } from 'edc-web-sdk/requests';
import { useAppInfo } from '../contexts/Provider';
import useApi from '../utils/useCommonApi';
import { useOnboardingData } from '../contexts/OnboardingProvider';

interface DevelopingSkillsProps {
  Navigator: ReactNode
}

const DevelopingSkills: React.FC<DevelopingSkillsProps> = ({ Navigator }) => {
  const { user } = useAppInfo();
  const currentUser = { id: user.get('id') };
  const { postOnboardingStep } = useApi();
  const { componentsPayload, setComponentData, setComponentsPayload } = useOnboardingData();
  const skillsLimit = getConfig("limit_options")?.interests_limit ?? undefined;
  const atLeastOneSkillSelected = !!componentsPayload?.['learning_goals']?.length
  const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(false);

  const onNextClick = async (isLastStep: boolean, currentStep: number) => {
    const response = users.postLearningDomainTopicsNew(componentsPayload['learning_goals'], user.get('id'));
    if (response) {
      return !!await postOnboardingStep(currentUser, currentStep, isLastStep);
    } else return false;
  };


  const resolveTooltipMessage = () :string => {
    if (!atLeastOneSkillSelected){
      return "You have to select at least one skill.";
    }
    return "";
  }

  return (
    <>
      <Info
        title={translatr('web.myprofile.main', 'AddSkillsToDevelop')}
        description={translatr('web.myprofile.main', 'AddSkillsToDevelopDesc')}
      />
      <div className="form-segment">
        <div className="form-wrapper">
          <SkillsToDevelop
            setComponentData={setComponentData}
            setComponentsPayload={setComponentsPayload}
            selectedSkillsLimit={skillsLimit}
            setIsSaveButtonDisabled={setIsNextButtonDisabled}
          />
        </div>
      </div>
      <Navigator
        onNextClick={onNextClick}
        canGoNext={atLeastOneSkillSelected && !isNextButtonDisabled}
        tooltipMessage={resolveTooltipMessage()}
        skipSteps={false}
      />
    </>
  );
};

export default DevelopingSkills;
