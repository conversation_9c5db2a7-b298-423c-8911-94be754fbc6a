import React, { useEffect, useState } from 'react';
import { elementType } from 'prop-types';
import { AsyncSearchInput, Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  getAllProficiencyLevels,
  selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import cx from 'classnames';

import { getOcgOrEgt } from '@utils/utils';
import { useAppInfo } from '../contexts/Provider';
import useApi from '../utils/useCommonApi';
import Info from '../components/Info';
import SortedTags from '../components/SortedTags';
import errorBoundaryWrapper from '../components/ErrorBoundaryWrapper';

function Expertise({ Navigator }) {
  const {
    teamConfig,
    profile,
    currentUserLang,
    isOcgEnabled,
    isEgtEnabled,
    user,
    team
  } = useAppInfo();
  const limit = (teamConfig.limit_options && teamConfig.limit_options.expertise_limit) || 3;
  const bia = teamConfig.enabled_bia;

  const [error, setError] = useState('');

  const [value, setValue] = useState(null);
  const [level, setLevel] = useState('');
  const [mode, setMode] = useState(0);
  const [levels, setLevels] = useState([]);

  const { postExpertiseDomainTopics, postOnboardingStep } = useApi();

  const onSearchChange = skillParam => {
    setLevel(levels[0].value);
    const expertTopic = {
      topic_id: skillParam.id,
      topic_label: skillParam.label,
      topic_name: skillParam.name
    };

    if (!bia) {
      if (checkLimit()) {
        setList(prev => ({
          ...prev,
          [`_${expertTopic.topic_id}`]: expertTopic
        }));
        setValue(null);
      }
    } else {
      setValue(expertTopic);
    }
  };

  const [list, setList] = useState({});

  const checkLimit = () => {
    if (limit) {
      const total = Object.keys(list).length + 1;
      if (total > limit) {
        const errText = translatr('onboarding.common.common', 'ExpertiseSkillsLimit', {
          limit: limit
        });
        setError(() => errText);
        return false;
      }
      setError(() => '');
      return true;
    }
    setError(() => '');
    return true;
  };

  const addItem = () => {
    const newValue = { ...value };
    if (bia) {
      newValue.proficiencyLevel = level.name;
    }
    if (checkLimit()) {
      if (list[`_${value.topic_id}`]) {
        const errText = translatr('onboarding.common.common', 'SameSkillCannotBeAddedTwice');
        setError(errText);
      } else {
        setList(prev => ({
          ...prev,
          [`_${value.topic_id}`]: newValue
        }));
      }
      setLevel(levels[0].value);
      setValue(null);
    }
  };

  const getAvailableProficiencyLevels = () => {
    return levels.filter(proficiencyLevel => !proficiencyLevel.hidden);
  };

  useEffect(() => {
    if (!value) {
      setMode(prev => prev + 1);
    }
  }, [Object.keys(list).length]);

  useEffect(() => {
    const proficiencyLevels = team.get('proficiencyLevels');
    const newLevels = [
      { ...selectLevelPlaceholderOption(), disabled: true },
      ...getAllProficiencyLevels(proficiencyLevels, currentUserLang)
    ];
    setLevels(newLevels);
    setLevel(newLevels[0].value);
    if (profile.get('expertTopics')) {
      const expertTopics = profile.get('expertTopics').toJS();
      if (Array.isArray(expertTopics)) {
        setList(
          expertTopics.reduce((agg, topic) => {
            topic.proficiencyLevel = topic.proficiency_level || newLevels[1].name;
            agg[`_${topic.topic_id}`] = topic;
            return agg;
          }, {})
        );
      }
    }
  }, []);

  const validationFn = async (isLastStep, currentStep) => {
    setError(() => '');
    const currentUser = {
      id: user.get('id')
    };
    const newList = Object.values(list).map(({ proficiencyLevel, ...rest }) => ({
      ...rest,
      proficiency_level: proficiencyLevel
    }));
    const respExpert = await postExpertiseDomainTopics(newList, user.get('id'));
    if (!respExpert) return false;

    const respOnboard = await postOnboardingStep(currentUser, currentStep, isLastStep);
    return !!respOnboard;
  };
  return (
    <React.Fragment>
      <Info
        title={translatr('onboarding.common.common', 'AddSkills')}
        description={translatr('onboarding.common.common', 'TellOthersWhatYouAreGoodAt')}
      />
      <div className="xl-margin-top">
        <form className="ed-input-container margin-x-auto">
          <div className="justflex align-item-flex-end flex-wrap add-level">
            <div
              className={cx('segement-seperator m-padding-right', {
                'w-40': bia,
                'width-100': !bia
              })}
            >
              <AsyncSearchInput
                key={mode}
                users={false}
                groups={false}
                channels={false}
                onChange={onSearchChange}
                extraPayload={{
                  organization_id: teamConfig.orgId,
                  fields: 'id,label,name',
                  'taxo_subtypes[]': getOcgOrEgt(isOcgEnabled, isEgtEnabled)
                }}
                extraData={{
                  currentUserLang: currentUserLang
                }}
                topics={true}
                required
                title={translatr('onboarding.common.common', 'Skills')}
                placeholder={translatr('onboarding.common.common', 'SearchSkills')}
                description={translatr('onboarding.common.common', 'SelectSkills')}
                noOptionsMessage={translatr('onboarding.common.common', 'NoResultFound')}
                isClearable={true}
              />
            </div>
            {bia && (
              <div className="w-40 segement-seperator m-padding-right">
                <Select
                  description={translatr('web.common.main', 'SelectLevelOfTheSkill')}
                  defaultValue={level}
                  onChange={selectedLevel => setLevel(selectedLevel)}
                  required
                  disabled={!value}
                  items={getAvailableProficiencyLevels()}
                  title={translatr('cds.common.main', 'Level')}
                  isTranslated
                  translateDropDownOptions={false}
                />
              </div>
            )}

            {bia && (
              <div
                className={cx('w-20 segement-seperator flex-end ', {
                  'button-highlight': value || level !== ''
                })}
              >
                <button
                  type="button"
                  className="ed-btn ed-btn-primary-custom add-button"
                  disabled={!value || level === levels[0].value}
                  onClick={addItem}
                >
                  {translatr('onboarding.common.common', 'Add')}
                </button>
              </div>
            )}
          </div>
          <div className="s-margin-sides s-margin-ends input-error">{error}</div>

          <div className="user-level justflex flex-wrap">
            <SortedTags
              list={Object.values(list)}
              options={levels.slice(1)}
              setList={setList}
              sorted={bia}
              isTranslated
            />
          </div>
          <Navigator
            validationFn={validationFn}
            canSkip={Object.values(list).length ? false : true}
          />
        </form>
      </div>
    </React.Fragment>
  );
}
export default errorBoundaryWrapper(Expertise);

Expertise.propTypes = {
  Navigator: elementType
};
