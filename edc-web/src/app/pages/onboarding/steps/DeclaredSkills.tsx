import React, { ReactNode, useState } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import { updateUserSkills } from '@components/ProfileSteps/CurrentSkills/helper';
import CurrentSkills from '@components/ProfileSteps/CurrentSkills';
import Info from '../components/Info';
import useApi from '../utils/useCommonApi';
import { useAppInfo } from '../contexts/Provider';
import { useOnboardingData } from '../contexts/OnboardingProvider';

interface DeclaredSkillsProps {
  Navigator: ReactNode
}

const DeclaredSkills: React.FC<DeclaredSkillsProps> = ({ Navigator }) => {
  const { user } = useAppInfo();
  const currentUser = { id: user.get('id') };
  const { postOnboardingStep } = useApi();
  const { componentData, componentsPayload, setComponentData, setComponentsPayload } = useOnboardingData();
  const skillsLimit = getConfig("limit_options")?.expertise_limit ?? undefined;
  const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(false);

  const onNextClick = async (isLastStep: boolean, currentStep: number) => {
    const response = updateUserSkills(componentsPayload?.['current_skills']);
    if (response) {
      return  !!await postOnboardingStep(currentUser, currentStep, isLastStep);
    } else return false;
  };

  return (
    <>
      <Info
        title={translatr('web.myprofile.main', 'AddYourSkills')}
        description={translatr('web.myprofile.main', 'AddYourSkillsDesc')}
      />
      <div className="form-segment">
        <div className="form-wrapper">
          <CurrentSkills
            componentData={componentData}
            setComponentData={setComponentData}
            componentsPayload={componentsPayload}
            setComponentsPayload={setComponentsPayload}
            selectedSkillsLimit={skillsLimit}
            setIsSaveButtonDisabled={setIsNextButtonDisabled}
          />
        </div>
      </div>
      <Navigator
        onNextClick={onNextClick}
        canGoNext={!isNextButtonDisabled}
      />
    </>
  );
};

export default DeclaredSkills;
