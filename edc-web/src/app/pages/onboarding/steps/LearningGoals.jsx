import React, { useEffect, useState } from 'react';
import { elementType } from 'prop-types';
import cx from 'classnames';
import truncateMessageText from 'edc-web-sdk/helpers/truncateMessageText';
import { AsyncSearchInput, Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  getAllProficiencyLevels,
  selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import {
  getProfileLanguage,
  getTranslatedLabel
} from 'centralized-design-system/src/Utils/TranslatedLabels';

import { getOcgOrEgt } from '@utils/utils';
import { useAppInfo } from '../contexts/Provider';
import useApi from '../utils/useCommonApi';
import SortedTags from '../components/SortedTags';
import Info from '../components/Info';
import errorBoundaryWrapper from '../components/ErrorBoundaryWrapper';

const LearningGoals = ({ Navigator }) => {
  const {
    teamConfig,
    defaultTopics,
    currentUserLang,
    profile,
    user,
    currentUser,
    isOcgEnabled,
    isEgtEnabled,
    team
  } = useAppInfo();
  const limit = (teamConfig.limit_options && teamConfig.limit_options.interests_limit) || 3;
  const bia = teamConfig.enabled_bia;
  const [levels, setLevels] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [recommendedTopics, setRecommendedTopics] = useState([]);
  const [error, setError] = useState('');
  const [value, setValue] = useState(null);
  const [level, setLevel] = useState('');
  const [list, setList] = useState({});
  const [mode, setMode] = useState(0);
  const orgConfig = team?.get('OrgConfig');
  const labels = orgConfig?.labels?.['web/labels/interests'];
  const langs = team?.get('languages');
  const profileLanguage = getProfileLanguage({ langs, currentUserLang }) || 'english';
  const interestText = getTranslatedLabel(labels, profileLanguage);
  const interestLabelSingular = interestText || 'Learning Goal';
  const interestLabelPlural = interestText || 'Learning Goals';

  const { postLearningDomainTopics, postOnboardingStep } = useApi();

  const onSearchChange = (skillParam, addedFromRecommended = false) => {
    if (!skillParam) {
      setValue(null);
      setInputValue('');
      return;
    }

    setLevel(levels[0].value);
    const learningTopic = {
      topic_id: skillParam.id ? skillParam.id : skillParam.topic_id,
      topic_label: skillParam.label ? skillParam.label : skillParam.topic_label,
      topic_name: skillParam.name ? skillParam.name : skillParam.topic_name
    };

    if (!bia) {
      if (checkLimit()) {
        setList(prev => ({
          ...prev,
          [`_${learningTopic.topic_id}`]: learningTopic
        }));
        setValue(null);
      }
    } else {
      setValue(learningTopic);
      addedFromRecommended && setMode(prev => prev + 1);
    }
  };

  useEffect(() => {
    if (!value) {
      setMode(prev => prev + 1);
    }
  }, [Object.keys(list).length]);

  useEffect(() => {
    const proficiencyLevels = team.get('proficiencyLevels');
    const newLevels = [
      { ...selectLevelPlaceholderOption(), disabled: true },
      ...getAllProficiencyLevels(proficiencyLevels, currentUserLang)
    ];
    setLevels(newLevels);
    setLevel(newLevels[0].value);
    if (profile.get('learningTopics')) {
      const learningTopics = profile.get('learningTopics').toJS();
      if (Array.isArray(learningTopics)) {
        setList(
          learningTopics.reduce((agg, topic) => {
            topic.proficiencyLevel = topic.proficiency_level || newLevels[1].name;
            agg[`_${topic.topic_id}`] = topic;
            return agg;
          }, {})
        );
      }
    }
  }, []);

  useEffect(() => {
    const topicsIds = Object.values(list).map(item => item?.topic_id);
    setRecommendedTopics(
      (defaultTopics || []).filter(item => !topicsIds.includes(item.topic_id || item.id))
    );
  }, [defaultTopics, list]);

  const checkLimit = () => {
    if (limit) {
      const total = Object.keys(list).length + 1;
      if (total > limit) {
        const errText = translatr('onboarding.common.common', 'LearningGoalsLimit', {
          limit,
          interestLabelPlural,
          interestLabelSingular
        });
        setError(() => errText);
        return false;
      }
      setError(() => '');
      return true;
    }
    setError(() => '');
    return true;
  };

  const addItem = event => {
    event.preventDefault();
    const newValue = { ...value };
    if (bia) {
      newValue.proficiencyLevel = level.name;
    }
    if (checkLimit()) {
      if (list[`_${value.topic_id}`]) {
        const errText = translatr('onboarding.common.common', 'LimitError', {
          interestLabelErr: interestLabelSingular
        });
        setError(errText);
      } else {
        setList(prev => ({
          ...prev,
          [`_${value.topic_id}`]: newValue
        }));

        if (recommendedTopics?.length) {
          setRecommendedTopics(prev => {
            const newArr = [...prev];
            return newArr.filter(item => item.topic_id !== newValue.topic_id);
          });
        }
      }
      setLevel(levels[0].value);
      setValue(null);
      setInputValue('');
    }
  };

  const getAvailableProficiencyLevels = () => {
    return levels.filter(proficiencyLevel => !proficiencyLevel.hidden);
  };

  const validationFn = async (isLastStep, currentStep) => {
    if (Object.keys(list).length === 0) {
      const errText = translatr('onboarding.common.common', 'ValidationError', {
        interestLabelErr: interestLabelSingular
      });
      setError(() => errText);
      return false;
    }

    setError(() => '');
    const currentUserObj = {
      id: user.get('id')
    };

    const newList = Object.values(list).map(({ proficiencyLevel, ...rest }) => ({
      ...rest,
      proficiency_level: proficiencyLevel
    }));

    const respLearn = await postLearningDomainTopics(newList, user.get('id'));
    if (!respLearn) return false;
    const respOnboard = await postOnboardingStep(currentUserObj, currentStep, isLastStep);
    return !!respOnboard;
  };

  return (
    <>
      <Info
        title={translatr('onboarding.common.common', 'MainOnboardingLabel', {
          interestLabel: interestLabelPlural
        })}
        description={translatr('onboarding.learning-goal.step-two', 'PersonalizeContent', {
          name: currentUser.get('first_name'),
          interestLabelPlural
        })}
        desc={translatr('onboarding.learning-goal.step-two', 'LearningGoalLimitLabelV1', {
          interestLabelPlural,
          limit
        })}
      />
      <div className="xl-margin-top">
        <form className="ed-input-container margin-x-auto">
          <div className="justflex align-item-flex-end flex-wrap add-level">
            <div
              className={cx('segement-seperator m-padding-right', {
                'w-40': bia,
                'width-100': !bia
              })}
            >
              <AsyncSearchInput
                key={mode}
                isClearable={true}
                items={[value]}
                defaultInputValue={inputValue}
                users={false}
                groups={false}
                channels={false}
                onChange={onSearchChange}
                id="select-goal"
                topics={true}
                extraPayload={{
                  organization_id: teamConfig.orgId,
                  fields: 'id,label,name',
                  'taxo_subtypes[]': getOcgOrEgt(isOcgEnabled, isEgtEnabled)
                }}
                extraData={{
                  currentUserLang: currentUserLang
                }}
                required={true}
                optional="*"
                title={`${interestLabelPlural}`}
                placeholder={translatr(
                  'onboarding.common.common',
                  'LearningGoalSearchPlaceholder',
                  {
                    interestLabel:
                      interestLabelPlural.length > 20
                        ? truncateMessageText(interestLabelPlural, 14, true)
                        : interestLabelPlural
                  }
                )}
                description={translatr('onboarding.common.common', 'LearningGoalSearchLabel', {
                  interestLabel: interestLabelPlural
                })}
                noOptionsMessage={translatr('onboarding.common.common', 'NoResultFound')}
              />
            </div>
            {bia && (
              <div className="w-40 segement-seperator m-padding-right">
                <Select
                  id="select-level"
                  description={translatr('onboarding.common.common', 'LevelDescription', {
                    interestLabel: interestLabelPlural
                  })}
                  defaultValue={level}
                  onChange={selectedLevel => setLevel(selectedLevel)}
                  required
                  disabled={!value}
                  items={getAvailableProficiencyLevels()}
                  title={translatr('cds.common.main', 'Level')}
                  labelHtmlFor="select-level"
                  isTranslated
                  translateDropDownOptions={false}
                />
              </div>
            )}

            {bia && (
              <div
                className={cx('w-20 segement-seperator flex-end', {
                  'button-highlight': value || level !== '0'
                })}
              >
                <button
                  type="button"
                  className="ed-btn ed-btn-primary-custom add-button"
                  disabled={!value || level === levels[0].value}
                  onClick={addItem}
                >
                  {translatr('cds.common.main', 'Add')}
                </button>
              </div>
            )}
          </div>
          <div className="s-margin-sides s-margin-ends input-error">{error}</div>
          <div className={`user-level ${bia ? 'justflex flex-wrap' : ''}`}>
            <SortedTags
              list={Object.values(list)}
              options={levels.slice(1)}
              setList={setList}
              sorted={bia}
              defaultTopics={defaultTopics}
              recommendedTopics={recommendedTopics}
              setRecommendedTopics={setRecommendedTopics}
              onSearchChange={onSearchChange}
              setInputValue={setInputValue}
              interestLabel={interestLabelPlural}
              checkLimit={checkLimit}
              isTranslated
            />
          </div>
          <Navigator validationFn={validationFn} />
        </form>
      </div>
    </>
  );
};

export default errorBoundaryWrapper(LearningGoals);

LearningGoals.propTypes = {
  Navigator: elementType
};
