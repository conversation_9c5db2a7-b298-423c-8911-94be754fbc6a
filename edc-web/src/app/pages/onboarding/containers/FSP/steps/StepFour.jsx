import React, { useEffect, useState } from 'react';
import { elementType } from 'prop-types';
import cx from 'classnames';
import { Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  getAllProficiencyLevels,
  selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';

import useFSPInfo from '../util/useFSPInfo';
import { useAppInfo } from '../../../contexts/Provider';
import useApi from '../../../utils/useCommonApi';
import SortedTags from '../../../components/SortedTags';
import Info from '../../../components/Info';
import ProfileImage from '../../../components/ProfileImage';
import errorBoundaryWrapper from '../../../components/ErrorBoundaryWrapper';

function StepFour({ Navigator }) {
  const { teamConfig, currentUserLang, profile, user, currentUser, team } = useAppInfo();
  const { multiLevelTaxonomy } = useFSPInfo();
  const limit = (teamConfig.limit_options && teamConfig.limit_options.interests_limit) || 3;
  const bia = teamConfig.enabled_bia;

  const [error, setError] = useState('');

  const [value, setValue] = useState('0');
  const [level, setLevel] = useState('');
  const [list, setList] = useState({});
  const [levels, setLevels] = useState([]);

  const { postLearningDomainTopics, postOnboardingStep } = useApi();

  const onSearchChange = skillParam => {
    setValue('0');
    if (skillParam && skillParam.value) {
      const learningTopic = {
        topic_id: skillParam.id,
        topic_label: skillParam.label,
        topic_name: skillParam.name
      };

      if (!bia) {
        if (checkLimit()) {
          setList(prev => ({
            ...prev,
            [`_${learningTopic.topic_id}`]: learningTopic
          }));
          setValue('0');
        }
      } else {
        setValue(learningTopic);
      }
    }
  };

  useEffect(() => {
    const proficiencyLevels = team.get('proficiencyLevels');
    const newLevels = [
      { ...selectLevelPlaceholderOption(), disabled: true },
      ...getAllProficiencyLevels(proficiencyLevels, currentUserLang)
    ];
    setLevels(newLevels);
    setLevel(newLevels[0].value);
    if (profile.get('learningTopics')) {
      const learningTopics = profile.get('learningTopics').toJS();
      if (Array.isArray(learningTopics)) {
        const data = learningTopics
          .filter(learningTopic => {
            return multiLevelTaxonomy.some(
              mlTaxonomy => mlTaxonomy.value === learningTopic.topic_id
            );
          })
          .reduce((agg, topic) => {
            topic.proficiencyLevel = topic.proficiency_level || newLevels[1].name;
            agg[`_${topic.topic_id}`] = topic;
            return agg;
          }, {});
        setList(data);
      }
    }
  }, [multiLevelTaxonomy]);

  const checkLimit = () => {
    if (limit) {
      const total = Object.keys(list).length + 1;
      if (total > limit) {
        const errText = translatr('onboarding.fsp.step-four', 'DigitalSkillsLimit', {
          limit: limit
        });
        setError(errText);
        return false;
      }
      setError('');
      return true;
    }
    setError('');
    return true;
  };
  const addItem = () => {
    const newValue = { ...value };
    if (bia) {
      newValue.proficiencyLevel = level.name;
    }

    if (checkLimit()) {
      if (list[`_${value.topic_id}`]) {
        const errText = translatr('onboarding.common.common', 'SameSkillCannotBeAddedTwice');
        setError(errText);
      } else {
        setList(prev => ({
          ...prev,
          [`_${value.topic_id}`]: newValue
        }));
      }
      setLevel(levels[0].value);
      setValue('0');
    }
  };

  const validationFn = async (isLastStep, currentStep) => {
    if (Object.keys(list).length === 0) {
      const errText = translatr('onboarding.fsp.step-four', 'PleaseAddAtLeastOneDigitalSkill');
      setError(errText);
      return false;
    }

    setError('');
    const currentUserObj = {
      id: user.get('id')
    };

    const newList = Object.values(list).map(({ proficiencyLevel, ...rest }) => ({
      ...rest,
      proficiency_level: proficiencyLevel
    }));

    const respLearning = await postLearningDomainTopics(newList, user.get('id'));
    if (!respLearning) return false;
    const respOnboard = await postOnboardingStep(currentUserObj, currentStep, isLastStep);
    return !!respOnboard;
  };

  const getAvailableProficiencyLevels = () => {
    return levels.filter(proficiencyLevel => !proficiencyLevel.hidden);
  };

  return (
    <>
      <Info
        title={translatr('onboarding.common.common', 'WelcomeUser', {
          name: currentUser.get('first_name')
        })}
        description={translatr('onboarding.fsp.step-four', 'SelectSkillsInTheAreaOfYourInterest', {
          limit: limit
        })}
      />
      <div className="xl-margin-top">
        <div className="image-min-height">
          <div className="image-wrapper make-center">
            <ProfileImage disableUpload={true} />
          </div>
        </div>
        <form className="ed-input-container margin-x-auto">
          <div className="justflex align-item-flex-end flex-wrap">
            <div
              className={cx('segement-seperator m-padding-right', {
                'w-40': bia,
                'width-100': !bia
              })}
            >
              <Select
                onChange={onSearchChange}
                defaultValue={value}
                required
                items={[
                  {
                    id: 0,
                    value: '0',
                    label: translatr('web.common.main', 'ChooseOptions'),
                    disabled: true
                  },
                  ...(multiLevelTaxonomy || [])
                ]}
                title={translatr('onboarding.fsp.step-four', 'DigitalSkills')}
                placeholder={translatr('onboarding.fsp.step-four', 'SearchDigitalSkills')}
                translateDropDownOptions={false}
                isTranslated
              />
            </div>
            {bia && (
              <div className="w-40 segement-seperator m-padding-right">
                <Select
                  description={translatr(
                    'onboarding.fsp.step-four',
                    'SelectLevelOfTheLearningGoals'
                  )}
                  defaultValue={level}
                  onChange={selectedLevel => setLevel(selectedLevel)}
                  required
                  disabled={!value || value === '0'}
                  items={getAvailableProficiencyLevels()}
                  title={translatr('cds.common.main', 'Level')}
                  isTranslated
                  translateDropDownOptions={false}
                />
              </div>
            )}

            {bia && (
              <div className="w-20 segement-seperator flex-end ">
                <button
                  type="button"
                  className="ed-btn ed-btn-primary-custom add-button"
                  disabled={!value || level === levels[0]?.value}
                  onClick={addItem}
                >
                  {translatr('web.common.main', 'Add')}
                </button>
              </div>
            )}
          </div>
          <div className="s-margin-sides s-margin-ends input-error">{error}</div>
          <div className="user-level justflex flex-wrap">
            <SortedTags
              list={Object.values(list)}
              options={levels.slice(1)}
              setList={setList}
              sorted={bia}
              isTranslated
            />
          </div>
          <Navigator validationFn={validationFn} />
        </form>
      </div>
    </>
  );
}
export default errorBoundaryWrapper(StepFour);

StepFour.propTypes = {
  Navigator: elementType
};
