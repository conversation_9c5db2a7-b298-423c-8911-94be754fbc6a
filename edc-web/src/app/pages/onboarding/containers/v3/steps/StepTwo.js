import React, { useEffect, useState } from 'react';
import { elementType } from 'prop-types';
import { isArray } from 'lodash';
import cx from 'classnames';
import { Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  getAllProficiencyLevels,
  selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';

import { useAppInfo } from '../../../contexts/Provider';
import useApi from '../../../utils/useCommonApi';
import SortedTags from '../../../components/SortedTags';
import Info from '../../../components/Info';
import ProfileImage from '../../../components/ProfileImage';
import errorBoundaryWrapper from '../../../components/ErrorBoundaryWrapper';

function StepTwo({ Navigator }) {
  const { teamConfig, currentUserLang, profile, user, currentUser, team } = useAppInfo();
  const limit = (teamConfig.limit_options && teamConfig.limit_options.interests_limit) || 3;
  const bia = teamConfig.enabled_bia;

  const [error, setError] = useState('');

  const [value, setValue] = useState(null);
  const [level, setLevel] = useState('');
  const [list, setList] = useState({});
  const [jobRoles, setJobRoles] = useState([]);
  const [topicId, setTopicId] = useState(null);
  const [levels, setLevels] = useState([]);

  const { postLearningDomainTopics, postOnboardingStep, getJobRoles } = useApi();
  const taxonomyDomain = teamConfig?.taxonomy_domain;

  const onSearchChange = skillParam => {
    setValue(null);
    setLevel(levels[0].value);
    if (skillParam && skillParam.value) {
      const learningTopic = {
        topic_id: skillParam.id ? skillParam.id : skillParam.value,
        topic_label: skillParam.label ? skillParam.label : skillParam.topic_label,
        topic_name: skillParam.path ? skillParam.path : skillParam.topic_name
      };

      if (!bia) {
        if (checkLimit()) {
          setList(prev => ({
            ...prev,
            [`_${learningTopic.topic_id}`]: learningTopic
          }));
        }
      } else {
        setValue(learningTopic);
      }
    }
  };

  const getAvailableProficiencyLevels = () => {
    return levels.filter(proficiencyLevel => !proficiencyLevel.hidden);
  };

  useEffect(() => {
    const proficiencyLevels = team.get('proficiencyLevels');
    const newLevels = [
      { ...selectLevelPlaceholderOption(), disabled: true },
      ...getAllProficiencyLevels(proficiencyLevels, currentUserLang)
    ];
    setLevels(newLevels);
    setLevel(newLevels[0].value);
    getJobRoles(taxonomyDomain)
      .then(jobRolesResp => {
        setJobRoles(jobRolesResp);
      })
      .catch(err => console.error('Error while getting job roles', err));
    if (profile.get('learningTopics')) {
      const learningTopics = profile.get('learningTopics').toJS();
      if (isArray(learningTopics)) {
        const data = learningTopics
          .filter(learningTopic => {
            return jobRoles.some(jobRole => jobRole.value === learningTopic.topic_id);
          })
          .reduce((agg, topic) => {
            topic.proficiencyLevel = topic.proficiency_level || newLevels[1].name;
            agg[`_${topic.topic_id}`] = topic;
            return agg;
          }, {});
        setList(data);
      }
    }
  }, []);

  const checkLimit = () => {
    setError('');
    if (limit) {
      const total = Object.keys(list).length + 1;
      if (total > limit) {
        const errText = translatr('onboarding.v3.step-two', 'JobRolesLimit', {
          limit: limit
        });
        setError(errText);
        return false;
      }
      return true;
    }
    return true;
  };

  const addItem = () => {
    const newValue = { ...value };
    if (bia) {
      newValue.proficiencyLevel = level.name;
    }

    if (checkLimit()) {
      if (list[`_${value.topic_id}`]) {
        const errText = translatr('onboarding.v3.step-two', 'SameJobRoleCannotBeAddedTwice');
        setError(errText);
      } else {
        setList(prev => ({
          ...prev,
          [`_${value.topic_id}`]: newValue
        }));
      }
      setTopicId(value.topic_id);
      setLevel(levels[0].value);
      setValue(null);
    }
  };

  const validationFn = async (isLastStep, currentStep) => {
    if (Object.keys(list).length === 0) {
      const errText = translatr('onboarding.v3.step-two', 'PleaseSelectAtLeastOneJobRole');
      setError(errText);
      return false;
    }

    setError('');
    const currentUserObj = {
      id: user.get('id')
    };

    const newList = Object.values(list).map(({ proficiencyLevel, ...rest }) => ({
      ...rest,
      proficiency_level: proficiencyLevel
    }));

    const respLearn = await postLearningDomainTopics(newList, user.get('id'));
    if (!respLearn) return false;
    const respOnboard = await postOnboardingStep(currentUserObj, currentStep, isLastStep);
    if (!respOnboard) return false;
    if (isLastStep) {
      await postOnboardingStep(currentUserObj, 3, isLastStep);

      if (!respOnboard) return false;
    }
    return true;
  };

  return (
    <>
      <Info
        title={translatr('onboarding.common.common', 'WelcomeUser', {
          name: currentUser.get('first_name')
        })}
        description={translatr(
          'onboarding.v3.step-two',
          'SelectLimitJobRolesInTheAreaOfYourInterest',
          { limit: limit }
        )}
      />
      <div className="xl-margin-top">
        <div className="image-min-height">
          <div className="image-wrapper make-center">
            <ProfileImage disableUpload={true} />
          </div>
        </div>
        <form className="ed-input-container margin-x-auto mw-45">
          <div className="justflex align-item-flex-end flex-wrap">
            <div
              className={cx('segement-seperator m-padding-right', {
                'w-50': bia,
                'width-100': !bia
              })}
            >
              <Select
                description={translatr(
                  'onboarding.v3.step-two',
                  'SelectLimitJobRolesInTheAreaOfYourInterest',
                  { limit: limit }
                )}
                key={topicId}
                onChange={onSearchChange}
                required
                items={jobRoles}
                title={translatr('onboarding.v3.step-two', 'SelectJobRole')}
                placeholder={translatr('onboarding.v3.step-two', 'SelectJobRole')}
                isTranslated
                translateDropDownOptions={false}
              />
            </div>
            {bia && (
              <div className="w-30 segement-seperator m-padding-right">
                <Select
                  description={translatr('onboarding.v3.step-two', 'SelectLevelForJobRole')}
                  defaultValue={level}
                  onChange={selectedLevel => setLevel(selectedLevel)}
                  required
                  disabled={!value}
                  items={getAvailableProficiencyLevels()}
                  title={translatr('cds.common.main', 'Level')}
                  isTranslated
                  translateDropDownOptions={false}
                />
              </div>
            )}

            {bia && (
              <div className="w-20 segement-seperator flex-end ">
                <button
                  type="button"
                  className="w-100 ed-btn ed-btn-primary-custom add-button"
                  disabled={!value || level === levels[0].value}
                  onClick={addItem}
                >
                  {translatr('web.common.main', 'Add')}
                </button>
              </div>
            )}
          </div>
          <div className="s-margin-sides s-margin-ends input-error">{error}</div>
          <div className="user-level justflex flex-wrap">
            <SortedTags
              list={Object.values(list)}
              options={levels.slice(1)}
              setList={setList}
              sorted={bia}
              isTranslated
            />
          </div>
          <Navigator validationFn={validationFn} />
        </form>
      </div>
    </>
  );
}

export default errorBoundaryWrapper(StepTwo);

StepTwo.propTypes = {
  Navigator: elementType
};
