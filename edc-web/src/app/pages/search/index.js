import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import {
  CACHED_ADVANCED_FILTERS,
  GLOBAL_SEARCH_PARAM_PATTERN
} from 'centralized-design-system/src/Header/constants';

import { Route, useLocation, useNavigate } from 'react-router-dom';

import PropTypes from 'prop-types';
import SearchWrapper from './SearchWrapper';
import TabBar from 'centralized-design-system/src/TabBar';
import { RootRoutes } from '../../Router';
import capture from '../../../app/utils/datalayer';

import './search.scss';
import Chips from 'centralized-design-system/src/Chips';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import { OPPORTUNITY_TYPE, JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  shouldShowTMMentorshipSearch,
  shouldShowTMProjectSearch,
  shouldShowTMJobVacancySearch,
  shouldShowTMJobRoleSearch
} from '@pages/SearchOpportunities/util';

const Search = ({
  disableUserSearch,
  cardsTabQueryParams,
  isSearchSuggestionsAdminConfigEnabled,
  globalSearchSettings,
  theme
}) => {
  const location = useLocation();

  const [activeLink, setActiveLink] = useState('');
  const [showPublicCards, setShowPublicCards] = useState(true);
  const [searchParams, setSearchParams] = useState(
    new URLSearchParams(location.search.replace(/%26(filters|advancedFilters|skid)/g, '&$1'))
  );

  // Gets current search terms directly from URL to ensure immediate access to latest values
  // Returns both encoded (for links) and decoded (for display) versions
  const getCurrentSearchTerm = () => {
    const cleanedSearch = location.search.replace(/%26(filters|advancedFilters|skid)/g, '&$1');

    return {
      encoded: cleanedSearch.split('&')[0].replace('?q=', ''),
      decoded: new URLSearchParams(cleanedSearch).get('q')?.split(GLOBAL_SEARCH_PARAM_PATTERN)[0]
    };
  };

  const prefix = '/smartsearch';
  /**
   * Reason to keep two different variables for search term
   * Because we are applying decodeURIComponent on search term in search component of CDS we need to pass encoded search term and
   * to maintain uniformity of search term across different tabs.Passing encoded search term to link and decoded for display use
   */
  let { encoded: encodedSearchTerm, decoded: decodedSearchTerm } = getCurrentSearchTerm();

  const skid = searchParams.get('skid');
  const advancedFilters = JSON.parse(
    searchParams.get('advancedFilters') || window.sessionStorage.getItem(CACHED_ADVANCED_FILTERS)
  );
  const queryParamsForContentTab = skid ? window.location.search : `?q=${encodedSearchTerm}`;
  // Remove skid parameter only when skid exists and we're not on the Content Tab
  // This preserves skid for card search while cleaning it for other tab navigation
  if (skid && activeLink !== '/smartsearch/card') {
    encodedSearchTerm = encodedSearchTerm?.replace(/%26skid.*$/, '');
  }
  const [isNewSearchUIDesignEnable, setIsNewSearchUIDesignEnable] = useState(false);
  const [peopleFilters, setPeopleFilters] = useState({});
  const navigate = useNavigate();

  const {
    isContentSearchEnabled,
    isPeopleSearchEnabled,
    isChannelSearchEnabled,
    isTeamSearchEnabled
  } = globalSearchSettings;

  const tabs = [
    isContentSearchEnabled && {
      name: 'Content',
      label: translatr('web.common.main', 'Content'),
      link: encodedSearchTerm
        ? `${prefix}/card${queryParamsForContentTab}` || `${prefix}?q=${encodedSearchTerm}`
        : `${prefix}/card` || prefix,
      key: 'card'
    },
    isPeopleSearchEnabled &&
      !disableUserSearch && {
        name: 'People',
        label: translatr('web.common.main', 'People'),
        link: encodedSearchTerm ? `${prefix}/user?q=${encodedSearchTerm}` : `${prefix}/user`,
        key: 'user'
      },
    isChannelSearchEnabled && {
      name: 'Channels',
      label: translatr('web.common.main', 'Channels'),
      link: encodedSearchTerm ? `${prefix}/channel?q=${encodedSearchTerm}` : `${prefix}/channel`,
      key: 'channel'
    },
    isTeamSearchEnabled && {
      name: 'Groups',
      label: translatr('web.common.main', 'Groups'),
      link: encodedSearchTerm ? `${prefix}/group?q=${encodedSearchTerm}` : `${prefix}/group`,
      key: 'group'
    },
    shouldShowTMJobRoleSearch() && {
      name: 'Roles',
      label: omp('tm_tm_job_roles'),
      link: encodedSearchTerm
        ? `${prefix}/${JOB_TYPE.ROLE}?q=${encodedSearchTerm}`
        : `${prefix}/${JOB_TYPE.ROLE}`,
      key: JOB_TYPE.ROLE
    },
    shouldShowTMJobVacancySearch() && {
      name: 'Vacancies',
      label: omp('tm_tm_job_vacancies'),
      link: encodedSearchTerm
        ? `${prefix}/${JOB_TYPE.VACANCY}?q=${encodedSearchTerm}`
        : `${prefix}/${JOB_TYPE.VACANCY}`,
      key: JOB_TYPE.VACANCY
    },
    shouldShowTMProjectSearch() && {
      name: 'Projects',
      label: omp('tm_tm_projects'),
      link: encodedSearchTerm ? `${prefix}/project?q=${encodedSearchTerm}` : `${prefix}/project`,
      key: OPPORTUNITY_TYPE.PROJECT
    },
    shouldShowTMMentorshipSearch() && {
      name: 'Mentors',
      label: omp('tm_tm_mentors'),
      link: encodedSearchTerm
        ? `${prefix}/mentorship?q=${encodedSearchTerm}`
        : `${prefix}/mentorship`,
      key: OPPORTUNITY_TYPE.MENTORSHIP
    }
  ].filter(Boolean);

  const getActiveTabIndexFromURL = () => {
    const currentPath = location.pathname;
    const tabIndex = tabs.findIndex(tab => currentPath.includes(tab.key));
    return tabIndex !== -1 ? tabIndex : 0;
  };

  const [activeTabIndex, setActiveTabIndex] = useState(getActiveTabIndexFromURL());

  useEffect(() => {
    capture('Searched', { Query: encodedSearchTerm });
  }, []);

  useEffect(() => {
    const shouldRedirectToContentTab =
      location.pathname != '/smartsearch/card' &&
      isSearchSuggestionsAdminConfigEnabled &&
      location.search.includes('skid');

    if (shouldRedirectToContentTab) {
      // This workaround addresses an issue, if for eg: the user is on the "Channels" tab and clicks a skill,
      // It shud redirect to the Content tab
      document.querySelector('.search-tabs a')?.click();
    }
  }, [cardsTabQueryParams]);

  useEffect(() => {
    // This will trigger re-render whenever search params change.
    // Skip searchParams update when skill ID exists to avoid duplicate re-renders as that's handled in the above effect.
    if (!skid) {
      setSearchParams(
        new URLSearchParams(location.search.replace(/%26(filters|advancedFilters|skid)/g, '&$1'))
      );
    }
  }, [location]);

  useEffect(() => {
    if (theme === ThemeId.PLARE) {
      setIsNewSearchUIDesignEnable(true);
    } else {
      setIsNewSearchUIDesignEnable(false);
    }
  }, [theme]);

  useEffect(() => {
    setActiveTabIndex(getActiveTabIndexFromURL());
  }, [location]);

  const getActiveLinkUrl = url => {
    setActiveLink(url);
  };

  return (
    <div className="main-container smart-search-container">
      <div className="search-container ed-ui ">
        <h1 className="sr-only">{translatr('web.search.main', 'SearchResults')}</h1>
        {isNewSearchUIDesignEnable ? (
          <Chips
            chips={tabs}
            defaultSelectedChips={[activeTabIndex]}
            classNames={'bg-color-white'}
            onChipClick={selectedChips => {
              const index = selectedChips[0];
              const selectedTab = tabs[index];
              if (selectedTab?.link) {
                navigate(selectedTab.link);
              }
            }}
          />
        ) : (
          <TabBar
            tabs={tabs}
            className="block search-tabs --ed-bg-color-1 search-keyboard-focus-tabs"
            activeLink={activeLink}
            handleAriaSelected={
              window.location.pathname == '/smartsearch' ? index => index == 0 : null
            }
          />
        )}
        <RootRoutes>
          <Route
            key={`router-initial-tab-route`}
            path={prefix}
            element={
              <SearchWrapper
                key={`router-initial-tab-route`}
                getActiveLinkUrl={getActiveLinkUrl}
                activeTab={`${tabs[0].key}`}
                searchTerm={decodedSearchTerm}
                advancedFilters={advancedFilters}
                tabName={tabs[0].name}
                tabLabel={tabs[0].label}
                showPublicCards={showPublicCards}
                setShowPublicCards={setShowPublicCards}
                isNewSearchConfigEnable={isNewSearchUIDesignEnable}
              />
            }
          />
          {tabs.map(tab => {
            const path = `${prefix}/${tab.key}`;
            const key = `router-${tab.key}`;
            return (
              <Route
                key={key}
                path={path}
                element={
                  <SearchWrapper
                    key={key}
                    getActiveLinkUrl={getActiveLinkUrl}
                    activeTab={tab.key}
                    searchTerm={decodedSearchTerm}
                    advancedFilters={advancedFilters}
                    tabName={tab.name}
                    tabLabel={tab.label}
                    showPublicCards={showPublicCards}
                    setShowPublicCards={setShowPublicCards}
                    isNewSearchConfigEnable={isNewSearchUIDesignEnable}
                    peopleFilters={peopleFilters}
                    setPeopleFilters={setPeopleFilters}
                  />
                }
              />
            );
          })}
        </RootRoutes>
      </div>
    </div>
  );
};

Search.propTypes = {
  disableUserSearch: PropTypes.bool,
  cardsTabQueryParams: PropTypes.string,
  isSearchSuggestionsEnabled: PropTypes.bool,
  theme: PropTypes.string,
  isSearchSuggestionsAdminConfigEnabled: PropTypes.bool,
  globalSearchSettings: PropTypes.shape({
    isContentSearchEnabled: PropTypes.bool,
    isPeopleSearchEnabled: PropTypes.bool,
    isChannelSearchEnabled: PropTypes.bool,
    isTeamSearchEnabled: PropTypes.bool
  })
};

const mapStoreStateToProps = ({ team, currentUser, search, theme }) => {
  return {
    disableUserSearch: team.get('config')?.disable_user_search,
    cardsTabQueryParams: search?.get('card')?.cardsTabQueryParams,
    isSearchSuggestionsAdminConfigEnabled: currentUser.get('isSearchSuggestionEnabled'),
    globalSearchSettings: currentUser.get('globalSearchSettings').toJS(),
    theme: theme?.get('themeId')
  };
};

export default connect(mapStoreStateToProps)(Search);
