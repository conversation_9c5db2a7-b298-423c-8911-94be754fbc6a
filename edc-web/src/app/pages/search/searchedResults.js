import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import PropTypes from 'prop-types';
import CardWrapper from '@components/cardStandardization/CardWrapper';
import UserCard from 'centralized-design-system/src/PeopleCard/index';
import Loading from 'centralized-design-system/src/Loading';
import GroupChannelCard from 'centralized-design-system/src/GroupChannelCard/GroupChannelCard';
import { follow, unfollow } from 'edc-web-sdk/requests/users.v2';
import { Permissions } from '../../../app/utils/checkPermissions';
import { connect } from 'react-redux';
import { snackBarOpenClose } from '../../../app/actions/channelsActionsV2';
import SearchSmartCardSkeletonScreen from './components/SkeletonLoaders/SearchSmartCardSkeletonScreen';
import NewSearchSmartCardSkeletonScreen from './components/SkeletonLoaders/SearchSmartCardSkeletonScreen/NewSearchSmartCardSkeletonScreen';
import isLmsProviderEnabledInstance from '@utils/isLmsProviderEnabledInstance';
import getAccessibleCards from 'centralized-design-system/src/Utils/getAccessibleCards';
import uniqBy from 'lodash/uniqBy';
import { shouldShowNewSearchUI } from './functions/utils';

const TABS_TYPES = {
  CARD: 'card',
  CHANNEL: 'channel',
  GROUP: 'group',
  USER: 'user'
};

const searchedResults = ({
  dispatch,
  data,
  activeTab,
  isSearchLoading,
  isLoadingMore,
  showMore,
  currentUserId,
  showLockIcon,
  hasMoreData,
  isNewSearchConfigEnable,
  theme
}) => {
  const navigate = useNavigate();
  const [toggle, setToggle] = useState({});
  const [accessibleCards, setAccessibleCards] = useState([]);
  const allowFollow = !Permissions.has('DISABLE_USER_FOLLOW');
  const uid = currentUserId;
  const isActiveTabCard = activeTab === TABS_TYPES.CARD;

  useEffect(() => {
    if (data?.cards) {
      const filteredCards = data.cards.filter(getAccessibleCards);
      setAccessibleCards(filteredCards);
    }
  }, [data]);

  const userClickHandler = user => {
    navigate(`/${user.handle}`);
  };
  const goToStandAloneView = transitionUrl => {
    navigate(transitionUrl);
  };
  const handleToggle = (id, isFollowing) => {
    if (isFollowing) {
      unfollow(id)
        .then(() => {
          setToggle({ ...toggle, [id]: true });
        })
        .catch(err => {
          setToggle({ ...toggle, [id]: false });
          console.error(`Error in Team.handleToggle.unfollow func ${err}`);
        });
    } else {
      follow(id)
        .then(() => {
          setToggle({ ...toggle, [id]: true });
        })
        .catch(err => {
          setToggle({ ...toggle, [id]: false });
          console.error(`Error in Team.handleToggle.follow func ${err}`);
        });
    }
  };

  const snackbarErrorHandler = error => dispatch(snackBarOpenClose(error, 3000));

  const removeCardFromList = cardId => {
    const updatedCards = accessibleCards.filter(card => card.id !== cardId);
    setAccessibleCards(updatedCards);
  };
  let activeComponent;
  switch (activeTab) {
    case TABS_TYPES.CARD:
      activeComponent =
        data.cards?.length > 0 &&
        accessibleCards.map(card => {
          return (
            <CardWrapper
              key={card.id}
              card={card.assignable || card}
              dueAt={card.dueAt || card.assignment?.dueAt}
              assignedAt={card.createdAt}
              assignedBy={card.assignor?.name}
              assignorId={card.assignor?.id}
              startDate={card.startDate || card.assignment?.startDate}
              isAssignmentPage={true}
              removeCardFromList={removeCardFromList}
              {...card}
            />
          );
        });
      break;

    case TABS_TYPES.CHANNEL:
      activeComponent =
        data.channels?.length > 0 &&
        data.channels.map(channel => {
          return (
            <GroupChannelCard
              key={channel.id}
              channel={channel}
              groupOrChannelId={channel.id}
              tagName={translatr('web.search.main', 'Channel')}
              cardImage={channel.profileImageUrl || channel.bannerImageUrls.medium_url}
              title={channel.label}
              userCount={channel.followersCount}
              imageAltText={
                channel.mobileImageAltText || translatr('web.search.main', 'ThisIsAChannelImage')
              }
              transitionUrl={`/channel/${channel.slug}`}
              isPrivate={channel.isPrivate}
              showLockIcon={showLockIcon}
              isChannel={true}
              isUserIsPartOfGroupOrChannel={channel.isFollowing}
              channelTopics={channel.topics || []}
              goToStandAloneView={goToStandAloneView}
              allowFollow={channel.allowFollow}
              snackbarErrorHandler={error => dispatch(snackBarOpenClose(tr(error.message), 3000))}
            />
          );
        });
      break;

    case TABS_TYPES.GROUP:
      activeComponent =
        data.groups?.length > 0 &&
        data.groups.map(group => {
          const isPartOfGroup =
            group.isMember || group.isTeamAdmin || group.isTeamSubAdmin || group.isTeamModerator;
          const isGroupAccessible = isPartOfGroup || !group.isPrivate;
          return (
            <GroupChannelCard
              key={group.id}
              groupOrChannelId={group.id}
              group={group}
              tagName={translatr('web.search.main', 'Group')}
              cardImage={group.imageUrls.medium}
              title={group.name}
              userCount={group.membersCount}
              imageAltText={group.imageAltText || translatr('web.search.main', 'ThisIsAGroupImage')}
              transitionUrl={`/teams/${group.slug}`}
              isPrivate={group.isPrivate}
              showLockIcon={showLockIcon}
              goToStandAloneView={goToStandAloneView}
              isUserIsPartOfGroupOrChannel={isPartOfGroup}
              isInvitationPending={group.isPending}
              isMandatory={group.isMandatory}
              isEveryoneTeam={group.isEveryoneTeam}
              isGroupAccessible={isGroupAccessible}
              snackbarErrorHandler={snackbarErrorHandler}
            />
          );
        });
      break;

    case TABS_TYPES.USER:
      const uniqueUsers = uniqBy(data.users, 'id');
      activeComponent =
        uniqueUsers?.length > 0 &&
        uniqueUsers.map(user => {
          return (
            <UserCard
              key={user.id}
              user={user}
              cb={userClickHandler}
              toggleFollow={handleToggle}
              isComplete={toggle[user.id]}
              allowFollow={allowFollow && uid !== user.id}
              currentUserId={uid}
            />
          );
        });
      break;
    default:
      activeComponent = null;
      break;
  }

  const cardsPresentButInAccessibleToUser =
    isLmsProviderEnabledInstance() &&
    isActiveTabCard &&
    data.cards?.length === 0 &&
    data.total_items > 0;

  const shouldShowNewSearch = shouldShowNewSearchUI(isNewSearchConfigEnable, activeTab, theme);

  const resultsHeading = {
    [TABS_TYPES.CARD]: accessibleCards.length > 0 && translatr('web.common.main', 'AllContent'),
    [TABS_TYPES.CHANNEL]: data.channels?.length > 0 && translatr('web.common.main', 'AllChannels'),
    [TABS_TYPES.GROUP]: data.groups?.length > 0 && translatr('web.common.main', 'AllGroups')
  };

  return (
    <>
      {isSearchLoading ? (
        isActiveTabCard ? (
          <div className={'results-section card-section'}>
            {shouldShowNewSearch ? (
              <NewSearchSmartCardSkeletonScreen />
            ) : (
              <SearchSmartCardSkeletonScreen />
            )}
          </div>
        ) : (
          <div className="search-spinner">
            <Loading />
          </div>
        )
      ) : (
        <>
          <div
            role="tabpanel"
            // This ID matches the aria-controls attribute on the corresponding tab element
            // to create a relationship between the tab and this panel for screen reader
            id={`results-section-${activeTab}-section`}
            className={`results-section ${activeTab}-section `}
          >
            {shouldShowNewSearch && (
              <div className="s-margin-bottom s-margin-sides new-search-heading">
                {resultsHeading[activeTab]}
              </div>
            )}
            {activeComponent}
          </div>

          {cardsPresentButInAccessibleToUser && (
            <span className="display-block font-size-xxxl ed-text-color text-center">
              {hasMoreData()
                ? translatr('web.search.main', 'RefineSearchQueryOrClickOnShowMore')
                : translatr('web.search.main', 'RefineSearchQuery')}
            </span>
          )}

          {hasMoreData() && (
            <div
              className={classNames('make-center', {
                'm-padding-bottom': shouldShowNewSearch
              })}
            >
              {isLoadingMore ? (
                <div className="mt-16">
                  <Loading wide={false} />
                </div>
              ) : (
                <button onClick={showMore} className="ed-btn ed-btn-neutral">
                  {translatr('web.search.main', 'ShowMore')}
                </button>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};

searchedResults.propTypes = {
  data: PropTypes.object,
  activeTab: PropTypes.string,
  isSearchLoading: PropTypes.bool,
  showLockIcon: PropTypes.bool,
  showMore: PropTypes.func,
  currentUserId: PropTypes.number,
  hasMoreData: PropTypes.func,
  isNewSearchConfigEnable: PropTypes.bool
};

const mapStateToProps = ({ currentUser, team, theme }) => {
  return {
    currentUserId: currentUser.get('id'),
    showLockIcon: team?.get('config')?.show_lock_icon,
    theme: theme?.get('themeId')
  };
};

export default connect(mapStateToProps)(searchedResults);
