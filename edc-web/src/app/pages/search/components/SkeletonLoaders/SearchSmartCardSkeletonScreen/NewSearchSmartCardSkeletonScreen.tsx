import React from 'react';

import Stack from 'centralized-design-system/src/Stack';
import TileViewSkeletonLoader from 'centralized-design-system/src/SkeletonAnimations/SmartCardSkeletonLoaders/TileViewSkeletonLoader';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import withBaseColor from 'centralized-design-system/src/SkeletonAnimations/Skeleton/HOC/withBaseColor';
import withDuration from 'centralized-design-system/src/SkeletonAnimations/Skeleton/HOC/withDuration';

import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';

interface SkeletonConfig {
  width: number;
  height: number;
}

const SkeletonWithConfigs = withDuration(withBaseColor(Skeleton, '#E8E8EF'), 2);

const NewSearchCardLoaderContainer: React.FC = () => {
  const { width: screenWidth } = useWindowSize();
  /**
   * 550 - Taken as per the breakpoint in search.scss
   */
  const isViewPortLessThanFiveFiftyPx: boolean = screenWidth < 550;
  const isViewPortLessThanFiveSeventySevenPx: boolean = screenWidth < 577;
  /**
   * This targets viewport between 550 and 654px
   * This is also as per the breakpoint in search.scss
   */
  const isViewPortInBetweenFiveFiftyAndSixFiftyFourPx: boolean =
    !isViewPortLessThanFiveFiftyPx && screenWidth < 655;

  const smartCardArrayCount: number =
    isViewPortInBetweenFiveFiftyAndSixFiftyFourPx || isViewPortLessThanFiveFiftyPx ? 4 : 8;

  const skeletonForLessThanFiveSeventySevenPx = (): JSX.Element => {
    const skeletonConfigs: SkeletonConfig[] = [
      { width: 150, height: 30 },
      { width: 200, height: 30 },
      { width: 150, height: 30 }
    ];

    return (
      <Stack direction="column" className="align-self-start new-search-skeleton">
        {skeletonConfigs.map((config: SkeletonConfig, index: number) => (
          <div key={index}>
            <SkeletonWithConfigs {...config} />
          </div>
        ))}
      </Stack>
    );
  };

  return (
    <>
      {!isViewPortLessThanFiveSeventySevenPx ? (
        <div className="search-skeleton-container l-margin-top">
          <Stack
            className={
              'search-skeleton-container__filters-wrapper height-100 flex-space-between new-search-skeleton'
            }
          >
            <div className="flex-1">
              <Stack direction="column" className="align-self-start">
                <div className="search-skeleton-container__filter-dropdowns">
                  <Stack>
                    <SkeletonWithConfigs
                      width={100}
                      height={30}
                      count={4}
                      containerClassName={'flex justify-content-space-around'}
                    />
                  </Stack>
                </div>
                <div>
                  <SkeletonWithConfigs width={250} height={30} />
                </div>
              </Stack>
            </div>
            <Stack
              direction="column"
              className={'search-skeleton-container__filters flex-1 flex-space-between align-items-end'}
            >
              <SkeletonWithConfigs width={150} height={30} />
            </Stack>
          </Stack>
        </div>
      ) : (
        skeletonForLessThanFiveSeventySevenPx()
      )}

      <Stack className="justify-center flex-wrap l-margin-top">
        {Array(smartCardArrayCount)
          .fill(null)
          .map((_, idx: number) => {
            return <TileViewSkeletonLoader key={idx} idx={idx} />;
          })}
      </Stack>
    </>
  );
};

export default NewSearchCardLoaderContainer;