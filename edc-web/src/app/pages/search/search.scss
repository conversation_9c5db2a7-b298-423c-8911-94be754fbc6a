@import '~centralized-design-system/src/Styles/_variables.scss';

.filters {
  padding: var(--ed-spacing-base) 0;
  margin-top: -1rem;
  order: 1;

  & span {
    margin-left: 1.5rem;

    & span {
      margin-left: 0.31rem;
    }
  }
  .preferred-language-checkbox-container {
    & span {
      margin-left: var(--ed-spacing-2xs);
      color: var(--ed-black);
    }
  }
}

.dp-span-margin-reset {
  .form-control__button-content-wrapper span {
    margin: 0;
  }
}

.text-lowercase {
  text-transform: lowercase;
}

.ed-ui {
  .top-panel {
    min-height: rem-calc(41);
  }

  .view-type-toggle {
    margin: 0 0.5rem;

    a {
      font-size: var(--ed-font-size-xl);
      margin-left: var(--ed-spacing-2xs);
      color: var(--ed-text-color-supporting);

      &.active {
        color: var(--ed-header-create-button-bg-color);
      }
    }
  }

  // Overwrite default styles
  @include not-mobile() {
    .swim-wrapper .swim-lanes .header-sub-menu_wrapper a {
      margin-right: 0;
    }
  }

  .header-sub-menu_wrapper {
    display: inline;
    font-size: var(--ed-font-size-base);

    @include not-mobile() {
      position: absolute;
      top: 1.5rem;
      right: 0;
      display: flex;
      flex-direction: column;
      z-index: 101; // Some card items have really large z-index values
    }
  }
  .filters-opt-sec {
    margin-left: auto;
  }
}

.view-type-toggle [class*='icon-menu'] {
  font-size: rem-calc(22);
}

.search-container.ed-ui {
  width: 100%;

  .new-search-result-wrapper {
    background-color: var(--ed-white);
    box-shadow: 0 rem-calc(2) rem-calc(4) 0 rgba(0, 0, 0, 0.16);

    .card-std-tile {
      border: var(--ed-border-size-sm) solid var(--ed-gray-2);
      box-shadow: none;
    }

    .new-search-heading {
      font-size: var(--ed-font-size-lg);
      color: var(--ed-gray-7);
      font-weight: var(--ed-font-weight-bold);
      line-height: var(--ed-line-height-sm);
    }

    .result-info {
      gap: var(--ed-spacing-base);

      .filter-options {
        margin-top: var(--ed-spacing-base);

        .new-search-filters-wrapper {
          width: rem-calc(1168);
          gap: var(--ed-spacing-2xs);

          .new-search-preferred-language-checkbox-container {
            label {
              gap: var(--ed-spacing-2xs);

              input#preferred-language--checkbox {
                width: rem-calc(24);
                height: rem-calc(24);
                margin-right: 0;
              }

              .ed-checkbox-label {
                margin-left: 0;
                font-size: var(--ed-font-size-base);
                font-weight: var(--ed-font-weight-normal);
                line-height: var(--ed-line-height-sm);
                color: var(--ed-neutral-1);
              }
            }
          }

          i.search-filter-icon {
            color: var(--ed-tag-icon-color);
            font-size: var(--ed-font-size-lg);
          }

          .filter-btn-label {
            font-size: var(--ed-font-size-base);
            font-weight: var(--ed-font-weight-bold);
            line-height: var(--ed-line-height-sm);
            color: var(--ed-gray-6);
            margin-left: 0;
            padding-right: var(--ed-spacing-2xs);
          }

          .filter-with-checkbox {
            .ed-dropdown {
              .dropdown-btn {
                display: flex;
                justify-content: center;
                border: var(--ed-border-size-sm) solid var(--ed-gray-4);
                box-shadow: 0 rem-calc(1) rem-calc(2) rem-calc(0) rgba(0, 0, 0, 0.04);
                background: var(--ed-white);
                border-radius: var(--ed-border-radius-md);
                height: rem-calc(40);
                padding: 0 var(--ed-spacing-2xs);

                //Remove this when the dropdown btn is replaced with actual button component
                &:hover {
                  background: var(--ed-button-secondary-ghost-hover-bg-color);
                }
              }

              .dropdown-content {
                box-shadow: var(--ed-shadow-base);
                right: auto;
                width: rem-calc(304);
                height: auto;
                max-width: rem-calc(304);

                .content-container {
                  padding: var(--ed-spacing-base);
                  gap: var(--ed-spacing-2xs);

                  .dropdown-header {
                    margin-bottom: var(--ed-spacing-2xs);

                    span {
                      margin-left: 0;
                      color: var(--ed-header-text-color);
                      font-size: var(--ed-font-size-sm);
                      font-weight: var(--ed-font-weight-black);
                    }
                    button {
                      font-size: var(--ed-font-size-lg);
                      color: var(--ed-tag-icon-color);
                      cursor: pointer;
                    }
                  }
                  .ed-input-container {
                    .input-group {
                      .input-field {
                        min-height: rem-calc(32);
                      }
                    }
                  }

                  .checkbox-container {
                    max-height: 12.5rem;

                    .ed-checkbox-label {
                      font-size: var(--ed-font-size-sm);
                      font-weight: var(--ed-font-weight-normal);
                    }
                  }

                  .button-container {
                    border-top: none;
                    padding: 0;
                    margin-top: 0;
                  }
                }
              }
            }
          }
        }
      }
      .new-search-divider {
        width: rem-calc(1145);
        padding: var(--ed-spacing-4xs) 0;
        gap: var(--ed-spacing-3xs);
        margin: 0;
      }

      .sortby-heading {
        font-size: var(--ed-font-size-sm);
        font-weight: var(--ed-font-weight-bold);
        line-height: var(--ed-line-height-sm);
        color: var(--ed-gray-5);
        margin-bottom: var(--ed-spacing-2xs);
      }

      .sortby-input {
        .ed-input-container {
          .ed-select {
            width: rem-calc(160);
            height: rem-calc(40);
            border-radius: var(--ed-border-radius-md);
            border: var(--ed-border-size-sm) solid var(--ed-gray-3);
          }
        }
      }

      .new-search-selected-filters {
        .ed-tag-container {
          height: rem-calc(26);
          padding: rem-calc(1) var(--ed-spacing-sm);

          .tag-name {
            display: flex;
            align-items: center;
            font-size: var(--ed-tag-font-size);
            font-weight: var(--ed-tag-font-weight);
            line-height: var(--ed-line-height-sm);
            height: rem-calc(24);
            color: var(--ed-mui-darkestgrey);
          }

          .tag-close-icon {
            i {
              color: var(--ed-gray-6);
            }
          }
        }

        .topic-tags {
          position: relative;
          gap: var(--ed-spacing-2xs);
          max-height: rem-calc(75);
          height: auto;

          .applied-filters-label {
            font-weight: var(--ed-font-weight-normal);
            color: var(--ed-gray-6);
            line-height: var(--ed-line-height-sm);
            height: 1.625rem;
          }

          .xmoreBtnWrapper {
            gap: var(--ed-spacing-2xs);
            width: auto;
          }
        }
      }

      &.channel-result-info,
      &.group-result-info {
        margin: var(--ed-spacing-base) var(--ed-spacing-base);
      }
    }

    .channel-section,
    .group-section {
      .group-channel-card {
        border: var(--ed-border-size-sm) solid var(--ed-gray-2);
        box-shadow: none;
      }
    }
  }

  & .swim-wrapper {
    & .swim-lanes {
      justify-content: flex-start;
    }
  }

  & .ed-tag-container {
    padding: 0.2rem 0.5rem 0.2rem 1rem;

    & span {
      margin-top: -0.375rem;
      position: relative;
      top: 0.188rem;
    }
  }
  .search-spinner {
    .spinner-container {
      margin-top: var(--ed-spacing-base);
    }
  }
}

.main-container.row {
  max-width: none;
  padding-right: 2.5rem;
  padding-left: 2.5rem;
}

.results-section {
  margin: 0 auto;
  &.promoted-content {
    .ed-carousel-container {
      .ed-carousel-wrapper {
        .ed-carousel {
          .carousel-item {
            scroll-snap-align: start;
            .card-std-tile {
              &.ed-ui {
                position: static;
              }
            }
          }
        }
      }

      &.right-scroll,
      &.left-scroll {
        .scroll-btn {
          z-index: 3;

          &.right {
            right: 0;
          }

          &.left {
            left: 0;
          }
        }
      }
    }
    .promoted-content-heading {
      margin: 0;
      font-weight: var(--ed-font-weight-bold);
      font-size: inherit;
      line-height: var(--ed-line-height-sm);
      display: inline; // Make the heading behave like an inline element
    }
  }

  & .ed-user-default {
    display: inline-block;
    margin: 0 rem-calc(6) rem-calc(16);
  }

  .group-channel-card-link {
    display: inline-block;
    margin: 0 rem-calc(6) rem-calc(16);
  }

  &.card-section {
    width: rem-calc(1170);
  }
}

.smart-search-container {
  padding: 0 0.9375rem;
  max-width: 76.875rem;
  margin: auto;

  .search-skeleton-container {
    height: rem-calc(135);

    &__filter-dropdowns {
      width: 65%;
    }
  }

  & .result-info {
    margin: var(--ed-spacing-base) auto;

    & .results-info-section {
      font-size: var(--ed-font-size-sm);
    }

    & .filters {
      padding: 0rem;
      margin-left: auto !important;

      span.icon-new-filter {
        &:hover {
          color: var(--ed-state-active-color);
        }
      }
    }

    & ~ .justflex {
      align-items: center;
      margin-bottom: var(--ed-spacing-base);
    }

    &.card-result-info,
    &.card-results-sec {
      width: rem-calc(1145);
    }
  }

  & .ed-ui {
    & .tab-bar.block {
      & .tabs .tab {
        min-width: 6.25rem;
        text-align: center;
        margin-right: 0;
        margin-left: 0;
      }
    }
  }

  button {
    span.icon-clear-filter {
      margin-left: var(--ed-spacing-2xs);
      color: var(--ed-text-color-supporting);
      font-size: rem-calc(22);

      &:hover {
        color: var(--ed-state-active-color);
      }
    }
  }

  & .ed-dialog-modal.search_modal {
    & .ed-dialog-modal-wrapper {
      overflow: hidden;

      & .content {
        & .ed-dialog-modal-content {
          display: flex;
          flex-wrap: wrap;
          gap: rem-calc(50);
          & .list-container {
            margin-left: 0rem;
            max-height: 17.8rem;
            overflow-y: auto;
            & .ed-checkbox-label {
              margin-left: 0.3rem;
            }
          }
          .date-range-label {
            h3 {
              color: var(--ed-text-color-primary);
            }
            .date-range-placeholder {
              margin-left: 0;
            }
          }
          .preferred-language-checkbox-modal {
            padding-top: rem-calc(42);
          }
        }
      }
    }
  }
  & .ed-dialog-modal-content {
    & .year-filters {
      display: grid;
      grid-template-columns: rem-calc(196) rem-calc(196);
      grid-gap: rem-calc(50);

      & .ed-input-container {
        margin: 0;
      }
    }

    & .options-with-filter-wrapper {
      position: relative;

      & .ed-input-container {
        & .input-group {
          & .input-field-icon {
            border-left: 0.063rem solid var(--ed-border-color) !important;
          }
        }
      }

      & .label-heading-black {
        padding: 0rem;
      }

      & .list-container {
        & li {
          & label {
            margin-bottom: 0.6rem;
            align-items: flex-start;
            word-break: break-word;

            .ed-checkbox-label {
              width: rem-calc(141);
            }
          }
        }

        & ~ button {
          color: var(--ed-text-color-supporting);
        }
      }

      & ~ button {
        display: none;
      }

      & .optionlist-skeleton-container {
        span {
          margin-left: 0;
        }
      }
    }
  }

  .make-center .ed-btn-neutral {
    background-color: var(--ed-white);
    margin-top: 1.125rem;

    &:hover {
      background: var(--ed-input-hover-bg-color);
    }
  }

  .search-filters-sec {
    margin: var(--ed-spacing-base) auto;

    &.card-search-filter {
      width: rem-calc(1156);
    }
  }

  .new-search-result-wrapper {
    .search-filters-sec {
      margin: 0;
    }

    .new-search-skeleton {
      padding-top: 0.5rem;
      .stack.large {
        gap: 4rem;
      }

      .search-skeleton-container__filters {
        justify-content: flex-end;
        align-items: flex-end;
      }

      .search-skeleton-container__filter-dropdowns {
        width: 85% !important;
      }
    }
  }
}

// Design is coming from CDS so i have added css here for the formating
.filters {
  & .filter-right-bar {
    & h6 {
      font-weight: var(--ed-font-weight-normal);
    }

    & .ed-dropdown {
      margin: 0;
    }

    & ~ button {
      & .justflex {
        margin: 0;
      }
    }

    & .dropdown-content {
      max-width: 16rem;
      min-width: 16rem;

      & .checkbox-container {
        align-items: flex-start;

        & .checkbox {
          & .ed-checkbox-label {
            display: block;
            max-width: 88%;
            margin-left: var(--ed-spacing-2xs);
          }
        }
      }
    }
  }
}

.all-filters-modal-btn {
  .remove-margin {
    margin-left: rem-calc(0);
  }
}

@media (max-width: $breakpoint-md) {
  .smart-search-container {
    .new-search-result-wrapper {
      .result-info {
        margin: var(--ed-spacing-base) var(--ed-spacing-base) !important;

        &.card-result-info,
        &.channel-result-info,
        &.group-result-info {
          width: rem-calc(928);

          .filter-options {
            .filters-opt-sec {
              width: rem-calc(928);

              .new-search-filters-wrapper {
                width: rem-calc(928);
              }
            }
          }

          .selected-filters {
            .new-search-selected-filters {
              .topic-tags {
                width: rem-calc(928) !important;
                max-height: rem-calc(75);
              }
            }
          }
        }

        .new-search-divider {
          width: rem-calc(928) !important;
        }
      }
      .results-section {
        margin: 0 var(--ed-spacing-base);

        &.card-section {
          width: rem-calc(928);
        }

        .ed-carousel-container {
          &.right-scroll .scroll-btn.right {
            right: -1.25rem;
          }
          &.left-scroll .scroll-btn.left {
            left: -1.25rem;
          }
        }
      }

      .new-search-skeleton {
        .search-skeleton-container__filter-dropdowns {
          width: 100% !important;
        }
      }
    }
  }
}

@media (max-width: $breakpoint-xs) {
  .smart-search-container {
    .search-container {
      .new-search-result-wrapper {
        .result-info {
          display: flex !important;
          gap: var(--ed-spacing-base);
          margin: var(--ed-spacing-base) var(--ed-spacing-base) !important;

          .new-search-divider {
            width: rem-calc(512) !important;
          }

          &.card-result-info,
          &.channel-result-info,
          &.group-result-info {
            width: rem-calc(512);

            .filter-right-bar,
            .preferred-language-checkbox-container {
              display: none;
            }

            .filter-options {
              .filters-opt-sec {
                width: rem-calc(512);

                .new-search-filters-wrapper {
                  width: rem-calc(512);
                }
              }
            }

            .selected-filters {
              .new-search-selected-filters {
                .topic-tags {
                  width: rem-calc(512) !important;
                  max-height: rem-calc(110);
                }
              }
            }
          }

          .result-sortby {
            flex-direction: column;
            gap: var(--ed-spacing-base);

            .sortby-heading {
              justify-content: flex-start;
            }

            .sortby-input {
              width: rem-calc(160);
            }
          }
        }
        .results-section {
          margin: 0 var(--ed-spacing-base) !important;

          &.card-section {
            width: rem-calc(512);
          }

          .ed-carousel-container {
            &.right-scroll .scroll-btn.right {
              right: -1.0625rem;
            }
            &.left-scroll .scroll-btn.left {
              left: -1.0625rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: $breakpoint-xxs) {
  .smart-search-container {
    .search-container {
      .new-search-result-wrapper {
        .result-info {
          .new-search-divider {
            width: rem-calc(260) !important;
          }

          &.card-result-info,
          &.channel-result-info,
          &.group-result-info {
            width: rem-calc(260);

            .filter-options {
              .filters-opt-sec {
                width: rem-calc(260);

                .new-search-filters-wrapper {
                  width: rem-calc(260);
                }
              }
            }

            .selected-filters {
              .new-search-selected-filters {
                .topic-tags {
                  width: rem-calc(260) !important;
                  max-height: rem-calc(110);
                }
              }
            }
          }
        }

        .results-section {
          margin: 0 auto !important;

          &.card-section {
            width: rem-calc(288);
          }
        }

        .new-search-skeleton {
          margin: 0 var(--ed-spacing-base);
        }
      }
    }
  }
}

@media screen and (min-width: 930px) and (max-width: 1199px) {
  .smart-search-container {
    .search-skeleton-container__filter-dropdowns {
      width: 87%;
    }

    .results-section {
      &.card-section {
        width: rem-calc(880);
      }
    }

    .result-info {
      &.card-result-info,
      &.card-results-sec {
        width: rem-calc(850);
      }
    }

    .search-filters-sec {
      &.card-search-filter {
        width: rem-calc(866);
      }
    }
  }
}

@media screen and (max-width: 929px) and (min-width: 655px) {
  .smart-search-container {
    .search-skeleton-container__filter-dropdowns {
      width: 125%;
    }

    .results-section {
      &.card-section {
        width: rem-calc(590);
      }
    }

    .result-info {
      &.card-result-info,
      &.card-results-sec {
        display: block;
        width: rem-calc(569);
      }
    }

    .search-filters-sec {
      &.card-search-filter {
        width: rem-calc(572);
      }
    }
  }
}

@media screen and (max-width: 654px) {
  .smart-search-container {
    .search-skeleton-container {
      width: 168%;
      height: rem-calc(119);
      transform: translateX(rem-calc(-100));
      &__filter-dropdowns {
        width: 110%;
      }
    }

    .results-section {
      &.card-section {
        width: rem-calc(296);
      }
    }

    .result-info {
      &.card-result-info,
      &.card-results-sec {
        width: rem-calc(509);
        display: block;
      }

      .results-info-section {
        white-space: pre-wrap;
      }
    }

    .search-filters-sec {
      &.card-search-filter {
        width: rem-calc(512);
      }
    }
  }
}

@media screen and (max-width: 549px) {
  .smart-search-container {
    .search-skeleton-container {
      width: 100%;
      height: rem-calc(170);
      transform: none;
    }

    .result-info {
      &.card-result-info,
      &.card-results-sec {
        width: rem-calc(266);
      }

      &.channel-result-info,
      &.card-result-info {
        .filter-right-bar,
        .preferred-language-checkbox-container {
          display: none;
        }
      }

      &.card-result-info {
        .filters-opt-sec {
          margin-top: var(--ed-spacing-base);
          .ed-select {
            width: rem-calc(200);
          }
        }
      }
    }

    .search-filters-sec {
      &.card-search-filter {
        width: rem-calc(270);
      }
    }
  }
}

@media screen and (max-width: 1248px) and (min-width: 970px) {
  .smart-search-container {
    .results-section {
      &.user-section {
        width: rem-calc(910);
      }

      &.channel-section,
      &.group-section {
        width: rem-calc(905);
      }
    }

    .result-info {
      &.user-result-info,
      &.channel-result-info,
      &.group-result-info {
        width: rem-calc(886);
      }
    }
  }

  .search-filters-sec {
    &.user-search-filter,
    &.channel-search-filter,
    &.group-result-filter {
      width: rem-calc(890);
    }
  }
}

@media screen and (max-width: 969px) and (min-width: 690px) {
  .smart-search-container {
    .search-skeleton-container {
      width: 45%;
    }

    .results-section {
      &.user-section {
        width: rem-calc(615);
      }

      &.channel-section,
      &.group-section {
        width: rem-calc(610);
      }
    }

    .result-info {
      &.user-result-info,
      &.channel-result-info,
      &.group-result-info {
        width: rem-calc(590);
      }
    }

    .search-filters-sec {
      &.user-search-filter,
      &.channel-search-filter,
      &.group-result-filter {
        width: rem-calc(593);
      }
    }
  }
}

@media screen and (max-width: 689px) {
  .smart-search-container {
    .results-section {
      &.user-section {
        width: rem-calc(315);
      }

      &.channel-section,
      &.group-section {
        width: rem-calc(310);
      }
    }

    .result-info {
      &.user-result-info,
      &.channel-result-info,
      &.group-result-info {
        display: block;
      }
    }
  }
}

@media screen and (max-width: 1240px) {
  .results-section {
    .ed-user-default {
      margin: 0 0.5rem 1rem;
    }
  }
}

@media screen and (max-width: 325px) {
  .search-skeleton-container {
    padding: 0 rem-calc(18) !important;
  }

  .results-section {
    .ed-user-default,
    .group-channel-card-link {
      margin: 0 0.5rem 1rem 0;
    }

    &.user-section {
      width: rem-calc(297);
    }
  }
}

@media screen and (width: 768px) {
  .smart-search-container .ed-dialog-modal-content .options-with-filter-wrapper {
    margin-right: rem-calc(35);
    max-width: rem-calc(230);
  }
}

@media screen and (max-width: 380px) {
  .smart-search-container {
    .ed-dialog-modal-content {
      .options-with-filter-wrapper {
        max-width: rem-calc(310);
        margin-right: 0;
      }
    }
  }
}
