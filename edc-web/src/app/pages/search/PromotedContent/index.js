import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { connect } from 'react-redux';
import CardWrapper from '@components/cardStandardization/CardWrapper';
import Carousel from 'centralized-design-system/src/Carousel';
import classNames from 'classnames';
import { shouldShowNewSearchUI } from '../functions/utils';
import { isRtl } from '@utils/locale';

const PromotedContent = ({
  activeTab,
  data,
  isSearchLoading,
  onNextClick,
  removeCardFromList,
  isNewSearchConfigEnable,
  theme
}) => {
  const isActiveTabCard = activeTab === 'card';

  const totalSlides = Math.ceil(data.length / 4);

  const [currentSlide, setCurrentSlide] = useState(0);

  function renderCards() {
    if (!data?.length) {
      return null;
    }
    return data
      .filter(card => card.isOfficial)
      .map(card => {
        return (
          <li>
            <div
              className="carousel-item no-padding"
              key={card.id}
              data-category="carousel: promoted content"
              data-label={card.title || card.message || 'Unknown'}
            >
              <CardWrapper
                key={card.id}
                isPromotedCard={true}
                card={card.assignable || card}
                dueAt={card.dueAt || card.assignment?.dueAt}
                assignedAt={card.createdAt}
                assignedBy={card.assignor?.name}
                assignorId={card.assignor?.id}
                startDate={card.startDate || card.assignment?.startDate}
                isAssignmentPage={true}
                removeCardFromList={removeCardFromList}
                {...card}
              />
            </div>
          </li>
        );
      });
  }

  const onNextBtnClick = page => {
    if (page === 4) {
      onNextClick();
    }
  };

  const handleNextClick = () => {
    if (currentSlide + 1 === totalSlides) {
      return null;
    }
    setCurrentSlide(c => c + 1);
    onNextBtnClick(currentSlide + 1);
  };

  const handlePrevClick = () => {
    setCurrentSlide(c => c - 1);
  };

  if (isSearchLoading || !isActiveTabCard) {
    return null;
  }
  const shouldShowNewSearch = shouldShowNewSearchUI(isNewSearchConfigEnable, activeTab, theme);
  return (
    <div className={`results-section promoted-content card-section relative`}>
      <div
        className={classNames('m-margin-ends s-margin-sides', {
          'new-search-heading': shouldShowNewSearch
        })}
      >
        <h3 className="promoted-content-heading">
          {translatr('web.search.main', 'PromotedContent')}
        </h3>
      </div>
      <Carousel
        className="s-margin-ends"
        ariaLabelPrefix={`promoted-content (${data.length})`}
        onNextBtnClickCB={isRtl() ? handlePrevClick : handleNextClick}
        onPrevBtnClickCB={isRtl() ? handleNextClick : handlePrevClick}
      >
        {renderCards()}
      </Carousel>
    </div>
  );
};

const mapStateToProps = ({ theme }) => {
  return {
    theme: theme?.get('themeId')
  };
};

PromotedContent.propTypes = {
  activeTab: PropTypes.string,
  data: PropTypes.array,
  isSearchLoading: PropTypes.bool,
  onNextClick: PropTypes.func,
  removeCardFromList: PropTypes.func,
  isNewSearchConfigEnable: PropTypes.bool,
  theme: PropTypes.string
};

export default connect(mapStateToProps)(PromotedContent);
