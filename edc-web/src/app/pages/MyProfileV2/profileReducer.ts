import {
  mapAssessmentAfterSave,
  mapCertificateAfterSave,
  mapBadgeAfterSave,
  mapBadges,
  mapCertifications,
  mapPatentAfterSave,
  mapWorkExperiences,
  mapActivity,
  mapInterestsGroups,
  mapInterestsChannels,
  mapContentList
} from './Overview/dataMapper';
import { ProfileAction, ProfileDataState } from './types';
import {
  mapDeclaredSkills,
  mapDevelopingSkills, mapSkillAfterSave,
  mapSkillsEvaluationChartData
} from './Skills/dataMapper';
import { prepareUpdatedDashboardInfoCopy } from '@pages/MyProfileV2/utils';

export const reducer = (state: ProfileDataState, action: ProfileAction): ProfileDataState => {
  switch (action.type) {
    case 'OVERVIEW_TAB_LOADING': {
      return {
        ...state,
        overview: {
          ...state.overview,
          loading: true,
        }
      };
    }
    case 'GET_OVERVIEW_DATA_SUCCESS': {
      const { profileCompletionData, userProfile, sections, experiences,
        groups, channels, activity, selfView, dashboardInfoCopy, secondaryManagers,
        reporters, manager, content, currentViewedUserId } = action.initialOverviewData;
      const workExperienceList = mapWorkExperiences(experiences?.result);
      const activityList = mapActivity(activity);
      const contentList = mapContentList(content);
      return {
        ...state,
        currentViewedUserId: currentViewedUserId,
        overview: {
          ...state.overview,
          loading: false,
          certifications: mapCertifications(userProfile, sections),
          badges: mapBadges(userProfile, sections),
          experiences: {
            list: workExperienceList,
            visible: sections?.experiences.isVisible,
            private: sections?.experiences.isPrivate,
            canAdd: sections?.experiences.canAdd,
            label: sections?.experiences.label
          },
          interests: {
            groupList: mapInterestsGroups(groups),
            offsetGroups: 0,
            totalGroups: groups?.total,
            groupsVisible: groups?.total > 0 && sections?.groups.isVisible,
            groupsPrivate: sections?.groups.isPrivate,
            channelList: mapInterestsChannels(channels),
            offsetChannels: 0,
            totalChannels: channels?.total,
            channelsVisible: channels?.total > 0 && sections?.channels.isVisible,
            channelsPrivate: sections?.channels?.isPrivate,
            label: sections?.channels.label
          },
          activities: {
            list: activityList,
            offset: 0,
            visible: sections?.activity?.isVisible,
            private: sections?.activity?.isPrivate,
            label: sections?.activity?.label
          },
          content: {
            visible: sections?.content?.isVisible,
            private: sections?.content?.isPrivate,
            label: sections?.content?.label,
            list: contentList,
            total: content?.total
          },
          selfView: selfView,
          completeYourProfile: {
            visible: sections?.completeYourProfile?.isVisible,
            label: sections?.completeYourProfile?.label,
          },
          profileCompletionData: profileCompletionData,
          reloadProfileCompletion: false,
          dashboardInfoCopy,
          secondaryManagers
        },
        userRelations: {
          ...state.userRelations,
          manager,
          reporters,
          secondaryManagers
        }
      };
    }
    case 'GET_OVERVIEW_DATA_ERROR': {
      return {
        ...state,
        overview: {
          ...state.overview,
          loading: false,
          error: true
        }
      }
    }
    case 'GET_COMPLETE_PROFILE_DATA_SUCCESS': {
      return {
        ...state,
        overview: {
          ...state.overview,
          profileCompletionData: action.profileCompletionData,
          reloadProfileCompletion: false
        }
      }
    }
    case 'CHANGED_PRIVATES_OF_SECTION': {
      const { section, isPrivate } = action;
      switch (section) {
        case "channels":
          return {
            ...state,
            overview: {
              ...state.overview,
              interests: {
                ...state.overview.interests,
                channelsPrivate: isPrivate,
              },
              dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.overview.dashboardInfoCopy, ["My Channels"], !isPrivate)
            }
          };
        case "groups":
          return {
            ...state,
            overview: {
              ...state.overview,
              interests: {
                ...state.overview.interests,
                groupsPrivate: isPrivate,
              },
              dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.overview.dashboardInfoCopy, ["My Groups"], !isPrivate)
            }
          };
        case "certifications":
        case "assessments":
        case "patents":
          return {
            ...state,
            overview: {
              ...state.overview,
              certifications: {
                ...state.overview.certifications,
                certificatesPrivate: isPrivate,
                assessmentsPrivate: isPrivate,
                patentsPrivate: isPrivate,
              },
              dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.overview.dashboardInfoCopy, ["My Certificates", "My Assessments"], !isPrivate)
            }
          };

        case "experiences":
          return {
            ...state,
            overview: {
              ...state.overview,
              experiences: {
                ...state.overview.experiences,
                private: isPrivate,
              },
              dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.overview.dashboardInfoCopy, ["My Work Histories"], !isPrivate)
            }
          };

        case "badges":
          return {
            ...state,
            overview: {
              ...state.overview,
              badges: {
                ...state.overview.badges,
                private: isPrivate,
              },
              dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.overview.dashboardInfoCopy, ["My Badges"], !isPrivate)
            }
          };
        case "content":
          return {
            ...state,
            overview: {
              ...state.overview,
              content: {
                ...state.overview.content,
                private: isPrivate,
              },
              dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.overview.dashboardInfoCopy, ["My Content"], !isPrivate)
            }
          };

        default:
          return state;
      }
    }
    case 'WORK_EXPERIENCES_UPDATED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          experiences: {
            ...state.overview.experiences,
            list: mapWorkExperiences(action.payload)
          },
          reloadProfileCompletion: action?.reloadProfileCompletion
        }
      }
    }
    case 'CERTIFICATE_REMOVED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            certificateList: state.overview.certifications.certificateList.filter(certificate => certificate.id !== action.removedCertificateId),
            certificatesTotal: state.overview.certifications.certificatesTotal - 1,
          }
        }
      };
    }
    case 'CERTIFICATE_UPDATED': {
      const { payload, uuid } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            certificateList: state.overview.certifications.certificateList.map(certificate => {
              if(certificate.id === uuid) {
                return mapCertificateAfterSave(payload);
              }
              return certificate
            }),
          }
        }
      };
    }
    case 'CERTIFICATE_CREATED': {
      const { payload } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            certificateList: [
              ...state.overview.certifications.certificateList,
              mapCertificateAfterSave(payload),
            ],
            certificatesTotal: state.overview.certifications.certificatesTotal + 1
          }
        }

      };
    }
    case 'ASSESSMENT_REMOVED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            assessmentList: state.overview.certifications.assessmentList.filter(assessment => assessment.id !== action.removedAssessmentId),
            assessmentsTotal: state.overview.certifications.assessmentsTotal - 1,
          }
        }

      };
    }
    case 'ASSESSMENT_UPDATED': {
      const { payload, uuid } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            assessmentList: state.overview.certifications.assessmentList.map(assessment => {
              if (assessment.id === uuid) {
                return mapAssessmentAfterSave(payload);
              }
              return assessment
            }),
          }
        }
      };
    }
    case 'ASSESSMENT_CREATED': {
      const { payload } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            assessmentList: [
              ...state.overview.certifications.assessmentList,
              mapAssessmentAfterSave(payload),
            ],
            assessmentsTotal: state.overview.certifications.assessmentsTotal + 1
          }
        }
      };
    }
    case 'PATENT_REMOVED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            patentList: state.overview.certifications.patentList.filter(patent => patent.id !== action.removedPatentId),
            patentsTotal: state.overview.certifications.patentsTotal - 1,
          }
        }
      };
    }
    case 'PATENT_UPDATED': {
      const { payload, newPatentFormData, uuid } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            patentList: state.overview.certifications.patentList.map(patent => {
              if (patent.id === uuid) {
                return mapPatentAfterSave(payload, newPatentFormData);
              }
              return patent
            }),
          }
        }
      };
    }
    case 'PATENT_CREATED': {
      const { payload, newPatentFormData } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          certifications: {
            ...state.overview.certifications,
            patentList: [
              ...state.overview.certifications.patentList,
              mapPatentAfterSave(payload, newPatentFormData),
            ],
            patentsTotal: state.overview.certifications.patentsTotal + 1
          }
        }
      };
    }
    case 'BADGE_REMOVED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          badges: {
            ...state.overview.badges,
            list: state.overview.badges.list.filter(badge => badge.id !== action.removedBadgeId),
            total: state.overview.badges.total - 1,
          }
        }
      };
    }
    case 'BADGE_UPDATED': {
      const { payload, uuid } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          badges: {
            ...state.overview.badges,
            list: state.overview.badges.list.map(badge => {
              if (badge.id === uuid) {
                return mapBadgeAfterSave(payload);
              }
              return badge
            }),
          }
        }
      }
    }
    case 'BADGE_CREATED': {
      const { payload } = action;
      return {
        ...state,
        overview: {
          ...state.overview,
          badges: {
            ...state.overview.badges,
            list: [
              ...state.overview.badges.list,
              mapBadgeAfterSave(payload),
            ],
            total: state.overview.badges.total + 1
          }
        }
      };
    }
    case 'MORE_ACTIVITIES_LOADED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          activities: {
            ...state.overview.activities,
            list: [
              ...state.overview.activities.list,
              ...action.newActivities
            ]
          }
        }
      }
    }
    case 'MORE_GROUPS_LOADED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          interests: {
            ...state.overview.interests,
            groupList: [
              ...state.overview.interests.groupList,
              ...action.newGroups
            ]
          }
        }
      }
    }
    case 'MORE_CHANNELS_LOADED': {
      return {
        ...state,
        overview: {
          ...state.overview,
          interests: {
            ...state.overview.interests,
            channelList: [
              ...state.overview.interests.channelList,
              ...action.newChannels
            ]
          }
        }
      }
    }
    case "OPEN_MODAL":
      return {
        ...state,
        overview: {
          ...state.overview,
          modals: {
            open: action.modal,
            action: action.action,
            data: action.data,
          }
        }
      };
    case "CLOSE_MODAL": {
      return {
        ...state,
        overview: {
          ...state.overview,
          modals: {
            open: undefined,
            action: "add"
          }
        }
      };
    }
    case 'SKILLS_TAB_LOADING': {
      return {
        ...state,
        skills: {
          ...state.skills,
          loading: true,
        }
      };
    }
    case 'GET_SKILLS_DATA_SUCCESS': {
      const { userSkills, sections, userProfile, skillsEvaluation, selfView, currentlyViewedUserFullName, dashboardInfoCopy, LXMediaHubConfigValue } = action;

      const developingSkillList = mapDevelopingSkills(userProfile?.learningTopics);
      const declaredSkillList = mapDeclaredSkills(userSkills, LXMediaHubConfigValue);
      return {
        ...state,
        skills: {
          ...state.skills,
          loading: false,
          declaredSkills: {
            list: declaredSkillList,
            isVisible: sections.declaredSkills.isVisible,
            canAdd: sections.declaredSkills.canAdd,
            isPrivate: sections.declaredSkills.isPrivate,
            label: sections.declaredSkills.label
          },
          developingSkills: {
            list: developingSkillList,
            isVisible: sections.developingSkills.isVisible,
            canAdd: sections.developingSkills.canAdd,
            isPrivate: sections.developingSkills.isPrivate,
            label: sections.developingSkills.label
          },
          skillsEvaluation: {
            loading: false,
            chartData: mapSkillsEvaluationChartData(skillsEvaluation),
            isVisible: sections.skillsEvaluation.isVisible,
            isPrivate: sections.skillsEvaluation.isPrivate,
            label: sections.skillsEvaluation.label,
            error: skillsEvaluation?.errorDuringLoadingSkillsAssessments
          },
          selfView: selfView,
          currentlyViewedUserFullName,
          dashboardInfoCopy
        }
      };
    }
    case 'GET_SKILLS_DATA_ERROR': {
      return {
        ...state,
        skills: {
          ...state.skills,
          loading: false,
          error: true
        }
      }
    }
    case "OPEN_SKILL_MODAL":
      return {
        ...state,
        skills: {
          ...state.skills,
          modals: {
            open: action.modal,
            action: action.action,
            data: action.data,
          }
        }
      };
    case "CLOSE_SKILL_MODAL": {
      return {
        ...state,
        skills: {
          ...state.skills,
          modals: {
            open: undefined,
            action: "add"
          }
        }
      };
    }
    case 'SKILL_UPDATED': {
      const { payload, uuid } = action;
      return {
        ...state,
        skills: {
          ...state.skills,
          declaredSkills: {
            ...state.skills.declaredSkills,
            list: state.skills.declaredSkills.list.map(skill => {
              if (skill.id === uuid) {
                return mapSkillAfterSave(payload);
              }
              return skill
            })
          },
        }
      }
    }
    case 'SKILL_CREATED': {
      const { payload } = action;
      return {
        ...state,
        skills: {
          ...state.skills,
          declaredSkills: {
            ...state.skills.declaredSkills,
            list: [
              ...state.skills.declaredSkills.list,
              mapSkillAfterSave(payload),
            ],
          }
        }
      }
    }
    case 'SKILL_REMOVED': {
      return {
        ...state,
        skills: {
          ...state.skills,
          declaredSkills: {
            ...state.skills.declaredSkills,
            list: state.skills.declaredSkills.list.filter(skill => skill.id !== action.removedSkillId)
          }
        }
      };
    }
    case 'SKILL_EVALUATION_FILTERED': {
      return {
        ...state,
        skills: {
          ...state.skills,
          skillsEvaluation: {
            ...state.skills.skillsEvaluation,
            chartData: state.skills.skillsEvaluation.chartData.map(skill => ({
              ...skill,
              show: action.selectedSkills[skill.id]
            }))
          }
        }
      };
    }
    case 'SKILL_EVALUATION_REFRESHING_IN_PROGRESS': {
      return {
        ...state,
        skills: {
          ...state.skills,
          skillsEvaluation: {
            ...state.skills.skillsEvaluation,
            loading: true,
          }
        }
      };
    }
    case 'SKILL_EVALUATION_REFRESHED': {
      return {
        ...state,
        skills: {
          ...state.skills,
          skillsEvaluation: {
            ...state.skills.skillsEvaluation,
            loading: false,
            chartData: mapSkillsEvaluationChartData(action.skillsEvaluation),
          }
        }
      };
    }
    case 'SKILL_EVALUATION_REFRESHED_UNCOMPLETED': {
      return {
        ...state,
        skills: {
          ...state.skills,
          skillsEvaluation: {
            ...state.skills.skillsEvaluation,
            loading: false,
          }
        }
      }
    }
    case 'SKILLS_DEVELOPING_UPDATED': {
      return {
        ...state,
        skills: {
          ...state.skills,
          developingSkills: {
            ...state.skills.developingSkills,
            list: mapDevelopingSkills(action?.payload?.profile?.learningTopics)
          }
        }
      };
    }
    case 'CHANGED_PRIVATES_OF_DECLARED_SKILLS_SECTION': {
      const { isPrivate } = action;
      return {
        ...state,
        skills: {
          ...state.skills,
          declaredSkills: {
            ...state.skills.declaredSkills,
            isPrivate,
          },
          dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.skills.dashboardInfoCopy, ["My Skills"], !isPrivate)
        }
      }
    }
    case 'CHANGED_PRIVATES_OF_DEVELOPING_SKILLS_SECTION': {
      const { isPrivate } = action;
      return {
        ...state,
        skills: {
          ...state.skills,
          developingSkills: {
            ...state.skills.developingSkills,
            isPrivate,
          },
          dashboardInfoCopy: prepareUpdatedDashboardInfoCopy(state.skills.dashboardInfoCopy, ["My Developing Skills"], !isPrivate)
        }
      };
    }
    default:
      return state;
  }
};
