@import '~centralized-design-system/src/Styles/_variables.scss';
@import '~centralized-design-system/src/MUIComponents/styles/partials/colors';

.ed-ui.profile-header {
  min-height: 0;
  .banner {
    max-height: 128px;
    img {
      max-height: 128px;
    }
  }
}
.profile-header {
  min-height: 192px;
  box-shadow: var(--ed-shadow-base);
  border-radius: 0 0 var(--ed-border-radius-lg) var(--ed-border-radius-lg);
  background-color: var(--ed-white);
  height: auto;
  position: relative;

  .avatar {
    min-width: 10.5rem;
    margin-right: -20px;

    @media (max-width: 576px) {
      margin-bottom: -40px !important;
    }

    .ed-avatar {
      position: relative;
      width: 8rem;
      height: 8rem;
      margin: 0 0 0 var(--ed-spacing-base) !important;
      top: calc(-1 * var(--ed-spacing-3xl));
      border: 0.188rem solid var(--ed-white);
      background-color: var(--ed-white);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.header-banner {
  max-height: 128px;
  background-color: var(--ed-neutral-8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--ed-shadow-base);
  border-radius: var(--ed-border-radius-lg) var(--ed-border-radius-lg) 0 0;
}

.profile-summary {
  width: 100%;
  display: flex;
  flex-direction: column;
  @media (min-width: 576px) {
    flex-direction: row;
  }

  .profile-contact-info {
    display: flex;
    align-items: center;
    margin-bottom: var(--ed-spacing-2xs);
    margin-top: var(--ed-spacing-4xs);
  }

  .profile-info {
    flex: 3;
    padding: 0 3rem 0 var(--ed-spacing-base);
    margin-bottom: 0 !important;

    @media (min-width: 576px) {
      padding: 0 3rem 0 var(--ed-spacing-base);
    }

    .profile-info-heading {
      font-weight: var(--ed-font-weight-bold);
      color: var(--ed-gray-6);
    }

    .icon-container {
      margin-bottom: 1.125rem;

      span {
        width: 2rem;
        height: 2rem;
        border-radius: var(--ed-border-radius-circle);
        border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
      }

      a {
        color: var(--ed-gray-6);
      }
    }

    .more-details {
      height: 32px;
      padding: var(--ed-spacing-4xs) 0;
    }

    .more-options {
      cursor: pointer;
      margin-bottom: var(--ed-spacing-xs);
    }

    .user-follow-data {
      margin-top: 0;
      margin-bottom: var(--ed-spacing-2xs);

      i {
        margin: 0 rem-calc(8);
      }
    }

    .ed-btn-neutral {
      color: grey;
    }

    .bold-text {
      font-weight: var(--ed-font-weight-bold);
    }

    .grey-text {
      color: $darkgrey !important;
    }

    .ed-btn {
      font-size: var(--ed-font-size-xs) !important;
      max-width: 240px !important;
      margin-left: 0 !important;
      margin-right: var(--ed-spacing-xs);
    }

    .btn-text {
      overflow: hidden;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 0 !important;
    }

    .icon-oval-fill {
      font-size: 0.2rem;
      margin-bottom: 0 !important;
    }

    .view-details {
      margin-bottom: 14px !important;
    }

    .job-title {
      font-size: var(--ed-font-size-sm);
      line-height: var(--ed-line-height-xs);
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;

      .contact-info {
        margin-bottom: 0 !important;
      }

      .icon-oval-fill {
        font-size: 0.2rem;
      }
    }

    .buttons-container-private {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: var(--ed-spacing-xs);
      margin-bottom: var(--ed-spacing-lg);

      .dropdown-btn {
        .icon-ellipsis-h {
          font-size: var(--ed-font-size-base);
          font-weight: var(--ed-font-weight-bold);
        }
      }

      .ed-dropdown {
        .tab-drpdwn,
        .subtab-dropdown {
          label {
            font-size: var(--ed-font-size-supporting);
            color: var(--ed-text-color-primary);
          }
        }

        .tab-drpdwn {
          position: relative;
        }

        .my-profile-v2_card-container {
          top: 0;
        }

        .dropdown-content {
          max-width: rem-calc(210);
          left: 0;
          right: unset;

          li {
            color: var(--ed-text-color-primary);
            padding-left: var(--ed-spacing-base);
            padding-right: var(--ed-spacing-base);

            &:hover {
              background-color: var(--ed-input-hover-bg-color);
            }
          }
        }
      }

      .ed-btn {
        margin-bottom: var(--ed-spacing-xs) !important;
      }
      @media (max-width: 320px) {
        flex-direction: column;
        align-items: stretch;
        margin-bottom: var(--ed-spacing-xs);

        .ed-btn {
          margin-bottom: var(--ed-spacing-xs);
        }
      }
    }

    .buttons-container-public {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: var(--ed-spacing-xs);
      margin-bottom: var(--ed-spacing-xs);
      .icon-ellipsis-h {
        font-size: 35px !important;
        font-weight: var(--ed-font-weight-bold);
      }
      .tm__mentorship-card-footer-meta {
        width: 30%;
        margin-bottom: var(--ed-spacing-xs);
      }
      .ed-dropdown {
        .tab-drpdwn,
        .subtab-dropdown {
          label {
            font-size: var(--ed-font-size-supporting);
            color: var(--ed-text-color-primary);
          }
        }

        .dropdown-content {
          max-width: rem-calc(210);

          li {
            color: var(--ed-text-color-primary);
            padding-left: var(--ed-spacing-base);
            padding-right: var(--ed-spacing-base);

            &:hover {
              background-color: var(--ed-input-hover-bg-color);
            }
          }
        }
      }
      .mentorship-container {
        margin-left: var(--ed-spacing-xs);
      }

      .ed-btn {
        margin-bottom: var(--ed-spacing-xs) !important;
      }
      @media (max-width: 320px) {
        flex-direction: column;
        align-items: stretch;
        margin-bottom: var(--ed-spacing-xs);

        .ed-btn {
          margin-bottom: var(--ed-spacing-xs);
        }
      }
    }
  }
}

.utility-container {
  flex: 1;
  padding: 0 3rem 0 var(--ed-spacing-base);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .exit-public-profile {
    margin-bottom: var(--ed-spacing-xs);

    .ed-btn-caution-v2 {
      position: absolute;
      top: var(--ed-spacing-base);
      right: var(--ed-spacing-base);
    }
  }

  @media (min-width: 576px) {
    padding: 0 3rem 0 var(--ed-spacing-base);
  }

  .edit-profile {
    cursor: pointer;
    position: absolute;
    top: var(--ed-spacing-base);
    right: var(--ed-spacing-base);
    font-size: var(--ed-font-size-lg);
    color: var(--ed-gray-6);
  }

  .icon-edit-light {
    font-size: 1.4rem;
  }

  .organization-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 250px;
    margin-bottom: var(--ed-spacing-lg);

    &-reports-to {
      display: flex;
      flex-direction: column;
      gap: var(--ed-spacing-4xs);
      p {
        color: var(--ed-gray-7);
        font-size: var(--ed-font-size-2xs) !important;
        line-height: 18px;
      }
      &-name {
        color: var(--ed-gray-7);
        display: flex;
        flex-direction: row;
        line-height: var(--ed-line-height-xs);
        gap: var(--ed-spacing-3xs);
        align-items: center;
        .ed-avatar {
          width: 32px;
          height: 32px;
        }
        &:hover {
          cursor: pointer;
        }
      }
    }

    &-show-organization {
      padding: var(--ed-spacing-4xs) 0;
      height: 32px;
    }

    @media (min-width: 576px) {
      margin-top: 46px;
    }
  }
}
