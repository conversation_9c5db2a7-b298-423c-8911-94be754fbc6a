import React, { useEffect, useRef, useState } from 'react';
import <PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer
} from 'centralized-design-system/src/Modals';
import FocusLock from 'react-focus-lock';
import { translatr } from "centralized-design-system/src/Translatr";
import { getProfileTranscript } from 'edc-web-sdk/requests/transcript';
import { Button } from 'centralized-design-system/src/Buttons';
import Spinner from '@components/common/spinner';
import './GetTranscriptModal.scss';
import FilePreview
  from '@components/FilePreview/FilePreview';
import moment from 'moment/moment';

interface GetTranscriptModalProps {
  onClose: () => void;
}

interface DownloadedTranscript {
  url: string;
  fileName: string;
  generatedTime: string;
}
const DATE_TIME_FORMAT = 'DD MMM YYYY hh:mm A';


const GetTranscriptModal = ({ onClose }: GetTranscriptModalProps) => {
  const [ getTranscriptLoading, setGetTranscriptLoading ] = useState(false);
  const [ downloadedTranscript, setDownloadedTranscript ] = useState<DownloadedTranscript | null>(null);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const formatDate = (date: any) => {
    return date ? moment(date).format(DATE_TIME_FORMAT) : '--';
  };

  const retrieveFileName = (response: any) => {
    let fileName = '';
    const contentDisposition = response.header['content-disposition'];

    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = fileNameMatch[1].replace(/['"]/g, '');
      }
    }
    return fileName;
  };

  const fetchTranscript = async () => {
    setGetTranscriptLoading(true);

    abortControllerRef.current = new AbortController();

    try {
      const response = await getProfileTranscript();
      const currentDateTime = formatDate(Date.now());

      if (response.body instanceof Blob) {
        const url = window.URL.createObjectURL(response.body);
        // Extract filename from Content-Disposition header if available
        const fileName = retrieveFileName(response);
        setDownloadedTranscript({ url, fileName, generatedTime: currentDateTime });
      }
      else if (response.body && typeof response.body === 'object') {
        const { url, fileName } = response.body;
        setDownloadedTranscript({ url, fileName, generatedTime: currentDateTime });
      } else {
        throw new Error('Unexpected response format');
      }
    } catch (err) {
      console.error('Error downloading transcript:', err);
      setError('Failed to download transcript. Please try again later.');
    } finally {
      setGetTranscriptLoading(false);
    }
  };

  const abortDownload = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clean up any created object URLs
    if (downloadedTranscript?.url) {
      window.URL.revokeObjectURL(downloadedTranscript.url);
    }
  };

  useEffect(() => {
    fetchTranscript();
    return () => {
      abortDownload();
    };
  }, []);

  const handleDownload = () => {
    if (downloadedTranscript) {
      const { url, fileName } = downloadedTranscript;

      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleClose = () => {
    abortDownload();
    onClose();
  };

  const loaderComponent = (
    <div className={"loader"}>
      <Spinner />
      <p>{translatr('web.myprofile.main', "TranscriptGenerating")}</p>
    </div>
  );

  return (
    // @ts-ignore
    <Modal size="small" className="getTranscript-modal">
      <FocusLock>
        {/*@ts-ignore*/}
        <ModalHeader
          title={translatr('web.myprofile.main', "GetTranscript")}
          onClose={onClose}
        />
        <ModalContent>
          <div className="getTranscript-content">
            {getTranscriptLoading && loaderComponent}

            {error && (<h1 className={"heading text-center font-normal font-size-xxxl"}>{error}</h1>)}

            {downloadedTranscript && !getTranscriptLoading && (
                  <FilePreview
                    fileName={downloadedTranscript.fileName}
                    description={downloadedTranscript.generatedTime}
                    onDownloadClick={handleDownload}/>
            )}
          </div>
        </ModalContent>
        <ModalFooter>
          <Button color={'secondary'} onClick={handleClose}>
            {translatr('web.myprofile.main', "Close")}
          </Button>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

export default GetTranscriptModal;
