import React, { useState } from 'react';
import FocusLock from 'react-focus-lock';
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, ModalFooter } from 'centralized-design-system/src/Modals/index';
import { translatr } from 'centralized-design-system/src/Translatr';
import { AsyncSearchInput } from 'centralized-design-system/src/Inputs';
import './EditManagerModal.scss';
import { setMyManager } from 'edc-web-sdk/requests/managerDashboard';
import { Manager } from '@pages/MyProfileV2/types';

interface EditManagerModalProps {
  onClose: () => void;
  onSuccessManagerUpdate: (newManager: Manager) => void;
  currentUser: {
    id: string | number;
    [key: string]: any;
  };
  currentManager?: {
    id: number;
    [key: string]: any;
  };
}

const API_EDIT_MANAGER_ERROR = 'User Manager cannot be added as reportee'

const EditManagerModal: React.FC<EditManagerModalProps> = ({ onClose, onSuccessManagerUpdate, currentUser, currentManager }) => {
  const [userSelected, setUserSelected] = useState<Manager | null>(null);
  const [error, setError] = useState<string | null>(null);

  function onSelected(val: any) {
    setUserSelected({
      id: val.value,
      firstName: val.firstName,
      lastName: val.lastName,
      imgUrl: val.avatarimages.medium,
      name: val.name,
      handle: val.handle
    });
  }

  const handleClose = () => {
    onClose();
  };

  const handleSetManager = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (!userSelected) return;

    if (currentUser.id === userSelected.id) {
      setError(translatr('web.common.main', 'YouCanNotAddYourselfAsManager'));
    } else if (currentManager?.id === userSelected.id) {
      setError(translatr('web.common.main', 'ManagerAlreadyAdded'));
    } else {
      setMyManager(userSelected.id)
        .then(() => {
          onSuccessManagerUpdate(userSelected);
          onClose();

          window.dispatchEvent(new CustomEvent('manager-updated', {
            detail: { userId: currentUser.id }
          }));
        })
        .catch((e) => {
          if (e.message === API_EDIT_MANAGER_ERROR) {
            const translatedError = translatr('web.common.main', 'ReporteeCannotBeManagerError');
            setError(translatedError);
          } else {
            setError(e.message || JSON.stringify(e));
          }
        });
    }
  };

  return (
    <Modal size="small" className="edit-manager-modal-wrapper">
      <FocusLock>
        <ModalHeader
          title={translatr('web.myprofile.main', 'EditManager')}
          onClose={handleClose}
        />
        <ModalContent>
          <div className="edit-manager-dropdown s-margin-top s-margin-bottom">
            <AsyncSearchInput
              id="managerSearchInput"
              title={translatr('web.myprofile.main', 'Manager')}
              placeholder={translatr('web.myprofile.main', 'SelectManager')}
              items={[]}
              users={true}
              groups={false}
              channels={false}
              roles={false}
              multiselect={false}
              onChange={(data: any) => onSelected(data)}
              extraPayload={{
                fields: 'id,full_name,handle,external_id,avatarimages,first_name,last_name'
              }}
            />
            <div className="error-text mandatory-error-text-color s-margin-top">
              {error}
            </div>
            <div className="edit-manager-description s-margin-top">
              {translatr('web.myprofile.main', 'ManagerDescription')}
            </div>
          </div>
        </ModalContent>
        <ModalFooter>
          <button className="ed-btn ed-btn-neutral" onClick={handleClose}>
            {translatr('web.common.main', 'Cancel')}
          </button>
          <button
            className="ed-btn ed-btn-primary"
            onClick={handleSetManager}
            disabled={!userSelected}
          >
            {translatr('web.common.main', 'Save')}
          </button>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

export default EditManagerModal;
