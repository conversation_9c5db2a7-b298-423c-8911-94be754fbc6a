import React, {useEffect, useState} from "react";
import Avatar from 'centralized-design-system/src/Avatar';
import Dropdown from 'centralized-design-system/src/Dropdown';
// @ts-ignore
import unescape from "lodash/unescape";
import { translatr } from "centralized-design-system/src/Translatr";
import classNames from "classnames";
import { useMediaQuery } from "@utils/hooks";
import { useNavigate } from "react-router-dom";
import FollowersListModal from "./FollowersListModal";
import DetailsModal from "./DetailsModal";
import ContactInfoModal from "./ContactInfoModal";
import usePrivacy from "../hooks/usePrivacy";
import OrganizationTreeModal from './OrganizationTree/OrganizationTreeModal';
import UserFollowControl from "./UserFollowControl";
import './ProfileHeader.scss';
import EditManagerModal from './EditManagerModal';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import { obtainLabelFromAdminConfiguration, translate, transformMapToObject } from '../utils';
import AddProfileSection from './AddProfileSection';
import MentorshipControl from "./MentorshipControl";
import GrayishTag from '../Components/GrayishTag';
import { Button, ButtonAnchor } from "centralized-design-system/src/Buttons";
import { Manager } from '@pages/MyProfileV2/types';
import GetTranscriptModal
  from './DownloadTranscriptPDDModal/GetTranscriptModal';

interface FollowButtonProps {
  type: 'follower' | 'following';
  count: number;
  onClick: (e: React.MouseEvent<HTMLButtonElement>, type: 'follower' | 'following', count: number) => void;
}

const FollowButton: React.FC<FollowButtonProps> = ({ type, count, onClick }) => {
  const buttonText = type === 'follower' ? 'Followers' : 'Following';

  return (
    <button
      className={classNames(
        'supporting-text-color',
        { pointer: !!count },
        { 'not-allowed': !count }
      )}
      aria-disabled={!count}
      onClick={(e) => onClick(e, type, count)}
    >
      <span className="font-weight-600">{count}</span>
      {translatr('web.myprofile.main', buttonText)}
    </button>
  );
};

interface UserDetails {
  id: string;
  followersCount: number;
  followingCount: number;
  name: string;
  firstName: string;
  lastName: string;
  handle: string;
  jobTitle: string;
  imgUrl: string;
  manager: any;
  location: string;
  workLocationId: string;
  [key: string]: any;
}

interface ProfileHeaderContainerProps {
  currentUser: any;
  dashboardInfo: any;
  isCurrentUser: boolean;
  userDetails: UserDetails;
  dispatch: React.Dispatch<any>;
  countries: any;
  availableLocations: Array<any>;
  isWorkLocation: boolean;
}

interface DropdownOption {
  key: string;
  label: string;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

const ProfileHeaderContainer: React.FC<ProfileHeaderContainerProps> = ({
  currentUser, isCurrentUser, userDetails, dashboardInfo, countries, availableLocations, isWorkLocation
}) => {
  const isShowGetTranscriptVisible = getConfig('download_pdf_transcript') === true;
  const isAllowAddChangeManagerEnabled = getConfig("allow_add_change_manager") === true;
  const showMoreButton = useMediaQuery('(max-width: 662px)') || isShowGetTranscriptVisible;
  const navigate = useNavigate();
  const [modalsState, setModalsState] = useState({
    userFollowListModal: {
      open: false,
      followType: null
    },
    organizationModalOpen: false,
    editManagerModalOpen: false,
    getTranscriptModalOpen: false
  });
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const [ detailsModalOpen, setDetailsModalOpen ] = useState(false);
  const [ contactInfoModalOpen, setContactInfoModalOpen ] = useState(false);
  const [manager, setManager] = useState<Manager>(null);

  const isPublicProfile = !isCurrentUser && isCurrentUser !== currentUser.get('handle');
  const isPreviewEnabled = currentUser.get('id') === userDetails.id;

  const seeMoreDetailsConfigFromAdmin = getConfig("OrgCustomizationConfig")?.web?.overview?.['web/overview/seeMoreDetails'] || {
    visible: !isPublicProfile,
  };
  const contactDetailsConfigFromAdmin = getConfig("OrgCustomizationConfig")?.web?.overview?.['web/overview/contactDetails'] || {
    visible: !isPublicProfile,
  };

  const { isEnabledForPublicProfile } = usePrivacy();

  const openFollow = (e: React.MouseEvent<HTMLButtonElement>, type: 'follower' | 'following', value: number) => {
    e.preventDefault();
    if (value) {
      setModalsState(ms => ({ ...ms, userFollowListModal: { open: true, followType: type }}));
    }
  };

  useEffect(() => {
    if (!userDetails.manager) {
      return;
    }

    const _manager = transformMapToObject(userDetails.manager);
    if (!_manager.handle) {
      return;
    }

    setManager({
      ..._manager,
      imgUrl: _manager.avatarimages?.medium,
    });
    return () => { setManager(null); };
  }, [userDetails.manager]);

  const handlePublicProfile = (e: React.MouseEvent<HTMLButtonElement>) => {
    e?.preventDefault();
    navigate('/@' + userDetails.handle, { replace: true });
  };

  const handleGetTranscript = (e: React.MouseEvent<HTMLButtonElement>) => {
    e?.preventDefault();
    setModalsState(ms => ({ ...ms, getTranscriptModalOpen: true }));
  };

  const handleExitPublicProfile = (e: React.MouseEvent<HTMLButtonElement>) => {
    e?.preventDefault();
    navigate('/me', { replace: true });
  };

  const handleEditProfile = (e: any) => {
    e?.preventDefault();
    navigate('/settings', );
  };

  const handleDropdownClick = () => {
    setDropdownOpen(false);
  };

  const handleMangerUpdate = (userSelected: Manager) => {
    if (userSelected) {
      setManager(userSelected);
    }
  };

  const privateProfileDropdownOptions: DropdownOption[] = [
    ...(!showMoreButton ? [] : [
      {
        key: 'viewPublicProfile',
        label: translatr('web.myprofile.main', 'ViewPublicProfile'),
        onClick: handlePublicProfile
      },
      ...(isShowGetTranscriptVisible ? [{
        key: 'getTranscript',
        label: translatr('web.myprofile.main', 'GetTranscript'),
        onClick: handleGetTranscript
      }] : [])
    ]),
  ];

  const renderDropdownOptions = (options: DropdownOption[]) => options.map(option => (
    <li key={option.key}>
      <button
        onClick={(e) => {
          handleDropdownClick();
          option.onClick && option.onClick(e);
        }}
        className="close-on-click cursor-pointer ed-text-color"
      >
        {option.label}
      </button>
    </li>
  ));

  const viewFeedBackRequests = window?.__ED__?.csxFeedbackPageUrl;

  return (
    <div className="profile-header">
      <div className="profile-summary justflex">
        <div className="avatar">
          <Avatar user={userDetails} blankAlt={true}/>
        </div>
        <div className="profile-info">
          <h1
            id={`name-${currentUser?.get('first_name')}`}
            className="no-margin m-padding-top text-ellipsis font-size-24 ed-text-color profile-info-heading"
          >
            {userDetails.fullName}
          </h1>
          <div className="profile-contact-info">
            <p className="no-margin font-normal job-title supporting-text-color">
              {userDetails.jobTitle ? (
                <>
                  <span className="no-margin font-normal job-title supporting-text-color">
                    {unescape(userDetails.jobTitle)}
                  </span>
                  {(!isPublicProfile || (isPublicProfile && isEnabledForPublicProfile("Contact info in user profile"))) && (
                    <i className="icon-oval-fill"/>
                  )}
                </>
              ) : null}
              {contactDetailsConfigFromAdmin.visible && (!isPublicProfile || (isPublicProfile && isEnabledForPublicProfile("Contact info in user profile"))) &&
                <>
                  <Button
                    color="primary"
                    variant="borderless"
                    size="medium"
                    padding="small"
                    onClick={() => setContactInfoModalOpen(true)}
                  >
                    {obtainLabelFromAdminConfiguration(translatr('web.myprofile.main', 'ContactInfo'), contactDetailsConfigFromAdmin)}
                  </Button>
                  {!isEnabledForPublicProfile("Contact info in user profile") && <GrayishTag label={translate("PrivateToYouTag")} />}
                </>
              }
            </p>
          </div>
          {isCurrentUser ? (
          <div
            className="user-follow-data supporting-text no-padding flex-center supporting-text-color">
            <FollowButton
              type="follower"
              count={userDetails.followersCount}
              onClick={openFollow}
            />
            <i className="icon-oval-fill"/>
            <FollowButton
              type="following"
              count={userDetails.followingCount}
              onClick={openFollow}
            />
          </div>
          ) : null}
          <div className="more-details">
          {seeMoreDetailsConfigFromAdmin.visible &&  (!isPublicProfile || (isPublicProfile && isEnabledForPublicProfile("Details in user profile"))) &&
            <>
              <Button
                color="secondary"
                variant="borderless"
                size="medium"
                padding="small"
                onClick={() => setDetailsModalOpen(true)}
              >
                {obtainLabelFromAdminConfiguration(translatr('web.myprofile.main', 'ShowMoreDetail'), seeMoreDetailsConfigFromAdmin)}
              </Button>
              {!isEnabledForPublicProfile("Details in user profile") && <GrayishTag label={translate("PrivateToYouTag")} />}
            </>
          }
          </div>
          {isCurrentUser ? (
            <div className="buttons-container-private s-column-gap">
              {viewFeedBackRequests && (
                <ButtonAnchor
                  color="secondary"
                  variant="ghost"
                  size="medium"
                  href={viewFeedBackRequests}
                >
                  {translatr('web.myprofile.main', 'ViewFeedbackRequests') || 'View feedback requests'}
                </ButtonAnchor>
              )}
              <AddProfileSection />
              {!showMoreButton ? (
                <Button
                  color="secondary"
                  variant="ghost"
                  size="medium"
                  onClick={handlePublicProfile}
                >
                  {translatr('web.myprofile.main', 'ViewPublicProfile')}
                  <i className="icon-external-link" />
                </Button>
              ) : null}
              {showMoreButton && (
                <Dropdown
                  icon={<i className="icon-ellipsis-h card-icon" />}
                  wrapperClass="tab-drpdwn"
                  openDropdown={dropdownOpen}
                  setOpenDropdown={setDropdownOpen}
                  ariaLabel={translatr('web.myprofile.main', 'ViewMore')}
                >
                  <ul>{renderDropdownOptions(privateProfileDropdownOptions)}</ul>
                </Dropdown>
              )}
            </div>
          ) : (
            <div className="buttons-container-public">
              {!isPreviewEnabled ? (
                <UserFollowControl
                  isFollowing={userDetails.isFollowing}
                  userId={parseInt(userDetails.id)}
                  name={userDetails.fullName}
                />
              ) : null}
              <MentorshipControl
                userDetails={userDetails}
                isPreviewEnabled={isPreviewEnabled}
                showMoreButton={showMoreButton}
              />
            </div>
          )}
        </div>
        <div className="utility-container">
          {isCurrentUser && currentUser.id !== userDetails.id ? (
            <button
              className="edit-profile"
              onClick={handleEditProfile}
              aria-label={translatr('web.myprofile.main', 'EditProfile')}
            >
              <i className="icon-pencil" />
            </button>
          ) : null}
          {isPreviewEnabled && isPublicProfile ? (
            <div className="exit-public-profile">
              <Button
                color="caution"
                variant="borderless"
                onClick={handleExitPublicProfile}
              >
                {translatr('web.myprofile.main', 'ExitPublicProfile')}
              </Button>
            </div>
          ) : null}
          <div className="organization-container">
            <div className="organization-container-reports-to">
              {manager && (
                <>
                  <p>{translatr('web.myprofile.main', 'ReportsTo')}</p>
                  <div
                    className="organization-container-reports-to-name"
                    onClick={() => navigate(`/${manager.handle}`)}
                    onKeyDown={({ key }) => key === "Enter" && navigate(`/${manager.handle}`)}
                    tabIndex={0}
                    role={"button"}
                  >
                    <Avatar user={manager} blankAlt={true} />
                    <div className="supporting-text">
                      {manager.name || `${manager.firstName} ${manager.lastName}`}
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className="organization-container-show-organization">
              <Button
                color="secondary"
                variant="borderless"
                size="medium"
                padding="small"
                onClick={() => setModalsState(ms => ({ ...ms, organizationModalOpen: true }))}
              >
                {translatr('web.myprofile.main', 'ShowOrganization')}
              </Button>
            </div>
            {isAllowAddChangeManagerEnabled && !isPublicProfile && (
            <div className="organization-container-edit-manager">
              <Button
                color="secondary"
                variant="borderless"
                size="medium"
                padding="small"
                onClick={() => setModalsState(ms => ({ ...ms, editManagerModalOpen: true }))}
              >
                {translatr('web.myprofile.main', 'EditManager')}
              </Button>
            </div>
            )}
          </div>
        </div>
      </div>
      {modalsState.userFollowListModal.open && (
        <FollowersListModal
          followType={modalsState.userFollowListModal.followType}
          closeHandler={() =>
            setModalsState(ms => ({ ...ms, userFollowListModal: { ...ms.userFollowListModal, open: false }}))
          }
          currentUserID={currentUser.id}
        />
      )}
      {detailsModalOpen &&
        <DetailsModal
          onClose={() => setDetailsModalOpen(false)}
          userData={userDetails}
          dashboardInfo={dashboardInfo}
          isPublicProfile={isPublicProfile}
          isCurrentUser={isCurrentUser}
        />
      }
      {contactInfoModalOpen &&
        <ContactInfoModal
          onClose={() => setContactInfoModalOpen(false)}
          isCurrentUser={isCurrentUser}
          userData={userDetails}
        />
      }
      {modalsState.organizationModalOpen && (
        <OrganizationTreeModal
          countries={countries}
          user={userDetails}
          availableLocations={availableLocations}
          isWorkLocation={isWorkLocation}
          onClose={() => setModalsState(ms => ({ ...ms, organizationModalOpen: false }))}
        />
      )}
      {modalsState.editManagerModalOpen && (
        <EditManagerModal
          currentUser={userDetails}
          currentManager={manager}
          onSuccessManagerUpdate={handleMangerUpdate}
          onClose={() => setModalsState(ms => ({ ...ms, editManagerModalOpen: false }))}
        />
      )}
      {modalsState.getTranscriptModalOpen && (
        <GetTranscriptModal
          onClose={() => setModalsState(ms => ({ ...ms, getTranscriptModalOpen: false }))}
        />)
      }
    </div>
  );
};

export default ProfileHeaderContainer;
