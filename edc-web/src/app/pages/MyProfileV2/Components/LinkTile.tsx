import React from 'react';
import Tile from './Tile';
import { Link } from 'react-router-dom';
import './LinkTile.scss';
import cn from 'classnames';

interface LinkTileProps {
  linkTo: string,
  title: string,
  descriptionLabel: string,
  img: string,
  altText?: string,
  imgClassNames?: string,
  defaultImg?: string,
}

const LinkTile: React.FC<LinkTileProps> = ({
  linkTo,
  title,
  descriptionLabel,
  img,
  altText = '',
  imgClassNames,
  defaultImg
}) => {

  const onImageLoadError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    if (defaultImg && e.currentTarget.src !== defaultImg) {
      e.currentTarget.src = defaultImg;
      e.currentTarget.alt = '';
    }
  };

  return (
    <Link to={linkTo}>
      <Tile additionalClasses={['link-tile_column', 'link-tile_container']}>
          <div className="link-tile_image-container">
            <img
              className={cn(imgClassNames)}
              src={img}
              alt={altText || title}
              onError={defaultImg ? onImageLoadError : undefined}
            />
          </div>
          <div className="link-tile_details">
            <div className="link-tile_title">
              <span>{title}</span>
            </div>
            <div className="link-tile_member-quantity">
              <span>{descriptionLabel}</span>
            </div>
          </div>
      </Tile>
    </Link>
  );
};

export default LinkTile;
