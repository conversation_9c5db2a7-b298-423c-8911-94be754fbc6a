import React, { createContext, useReducer, useEffect, useContext, ReactNode } from 'react';
import { useParams } from 'react-router-dom';
import { reducer } from './profileReducer';
import { ProfileDataContextType } from './types';
import { fetchInitialOverviewData } from './Overview/restAction';
import { overviewInitialState } from './Overview/overviewTabState';
import { skillsInitialState } from './Skills/skillsTabState';
import {
  userRelationInitialState
} from './UserRelationsState';

const ProfileDataContext = createContext<ProfileDataContextType | undefined>(undefined);

interface ProfileProviderProps {
  children: ReactNode
}

export const ProfileProvider: React.FC<ProfileProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, { currentViewedUserId: undefined, overview: overviewInitialState, skills: skillsInitialState, userRelations: userRelationInitialState });

  const { handle: unformattedHandle } = useParams();
  const userNameOfOpenedProfile = unformattedHandle?.replace('@', '');

  useEffect(() => {
      fetchInitialOverviewData(userNameOfOpenedProfile, dispatch, state.overview.loading);
  }, []);

  return (
    <ProfileDataContext.Provider value={{ state, dispatch }}>
      {children}
    </ProfileDataContext.Provider>
  );
};

export const useProfileData = () => useContext(ProfileDataContext);

export const useSkillsTabData = () => {
  const { state, dispatch } = useProfileData();
  return { state: state.skills, dispatch };
}

export const useOverviewTabData = () => {
  const { state, dispatch } = useProfileData();
  return { state: state.overview, dispatch };
}

export const useUserRelationsData = () => {
  const { state, dispatch } = useProfileData();
  return { state: state.userRelations, dispatch };
}
