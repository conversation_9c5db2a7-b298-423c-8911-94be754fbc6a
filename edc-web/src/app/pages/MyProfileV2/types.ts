import {
  OverviewDashboardInfoSectionName,
  OverviewTabAction,
  OverviewTabState
} from './Overview/types';
import {
  SkillDashboardInfoSectionName,
  SkillsTabAction,
  SkillsTabState
} from './Skills/types';

export interface ProfileDataContextType {
  state: ProfileDataState;
  dispatch: React.Dispatch<ProfileAction>;
}

export type ProfileAction = OverviewTabAction | SkillsTabAction;

export interface ProfileDataState {
  currentViewedUserId: number,
  overview: OverviewTabState,
  skills: SkillsTabState,
  userRelations: UserRelations
}

export interface UserDetails {
  name: string;
  firstName: string;
  lastName: string;
  handle: string;
  jobTitle: string;
  imgUrl: string;
  id: string;
  location: any;
  workLocationId: string;
  avatarimages: AvatarImages;
}

export interface AvatarImages {
  tiny: string;
  small: string;
  medium: string;
  large: string;
}

export interface UserRelations {
  manager: UserDetails,
  secondaryManagers: Array<UserDetails>,
  reporters: Array<UserDetails>
}

export interface SectionInfo {
  isVisible: boolean,
  isPrivate: boolean,
  canAdd: boolean,
  label: string
}

export type DashboardInfoSectionName = SkillDashboardInfoSectionName | OverviewDashboardInfoSectionName;

export interface DashboardSectionInfo {
  name: DashboardInfoSectionName,
  visible: boolean
}

export interface AdminConfigSectionInfo {
  visible: boolean,
  defaultLabel?: string,
  label?: string,
  languages?: { [key: string]: string }
}

export enum CREDENTIAL_TYPES {
  skill = 1,
  badge = 2,
  certificate = 3,
  assessment = 4,
  patent = 5
}

export interface AttachmentField {
  file: {
    alt_text?: string,
    container?: string,
    filename: string,
    handle: string,
    key?: string,
    mimetype: string,
    originalPath?: string,
    size: number,
    source?: string,
    status?: string,
    uploadId?: string,
    url: string,
    previewUrl?: string,
  },
  securedUrl: string
}

export interface Manager {
  id: number;
  firstName: string;
  lastName: string;
  imgUrl: string;
  name: string;
  handle: string;
}
