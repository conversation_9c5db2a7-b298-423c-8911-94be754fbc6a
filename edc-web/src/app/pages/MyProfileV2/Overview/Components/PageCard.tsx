import React, { ReactNode } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { translate } from '../../utils';
import Card from 'centralized-design-system/src/Card/CardContainer';
import "./PageCard.scss";
import { BackButton } from '../../Components/ActionButtons';

interface PageCardProps {
  header: ReactNode,
  body: ReactNode,
  additionalCardClasses?: Array<string>
}

const PageCard: React.FC<PageCardProps> = ({ header, body, additionalCardClasses }) => {
  const { handle } = useParams();
  const navigate = useNavigate();

  return (
    <Card additionalClasses={additionalCardClasses}>
      <div className="my-profile-v2_page-card-header-container">
        <BackButton
          onClick={() => navigate(!handle ? "/me/overview" : `/${handle}/overview`)}
          ariaLabel={translate("AriaLabelForBackButton")}
        />
        {header}
      </div>
      <div className="my-profile-v2_page-card-body">
        {body}
      </div>
    </Card>
  );
};

export default PageCard;
