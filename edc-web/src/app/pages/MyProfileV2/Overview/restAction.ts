import { users } from 'edc-web-sdk/requests';
import {
  getWorkHistories,
  saveWorkHistories
} from 'edc-web-sdk/requests/talentMarket';
import {
  fetchGroups,
  fetchPublicGroupsForUser
} from 'edc-web-sdk/requests/groups.v2';
import {
  getChannelsMinimal,
  getPublicChannelsForUser
} from 'edc-web-sdk/requests/channels.v2';
import {
  postSkillPassport,
  removeUserPassport,
  updateSkillPassport
} from 'edc-web-sdk/requests/users';
import { getUserBasicInfo, getUserProfileCompletionStatus } from 'edc-web-sdk/requests/users.v2';
import { getUserActivity } from 'edc-web-sdk/requests/profile.v2';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import {
  Activity,
  AssessmentFormData,
  BadgeFormData,
  CertificateFormData,
  DashboardInfo,
  PatentFormData,
  OverviewSectionsInfo,
  SectionType,
  SkillField,
  WorkExperienceFormData, ProfileCompletionDataType,
  CredentialDetails, InitialOverviewData
} from './types';
import {
  applyCredentialFileHandling,
  findSectionInDashboardInfo,
  getSectionInfo,
  handleRestError,
  handleSoftRestError,
  translate
} from '../utils';
import { ACTIVITY_LIMIT, CONTENT_LIMIT_ON_OVERVIEW_PAGE, INTERESTS_LIMIT } from './constants';
import {
  mapActivity,
  mapInterestsChannels,
  mapInterestsGroups
} from './dataMapper';
import { CREDENTIAL_TYPES, DashboardSectionInfo, ProfileAction } from '../types';
import { currentLocale } from 'centralized-design-system/src/Translatr/utils';
import { getCards, getCardsListing } from 'edc-web-sdk/requests/cards';
import { isNewContentEnabled } from '@pages/MyContentV2/utils';

const getSectionsInfo = (dashboardInfo: Array<DashboardSectionInfo>, isPublicProfile: boolean): OverviewSectionsInfo => {
  const overviewAdminConfig = getConfig("OrgCustomizationConfig")?.web?.overview;
  return {
    completeYourProfile: getSectionInfo({
      defaultSectionLabel: translate('CompleteProfile'),
      isPublicProfile,
      configFromAdmin: overviewAdminConfig["web/overview/completeYourProfile"],
    }),
    experiences: getSectionInfo({
      defaultSectionLabel: translate("ExperienceCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Work Histories"),
      configFromAdmin: overviewAdminConfig["web/overview/experience"],
      role: "ADD_WORK_HISTORY"
    }),
    certifications: getSectionInfo({
      defaultSectionLabel: translate("CertificationCredentialsCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Certificates"),
      configFromAdmin: overviewAdminConfig["web/overview/certificatesAndCredentials"],
      role: "ADD_CERTIFICATES"
    }),
    assessments: getSectionInfo({
      defaultSectionLabel: translate("CertificationCredentialsCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Assessments"),
      configFromAdmin: overviewAdminConfig["web/overview/certificatesAndCredentials"],
      role: "ADD_ASSESSMENTS"
    }),
    patents: getSectionInfo({
      defaultSectionLabel: translate("CertificationCredentialsCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Assessments"),
      configFromAdmin: overviewAdminConfig["web/overview/certificatesAndCredentials"],
      role: "ADD_PATENTS"
    }),
    badges: getSectionInfo({
      defaultSectionLabel: translate("BadgeCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Badges"),
      configFromAdmin: overviewAdminConfig["web/overview/badges"],
      role: "ADD_BADGES"
    }),
    channels: getSectionInfo({
      defaultSectionLabel: translate("InterestsCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Channels"),
      configFromAdmin: overviewAdminConfig["web/overview/interests"],
    }),
    groups: getSectionInfo({
      defaultSectionLabel: translate("InterestsCardTitle"),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Groups"),
      configFromAdmin: overviewAdminConfig["web/overview/interests"]
    }),
    activity: getSectionInfo({
      defaultSectionLabel: translate('Activity'),
      isPublicProfile,
      configurableFromAdmin: false
    }),
    content: getSectionInfo({
      defaultSectionLabel: translate('Content'),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Content"),
      configFromAdmin: overviewAdminConfig["web/overview/content"],
      isEnabled: isNewContentEnabled()
    }),
  };
}

export const fetchInitialOverviewData = (userNameOfOpenedProfile: string | undefined, dispatch: React.Dispatch<ProfileAction>, alreadyFetching: boolean) => {
  if(alreadyFetching) {
    return;
  }

  dispatch({ type: 'OVERVIEW_TAB_LOADING' })
  getInitialOverviewData(userNameOfOpenedProfile)
    .then((initialOverviewData: InitialOverviewData) => {
      dispatch({
        type: 'GET_OVERVIEW_DATA_SUCCESS',
        initialOverviewData: initialOverviewData
      });
    })
    .catch(() => {
      dispatch({ type: 'GET_OVERVIEW_DATA_ERROR' });
    });
}

export const getInitialOverviewData = async (userNameOfOpenedProfile: string | undefined): Promise<InitialOverviewData> => {
  const isPublicProfile = !!userNameOfOpenedProfile;

  const userInfo = await getUserInfo(isPublicProfile, userNameOfOpenedProfile);
  const sectionsInfo = getSectionsInfo(userInfo?.dashboardInfo, isPublicProfile);
  const currentViewedUserId = userInfo?.id;

  const getGroupsPromise = () => {
    if (!sectionsInfo.groups.isVisible) {
      return undefined;
    }
    return !isPublicProfile
      ? fetchGroups({ offset: 0, limit: INTERESTS_LIMIT,
        fields: 'id,name,members_count,image_urls'
      }).catch(handleSoftRestError)
      : fetchPublicGroupsForUser({ user_id: currentViewedUserId, offset: 0,
        limit: INTERESTS_LIMIT, fields: 'id,name,members_count,image_urls'
      }).catch(handleSoftRestError);
  };

  const getChannelsPromise = () => {
    if (!sectionsInfo.channels.isVisible) {
      return undefined;
    }
    return !isPublicProfile
      ? getChannelsMinimal({ offset: 0, limit: INTERESTS_LIMIT, is_following: true,
        sort: 'created_at', order_label: 'desc',
        fields: 'id,label,followers_count,banner_image_urls,profile_image_urls'
      }).catch(handleSoftRestError)
      : getPublicChannelsForUser({ user_id: currentViewedUserId, offset: 0, limit: INTERESTS_LIMIT,
        is_following: true, sort: 'created_at', order_label: 'desc',
        fields: 'id,label,followers_count,banner_image_urls,profile_image_urls'
      }).catch(handleSoftRestError);
  };

  const userProfilePromise = users.getUserPassport(currentViewedUserId).catch(handleSoftRestError);
  const completeYourProfilePromise = sectionsInfo.completeYourProfile.isVisible
    ? getCompleteYourProfileData(isPublicProfile).catch(handleSoftRestError)
    : undefined;
  const workExperiencesPromise = sectionsInfo.experiences.isVisible
    ? getWorkHistories({ limit: 0, language: currentLocale(), user_id: currentViewedUserId }).catch(handleSoftRestError)
    : undefined;


  const channelsPromise = getChannelsPromise();
  const groupsPromise = getGroupsPromise()
  const activityPromise = !isPublicProfile && sectionsInfo.activity.isVisible
    ? getUserActivity({ user_id: currentViewedUserId, offset: 0, limit: ACTIVITY_LIMIT }).catch(handleSoftRestError)
    : undefined

  const getContentPromise = () => {
    if (!sectionsInfo.content.isVisible) {
      return undefined;
    }
    return !isPublicProfile
      ? getCards({ author_id: currentViewedUserId, limit: CONTENT_LIMIT_ON_OVERVIEW_PAGE, offset: 0, sort: 'created', order: 'desc' }, true).catch(handleSoftRestError)
      : getCardsListing({ author_id: currentViewedUserId, limit: CONTENT_LIMIT_ON_OVERVIEW_PAGE, offset: 0, sort: 'created', order: 'desc' }, true).catch(handleSoftRestError);
  }

  const contentPromise = getContentPromise();

  const [completeYourProfileResponse, userProfileResponse, workExperiencesResponse, groupsResponse, channelsResponse, activityResponse, contentResponse] = await Promise.all([completeYourProfilePromise, userProfilePromise, workExperiencesPromise, groupsPromise, channelsPromise, activityPromise, contentPromise]);

  return {
    profileCompletionData: completeYourProfileResponse,
    userProfile: userProfileResponse,
    experiences: workExperiencesResponse,
    groups: groupsResponse,
    channels: channelsResponse,
    activity: activityResponse,
    sections: sectionsInfo,
    selfView: !isPublicProfile,
    dashboardInfoCopy: userInfo?.dashboardInfo,
    secondaryManagers: userInfo?.secondaryManagers,
    reporters: userInfo?.reporters,
    manager: userInfo?.manager,
    content: contentResponse,
    currentViewedUserId: currentViewedUserId
  };
}

export const getCompleteYourProfileData = async (isPublicView: boolean = false) => {
  if(isPublicView) {
    return;
  }
  return getUserProfileCompletionStatus().then((result: Array<ProfileCompletionDataType>) => {
    if(Array.isArray(result)) {
      return result;
    }
    return [];
  });
}

const getUserInfo = (isPublicProfile: boolean, userNameOfOpenedProfile: string) => {
  if(isPublicProfile) {
    return users.getPublicProfile(`@${userNameOfOpenedProfile}`).then((response: any) => {
      const { profile } = response;
      return getUserBasicInfo(profile?.id,
        { show_users_relations: true }
        ).catch(handleRestError)
    }).catch(handleRestError)
  }
  return getUserBasicInfo(window.__ED__.id, { show_users_relations: true }).catch(handleRestError)
}

export const changePrivatesOfSection = async (
  dashboardInfo: DashboardInfo,
  currentUserId: string,
  sectionName: SectionType,
  isPrivate: boolean) => {

  const obtainDashboardInfoIndex = (): Array<string> => {
    switch (sectionName) {
      case "experiences": return ["My Work Histories"];
      case "certifications":
      case "assessments":
      case "patents": return ["My Certificates", "My Assessments"];
      case "badges": return ["My Badges"];
      case "channels": return ["My Channels"];
      case "groups": return ["My Groups"];
      case "content": return ["My Content"];
      default:
        console.error("Invalid section name");
        return undefined;
    }
  }

  const sectionNames = obtainDashboardInfoIndex();
  let newDashboardInfo = [...dashboardInfo];

  sectionNames.forEach(sectionName => {
    const sectionExists = dashboardInfo.some(item => item.name === sectionName);

    if (sectionExists) {
      newDashboardInfo = newDashboardInfo.map(item =>
        item.name === sectionName ? { ...item, visible: !isPrivate } : item
      );
    } else {
      newDashboardInfo.push({
        name: sectionName,
        visible: !isPrivate
      });
    }
  });

  return users.postDashboardInfo(newDashboardInfo, currentUserId).then((response: any) => {
    if(!response) {
      throw new Error();
    }
    const sectionStatus = response?.dashboardInfo?.find((el: DashboardSectionInfo) => obtainDashboardInfoIndex().includes(el.name));
    return !sectionStatus.visible;
  }).catch(handleRestError);
}

const mapCurrentWorkExperiences = (workExperience: any) => ({
    uuid: workExperience.uuid,
    title: workExperience.title,
    description: workExperience.description,
    company: workExperience.company,
    internal: workExperience.internal,
    job_role_detail: workExperience.job_role_detail,
    event_type: workExperience.event_type,
    start_date: workExperience.start_date,
    end_date: workExperience.end_date || undefined
})

const mapNewWorkExperiencePayload = (uuid: string, newWorkExperience: WorkExperienceFormData) => {
  const presentCompanyName = window.__ED__?.organization?.name;

  return {
    uuid: uuid || '',
    title: newWorkExperience.title,
    description: newWorkExperience.description,
    company: newWorkExperience.companyName,
    internal: presentCompanyName === newWorkExperience.companyName,
    job_role_detail: newWorkExperience?.role?.id && {
      id: newWorkExperience.role.id,
      title: newWorkExperience.role.label,
      title_text: newWorkExperience.role.label
    },
    event_type: 'workHistory',
    start_date: newWorkExperience.startDate,
    end_date: !newWorkExperience.currentlyWorkingHere ? newWorkExperience.endDate : undefined
  };
}

const mapWorkExperiencePayload = (uuid: string | undefined, currentWorkExperiencesPayload: Array<any>, newWorkExperiencePayload: any) => {
  const addNew = uuid == undefined;
  if (addNew) {
    return [
      ...currentWorkExperiencesPayload,
      newWorkExperiencePayload
    ];
  }
  return currentWorkExperiencesPayload.map(workExperience => {
    if (workExperience.uuid === uuid) {
      return newWorkExperiencePayload;
    }
    return workExperience;
  });
}

const loadRecentWorkHistories = async () => {
  const response = await getWorkHistories({ limit: 0 }).catch(handleSoftRestError);
  return response?.result?.map(mapCurrentWorkExperiences) ?? [];
}

export const updateWorkExperiences = async (uuid: string, newWorkExperience: WorkExperienceFormData) => {
  const newWorkExperiencePayload = mapNewWorkExperiencePayload(uuid, newWorkExperience);

  const currentWorkExperiencesPayload = await loadRecentWorkHistories();
  const workExperiencePayloadToSave = mapWorkExperiencePayload(uuid, currentWorkExperiencesPayload, newWorkExperiencePayload);
  return saveWorkExperiences(workExperiencePayloadToSave);
}

export const saveWorkExperiences = async (workExperiencePayloadToSave: any[]) => {
  try {
    await saveWorkHistories({ work_histories: workExperiencePayloadToSave });
    return await loadRecentWorkHistories() || workExperiencePayloadToSave;
  } catch (error) {
    return handleRestError(error);
  }
}

export const removeWorkExperience = async (uuid: string) => {
  const currentWorkExperiencesPayload = await loadRecentWorkHistories();
  const workExperiencePayloadToSave = currentWorkExperiencesPayload.filter((workExperience: any) => workExperience.uuid !== uuid);
  return saveWorkHistories({ work_histories: workExperiencePayloadToSave })
    .then(() => workExperiencePayloadToSave)
    .catch(handleRestError)
}

const mapCertificatePayload = (newCertificate: CertificateFormData) => {
  const apiPayload: { credential_details: CredentialDetails } = {
    credential_details: {
      credential_name: newCertificate.title,
      description: newCertificate?.description,
      credential_type: CREDENTIAL_TYPES.certificate,
      issuer: newCertificate.issuer,
      issue_date: newCertificate?.issueDate,
      credential_url: newCertificate?.url,
      skills: newCertificate.skills?.map((value: SkillField) => ({
        topic_id: value.id,
        topic_label: value.label,
        topic_name: value.name,
      })),
      credential_id: newCertificate.certificateId,
      expiry_date: newCertificate?.expiryDate ?? null,
      proficiency_level: newCertificate?.level?.value
    }
  }
  applyCredentialFileHandling(newCertificate?.attachment?.file, apiPayload.credential_details);
  return apiPayload;
}

export const saveCertificate = async (uuid: string, newCertificate: CertificateFormData) => {
  const certificatePayload = mapCertificatePayload(newCertificate);

  if(uuid) {
    return updateSkillPassport(certificatePayload, uuid)
      .then((response: any) => response)
      .catch(handleRestError);
  }
  return postSkillPassport(certificatePayload)
    .then((response: any) => response)
    .catch(handleRestError);

}

const mapAssessmentPayload = (newAssessment: AssessmentFormData) => {
  const apiPayload: { credential_details: CredentialDetails } = {
    credential_details: {
      credential_name: newAssessment.title,
      description: newAssessment?.description,
      credential_type: CREDENTIAL_TYPES.assessment,
      issuer: newAssessment.issuer,
      issue_date: newAssessment?.issueDate,
      credential_url: newAssessment?.url,
      skills: newAssessment.skills?.map((value: SkillField) => ({
        topic_id: value.id,
        topic_label: value.label,
        topic_name: value.name,
      })),
      proficiency_level: newAssessment?.level?.value,
      score: newAssessment.score,
    }
  };
  applyCredentialFileHandling(newAssessment?.attachment?.file, apiPayload.credential_details);
  return apiPayload;
}

export const saveAssessment = async (uuid: string, newAssessment: AssessmentFormData) => {
  const assessmentPayload = mapAssessmentPayload(newAssessment);

  if(uuid) {
    return updateSkillPassport(assessmentPayload, uuid)
      .then((response: any) => response)
      .catch(handleRestError);
  }
  return postSkillPassport(assessmentPayload)
    .then((response: any) => response)
    .catch(handleRestError);

}

const mapPatentPayload = (newAssessment: PatentFormData) => {
  const apiPayload: { credential_details: CredentialDetails } = {
    credential_details: {
      credential_name: newAssessment.title,
      description: newAssessment?.description,
      credential_type: CREDENTIAL_TYPES.patent,
      credential_url: newAssessment?.url,
      expiry_date: newAssessment?.expiryDate ?? null,
      patent: {
        application_number: newAssessment?.applicationNumber,
        awarded_by: newAssessment?.awardedBy,
        date_of_patent: newAssessment?.dateOfPatent,
        filed_on: newAssessment?.filedOn,
        name_of_inventor: newAssessment.nameOfInventors,
        patent_number: newAssessment?.patentNumber
      }
    }
  };
  applyCredentialFileHandling(newAssessment?.attachment?.file, apiPayload.credential_details);
  return apiPayload;
}

export const savePatent = async (uuid: string, newAssessment: PatentFormData) => {
  const assessmentPayload = mapPatentPayload(newAssessment);

  if(uuid) {
    return updateSkillPassport(assessmentPayload, uuid)
      .then((response: any) => response)
      .catch(handleRestError);
  }
  return postSkillPassport(assessmentPayload)
    .then((response: any) => response)
    .catch(handleRestError);

}

export const removeCertification = async (uuid: string): Promise<string> => {
  return removeUserPassport(uuid)
    .then(() => uuid)
    .catch(handleRestError)
}

const mapBadgePayload = (newBadge: BadgeFormData) => {
  const apiPayload: { credential_details: CredentialDetails } = {
    credential_details: {
      credential_name: newBadge.title,
      description: newBadge?.description,
      credential_type: CREDENTIAL_TYPES.badge,
      issuer: newBadge.issuer,
      issue_date: newBadge?.issueDate,
      credential_url: newBadge?.url,
      skills: newBadge.skills?.map((value: SkillField) => ({
        topic_id: value.id,
        topic_label: value.label,
        topic_name: value.name,
      })),
      credential_id: newBadge.badgeId,
      expiry_date: newBadge?.expiryDate ?? null,
      proficiency_level: newBadge?.level?.value
    }
  };
  applyCredentialFileHandling(newBadge?.attachment?.file, apiPayload.credential_details);
  return apiPayload;
}

export const saveBadge = async (uuid: string, newBadge: BadgeFormData) => {
  const assessmentPayload = mapBadgePayload(newBadge);

  if(uuid) {
    return updateSkillPassport(assessmentPayload, uuid)
      .then((response: any) => response)
      .catch(handleRestError);
  }
  return postSkillPassport(assessmentPayload)
    .then((response: any) => response)
    .catch(handleRestError);
}

export const removeBadge = async (uuid: string): Promise<string> => {
  return removeUserPassport(uuid)
    .then(() => uuid)
    .catch(handleRestError)
}

export const loadMoreActivity = async (currentUserId: number, newOffset: number): Promise<Array<Activity>> => {
  return getUserActivity({ user_id: currentUserId, offset: newOffset, limit: ACTIVITY_LIMIT }).then((response: any) => {
    return mapActivity(response);
  }).catch(handleRestError)
}

export const loadMoreGroups = async (newOffset: number) => {
  return fetchGroups({ offset: newOffset, limit: INTERESTS_LIMIT, fields: "id,name,members_count,image_urls" })
    .then((response: any) => {
      return mapInterestsGroups(response)
    }).catch(handleRestError)
}

export const loadMoreChannels = async (newOffset: number) => {
  return getChannelsMinimal({ offset: newOffset, limit: INTERESTS_LIMIT, is_following: true, sort: "created_at", order_label: "desc", fields: "id,label,followers_count,banner_image_urls,profile_image_urls" })
    .then((response: any) => {
      return mapInterestsChannels(response)
    }).catch(handleRestError)
}
