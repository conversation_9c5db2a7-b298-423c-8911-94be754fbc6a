import {
  AttachmentField,
  DashboardSectionInfo,
  SectionInfo,
  UserDetails
} from '../types';

export type OverviewTabPage = "experiences" | "certifications" | "badges" | "groups" | "channels" | "activity" | "content" | undefined;

export interface OverviewTabState {
  experiences: Experiences,
  certifications: Certifications,
  badges: Badges,
  interests: {
    groupList: Array<Group>,
    offsetGroups: number,
    totalGroups: number,
    groupsVisible: boolean,
    groupsPrivate: boolean,
    channelList: Array<Channel>,
    offsetChannels: number,
    totalChannels: number,
    channelsVisible: boolean,
    channelsPrivate: boolean,
    label: string,
  },
  activities: Activities,
  modals: {
    open: ModalType | undefined;
    action: ModalAction,
    data?: any
  }
  selfView: boolean,
  loading: boolean;
  error: boolean;
  completeYourProfile: {
    visible: boolean,
    label: string,
  }
  profileCompletionData: Array<ProfileCompletionDataType>,
  reloadProfileCompletion: boolean,
  dashboardInfoCopy: Array<DashboardSectionInfo>,
  secondaryManagers: Array<UserDetails>,
  content: Content,
}

export interface Certifications {
  certificateList: Array<Certificate>,
  certificatesTotal: number,
  certificatesPrivate: boolean,
  assessmentList: Array<Assessment>,
  assessmentsTotal: number,
  assessmentsPrivate: boolean,
  patentList: Array<Patent>,
  patentsTotal: number,
  patentsPrivate: boolean
  visible: boolean,
  certificateAddAvailable: boolean,
  assessmentAddAvailable: boolean,
  patentAddAvailable: boolean,
  label: string
}

export interface Certificate {
  id: string,
  type: CertificationType,
  title: string,
  description?: string,
  attachment?: AttachmentRaw,
  skills: Array<SkillRaw>,
  level: LevelField,
  issuer: string,
  url?: string,
  certificateId?: string,
  issueDate?: string,
  expiryDate?: string,
  creationDate: string,
  canManage: boolean
}

export interface Patent {
  id: string,
  type: CertificationType,
  title: string,
  description?: string,
  attachment?: AttachmentRaw,
  nameOfInventors: string,
  awardedBy?: string,
  url?: string,
  applicationNumber?: string,
  patentNumber?: string,
  dateOfPatent?: string,
  filedOn?: string,
  expiryDate?: string,
  creationDate?: string,
  canManage: boolean
}

export interface Assessment {
  id: string,
  type: CertificationType,
  title: string,
  description?: string,
  attachment?: AttachmentRaw,
  skills?: Array<SkillRaw>,
  level: LevelField,
  issuer: string,
  score: number,
  url?: string,
  issueDate?: string,
  creationDate?: string,
  canManage: boolean
}

export interface CertificationTileData {
  id: string,
  type: CertificationType
  title: string,
  entityInformation?: Array<{ label?: string, value: string }>,
  entityDetails?: Array<{ label?: string, value: string }>,
  skills?: Array<string>,
  attachment?: AttachmentRaw,
  url?: string,
  dateForSorting?: string,
  creationDate: string,
  rawData: Certificate | Patent | Assessment,
  canManage: boolean
}

export type CertificationType = "certificate" | "patent" | "assessment";

export interface Experiences {
  list: Array<Experience>,
  visible: boolean,
  private: boolean,
  canAdd: boolean,
  label: string
}

export interface Experience {
  id: string,
  title: string,
  company: string,
  startDate: string,
  endDate?: string,
  role: {
    id: string,
    label: string,
  },
  description?: string,
}

export interface Badges {
  list: Array<Badge>
  total: number,
  visible: boolean,
  private: boolean,
  canAdd: boolean,
  label: string
};

export interface Badge {
  identifier: string,
  id: string,
  title: string,
  description?: string,
  attachment?: AttachmentRaw,
  issuer: string
  skills?: Array<SkillRaw>,
  level: LevelField,
  issueDate?: string,
  expiryDate?: string,
  badgeId?: string,
  url?: string,
  creationDate: string,
  canManage: boolean,
  badgeUserId: string
}

export interface Activities {
  list: Array<Activity>
  offset: number,
  visible: boolean,
  private: boolean,
  label: string,
}

export interface Activity {
  action: string,
  createdAt: string,
  snippet: string,
  linkUrl: string,
  linkPrefix: string
}

export interface Group {
  id: string,
  name: string,
  membersCount: number,
  img: string,
}

export interface Channel {
  id: string,
  name: string,
  followersCount: number,
  img: string,
}

export interface ContentItem {
  id: string,
  title: string,
  description: string,
  cardType: string,
  attachment?: AttachmentField,
  url?: string,
  creationDate: string,
  canManage: boolean,
  assignable: boolean,
  authorId: number,
  isOfficial: boolean,
  dueAt: string,
  startDate: object,
  assignment: {
    assignedDate: string,
    assignmentCompletedAt: string,
    assignmentStartedAt: string,
    assignor: {
      id: number,
      name: string,
    },
    completedAt: string,
    dueAt: string,
    id: number,
    startDate: string,
    startedAt: string,
  },
  author: {
    id: number,
    name: string,
  }
}

export interface Content {
  visible: boolean,
  private: boolean,
  label: string,
  list: Array<ContentItem>,
  total: number,
}

export type OverviewDashboardInfoSectionName = "My Work Histories" | "My Certificates" | "My Assessments" | "My Badges" | "My Channels" | "My Groups" | "My Content"

export type SectionType = "experiences" | "certifications" | "assessments" | "patents" | "badges" | "channels" | "groups" | "activity" | "content";

export type ModalType = "experiences" | "certificates" | "patents" | "assessments" | "badges";

export interface OverviewSectionsInfo {
  completeYourProfile: SectionInfo,
  experiences: SectionInfo,
  certifications: SectionInfo,
  assessments: SectionInfo,
  patents: SectionInfo,
  badges: SectionInfo,
  channels: SectionInfo,
  groups: SectionInfo,
  activity: SectionInfo
  content: SectionInfo
}

export type ModalAction = "add" | "edit";

export type OverviewTabAction =
  | { type: 'OVERVIEW_TAB_LOADING'; }
  | { type: 'GET_OVERVIEW_DATA_SUCCESS'; initialOverviewData: InitialOverviewData }
  | { type: 'GET_OVERVIEW_DATA_ERROR'; }
  | { type: 'GET_COMPLETE_PROFILE_DATA_SUCCESS', profileCompletionData: Array<ProfileCompletionDataType> }
  | { type: 'CHANGED_PRIVATES_OF_SECTION', section: SectionType, isPrivate: boolean }
  | { type: 'WORK_EXPERIENCES_UPDATED', payload: any, reloadProfileCompletion?: boolean }
  | { type: 'CERTIFICATE_REMOVED', removedCertificateId: string }
  | { type: 'CERTIFICATE_UPDATED', payload: any, uuid: string }
  | { type: 'CERTIFICATE_CREATED', payload: any }
  | { type: 'PATENT_REMOVED', removedPatentId: string }
  | { type: 'PATENT_UPDATED', payload: any, newPatentFormData: PatentFormData, uuid: string }
  | { type: 'PATENT_CREATED', payload: any, newPatentFormData: PatentFormData }
  | { type: 'ASSESSMENT_REMOVED', removedAssessmentId: string }
  | { type: 'ASSESSMENT_UPDATED', payload: any, uuid: string }
  | { type: 'ASSESSMENT_CREATED', payload: any }
  | { type: 'BADGE_REMOVED', removedBadgeId: string }
  | { type: 'BADGE_UPDATED', payload: any, uuid: string }
  | { type: 'BADGE_CREATED', payload: any }
  | { type: 'MORE_ACTIVITIES_LOADED', newActivities: Array<Activity> }
  | { type: 'MORE_GROUPS_LOADED', newGroups: Array<Group> }
  | { type: 'MORE_CHANNELS_LOADED', newChannels: Array<Channel> }
  | { type: 'OPEN_MODAL', modal: ModalType, action: ModalAction, data?: any }
  | { type: 'CLOSE_MODAL' }

export interface InitialOverviewData {
  profileCompletionData: Array<ProfileCompletionDataType>;
  userProfile: any;
  experiences: any;
  groups: any;
  channels: any;
  activity: any;
  content: any;
  selfView: boolean;
  sections: OverviewSectionsInfo;
  dashboardInfoCopy: Array<DashboardSectionInfo>;
  secondaryManagers: Array<UserDetails>;
  reporters: Array<UserDetails>;
  manager: UserDetails;
  currentViewedUserId: number;
}

export interface SkillRaw {
  topicId: string,
  topicLabel: string,
  topicName: string
}

export interface SkillField {
  id: string,
  label: string,
  name: string,
  topic_label: string,
  type: "topic",
  value: string,
}

export interface LevelField {
  label: string,
  value: string
}

export interface AttachmentRaw {
  filename: string,
  handle: string,
  mimetype?: string,
  contentType?: string,
  size: number,
  url: string,
  previewUrl?: string,
}

export interface WorkExperienceFormData {
  title: string,
  companyName: string,
  startDate: Date,
  endDate?: Date | undefined,
  currentlyWorkingHere: boolean,
  role?: {
    id: string,
    label: string,
  },
  description?: string
}

export interface WorkExperienceFormErrors {
  title?: string,
  companyName?: string,
  startDate?: string,
  endDate?: string
}

export interface CredentialDetails {
  credential_name: string;
  description?: string;
  credential_type: number;
  issuer?: string;
  issue_date?: Date;
  credential_url?: string;
  skills?: { topic_id: string; topic_label: string; topic_name: string; }[];
  credential_id?: string;
  expiry_date?: Date | null;
  proficiency_level?: string;
  credential?: any[];
  experience?: string;
  patent?: {
    application_number?: string;
    awarded_by?: string;
    date_of_patent?: Date;
    filed_on?: Date;
    name_of_inventor: string;
    patent_number?: string;
  };
  score?: number;
}

export interface CertificateFormData {
  title: string,
  description?: string,
  attachment?: AttachmentField,
  skills?: Array<SkillField>,
  level: LevelField,
  issuer: string,
  url?: string,
  certificateId?: string,
  issueDate?: Date,
  expiryDate?: Date,
  showExpiryDate: boolean,
}

export interface CertificateFormErrors {
  title?: string,
  level?: string
  issuer?: string,
  url?: string
}

export interface AssessmentFormData {
  title: string,
  description?: string,
  attachment?: AttachmentField,
  level: LevelField,
  skills?: Array<SkillField>,
  issuer: string,
  score?: number,
  url?: string,
  issueDate?: Date,
}

export interface AssessmentFormErrors {
  title?: string,
  level?: string,
  issuer?: string,
  score?: string,
  url?: string,
}

export interface PatentFormData {
  title: string,
  nameOfInventors: string,
  description?: string,
  attachment?: AttachmentField,
  awardedBy?: string,
  url?: string,
  applicationNumber?: string,
  patentNumber?: string,
  filedOn?: Date,
  dateOfPatent?: Date,
  expiryDate?: Date,
  showExpiryDate: boolean,
}

export interface PatentFormErrors {
  title?: string,
  nameOfInventors?: string,
  url?: string
}

export type DashboardInfo = Array<{ name: string, visible: boolean }>

export interface BadgeFormData {
  title: string,
  description?: string,
  attachment?: AttachmentField,
  level: LevelField,
  skills?: Array<SkillField>,
  issuer: string,
  badgeId?: string,
  url?: string,
  issueDate?: Date,
  expiryDate?: Date,
  showExpiryDate: boolean,
}

export interface BadgeFormErrors {
  title?: string,
  level?: string,
  issuer?: string,
  badgeId?: string,
  url?: string
}

export interface ProfileCompletionDataType {
  id: string,
  progress: number,
  is_active: boolean
}
