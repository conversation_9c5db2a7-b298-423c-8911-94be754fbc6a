import React from 'react';
import { OverviewTabPage } from './types';
import { useOverviewTabData } from '../ProfileProvider';
import ErrorPage from '../Components/EmptyState/ErrorPage';
import EmptyPublicProfilePage from '../Components/EmptyState/EmptyPublicProfilePage';
import CompleteProfile from '../CompleteProfile/CompleteProfile';
import WorkExperiences from '../Overview/WorkExperience/WorkExperiences';
import Certifications from '../Overview/Certifictions/Certifications';
import Badges from '../Overview/Badges/Badges';
import Interests from '../Overview/Interests/Interests';
import Content from '../Overview/Content/Content';
import WorkExperiencesPage from '../Overview/WorkExperience/WorkExperiencesPage';
import CertificationsPage from '../Overview/Certifictions/CertificationsPage';
import BadgesPage from '../Overview/Badges/BadgesPage';
import GroupsPage from '../Overview/Interests/GroupsPage';
import ChannelsPage from '../Overview/Interests/ChannelsPage';
import ActivityPage from '../Overview/Activity/ActivityPage';
import Activity from '../Overview/Activity/Activity';
import ContentPage from '@pages/MyProfileV2/Overview/Content/ContentPage';

const Sections = () => (
  <>
    <CompleteProfile />
    <WorkExperiences />
    <Certifications />
    <Badges />
    <Interests />
    <Activity />
    <Content />
  </>
);

interface OverviewTabContentProps {
  page: OverviewTabPage
}

const OverviewTabContent: React.FC<OverviewTabContentProps> = ({ page }) => {
  const { state: { error, selfView, experiences, certifications, interests, activities, badges, content } } = useOverviewTabData();

  if(error) {
    return <ErrorPage />;
  }
  const experienceSectionHidden = !experiences.visible || !experiences.list.length;
  const certificationsSectionHidden = !certifications.visible || (!certifications.certificateList?.length && !certifications.patentList.length && !certifications.assessmentList.length)
  const groupsTabHidden = !interests.groupsVisible || !interests.groupList.length;
  const channelsTabHidden = !interests.channelsVisible || !interests.channelList.length;
  const badgesSectionHidden = !badges.visible || !badges.list.length;
  const activitySectionHidden = !activities.visible || !activities.list.length;
  const contentSectionHidden = !content.visible || !content.list.length;

  if (!selfView && experienceSectionHidden && certificationsSectionHidden && groupsTabHidden && channelsTabHidden && badgesSectionHidden && activitySectionHidden && contentSectionHidden) {
    return <EmptyPublicProfilePage />
  }

  switch (page) {
    case 'experiences':
      return <WorkExperiencesPage />;
    case 'certifications':
      return <CertificationsPage />;
    case 'badges':
      return <BadgesPage />;
    case 'groups':
      return <GroupsPage />;
    case 'channels':
      return <ChannelsPage />;
    case 'activity':
      return <ActivityPage />
    case 'content':
      return <ContentPage />;
    default:
      return <Sections />;
  }
};

export default OverviewTabContent;
