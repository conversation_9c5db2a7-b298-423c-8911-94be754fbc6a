@import '~centralized-design-system/src/Styles/_variables.scss';
.link_tiles-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--ed-spacing-2xs) rem-calc(16);
  margin-top: var(--ed-spacing-base);
  margin-bottom: var(--ed-spacing-base);
  border: 0;

  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
  }
}

.link-page_tiles-container {
  display: flex;
  flex-direction: column;
  gap: var(--ed-spacing-2xs);
}

.link_tiles-container,
.link-page_tiles-container {
  a {
    color: var(--ed-gray-6) !important;
  }

  .link-tile-card-thumbnail {
    min-width: 64px;
    width: 64px;
    height: 64px;
    border-radius: var(--ed-border-radius-md);
    object-fit: cover;
  }
}

.content-page-container {
  .my-content-list-card-container {
    border-radius: 0;
    box-shadow: none;
  }
}
