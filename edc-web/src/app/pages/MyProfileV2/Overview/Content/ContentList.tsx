import React from 'react';
import LinkTile from '@pages/MyProfileV2/Components/LinkTile';
import './Content.scss';
import getStaticImgPath from 'edc-web-sdk/helpers/getStaticImgPath';
import { getCardImage } from '@utils/smartCardUtilsV2';
import { NavigateTo } from '@pages/home/<USER>/utils';
interface ContentListProps {
  list: Array<any>,
}

interface ImageObject {
  img: string;
  altText?: string;
}

const MAX_NUMBER_OF_CONTENT_ITEMS = 4;

const ContentList: React.FC<ContentListProps> = ({ list }) => {

  const defaultThumbnail = getStaticImgPath("/i/images/thumbnail-card-placeholder.png");

  const fetchCardThumbnail = (content_item: any) => {
    let { img, altText } = getCardImage(content_item) as ImageObject;
    if (!img) {
      img = defaultThumbnail;
      altText = '';
    }
    return { img, altText };
  };
  return (
    <div className="link_tiles-container">
      {list.slice(0, MAX_NUMBER_OF_CONTENT_ITEMS).map(content_item => {
        const { img, altText } = fetchCardThumbnail(content_item);

        return (
          <LinkTile
            key={content_item.id}
            linkTo={NavigateTo(content_item)}
            title={content_item.title}
            img={img}
            altText={altText}
            imgClassNames={'link-tile-card-thumbnail'}
            descriptionLabel={content_item.readableCardType}
            defaultImg={defaultThumbnail}
          />
        );
      })}
    </div>
  );
};

export default ContentList;
