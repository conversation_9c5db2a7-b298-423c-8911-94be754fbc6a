import React from 'react';
import { useOverviewTabData, useProfileData } from '../../ProfileProvider';
import PageCard from '../Components/PageCard';
import OverviewPageHeader from '../Components/OverviewPageHeader';
import Loader from '@components/Loader/Loader';
import ContentPanel from '@components/ContentFilter/ContentPanel';
import { Navigate } from 'react-router-dom';

const ContentPage = () => {
  const { state: { content, loading, selfView } } = useOverviewTabData();
  const { state: { currentViewedUserId }} = useProfileData();

  if (loading) {
    return <Loader />;
  }

  return (
    content.visible ? (
      <PageCard
        header={
          <OverviewPageHeader
            sectionName={'content'}
            title={content.label}
            privateToYou={content.private}
            showSwitch={selfView}
          />
        }
        body={<div className="content-page-container">
          <ContentPanel
            authorId={currentViewedUserId}
            publicView={!selfView}
          />
        </div>}
      />
    ) : <Navigate to="overview" replace />
  );
};

export default ContentPage;
