import React from 'react';
import './Content.scss';
import Card from 'centralized-design-system/src/Card/CardContainer';
import NavigationLink from '../../Components/NavigationLink';
import CardHeader from '../../Components/Card/CardHeader';
import ContentList from './ContentList';
import { translate } from '@pages/MyProfileV2/utils';
import { useOverviewTabData } from '@pages/MyProfileV2/ProfileProvider';
import { Link } from 'react-router-dom';

const Content = () => {
  const {
    state: { content, selfView }
  } = useOverviewTabData();

  const isContentSectionVisible = content?.visible && content?.list?.length > 0;

  const contentButtons = [
    selfView && (
      <Link to="content">
        <span
          className="icon-pencil"
          role="button"
          aria-label={translate('AriaLabelEditIcon')}
        ></span>
      </Link>
    )
  ].filter(Boolean);

  return (
    isContentSectionVisible && (
      <Card>
        <CardHeader
          cardTitle={content?.label ?? translate('Content')}
          showActionButtons={selfView}
          showPrivateToYouTag={selfView && content?.private}
          buttons={contentButtons}
        />
        <ContentList list={content.list} />
        <NavigationLink to="content">
          {translate('ShowAllContent', { total: content.total })}
        </NavigationLink>
      </Card>
    )
  );
};

export default Content;
