import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useOverviewTabData } from '../ProfileProvider';
import Loader from '@components/Loader/Loader';
import TabContainer from '../Components/TabContainer';
import { OverviewTabPage } from './types';
import { fetchInitialOverviewData } from './restAction';
import OverviewTabContent from './OverviewTabContent';
import "./OverviewContainer.scss";

interface OverviewContainerProps {
  page: OverviewTabPage
}

const OverviewContainer: React.FC<OverviewContainerProps> = ({ page }) => {
  const { state: { loading }, dispatch } = useOverviewTabData();

  const { handle: unformattedHandle } = useParams();
  const userNameOfOpenedProfile = unformattedHandle?.replace('@', '');

  useEffect(() => {
    if(!page) {
      fetchInitialOverviewData(userNameOfOpenedProfile, dispatch, loading);
    }
  }, []);

  useEffect(() => {
    const isSectionPageView = ["experiences", "certifications", "badges", "groups", "channels", "activity", "content"].includes(page);
    if(isSectionPageView) {
      const firstHeadingLevelThree = document.querySelector(".my-profile-v2_overview-page-header-title");
      if (firstHeadingLevelThree) {
        firstHeadingLevelThree.scrollIntoView({
          block: 'center'
        });
      }
    }
  }, [page]);

  if(loading) {
    return <Loader center />
  }

  return (
    <TabContainer>
      <OverviewTabContent page={page} />
    </TabContainer>
  );
};

export default OverviewContainer;
