import { OverviewTabState } from './types';

export const overviewInitialState: OverviewTabState = {
  experiences: {
    list: [],
    visible: false,
    private: true,
    canAdd: false,
    label: ""
  },
  certifications: {
    certificateList: [],
    certificatesTotal: 0,
    certificatesPrivate: true,
    assessmentList: [],
    assessmentsTotal: 0,
    assessmentsPrivate: true,
    patentList: [],
    patentsTotal: 0,
    patentsPrivate: true,
    visible: false,
    certificateAddAvailable: false,
    assessmentAddAvailable: false,
    patentAddAvailable: false,
    label: ""
  },
  badges: {
    list: [],
    total: 0,
    visible: false,
    private: true,
    canAdd: false,
    label: ""
  },
  interests: {
    groupList: [],
    offsetGroups: 0,
    totalGroups: 0,
    groupsVisible: false,
    groupsPrivate: true,
    channelList: [],
    offsetChannels: 0,
    totalChannels: 0,
    channelsVisible: false,
    channelsPrivate: true,
    label: ""
  },
  activities: {
    list: [],
    offset: 0,
    visible: false,
    private: false,
    label: ""
  },
  modals: {
    open: undefined,
    action: "add"
  },
  selfView: false,
  loading: false,
  error: false,
  completeYourProfile: {
    visible: false,
    label: ''
  },
  profileCompletionData: [],
  reloadProfileCompletion: false,
  dashboardInfoCopy: [],
  secondaryManagers: [],
  content: {
    visible: false,
    private: true,
    list: [],
    label: "",
    total: 0
  }
};
