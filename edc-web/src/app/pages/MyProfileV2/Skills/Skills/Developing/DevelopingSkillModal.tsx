import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import cn from 'classnames';
import Mo<PERSON>, {  <PERSON><PERSON><PERSON>ontent, ModalFooter,  ModalHeader } from 'centralized-design-system/src/Modals';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Button } from 'centralized-design-system/src/Buttons';
import SkillsToDevelop from '@components/ProfileSteps/SkillsToDevelop';
import { open_v2 } from '../../../../../actions/snackBarActions';
import { translate } from '../../../utils';
import { useSkillsTabData } from '../../../ProfileProvider';
import { refreshSkillsEvaluationChart, saveDevelopingSkills } from '../../restAction';
import '../SkillModal.scss';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';

const DevelopingSkillModal = () => {
  const { dispatch } = useSkillsTabData();
  const dispatchGlobal = useDispatch();

  const [componentsPayload, setComponentsPayload] = useState({
    "learning_goals": []
  });

  const [savingInProgress, setSavingInProgress] = useState<boolean>(false);
  const [removingInProgress, setRemovingInProgress] = useState<boolean>(false);
  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState<boolean>(false);

  const closeModal = () => {
    dispatch({ type: "CLOSE_SKILL_MODAL" });
  };

  const buttonsDisabled = savingInProgress || removingInProgress;
  const skillsLimit = getConfig("limit_options")?.interests_limit ?? undefined;

  const handleSave = () => {
    setSavingInProgress(true);
    saveDevelopingSkills(componentsPayload["learning_goals"])
      .then((response: any) => {
        dispatch({ type: "SKILLS_DEVELOPING_UPDATED", payload: response });
        refreshSkillsEvaluationChart(dispatch);
        const toastSuccessMessage = translate("SkillSaveSuccessToastMessage");
        dispatchGlobal(open_v2(toastSuccessMessage, "success", false))
        setSavingInProgress(false);
        closeModal();
      })
      .catch(() => {
        const toastErrorMessage = translate("SkillSaveErrorToastMessage");
        dispatchGlobal(open_v2(toastErrorMessage, "error", false))
        setSavingInProgress(false);
      })
  }

  return (
    <Modal size="small" className={"skill-modal"}>
      <ModalHeader
        title={translatr('web.myprofile.main', 'AddSkillsToDevelopModalTitle')}
        onClose={closeModal}
      />
      <ModalContent>
        <SkillsToDevelop
          setComponentData={() => {}}
          setComponentsPayload={setComponentsPayload}
          selectedSkillsLimit={skillsLimit}
          setIsSaveButtonDisabled={setIsSaveButtonDisabled}
        />
      </ModalContent>
      <ModalFooter className={cn("skill-modal_footer")}>
        <div>
          <Button
            color="secondary"
            variant="ghost"
            onClick={closeModal}
            disabled={buttonsDisabled}
          >
            {translatr('web.common.main', 'Cancel')}
          </Button>
          <Button
            color="primary"
            disabled={buttonsDisabled || isSaveButtonDisabled}
            onClick={() => handleSave()}
          >
            {(savingInProgress ? translate("SavingButtonLabel") : translate("SaveButtonLabel"))}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default DevelopingSkillModal;
