import React, { useState } from 'react';
import { Legend, PolarAngleAxis, PolarGrid, PolarRadiusAxis, Radar, RadarChart } from 'recharts';
import { darkestGrey } from 'centralized-design-system/src/MUIComponents/colors/colors';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Button } from 'centralized-design-system/src/Buttons';
import EmptyState from 'centralized-design-system/src/EmptyState';
import Loader from '@components/Loader/Loader';
import { translate } from '../../utils';
import { useSkillsTabData } from '../../ProfileProvider';
import { SkillsEvaluationChartData, SkillsEvaluationChartProperties } from '../types';
import CustomLegend from './CustomLegend';

const tickStyle = {
  fontSize: 14,
  width: 50,
  fill: darkestGrey,
  transform: 'scale(1, 1.12)',
  'aria-hidden': 'true'
};

const EVALUATION_PROPERTIES: SkillsEvaluationChartProperties = {
  self: {
    name: translatr('web.common.main', 'SelfAssessedSkillLevel'),
    opacity: 0.2,
    fill: 'var(--ed-primary-base)',
  },
  peer: {
    name: translatr('web.common.main', 'AveragePeerAssessment'),
    opacity: 0.1,
    fill: "#118DA3",
  },
  recommended: {
    name: translatr('web.common.main', 'RecommendedSkillLevel'),
    opacity: 0.2,
    fill: "#BBBBBB",
  },
};

const defaultOpacity = {
  self: { fillOpacity: EVALUATION_PROPERTIES.self.opacity, strokeWidth: 0.2 },
  peer: { fillOpacity: EVALUATION_PROPERTIES.peer.opacity, strokeWidth: 0.2 },
  recommended: { fillOpacity: EVALUATION_PROPERTIES.recommended.opacity, strokeWidth: 0.2 }
};

const getGraphDescription = (data: Array<SkillsEvaluationChartData>) => data.map(skillData => skillData.description).join(', ');

const SkillsEvaluationChart = () => {
  const { state: { skillsEvaluation, declaredSkills }, dispatch } = useSkillsTabData();

  const {self, peer, recommended } = EVALUATION_PROPERTIES;


  const [opacity, setOpacity] = useState(defaultOpacity)

  const handleOpacityChange = (evaluationPropertiesKey?: keyof SkillsEvaluationChartProperties) => {
    if(!evaluationPropertiesKey) {
      setOpacity(defaultOpacity);
      return;
    }
    setOpacity({
      ...defaultOpacity,
      [evaluationPropertiesKey]: { fillOpacity: 0.8, strokeWidth: 2 }
    });
  };

  const skillsEvaluationChartData = skillsEvaluation?.chartData?.filter(data => data.show);  
  const shouldDisplayChart = skillsEvaluationChartData?.length >= 3;

  if(skillsEvaluation.loading) {
    return <div className={"skills-evaluation-chart-loader"}><Loader /></div>
  }

  return (
    <>
      {shouldDisplayChart
        ? <div className="skills-evaluation-chart">
            <div className="skills-evaluation-chart_buttons">
              <Button
                color="secondary"
                variant="ghost"
                onClick={() => dispatch({ type: "OPEN_SKILL_MODAL", modal: "filterSkillsEvaluationChart" })}
              >
                <span>{translate("SkillsLabel")}</span>
                {skillsEvaluationChartData.length && (
                  <span className="skills-evaluation-chart_selected-filter-count">
                    {skillsEvaluationChartData.length}
                  </span>
                )}
              </Button>
            </div>
            <RadarChart
              data={skillsEvaluationChartData}
              width={650}
              height={650}
              aria-labelledby="imageTitle imageDescription"
              role="img"
            >
              <title id="imageTitle">{translate("SkillsAssessmentGraph")}</title>
              <desc id="imageDescription">{getGraphDescription(skillsEvaluationChartData)}</desc>
              <PolarGrid />
              <PolarAngleAxis tick={tickStyle} tickLine={false} dataKey="skillName" radius={100} />
              <PolarAngleAxis dataKey="label" tick={tickStyle} radius={100} />
              <PolarRadiusAxis domain={[0, 3]} axisLine={false} tick={false} />
              <Radar
                name={self.name}
                style={{ fill: self.fill}}
                dataKey={"self"}
                fillOpacity={opacity["self"].fillOpacity}
                stroke={self.fill}
                strokeWidth={opacity["self"].strokeWidth}
              />
              <Radar
                name={peer.name}
                fill={peer.fill}
                dataKey={"peer"}
                stroke={peer.fill}
                strokeWidth={opacity["peer"].strokeWidth}
                fillOpacity={opacity["peer"].fillOpacity}
              />
              <Radar
                name={recommended.name}
                fill={recommended.fill}
                dataKey={"recommended"}
                stroke={recommended.fill}
                strokeWidth={opacity["recommended"].strokeWidth}
                fillOpacity={opacity["recommended"].fillOpacity}
              />
              <Legend
                content={<CustomLegend evaluationProperties={EVALUATION_PROPERTIES} changeOpacity={handleOpacityChange} />}
                wrapperStyle={{
                  position: "unset",
                  width: "100%",
                  height: "auto",
                }} // to overwrite default
              />
            </RadarChart>
          </div>
        : <EmptyState
            title={translate("SkillsEvaluationChartEmptyStateTitle")}
            description={translate("SkillsEvaluationChartEmptyStateDescription")}
            button={declaredSkills.canAdd ?
              <Button
                color="secondary"
                variant="ghost"
                onClick={() => dispatch({ type: "OPEN_SKILL_MODAL", modal: "addDeclaredSkill", action: "add" })}
              >
                {translate('AddNewButtonLabel')}
              </Button>
              : undefined
            }
          />
      }
    </>
  )
};

export default SkillsEvaluationChart;
