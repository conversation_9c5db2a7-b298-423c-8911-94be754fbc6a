import { users } from 'edc-web-sdk/requests';
import {
  postSkillPassport,
  removeUserPassport,
  updateSkillPassport
} from 'edc-web-sdk/requests/users';
import {
  SkillsSectionsInfo,
  Skill,
  SkillsTabAction,
  SkillDashboardInfoSectionName
} from './types';
import { getUserBasicInfo } from 'edc-web-sdk/requests/users.v2';
import {
  getConfig
} from 'centralized-design-system/src/Utils/OrgConfigs';
import { CredentialDetails, DashboardInfo } from '../Overview/types';
import {
  applyCredentialFileHandling,
  findSectionInDashboardInfo,
  getSectionInfo, handleRestError, handleSoftRestError,
  translate
} from '../utils';
import { CREDENTIAL_TYPES, DashboardSectionInfo } from '../types';
import { getSelfAssessments } from 'edc-web-sdk/requests/skills.v2';

const getSectionsInfo = (dashboardInfo: Array<DashboardSectionInfo>, isPublicProfile: boolean): SkillsSectionsInfo => {
  const skillsConfig = getConfig("OrgCustomizationConfig")?.web?.skills;
  return {
    declaredSkills: getSectionInfo({
      defaultSectionLabel: translate('Declared'),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Skills"),
      configFromAdmin: skillsConfig["web/skills/declaredSkills"],
      role: "ADD_SKILLS"
    }),
    developingSkills: getSectionInfo({
      defaultSectionLabel: translate('Developing'),
      isPublicProfile,
      section: findSectionInDashboardInfo(dashboardInfo, "My Developing Skills"),
      configFromAdmin: skillsConfig["web/skills/developingSkills"],
      alwaysCanAddNewRecords: true
    }),
    skillsEvaluation: getSectionInfo({
      defaultSectionLabel: translate("SkillsEvaluationChartCardTitle"),
      isPublicProfile,
      section: skillsConfig["web/skills/skillEvaluationChart"],
      configurableFromAdmin: false
  }),
  }
}

export const getInitialSkillsData = async (userNameOfOpenedProfile: string | undefined)  => {
  const isPublicProfile = !!userNameOfOpenedProfile;

  const userInfo = await getUserInfo(isPublicProfile, userNameOfOpenedProfile);

  const currentUserId = userInfo?.id;
  const credentialsPromise = users.getUserPassport(currentUserId).catch(handleSoftRestError);
  const skillsEvaluationPromise = getSelfAssessments().catch(() => {
    handleSoftRestError();
    return {
      errorDuringLoadingSkillsAssessments: true
    }
  });

  const [credentialsResponse, skillsEvaluationResponse] = await Promise.all([credentialsPromise, skillsEvaluationPromise]);

  return {
    userProfile: userInfo.profile,
    userSkills: credentialsResponse.skills,
    skillsEvaluation: skillsEvaluationResponse,
    selfView: !isPublicProfile,
    currentlyViewedUserFullName: userInfo.fullName,
    sections: getSectionsInfo(userInfo?.dashboardInfo, isPublicProfile),
    dashboardInfoCopy: userInfo?.dashboardInfo,
  };
}

const getUserInfo = (isPublicProfile: boolean, userNameOfOpenedProfile: string) => {
  if(isPublicProfile) {
    return users.getPublicProfile(`@${userNameOfOpenedProfile}`).then((response: any) => {
      const { profile } = response;
      return getUserBasicInfo(profile?.id).catch(handleRestError)
    }).catch(handleRestError)
  }
  return getUserBasicInfo(window.__ED__.id).catch(handleRestError)
}

export const changePrivatesOfSection = async (dashboardInfo: DashboardInfo, currentUserId: string, isPrivate: boolean, skillSectionName: SkillDashboardInfoSectionName) => {
  let newDashboardInfo = [...dashboardInfo];
  if(dashboardInfo.find(info => info.name === skillSectionName)) {
    newDashboardInfo = dashboardInfo.map((item: DashboardSectionInfo) =>
      skillSectionName === item.name
        ? { ...item, visible: !isPrivate }
        : item
    );
  } else {
    newDashboardInfo = [...newDashboardInfo,
      {
        name: skillSectionName,
        visible: !isPrivate
      }
    ]
  }

  return users.postDashboardInfo(newDashboardInfo, currentUserId).then((response: any) => {
    if(!response) {
      throw new Error();
    }
    const sectionStatus = response?.dashboardInfo?.find((el: DashboardSectionInfo) => skillSectionName === el.name);
    return !sectionStatus.visible;
  }).catch(handleRestError);
}

const mapDeclaredSkillPayload = (newSkill: Skill) => {
  const apiPayload: { credential_details: CredentialDetails } = {
    credential_details: {
      credential_name: newSkill.skill.label,
      credential_type: CREDENTIAL_TYPES.skill,
      description: newSkill.description,
      experience: newSkill.yourExperience,
      issuer: "",
      proficiency_level: newSkill.level.value,
      skills: [{
        topic_id: newSkill.skill.id,
        topic_label: newSkill.skill.label,
        topic_name: newSkill.skill.name,
      }],
    }
  };
  applyCredentialFileHandling(newSkill?.attachment?.file, apiPayload.credential_details);
  return apiPayload;
}

export const saveDeclaredSkill = async (uuid: string, newSkill: Skill) => {
  const skillPayload = mapDeclaredSkillPayload(newSkill);

  if (uuid) {
    return updateSkillPassport(skillPayload, uuid)
      .then((response: any) => response)
      .catch(handleRestError);
  }
  return postSkillPassport(skillPayload)
    .then((response: any) => response)
    .catch(handleRestError);
}

export const removeDeclaredSkill = async (uuid: string): Promise<string> => {
  return removeUserPassport(uuid)
    .then(() => uuid)
    .catch(handleRestError)
}

export const refreshSkillsEvaluationChart = async (dispatch: React.Dispatch<SkillsTabAction>) => {
  dispatch({ type: "SKILL_EVALUATION_REFRESHING_IN_PROGRESS" })
  getSelfAssessments()
    .then((response: any) => {
     dispatch({ type: "SKILL_EVALUATION_REFRESHED", skillsEvaluation: response })
    })
    .catch((err: any) => {
      dispatch({ type: "SKILL_EVALUATION_REFRESHED_UNCOMPLETED" });
      handleSoftRestError();
    });
}

export const removeDevelopingSkill = async (topicIdToRemove: string): Promise<any> => {
  try {
    const { profile: { learningTopics } } = await getUserBasicInfo(window.__ED__.id);
    const newDevelopingSkillsPayload = learningTopics.filter((topic: any) => topic.topic_id !== topicIdToRemove);
    return users.postLearningDomainTopicsNew(newDevelopingSkillsPayload, window.__ED__.id);
  } catch (error) {
    return handleRestError(error);
  }
}

export const saveDevelopingSkills = async (payload: any): Promise<any> => {
  const currentUserId = window.__ED__.id;
  return users.postLearningDomainTopicsNew(payload, currentUserId)
    .then((response: any) => response)
    .catch(handleRestError)
}
