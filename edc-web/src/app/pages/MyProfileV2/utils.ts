import moment from 'moment';
import _ from 'lodash';
import { MONTH_AND_YEAR_FORMAT } from '@utils/constants';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import getLanguageCodeForMoment from 'centralized-design-system/src/Utils/getLanguageCodeForMoment';
import { langs } from '../../constants/languages';
import {
  AdminConfigSectionInfo, DashboardInfoSectionName,
  DashboardSectionInfo,
  SectionInfo
} from './types';
import { currentLocale } from 'centralized-design-system/src/Translatr/utils';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';
import { updateCredentialFileArray } from '@utils/utils';
import { CredentialDetails } from './Overview/types';

export const isNewProfileEnabled = (): boolean => {
  return getConfig("OrgCustomizationConfig")?.web?.enableNewUserProfile;
}

export const dateToMonthYearFormat = (date: string | undefined) => {
  if(date) {
    return moment(date).locale(getLanguageCodeForMoment()).format(MONTH_AND_YEAR_FORMAT)
  }
  return undefined;
}

export const getLevelInArray = (data: { level?: { value: string } }): Array<{ value: string }> => {
  const level = data?.level;
  if (level) {
    return [{ value: level.value }];
  }
  return [];
}

export const transformMapToObject = (mapObject: Map<string, any>) =>
  mapObject ? JSON.parse(JSON.stringify(mapObject)) : mapObject;

export const translate = (key: string, variables?: { [key in string]: string | number }) => translatr("web.myprofile.main", key, variables);

export const obtainLabelFromAdminConfiguration = (defaultLabel: string, configFromAdmin?: AdminConfigSectionInfo) => {
  const lang = Object.entries(langs).find(([ ,value]) => currentLocale() === value)?.[0].toLowerCase();
  return configFromAdmin?.languages?.[lang] || configFromAdmin?.label || defaultLabel || configFromAdmin?.defaultLabel;
}

export const findSectionInDashboardInfo = (dashboardInfo: Array<DashboardSectionInfo>, sectionName: DashboardInfoSectionName) => {
  return dashboardInfo?.find((section: DashboardSectionInfo) => section.name === sectionName);
}
export interface GetSectionInfoParams {
  defaultSectionLabel: string;
  isPublicProfile: boolean;
  section?: DashboardSectionInfo;
  configFromAdmin?: AdminConfigSectionInfo;
  role?: string;
  alwaysCanAddNewRecords?: boolean;
  isEnabled?: boolean;
  configurableFromAdmin?: boolean;
}

export const getSectionInfo = ({
                                 defaultSectionLabel,
                                 isPublicProfile,
                                 section = null,
                                 role = null,
                                 alwaysCanAddNewRecords = false,
                                 isEnabled = true,
                                 configFromAdmin = null,
                                 configurableFromAdmin = true,
                               }: GetSectionInfoParams): SectionInfo => {
  const adminMakeSectionVisible = configurableFromAdmin ? configFromAdmin?.visible : !isPublicProfile; // If configurable from admin, use admin setting; otherwise, visible only in private profile
  const ownerMakeSectionPrivate = !section?.visible;
  const userCouldSeeTheSection = !isPublicProfile || (isPublicProfile && !ownerMakeSectionPrivate);
  const hasPermissionToAddNewRecords = role ? window.__ED__.permissions.includes(role) : alwaysCanAddNewRecords
  return {
    isVisible: isEnabled && adminMakeSectionVisible && userCouldSeeTheSection,
    isPrivate: ownerMakeSectionPrivate,
    canAdd: !isPublicProfile && hasPermissionToAddNewRecords,
    label: obtainLabelFromAdminConfiguration(defaultSectionLabel, configFromAdmin)
  };
}

export const handleRestError = (error: any) => {
  handleSoftRestError();
  throw error?.message || translate("PleaseTryAgainLaterRestErrorMessage");
}

export const handleSoftRestError = () => {
  console.error("failed to fetch data");
}

export const prepareUpdatedDashboardInfoCopy = (dashboardInfoCopy: Array<DashboardSectionInfo>, dashboardInfoNames: Array<DashboardInfoSectionName>, visible: boolean): Array<DashboardSectionInfo> => {
  const updatedDashboardInfo = _.cloneDeep(dashboardInfoCopy);
  dashboardInfoNames.forEach(dashboardInfoName => {
    const existingSection = updatedDashboardInfo.find((info: DashboardSectionInfo) => info.name === dashboardInfoName);
    if (existingSection) {
      existingSection.visible = visible;
    } else {
      updatedDashboardInfo.push({ name: dashboardInfoName, visible });
    }
  });

  return updatedDashboardInfo;
}

export const addLxMediaUrlInResponse = (response: any, formData:any) => {
    const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
    if (isLXMediaHubEnabled) {
      const url = formData?.attachment?.securedUrl;
      if (url) {
        return {
          ...response,
          credential: response.credential.map((cred: any) => ({
            ...cred,
            url: formData.attachment.securedUrl,
            previewUrl: formData.attachment.securedUrl
          }))
        };
      }
      return response;
    }
    return response;
  };

/**
 * Applies credential file handling logic based on LX Media Hub configuration
 * @param file - The file attachment to process
 * @param credentialDetails - The credential details object to update
 */
export const applyCredentialFileHandling = (file: File | null | undefined, credentialDetails: CredentialDetails) => {
  const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
  if (isLXMediaHubEnabled) {
    try {
      updateCredentialFileArray(file, credentialDetails);
     } catch (error) {
      console.error('Error updating credential file array:', error);
      credentialDetails.credential = undefined;
     }
  } else {
    credentialDetails.credential = file ? [file] : [];
  }
};
