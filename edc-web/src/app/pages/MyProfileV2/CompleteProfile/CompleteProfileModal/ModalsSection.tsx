import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { translatr } from 'centralized-design-system/src/Translatr';
import { users } from 'edc-web-sdk/requests';
import { saveWorkHistories } from 'edc-web-sdk/requests/talentMarket';
import { searchLocationByGeolocation } from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  createRequestPayloadForNormalizedGeolocation,
  isNormalizationOfGeolocationsRequired, normalizeGeolocations
} from '@components/geolocation/utils';
import { saveCareerPreferences } from '../../../../actions/careerPreferencesActions';
import {
  mapSkillsDataToPayload,
  updateUserSkills
} from '@components/ProfileSteps/CurrentSkills/helper';
import CurrentSkills from '@components/ProfileSteps/CurrentSkills';
import SkillsToDevelop from '@components/ProfileSteps/SkillsToDevelop';
import WorkExperience from '@components/ProfileSteps/WorkExperience';
import CareerPreferences from '@components/ProfileSteps/CareerPreferences';
import { ModalMapType, ShowModalType } from '../types';
import { useProfileData } from '../../ProfileProvider';
import ModalWrapper from './ModalWrapper';
import { EMPTY_SKILLS_DATA } from '@components/UserSkillsForm/utils';
import { getConfigService } from 'edc-web-sdk/helpers/getConfigService';
import { CONFIG_SERVICE_CV_PARSING } from '@pages/TalentMarketplace/util';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';

interface ModalsSectionProps {
  showModal: ShowModalType
  closeModal(): void,
  reloadProfileCompletionStatus: () => void
}

const ModalsSection: React.FC<ModalsSectionProps> = ({
  showModal,
  closeModal,
  reloadProfileCompletionStatus
}) => {
  const dispatch = useDispatch();
  const currentUserId = useSelector((state: any) => state.currentUser?.get('id'));
  const profileDataDispatch = useProfileData().dispatch;
  const skillsLimit = getConfig("limit_options")?.interests_limit ?? undefined;
  const [componentData, setComponentData] = useState({
    current_skills: EMPTY_SKILLS_DATA,
    learning_goals: {}
  });
  const [componentsPayload, setComponentsPayload] = useState<any>({});
  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState(false);
  const [isCvParsingEnabled, setIsCvParsingEnabled] = useState(false);

  useEffect(() => {
    setComponentData((prev: any) => ({ ...prev, current_skills: componentData.current_skills }));
    setComponentsPayload((payload: any) => ({
      ...payload,
      current_skills: mapSkillsDataToPayload(componentData.current_skills)
    }));
  }, [componentData.current_skills.created]);


  useEffect(() => {
    getConfigService(CONFIG_SERVICE_CV_PARSING).then((result) => {
      setIsCvParsingEnabled(result?.value || false);
    }).catch(err => {
      console.error(err);
      return false;
    });
  }, []);

  const updateLearningGoals = (data: any) => {
    return users.postLearningDomainTopicsNew(data, currentUserId);
  };

  const updateCareerPreferences = async (data: any) => {
    if (isNormalizationOfGeolocationsRequired(data?.career_preferences?.geolocation)) {
      //Need to perform normalization of geolocation here to avoid additional requests to an external service, which are limited.
      const requestPayloads = createRequestPayloadForNormalizedGeolocation(
        data.career_preferences.geolocation
      );
      const normalizedGeolocationsRequest: any[] = [];
      requestPayloads.forEach((payload: any) => {
        normalizedGeolocationsRequest.push(searchLocationByGeolocation(payload));
      });
      const results = await Promise.allSettled(normalizedGeolocationsRequest);
      const normalizedGeolocations = normalizeGeolocations(
        data.career_preferences.geolocation,
        results
      );
      return dispatch(saveCareerPreferences({ career_preferences: { ...data['career_preferences'], geolocation: normalizedGeolocations }})
      );
    } else {
      return dispatch(saveCareerPreferences(data));
    }
  };

  const updateWorkExperience = () => {
    const workExperienceTasks = [];
    if (componentsPayload['current_skills']) {
      workExperienceTasks.push(updateUserSkills(componentsPayload['current_skills']));
    }
    workExperienceTasks.push(saveWorkHistories(componentsPayload['work_experience'])
      .then(() => profileDataDispatch({ type: 'WORK_EXPERIENCES_UPDATED', payload: componentsPayload['work_experience'].work_histories })));

    return Promise.allSettled(workExperienceTasks);
  };

  const modalMap: ModalMapType = {
    current_skills: {
      save: () => updateUserSkills(componentsPayload['current_skills']),
      title: translatr('web.common.main', 'AddSkillsYouHave'),
      description: translatr(
        'web.common.main',
        'WeHaveIdentifiedTopSkillsThatYouPossessBasedOnYourJobRole'
      ),
      component: <CurrentSkills
        componentData={componentData}
        setComponentData={setComponentData}
        componentsPayload={componentsPayload}
        setComponentsPayload={setComponentsPayload}
        setIsSaveButtonDisabled={setIsSaveButtonDisabled}
      />
    },
    learning_goals: {
      save: () => updateLearningGoals(componentsPayload['learning_goals']),
      title: translatr('web.myprofile.main', 'AddSkillsToDevelopModalTitle'),
      description: translatr('web.myprofile.main', 'AddSkillsToDevelopDesc'),
      component: <SkillsToDevelop
        setComponentData={setComponentData}
        setComponentsPayload={setComponentsPayload}
        selectedSkillsLimit={skillsLimit}
        setIsSaveButtonDisabled={setIsSaveButtonDisabled}
      />
    },
    work_experience: {
      save: () => updateWorkExperience(),
      title: translatr('web.myprofile.main', 'AddWorkExperience'),
      description: isCvParsingEnabled ? translatr( 'web.common.main', 'WorkExperienceGuidanceWithCVParsing') : translatr( 'web.common.main', 'WorkExperienceGuidanceWithoutCVParsing'),
      info: `${translatr('web.talentmarketplace.main', 'WeUseYourInformationToProvideYouSkillSuggestionsAndRecommendations')} ${translatr('web.talentmarketplace.main', 'UpdatesToRecommendationsMayTakeTime')}`,
      component: <WorkExperience
        setComponentData={setComponentData}
        setComponentsPayload={setComponentsPayload}
        componentsPayload={componentsPayload}
        componentData={componentData}
        setIsSaveButtonDisabled={setIsSaveButtonDisabled}
        hideSkillSuggestionInfo
      />
    },
    career_preference: {
      save: () => updateCareerPreferences(componentsPayload['career_preference']),
      title: translatr('web.common.main', 'CareerPreferences'),
      info: `${translatr('web.talentmarketplace.main', 'WeUseYourInformationToProvideYouSkillSuggestionsAndRecommendations')} ${translatr('web.talentmarketplace.main', 'UpdatesToRecommendationsMayTakeTime')}`,
      component: <CareerPreferences
        setComponentData={setComponentData}
        setComponentsPayload={setComponentsPayload}
        setIsSaveButtonDisabled={setIsSaveButtonDisabled}
      />
    }
  };
  const currentModal = showModal && modalMap[showModal];

  return currentModal ? <ModalWrapper
    save={currentModal.save}
    title={currentModal.title}
    description={currentModal.description}
    info={currentModal.info}
    children={currentModal.component}
    closeModal={closeModal}
    isSaveButtonDisabled={isSaveButtonDisabled}
    reloadProfileCompletionStatus={reloadProfileCompletionStatus}
  /> : <></>;
};

export default ModalsSection;
