import React, { useEffect, useState, useRef } from 'react';
import { number, bool, object } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { connect } from 'react-redux';
import { Select } from 'centralized-design-system/src/Inputs';
import { BaseCard, EmptyWidgetState } from '@edc-blocks/blocks-component-library';
import { useNavigate } from 'react-router-dom';
import {
  SmallCardWrapper,
  SmallCard
} from '../../lib/packages/blocks-component-library/shared/components/SmallCard';
import { getContentTypeLabel } from '@utils/getContentTypeLabel';
import { getImageDetails } from '../../../../../app/pages/MyLearningPlan/Common/util';
import { getListV2, leaveFromGroup, joinToGroup } from 'edc-web-sdk/requests/groups.v2';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import Carousel from 'centralized-design-system/src/Carousel';
import GroupChannelCard from 'centralized-design-system/src/GroupChannelCard/GroupChannelCard';
import { getCustomTitle } from '../../helpers';
// import { snackBarOpenClose } from '../../../../app/actions/channelsActionsV2';
import { snackBarOpenClose } from '@actions/snackBarActions';

const Groups = ({ customInfo, dispatch }) => {
  const navigate = useNavigate();
  const elRef = useRef();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const feed = useRef('all');

  const dropdownItems = [
    {
      id: 'all',
      label: translatr('web.group.main', 'MyGroups')
    },
    {
      id: 'sub_admin',
      label: translatr('web.group.main', 'GroupsIAmAnAdminOf')
    },
    {
      id: 'admin',
      label: translatr('web.group.main', 'GroupsIAmALeaderOf')
    },
    {
      id: 'member',
      label: translatr('web.group.main', 'GroupsIAmAMemberOf')
    }
  ];

  async function init() {
    const infoFields =
      'id,name,slug,description,image_urls,is_member,is_everyone_team,is_private,is_mandatory,members_count,is_pending,is_team_sub_admin,is_team_admin,image_alt_text,bannerimage_alt_text';
    const payload = {
      limit: 20,
      offset: 0,
      fields: infoFields,
      is_dynamic: false,
      q: '',
      all: ''
    };

    switch (feed.current) {
      case 'sub_admin':
        payload.role = 'sub_admin';
        break;
      case 'admin':
        payload.role = 'admin';
        break;
      case 'member':
        payload.role = 'member';
        break;
      default:
        payload.role = 'my';
    }

    const resp = await getListV2(payload);
    setData(resp?.teams || []);
    setLoading(false);
  }

  useEffect(() => {
    init();
  }, []);

  function getUrl(card) {
    return `/teams/${card.slug}`;
  }

  const seeAllLink = '/org-groups';
  const footerConfig = {
    type: 'link',
    label:
      data?.length === 0 && loading === false ? null : translatr('web.landingpage.main', 'SeeAll'),
    attributes: {
      href: data?.length === 0 && loading === false ? null : seeAllLink,
      onClick: () => {
        navigate(data?.length === 0 && loading === false ? null : seeAllLink);
      }
    }
  };

  function renderLoading() {
    return Array.from({ length: 5 }).map((_, idx) => (
      <div key={idx} className="bookmarks-block__item">
        <Skeleton height={64} style={{ marginBottom: '1rem' }} />
      </div>
    ));
  }

  function renderEmptyState() {
    return (
      <EmptyWidgetState
        icon="icon-users-fill"
        subtext={translatr('web.landingpage.main', 'NoGroupsAvailable') || 'No groups available'}
        cta={{
          label: translatr('web.landingpage.main', 'ExploreContent'),
          href: '/org-groups',
          onClick: e => {
            e.preventDefault();
            navigate('/org-groups');
          }
        }}
      />
    );
  }

  function renderAsList() {
    return (
      <React.Fragment>
        {data?.map(bm => {
          const { image, altText } = getImageDetails(bm);
          const isPartOfGroup =
            bm.isMember || bm.isTeamAdmin || bm.isTeamSubAdmin || bm.isTeamModerator;
          const isMandatory = bm.isMandatory;
          let actions = (
            <CardAction
              id={bm.id}
              isActive={isPartOfGroup}
              snackbarErrorHandler={snackbarErrorHandler}
            />
          );
          if (isMandatory) {
            actions = null;
          }
          return (
            <div className="bookmarks-block__item" key={`bookmark-${bm.id}`}>
              <SmallCard
                id={bm.id}
                title={bm.name}
                headline={getContentTypeLabel(bm.readableCardType)}
                imgUrl={image}
                altText={altText}
                navigationUrl={getUrl(bm)}
                action={actions}
              />
            </div>
          );
        })}
      </React.Fragment>
    );
  }

  const goToStandAloneView = transitionUrl => {
    navigate(transitionUrl);
  };

  const snackbarErrorHandler = error => dispatch(snackBarOpenClose(error, 3000));

  function renderAsCards() {
    return (
      <span className="ed-ui">
        <Carousel
          key="bookmarks-carousel"
          ariaLabelPrefix={
            translatr('web.landingpage.main', 'GroupsWidgetDefaultTitle') || 'My Groups'
          }
        >
          {data?.map(group => {
            const isPartOfGroup =
              group.isMember || group.isTeamAdmin || group.isTeamSubAdmin || group.isTeamModerator;
            const isGroupAccessible = isPartOfGroup || !group.isPrivate;
            return (
              <GroupChannelCard
                key={group.id}
                group={group}
                groupOrChannelId={group.id}
                tagName={translatr('web.group.main', 'Group')}
                cardImage={group.imageUrls.medium}
                title={group.name}
                userCount={group.membersCount}
                imageAltText={group.imageAltText || ''}
                transitionUrl={`/teams/${group.slug}`}
                isPrivate={group.isPrivate}
                goToStandAloneView={goToStandAloneView}
                isUserIsPartOfGroupOrChannel={isPartOfGroup}
                isInvitationPending={group.isPending}
                isMandatory={group.isMandatory}
                isGroupAccessible={isGroupAccessible}
                isEveryoneTeam={group.isEveryoneTeam}
                snackbarErrorHandler={snackbarErrorHandler}
              />
            );
          })}
        </Carousel>
      </span>
    );
  }

  function handleDropdown(item) {
    setLoading(true);
    setData([]);
    feed.current = item.id;
    init();
  }

  return (
    <section style={{ height: '100%' }} ref={elRef}>
      <BaseCard
        title={
          customInfo
            ? getCustomTitle(customInfo, translatr('web.common.main', 'Groups') || 'Groups')
            : translatr('web.common.main', 'Groups') || 'Groups'
        }
        footerConfig={data?.length > 0 && footerConfig}
      >
        <Select items={dropdownItems} onChange={handleDropdown} />
        <SmallCardWrapper>
          {loading && renderLoading()}
          {!loading && data.length === 0 && renderEmptyState()}
          {!loading && data.length > 0 && elRef.current?.clientWidth < 400
            ? renderAsList()
            : renderAsCards()}
        </SmallCardWrapper>
      </BaseCard>
    </section>
  );
};

const CardAction = ({ id, isActive, snackbarErrorHandler }) => {
  const [cardAction, setCardAction] = useState(isActive);

  const handleAction = () => {
    if (!isActive) {
      joinToGroup(id)
        .then(() => {
          setCardAction(true);
        })
        .catch(error => {
          const message = error?.response?.body ? error.response.body.message : error.message;
          snackbarErrorHandler(message);
          console.error(`Error in Groups.joinToGroup.func: ${error}`);
        });
    } else {
      leaveFromGroup(id)
        .then(() => {
          setCardAction(false);
        })
        .catch(error => {
          const message = error?.response?.body ? error.response.body.message : error.message;
          snackbarErrorHandler(message);
          console.error(`Error in Groups.leaveFromGroup.func: ${error}`);
        });
    }
  };

  return (
    <button
      className={`${!cardAction ? 'icon-plus-circle' : 'icon-check'}`}
      aria-label={`${!cardAction ? 'follow' : 'unfollow'}`}
      onClick={handleAction}
    />
  );
};

CardAction.propTypes = {
  id: number,
  isActive: bool,
  snackbarErrorHandler: object
};

Groups.propTypes = {
  customInfo: object
};

export default connect(({ currentUser }) => ({
  id: currentUser.get('id')
}))(Groups);
