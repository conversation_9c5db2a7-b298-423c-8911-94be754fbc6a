import { DiscoverMentorshipCard } from '@components/MentorCard';
import ProjectCard from '@components/ProjectCard';
import ProjectList from './SmallCarousalComponents/ProjectList';
import unescape from 'lodash/unescape';
import { MENTORSHIP_STATUS } from 'opportunity-marketplace/Mentorship/shared/helpers';
import CardWrapper from '@components/cardStandardization/CardWrapper';
import MentorsList from './SmallCarousalComponents/MentorsList';
import ChannelList from './SmallCarousalComponents/ChannelList';
import ChannelCardWrapper from './Wrappers/ChannelCardWrapper';
import UserCardWrapper from './Wrappers/UserCardWrapper';
import GroupCardWrapper from './Wrappers/GroupCardWrapper';
import UsersList from './SmallCarousalComponents/UsersList';
import ContentList from './SmallCarousalComponents/ContentList';
import GroupsList from './SmallCarousalComponents/GroupsList';
import { fetchCarouselData } from 'edc-web-sdk/requests/blocks';

// Utility function for groups to fetch data and convert it in the form required by the card component
const getGroupsData = async payload => {
  const groupsData = [];
  await fetchCarouselData(payload).then(res => {
    res?.forEach(group => {
      const isPartOfGroup =
        group.isMember || group.isTeamAdmin || group.isTeamSubAdmin || group.isTeamModerator;
      const isGroupAccessible = isPartOfGroup || !group.isPrivate;
      groupsData.push({
        key: group.id,
        group,
        groupOrChannelId: group.id,
        tagName: 'group',
        cardImage: group.imageUrls.medium,
        title: group.name,
        userCount: group.membersCount,
        imageAltText: group.imageAltText || '',
        transitionUrl: `/teams/${group.slug}`,
        isPrivate: group.isPrivate,
        isUserIsPartOfGroupOrChannel: isPartOfGroup,
        isInvitationPending: group.isPending,
        isMandatory: group.isMandatory,
        isGroupAccessible: isGroupAccessible,
        isEveryoneTeam: group.isEveryoneTeam
      });
    });
  });

  return groupsData;
};

// Utility function for projects to fetch data and convert it in the form required by the card component
const getProjectData = async payload => {
  const projectsData = [];
  //   await getBulkOpportunities('project', {"ids":dataIds})
  // await dummyData()
  await fetchCarouselData(payload)
    .then(res => {
      res?.forEach(project => {
        const {
          id,
          isBookmarked,
          isDismissed,
          isApplicationRequired,
          locations,
          maxPositions,
          positionsFilled,
          requireApplication,
          remoteWorkPossible,
          thumbnail,
          title,
          divisions
        } = project;

        projectsData.push({
          dismissable: false,
          key: id,
          defaultBookmarked: isBookmarked,
          defaultDismissed: isDismissed,
          id,
          isApplicationRequired: isApplicationRequired || requireApplication,
          isRemotePossible: remoteWorkPossible,
          locations,
          maxPositions,
          originalData: project,
          positionsFilled,
          thumbnail,
          title,
          divisions
        });
      });
    })
    .catch(err => {
      console.error(err);
    });
  return projectsData;
};

// Utility function for Mentorships to fetch data and convert it in the form required by the card component
const getMentorshipData = async payload => {
  const mentorshipData = [];
  //   await getBulkOpportunities('mentor', {"ids":dataIds})
  // await dummyMentorshipData()
  await fetchCarouselData(payload)
    .then(res => {
      res?.forEach(mentor => {
        const { id, userId: user, application_approval_status, application_status } = mentor;

        const skills = mentor.skillDetails || mentor.skillsDetail;

        mentorshipData.push({
          key: id,
          name: user?.fullName,
          id: id,
          skills: skills,
          position: unescape(user?.profile?.jobTitle),
          handle: user?.handle,
          avatarUrl: user?.avatarimages?.medium,
          userId: user?.id,
          originalData: mentor,
          status:
            application_status === MENTORSHIP_STATUS.COMPLETED
              ? MENTORSHIP_STATUS.COMPLETED
              : application_approval_status
        });
      });
    })
    .catch(err => {
      console.error(err);
    });
  return mentorshipData;
};

// Utility function for Channel to fetch data and convert it in the form required by the card component
const getChannelData = async (payload, type) => {
  const channelData = [];
  await fetchCarouselData(payload).then(res => {
    res.forEach(card => {
      channelData.push({
        channelData: card,
        cardType: type === 'photo' ? 'vertical' : 'default'
      });
    });
  });
  return channelData;
};

// Utility function for Content to fetch data and convert it in the form required by the card component
const getContentata = async payload => {
  const contentData = [];
  await fetchCarouselData(payload).then(res => {
    res.forEach((card, key) => {
      contentData.push({
        key,
        card,
        blankAlt: true
      });
    });
  });
  return contentData;
};

// Utility function for People to fetch data and convert it in the form required by the card component
const getPeopleData = async payload => {
  const peopleData = [];
  await fetchCarouselData(payload).then(res => {
    res.forEach(user => {
      peopleData.push({
        user
      });
    });
  });
  return peopleData;
};

export const CAROUSEL_DATA_MAP = {
  card: {
    getData: getContentata,
    component: { standard: CardWrapper },
    componentSm: ContentList
  },
  project: {
    getData: getProjectData,
    component: { standard: ProjectCard },
    componentSm: ProjectList
  },
  mentorship: {
    getData: getMentorshipData,
    component: { standard: DiscoverMentorshipCard },
    componentSm: MentorsList
  },
  channel: {
    getData: getChannelData,
    component: { standard: ChannelCardWrapper },
    componentSm: ChannelList
  },
  user: {
    getData: getPeopleData,
    component: { standard: UserCardWrapper },
    componentSm: UsersList
  },
  team: {
    getData: getGroupsData,
    component: { standard: GroupCardWrapper },
    componentSm: GroupsList
  }
};

export const getCarouselDescription = carouselData => {
  const currentSelectedLanguage = window?.__ED__?.profile.language;
  const descriptionInfo = carouselData?.descriptionInfo;
  if (!descriptionInfo.visible) return null;
  if (!currentSelectedLanguage || !(currentSelectedLanguage in descriptionInfo.languages)) {
    return descriptionInfo.languages[descriptionInfo.defaultLanguage].description;
  } else {
    return descriptionInfo.languages[currentSelectedLanguage].description;
  }
};

export const getCarouselTitle = carouselData => {
  const currentSelectedLanguage = window?.__ED__?.profile.language;
  const titleInfo = carouselData?.titleInfo;
  if (!currentSelectedLanguage || !(currentSelectedLanguage in titleInfo.languages)) {
    return titleInfo.languages[titleInfo.defaultLanguage].title;
  } else {
    return titleInfo.languages[currentSelectedLanguage].title;
  }
};
