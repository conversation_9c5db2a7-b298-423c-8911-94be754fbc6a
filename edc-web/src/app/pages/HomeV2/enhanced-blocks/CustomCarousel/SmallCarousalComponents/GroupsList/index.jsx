import React, { useState } from 'react';
import './styles.scss';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { leaveFromGroup, joinToGroup } from 'edc-web-sdk/requests/groups.v2';
import { translatr } from 'centralized-design-system/src/Translatr';
import PropTypes from 'prop-types';

const GroupsListItem = ({ group }) => {
  const navigate = useNavigate();

  const isPartOfGroup =
    group.isMember || group.isTeamAdmin || group.isTeamSubAdmin || group.isTeamModerator;

  const [loading, setLoading] = useState(false);
  const [isMember, setIsMember] = useState(isPartOfGroup);
  const [groupMembersCount, setGroupMembersCount] = useState(group.membersCount);

  const goToStandAloneView = () => {
    navigate(`/teams/${group.slug}`);
  };

  const handleJoin = () => {
    setLoading(true);
    joinToGroup(group.id)
      .then(() => {
        setIsMember(true);
        setGroupMembersCount(prev => prev + 1);
        setLoading(false);
      })
      .catch(err => {
        console.error(err);
        setLoading(false);
      });
  };

  const handleLeave = () => {
    setLoading(true);
    leaveFromGroup(group.id)
      .then(() => {
        setIsMember(false);
        setGroupMembersCount(prev => prev - 1);
        setLoading(false);
      })
      .catch(err => {
        console.error(err);
        setLoading(false);
      });
  };

  const handleClick = e => {
    e.stopPropagation();
    if (isMember) {
      handleLeave();
    } else {
      handleJoin();
    }
  };

  const membersLabel =
    groupMembersCount === 1
      ? translatr('cds.common.main', 'Member')
      : translatr('cds.common.main', 'Members');

  return (
    <div role="listitem" key={group.id}>
      <div className="carousel-list-container-item">
        <div className="thumbnail">
          <img className="thumbnail-img" src={group.imageUrls.medium} alt="" />
        </div>
        <div
          className="meta-wrapper"
          role="button"
          onClick={goToStandAloneView}
          tabIndex="0"
          onKeyDown={e => e.key === 'Enter' && goToStandAloneView()}
        >
          <div className="container-item-meta">
            <h5>{group.name}</h5>
            <span>
              {groupMembersCount} {membersLabel}{' '}
            </span>
          </div>
          <div className="cta">
            <button
              onClick={e => handleClick(e)}
              disabled={loading || group.isEveryoneTeam || group.isPrivate}
            >
              {isMember ? <i className="icon-check" /> : <i className="icon-plus-circle" />}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const GroupsList = ({ data }) => {
  return (
    <div className="ed-ui carousel-list-container">
      {data.map(({ group }) => {
        return <GroupsListItem group={group} />;
      })}
    </div>
  );
};

GroupsList.propTypes = {
  data: PropTypes.array
};

GroupsListItem.propTypes = {
  group: PropTypes.object
};

export default connect(null, dispatch => ({
  dispatch
}))(GroupsList);
