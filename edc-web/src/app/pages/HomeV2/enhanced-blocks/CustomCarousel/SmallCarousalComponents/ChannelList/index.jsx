import React, { useState } from 'react';
import './styles.scss';
import { connect } from 'react-redux';
import { unfollow, follow } from 'edc-web-sdk/requests/channels.v2';
import { saveSearchTabsState } from 'centralized-design-system/src/Utils/Actions/searchActions';
import { UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID } from '../../../../../../../app/constants/actionTypes';
import { snackBarOpenClose } from '../../../../../../../app/actions/channelsActionsV2';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';

const ChannelListItem = ({ channel, key, handleToggle }) => {
  const { label, bannerImageUrls, isFollowing, profileImageUrls } = channel;
  const [isFollowingChannel, setIsFollowingChannel] = useState(isFollowing);
  const navigate = useNavigate();

  function redirectToChannelPage(slug) {
    navigate(`/channel/${slug}`);
  }

  return (
    <div role="listitem" key={key}>
      <div className="carousel-list-container-item">
        <div className="thumbnail">
          <img
            className="thumbnail-img"
            src={profileImageUrls?.small_url || bannerImageUrls.small_url}
            alt=""
          />
        </div>
        <div
          className="meta-wrapper"
          role="button"
          onClick={() => redirectToChannelPage(channel.slug)}
          tabIndex="0"
          onKeyDown={e => e.key === 'Enter' && redirectToChannelPage(channel.slug)}
        >
          <div className="container-item-meta">
            <h5>{label}</h5>
          </div>
        </div>
        <div className="cta">
          <button
            onClick={e => handleToggle(e, channel, isFollowingChannel, setIsFollowingChannel)}
          >
            {isFollowingChannel ? <i className="icon-check" /> : <i className="icon-plus-circle" />}
          </button>
        </div>
      </div>
    </div>
  );
};

const ChannelList = ({ data, dispatch }) => {
  const handleToggle = (e, channel, isFollowing, cb) => {
    if (isFollowing) {
      unfollow(channel.id)
        .then(() => {
          const updatedObject = {
            id: channel.id,
            updatedProperty: 'isFollowing',
            updatedValue: false
          };
          dispatch({
            type: UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID,
            payload: { ...updatedObject }
          });
          dispatch(
            saveSearchTabsState({
              tab: 'channel',
              tabType: 'channels',
              typeId: channel.id,
              property: 'isFollowing',
              value: false
            })
          );
          cb(prev => !prev);
        })
        .catch(err => {
          dispatch(snackBarOpenClose(err.message, 3000));
          console.error(`Error in ChannelCarousels handleToggle.unfollow: ${err}`);
        });
    } else {
      follow(channel.id)
        .then(() => {
          const channelID = channel?.id;
          const updatedObject = {
            id: channelID,
            updatedProperty: 'isFollowing',
            updatedValue: true
          };
          dispatch({
            type: UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID,
            payload: { ...updatedObject }
          });
          dispatch(
            saveSearchTabsState({
              tab: 'channel',
              tabType: 'channels',
              typeId: channelID,
              property: 'isFollowing',
              value: true
            })
          );
          cb(prev => !prev);
        })
        .catch(err => {
          console.error(`Error in ChannelCarousels handleToggle.follow func ${err}`);
        });
    }
  };

  return (
    <div className="ed-ui carousel-list-container">
      {data.map((dataItem, i) => {
        return (
          <ChannelListItem channel={dataItem.channelData} key={i} handleToggle={handleToggle} />
        );
      })}
    </div>
  );
};

ChannelListItem.propTypes = {
  channel: PropTypes.object,
  key: PropTypes.string,
  handleToggle: PropTypes.func
};

ChannelList.propTypes = {
  data: PropTypes.array
};

export default connect(null, dispatch => ({
  dispatch
}))(ChannelList);
