import React from 'react';
import GroupChannelCard from 'centralized-design-system/src/GroupChannelCard/GroupChannelCard';
import { Translatr, translatr } from 'centralized-design-system/src/Translatr';
import { useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { snackBarOpenClose } from '../../../../../../app/actions/channelsActionsV2';

const GroupCardWrapper = ({ group, dispatch }) => {
  const navigate = useNavigate();
  const goToStandAloneView = transitionUrl => {
    navigate(transitionUrl);
  };
  const snackbarErrorHandler = error => dispatch(snackBarOpenClose(error, 3000));

  const isPartOfGroup =
    group.isMember || group.isTeamAdmin || group.isTeamSubAdmin || group.isTeamModerator;
  const isGroupAccessible = isPartOfGroup || !group.isPrivate;
  return (
    <GroupChannelCard
      key={group.id}
      group={group}
      groupOrChannelId={group.id}
      tagName={translatr('web.group.main', 'Group')}
      cardImage={group.imageUrls.medium}
      title={group.name}
      userCount={group.membersCount}
      imageAltText={group.imageAltText || ''}
      transitionUrl={`/teams/${group.slug}`}
      isPrivate={group.isPrivate}
      goToStandAloneView={goToStandAloneView}
      isUserIsPartOfGroupOrChannel={isPartOfGroup}
      isInvitationPending={group.isPending}
      isMandatory={group.isMandatory}
      isGroupAccessible={isGroupAccessible}
      snackbarErrorHandler={snackbarErrorHandler}
      isEveryoneTeam={group.isEveryoneTeam}
    />
  );
};

GroupCardWrapper.propTypes = {
  group: PropTypes.object
};

export default connect()(GroupCardWrapper);
