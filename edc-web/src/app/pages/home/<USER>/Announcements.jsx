import React, { useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useEffectAfterInitialMount } from 'centralized-design-system/src/Utils/hooks';

import { translatr } from 'centralized-design-system/src/Translatr';
import { isHTML } from '../../../../app/utils/index';
import { EmptyCard } from 'centralized-design-system/src/Widgets';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import AccessibleSlider from '../../../../app/components/AccessibleSlider';

import PlaypauseButton from './PlayPauseButton';
import useGetAnnouncements from '../hooks/useGetAnnouncements';
import LD from '../../../../app/containers/LDStore';
import WidgetSkeletonLoader from './WidgetSkeletonLoader';

export const Announcements = props => {
  const { announcements, annoucementsLoading } = useGetAnnouncements({ orgId: props.orgId });

  const sortTextAnnouncements = LD.annoncementSortPlainText();
  const [isCarouselPaused, setIsCarouselPaused] = useState(false);
  const [announcementsLabel, setAnnouncementsLabel] = useState(props.label || 'Announcements');
  const carouselRef = useRef();

  useEffectAfterInitialMount(() => {
    isCarouselPaused ? carouselRef.current?.slickPause?.() : carouselRef.current?.slickPlay?.();
    getLabel();
  }, [isCarouselPaused]);

  function getAnnouncements() {
    let lists = announcements || [];
    if (sortTextAnnouncements) {
      return lists.sort((a, b) =>
        sortTextAnnouncements === 'first'
          ? isHTML(a.label) - isHTML(b.label)
          : isHTML(b.label) - isHTML(a.label)
      );
    }
    return lists;
  }

  function getLabel() {
    try {
      const leftRailConfig = window.__ED__.organization.OrgConfig?.web?.leftRail;
      const label = leftRailConfig['web/leftRail/announcements']?.label || '';
      if (!!label?.trim()?.length) {
        setAnnouncementsLabel(label);
      }
    } catch (e) {}
  }
  function renderAnnouncements() {
    const lists = getAnnouncements();

    const settings = {
      slidesToShow: 1,
      autoplay: lists.length > 1,
      autoplaySpeed: 6000,
      infinite: lists.length > 1,
      arrows: false
    };

    return (
      <div className={`announcement-list-item block`}>
        <div className="announcement-label mb-12">
          <h2
            id="announcements"
            className="font-bold ed-text-color font-size-xl"
            title={props.label}
          >
            {announcementsLabel}
          </h2>
        </div>
        <AccessibleSlider ref={carouselRef} {...settings}>
          {lists.map((announcement, i) => {
            const domOrText = (document.createElement('div').innerHTML = announcement.label);
            const announcementStyleString = `${
              isHTML(announcement.label) ? 'html' : 'plaintext'
            }-announcement`;

            return (
              <div
                className={`${announcementStyleString}`}
                key={`announcement-container-${announcement.id || i}`}
              >
                <div dangerouslySetInnerHTML={{ __html: safeRender(domOrText) }} />
              </div>
            );
          })}
        </AccessibleSlider>

        {lists.length > 1 && (
          <PlaypauseButton
            showPlayIcon={isCarouselPaused}
            btnClickhandler={() => setIsCarouselPaused(!isCarouselPaused)}
          />
        )}
      </div>
    );
  }

  return (
    <section aria-labelledby="announcements">
      {annoucementsLoading ? (
        <WidgetSkeletonLoader
          config={{
            title: [120, 18],
            descriptionFirstBlock: [240, 14],
            descriptionSecondBlock: [150, 14]
          }}
        />
      ) : announcements?.length > 0 ? (
        renderAnnouncements()
      ) : (
        <EmptyCard
          title={announcementsLabel}
          description={translatr(
            'web.home.main',
            'NothingInYourQueueYetYourAnnouncementsWillAppearHere'
          )}
          headingId="announcements"
        />
      )}
    </section>
  );
};

Announcements.propTypes = {
  announcements: PropTypes.array,
  label: PropTypes.string,
  orgId: PropTypes.number
};
