import React, { useEffect, useState, useCallback, useRef } from 'react';
import { connect } from 'react-redux';
import { array, string, object } from 'prop-types';
import { NavLink } from 'react-router-dom';
import {
  getProfileLanguage,
  getNewTranslatedLabel
} from '../../../../app/components/common/TranslatedLabel';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { translatr } from 'centralized-design-system/src/Translatr';
import { updateUser } from 'edc-web-sdk/requests/users';
import { getFeedLanguages } from 'edc-web-sdk/requests/orgSettings.v2';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { isRtl } from '../../../../app/utils/locale';
import classNames from 'classnames';
import { SELECTED_LANGUAGE } from '../../../../app/constants/localStorageConstants';
import { FilterHostname } from '../../../../app/components/common/FilterHostname';

const HomeTabsContext = React.createContext({});
HomeTabsContext.letterSpacing = 'normal';
HomeTabsContext.wordSpacing = '0px';
HomeTabsContext.previousTabs = [];
HomeTabsContext.isPreviousTabsSet = false;

const HomeTabs = ({ tabs, feedMap, allLangs, currentUserLang, profileLanguageName }) => {
  const windowSize = useWindowSize();
  const isLargeScreen = windowSize.width > 991;
  const [menuOpen, setMenuOpen] = useState(!isLargeScreen);
  const [subMenu, setSubMenu] = useState([]);
  const [menu, setMenu] = useState(tabs);
  const homeTabsWraper = useRef();
  const [showSubMenu, setShowSubMenu] = useState(false);
  const [hideMenuTooltip, setHideMenuTooltip] = useState({});
  const [userDefaultLanguage, setUserDefaultLanguage] = useState(currentUserLang);
  const [isLoading, setIsLoading] = useState(true);

  function textSpacingChanged() {
    const computedStyle = window.getComputedStyle(document.body);

    // Access the letter-spacing and word-spacing properties
    const letterSpacing = computedStyle?.getPropertyValue('letter-spacing');
    const wordSpacing = computedStyle?.getPropertyValue('word-spacing');
    const spacingUsed = letterSpacing !== 'normal' || wordSpacing !== '0px';
    const spacingReverted = letterSpacing === 'normal' && wordSpacing === '0px';
    let updatedTabs = [];
    let subTabs = [];
    const tabsContainer = document.querySelector('.swim-wrapper')?.getBoundingClientRect();
    const moreContainer = document.querySelector('.sub-menu-dropdown')?.getBoundingClientRect();
    const bodyWidth = document.body.clientWidth;
    const tabsContainerPosition = isRtl() ? bodyWidth - tabsContainer?.left : tabsContainer?.right;
    const moreContainerPosition = isRtl() ? bodyWidth - moreContainer?.left : moreContainer?.right;

    if (
      isLargeScreen &&
      spacingUsed &&
      (letterSpacing !== HomeTabsContext.letterSpacing ||
        wordSpacing !== HomeTabsContext.wordSpacing ||
        moreContainerPosition >= tabsContainerPosition)
    ) {
      HomeTabsContext.letterSpacing = letterSpacing;
      HomeTabsContext.wordSpacing = wordSpacing;
      const parentElement = homeTabsWraper.current;
      const allChild = parentElement?.children;

      // Loop over all elements and check which one is over the width for non-subMenus
      for (let i = 0; i < allChild?.length; i++) {
        const individualChild = allChild[i]?.getBoundingClientRect();
        const individualChildPosition = isRtl()
          ? bodyWidth - individualChild?.left
          : individualChild?.right;
        if (individualChildPosition < tabsContainerPosition) {
          updatedTabs.push(tabs[i]);
        }
        if (!HomeTabsContext.isPreviousTabsSet && allChild[i].className !== 'header-sub-menu') {
          HomeTabsContext.previousTabs.push(tabs[i]);
        }
      }

      // Ensure to make room for the "More" menu container.
      if (moreContainerPosition >= tabsContainerPosition) {
        let subTab = updatedTabs.pop();
        subTabs.unshift(subTab);
      }

      // Using the given menu, check which would be the subMenus/subTabs
      subTabs = menu.filter(m => !updatedTabs.includes(m));
      setMenu(updatedTabs);
      setSubMenu(subTabs);
    } else if (
      spacingReverted &&
      (letterSpacing !== HomeTabsContext.letterSpacing ||
        wordSpacing !== HomeTabsContext.wordSpacing) &&
      HomeTabsContext.previousTabs.length &&
      HomeTabsContext.isPreviousTabsSet
    ) {
      HomeTabsContext.letterSpacing = letterSpacing;
      HomeTabsContext.wordSpacing = wordSpacing;
      // Using the given menu, check which would be the subMenus/subTabs
      updatedTabs = Object.assign([], HomeTabsContext.previousTabs);
      subTabs = menu.filter(m => !updatedTabs.includes(m));
      setMenu(updatedTabs);
      setSubMenu(subTabs);
    }

    if (!HomeTabsContext.isPreviousTabsSet && HomeTabsContext.previousTabs.length)
      HomeTabsContext.isPreviousTabsSet = true;
  }

  useEffect(() => {
    // Add the monitor
    const intervalId = setInterval(textSpacingChanged, 3000);

    // Release interval
    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    if (isLargeScreen) {
      document.addEventListener('click', clickHandler);
    }
    return () => {
      document.removeEventListener('click', clickHandler);
    };
  }, [menuOpen]);

  useEffect(() => {
    document.title = FilterHostname(translatr('web.home.main', 'Homepage'));
  }, []);

  const clickHandler = useCallback(() => {
    if (menuOpen) {
      setMenuOpen(false);
    }
  }, [menuOpen]);

  const moreClassHandler = () => {
    let active = false;
    subMenu.map(tab => {
      if (tab.path.includes(window.location.pathname)) {
        active = true;
      }
    });
    return active;
  };

  const renderTabs = (tabArray, isMenu = false) => {
    return tabArray?.map((tab, index) => {
      if (tab.key?.indexOf('submission') > 0) {
        return;
      }
      let href = feedMap[tab.key];
      let isActiveTab = false;

      // This occurs if it's a custom route
      if (!href && tab.href) {
        href = tab.href;
      }
      if (window.location.pathname !== '/' && window.location.pathname.indexOf('feed') === -1) {
        href = href?.replace('/feed', '');
      }

      if (index === 0 && isMenu && ['/', '/feed', '/home'].includes(window.location.pathname)) {
        isActiveTab = true;
      }

      if (tab.defaultLabel == "Today's Insights") {
        tab.defaultLabel = 'Todays Insights';
      }

      let translatedLabel = getNewTranslatedLabel({
        labelObj: tab,
        appName: 'web.home.main',
        profileLanguage: profileLanguageName
      });

      const label = tab.label ? translatedLabel : translatedLabel?.toUpperCase() || '';

      const tooltipObj = tab['languages']?.tooltip;
      const labelObj = tab['languages']?.label || {};

      let _tooltip = tab?.tooltip || tooltipObj?.en;
      let _label = label;
      if (userDefaultLanguage !== 'en') {
        _tooltip = tooltipObj ? tooltipObj[userDefaultLanguage] : undefined;
        _label = labelObj[userDefaultLanguage] || label;
      }
      return _tooltip && !isLoading ? (
        <div
          onMouseEnter={() => setHideMenuTooltip({ ...hideMenuTooltip, [index]: false })}
          onMouseLeave={() => setHideMenuTooltip({ ...hideMenuTooltip, [index]: true })}
        >
          <NavLink key={tab?.key} className={isActiveTab ? 'active' : ''} to={href || ''}>
            <Tooltip message={_tooltip || ''} hide={hideMenuTooltip?.[index]}>
              {_label}
            </Tooltip>
          </NavLink>
        </div>
      ) : (
        <NavLink
          className={`header-sub-menu ${isActiveTab ? 'active' : ''}`}
          key={tab?.key}
          to={href || ''}
        >
          {label}
        </NavLink>
      );
    });
  };

  const updateUserLanguage = async () => {
    const selectedLanguages = Object.values(allLangs);
    if (!selectedLanguages.includes(userDefaultLanguage)) {
      let theLanguage =
        window.__edOrgData.configs.filter(c => c.name === 'DefaultOrgLanguage')[0]?.value || '';
      if (!selectedLanguages.includes(theLanguage)) {
        theLanguage = 'en';
      }

      const userObj = {
        profile_attributes: { language: theLanguage }
      };
      const resp = await updateUser(window.__ED__.id, userObj).catch(err => {
        console.error('Error: Unable to update users language', err);
      });

      if (resp) {
        setUserDefaultLanguage(theLanguage);
        window.localStorage.setItem(SELECTED_LANGUAGE, theLanguage);
        window.location.reload();
      }
    }
  };

  const updateFeedTooltips = async () => {
    const orgId = window.__edOrgData.configs.find(item => item.name === 'OrgCustomizationConfig')
      ?.id;
    const feedPayload = {
      entity_id: orgId,
      entity_type: 'Config',
      dynamic_field: true,
      field_names: [],
      languages: [userDefaultLanguage],
      status: 'active'
    };
    const feedLangs = await getFeedLanguages(feedPayload).catch(err => {
      setIsLoading(false);
      const errMsg = err.message || err.toString() || '';
      console.error(errMsg);
    });

    if (feedLangs) {
      const feedLangObj = {};
      feedLangs.forEach(lang => {
        const { entity_field, entity_field_languages } = lang;
        const feedPaths = entity_field.field_name.split('.');

        if (!entity_field_languages?.length || feedPaths?.length !== 3 || feedPaths[0] !== 'feed') {
          setIsLoading(false);
          return;
        }
        entity_field_languages.forEach(item => {
          const { language, value } = item;
          if (feedLangObj.hasOwnProperty(feedPaths[1])) {
            feedLangObj[feedPaths[1]] = {
              ...feedLangObj[feedPaths[1]],
              [feedPaths[2]]: {
                ...(feedLangObj[feedPaths[1]][feedPaths[2]] || {}),
                [language]: value
              }
            };
          } else {
            feedLangObj[feedPaths[1]] = {
              [feedPaths[2]]: { [language]: value }
            };
          }
        });
      });

      if (Object.keys(feedLangObj).length) {
        tabs.forEach(item => {
          if (feedLangObj.hasOwnProperty(item.key)) {
            item.languages = {
              ...item.languages,
              ...feedLangObj[item.key]
            };
          }
        });
      }
    }
    setIsLoading(false);
  };

  useEffect(() => {
    updateUserLanguage();
    updateFeedTooltips();
    const parentElement = homeTabsWraper.current;
    // 536px is the max width for homeTabsWraper
    // Check is homeTabsWraper overflow the 536px width
    if (parentElement.scrollWidth > 536 && isLargeScreen) {
      const bodyWidth = document.body.clientWidth;
      setShowSubMenu(true);
      const allChild = parentElement?.children;
      let updatedTabs = [];
      let subTabs = [];
      const tabsContainer = document.querySelector('.swim-wrapper')?.getBoundingClientRect();
      const tabsContainerPosition = isRtl()
        ? bodyWidth - tabsContainer?.left
        : tabsContainer?.right;
      // loop over all elements to find creates subMenu and update menu
      for (let i = 0; i < allChild?.length; i++) {
        const individualChild = allChild[i]?.getBoundingClientRect();
        const individualChildPosition = isRtl()
          ? bodyWidth - individualChild?.left
          : individualChild?.right;
        // condition to check is element lies outside of tabs container
        if (individualChildPosition > tabsContainerPosition) {
          subTabs.push(tabs[i]);
        }
        if (individualChildPosition < tabsContainerPosition) {
          updatedTabs.push(tabs[i]);
        }
      }
      setMenu(updatedTabs);
      setSubMenu(subTabs);
    }
  }, []);

  useEffect(() => {
    if (subMenu.length > 0 && isLargeScreen) {
      const bodyWidth = document.body.clientWidth;
      const subMenuDropdown = document.querySelector('.sub-menu-dropdown')?.getBoundingClientRect();
      const subMenuDropdownPosition = isRtl()
        ? bodyWidth - subMenuDropdown?.left
        : subMenuDropdown?.right;
      const tabsContainer = document.querySelector('.swim-wrapper')?.getBoundingClientRect();
      const tabsContainerPosition = isRtl()
        ? bodyWidth - tabsContainer?.left
        : tabsContainer?.right;
      // condition to check  is subMenu Button lies outside Tabs container
      if (subMenuDropdownPosition > tabsContainerPosition) {
        setSubMenu(prevState => [menu[menu.length - 1], ...prevState]);
        setMenu(prevState => prevState.slice(0, prevState.length - 1));
      }
    }
  }, [showSubMenu]);

  return (
    <div className={classNames('block swim-wrapper', { justflex: !subMenu.length })}>
      <div className="swim-lanes" ref={homeTabsWraper}>
        {renderTabs(menu, true)}
        {subMenu.length > 0 && (
          <label className={classNames('header-sub-menu', { active: moreClassHandler() })}>
            {windowSize.width > 991 && (
              <span
                onClick={e => {
                  e.stopPropagation();
                  if (isLargeScreen) {
                    setMenuOpen(true);
                  }
                }}
                className="sub-menu-dropdown"
                onKeyDown={e => {
                  if (e.key === 'Enter' && isLargeScreen) {
                    setMenuOpen(true);
                  }
                }}
                role="button"
                tabIndex="0"
                aria-expanded={menuOpen}
              >
                {translatr('web.home.main', 'More')}
                <i className="icon-caret-down-arrow"></i>
              </span>
            )}
            {menuOpen && <div className="header-sub-menu_wrapper block">{renderTabs(subMenu)}</div>}
          </label>
        )}
      </div>
    </div>
  );
};

HomeTabs.propTypes = {
  tabs: array,
  feedMap: object,
  allLangs: array,
  currentUserLang: string,
  profileLanguageName: string
};

const mapStateToProps = ({ team, currentUser }) => {
  const userProfile = currentUser.get('profile');
  const profileLanguageName = getProfileLanguage({
    langs: team.get('languages'),
    currentUserLang: userProfile?.get?.('language')
  });

  return {
    allLangs: team.get('languages'),
    currentUserLang: userProfile?.get?.('language'),
    profileLanguageName
  };
};

export default connect(mapStateToProps)(HomeTabs);
