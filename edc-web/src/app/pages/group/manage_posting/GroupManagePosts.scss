@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui.group-manage-posts {
  max-width: 75rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.group-manage-posts_header-container {
  margin: 0 0 rem-calc(16);
  display: flex;
  align-items: center;
  gap: 0 rem-calc(20);

  h2 {
    color: var(--ed-gray-7);
    font-size: var(--ed-font-size-lg) !important;
    font-weight: var(--ed-font-weight-semibold);
    line-height: rem-calc(27);
    margin: 0;
  }
}

.group-manage-posts_body {
  margin: var(--ed-spacing-base) 0;
}

.group-manage-posts_back-button {
  color: var(--ed-gray-6);
  cursor: pointer;

  .icon-arrow-left {
    font-size: var(--ed-font-size-lg);
  }
}

.group-manage-posts_access-denied-container {
  .access-denied-container {
    border: unset;
  }
}

.group-manage-posts_tab-container {
  display: flex;
  flex-direction: column;
  gap: var(--ed-spacing-2xs);
  margin-top: rem-calc(24);
}

@media (max-width: $breakpoint-xs) {
  .group-manage-posts_tab-container {
    .group-manage-posts_tab-header {
      flex-direction: column;

      .group-manage-posts_tab-header-selectors {
        flex-direction: column;
        margin-bottom: rem-calc(8);
      }
    }
  }
}

.manage-posts-link {
  font-size: var(--ed-font-size-sm);
  font-weight: 700;
  color: var(--ed-primary-base);

  &:hover {
    cursor: pointer;
  }
}
