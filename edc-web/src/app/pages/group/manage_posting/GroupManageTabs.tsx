import React, { useEffect, useState } from 'react';
import Tab from 'centralized-design-system/src/TabBar/SimpleTabs';
import TopicTab from './Topic/TopicTab';
import PostsTab from './Post/PostsTab';
import ReportedContentTab from './ReportedContent/ReportedContentTab';
import { useGroupManagePostsContext } from './GroupManagePostsProvider';
import { translate } from './utils';
import AccessDenied from '@pages/AccessDenied';
import { TabKey } from './types';

const GroupManageTabs = () => {
  const { groupManagePosts: { visibleTabs, couldManagePosts, activeTab }, tabChanged } = useGroupManagePostsContext();

  if(!couldManagePosts) {
    return (
      <div className="group-manage-posts_access-denied-container">
        <AccessDenied />
      </div>
    );
  }

  // This is required because Tab component is not re-rendering when active props changes,
  // so we need to force re-render the component when searchParams changes using such hack
  const [forceRender, setForceRender] = useState(0);
  useEffect(() => {
    setForceRender(prev => prev + 1);
  }, [activeTab]);

  const tabs: Array<{ key: TabKey, label: string, visible: boolean, component: React.ReactNode }> = [
    {
      key: 'topics',
      label: translate('TopicsTabLabel').toUpperCase(),
      visible: visibleTabs.topics,
      component: <TopicTab />,
    },
    {
      key: 'posts',
      label: translate('PostsTabLabel').toUpperCase(),
      visible: visibleTabs.posts,
      component: <PostsTab /> },
    {
      key: 'reported-content',
      label: translate('ReportedContentTabLabel').toUpperCase(),
      visible: visibleTabs.reportedContent,
      component: <ReportedContentTab />
    }
  ];

  const updateUrlWithCurrentTab = (tabLabel: string) => {
    tabChanged({
      selectedTab: tabs
        .filter(tab => tab.visible)
        .find(tab => tab.label === tabLabel)?.key ?? null
    });
  };

  const activeTabIndex = tabs
    .filter(tab => tab.visible)
    .findIndex(tab => tab.key === activeTab);

return (
    <Tab
      key={forceRender}
      OnTabClickCB={(tab) => updateUrlWithCurrentTab(tab)}
      active={activeTabIndex !== -1 ? activeTabIndex : 0}
    >
      {tabs
        .filter(tab => tab.visible)
        .map(tab => (
          <Tab.TabPane
            key={tab.key}
            tab={tab.label}
          >
            <div className='group-manage-posts_tab-container'>
              {tab.component}
            </div>
          </Tab.TabPane>
        ))}
    </Tab>
  );
};

export default GroupManageTabs;
