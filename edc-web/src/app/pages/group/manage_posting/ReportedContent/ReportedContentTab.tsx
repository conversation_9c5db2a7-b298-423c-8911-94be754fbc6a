import React, { useEffect, useState } from 'react';
import ManageTable from '../Components/Table/ManageTable';
import RowActions from '../Components/RowActions';
import { ReportedContent, ReportedContentStatus, SortOrder, TableHeaderElement } from '../types';
import { useGroupManagePostsContext } from '../GroupManagePostsProvider';
import { translate } from '../utils';
import { useReportedContent } from './useReportedContent';
import { useTopics } from '@pages/group/manage_posting/Topic/useTopics';
import PostCard from '@pages/group/manage_posting/Post/PostCard/PostCard';
import {
  FOCUS_POST_ID_QUERY_PARAM
} from '@pages/group/consumption/GroupPosting/utils';
import { useNavigate } from 'react-router-dom';
import {
  dismissCommunityPost,
  trashCommunityPost
} from 'edc-web-sdk/requests/communityPostReportings';
import { unreportAComment } from 'edc-web-sdk/requests/comments';

const RECORDS_PER_PAGE = 9;

const reportedContentNavigationElement: Array<{
  value: ReportedContentStatus;
  label: string;
}> = [
  { value: 'reported', label: translate('ReportedNavigation') },
  { value: 'trashed', label: translate('TrashedNavigation') }
];

const ReportedContentTab = () => {
  const navigate = useNavigate();
  const { groupManagePosts: { groupId, slug }, openSnackbar } = useGroupManagePostsContext();
  const { reportedContent, loadReportedContent } = useReportedContent(groupId);
  const { topics, loadTopics } = useTopics(groupId);

  const [queryData, setQueryData] = useState({
    selectedTopic: null as string,
    statusFilter: "reported" as ReportedContentStatus,
    sort: null as string,
    order: null as string,
    offset: 0 as number,
    limit: RECORDS_PER_PAGE as number,
  });

  useEffect(() => {
    loadContent()
  }, [queryData]);

  useEffect(() => {
    if (reportedContent.data.length === 0 && queryData.offset > 0) {
      setQueryData((qd) => ({
        ...qd,
        offset: queryData.offset - RECORDS_PER_PAGE
      }))
    }
  }, [reportedContent.data]);

  useEffect(() => {
    loadTopics("all", "creation_date_desc", 0);
  }, []);

  const loadContent = () => {
    loadReportedContent({
      topic: queryData.selectedTopic,
      status: queryData.statusFilter,
      sort: queryData.sort,
      order: queryData.order,
      offset: queryData.offset,
      limit: queryData.limit,
    });
  }

  const topicSelectItems: Array<{ value: string; label: string }> = [
    { value: 'all', label: translate('SelectTopicsDefaultItem') },
    ...(topics.data ? topics.data.map(topic => ({
      value: topic.id.toString(),
      label: topic.name
    })) : [])
  ];

  const reportedContentData = reportedContent.data.map(content => ({
    ...content,
    name: <PostCard name={content.name} type={''} onNameClick={() => onCommunityPostDetails(content.communityPostId)} />,
    actions: (
      <RowActions
        dropdownOptions={[
          {
            id: 'dismiss',
            label: translate('DismissReportedContentAction'),
            onClick: () => onReportedContentDismiss(content)
          },
          {
            id: 'delete',
            label: translate('DeleteReportedContentAction'),
            onClick: () => onReportedContentDelete(content)
          }
        ]}
      />
    )
  }));

  const onReportedContentDismiss = (obj: ReportedContent)=> {
    if (obj.recordType === 'Post') {
      let payload = {
        community_post_id: obj.communityPostId
      };
      dismissCommunityPost(payload)
        .then(() => {
          openSnackbar(
            translate('CommunityPostReportedContentDismissed'),
            'success',
            false
          )
          loadContent();
        })
    } else {
      unreportAComment(obj.id)
        .then(() => {
          openSnackbar(
            translate('CommentReportedContentDismissed'),
            'success',
            false
          )
          loadContent();
        })
    }
  }

  const onReportedContentDelete = (obj: ReportedContent)=> {
    alert('work in progress');
    //
    // if (obj.recordType === 'Post') {
    //   let payload = {
    //     community_post_id: obj.communityPostId
    //   };
    //   trashCommunityPost(payload)
    //     .then(() => {
    //       openSnackbar(
    //         translate('CommunityPostReportedContentTrashed'),
    //         'success',
    //         false
    //       )
    //       loadContent();
    //     })
    // } else {
    //   unreportAComment(obj.id)
    //     .then(() => {
    //       openSnackbar(
    //         translate('CommentReportedContentTrashed'),
    //         'success',
    //         false
    //       )
    //       loadContent();
    //     })
    // }
  }

  const onCommunityPostDetails = (communityPostId: number) => {
    navigate(`/teams/${slug}?${FOCUS_POST_ID_QUERY_PARAM}=${communityPostId}`);
  }

  const updateQueryData = (data: Partial<typeof queryData>) => {
    setQueryData((qd) => ({ ...qd, ...data }));
  };

  const onPageChange = (page: number) => {
    const newOffset = (page - 1) * RECORDS_PER_PAGE;
    if (queryData.offset != newOffset) {
      updateQueryData({ offset: newOffset });
    }
  }

  const onSortChange = (columnName: TableHeaderElement['columnId'], sortOrder: SortOrder) => {
    setQueryData((qd) => ({
      ...qd,
      sort: columnName,
      order: sortOrder
    }));
  };

  const getReportedContentHeaders = () => {
    let reportedContentHeaders: Array<TableHeaderElement> = [
      { label: translate('NameColumnName'), columnId: 'name', sortable: false, isNode: true },
      { label: translate('TopicColumnName'), columnId: 'communityTopicName', sortable: false },
      { label: translate('TypeColumnName'), columnId: 'recordType', sortable: false },
      { label: translate('AuthorColumnName'), columnId: 'userName', sortable: false },
      { label: translate('CreatedColumnName'), columnId: 'createdAt', sortable: true, isDate: true },
      { label: translate('ReasonColumnName'), columnId: 'reason', sortable: false },
      { label: translate('ReportersColumnName'), columnId: 'reportersCount', sortable: false }
    ];
    if (queryData.statusFilter === 'reported')
      reportedContentHeaders.push({ label: translate('ActionsColumnName'), columnId: 'actions', sortable: false, isNode: true })

    return reportedContentHeaders;
  }

  return (
    <div>
      <ManageTable
        filters={[
          {
            id: 'topic-select',
            value: topicSelectItems.find(item => item.value === queryData.selectedTopic)
              ?.value,
            items: topicSelectItems,
            onSelect: ({ value: selectedTopic }) => updateQueryData(({ selectedTopic })),
            label: translate('SelectTopicsLabel')
          },
          {
            id: 'reported-content-state',
            value: reportedContentNavigationElement.find(
              item => item.value === queryData.statusFilter
            )?.value,
            items: reportedContentNavigationElement,
            onSelect: ({ value: statusFilter }) => updateQueryData({ statusFilter }),
            label: translate('SelectStatusLabel')
          }
        ]}
        headers={getReportedContentHeaders()}
        data={reportedContentData}
        showError={reportedContent.error}
        loading={reportedContent.loading}
        total={reportedContent.count}
        elementsPerPage={RECORDS_PER_PAGE}
        onSort={onSortChange}
        onPageChange={onPageChange}
      />
    </div>
  );
};

export default ReportedContentTab;
