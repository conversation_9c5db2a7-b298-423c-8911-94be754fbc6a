import {
  LoadCommunityReportingsType,
  ReportedContent,
  TableError
} from '../types';
import { useReducedState } from '../hooks/useReducedState';
import { loadReportedContent } from './api';

interface ReportedContentState {
  data: Array<ReportedContent>,
  count: number,
  loading: boolean,
  error: TableError,
}
const initialState: ReportedContentState = {
  data: [],
  count: 0,
  loading: true,
  error: null,
};

const reducers = {
  reportedContentLoading: () => () => ({
    loading: true,
    error: null as TableError,
  }),
  reportedContentLoaded: (data: Array<ReportedContent>, count: number) => () => ({
    data,
    count,
    loading: false,
    error: null as TableError
  }),
  reportedContentLoadFailed: () => () => ({
    loading: false,
    error: 'general' as TableError,
  }),
};

export const useReportedContent = (groupId: number/*TODO: maybe take this from context*/) => {
  const { state, actions: {
    reportedContentLoading,
    reportedContentLoaded,
    reportedContentLoadFailed
  } } = useReducedState<ReportedContentState, typeof reducers>(reducers, initialState);

  const load = ({ topic = 'all', status = 'reported', sort, order, offset, limit, }: LoadCommunityReportingsType) => {
    reportedContentLoading();
    loadReportedContent(groupId, { topic, status, sort, order, offset, limit })
      .then(({ list, count }) => {
        reportedContentLoaded(list, count);
      })
      .catch(() => {
        reportedContentLoadFailed();
      });
  };

  return {
    reportedContent: state,
    loadReportedContent: load
  };
}
