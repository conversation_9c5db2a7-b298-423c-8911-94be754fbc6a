import {
  LoadCommunityReportingsType,
  ReportedContent
} from '../types';
import { getCommunityReportings } from 'edc-web-sdk/requests/communityReportings';

export const loadReportedContent = async (
  communityId: number,
  { topic, order, sort, offset, limit, status = "reported" } : LoadCommunityReportingsType
): Promise<{ list: Array<ReportedContent>, count: number }> => {
  const payload = { topic, status, sort, order, limit, offset };

  return getCommunityReportings(payload, communityId)
    .then(({ results, total }: { results: Array<any>, total: number }) => ({ list: results, count: total }));
};
