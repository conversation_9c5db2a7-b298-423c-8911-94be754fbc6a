import React, { ReactNode, useEffect, useState } from 'react';
import moment from 'moment';
import { Button } from 'centralized-design-system/src/Buttons';
import { Select } from 'centralized-design-system/src/Inputs';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  PostTableRow,
  ReportedContent,
  SortOrder,
  TableData,
  TableError,
  TableHeaderElement, TopicTableRow
} from '../../types';
import { Topic } from '../../../components/types';
import { translate } from '../../utils';
import TableContainer from './TableContainer';
import './ManageTable.scss';

type MobileSortItem = { value: string, label: string, header: TableHeaderElement, sort: SortOrder };
type Filters = Array<{
  id: string,
  value: string,
  items: Array<{ value: string, label: string }>,
  onSelect: (selectedItem: { value: string, label: string }) => void,
  defaultValue?: string,
  label: string
}>;

const mapDataToTableFormat = (rawData: Array<TopicTableRow | PostTableRow | ReportedContent>, headers: Array<TableHeaderElement>): Array<TableData> =>
  rawData.map((data: any) =>
    headers.map((header) => {
      const value = data[header.columnId];
      return {
        id: header.columnId,
        dataId: data.id,
        ...(header.isNode ? {
          children: value
        } : {
          label: header.isDate ? moment(value).format("DD/MM/YYYY HH:mm:ss") : (value?.toString() || '')
        }),
      };
    })
  );

const prepareMobileSortSelectItems = (headers: Array<TableHeaderElement>): Array<MobileSortItem> => {
  return headers
    .filter((header: TableHeaderElement) => header.sortable)
    .flatMap(header => {
      const ascendingLabel = (header.columnId === "created" || header.columnId === "lastUpdate") ? translate("NewestFirstSortLabel") || "Newest first" : translatr('web.common.main', 'Ascending');
      const descendingLabel = (header.columnId === "created" || header.columnId === "lastUpdate") ? translate("LatestFirstSortLabel") || "Latest first" : translatr('web.common.main', 'Descending');

      return [
        {
          value: `${header.columnId}_ASC`,
          label: `${header.label} - ${ascendingLabel}`,
          header: header,
          sort: "ASC"
        },
        {
          value: `${header.columnId}_DESC`,
          label: `${header.label} - ${descendingLabel}`,
          header: header,
          sort: "DESC"
        }
      ]
    });
}

const obtainSortValue = (sortItems: Array<MobileSortItem>, sortKey: string, sort: SortOrder) => {
  return sortItems.find((sortItem: MobileSortItem) => sortItem.header.columnId === sortKey && sortItem.sort === sort)?.value;
}

interface ManageTableProps {
  filters: Filters,
  buttons?: Array<{ id?: string, label: string, onClick: () => void }>,
  headers: Array<TableHeaderElement>,
  data: Array<TopicTableRow | PostTableRow | ReportedContent>,
  total: number,
  elementsPerPage: number,
  loading: boolean,
  onSort: (columnName: TableHeaderElement['columnId'], sortOrder: SortOrder) => void,
  onPageChange: (pageNumber: number) => void,
  showError: TableError,
  emptyComponent?: ReactNode,
  defaultSortKey?: string
}

const ManageTable: React.FC<ManageTableProps> = ({
  filters,
  buttons,
  headers,
  data,
  total,
  elementsPerPage,
  loading,
  onSort,
  onPageChange,
  showError,
  emptyComponent,
  defaultSortKey
}) => {
  const { width } = useWindowSize();
  const isMobile = width < 992;

  const [tableData, setTableData] = useState<Array<TableData>>(mapDataToTableFormat(data, headers));
  const [sortAscending, setSortAscending] = useState(false);
  const [sortKey, setSortKey] = useState(defaultSortKey || headers[0].columnId);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    setTableData(mapDataToTableFormat(data, headers));
  }, [data, headers]);

  useEffect(() => {
    onPageChange(currentPage);
  }, [currentPage]);

  const sortByColumn = (column: TableHeaderElement, sortOrder: SortOrder) => {
    if (!column.sortable) return;

    const shouldSortAscending = sortOrder === 'ASC';

    setSortAscending(shouldSortAscending);
    setSortKey(column.columnId);

    onSort(column.columnId, sortOrder);
  };

  const paginate = (resp: { event: string | number }) => {
    if (resp.event === 'next') {
      setCurrentPage(prev => prev + 1);
    } else if (resp.event === 'prev') {
      setCurrentPage(prev => Math.max(1, prev - 1));
    } else if (Number.isInteger(resp.event)) {
      setCurrentPage(resp.event as number);
    }
  };

  const prepareFilters = () => {
    if (!isMobile) {
      return filters;
    }

    if (isMobile) {
      const sortItems = prepareMobileSortSelectItems(headers);
      return [
        ...filters,
        {
          id: 'sort-table',
          value: obtainSortValue(sortItems, sortKey, sortAscending ? 'ASC' : 'DESC'),
          items: sortItems,
          onSelect: (selectedItem: MobileSortItem) =>
            sortByColumn(selectedItem.header, selectedItem.sort),
          label: translatr('web.common.main', 'SortBy2'),
        }
      ];
    }
  };

  return (
    <div className='group-manage-posts-manage-table'>
      <div className='group-manage-posts-manage-table_tab-header'>
        <div className='selectors'>
          {prepareFilters()?.map(filter => (
            <Select
              key={filter.id}
              defaultValue={filter.value}
              items={filter.items}
              onChange={filter.onSelect}
              title={filter.label}
            />
          ))}
        </div>
        {buttons?.map(button =>
          <Button id={button.id} key={button.id} color="secondary" variant="ghost" onClick={button.onClick}>
            {button.label}
          </Button>
        )}
      </div>
      <TableContainer
        isMobile={isMobile}
        loading={loading}
        tableData={tableData.slice(0, elementsPerPage)}
        headers={headers}
        sortAscending={sortAscending}
        sortKey={sortKey}
        total={total}
        elementsPerPage={elementsPerPage}
        currentPage={currentPage}
        paginate={paginate}
        showError={showError}
        emptyComponent={emptyComponent}
        sortByColumn={sortByColumn}
      />
    </div>
  );
};

export default ManageTable;
