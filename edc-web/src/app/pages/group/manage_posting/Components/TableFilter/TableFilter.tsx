import React, { useEffect, useState } from 'react';
import { Click<PERSON>wayListener, Popper } from '@mui/material';
import { translatr } from 'centralized-design-system/src/Translatr';
import { SearchInput } from 'centralized-design-system/src/Inputs';
import Checkbox from 'centralized-design-system/src/Checkbox';
import { useDebouncedState } from '../../hooks/useDebouncedState';
import './TableFilter.scss';

type Filter = { value: string | number, label: string };
interface TableFilterProps {
  anchorEl: HTMLElement,
  filters: Array<Filter>,
  excludeMode: boolean,
  onClose: (list: Array<number | string>, hasChanged: boolean) => void,
  initialSelected?: Array<number | string>,
}
const TableFilter = ({ anchorEl, filters, excludeMode = false, onClose, initialSelected = [] }: TableFilterProps) => {
  const [hasChanged, setHasChanged] = useState(false);
  const [selected, setSelected] = useState(initialSelected);
  const [displayFilters, setDisplayFilters] = useState(filters);
  const [searchQuery, setSearchQuery] = useDebouncedState<string>(null, 300);

  useEffect(() => {
    if (searchQuery === null) {
      return;
    }
    setDisplayFilters(
      filters.filter(filter => filter.label.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [searchQuery]);

  const onAllSelect = (checked: boolean) => {
    setHasChanged(true);
    setSelected((excludeMode !== checked) ? filters.map(filter => filter.value) : []);
  };

  const onSelect = (id: any, checked: boolean) => {
    setHasChanged(true);
    setSelected(alreadySelected => (excludeMode !== checked) ? [...alreadySelected, id] : alreadySelected.filter(s => s !== id));
  };

  const onClickAway = () => {
    onClose(selected, hasChanged);
  };

  const isChecked = (value: Filter['value']) => excludeMode !== selected.includes(value);
  const isAllChecked = () => excludeMode ? selected.length === 0 : filters.length === selected.length;
  const getAllCheckedNumber = () => excludeMode ? filters.length - selected.length : selected.length;

  return (
    <ClickAwayListener onClickAway={onClickAway}>
      <Popper
        open
        placement='bottom-start'
        anchorEl={anchorEl}
        style={{zIndex: 100}}
        disablePortal
      >
        <div className='table-filter'>
          <SearchInput
            //@ts-ignore
            placeholder={`${translatr('web.common.main', 'Search')}...`}
            onSearch={setSearchQuery}
          />
          <div className='table-filter-checkboxes'>
            <Checkbox
              label={`${translatr('web.common.main', 'Selected')} (${getAllCheckedNumber()})`}
              checked={isAllChecked()}
              indeterminate={filters.length > selected.length && selected.length > 0}
              onChange={({ target: { checked }}) => onAllSelect(checked)}
            />
            <div className='table-filter-separator'></div>
            {displayFilters.map(filter => (
              <Checkbox
                key={`filter-checkbox-${filter.value}`}
                label={filter.label}
                checked={isChecked(filter.value)}
                onChange={({ target: { checked }}) => onSelect(filter.value, checked)}
              />
            ))}
          </div>
        </div>
      </Popper>
    </ClickAwayListener>
  )
};

export default TableFilter;