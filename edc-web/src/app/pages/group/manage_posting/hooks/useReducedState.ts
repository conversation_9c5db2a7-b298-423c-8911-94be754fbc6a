import { useReducer } from 'react';

type StateModifier<T> = (state: T) => Partial<T>;
type ActionArgs<T> = {
  [K in keyof T]: T[K] extends (...args: infer P) => any ? P : never;
};
type Actions<T> = {
  [K in keyof T]: (...args: ActionArgs<T>[K]) => void;
};

export const useReducedState = <T, R>(reducers: Record<string, (...args: any) => StateModifier<T>>, initialState: T) => {
  const [state, dispatch] = useReducer((state: T, action: StateModifier<T>) => action(state), initialState);
  const mutState = (stateModifier: StateModifier<T>) => {
    dispatch((state) => ({
      ...state,
      ...stateModifier(state),
    }));
  }

  return {
    state,
    actions: Object.fromEntries(
      Object.entries(reducers)
        .map(([actionName, actionCreator]) => [actionName, (...args: Parameters<typeof actionCreator>) => { mutState(actionCreator(...args)); }])
    ),
  } as {
    state: Readonly<T>,
    actions: Actions<R>,
  };
};
