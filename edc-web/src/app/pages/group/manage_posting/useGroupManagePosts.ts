import { CommunityType } from '../components/types';
import { useReducedState } from './hooks/useReducedState';
import { getGroupInformation } from './api';
import { TabKey } from './types';

interface PostLoadedPayload {
  groupId: number,
  slug: string,
  topicsEnabled: boolean,
  communityType: CommunityType,
  couldManagePosts: boolean
}

export interface GroupManagePostsState {
  groupId: number,
  slug: string,
  groupLoading: boolean,
  communityType: CommunityType,
  visibleTabs: {
    topics: boolean,
    posts: boolean,
    reportedContent: boolean,
  },
  couldManagePosts: boolean,
  activeTab: TabKey | null,
  activeTopicId: number | null
}
const initialState: GroupManagePostsState = {
  groupId: null,
  slug: null,
  groupLoading: true,
  communityType: null,
  visibleTabs: {
    topics: false,
    posts: false,
    reportedContent: false,
  },
  couldManagePosts: false,
  activeTab: null,
  activeTopicId: null
};

const reducers = {
  groupManagePostsLoading: () => () => ({
    groupLoading: true,
  }),
  groupManagePostsLoaded: ({ groupId, slug, topicsEnabled, communityType, couldManagePosts } : PostLoadedPayload) => () => ({
    groupLoading: false,
    groupId,
    slug,
    communityType,
    visibleTabs: {
      topics: topicsEnabled,
      posts: true,
      reportedContent: true,
    },
    couldManagePosts: couldManagePosts
  }),
  topicPostsCountClicked: ({ selectedTopicId }: { selectedTopicId: number }) => () => ({
    activeTab: 'posts',
    activeTopicId: selectedTopicId
  }),
  tabChanged: ({ selectedTab }: { selectedTab: TabKey }) => (): Partial<GroupManagePostsState> => ({
    activeTab: selectedTab,
    activeTopicId: null
  })
};

export const useGroupManagePosts = () => {
  const { state: groupManagePosts, actions: {
    groupManagePostsLoading,
    groupManagePostsLoaded,
    topicPostsCountClicked,
    tabChanged
  }} = useReducedState<GroupManagePostsState, typeof reducers>(reducers, initialState);

  const load = (slug: string) => {
    groupManagePostsLoading();
    getGroupInformation(slug)
      .then(groupManagePostsLoaded)
      .catch((err: any) => {
        console.error(`Error while fetching group details: ${err}`);
      });
  }

  return {
    groupManagePosts,
    loadGroupDetails: load,
    topicPostsCountClicked,
    tabChanged
  };
}
