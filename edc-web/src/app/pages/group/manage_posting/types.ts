import React, { ReactNode } from 'react';
import { Post as PostingFeedPost, Topic } from '../components/types';

export type TopicsFilter = "all" | "archived";
export type ReportedContentStatus = "reported" | "trashed";

export interface LoadCommunityReportingsType extends SearchBasicType{
  topic: string,
  status: ReportedContentStatus
}

export interface SearchBasicType {
  sort: string,
  order: string,
  offset: number,
  limit: number,
}

export interface User {
  id: number;
  handle: string;
  firstName: string;
  lastName: string;
  name: string;
}

export interface TableRow {
  actions: ReactNode
}

/*
  'general' - something went wrong,
  'no-results' - applied filters couldn't match any result,
  'empty' - no entity has been created yet,
  null - unset/no error
 */
export type TableError = 'general' | 'no-results' | 'empty' | null;

export interface PostsFilters {
  status: 'all' & Post['status'],
  type: Array<Post['model']>, //TODO: probably this will be type instead of model
  author: Array<Post['user']['id']>,
  topicId: Topic['id'],
}
export type Post = PostingFeedPost & {
  status: 'pending' | 'published',
}
export type PostTableRow = Omit<Post, 'name'> & TableRow & {
  name: ReactNode
}

export interface ReportedContent {
  communityPostId: number;
  id: number,
  name: string,
  communityTopicId: number,
  communityTopicName: string,
  recordType: "Post" | "Comment",
  userId: string,
  userName: string,
  createdAt: Date,
  reason: string,
  status: string,
  reportersCount: number
}

export interface TopicFormData {
  name: string,
  description: string
}

export interface TopicFormError {
  name?: string
}

export interface TableHeaderElement {
  columnId: string,
  label: string,
  isNode?: boolean,
  isDate?: boolean,
  sortable?: boolean,
  onFilter?: (event?: React.MouseEvent<HTMLButtonElement>) => void,
  className?: string,
}

export type TopicTableRow = Omit<Topic, 'posts'> & {
  posts: ReactNode
}

export type TableData = Array<{ id: string, dataId: number, label?: string, children?: ReactNode }>;

export type TopicSortOptionId = "creation_date_desc" | "creation_date_asc" | "last_update_desc" | "last_update_asc" | "posts_count_desc" | "posts_count_asc"

export type SortOrder = "ASC" | "DESC";

export type TabKey = 'topics' | 'posts' | 'reported-content';

export interface SortOption {
  columnId: TableHeaderElement['columnId'],
  sort: SortOrder
}
