import React, { useContext, ReactNode, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { open_v2 } from '@actions/snackBarActions';
import { GroupManagePostsState, useGroupManagePosts } from "./useGroupManagePosts";
import { TabKey } from './types';

type OpenSnackbar = (message: string, type: 'success' | 'error', translate?: boolean) => void;
type GroupManagePostsContextType = {
  groupManagePosts: GroupManagePostsState;
  openSnackbar: OpenSnackbar;
  topicPostsCountClicked: ({ selectedTopicId }: { selectedTopicId: number }) => void;
  tabChanged: ({ selectedTab }: { selectedTab: TabKey }) => void;
};

const GroupManagePostsContext = React.createContext<GroupManagePostsContextType>({
  groupManagePosts: null,
  openSnackbar: null,
  topicPostsCountClicked: null,
  tabChanged: null,
});

const GroupManagePostsProvider = ({ children }: { children: ReactNode }) => {
  const { slug } = useParams();

  const globalDispatch = useDispatch();
  const { groupManagePosts, loadGroupDetails, topicPostsCountClicked, tabChanged } = useGroupManagePosts();

  useEffect(() => {
    loadGroupDetails(slug);
  }, []);

  const openSnackbar: OpenSnackbar = (message, type, translate = false) => {
    globalDispatch(open_v2(message, type, translate));
  }

  return (
    <GroupManagePostsContext.Provider value={{
      groupManagePosts,
      openSnackbar,
      topicPostsCountClicked,
      tabChanged
    }}>
      {children}
    </GroupManagePostsContext.Provider>
  );
};

const useGroupManagePostsContext = () => useContext(GroupManagePostsContext);

export { GroupManagePostsProvider, useGroupManagePostsContext };
