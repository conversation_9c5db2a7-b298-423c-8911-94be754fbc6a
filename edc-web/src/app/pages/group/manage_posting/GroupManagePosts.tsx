import React from 'react';
import { useNavigate } from 'react-router-dom';
import Card from 'centralized-design-system/src/Card/CardContainer';
import Loader from '@components/Loader/Loader';
import BackButton from './Components/BackButton';
import GroupManageTabs from './GroupManageTabs';
import { useGroupManagePostsContext } from "./GroupManagePostsProvider";
import { translatr } from 'centralized-design-system/src/Translatr';
import { translate } from './utils';
import "./GroupManagePosts.scss";

const GroupManagePosts = () => {
  const { groupManagePosts: { groupLoading } } = useGroupManagePostsContext();

  const navigate = useNavigate();

  const canGoBack = window.history.length > 1;

  return (
    <div className="group-manage-posts ed-ui">
      <Card>
        <div className="group-manage-posts_header-container">
          {canGoBack &&
            <BackButton
              aria-label = {translatr('web.common.main', 'Back')}
              onClick={() => navigate(-1)}
            />
          }
          <h2 className="group-manage-posts_title">{translate("ManagePostPageTitle")}</h2>
        </div>
        <div className="group-manage-posts_body">
          { groupLoading ? <Loader center /> : <GroupManageTabs /> }
        </div>
      </Card>
    </div>
  );
};

export default GroupManagePosts;
