import { Topic<PERSON>ormD<PERSON>, TopicsFilter, TopicSortOptionId } from '../types';
import { Topic } from '../../components/types';
import {
  createTopic,
  deleteTopic,
  getActiveTopics,
  getArchivedTopics,
  updateTopic
} from 'edc-web-sdk/requests/communityTopics';

export const TOPIC_ELEMENTS_PER_PAGE = 9;

const SORT_OPTION = {
  creation_date_desc: {
    sort: 'created_at',
    order: 'desc',
  },
  creation_date_asc: {
    sort: 'created_at',
    order: 'asc',
  },
  last_update_desc: {
    sort: 'updated_at',
    order: 'desc',
  },
  last_update_asc: {
    sort: 'updated_at',
    order: 'asc',
  },
  posts_count_desc: {
    sort: 'posts_count',
    order: 'desc',
  },
  posts_count_asc: {
    sort: 'posts_count',
    order: 'asc'
  }
};

const mapTopic = (topic: any): Topic => ({
  id: topic.id,
  name: topic.name,
  description: topic.description,
  created: topic.created_at,
  lastUpdate: topic.updated_at,
  posts: topic?.posts_count || 0
});

const mapResponseTopics = (response: any): { list: Array<Topic>, count: number } => ({
  list: response.data.map((topic: any) => mapTopic(topic)),
  count: response.count
});

export const loadTopics = async (groupId: number, filter: TopicsFilter = "all", sortOption: TopicSortOptionId = "creation_date_desc", offset: number = 0): Promise<{ list: Array<Topic>, count: number }> => {
  const payload = {
    ...SORT_OPTION[sortOption],
    limit: TOPIC_ELEMENTS_PER_PAGE,
    offset
  };

  const queryMap: Record<TopicsFilter, (payload: any, groupId: number) => Promise<any>> = {
    all: getActiveTopics,
    archived: getArchivedTopics
  };

  const response = await queryMap[filter]?.(payload, groupId);
  if (!response) {
    console.error("Invalid filter value");
    return { list: [], count: 0 };
  }

  return mapResponseTopics(response);
};

export const saveTopic = async (formData: TopicFormData, groupId: number, uuid?: number) => {
  const payload = {
    name: formData.name,
    description: formData.description,
  };
  const topic = uuid ? await updateTopic(uuid, payload, groupId) : await createTopic(payload, groupId);
  return mapTopic(topic);
};

export const removeTopic = async (uuid: number, groupId: number) => {
  const topic = await deleteTopic(uuid, groupId);
  return topic.id;
}
