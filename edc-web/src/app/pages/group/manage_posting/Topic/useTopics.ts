import { TableError, TopicFormData, Topics<PERSON>ilter, TopicSortOptionId } from '../types';
import { Topic } from '../../components/types';
import { useReducedState } from '../hooks/useReducedState';
import { loadTopics, removeTopic, saveTopic } from './api';

interface TopicState {
  data: Array<Topic>,
  count: number,
  loading: boolean,
  error: TableError,
}
const initialState: TopicState = {
  data: [] as Array<Topic>,
  count: 0,
  loading: true,
  error: null,
};

const reducers = {
  topicsLoading: () => () => ({
    loading: true,
    error: null as TableError,
  }),
  topicsLoaded: (data: Array<Topic>, count: number, error?: TableError) => () => ({
    data,
    count,
    loading: false,
    error: error,
  }),
  topicsLoadFailed: () => () => ({
    loading: false,
    error: 'general' as TableError,
  }),
  topicDeleted: (topicId: Topic["id"]) => (state: TopicState) => ({
    data: state.data.filter(topic => topic.id !== topicId),
  }),
  topicCreated: (topic: Topic) => (state: TopicState) => ({
    data: [topic, ...state.data],
    count: state.count + 1,
  }),
  topicUpdated: (updatedTopic: Topic) => (state: TopicState) => ({
    data: state.data.map(topic => updatedTopic.id === topic.id ? updatedTopic : topic),
  }),
};

export const useTopics = (groupId: number) => {
  const { state, actions: {
    topicsLoading,
    topicsLoaded,
    topicsLoadFailed,
    topicDeleted,
    topicUpdated,
    topicCreated
  }} = useReducedState<TopicState, typeof reducers>(reducers, initialState);

  const load = (filter: TopicsFilter, sortOption: TopicSortOptionId, offset: number) => {
    topicsLoading();
    loadTopics(groupId, filter, sortOption, offset)
      .then(({ list, count}) => {
        topicsLoaded(list, count, list.length === 0 ? 'no-results' : null);
      })
      .catch(() => {
        topicsLoadFailed();
      });
  }

  const remove = (topicId: Topic["id"]) =>
    removeTopic(topicId, groupId)
      .then(() => {
        topicDeleted(topicId);
      });

  const save = (formData: TopicFormData, topicId?: Topic["id"]) =>
    saveTopic(formData, groupId, topicId)
      .then((topic: Topic) => {
        topicId ? topicUpdated(topic) : topicCreated(topic);
        return topic;
      });

  return {
    topics: state,
    loadTopics: load,
    removeTopic: remove,
    saveTopic: save,
  };
}
