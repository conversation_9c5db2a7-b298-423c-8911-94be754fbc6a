import React, { useEffect, useMemo, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import EmptyState from 'centralized-design-system/src/EmptyState';
import { Button } from 'centralized-design-system/src/Buttons';
import PostCreateEditModal from '../../components/PostCreateEdit/PostCreateEditModal';
import { FOCUS_POST_ID_QUERY_PARAM } from '../../consumption/GroupPosting/utils';
import ManageTable from '../Components/Table/ManageTable';
import TableFilter from '../Components/TableFilter/TableFilter';
import RowActions from '../Components/RowActions';
import ConfirmationModal from "../Components/ConfirmationModal";
import { Post, PostsFilters, SortOption, SortOrder, TableHeaderElement } from '../types';
import { useGroupManagePostsContext } from '../GroupManagePostsProvider';
import { translate } from '../utils';
import { useStatePartial } from '../hooks/useStatePartial';
import PostCard from './PostCard/PostCard';
import PostStatus from './PostStatus/PostStatus';
import { PostState, usePosts } from './usePosts';
import { POSTS_PER_PAGE } from './api';
import { useTopics } from '@pages/group/manage_posting/Topic/useTopics';

type PostsStatusFilter = PostsFilters['status'];
type PostFilterType = keyof PostState['filters'];

const postStatusFilters: Array<{ value: PostsStatusFilter, label: string }> = [
  { value: 'all' as PostsStatusFilter, label: translate('AllPostsNavigation') },
  { value: 'pending' as PostsStatusFilter, label: translate('WaitingForApprovalNavigation') },
  { value: 'published' as PostsStatusFilter, label: translate('ApprovedNavigation') },
];

const createPostsHeaders = (onFilter: (filter: PostFilterType, element: EventTarget) => void): Array<TableHeaderElement> => [
  { label: translate('PostColumnName'), columnId: 'name', isNode: true, sortable: false },
  { label: translate('AuthorColumnName'), columnId: 'author', sortable: false },
  { label: translate('StatusColumnName'), columnId: 'status', sortable: true, isNode: true },
  { label: translate('CreatedColumnName'), columnId: 'createdAt', sortable: true, isDate: true },
  // { label: translate('ViewsColumnName'), columnId: 'views', sortable: true },
  { label: translate('LikesColumnName'), columnId: 'votesCount', sortable: true },
  { label: translate('RepliesColumnName'), columnId: 'commentsCount', sortable: true },
  { label: translate('ActionsColumnName'), columnId: 'actions', sortable: false, isNode: true }
];

const PostsTab = () => {
  const navigate = useNavigate();
  const filterAnchor = useRef<HTMLElement>();

  const { groupManagePosts: { groupId, slug, communityType, activeTopicId } } = useGroupManagePostsContext();
  const { posts, loadPosts, removePost, addNewPost, updatePost, approvePost } = usePosts(groupId);
  const { topics, loadTopics } = useTopics(groupId);

  const [ topicSelectItems, setTopicSelectItems ] = useState<Array<{ value: string; label: string }>>(null);
  const [postModalOpen, setPostModalOpen] = useState(false);
  const [postToEdit, setPostToEdit] = useState<Post>();
  const [postToDelete, setPostToDelete] = useState<Post>();

  const [tableFilterType, setTableFilterType] = useState<PostFilterType>();

  const [postsRowData, setPostsRowData] = useState([]);
  const [queryData, setQueryData] = useStatePartial({
    postsFilters: {
      topicId: null,
      status: 'all'
    } as PostsFilters,
    sortOption: {
      columnId: 'created_at',
      sort: 'DESC',
    } as SortOption,
    page: 1,
  });

  const postsHeaders: Array<TableHeaderElement> = useMemo(() =>
    createPostsHeaders((filter: PostFilterType, element: HTMLElement) => {
      setTableFilterType(filter);
      filterAnchor.current = element;
    }), []);

  const mapDropdownOptions = (post: Post) => {
    const dropdownOptions = [
      { id: 'edit', label: translate('EditPostsAction'), onClick: () => onPostEdit(post) },
      { id: 'delete', label: translate('DeletePostsAction'), onClick: () => setPostToDelete(post) }
    ]

    if(post.needApprove) {
      dropdownOptions.unshift(
        { id: 'approve', label: translate('ApprovePostsAction'), onClick: () => approvePost(post.id) },
      )
    }

    return dropdownOptions;
  }

  useEffect(() => {
    loadTopics("all", "creation_date_desc", 0);
  }, []);

  useEffect(() => {
    if(topics.loading) {
      return;
    }
    if (activeTopicId && !queryData.postsFilters.topicId) {
      setQueryData({
        postsFilters: { ...queryData.postsFilters, topicId: activeTopicId },
      });
    }
    setTopicSelectItems([
      { value: 'all', label: translate('SelectTopicsDefaultItem') },
      ...(
        topics.data ? topics.data.map(topic => ({
          value: topic.id.toString(),
          label: topic.name
        })) : []
      )
    ]);
  }, [topics.loading]);

  useEffect(() => {
    if(topicSelectItems === null) {
      return;
    }
    loadPosts(queryData.sortOption, queryData.postsFilters, queryData.page - 1);
  }, [queryData, topicSelectItems]);

  useEffect(() => {
    if (!posts) {
      return;
    }

    setPostsRowData(
      posts.data.map((post: Post) => ({
        ...post,
        name: <PostCard name={post.content} type={''} onNameClick={() => onPostDetails(post.id)} />,
        status: <PostStatus status={post.needApprove ? 'pending' : 'published'} />,
        author: post.user?.name || post.author?.name,
        actions: <RowActions dropdownOptions={mapDropdownOptions(post)} />
      }))
    );
  }, [posts]);

  const onCloseFilterModal = (selected: Array<string | number>, hasChanged: boolean) => {
    if (hasChanged) {
      onFilterChange({ [tableFilterType]: selected });
    }
    setTableFilterType(null);
  }

  const onFilterChange = (filter: Partial<PostsFilters>) => {
    setQueryData((qd) => ({
      postsFilters: {
        ...qd.postsFilters,
        ...filter,
      }
    }));
  };

  const onSortChange = (columnName: TableHeaderElement['columnId'], sortOrder: SortOrder) => {
    setQueryData({
      sortOption: {
        columnId: columnName,
        sort: sortOrder,
      } as SortOption,
    });
  };

  const onPageChange = (page: number) => {
    if (queryData.page != page) {
      setQueryData({ page });
    }
  };

  const onPostDetails = (postId: Post['id']) => {
    navigate(`/teams/${slug}?${FOCUS_POST_ID_QUERY_PARAM}=${postId}`);
  }

  const onPostRemove = () => {
    removePost(postToDelete.id);
    setPostToDelete(null);
  }

  const onPostEdit = (post: Post) => {
    setPostToEdit(post);
    setPostModalOpen(true);
  }

  const onPostModalClose = (post?: Post) => {
    setPostModalOpen(false);
    if (post) {
      postToEdit ? updatePost(post) : addNewPost(post);
    }
    setPostToEdit(null);
  };

  return (
    <>
      <ManageTable
        filters={[
          ...(topicSelectItems?.length > 1 ? [{
            id: 'topic-select',
            value: queryData.postsFilters.topicId,
            items: topicSelectItems,
            label: translate('SelectTopicsLabel'),
            onSelect: ({ value }: { value: string }) => onFilterChange({ topicId: Number(value) }),
          }] : []),
          {
            id: 'posts-state',
            value: postStatusFilters.find(status => status.value === queryData.postsFilters.status)?.value,
            items: postStatusFilters,
            label: translate('SelectStatusLabel'),
            onSelect: ({ value: status }: { value: PostsStatusFilter }) => onFilterChange({ status }),
          }
        ]}
        buttons={[{
          label: translate('ButtonAddPostLabel'),
          onClick: () => setPostModalOpen(true)
        }]}
        headers={postsHeaders}
        data={postsRowData}
        onSort={onSortChange}
        loading={posts.loading}
        total={posts.count}
        elementsPerPage={POSTS_PER_PAGE}
        onPageChange={onPageChange}
        showError={posts.error}
        emptyComponent={
          <EmptyState
            title={translate('NoPostAdded')}
            description={translate(`CreateFirstPostIn${communityType === null ? 'Group' : 'Community'}`)}
            icon='icon-list'
            button={
              <Button color='primary' onClick={() => setPostModalOpen(true)}>
                {translate('ButtonAddPostLabel')}
              </Button>
            }
          />
        }
      />
      {tableFilterType && (
        <TableFilter
          anchorEl={filterAnchor.current}
          filters={posts?.filters?.[tableFilterType]}
          //@ts-ignore todo: once api is defined remove this ignore
          initialSelected={queryData.postsFilters[tableFilterType]}
          onClose={onCloseFilterModal}
          excludeMode
        />
      )}
      {postModalOpen && (
        <PostCreateEditModal
          groupId={groupId}
          onClose={onPostModalClose}
          postId={postToEdit ? postToEdit.id : null}
          communityType={communityType}
        />
      )}
      {postToDelete && (
        <ConfirmationModal
          title={translate('DeletePostConfirmationModalTitle')}
          description={translate('DeletePostConfirmationModalDescription')}
          handleClose={() => setPostToDelete(null)}
          handleAction={onPostRemove}
          actionLabel={translatr('web.common.main', 'Delete')}
          actionVariant={'caution'}
        />
      )}
    </>
  );
};

export default PostsTab;
