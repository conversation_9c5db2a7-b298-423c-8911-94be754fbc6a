import { approveCommunityPost, getCommunityPosts, removeCommunityPost } from 'edc-web-sdk/requests/communityPosts';
import { mapResponsePost } from '../../components/utils';
import { Post } from '../types';

export const POSTS_PER_PAGE = 9;

export const loadPosts = async (groupId: number, offset: number, limit: number = POSTS_PER_PAGE,
                                order: string, sort: string, communityTopicId?: number,
                                needApprove?: boolean): Promise<{
  data: Array<Post>,
  count: number
}> => getCommunityPosts(groupId, offset, limit, order, sort, communityTopicId, needApprove)
    .then(({ data, count }: {
      data: Array<any>,
      count: number
    }) => ({ data: data.map(mapResponsePost), count }));

export const removePost = async (groupId: number, postId: Post['id']) =>
  removeCommunityPost(groupId, postId);

export const approvePost = async (groupId: number, postId: Post['id']) =>
  approveCommunityPost(groupId, postId)
    .then((post: any) => mapResponsePost(post));

