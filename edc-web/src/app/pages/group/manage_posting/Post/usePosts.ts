import { Post, PostsFilters, TableError, SortOption } from '../types';
import { Topic } from '../../components/types';
import { useReducedState } from '../hooks/useReducedState';
import { approvePost, loadPosts, POSTS_PER_PAGE, removePost } from './api';

export interface PostState {
  data: Array<Post>,
  count: number,
  filters: {
    topic: Array<{ value: Topic['id'], label: Topic['name'] }>,
    type: Array<{ value: string, label: string }>,//This should be Post['type'] once is defined in the API
    author: Array<{ value: Post['user']['id'], label: Post['user']['name'] }>,
  }, //all possible filter values
  loading: boolean,
  error: TableError,
}
const initialState: PostState = {
  data: [],
  count: 0,
  filters: {
    topic: [],
    type: [],
    author: [],
  },
  loading: true,
  error: null,
};

const checkForError = (resultLength: number, total: number): TableError => {
  const errorType = total > 0 ? 'no-results' : 'empty';
  return resultLength === 0 ? errorType : null;
}

const reducers = {
  postsLoading: () => () => ({
    loading: true,
    error: null as TableError,
  }),
  postsLoaded: (data: Array<Post>, count: number, error?: TableError) => () => ({
    data,
    count,
    loading: false,
    error
  }),
  postsLoadFailed: (type: TableError) => () => ({
    loading: false,
    error: type,
  }),
  postRemoved: (id: Post['id']) => (state: PostState) => {
    const data = state.data.filter(post => post.id != id);
    return ({
      data,
      error: checkForError(data.length, state.count),
    })
  },
  addNewPost: (post: Post) => (state: PostState) => ({
    data: [post, ...state.data],
    error: null as TableError,
  }),
  updatePost: (updatedPost: Post) => (state: PostState) => ({
    data: state.data.map(post => post.id === updatedPost.id ? updatedPost : post)
  })
};

const columnsMap = {
  'status': 'need_approve',
  'createdAt': 'created_at',
  'votesCount': 'votes_count',
  'commentsCount': 'comments_count',
}

export const usePosts = (groupId: number) => {
  const { state, actions: {
    postsLoading,
    postsLoaded,
    postsLoadFailed,
    postRemoved,
    addNewPost,
    updatePost
  }} = useReducedState<PostState, typeof reducers>(reducers, initialState);

  const mapSorting = (sortOption: SortOption) => {
    const sort = sortOption.sort.toLowerCase();
    const columnId = columnsMap[sortOption.columnId] || sortOption.columnId;
    return { columnId, sort };
  }

  const mapFilters = (postsFilter?: PostsFilters) => {
    const communityTopicId = postsFilter?.topicId && postsFilter.topicId > 0 ? postsFilter.topicId : undefined;
    let needApprove = undefined;

    if(postsFilter.status && postsFilter.status !== 'all') {
      needApprove = postsFilter.status === 'pending'
    }

    return { communityTopicId, needApprove };
  }

  const load = (sortOption: SortOption, postsFilter?: PostsFilters,  page: number = 0) => {
    postsLoading();
    const { sort, columnId} = mapSorting(sortOption);
    const { communityTopicId, needApprove } = mapFilters(postsFilter)
    loadPosts(groupId, page * (state.data?.length || POSTS_PER_PAGE), POSTS_PER_PAGE, sort, columnId, communityTopicId, needApprove)
      .then(({ data, count }) => {
        postsLoaded(data, count, checkForError(data.length, count));//TODO: endpoint should return possible filter values, set it here
      }).catch((err: any) => {
        console.error(`Error while fetching posts: ${err}`);
        postsLoadFailed('general');
      });
  }

  const remove = (postId: Post['id']) => {
    removePost(groupId, postId)
      .then(() => postRemoved(postId))
      .catch((err: any) => {
        console.error(`Error while removing post: ${err}`);
      });
  }

  const approve = (postId: Post['id']) => {
    approvePost(groupId, postId)
      .then((post) => updatePost(post as Post))
      .catch((err: any) => {
        console.error(`Error while approving post: ${err}`);
      });
  }

  return {
    posts: state,
    loadPosts: load,
    removePost: remove,
    addNewPost,
    updatePost,
    approvePost: approve
  };
}
