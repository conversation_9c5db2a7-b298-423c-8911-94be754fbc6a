import React, { useContext } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { func, array, bool } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import GroupChannelCard from 'centralized-design-system/src/GroupChannelCard/GroupChannelCard';
import Spinner from '@components/common/spinner';
import GroupChannelLoadingLayout from '@components/GroupChannelLoadingLayout/GroupChannelLoadingLayout';
import { snackBarOpenClose } from '../../../../app/actions/channelsActionsV2';
import { AllGroupPageContext } from './AllGroupPageProvider';

const GroupListing = ({
  dispatch,
  groupList,
  isPaginationInProgress,
  isGroupsFetchInProcess,
  declineClickHandler,
  setGroupList
}) => {
  const { referenceRef } = useContext(AllGroupPageContext);
  const navigate = useNavigate();

  const goToStandAloneView = transitionUrl => {
    navigate(transitionUrl);
  };

  const updateGroup = id => {
    const updatedGroupList = groupList.filter(group => group.id != id);
    setGroupList(updatedGroupList);
  };
  const snackbarErrorHandler = error => dispatch(snackBarOpenClose(error, 3000));
  return (
    <div className="group-channel-cards">
      {isGroupsFetchInProcess ? (
        <GroupChannelLoadingLayout />
      ) : (
        <>
          {groupList?.length > 0 ? (
            <>
              {groupList.map((group, index) => {
                const isPartOfGroup =
                  group.isMember ||
                  group.isTeamAdmin ||
                  group.isTeamSubAdmin ||
                  group.isTeamModerator;
                const isGroupAccessible = isPartOfGroup || !group.isPrivate;
                return (
                  <GroupChannelCard
                    key={group.id}
                    group={group}
                    referenceRef={index === 0 ? referenceRef : null}
                    groupOrChannelId={group.id}
                    tagName={translatr('web.group.main', 'Group')}
                    cardImage={group.imageUrls.medium}
                    title={group.name}
                    userCount={group.membersCount}
                    imageAltText={group.imageAltText || ''}
                    transitionUrl={`/teams/${group.slug}`}
                    isPrivate={group.isPrivate}
                    goToStandAloneView={goToStandAloneView}
                    isUserIsPartOfGroupOrChannel={isPartOfGroup}
                    declineClickHandler={declineClickHandler}
                    isInvitationPending={group.isPending}
                    isMandatory={group.isMandatory}
                    isGroupAccessible={isGroupAccessible}
                    snackbarErrorHandler={snackbarErrorHandler}
                    updateGroup={updateGroup}
                    isEveryoneTeam={group.isEveryoneTeam}
                  />
                );
              })}
            </>
          ) : (
            <div className="make-center width-100 no-group__container">
              <p aria-live="polite">{translatr('web.group.main', 'YouDoNotHaveAnyGroup')}</p>
            </div>
          )}
        </>
      )}
      {isPaginationInProgress && (
        <div className="width-100  make-center">
          <Spinner />
        </div>
      )}
    </div>
  );
};

GroupListing.propTypes = {
  groupList: array,
  isPaginationInProgress: bool,
  isGroupsFetchInProcess: bool,
  declineClickHandler: func,
  setGroupList: func
};

export default connect()(GroupListing);
