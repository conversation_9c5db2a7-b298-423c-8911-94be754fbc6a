import React, { createContext, useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { node, object } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getListV2, searchGroups, declineInviteToGroup } from 'edc-web-sdk/requests/groups.v2';
import teamFields from '../../../../app/utils/apiFieldsParams';
import debounce from 'lodash/debounce';
import { connect } from 'react-redux';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { GROUP_TO_SHARE_CONTENT } from '../../../../app/constants/localStorageConstants';
import { getConfigService } from 'edc-web-sdk/helpers/getConfigService';
import { SHOW_AUTOMATIC_GROUPS_CONFIG_SERVICE_PAYLOAD } from './constants';
import { isPostingFeedEnabled } from '@pages/group/consumption/GroupPosting/utils';

export const AllGroupPageContext = createContext();

const initialTabs = [
  {
    key: 'my',
    label: translatr('web.group.main', 'MyGroups'),
    subTabs: [
      {
        key: 'sub_admin',
        label: translatr('web.group.main', 'GroupsIAmAnAdminOf')
      },
      {
        key: 'admin',
        label: translatr('web.group.main', 'GroupsIAmALeaderOf')
      },
      {
        key: 'member',
        label: translatr('web.group.main', 'GroupsIAmAMemberOf')
      }
    ]
  },
  {
    key: 'pending',
    label: translatr('web.group.main', 'MyPendingRequests')
  },
  {
    key: 'all',
    label: translatr('web.group.main', 'OtherPublicGroups')
  }
];

const typeFilterOptions = [
  { doc_count: 0, key: translatr('web.group.main', 'Private') },
  { doc_count: 0, key: translatr('web.group.main', 'Public') }
];

const getGroups = payload => {
  return getListV2(payload)
    .then(response => response)
    .catch(error => error);
};

const getGroupsByQuery = payload => {
  return searchGroups(payload)
    .then(response => response)
    .catch(error => error);
};

function GroupPayload({ limit = 20, offset = 0, role = 'my', q = '', all = '', is_dynamic } = {}) {
  this.limit = limit;
  this.offset = offset;
  this.role = role;
  this.fields = teamFields;
  this.q = q;
  this.all = all;
  this.is_dynamic = is_dynamic;
}

const AllGroupPageProvider = ({ children, newlyCreatedGroup }) => {
  const [tabs, setTabs] = useState(initialTabs);
  const [groupList, setGroupList] = useState([]);
  const [activeTab, setActiveTab] = useState('');
  const referenceRef = useRef(null);
  const [isGroupsFetchInProcess, setIsGroupsFetchInProcess] = useState(false);
  const [isPaginationInProgress, setIsPaginationInProgress] = useState(false);
  const [totalGroups, setTotalGroups] = useState(0);
  const [adminsList, setAdminsList] = useState([]);
  const [leadersList, setLeadersList] = useState([]);
  const [selectedAdmins, setSelectedAdmins] = useState([]);
  const [selectedLeaders, setSelectedLeaders] = useState([]);
  const [selectedType, setSelectedType] = useState([]);
  const [isDeclineInvitationModalOpen, setIsDeclineInvitationModalOpen] = useState(false);
  const [invitationGroupId, setInvitationGroupId] = useState('');
  const [invitationGroupName, setInvitationGroupName] = useState('');
  const [callSaveApi, setCallSaveApi] = useState(false);
  const [dgmToggle, setDgmToggle] = useState(false);
  const [displayDgmToggle, setDisplayDgmToggle] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const query = new URLSearchParams(location.search);

  const isSearchContainer = location.pathname.includes('/org-groups/search') && query.get('q');

  const searchQuery = isSearchContainer ? query.get('q') : '';
  const [searchText, setSearchText] = useState(searchQuery);

  const [groupsSuggestionObj, setGroupsSuggestionObj] = useState({
    isLoading: false,
    data: []
  });

  // creating references to get values in event listener
  const groupListRef = useRef(groupList);
  const totalGroupsRef = useRef(totalGroups);
  const searchTextRef = useRef(searchText);
  const selectedAdminsRef = useRef(selectedAdmins);
  const selectedLeadersRef = useRef(selectedLeaders);
  const selectedTypeRef = useRef(selectedType);
  const activeTabRef = useRef(activeTab);

  // onScroll event listener
  const handleScroll = debounce(() => {
    const isUserScrolledContent =
      window.innerHeight + Math.ceil(window.pageYOffset) >= document.body.offsetHeight;
    const isAllGroupsFetched = groupListRef.current.length === totalGroupsRef.current;
    if (isUserScrolledContent && !isPaginationInProgress && !isAllGroupsFetched) {
      handlePagination();
    }
  }, 800);

  // get the screen size
  const windowSizeObject = useWindowSize();
  const screenWidth = windowSizeObject.width;

  // update of state and ref for group list
  const updateGroupList = groupItems => {
    groupListRef.current = groupItems;
    setGroupList(groupItems);
  };

  // update of state and ref for groups count
  const updateTotalGroupsCount = total => {
    totalGroupsRef.current = total;
    setTotalGroups(total);
  };

  // update of state and ref for search text
  const updateSearchText = searchString => {
    resetFilter();
    searchTextRef.current = searchString;
    setSearchText(searchString);
    if (isSearchContainer) {
      navigate(`/org-groups/search?q=${searchString}`, { replace: true });
    } else {
      navigate(`/org-groups/search?q=${searchString}`);
    }
  };

  const fetchSuggestionsOnSearch = async searchTxt => {
    try {
      setGroupsSuggestionObj({ ...groupsSuggestionObj, isLoading: true });
      const groupsObj = await getGroupsByQuery({ q: searchTxt });
      setGroupsSuggestionObj({ data: groupsObj.teams, isLoading: false });
    } catch (error) {
      setGroupsSuggestionObj({ data: [], isLoading: false });
      console.error('Error in AllGroupPageProvider.jsx.showSuggestions', error);
    }
  };

  useEffect(() => {
    const moderatorSubTab = {
      key: 'moderator',
      label:
        translatr('web.group.main', 'CommunitiesIAmAModeratorOf') ||
        'Communities I am a moderator of'
    };

    const updateTabs = async () => {
      const shouldAppendModeratorOption = await isPostingFeedEnabled();
      if (shouldAppendModeratorOption) {
        setTabs(prev => {
          return prev.map(tab => {
            if (tab.key === 'my') {
              return {
                ...tab,
                subTabs: [...tab.subTabs, moderatorSubTab]
              };
            }
            return tab;
          });
        });
      }
    };

    updateTabs();
  }, []);

  // handle newly created group
  useEffect(() => {
    if (newlyCreatedGroup) {
      const groupsItems = [...groupList];
      const isPublicGroup = !(newlyCreatedGroup?.isMandatory || newlyCreatedGroup?.isPrivate);
      const isPublicGroupsList = window.location.pathname === '/org-groups/all';
      if ((isPublicGroupsList && isPublicGroup) || !isPublicGroupsList) {
        groupsItems.unshift(newlyCreatedGroup);
      }
      setGroupList(groupsItems);
    }
  }, [newlyCreatedGroup]);

  // handling show automatic group toggle button from admin config
  useEffect(() => {
    getConfigService(SHOW_AUTOMATIC_GROUPS_CONFIG_SERVICE_PAYLOAD)
      .then(result => {
        setDgmToggle(result?.value || false);
      })
      .catch(err => {
        console.error(`Error in AllGroupPageProvider.useEffect.getConfigService.fun ${err}`);
      });
  }, []);

  // handle filter selection logic
  const toggleFilterSelection = (selectedFilters, selectedKey) => {
    const indexOfSelectedKey = selectedFilters.indexOf(selectedKey);
    const allSelectedFilterKeys = [...selectedFilters];
    if (indexOfSelectedKey === -1) {
      allSelectedFilterKeys.push(selectedKey);
    } else {
      allSelectedFilterKeys.splice(indexOfSelectedKey, 1);
    }
    return allSelectedFilterKeys;
  };

  // update of state and ref for admins selected filter list list
  const onCheckedHandlerForAdminsFilter = adminName => {
    const allSelectedAdmins = toggleFilterSelection(selectedAdmins, adminName);
    selectedAdminsRef.current = allSelectedAdmins;
    setSelectedAdmins(allSelectedAdmins);
  };

  const onCheckedHandlerForLeadersFilter = leaderName => {
    const allSelectedLeaders = toggleFilterSelection(selectedLeaders, leaderName);
    selectedLeadersRef.current = allSelectedLeaders;
    setSelectedLeaders(allSelectedLeaders);
  };

  const onCheckedHandlerForType = type => {
    const allSelectedTypes = toggleFilterSelection(selectedType, type);
    selectedTypeRef.current = allSelectedTypes;
    setSelectedType(allSelectedTypes);
  };

  // set groups leader and admin filter
  const setGroupsFilters = filters => {
    const admins = filters?.admins?.filtered?.values?.buckets;
    const leaders = filters?.leaders?.filtered?.values?.buckets;
    setAdminsList(admins);
    setLeadersList(leaders);
  };

  // event listener on did mount and unmount
  useEffect(() => {
    if (screenWidth <= 689) {
      tabs[0]?.subTabs?.unshift({ key: 'my', label: translatr('web.group.main', 'All') });
    }
    window.addEventListener('scroll', handleScroll);
    if (!isSearchContainer) {
      const currentActiveTab = location.pathname.split('/')[2] || tabs[0].key;
      setActiveTab(currentActiveTab);
    }
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [dgmToggle]);

  // get active tab groups
  const loadActiveTabsGroups = async () => {
    try {
      const payload = new GroupPayload();
      payload.role = activeTab;
      if (!dgmToggle) {
        payload.is_dynamic = dgmToggle;
      }
      if (activeTab === 'pending') {
        delete payload.role;
        payload.pending = true;
      }
      if (activeTab === 'all') {
        payload.all = true;
      }
      setIsGroupsFetchInProcess(true);
      const groupsResponse = await getGroups(payload);
      updateTotalGroupsCount(groupsResponse.total);
      setIsGroupsFetchInProcess(false);
      updateGroupList(groupsResponse?.teams);
      setDisplayDgmToggle(groupsResponse?.showDgmToggle);
    } catch (error) {
      console.error(`Error in AllGroupPageProvider.useEffect.activeTab.fun ${error}`);
    }
  };

  useEffect(() => {
    if (
      referenceRef.current !== null &&
      groupList.length !== 0 &&
      window.location.pathname !== '/org-groups'
    ) {
      referenceRef.current.focus();
    }
  }, [groupList]);

  const resetFilter = () => {
    groupListRef.current = [];
    totalGroupsRef.current = [];
    searchTextRef.current = [];
    selectedAdminsRef.current = [];
    selectedLeadersRef.current = [];
    selectedTypeRef.current = [];
    setSearchText('');
    updateGroupList([]);
    setAdminsList([]);
    setLeadersList([]);
    setSelectedAdmins([]);
    setSelectedLeaders([]);
    setSelectedType([]);
  };

  useEffect(() => {
    if (!isSearchContainer && activeTab) {
      resetFilter();
      loadActiveTabsGroups();
    }
  }, [activeTab, isSearchContainer, dgmToggle]);

  const getSearchPayload = isPagination => {
    const groupItems = groupListRef.current;
    const isSearchTermPresent = searchTextRef.current.length > 0;
    const payload = new GroupPayload();
    if (!dgmToggle) {
      payload.is_dynamic = dgmToggle;
    }
    payload.q = searchTextRef.current;
    payload.offset = isPagination ? groupItems.length : 0;
    payload.role = activeTabRef.current;
    if (activeTabRef.current === 'pending') {
      payload.pending = true;
    }
    if (activeTabRef.current === 'all') {
      payload.all = true;
    }
    if (isSearchTermPresent) {
      delete payload.role;
      payload.skip_aggs = false;
      payload['admins_names[]'] = selectedAdminsRef.current;
      payload['leaders_names[]'] = selectedLeadersRef.current;
      if (selectedTypeRef.current.length === 1) {
        payload.is_private = selectedTypeRef.current.includes(
          translatr('web.group.main', 'Private')
        );
      }
    }
    return payload;
  };

  // get groups on user onScroll
  const handlePagination = async () => {
    setIsPaginationInProgress(true);
    const isSearchTermPresent = searchTextRef.current.length > 0;
    const groupItems = groupListRef.current;
    const payload = getSearchPayload(true);
    const groupPaginationCall = isSearchTermPresent ? getGroupsByQuery : getGroups;
    const groupsResponse = await groupPaginationCall(payload);
    setIsPaginationInProgress(false);
    const allGroupItems = [...groupItems, ...groupsResponse?.teams];
    updateGroupList(allGroupItems);
    if (isSearchTermPresent) {
      // Set leader and admin
      setGroupsFilters(groupsResponse.aggs);
    }
  };

  // get groups when user search
  const loadGroupsByQuerySearch = async () => {
    const payload = getSearchPayload();
    setIsGroupsFetchInProcess(true);
    const groupsResponse = await getGroupsByQuery(payload);
    updateTotalGroupsCount(groupsResponse.total);
    setIsGroupsFetchInProcess(false);
    updateGroupList(groupsResponse?.teams);
    // Set leader and admin
    setGroupsFilters(groupsResponse.aggs);
  };

  const selectedFilterKey =
    screenWidth > 680 || callSaveApi ? [selectedAdmins, selectedLeaders, selectedType] : [];

  useEffect(() => {
    if (searchText.length > 0) {
      setCallSaveApi(false);
      loadGroupsByQuerySearch();
    }
  }, [searchText, ...selectedFilterKey]);

  const onApplyFilterHandler = () => {
    setCallSaveApi(true);
    loadGroupsByQuerySearch();
  };

  const currentActiveTab = location.pathname.split('/')[2] || tabs[0].key;

  useEffect(() => {
    changeActiveTab(currentActiveTab);
  }, [location.pathname]);

  const changeActiveTab = activeTabKey => {
    activeTabRef.current = activeTabKey;
    setActiveTab(activeTabKey);
  };

  const updateSelectedTab = activeTabKey => {
    changeActiveTab(activeTabKey);
    navigate(`/org-groups/${activeTabKey}`);
  };

  const groupCreationHandler = () => {
    localStorage.removeItem(GROUP_TO_SHARE_CONTENT);
    navigate('/org-groups/create');
  };

  const declineGroupInvitationModalHandler = (groupId, groupName) => {
    setIsDeclineInvitationModalOpen(!isDeclineInvitationModalOpen);
    setInvitationGroupId(groupId);
    setInvitationGroupName(groupName);
  };

  const declineGroupInviteRequest = () => {
    const groupItems = groupListRef.current;
    const groupIndex = groupItems.findIndex(obj => obj.id === invitationGroupId);
    declineInviteToGroup(invitationGroupId)
      .then(() => {
        groupItems.splice(groupIndex, 1);
        updateGroupList(groupItems);
        setIsDeclineInvitationModalOpen(!isDeclineInvitationModalOpen);
      })
      .catch(error => {
        setIsDeclineInvitationModalOpen(!isDeclineInvitationModalOpen);
        console.error(`Error in declineGroupInviteRequest.declineInviteToGroup fun ${error}`);
      });
  };

  const handleDgmToggle = () => {
    setDgmToggle(!dgmToggle);
  };

  const providerObject = {
    tabs,
    activeTab,
    referenceRef,
    changeActiveTab: updateSelectedTab,
    groupList,
    setGroupList,
    isGroupsFetchInProcess,
    updateSearchText,
    fetchSuggestionsOnSearch,
    groupsSuggestionObj,
    searchText,
    isPaginationInProgress,
    adminsList,
    onCheckedHandlerForAdminsFilter,
    selectedAdmins,
    leadersList,
    onCheckedHandlerForLeadersFilter,
    selectedLeaders,
    typeFilterOptions,
    onCheckedHandlerForType,
    selectedType,
    groupCreationHandler,
    declineGroupInvitationModalHandler,
    isDeclineInvitationModalOpen,
    declineGroupInviteRequest,
    invitationGroupId,
    invitationGroupName,
    onApplyFilterHandler,
    screenWidth,
    totalGroups,
    callSaveApi,
    setCallSaveApi,
    handleDgmToggle,
    dgmToggle,
    displayDgmToggle
  };

  return (
    <AllGroupPageContext.Provider value={providerObject}>{children}</AllGroupPageContext.Provider>
  );
};

AllGroupPageProvider.propTypes = {
  children: node,
  newlyCreatedGroup: object
};

const mapStoreStateToProps = ({ groupsV2 }) => {
  return {
    newlyCreatedGroup: groupsV2.get('newlyCreatedGroup')
  };
};

export default connect(mapStoreStateToProps)(AllGroupPageProvider);
