export interface User {
  id: number;
  handle: string;
  firstName: string;
  lastName: string;
  name: string;
  avatarimages?: {
    tiny: string;
    small: string;
    medium: string;
    large: string;
  };
  avatar?: string;
  jobTitle?: string;
}

export interface Post {
  model: "CommunityPost" | "Card";
  id: number;
  title?: string;
  commentsCount: number;
  updatedBy?: User;
  createdAt: string;
  updatedAt: string;
  isUpvoted: boolean;
  votesCount: number;
  mentions: Array<User>;
  content: string;
  user?: User;
  author?: User;
  state: string;
  readableCardType?: string;
  communityTopic?: {
    id: number;
    name: string;
  },
  needApprove: boolean;
  questionnaire?: any;
}
export type PostId = Post["id"];

export interface Comment {
  id: number;
  user: {
    id: number;
    name: string;
    jobTitle?: string;
    handle: string;
    avatarimages: {
      tiny: string;
      small: string;
      medium: string;
      large: string;
    },
  },
  createdAt: string;
  message: string;
  isUpvoted: boolean;
  votesCount: number;
  mentions: any[];
  commentsCount?: number;
  replies?: {
    data: Array<Reply>;
    isLoading: boolean;
    total: number;
  };
  lastCommentDate?: string;
}
export type Reply = Omit<Comment, "replies" | "commentsCount">;

export interface MentionUser {
  id: number;
  name: string;
  handle: string;
  avatarimages: {
    large: string;
    medium: string;
    small: string;
    tiny: string;
  }
}

export type PostType = 'text' | 'image' | 'video' | 'file_attachment' | 'poll' | 'quiz';

export type CommunityType = "based-community" | "learning-community" | null;

export interface Topic {
  id: number,
  name: string,
  description: string,
  created: Date,
  lastUpdate: Date,
  posts: number
}
