import { Post } from "./types";

export const mapResponsePost = (post: any): Post => ({
  ...post,
  user: {
    ...post.author,
    firstName: post.author.first_name,
    lastName: post.author.last_name,
    name: post.author.full_name,
  },
  commentsCount: post.comments_count,
  createdAt: post.created_at,
  updatedAt: post.updated_at,
  votesCount: post.votes_count,
  needApprove: post.need_approve,
  communityTopic: post.community_topic ? {
    id: post?.community_topic?.id,
    name: post?.community_topic?.name
  } : null
});
