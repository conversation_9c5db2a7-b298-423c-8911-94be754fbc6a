import { useState } from 'react';
import { createCommunityPost, updateCommunityPost } from 'edc-web-sdk/requests/communityPosts';
import { Post } from '../../types';
import { POST_MODEL } from '../../constants';
import { mapResponsePost } from "../../utils";

interface PostForm {
  post: string,
  topic: number,
  questionnaire: any,
}

interface useSavePostProps {
  groupId: number,
  onSuccess: (post: Post) => void;
}
const useSavePost = ({ groupId, onSuccess }: useSavePostProps) => {
  const [postSaving, setPostSaving] = useState(false);

  const savePost = ({ form: { post: content, topic, questionnaire }, postId }: { form: PostForm, postId?: Post['id'] }) => {
    setPostSaving(true);
    (postId ?
        updateCommunityPost(groupId, postId, { content, topic_id: topic, questionnaire }) :
        createCommunityPost(groupId, { content, topic_id: topic, questionnaire })
    ).then((post: Post) => {
        setPostSaving(false);
        onSuccess({
          model: POST_MODEL.COMMUNITY_POST,
          ...mapResponsePost(post)
        });
      }).catch((err: any) => {
        setPostSaving(false);
        console.error(`Error in teams._createPostingFeed.func: ${err}`);
      });
  }

  return {
    postSaving,
    savePost,
  };
};

export default useSavePost;
