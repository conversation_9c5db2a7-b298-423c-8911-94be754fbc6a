import { useEffect, useState } from 'react';
import { getTopicsList } from 'edc-web-sdk/requests/communityTopics';

interface UseGetTopics {
  groupId: number;
  onSuccess: (topics: Array<{ value: number, label: string }>) => void;
}
export const useGetTopics = ({ groupId, onSuccess }: UseGetTopics) => {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTopics();
  }, []);

  const getTopics = () => {
    setLoading(true);
    getTopicsList(groupId).then(({ data }: { data: Array<{ id: number, name: string }> }) => {
      onSuccess(data.map(topic => ({
        value: topic.id,
        label: topic.name,
      })));
      setLoading(false);
    }).catch((err: any) => {
      console.error(`Error in fetchTopics ${err}`);
      setLoading(false);
    });
  }

  return {
    loading
  }
};
