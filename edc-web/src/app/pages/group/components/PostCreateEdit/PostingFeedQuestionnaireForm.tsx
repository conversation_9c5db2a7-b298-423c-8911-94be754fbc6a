import React, { useCallback, useEffect, useState, memo } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import groupBy from 'lodash/groupBy';
import { Button } from "centralized-design-system/src/Buttons";
import PostingFeedQuestionnaireQuestionForm from './PostingFeedQuestionnaireQuestionForm';
import "./PostingFeedQuestionnaireForm.scss";

const MAX_QUESTIONS_COUNT = 10;

const questionInitialData = {
  question: '',
  options: [{
    text: '',
  }, {
    text: '',
  }]
};

interface Option {
  text: string;
}

interface Question {
  question: string;
  options: Option[];
}

export const validateQuestionnaire = (form) => {
  let questionErrors = {};

  form.questions.forEach((questionItem, idx) => {
    if (questionItem.question.trim() === '') {
      questionErrors = {
        ...questionErrors,
        [idx]: {
          ...questionErrors[idx],
          name: translatr('web.group.main', 'NameIsRequired')
        }
      }
    }

    let optionErrors = {};
    const optionsGroupByValue = groupBy(questionItem.options, 'text');
    questionItem.options.forEach((option, optIdx) => {
      if (option.text.trim() === '') {
        optionErrors[optIdx] = translatr('web.group.main', 'OptionIsRequired');
      }
      if (optionsGroupByValue[option.text].length >= 2) {
        optionErrors[optIdx] = translatr('cds.create-smartcard.main', 'AllPollOptionsMustBeUnique');
      }
    });
    if (Object.keys(optionErrors).length) {
      questionErrors = {
        ...questionErrors,
        [idx]: {
          ...questionErrors[idx],
          options: optionErrors
        }
      }
    }
  });

  return Object.keys(questionErrors).length ? {
    questions: questionErrors
  } : null;
};

interface PostingFeedQuestionnaireFormProps {
  onClose: () => void;
  onChange: ({ questions }: { questions: Question[] }) => void;
  quizEnabled: boolean;
  errors: any;
}

const PostingFeedQuestionnaireForm = memo(({ onClose, onChange, quizEnabled = false, errors = null }: PostingFeedQuestionnaireFormProps) => {
  const [state, setState] = useState({
    questions: [questionInitialData],
    type: quizEnabled ? 'quiz' : 'poll'
  });

  const handleAddQuestion = useCallback(() => {
    setState((prevState) => ({
      ...prevState,
      questions: [...prevState.questions, questionInitialData]
    }))
  }, []);

  const handleRemoveQuestion = useCallback((id) => {
    setState((prevState) => ({
      ...prevState,
      questions: prevState.questions.filter((item, itemIdx) => itemIdx !== id)
    }));
  }, []);

  const handleSetQuestion = useCallback((id, cb) => {
    setState((prevState) => ({
      ...prevState,
      questions: prevState.questions.map((item, itemIdx) => {
        if (id === itemIdx) {
          return cb(item);
        }
        return item;
      })
    }));
  }, []);

  useEffect(() => {
    onChange(state);
  }, [state]);

  return (
    <div className="posting-feed-questionnaire-form">
      <div className="posting-feed-questionnaire-form__btnCloseContainer">
        <button
          className="ed-dialog-modal-header-close-button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      {state.questions.map((item, idx) => (
        <PostingFeedQuestionnaireQuestionForm
          key={idx}
          questionId={idx}
          question={item}
          setQuestion={handleSetQuestion}
          onRemoveQuestion={handleRemoveQuestion}
          removable={state.questions.length > 1}
          quizEnabled={quizEnabled}
          errors={errors ? errors.questions[idx] : null}
        />
      ))}
      {state.questions.length < MAX_QUESTIONS_COUNT && <Button
        color="secondary"
        size="large"
        onClick={handleAddQuestion}
      >
        <i className="icon-plus"/>{translatr('web.group.main', 'AddQuestion')}
      </Button>}
    </div>
  )
});

PostingFeedQuestionnaireForm.displayName = 'PostingFeedQuestionnaireForm';

export default PostingFeedQuestionnaireForm;
