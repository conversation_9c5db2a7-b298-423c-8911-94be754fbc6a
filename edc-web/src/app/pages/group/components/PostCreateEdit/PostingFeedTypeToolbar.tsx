import React, { useState } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';
import Dropdown from 'centralized-design-system/src/Dropdown';
import ConfirmationModal from 'centralized-design-system/src/Modals/ConfirmationModal';
import { POST_TYPES } from '../constants';
import { PostType } from '../types';
import "./PostingFeedTypeToolbar.scss";

interface TooltipWrapperProps {
  children: React.ReactNode;
  message: string;
}

const TooltipWrapper = ({ children, message }: TooltipWrapperProps) => {
  return (
    <Tooltip
      message={message}
      pos='top'
      isTranslated
    >
      {children}
    </Tooltip>
  );
};

interface PostingFeedTypeToolbarProps {
  currentPostType: PostType;
  onMentionClick: () => void;
  onImageClick: () => void;
  onMediaClick: () => void;
  onQuizClick: () => void;
  onPollClick: () => void;
  onFileClick: () => void;
}

const PostingFeedTypeToolbar = ({ currentPostType, onMentionClick, onImageClick, onMediaClick, onPollClick, onQuizClick, onFileClick }: PostingFeedTypeToolbarProps) => {
  const [openToolbar, setOpenToolbar] = useState(false);
  const [confirmModal, setConfirmModal] = useState(false);
  const [nextPostType, setNextPostType] = useState(null);

  const mapPostTypeToCb = {
    [POST_TYPES.IMAGE]: onImageClick,
    [POST_TYPES.VIDEO]: onMediaClick,
    [POST_TYPES.POLL]: onPollClick,
    [POST_TYPES.QUIZ]: onQuizClick,
    [POST_TYPES.FILE_ATTACHMENT]: onFileClick,
  };

  const checkPostType = (nextPostType: PostType) => {
    if ([POST_TYPES.POLL, POST_TYPES.QUIZ, POST_TYPES.IMAGE, POST_TYPES.VIDEO, POST_TYPES.FILE_ATTACHMENT].includes(currentPostType)) {
      setNextPostType(nextPostType);
      setConfirmModal(true);
    } else {
      if (mapPostTypeToCb[nextPostType]) {
        mapPostTypeToCb[nextPostType]();
      }
      setOpenToolbar(false);
    }
  };

  return (
    <div className="posting-feed-type-toolbar">
      {confirmModal &&
        <ConfirmationModal
          title={translatr('web.group.main', 'PostCreateChangePostTypeConfirmationTitle')}
          message={translatr('web.group.main', 'PostCreateChangePostTypeConfirmationMessage')}
          callback={() => {
            if (mapPostTypeToCb[nextPostType]) {
              mapPostTypeToCb[nextPostType]();
            }
            setNextPostType(null);
            setConfirmModal(false);
          }}
          cancelClick={() => {
            setConfirmModal(false);
          }}
          closeModal={() => {
            setConfirmModal(false);
          }}
        />
      }
      <TooltipWrapper message={translatr('web.group.main', 'AddMention')}>
        <i onClick={onMentionClick} className="icon-handle"/>
      </TooltipWrapper>
      <span className="posting-feed-type-toolbar__separator-v" />
      <Dropdown
        openDropdown={openToolbar}
        setOpenDropdown={setOpenToolbar}
        icon={
          <TooltipWrapper message={translatr('web.group.main', 'AddMedia')}>
            <span className="icon-image" />
          </TooltipWrapper>
        }
      >
        <ul>
          <li onClick={() => checkPostType(POST_TYPES.IMAGE)}>
            {translatr('web.common.main', 'Image')}
          </li>
          <li onClick={() => checkPostType(POST_TYPES.VIDEO)}>
            {translatr('web.common.main', 'Video')}
          </li>
        </ul>
      </Dropdown>
      <TooltipWrapper message={translatr('web.group.main', 'AddPoll')}>
        <i onClick={() => checkPostType(POST_TYPES.POLL)} className="icon-view-as-cards"/>
      </TooltipWrapper>
      <TooltipWrapper message={translatr('web.group.main', 'AddQuiz')}>
        <i onClick={() => checkPostType(POST_TYPES.QUIZ)} className="icon-comment-dots"/>
      </TooltipWrapper>
      <TooltipWrapper message={translatr('web.group.main', 'AddFile')}>
        <i
          onClick={() => checkPostType(POST_TYPES.FILE_ATTACHMENT)}
          className="icon-attach-file"
        />
      </TooltipWrapper>
    </div>
  );
};

export default PostingFeedTypeToolbar;
