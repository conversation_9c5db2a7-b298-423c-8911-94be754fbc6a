import React, { useState, useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { But<PERSON> } from 'centralized-design-system/src/Buttons';
import { Select } from 'centralized-design-system/src/Inputs';
import Modal, { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from 'centralized-design-system/src/Modals';
import Avatar from 'centralized-design-system/src/Avatar';
import { translatr } from 'centralized-design-system/src/Translatr';
import stripHtml from 'centralized-design-system/src/Inputs/ckeditorUtils/striphtml';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { PICTURES, MEDIA, RESOURCES } from 'centralized-design-system/src/Utils/Uploads/constants';
import { TEAMS } from 'centralized-design-system/src/Utils/Uploads/constants';
import Loader from '@components/Loader/Loader';
import { Post, PostType, CommunityType } from '../types';
import { POST_TYPES } from '../constants';
import { useGetPost } from './api/useGetPost';
import useSavePost from './api/useSavePost';
import { useGetTopics } from './api/useGetTopics';
import PostingFeedTypeToolbar from './PostingFeedTypeToolbar';
import PostingFeedTextForm from './PostingFeedTextForm';
import PostingFeedQuestionnaireForm, { validateQuestionnaire } from './PostingFeedQuestionnaireForm';
import './PostCreateEditModal.scss';

const MAX_POST_MESSAGE_LENGTH = 10000;

const uploadParams = {
  objectType: TEAMS,
  restrictMediaType: { video: false, audio: false }
};

interface FormErrors {
  post?: string;
  questionnaire?: any; //TODO: define error object here
  topic?: string;
}

interface TopicProps {
  groupId: number;
  defaultValue: number;
  onChange: (value: number) => void;
  error: string;
}
const Topics = ({ groupId, defaultValue, onChange, error }: TopicProps) => {
  const [topicsState, setTopicsState] = useState<{
    loaded: boolean,
    topics: Array<{ value: number, label: string }>
  }>({
    loaded: false,
    topics: [{ value: 0, label: translatr('web.group.main', 'SelectTopicsDefaultItem') }],
  });
  const { loading: topicLoading } = useGetTopics({
    groupId,
    onSuccess: availableTopics => {
      setTopicsState(ts => ({
        loaded: true,
        topics: [...(defaultValue ? [] : ts.topics), ...availableTopics],
      }));
    }
  });

  const removeDefaultOption = () =>
    setTopicsState(ts => ({ ...ts, topics: ts.topics.filter(topic => topic.value !== 0) }));

  useEffect(() => {
    if (defaultValue) {
      removeDefaultOption();
    }
  }, [defaultValue]);

  const handleOnChange = (value: string) => {
    removeDefaultOption();
    onChange(Number(value));
  }

  return <>
    {topicLoading && <Loader />}
    {topicsState.loaded &&
      <Select
        title={translatr('web.group.main', 'Topic')}
        items={topicsState.topics}
        onChange={(_: void, event: { target: { value: string } }) => handleOnChange(event.target.value)}
        defaultValue={defaultValue}
        error={error}
        required
        isTranslated
      />
    }
  </>
}

interface PostCreateEditModalProps {
  groupId: number;
  postId?: Post['id'];
  communityType: CommunityType;
  onClose: (post?: Post) => void;
}
const PostCreateEditModal = ({ groupId, postId, communityType, onClose }: PostCreateEditModalProps) => {
  const currentUser = useSelector((state: any) => state.currentUser.toJS());
  const allowedMediaMimeTypes = useSelector((state: any) => state.team.get('allowedMediaMimeTypes'));

  const { postSaving, savePost } = useSavePost({
    groupId,
    onSuccess: onClose
  });
  const { loading: postLoading } = useGetPost({
    groupId,
    postId,
    onSuccess: ({ content, community_topic }) => {
      setForm((prevForm) => ({
        ...prevForm,
        post: content,
        topic: community_topic?.id,
      }));
    },
    onError: () => {
      onClose();
    }
  });

  const [form, setForm] = useState({
    topic: null,
    post: "",
    fileAttachment: {} as { file?: { url: string }, securedUrl?: string },
    questionnaire: null,
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [uploadFileModal, setUploadFileModal] = useState({
    open: false,
    type: null,
    mimeType: null,
  });
  const [postType, setPostType] = useState<PostType>(POST_TYPES.TEXT);

  const handleFieldChange = (fieldName: keyof typeof form) => (value: any) => {
    setForm((prevForm) => ({
      ...prevForm,
      [fieldName]: value
    }));
  };

  const handleQuestionnaireChange = useCallback((formState: any) => {
    setForm((prevForm) => ({
      ...prevForm,
      questionnaire: formState,
    }));
  }, []);

  const handleQuestionnaireClose = useCallback(() => {
    setPostType(POST_TYPES.TEXT);
    setForm((prevForm) => ({
      ...prevForm,
      questionnaire: null,
    }));
  }, []);

  const validateForm = () => {
    const errors: FormErrors = {};
    const postMessageLength = stripHtml(form.post).replace(/&nbsp;/g, '').trimStart().trimEnd().length;
    const isPostMessageLengthError = postMessageLength === 0 || postMessageLength > MAX_POST_MESSAGE_LENGTH;

    if (isPostMessageLengthError) {
      errors.post = translatr('web.group.main', 'InvalidPostMessage');
    }
    if (form.questionnaire) {
      errors.questionnaire = validateQuestionnaire(form.questionnaire);
    }
    if (!!communityType && !form.topic) {
      errors.topic = translatr('web.group.main', 'TopicIsRequired');
    }
    if (Object.entries(errors).filter((item) => item[1] !== null).length) {
      setFormErrors(errors);
      return false;
    }

    return true;
  };

  const handleSendForm = () => {
    if (postSaving) {
      return;
    }
    if (validateForm()) {
      savePost({ form, postId });
    }
  };

  return (
    <Modal size="small" className="posting-feed-create-modal">
      <ModalHeader
        title={translatr('web.group.main', postId ? 'EditPost' : 'CreatePost')}
        onClose={() => onClose()}
      />
      <ModalContent>
        <div className="posting-feed-create-modal-user-info">
          <Avatar
            size="large"
            user={{ imgUrl: currentUser.avatar, name: currentUser.name, id: currentUser.id }}
          />
          <span>{currentUser.name}</span>
        </div>
        {((postId && form.post && !postLoading) || !postId) ?
          <PostingFeedTextForm
            post={form.post}
            postError={formErrors?.post}
            onFieldChange={handleFieldChange("post")}
          /> :
          <Loader center />
        }
        <div className="posting-feed-create-modal__separator" />
        <PostingFeedTypeToolbar
          currentPostType={postType}
          onMentionClick={() => {
            //@ts-ignore
            CKEDITOR.instances['ckeditor-post'].insertText('@');
            const event = new KeyboardEvent("keyup");
            document.querySelector('#ckeditor-post').dispatchEvent(event);
          }}
          onImageClick={() => {
            setUploadFileModal({
              open: true,
              type: PICTURES,
              mimeType: allowedMediaMimeTypes.image
            });
            setPostType(POST_TYPES.IMAGE);
          }}
          onMediaClick={() => {
            setUploadFileModal({
              open: true,
              type: MEDIA,
              mimeType: allowedMediaMimeTypes.video
            });
            setPostType(POST_TYPES.VIDEO);
          }}
          onPollClick={() => {
            setPostType(POST_TYPES.POLL);
          }}
          onQuizClick={() => {
            setPostType(POST_TYPES.QUIZ);
          }}
          onFileClick={() => {
            setUploadFileModal({
              open: true,
              type: RESOURCES,
              mimeType: allowedMediaMimeTypes.doc
            });
            setPostType(POST_TYPES.FILE_ATTACHMENT);
          }}
        />
        {([POST_TYPES.IMAGE, POST_TYPES.VIDEO, POST_TYPES.FILE_ATTACHMENT].includes(postType) && uploadFileModal.open) &&
          <div data-testid="posting-feed-file-upload" className="posting-feed-create-modal__file-upload">
            <FileUpload
              openOnLoad
              showMaxSize
              persistedFile={form.fileAttachment?.file}
              previewUrl={form.fileAttachment?.securedUrl || form.fileAttachment?.file?.url}
              showPreviewImage={true}
              //@ts-ignore - wrong type deduction from jsx component
              setValue={handleFieldChange('fileAttachment')}
              allowedFileType={uploadFileModal.mimeType}
              isTranslated
              uploadParams={{
                ...uploadParams,
                uploadType: uploadFileModal.type,
              }}
              removedFileCB={() => {
                setUploadFileModal({
                  open: false,
                  type: null,
                  mimeType: null,
                });
              }}
              onCancel={() => {
                if (!form.fileAttachment?.file) {
                  setUploadFileModal({
                    open: false,
                    type: null,
                    mimeType: null,
                  });
                  setPostType(POST_TYPES.TEXT);
                }
              }}
            />
          </div>
        }
        {[POST_TYPES.POLL, POST_TYPES.QUIZ].includes(postType) &&
          <PostingFeedQuestionnaireForm
            key={postType}
            quizEnabled={postType === POST_TYPES.QUIZ}
            onClose={handleQuestionnaireClose}
            onChange={handleQuestionnaireChange}
            errors={formErrors?.questionnaire}
          />
        }
        {!!communityType &&
          <Topics
            groupId={groupId}
            defaultValue={form.topic}
            onChange={handleFieldChange('topic')}
            error={formErrors.topic}
          />
        }
      </ModalContent>
      <ModalFooter className="posting-feed-create-modal__footer">
        <Button
          color="secondary"
          variant="ghost"
          size="large"
          onClick={() => onClose()}
        >
          {translatr("web.common.main", "Cancel")}
        </Button>
        <Button
          color="primary"
          size="large"
          onClick={handleSendForm}
          disabled={postSaving}
        >
          {postId ?
            translatr('web.common.main', postSaving ? 'Updating' : 'Update') :
            translatr('web.common.main', postSaving ? 'Posting' : 'Post')
          }
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default PostCreateEditModal;
