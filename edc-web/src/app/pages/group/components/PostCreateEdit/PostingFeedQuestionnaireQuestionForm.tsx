import React, { useCallback, memo } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { Button } from 'centralized-design-system/src/Buttons';
import Checkbox from 'centralized-design-system/src/Checkbox';
import './PostingFeedQuestionnaireQuestionForm.scss';

const DEFAULT_OPTION_COUNT = 2;
const MAX_OPTIONS_COUNT = 5;

interface OptionRowProps {
  id: number;
  text: string;
  correct?: boolean;
  removable: boolean;
  onRemove: (id: number) => void;
  onChange: (id: number, value: any) => void;
  onMarkAsCorrect: (id: number, correct: boolean) => void;
  quizEnabled: boolean;
  error: any;
}

const OptionRow = memo(({ id, text, correct, removable, onRemove, onChange, onMarkAsCorrect, quizEnabled, error = null }: OptionRowProps) => (
  <div className="posting-feed-questionnaire-question-form__option-row">
    <TextField
      defaultValue={text}
      placeholder={quizEnabled ?
        `${translatr('web.group.main', 'QuizOption')} ${id + 1}` :
        `${translatr('web.group.main', 'PollOption')} ${id + 1}`
      }
      setValue={(val: string) => onChange(id, val)}
      error={error}
    />
    {quizEnabled && <Checkbox
      onChange={({ target: { checked }}) => onMarkAsCorrect(id, checked)}
      checked={correct || false}
      isTranslated={true}
    />}
    {removable && <Button
      color='secondary'
      size='large'
      onClick={() => onRemove(id)}
    >
      <i className="icon-trash" />
    </Button>}
  </div>
));

type Option = { text: string, correct?: boolean };
type Question = { question: string, options: Array<Option> };

interface PostingFeedQuestionnaireQuestionFormProps {
  questionId: number;
  question: Question;
  setQuestion: (id: number, data: any) => void;
  onRemoveQuestion: (id: number) => void;
  removable: boolean;
  quizEnabled: boolean;
  errors: any;
}
const PostingFeedQuestionnaireQuestionForm = memo(({ questionId, question, setQuestion, onRemoveQuestion, removable, quizEnabled, errors = null }: PostingFeedQuestionnaireQuestionFormProps) => {
  const handleAddOption = () => {
    setQuestion(questionId, (prevQuestion: Question) => ({
      ...prevQuestion,
      options: [...prevQuestion.options, { text: '' }]
    }));
  };

  const handleRemoveOption = useCallback((id: number) => {
    setQuestion(questionId, (prevQuestion: Question) => ({
      ...prevQuestion,
      options: prevQuestion.options.filter((_, itemIdx: number) => itemIdx !== id)
    }));
  }, []);

  const handleQuestionChange = useCallback((value: string) => {
    setQuestion(questionId, (prevQuestion: Question) => ({
      ...prevQuestion,
      question: value,
    }));
  }, []);

  const modifyOption = (id: number, modifier: (option: Option) => Option) => {
    setQuestion(questionId, (prevQuestion: Question) => ({
      ...prevQuestion,
      options: prevQuestion.options.map((option, idx: number) => idx === id ? modifier(option) : option)
    }));
  }

  const handleOptionChange = useCallback((id: number, text: string) => {
    modifyOption(id, option => ({ ...option, text }));
  }, []);

  const handleMarkAsCorrect = useCallback((id: number, correct: boolean)=> {
    modifyOption(id, option => ({ ...option, correct }));
  }, []);

  return (
    <div>
      <div className="posting-feed-questionnaire-question-form__questionField">
        <TextField
          title={translatr('web.group.main', quizEnabled ? 'QuizQuestion' : 'PollQuestion')}
          placeholder={translatr('web.group.main', 'PleaseAddHereYourQuestion')}
          defaultValue={question.question}
          description={translatr('web.group.main', 'EnterQuestion')}
          shouldCheckForMaxChar
          maxLen={350}
          setValue={handleQuestionChange}
          required
          isTranslated
          error={errors ? errors.name : null}
        />
        {removable && <Button
          color='secondary'
          size='large'
          onClick={() => onRemoveQuestion(questionId)}
        >
          <i className="icon-trash" />
        </Button>}
      </div>
      <label className="posting-feed-questionnaire-question-form__questionnaireOptionsLabel">
        {translatr('web.group.main', quizEnabled ? 'QuizOptions' : 'PollOptions')}<span className="asterisk">*</span>
      </label>
      {question.options.map((option, idx) => (
        <OptionRow
          id={idx}
          key={idx}
          text={option.text}
          correct={option.correct}
          onChange={handleOptionChange}
          onRemove={handleRemoveOption}
          onMarkAsCorrect={handleMarkAsCorrect}
          removable={question.options.length > DEFAULT_OPTION_COUNT}
          quizEnabled={quizEnabled}
          error={(errors && errors.options) ? errors.options[idx] : null}
        />
      ))}
      <div className="posting-feed-questionnaire-question-form__actionBar">
        {question.options.length < MAX_OPTIONS_COUNT && <Button
          color="primary"
          size="large"
          onClick={handleAddOption}
        >
          <i className="icon-plus"/>{translatr('web.common.main', 'AddOption')}
        </Button>}
      </div>
    </div>
  )
});
PostingFeedQuestionnaireQuestionForm.displayName = 'PostingFeedQuestionnaireQuestionForm';

export default PostingFeedQuestionnaireQuestionForm;
