import React, { useState, useEffect, useContext } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Checkbox from 'centralized-design-system/src/Checkbox/index';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { CreateUpdateContext } from './CreateUpdateProvider';
import LD from '../../../../app/containers/LDStore';
import { func, object, string, bool } from 'prop-types';
import Switch from 'centralized-design-system/src/Switch';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';
import { BANNERS, IMAGES, TEAMS } from 'centralized-design-system/src/Utils/Uploads/constants';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';
import EngagementSection from './engagementSection/EngagementSection';
import WidgetSection from './WidgetSection';

const allowedBadgeUploadType = ['.png', '.jpeg', '.jpg', '.webp'];

const GroupSettings = ({
  groupSettingPayload,
  setGroupSettingPayload,
  groupName,
  about,
  groupImage,
  setGroupImage,
  setGroupImageAlt,
  groupBannerImage,
  setGroupBannerImage,
  setGroupBannerImageAlt,
  setIsValueChanged,
  isEditMode,
  sectionsErrors,
  setSectionsErrors,
  widget,
  setWidget,
  setIsWidgetCodeValid
}) => {
  const [checkBoxValues, setCheckBoxValues] = useState({
    openGroup: false,
    mandatoryGroup: false,
    enableContentSharing: false,
    autoAssignContent: false,
    enableSkillMatrix: false,
    onlyAdminCanRestrict: false,
    isContentApproval: false
  });
  const { groupDetail } = useContext(CreateUpdateContext);

  const HR_DATA_SERVICE_ENABLEMENT = global?.__edOrgData?.configs?.find(
    f => f.name === 'hr_data_service_enablement'
  )?.value;
  const SKILLS_ASSESSMENT_ENABLEMENT = global?.__edOrgData?.configs?.find(
    f => f.name === 'skills_assessment_enablement'
  )?.value;
  const MD_TEAM_SKILLS_ASSESSMENT = global?.__edOrgData?.configs?.find(
    f => f.name === 'md_configuration'
  )?.value.show_skill_assessment;

  const isGroupAdminOrSubAdmin = groupDetail?.isTeamAdmin || groupDetail?.isTeamSubAdmin;

  const TEAM_MATRIX_CONFIGS =
    HR_DATA_SERVICE_ENABLEMENT && SKILLS_ASSESSMENT_ENABLEMENT && MD_TEAM_SKILLS_ASSESSMENT;

  const SHOW_TEAM_MATRIX = isEditMode
    ? TEAM_MATRIX_CONFIGS && isGroupAdminOrSubAdmin
    : TEAM_MATRIX_CONFIGS;

  const handleCheckBox = e => {
    const { id } = e.target;
    if (id === 'mandatoryGroup') {
      checkBoxValues[id] = !checkBoxValues[id];
      checkBoxValues['openGroup'] = false;
    } else if (id === 'openGroup') {
      checkBoxValues[id] = !checkBoxValues[id];
      checkBoxValues['mandatoryGroup'] = false;
    } else {
      checkBoxValues[id] = !checkBoxValues[id];
    }
    setCheckBoxValues({ ...checkBoxValues });
    setGroupSettingPayload(prev => ({ ...prev, ...checkBoxValues }));
    setIsValueChanged(true);
  };

  useEffect(() => {
    if (groupDetail) {
      setCheckBoxValues({
        openGroup: !groupDetail?.isPrivate,
        mandatoryGroup: groupDetail?.isMandatory,
        enableContentSharing: !groupDetail?.onlyAdminCanPost,
        autoAssignContent: groupDetail?.autoAssignContent,
        enableSkillMatrix: groupDetail?.enableSkillMatrix,
        onlyAdminCanRestrict: !groupDetail?.onlyAdminCanRestrict,
        isContentApproval: groupDetail?.isContentApproval
      });
    }
    setGroupSettingPayload(prev => ({ ...prev, ...checkBoxValues }));
  }, [groupDetail, groupName, about, groupImage, groupBannerImage]);

  const settingImage = (setImage, fileStackObj) => {
    setIsValueChanged(true);
    const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
    if (isLXMediaHubEnabled && !fileStackObj?.file) {
      setImage({ file: {}, securedUrl: '' });
    } else if (!isLXMediaHubEnabled && !fileStackObj?.file?.url) {
      setImage({ file: { url: '' }, securedUrl: '' });
    } else {
      setImage(fileStackObj);
    }
  };

  const handleSkillMatrixChange = e => {
    const data = { ...checkBoxValues, enableSkillMatrix: e.target.checked };
    setCheckBoxValues(data);
    setGroupSettingPayload(data);
  };

  const uploadParams = {
    objectType: TEAMS,
    restrictMediaType: { video: false, audio: false }
  };

  return (
    <>
      <div className="supporting-text text-mute mt-16">
        <FileUpload
          label={translatr('web.group.main', 'GroupImage')}
          ariaLabel={translatr('web.common.main', 'UploadGroupImage')}
          description={translatr('web.group.main', 'ShownInYourGroupsCard', { size: '15MB' })}
          showMaxSize={false}
          setValue={settingImage.bind(this, setGroupImage)}
          setAlt={setGroupImageAlt}
          imageMax={[930, 523]}
          imageMin={[200, 200]}
          alt={groupDetail?.imageAltText}
          previewUrl={groupDetail?.imageUrls?.medium}
          allowedFileType={allowedBadgeUploadType}
          isTranslated={true}
          uploadParams={{ ...uploadParams, uploadType: IMAGES }}
        />
        <FileUpload
          label={translatr('web.group.main', 'GroupBannerImage')}
          ariaLabel={translatr('web.group.main', 'UploadGroupBannerImage')}
          description={translatr('web.group.main', 'ShownInYourGroupsHeader', { size: '15MB' })}
          showMaxSize={false}
          setValue={settingImage.bind(this, setGroupBannerImage)}
          setAlt={setGroupBannerImageAlt}
          imageMax={[1440, 380]}
          imageMin={[200, 200]}
          alt={groupDetail?.bannerimageAltText}
          previewUrl={groupDetail?.bannerimageUrls?.medium}
          allowedFileType={allowedBadgeUploadType}
          isTranslated={true}
          uploadParams={{ ...uploadParams, uploadType: BANNERS }}
        />
      </div>
      <WidgetSection
        widget={widget}
        setWidget={setWidget}
        setIsWidgetCodeValid={setIsWidgetCodeValid}
        sectionsErrors={sectionsErrors}
        setSectionsErrors={setSectionsErrors}
      />
      <EngagementSection
        groupSettingPayload={groupSettingPayload}
        setGroupSettingPayload={setGroupSettingPayload}
        isEditMode={isEditMode}
        sectionsErrors={sectionsErrors}
        setSectionsErrors={setSectionsErrors}
      />
      <h2 className="create-text-line" data-label={translatr('web.group.main', 'Configurations')}>
        <span aria-hidden="true">{translatr('web.group.main', 'Configurations')}</span>
      </h2>
      {SHOW_TEAM_MATRIX && (
        <div className="mt-16 mb-16">
          <Switch
            id="skillMatrixSwitchDesc"
            defaultChecked={checkBoxValues.enableSkillMatrix}
            onChange={handleSkillMatrixChange}
            name={translatr('web.group.main', 'SkillsMatrix') || 'Skills Matrix'}
            className="group-team-activity-switch mt-4"
          />
        </div>
      )}
      <fieldset>
        <legend className="mt-4">
          {translatr('web.group.main', 'GroupSettings')}{' '}
          <span className="group-supporting-text font-italic optional-text">
            {translatr('web.common.main', 'Optional')}
          </span>
        </legend>
        <div className="checkboxes mt-8">
          <Checkbox
            label={translatr('web.group.main', 'OpenGroup')}
            id="openGroup"
            className="pointer"
            checked={checkBoxValues?.openGroup}
            onChange={handleCheckBox}
            ariaLabelledby="openGroup openGroupDesc"
            isTranslated={true}
          />
          <div id="openGroupDesc" className="checkbox-description-text supporting-text text-mute">
            {translatr('web.group.main', 'UsersWillBeAbleToFindAndJoinThisGroup')}
          </div>
          {!checkBoxValues.openGroup && LD.isMandatoryGroupEnable() && (
            <>
              <Checkbox
                label={translatr('web.group.main', 'MandatoryGroup')}
                id="mandatoryGroup"
                className="pointer"
                checked={checkBoxValues?.mandatoryGroup}
                onChange={handleCheckBox}
                ariaLabelledby="mandatoryGroup mandatoryGroupDesc"
                isTranslated={true}
              />
              <div
                id="mandatoryGroupDesc"
                className="checkbox-description-text supporting-text text-mute"
              >
                {translatr('web.group.main', 'AddingMemberWarning')}
              </div>
            </>
          )}
        </div>{' '}
      </fieldset>

      <fieldset>
        <legend className="mt-16">
          {translatr('web.group.main', 'SharingAndAssignment')}{' '}
          <span className="group-supporting-text font-italic optional-text">
            {translatr('web.common.main', 'Optional')}
          </span>
        </legend>

        <div className="checkboxes mt-8">
          <Checkbox
            label={translatr('web.group.main', 'EnableContentSharing')}
            id="enableContentSharing"
            checked={checkBoxValues?.enableContentSharing}
            onChange={handleCheckBox}
            ariaLabelledby="enableContentSharing enableContentSharingDesc"
            isTranslated={true}
          />
          <div
            id="enableContentSharingDesc"
            className="checkbox-description-text supporting-text text-mute"
          >
            {translatr('web.group.main', 'MembersCanShareContentToThisGroup')}
          </div>
          <Checkbox
            label={translatr('web.group.main', 'EnableRestrictTo')}
            id="onlyAdminCanRestrict"
            checked={checkBoxValues?.onlyAdminCanRestrict}
            onChange={handleCheckBox}
            ariaLabelledby="onlyAdminCanRestrictDesc"
            isTranslated={true}
          />
          <div
            id="onlyAdminCanRestrictDesc"
            className="checkbox-description-text supporting-text text-mute"
          >
            {translatr('web.group.main', 'MembersCanRestrict')}
          </div>
          <Checkbox
            label={translatr('web.group.main', 'AutoAssignContentToNewGroupMembers')}
            id="autoAssignContent"
            checked={checkBoxValues?.autoAssignContent}
            onChange={handleCheckBox}
            ariaLabelledby="autoAssignContent autoAssignContentgDesc"
            isTranslated={true}
          />
          <div
            id="autoAssignContentgDesc"
            className="checkbox-description-text supporting-text text-mute"
          >
            {translatr('web.group.main', 'AutomaticPastAssignment')}
          </div>
          {groupSettingPayload.enablePostingFeed && (
            <>
              <Checkbox
                label={translatr('web.group.main', 'ContentApproval')}
                id="isContentApproval"
                checked={checkBoxValues?.isContentApproval}
                onChange={handleCheckBox}
                ariaLabelledby="isContentApprovalContentDesc"
                isTranslated={true}
              />
              <div
                id="isContentApprovalContentDesc"
                className="checkbox-description-text supporting-text text-mute"
              >
                {translatr('web.group.main', 'ContentApprovalDesc')}
              </div>
            </>
          )}
        </div>
      </fieldset>
    </>
  );
};

GroupSettings.propTypes = {
  setIsValueChanged: func,
  groupSettingPayload: object,
  setGroupSettingPayload: object,
  groupName: string,
  about: string,
  setGroupImage: func,
  setGroupBannerImage: func,
  setGroupBannerImageAlt: func,
  setGroupImageAlt: func,
  groupImage: object,
  groupBannerImage: object,
  isEditMode: bool,
  sectionsErrors: object,
  setSectionsErrors: func,
  widget: object,
  setWidget: func,
  setIsWidgetCodeValid: func
};

export default GroupSettings;
