import React, { useState, useContext, useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { AreaInput } from 'centralized-design-system/src/Inputs';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { CONFIG_TYPES_CONST } from 'centralized-design-system/src/Modals/AddUserTypeToObjectTypeModal/config';
import {
  createGroup,
  updateGroup,
  invite,
  deleteTeamUsers,
  deleteUsersFromTeam
} from 'edc-web-sdk/requests/groups.v2';
import { saveHtmlWidget, updateHtmlWidget } from 'edc-web-sdk/requests/htmlWidget';
import { translatr } from 'centralized-design-system/src/Translatr';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';

import { Permissions } from '../../../../app/utils/checkPermissions';

import GroupSettings from './GroupSettings';
import { CreateUpdateContext } from './CreateUpdateProvider';
import * as actionTypes from '../../../../app/constants/actionTypes';
import Spinner from '../../../../app/components/common/spinner';
import LD from '../../../../app/containers/LDStore';
import { FilterHostname } from '../../../../app/components/common/FilterHostname';
import { Button as MLMButton, Modal as MLMModal } from '@components/modals/MultipleLanguageModal';
import GroupUsersManagementSection from '@pages/group/creation/userManagement/GroupUsersManagementSection';

const groupFields = [
  {
    label: 'title',
    key: 'titles',
    title: 'Title',
    required: true,
    type: 'text',
    maxLen: 150
  },
  {
    label: 'message',
    key: 'descriptions',
    title: 'Description',
    required: true,
    type: 'editor',
    maxLen: 2000
  }
];

const CreateUpdateGroup = props => {
  const navigate = useNavigate();

  const [groupName, setGroupName] = useState('');
  const [about, setAbout] = useState('');
  const {
    groupDetail,
    updateGroupDetail,
    slug,
    isReloadContent,
    setIsValueChanged,
    learningMaterials,
    setLearningMaterials
  } = useContext(CreateUpdateContext);

  let groupDefaultTitle = '';
  let groupDefaultDescription = '';

  const getFieldLanguagesAttributes = fieldName =>
    groupDetail?.entityFields?.find(f => f.field_name === fieldName)
      ?.entity_field_languages_attributes || [];

  const [titleLanguages, setTitleLanguages] = useState(getFieldLanguagesAttributes('name'));
  const [descriptionLanguages, setDescriptionLanguages] = useState(
    getFieldLanguagesAttributes('description')
  );
  const [groupImage, setGroupImage] = useState(null);
  const [groupBannerImage, setGroupBannerImage] = useState(null);
  const [groupImageAlt, setGroupImageAlt] = useState('');
  const [groupBannerImageAlt, setGroupBannerImageAlt] = useState('');

  const [localAdminList, setLocalAdminList] = useState([]);
  const [localSubAdminList, setLocalSubAdminList] = useState([]);
  const [localMemberList, setLocalMemberList] = useState([]);
  const [localModeratorList, setLocalModeratorList] = useState([]);

  const [isUpdating, setIsUpdating] = useState(false);

  const [groupSettingPayload, setGroupSettingPayload] = useState({});
  const [open, setOpen] = useState(false);

  const [sectionsErrors, setSectionsErrors] = useState({ engagement: {}, widget: {} });
  const [isWidgetCodeValid, setIsWidgetCodeValid] = useState(true);
  const [widget, setWidget] = useState({});

  const isGroupCreationMode = !groupDetail;

  const postingFeedEnabled =
    groupSettingPayload?.enablePostingFeed ?? groupDetail?.enableActivityFeed;
  const isCommunity =
    postingFeedEnabled && (groupSettingPayload?.communityType ?? groupDetail?.communityType);

  const canUserEditRoles =
    isGroupCreationMode ||
    (groupDetail?.isTeamAdmin && Permissions.has('MANAGE_GROUP_ROLE')) ||
    Permissions.has('ADMIN_ONLY');

  if (isGroupCreationMode) {
    document.title = FilterHostname('Create Group');
  }
  const groupRef = useRef({ ...groupDetail });
  let rootLanguage = null;
  const rootTitles = {};
  const rootDescriptions = {};

  titleLanguages.forEach(titleLanguage => {
    rootTitles[titleLanguage.language] = titleLanguage.value;
    if (titleLanguage.is_default_language) {
      rootLanguage = titleLanguage.language;
    }
  });
  descriptionLanguages.forEach(descLanguage => {
    rootDescriptions[descLanguage.language] = descLanguage.value;
  });

  const handleCancel = () => {
    navigate(-1);
  };

  useEffect(() => {
    if (groupDetail && slug) {
      setGroupName(groupDetail?.name);
      setAbout(groupDetail?.description);
      setGroupImage(groupDetail?.imageUrls);
      setGroupBannerImage(groupDetail?.bannerImageUrls);
      setGroupImageAlt(groupDetail?.imageAltText || '');
      setGroupBannerImageAlt(groupDetail?.bannerimageAltText || '');
      setWidget(groupDetail?.widget);
      setLearningMaterials(mapFromLearningMaterials(groupDetail?.learningMaterials || []));
    }
  }, [groupDetail]);

  const clearState = () => {
    setGroupName('');
    setAbout('');
  };

  const _setGroupName = name => {
    setGroupName(name);
    let newTitleLanguages = [...titleLanguages];
    newTitleLanguages.forEach(tl => {
      if (tl.is_default_language) {
        tl.value = name;
      }
    });
  };

  const displayMessage = (message, snackbarType) => {
    props.dispatch({
      type: actionTypes.OPEN_SNACKBAR_V2,
      message: message,
      snackbarType,
      autoClose: true,
      translateMessage: false
    });
    setIsUpdating(false);
  };

  const updateDefaultValueField = (arrayOfObjects, newValue) => {
    return arrayOfObjects.map(obj => ({
      ...obj,
      value: obj.is_default_language ? newValue : obj.value
    }));
  };

  const getEntityFields = () => {
    let entityFields = [];
    let initialDataTitle = {};
    let initialDataDesc = {};
    let newTitleLanguages = titleLanguages;
    let newDescLanguages = descriptionLanguages;
    //eslint-disable-next-line no-unused-vars
    for (const field of groupRef.current?.entityFields || []) {
      const { field_name } = field;
      if (field_name === 'name') {
        initialDataTitle = field || {};
      } else if (field_name === 'description') {
        initialDataDesc = field || {};
      }
    }

    if (Object.keys(titleLanguages).length) {
      newTitleLanguages = updateDefaultValueField(titleLanguages, groupName);
      entityFields.push({
        field_name: 'name',
        searchable: true,
        entity_field_languages_attributes: newTitleLanguages,
        id: initialDataTitle?.id
      });
    }
    if (Object.keys(descriptionLanguages).length) {
      newDescLanguages = updateDefaultValueField(descriptionLanguages, about);
      entityFields.push({
        field_name: 'description',
        searchable: true,
        entity_field_languages_attributes: newDescLanguages,
        id: initialDataDesc?.id
      });
    }
    return entityFields?.length ? entityFields : null;
  };

  const handleClick = async () => {
    const communityTopicsShouldBeSet =
      groupSettingPayload.enablePostingFeed &&
      groupSettingPayload.communityType &&
      !groupSettingPayload.communityTopic &&
      (isGroupCreationMode || !groupDetail.communityType);

    if (communityTopicsShouldBeSet) {
      setSectionsErrors(prev => ({
        ...prev,
        engagement: {
          communityTopic: translatr('web.group.main', 'PleaseSelectAtLeastOneTopic')
        }
      }));
      return;
    }
    if (widget?.enabled && (!isWidgetCodeValid || !widget?.code || widget?.code.trim() === '')) {
      setSectionsErrors(prev => ({
        ...prev,
        widget: {
          code: translatr('web.common.main', 'FieldIsRequired')
        }
      }));
      return;
    }

    let payload = {};

    if (groupName && about) {
      const isMandatoryGroup =
        !LD.isMandatoryGroupEnable() && !groupSettingPayload.openGroup
          ? true
          : groupSettingPayload.mandatoryGroup;

      setIsUpdating(true);

      const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
      if (!slug) {
        payload = {
          name: groupName,
          description: about,
          only_admin_can_restrict: !groupSettingPayload?.onlyAdminCanRestrict,
          auto_assign_content: groupSettingPayload?.autoAssignContent,
          image: isLXMediaHubEnabled ? groupImage?.file : groupImage?.file?.url,
          bannerimage: isLXMediaHubEnabled ? groupBannerImage?.file : groupBannerImage?.file?.url,
          is_private: !groupSettingPayload?.openGroup,
          is_mandatory: isMandatoryGroup, // group may be mandatory only when group is private
          only_admin_can_post: !groupSettingPayload?.enableContentSharing,
          enable_activity_feed: groupSettingPayload?.enableActivityFeed,
          enable_posting_feed: groupSettingPayload?.enablePostingFeed,
          enable_skill_matrix: groupSettingPayload?.enableSkillMatrix,
          community_type: groupSettingPayload?.communityType,
          community_topic: groupSettingPayload?.communityTopic,
          entity_fields: getEntityFields(),
          isContentApproval: groupSettingPayload?.isContentApproval,
          learning_materials: mapToLearningMaterials(learningMaterials)
        };
        if (groupImageAlt.length) {
          payload['image_alt_text'] = groupImageAlt;
        }
        if (groupBannerImageAlt.length) {
          payload['bannerimage_alt_text'] = groupBannerImageAlt;
        }
        try {
          const resp = await createGroup(payload);
          await inviteUser(resp?.id);
          await saveUpdateWidget(resp?.id);
          clearState();
          displayMessage(translatr('web.group.main', 'GroupCreatedSuccessfully'), 'success');
          navigate(`/teams/${resp?.slug}`);
        } catch (err) {
          displayMessage(err?.body?.message || err?.response?.body?.message, 'error');
        }
      } else {
        payload = { ...groupDetail };
        payload['name'] = groupName;
        payload['description'] = about;
        payload['is_private'] =
          groupSettingPayload.openGroup !== undefined
            ? !groupSettingPayload.openGroup
            : groupDetail.isPrivate;
        payload['is_mandatory'] = groupSettingPayload?.mandatoryGroup;
        payload['only_admin_can_post'] =
          groupSettingPayload.enableContentSharing !== undefined
            ? !groupSettingPayload.enableContentSharing
            : groupDetail.only_admin_can_post;
        payload['only_admin_can_restrict'] = !groupSettingPayload?.onlyAdminCanRestrict;
        payload['auto_assign_content'] = groupSettingPayload?.autoAssignContent;
        payload['image'] = isLXMediaHubEnabled ? groupImage?.file : groupImage?.file?.url;
        payload['bannerimage'] = isLXMediaHubEnabled
          ? groupBannerImage?.file
          : groupBannerImage?.file?.url;
        payload['image_alt_text'] = groupImageAlt;
        payload['bannerimage_alt_text'] = groupBannerImageAlt;
        delete payload.enableActivityFeed;
        payload['enable_activity_feed'] = groupSettingPayload?.enableActivityFeed;
        payload['enable_skill_matrix'] = groupSettingPayload?.enableSkillMatrix;
        delete payload.enablePostingFeed;
        payload['enable_posting_feed'] = groupSettingPayload?.enablePostingFeed;
        delete payload.communityType; // Remove redundant key
        payload['community_type'] = groupSettingPayload?.communityType;
        payload['community_topic'] = groupSettingPayload?.communityTopic;
        payload['entity_fields'] = getEntityFields();
        delete payload['entityFields'];
        payload['is_content_approval'] = groupSettingPayload?.isContentApproval;
        delete payload['learningMaterials'];
        payload['learning_materials'] = mapToLearningMaterials(learningMaterials);
        delete payload.isContentApproval;
        try {
          let resp = await updateGroup(groupDetail?.id, payload);
          await inviteUser(groupDetail?.id);
          await saveUpdateWidget(groupDetail?.id);
          await deleteUsers();
          updateGroupDetail(resp);
          setIsValueChanged(false);
          navigate(`/teams/${slug}`);
        } catch (error) {
          console.error(error);
          displayMessage(error?.body?.message || error?.response?.body?.message, 'error');
          setIsUpdating(false);
          setIsValueChanged(false);
        }
      }
    }
  };

  const mapToLearningMaterials = materials => {
    return materials.map(material => {
      const mappedMaterial = {
        id: material.id,
        description: material.description,
        title: material.title,
        material_type: material.type
      };

      switch (material.type) {
        case 'file':
          mappedMaterial.material = material.data;
          break;
        case 'link':
        default:
          mappedMaterial.material = { url: material.data };
      }

      return mappedMaterial;
    });
  };

  const mapFromLearningMaterials = materials => {
    const LXMediaHubConfig = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
    return materials.map(material => {
      const mappedMaterial = {
        id: material.id,
        description: material.description,
        title: material.title,
        type: material.material_type
      };

      switch (material.material_type) {
        case 'file':
          mappedMaterial.data = LXMediaHubConfig
            ? material.material.media
            : material.material.filestack;
          break;
        case 'link':
        default:
          mappedMaterial.data = material.material.url;
      }

      return mappedMaterial;
    });
  };

  const deleteUsers = async () => {
    const hasRemainingActiveAdmins = () =>
      localAdminList.some(admin => !admin.isNew && !admin.isRemoved);
    const getIdsOfRemovedUsers = list => list.filter(user => user.isRemoved).map(user => user.id);

    const membersIdsToRemove = getIdsOfRemovedUsers(localMemberList);
    if (membersIdsToRemove.length) {
      await deleteUsersFromTeam(
        {
          user_ids: membersIdsToRemove
        },
        groupDetail?.id
      );
    }
    const subAdminsIdsToRemove = getIdsOfRemovedUsers(localSubAdminList);

    if (subAdminsIdsToRemove.length) {
      await deleteTeamUsers(groupDetail?.id, {
        role: CONFIG_TYPES_CONST.subAdmin,
        user_ids: subAdminsIdsToRemove
      });
    }
    const adminsIdsToRemove = getIdsOfRemovedUsers(localAdminList);
    if (hasRemainingActiveAdmins() && adminsIdsToRemove.length) {
      await deleteTeamUsers(groupDetail?.id, {
        role: CONFIG_TYPES_CONST.admin,
        user_ids: adminsIdsToRemove
      });
    }
    const moderatorsIdsToRemove = getIdsOfRemovedUsers(localModeratorList);
    if (moderatorsIdsToRemove.length) {
      await deleteTeamUsers(groupDetail?.id, {
        role: CONFIG_TYPES_CONST.moderator,
        user_ids: moderatorsIdsToRemove
      });
    }
  };

  const saveUpdateWidget = async groupId => {
    if (!widget || (!widget.id && !widget.enabled)) return;

    const widgetPayload = {
      id: widget.id,
      widget: {
        parent_id: groupId,
        parent_type: 'Team',
        context: 'team',
        enabled: widget.enabled,
        code: widget.code
      }
    };

    return widget.id ? updateHtmlWidget(widgetPayload) : saveHtmlWidget(widgetPayload);
  };

  const inviteUser = async groupId => {
    const hasNewUsersToInvite = list => list.some(user => user.isNew);
    const filterOnlyNewUsers = list => list.filter(user => user.isNew);

    const usersToInvite = {
      [CONFIG_TYPES_CONST.admin]: {
        shouldInvite: hasNewUsersToInvite(localAdminList),
        users: filterOnlyNewUsers(localAdminList)
      },
      [CONFIG_TYPES_CONST.subAdmin]: {
        shouldInvite: hasNewUsersToInvite(localSubAdminList),
        users: filterOnlyNewUsers(localSubAdminList)
      },
      [CONFIG_TYPES_CONST.moderator]: {
        shouldInvite: isCommunity && hasNewUsersToInvite(localModeratorList),
        users: filterOnlyNewUsers(localModeratorList)
      },
      [CONFIG_TYPES_CONST.member]: {
        shouldInvite: hasNewUsersToInvite(localMemberList),
        users: filterOnlyNewUsers(localMemberList)
      }
    };

    try {
      const inviteResults = {};

      for (const [roleType, { shouldInvite, users }] of Object.entries(usersToInvite)) {
        if (shouldInvite) {
          const payload = {
            roles: roleType,
            user_ids: users.map(user => user.id)
          };
          inviteResults[roleType] = await invite(groupId, payload);
        }
      }

      if (Object.values(inviteResults).find(result => result && result.message)) {
        displayMessage(translatr('web.group.main', 'SomeUsersWereAlreadyInvited'), 'error');
      } else {
        displayMessage(translatr('web.group.main', 'GroupUpdatedSuccessfully'), 'success');
      }
    } catch (error) {
      console.error('error at CreateUpdateGroup', error);
    }
  };

  const getTitles = (data, initialDataTitle) => {
    groupDefaultTitle = '';
    return Object.keys(data.languageValueMap.titles).map(title => {
      let obj = {
        value: data.languageValueMap.titles[title],
        language: title,
        is_default_language: data.defaultLanguage === title
      };
      if (data.defaultLanguage === title) {
        groupDefaultTitle = data.languageValueMap.titles[title];
      }

      const existingField = initialDataTitle.entity_field_languages_attributes?.filter?.(
        field => field.language === title
      )[0];
      if (existingField) {
        obj.id = existingField.id;
      }
      return obj;
    });
  };

  const getDescriptions = (data, initialDataDesc) => {
    groupDefaultDescription = '';
    return Object.keys(data.languageValueMap.descriptions).map(desc => {
      let obj = {
        value: data.languageValueMap?.descriptions[desc],
        language: desc,
        is_default_language: data.defaultLanguage === desc
      };
      if (data.defaultLanguage === desc) {
        groupDefaultDescription = data.languageValueMap.descriptions[desc];
      }

      const existingField = initialDataDesc.entity_field_languages_attributes?.filter?.(
        field => field.language === desc
      )[0];
      if (existingField) {
        obj.id = existingField.id;
      }
      return obj;
    });
  };

  const handleModalSave = data => {
    let initialDataTitle = {};
    let initialDataDesc = {};

    //eslint-disable-next-line no-unused-vars
    for (const field of groupRef.current?.entityFields || []) {
      if (field.field_name === 'name') {
        initialDataTitle = field || {};
      } else if (field.field_name === 'description') {
        initialDataDesc = field || {};
      }
    }
    let stateTitles = getTitles(data, initialDataTitle);
    let stateDescriptions = getDescriptions(data, initialDataDesc);

    //Add back in deleted language items
    data.deletedLanguages.forEach(deletedLanguage => {
      const existingDataTitle = initialDataTitle?.entity_field_languages_attributes?.find(
        stateTitle => stateTitle.language === deletedLanguage
      );
      const existingDataDescription = initialDataDesc?.entity_field_languages_attributes?.find(
        stateDesc => stateDesc.language === deletedLanguage
      );

      if (existingDataTitle) {
        stateTitles.push({
          id: existingDataTitle.id,
          is_default_language: false,
          language: existingDataTitle.language,
          value: existingDataTitle.value,
          _destroy: 1
        });
      }
      if (existingDataDescription) {
        stateDescriptions.push({
          id: existingDataDescription.id,
          is_default_language: false,
          language: existingDataDescription.language,
          value: existingDataDescription.value,
          _destroy: 1
        });
      }
    });

    setTitleLanguages(stateTitles);
    setDescriptionLanguages(stateDescriptions);
    setGroupName(groupDefaultTitle);
    setAbout(groupDefaultDescription);
  };

  return (
    <div className="block create-group-form justflex">
      {isReloadContent ? (
        <Spinner />
      ) : (
        <div className="group-form-content">
          <h1 className="text-center font-size-xxxl">
            {slug
              ? translatr('web.group.main', 'DetailsConfiguration')
              : translatr('web.group.main', 'NewGroup')}
          </h1>
          <h2 className="create-text-line" data-label={translatr('web.group.main', 'Details')}>
            <span aria-hidden="true">{translatr('web.group.main', 'Details')}</span>
          </h2>
          <div className="group-supporting-text font-size-l font-normal">
            {translatr('web.group.main', 'IndicatesRequired')}
          </div>
          <TextField
            id="groupName"
            placeholder={translatr('web.common.main', 'EnterGroupNameHere')}
            title={translatr('web.group.main', 'GroupName')}
            setValue={_setGroupName}
            defaultValue={groupName}
            name={translatr('web.group.main', 'Groupname')}
            type="text"
            required
            maxLen={150}
            shouldCheckForMaxChar={true}
            onChangeCB={() => setIsValueChanged(true)}
            isTranslated={true}
          />
          <AreaInput
            id="groupAbout"
            placeholder={translatr('web.common.main', 'EnterGroupDescriptionHere')}
            setValue={setAbout}
            title={translatr('web.group.main', 'About')}
            defaultTextInput={about}
            description={translatr(
              'web.group.main',
              'ADescriptionExplainingUsersWhatThisGroupIsAbout'
            )}
            optional={false}
            onChangeCB={() => setIsValueChanged(true)}
            isTranslated={true}
            required={true}
          />
          {LD.isMultiLangFeatureEnabled() && (
            <React.Fragment>
              <MLMButton
                modalHeaderText={translatr('web.multilang.multilang-modal', 'AddLanguages')}
                supportingText={translatr('web.group.main', 'DefineTranslationGroup')}
                openModal={() => setOpen(true)}
                isTranslated={true}
              />
              {open && (
                <MLMModal
                  onSave={handleModalSave}
                  rootLanguageValueMap={{ titles: rootTitles, descriptions: rootDescriptions }}
                  rootDefaults={{
                    titles: groupName,
                    descriptions: about
                  }}
                  fields={groupFields}
                  rootLanguage={rootLanguage}
                  showDefaultLanguageDropdown={true}
                  titlePlaceholder={translatr('web.common.main', 'EnterGroupNameHere')}
                  descriptionPlaceholder={translatr('web.common.main', 'EnterGroupDescriptionHere')}
                  titleField={translatr('web.group.main', 'GroupName')}
                  descriptionField={translatr('web.group.main', 'About')}
                  isDescriptionRequired={true}
                  buttonText={translatr('web.common.main', 'Add')}
                  defaultLanguageText={translatr('web.common.main', 'SelectLanguage')}
                  useCkEditor={false}
                  textInputRequired={true}
                  areaInputEnableMaxCharCheck={true}
                  closeModal={() => setOpen(false)}
                  modalHeaderText={translatr('web.multilang.multilang-modal', 'AddLanguages')}
                  isTranslated={true}
                />
              )}
            </React.Fragment>
          )}
          {canUserEditRoles && (
            <GroupUsersManagementSection
              moderatorOptionVisible={isCommunity}
              adminList={localAdminList}
              setAdminsList={setLocalAdminList}
              subAdminList={localSubAdminList}
              setSubAdminsList={setLocalSubAdminList}
              moderatorList={localModeratorList}
              setModeratorList={setLocalModeratorList}
              memberList={localMemberList}
              setMemberList={setLocalMemberList}
            />
          )}
          <GroupSettings
            groupSettingPayload={groupSettingPayload}
            setGroupSettingPayload={setGroupSettingPayload}
            groupName={groupName}
            about={about}
            groupImage={groupImage}
            setGroupImage={setGroupImage}
            setGroupImageAlt={setGroupImageAlt}
            groupBannerImage={groupBannerImage}
            setGroupBannerImage={setGroupBannerImage}
            setGroupBannerImageAlt={setGroupBannerImageAlt}
            setIsValueChanged={setIsValueChanged}
            maxLen={50}
            isEditMode={!isGroupCreationMode}
            sectionsErrors={sectionsErrors}
            setSectionsErrors={setSectionsErrors}
            widget={widget}
            setWidget={setWidget}
            setIsWidgetCodeValid={setIsWidgetCodeValid}
          />
          <div className="action-btn text-center mt-16 justflex align-center">
            <button className="ed-btn ed-btn-neutral" onClick={handleCancel}>
              {translatr('web.group.main', 'Cancel')}
            </button>
            <button
              className="ed-btn ed-btn-primary"
              disabled={isUpdating || !groupName?.trim() || !about?.trim()}
              onClick={handleClick}
            >
              {slug
                ? isUpdating
                  ? translatr('web.group.main', 'Updating')
                  : translatr('web.group.main', 'UpdateGroup')
                : isUpdating
                ? translatr('web.group.main', 'Creating')
                : translatr('web.group.main', 'CreateGroup')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default connect()(CreateUpdateGroup);
