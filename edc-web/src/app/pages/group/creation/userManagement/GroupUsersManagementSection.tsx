import React, { useContext } from 'react';
import UserRoleManagementBlock from '@pages/group/creation/userManagement/UserRoleManagementBlock';
import { Permissions } from '@utils/checkPermissions';
import { CreateUpdateContext } from '@pages/group/creation/CreateUpdateProvider';
import { User } from '@pages/group/common';

interface GroupUsersManagementSectionProps {
  moderatorOptionVisible: boolean;
  adminList: Array<User>;
  setAdminsList: (newList: Array<User>) => void;
  subAdminList: Array<User>;
  setSubAdminsList: (newList: Array<User>) => void;
  moderatorList: Array<User>;
  setModeratorList: (newList: Array<User>) => void;
  memberList: Array<User>;
  setMemberList: (newList: Array<User>) => void;
}


const GroupUsersManagementSection: React.FC<GroupUsersManagementSectionProps> = ({
  moderatorOptionVisible,
  adminList,
  setAdminsList,
  subAdminList,
  setSubAdminsList,
  moderatorList,
  setModeratorList,
  memberList,
  setMemberList
}) => {

  const {
    isCurrentUserIsAdmin,
  } = useContext(CreateUpdateContext);

  const hasPermissionToAddAdmins =
    (isCurrentUserIsAdmin && Permissions.has('ADMIN_ONLY')) ||
    Permissions.has('CREATE_GROUP_ADMIN');

  return (
    <ul className="section-list">
      <UserRoleManagementBlock
        usersList={adminList}
        setUsersList={setAdminsList}
        userRole={"admin"}
        excludedUsers={adminList}
      />
      {hasPermissionToAddAdmins &&
        <UserRoleManagementBlock
          usersList={subAdminList}
          setUsersList={setSubAdminsList}
          userRole={"sub_admin"}
          excludedUsers={subAdminList}
        />}
      {moderatorOptionVisible &&
        <UserRoleManagementBlock
          usersList={moderatorList}
          setUsersList={setModeratorList}
          userRole={"moderator"}
          excludedUsers={moderatorList}
        /> }
      <UserRoleManagementBlock
        usersList={memberList}
        setUsersList={setMemberList}
        userRole={"member"}
        excludedUsers={[...memberList, ...adminList, ...subAdminList, ...moderatorList]}
      />
    </ul>
  );
};

export default GroupUsersManagementSection;
