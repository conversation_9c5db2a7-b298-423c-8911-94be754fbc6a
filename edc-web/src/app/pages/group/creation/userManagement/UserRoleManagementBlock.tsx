import React, { useContext, useEffect, useState } from 'react';
import AddUserTypeToObjTypeSection from 'centralized-design-system/src/AddUserTypeToObjectTypeSection';
import { translatr } from 'centralized-design-system/src/Translatr';
import { CONFIG_TYPES_CONST } from 'centralized-design-system/src/Modals/AddUserTypeToObjectTypeModal/config';
import SearchOrRemoveUserFromList from 'centralized-design-system/src/SearchOrRemoveUserFromList';
import { CreateUpdateContext } from '@pages/group/creation/CreateUpdateProvider';
import {
  UserType,
  getUsers,
  User,
  UsersResponse
} from '@pages/group/common';

export const USERS_LIMIT = 10;

export const getUserRoleManagementBlockLabels = (userRole: UserType) => {
  switch(userRole) {
    case "admin":
      return {
        addHeader: translatr('web.group.main', 'AddGroupLeader'),
        searchPlaceholder: translatr('web.group.main', 'SearchGroupLeader'),
        removeHeader: translatr('web.group.main', 'GroupLeader')
      }
    case "sub_admin":
      return {
        addHeader: translatr('web.group.main', 'AddGroupAdmin'),
        searchPlaceholder: translatr('web.group.main', 'SearchGroupAdmin'),
        removeHeader: translatr('web.group.main', 'GroupAdmin')
      }
    case "moderator":
      return {
        addHeader: translatr('web.group.main', 'AddGroupModerator'),
        searchPlaceholder: translatr('web.group.main', 'SearchGroupModerator'),
        removeHeader: translatr('web.group.main', 'GroupModerator')
      }
    case "member":
      return {
        addHeader: translatr('web.group.main', 'AddGroupMember'),
        searchPlaceholder: translatr('web.group.main', 'SearchGroupMember'),
        removeHeader: translatr('web.group.main', 'GroupMember')
      }

  }
}

interface UserRoleManagementBlockProps {
  usersList: Array<User>;
  setUsersList(newUsers: Array<User>): void;
  userRole: UserType;
  excludedUsers?: Array<User>;
}

const UserRoleManagementBlock: React.FC<UserRoleManagementBlockProps> = ({ usersList, setUsersList, userRole, excludedUsers }) => {
  const { groupDetail, setIsValueChanged } = useContext(CreateUpdateContext);

  const isCreationMode = !groupDetail

  const [offset, setOffset] = useState(0);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const labels = getUserRoleManagementBlockLabels(userRole);

  useEffect(() => {
    if(isCreationMode) {
      return;
    }
    setIsLoading(true);
    getUsers(groupDetail?.id, userRole, USERS_LIMIT, 0)
      .then((response: UsersResponse) => {
        setUsersList(response.users);
        setTotal(response.total);
        setOffset(prevOffset => prevOffset + USERS_LIMIT)
      })
      .catch(() => {
        console.error("Error while fetching users");
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [groupDetail?.id])

  const createScrollHandler = () => {
    return (e: any) => {
      const { scrollHeight, scrollTop, clientHeight } = e.target;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 1;

      const hasMoreToLoad = usersList.length < total;

      if (isNearBottom && !isLoading && hasMoreToLoad) {
        setIsLoading(true);
        getUsers(groupDetail?.id, userRole, USERS_LIMIT, offset)
          .then((response: UsersResponse) => {
            if (response.users.length > 0) {
              setUsersList([...usersList, ...response.users])
              setOffset(prevOffset => prevOffset + USERS_LIMIT)
            }
          })
          .catch(() => {
            console.error("Error while fetching users");
          })
          .finally(() => {
            setIsLoading(false);
        });
      }
    };
  };

  const setUsers = (newUserList: Array<User>, userType: UserType) => {
    setIsValueChanged(true);
    newUserList.forEach(user => {
      user.isNew = true;
      user.role = userType;
    });
    setUsersList([...usersList, ...newUserList]);
  };

  const markRemovedUsers = (list: Array<User>, removedUsers: Array<User["id"]>): Array<User> => {
    const filteredList = list.filter(member => !(member.isNew && removedUsers.includes(member.id)));

    return filteredList.map(member => {
      if (removedUsers.includes(member.id)) {
        return {
          ...member,
          isRemoved: true
        };
      }
      return member;
    });
  };

  const calculateTotalUsersCount = () => {
    let newlyAddedUsers = usersList.filter(user => user.isNew).length;
    let removedUsers = usersList.filter(user => user.isRemoved).length;
    return total + newlyAddedUsers - removedUsers;
  }

  return (
    <li className="mt-16">
      <AddUserTypeToObjTypeSection
        btnLabel={labels.addHeader}
        headerLabel={labels.addHeader}
        objType={CONFIG_TYPES_CONST.groupObjType}
        userType={userRole}
        afterAddCallback={setUsers}
        required
        excludeUsers={excludedUsers || usersList}
        groupId={groupDetail?.id}
        maxLen={50}
      />
      <SearchOrRemoveUserFromList
        label={labels.searchPlaceholder}
        users={usersList.filter(user => !user.isRemoved)}
        userType={userRole}
        removeModalHeaderTitle={labels.removeHeader}
        removeUserFromListCallback={(removedUsers: Array<User["id"]>) => setUsersList(markRemovedUsers(usersList, removedUsers))}
        handleScroll={createScrollHandler()}
        isLoadMore={isLoading}
        totalMemberCount={calculateTotalUsersCount()}
        removeUserFromText={translatr('web.group.main', 'GroupsWithApostrophe')}
        maxLen={50}
      />
    </li>
  );
};

export default UserRoleManagementBlock;
