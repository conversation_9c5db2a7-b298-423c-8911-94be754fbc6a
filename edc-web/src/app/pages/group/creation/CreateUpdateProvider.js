import React, { createContext, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { getGroupDetails } from 'edc-web-sdk/requests/groups.v2';
import { getEntityFields } from 'edc-web-sdk/requests/cards.v2';
import { displayMessage } from './util';
import sortBy from 'lodash/sortBy';
import { node, bool, array } from 'prop-types';
import { getHtmlWidget } from 'edc-web-sdk/requests/htmlWidget';

export const CreateUpdateContext = createContext();

const CreateUpdateProvider = ({
  children,
  dispatch,
  isCurrentUserIsAdmin,
  isCurrentUserGroupLeader,
  isTeamActivityVisible,
  allowedMediaMimeTypes
}) => {
  const navigate = useNavigate();
  const { slug } = useParams();

  const [groupDetail, setGroupDetail] = useState(null);
  const [isReloadContent, setIsReloadContent] = useState(false);
  const [contentSections, setContentSections] = useState([]);
  const [isValueChanged, setIsValueChanged] = useState(false);
  const [learningMaterials, setLearningMaterials] = useState([]);

  const updateGroupDetail = payload => {
    setGroupDetail({ ...payload });
    setSections(payload.carousels);
  };

  const setSections = carousels => {
    const sortedArray = carousels && sortBy(carousels, o => o.index);
    const contentSectionList = sortedArray.map(carousel => {
      carousel.isSectionEditable = carousel.custom_carousel;
      carousel.isDraggable = true;
      carousel.isChangeVisibilityAllowed = carousel.default_label === 'Skills' ? false : true;
      carousel.isContentDraggable = true;
      carousel.isContentAdditionAllowed = true;
      carousel.isFeaturedSection = getIsFeaturedSection(carousel);
      carousel.isHardCoded = getIsFeaturedSection(carousel);

      return carousel;
    });
    setContentSections(contentSectionList);
  };

  const getIsFeaturedSection = carousel => {
    const labelCheck = carousel?.default_label?.toLowerCase();
    return labelCheck === 'featured' && !carousel?.custom_carousel;
  };

  useEffect(() => {
    if (groupDetail === null) loadInitialData();
  }, [groupDetail]);

  const updateContentSectionSequence = carousels => {
    setSections(carousels);
  };

  const loadInitialData = async () => {
    if (slug && !groupDetail) {
      setIsReloadContent(true);
      try {
        const resp = await getGroupDetails(slug, { default_translation: true });
        const entityFields = await getEntityFields({
          entity_id: resp.id,
          entity_type: 'Team',
          field_names: ['name', 'description']
        });
        resp.entityFields = [];
        if (entityFields.length > 0) {
          entityFields.forEach(element => {
            if (element?.entity_field?.field_name) {
              resp.entityFields.push({
                field_name: element.entity_field.field_name,
                searchable: false,
                id: element.entity_field.id,
                entity_field_languages_attributes: element?.entity_field_languages
              });
            }
          });
        }
        resp.defaultMultiLanguage = '';
        const widget = await getHtmlWidget({
          widget: { parent_id: resp.id, parent_type: 'Team', context: 'team' }
        });
        resp.widget = widget?.widgets?.length ? widget?.widgets[0] : {};
        updateGroupDetail(resp);
      } catch (error) {
        console.error(error);
        navigate('/org-groups');
        displayMessage(dispatch, 'You are not authorised to edit this group', 'error');
      }
      setIsReloadContent(false);
    }
  };

  const providerObject = {
    groupDetail,
    updateGroupDetail,
    slug,
    isReloadContent,
    contentSections,
    updateContentSectionSequence,
    dispatch,
    isCurrentUserIsAdmin,
    isCurrentUserGroupLeader,
    isValueChanged,
    setIsValueChanged,
    isTeamActivityVisible,
    learningMaterials,
    setLearningMaterials,
    allowedMediaMimeTypes
  };

  return (
    <CreateUpdateContext.Provider value={providerObject}>{children}</CreateUpdateContext.Provider>
  );
};

const mapStoreStateToProps = ({ currentUser, team }) => {
  const isTeamActivityVisible = team?.get('OrgConfig')?.leftRail?.['web/leftRail/teamActivity']
    ?.visible;

  return {
    isCurrentUserIsAdmin: currentUser?.get('isAdmin'),
    isCurrentUserGroupLeader: currentUser?.get('isGroupLeader'),
    isTeamActivityVisible,
    allowedMediaMimeTypes: team?.get('allowedMediaMimeTypes')
  };
};
CreateUpdateProvider.propTypes = {
  children: node,
  isCurrentUserIsAdmin: bool,
  isCurrentUserGroupLeader: bool,
  isTeamActivityVisible: bool,
  allowedMediaMimeTypes: array
};
export default connect(mapStoreStateToProps)(CreateUpdateProvider);
