import React, { useState, useEffect, useContext, useRef } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Switch from 'centralized-design-system/src/Switch';
import { isPostingFeedEnabled } from '../../consumption/GroupPosting/utils';
import './EngagementSection.scss';
import { CreateUpdateContext } from '../CreateUpdateProvider';
import { TopicFormData } from '../../manage_posting/types';
import CommunityOptions from './CommunityOptions';

interface EngagementSectionProps {
  groupSettingPayload: {
    enableActivityFeed: boolean,
    enablePostingFeed: boolean,
    communityType: string,
    communityTopic: TopicFormData
  },
  setGroupSettingPayload: (val: any) => void,
  isEditMode: boolean,
  sectionsErrors: {
    engagement: {
      communityTopic?: string
    }
  };
  setSectionsErrors: (val: any) => void;
}

const EngagementSection: React.FC<EngagementSectionProps> = ({ groupSettingPayload, setGroupSettingPayload, isEditMode, sectionsErrors, setSectionsErrors }) => {
  const { groupDetail, isTeamActivityVisible } = useContext(CreateUpdateContext);
  const [postingFeedEnabled, setPostingFeedEnabled] = useState(false);
  const [editingDisabled, setEditingDisabled] = useState(false);

  useEffect(() => {
    isPostingFeedEnabled()
      .then(setPostingFeedEnabled)
      .catch(() => console.error('Error while evaluating posting feed switch'));
  }, []);

  useEffect(() => {
    setEditingDisabled(isEditMode && groupDetail?.communityType);
    setGroupSettingPayload((prev: any) => ({
      ...prev,
      enableActivityFeed: isEditMode ? groupDetail?.enableActivityFeed : true,
      enablePostingFeed: groupDetail?.enablePostingFeed,
      communityType: groupDetail?.communityType || null
    }));
  }, [groupDetail]);

  const handleActivityFeedChange = (e: { target: { checked: boolean }; }) => {
    const isChecked = e.target.checked;
    setGroupSettingPayload((prev: any) => {
      const postingFeedEnabled = !isChecked && prev.enablePostingFeed;

      return {
        ...prev,
        enableActivityFeed: isChecked,
        enablePostingFeed: postingFeedEnabled,
        communityType: postingFeedEnabled ? prev.communityType : null,
        communityTopic: postingFeedEnabled ? prev.communityTopic : null
      }});
  };

  const handlePostingFeedChange = (e: { target: { checked: boolean }; }) => {
    const isChecked = e.target.checked;
    setGroupSettingPayload((prev: any) => {
      const postingFeedEnabled = isChecked;

      return {
        ...prev,
        enablePostingFeed: postingFeedEnabled,
        enableActivityFeed: !isChecked && prev.enableActivityFeed,
        communityType: postingFeedEnabled ? prev.communityType : null,
        communityTopic: postingFeedEnabled ? prev.communityTopic : null
      }
    });
  };

  return (
    (isTeamActivityVisible || postingFeedEnabled) && (
      <>
        <div className="group-engagement-section">
          <h2 className="create-text-line" data-label={translatr('web.group.main', 'Engagement')}>
            <span aria-hidden="true">{translatr('web.group.main', 'Engagement')}</span>
          </h2>
          {isTeamActivityVisible && (
            <Switch
              defaultChecked={groupSettingPayload.enableActivityFeed}
              onChange={handleActivityFeedChange}
              name={translatr('web.group.main', 'ActivityFeed')}
              disabled={editingDisabled}
              ariaChecked={groupSettingPayload.enableActivityFeed}
            />
          )}
          {postingFeedEnabled && (
            <>
              <Switch
                defaultChecked={groupSettingPayload.enablePostingFeed}
                onChange={handlePostingFeedChange}
                name={translatr('web.group.main', 'PostFeed')}
                disabled={editingDisabled}
                ariaChecked={groupSettingPayload.enablePostingFeed}
              />
              {groupSettingPayload.enablePostingFeed && (
                <CommunityOptions
                  sectionsErrors={sectionsErrors}
                  setSectionsErrors={setSectionsErrors}
                  groupSettingPayload={groupSettingPayload}
                  setGroupSettingPayload={setGroupSettingPayload}
                  editingDisabled={editingDisabled}
                />
              )}
            </>
          )}
        </div>
      </>
    )
  );
};

export default EngagementSection;
