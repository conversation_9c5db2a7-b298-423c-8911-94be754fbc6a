import React, { useContext, useState } from 'react';
import { Button } from 'centralized-design-system/src/Buttons';
import { translatr } from 'centralized-design-system/src/Translatr';
import TopicModal from '../../manage_posting/Topic/TopicModal';
import { TopicFormData } from '../../manage_posting/types';
import { OPEN_SNACKBAR_V2 } from '../../../../constants/actionTypes';
import { CreateUpdateContext } from '../CreateUpdateProvider';

const CreateTopicButton = ({ error, clearError, ref, saveTopicCallback }) => {
  const { dispatch } = useContext(CreateUpdateContext);

  const [openTopicForm, setOpenTopicForm] = useState(false);
  const [editedTopic, setEditedTopic] = useState(undefined);

  const handleSaveTopic = async (formData: TopicFormData) => {
    clearError();
    if (editedTopic) {
      showSuccessMessageOnDeleteTopic(translatr('web.group.main', 'TopicFormUpdatingSuccessfullyNotification'));
    } else {
      showSuccessMessageOnDeleteTopic(translatr('web.group.main', 'TopicFormSavingSuccessfullyNotification'));
    }

    setEditedTopic(formData);
    saveTopicCallback(formData);
    return Promise.resolve();
  };

  const removeTopic = () => {
    clearError();
    showSuccessMessageOnDeleteTopic(translatr('web.group.main', 'TopicDeletedSuccessMessage'));
    setEditedTopic(undefined);
    saveTopicCallback(undefined);
  };

  const showSuccessMessageOnDeleteTopic = (message: string) => {
    dispatch({
      type: OPEN_SNACKBAR_V2,
      message,
      snackbarType: 'success',
      autoClose: true,
      translateMessage: false
    });
  };

  return (
    <div>
      <div className={error && 'topic-error-button'}>
        <Button
          color="secondary"
          onClick={() => setOpenTopicForm(true)}
          ref={ref}
        >
          {editedTopic ? translatr('web.group.main', 'TopicModalEditTitle') : translatr('web.group.main', 'ButtonAddTopicLabel')}
        </Button>
        {editedTopic &&
          <Button
            color="secondary"
            variant="borderless"
            size="large"
            onClick={removeTopic}
          >
            <span className="icon-trash" aria-hidden="true"></span>
          </Button>
        }
      </div>
      {error && <span className="topic-error-message">{error}</span>}
      {openTopicForm &&
        <TopicModal
          handleClose={() => setOpenTopicForm(false)}
          editedTopic={editedTopic}
          onSaveTopic={handleSaveTopic}
        />
      }
    </div>
  );
};

export default CreateTopicButton;
