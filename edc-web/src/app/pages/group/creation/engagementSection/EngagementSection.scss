@import '~centralized-design-system/src/Styles/variables';

.ed-ui {
  .group-engagement-section {
    button.switch {
      &:not(:first-of-type) {
        padding-top: var(--ed-spacing-2xs);
      }

      &.switch {
        width: 20%;
        padding-left: 0;
        margin-bottom: var(--ed-spacing-2xs);
      }

      span {
        display: flex;
      }
    }

    &_learning-materials-buttons_container {
      display: flex;
      flex-direction: row;
      gap: var(--ed-spacing-base);

      .material-message {
        font-size: var(--ed-font-size-2xs);
      }
    }

    &_learning-materials-container {
      display: flex;
      flex-direction: row;
      column-gap: var(--ed-spacing-3xl);

      &-material {
        margin-top: var(--ed-spacing-sm);
        display: flex;
        flex-direction: row;
        align-items: flex-start;

        button:has(.icon-trash) {
          margin-top: 0;
        }

        &-data {
          display: flex;
          flex-direction: column;
          width: rem-calc(160);

          .learning-material-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: auto;
          }
        }
      }
    }

    &-info {
      font-size: var(--ed-font-size-sm);
      margin-bottom: var(--ed-spacing-4xs);
      margin-top: var(--ed-spacing-lg);
    }

    &-description {
      font-size: var(--ed-font-size-2xs);
      margin-bottom: var(--ed-spacing-2xs);
    }

    &-community-type {
      .group-engagement-section-radio-description {
        padding-left: rem-calc(36);
        font-size: var(--ed-font-size-2xs);
      }

      .topic-error-message {
        color: var(--ed-text-color-error);
        font-size: var(--ed-font-size-2xs);
      }

      .topic-error-button {
        button {
          border-color: var(--ed-text-color-error);
        }
      }

      button {
        margin: var(--ed-spacing-sm) 0 var(--ed-spacing-3xs) 0;

        &:not(:first-of-type) {
          margin-left: var(--ed-spacing-sm);
        }
      }

      button:has(.icon-trash) {
        padding: 0;
        width: auto;
      }

      a {
        margin: var(--ed-spacing-sm) 0 var(--ed-spacing-3xs) 0;
        display: inline-block;
      }

      .radio {
        padding-left: 0;
        padding-top: var(--ed-spacing-sm);
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        div {
          display: flex;
          align-items: center;

          span {
            font-size: var(--ed-font-size-base);
          }
        }
      }

      .icon-external-link {
        padding-right: var(--ed-spacing-2xs);
      }
    }

    .learning-material-form-container {
      display: flex;
      flex-direction: column;

      .learning-material-form-radio-container {
        margin-bottom: rem-calc(20);
      }
    }
  }
}
