import React, { useContext, useEffect, useRef } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Radio from 'centralized-design-system/src/RadioButtonV2';
import { ButtonLink } from 'centralized-design-system/src/Buttons';
import { COMMUNITY_TYPE } from '../../components/constants';
import CreateTopicButton from '../engagementSection/CreateTopicButton';
import { CreateUpdateContext } from '../../creation/CreateUpdateProvider';
import CreateLearningMaterialButton from './CreateLearningMaterialButton';
import { TopicFormData } from '../../manage_posting/types';
import MaterialsContainer from './MaterialsContainer';

interface CommunityOptionsProps {
  sectionsErrors: {
    engagement: {
      communityTopic?: string
    }
  };
  setSectionsErrors: (val: any) => void;
  groupSettingPayload: {
    enableActivityFeed: boolean,
    enablePostingFeed: boolean,
    communityType: string,
    communityTopic: TopicFormData
  },
  setGroupSettingPayload: (val: any) => void;
  editingDisabled: boolean;
}

const CommunityOptions: React.FC<CommunityOptionsProps> = ({ sectionsErrors, setSectionsErrors, groupSettingPayload, setGroupSettingPayload, editingDisabled }) => {
  const { slug } = useContext(CreateUpdateContext);

  const topicButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (sectionsErrors.engagement.communityTopic) {
      topicButtonRef.current?.focus();
    }
  }, [sectionsErrors.engagement]);

  const addLearningMaterialButton = (
    <CreateLearningMaterialButton />
  )

  const createTopicButton = (
    <CreateTopicButton
      error={sectionsErrors.engagement.communityTopic}
      clearError={() => setSectionsErrors((prev: any) => ({ ...prev, engagement: { communityTopic: undefined } }))}
      ref={topicButtonRef}
      saveTopicCallback={(formData: any) => setGroupSettingPayload((prev: any) => ({ ...prev, communityTopic: formData}))}
    />
  )

  const manageTopicsButton = (
    <ButtonLink
      color="secondary"
      to={`/teams/${slug}/manage-post`}
      target="_blank"
    >
      <span className="icon-external-link" aria-hidden="true"></span>
      {translatr('web.group.main', 'ManageTopics')}
    </ButtonLink>
  );

  const learningMaterialButtons = (
    <>
      <div className="group-engagement-section_learning-materials-buttons_container">
        {addLearningMaterialButton}
        {editingDisabled ? manageTopicsButton : createTopicButton}
      </div>
      <MaterialsContainer />
    </>
  )

  const communityOptions = [
    {
      label: translatr('web.group.main', 'NoCommunity'),
      value: null,
      defaultChecked: true
    },
    {
      label: translatr('web.group.main', 'TopicBasedCommunity'),
      value: COMMUNITY_TYPE.BASED_COMMUNITY,
      description: translatr('web.group.main', 'TopicBasedCommunityDescription'),
      additionalComponent: editingDisabled
        ? manageTopicsButton
        : createTopicButton
    },
    {
      label: translatr('web.group.main', 'LearningCommunity'),
      value: COMMUNITY_TYPE.LEARNING_COMMUNITY,
      description: translatr('web.group.main', 'LearningCommunityDescription'),
      additionalComponent: learningMaterialButtons
    }
  ];


  const handleChangeCommunityType = (e: any) => {
    setGroupSettingPayload((prev: any) => ({
      ...prev,
      communityType: e.target.value === '' ? null : e.target.value,
    }));
  };

  const isCommunitySelected = (communityType: string) => {
    return groupSettingPayload.communityType === communityType;
  };

  return (
    <>
      <div className="group-engagement-section-info">{translatr('web.group.main', 'Community')}</div>
      <div className="group-engagement-section-description">{translatr('web.group.main', 'CommunityDescription')}</div>
      {communityOptions.map((radio, index) =>
        <div className="group-engagement-section-community-type" key={`group-engagement-section-community-type-${index}`}>
          <Radio
            label={radio.label}
            groupName="group-engagement-section-community-type"
            keyName={`group-engagement-section-community-type-${index}`}
            value={radio.value || ''}
            checked={isCommunitySelected(radio.value)}
            disabled={editingDisabled}
            onChange={handleChangeCommunityType}
          />
          {radio.description &&
            <div className="group-engagement-section-radio-description">{radio.description}</div>
          }
          {isCommunitySelected(radio.value) &&
            radio.additionalComponent
          }
        </div>
      )}
    </>
  );
};

export default CommunityOptions;
