import React, { useContext } from 'react';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AreaInput } from 'centralized-design-system/src/Inputs';
import { translate } from '../../manage_posting/utils';
import Radio from 'centralized-design-system/src/RadioButtonV2';
import { LearningMaterialFormData } from './types';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { translatr } from 'centralized-design-system/src/Translatr';
import { FILESTACK_MAX_FILE_SIZE } from 'centralized-design-system/src/FileUpload/utils';
import { MEDIA } from 'centralized-design-system/src/Utils/Uploads/constants';
import { CreateUpdateContext } from '../CreateUpdateProvider';

interface LearningMaterialFormProps {
  data: LearningMaterialFormData,
  showErrors: boolean,
  errors: any,
  handleUpdateForm: (name: string, value: any) => void
}

const LearningMaterialForm: React.FC<LearningMaterialFormProps> = ({ data, showErrors, errors, handleUpdateForm }) => {
  const { allowedMediaMimeTypes } = useContext(CreateUpdateContext);
  const allTypesAllowed = allowedMediaMimeTypes
    ? [
        ...allowedMediaMimeTypes?.image,
        ...allowedMediaMimeTypes?.video,
        ...allowedMediaMimeTypes?.audio,
        ...allowedMediaMimeTypes?.doc
      ]
    : [];
  const learningMaterialOptions = [
    {
      label: translate('FileLabel'),
      value: 'file'
    },
    {
      label: translate('LinkLabel'),
      value: 'link'
    }
  ];

  return (
    <div className='learning-material-form-container'>
      <TextField
        id="title"
        title={translate('MaterialTitle')}
        placeholder={translate('MaterialTitle')}
        defaultValue={data.title}
        setValue={(newVal: string) => handleUpdateForm('title', newVal)}
        error={showErrors && errors?.name}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={100}
      />
      <AreaInput
        title={translate('MaterialDescription')}
        placeholder={translate('MaterialDescription')}
        defaultTextInput={data.description}
        setValue={(newVal: string) => handleUpdateForm('description', newVal)}
        isTranslated
        optional
        shouldCheckForMaxChar
        maxLen={200}
      />
      <div className='learning-material-form-radio-container'>
        <div>{translate('SelectTypeLabel')}</div>
        {learningMaterialOptions.map((radio, index) =>
          <div className="group-engagement-section-community-type" key={`group-engagement-section-community-material-type-${index}`}>
            <Radio
              label={radio.label}
              groupName="group-engagement-section-community-material-type"
              keyName={`group-engagement-section-community-material-type-${index}`}
              value={radio.value}
              checked={data.type === radio.value}
              onChange={(e: any) => handleUpdateForm('type', e.target.value)}
            />
          </div>
        )}
      </div>
      { data.type === 'link' ?
        <TextField
          id="link"
          title={translate('EnterLinkLabel')}
          placeholder={translate('EnterLinkLabel')}
          defaultValue={data.data}
          setValue={(newVal: string) => handleUpdateForm('data', newVal)}
          error={showErrors && errors?.name}
          isTranslated
          required
          shouldCheckForMaxChar
          maxLen={100}
        /> :
        <FileUpload
          label={translatr('cds.common.main', 'UploadFile')}
          persistedFile={data.data}
          setValue={(file: any) => handleUpdateForm('data', file?.file)}
          allowedFileType={allTypesAllowed}
          recommendedSize={FILESTACK_MAX_FILE_SIZE}
          isTranslated
          required
          uploadParams={{ isUGC: true, objectType: 'community_learning_materials', uploadType: MEDIA }}
        />
      }
    </div>
  );
};

export default LearningMaterialForm;
