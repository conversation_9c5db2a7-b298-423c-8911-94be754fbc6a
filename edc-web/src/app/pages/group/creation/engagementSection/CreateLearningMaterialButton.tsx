import React, { useContext, useState } from 'react';
import { Button } from 'centralized-design-system/src/Buttons';
import LearningMaterialModal from './LearningMaterialModal';
import { LearningMaterialFormData } from './types';
import { CreateUpdateContext } from '../CreateUpdateProvider';
import { translatr } from 'centralized-design-system/src/Translatr';

const CreateLearningMaterialButton = () => {
  const { learningMaterials, setLearningMaterials } = useContext(CreateUpdateContext);
  const [openLearningMaterialForm, setOpenLearningMaterialForm] = useState(false);
  const disableLearningMaterialButton = learningMaterials.length > 1;

  return (
    <>
      <div>
        <div>
          <Button color="secondary" onClick={() => setOpenLearningMaterialForm(true)} disabled={disableLearningMaterialButton}>
            {translatr('web.group.main', 'AddLearningMaterials')}
          </Button>
        </div>
        {disableLearningMaterialButton &&
          <span className="material-message">
            {translatr('web.group.main', 'MaximumLearningMaterials')}
          </span>
        }
      </div>
      {openLearningMaterialForm && (
        <LearningMaterialModal
          handleClose={() => setOpenLearningMaterialForm(false)}
          onSave={(formData: LearningMaterialFormData) => {
            setLearningMaterials((prev: []) => [ ...prev, formData]);
            setOpenLearningMaterialForm(false);
          }}
        />
      )}
    </>
  );
};

export default CreateLearningMaterialButton;
