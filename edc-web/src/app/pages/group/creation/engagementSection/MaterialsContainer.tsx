import React, { useContext, useEffect, useRef, useState } from 'react';
import { Button } from 'centralized-design-system/src/Buttons';
import { CreateUpdateContext } from '../../creation/CreateUpdateProvider';
import { LearningMaterial, LearningMaterialFormData } from '../engagementSection/types';
import LearningMaterialModal from './LearningMaterialModal';
import { displayMessage } from '../../creation/util';
import { translatr } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';

interface MaterialsContainerProps {
}

const isTextOverflowing = (element: HTMLElement) => {
  return element.scrollWidth > element.clientWidth;
};

const MaterialsContainer: React.FC<MaterialsContainerProps> = () => {
  const { learningMaterials, setLearningMaterials, dispatch } = useContext(CreateUpdateContext);
  const [openLearningMaterialForm, setOpenLearningMaterialForm] = useState(false);
  const [materialIndex, setMaterialIndex] = useState(0);
  const [hideMaterialTitleTooltip, setHideMaterialTileTooltip] = useState(true);
  const [hideMaterialDataTooltip, setHideMaterialDataTooltip] = useState(true);
  const materialTitleRef = useRef<HTMLDivElement>(null);
  const materialDataRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (materialTitleRef.current) {
      const shouldHideTooltip = !isTextOverflowing(materialTitleRef.current);
      setHideMaterialTileTooltip(shouldHideTooltip);
    }
    if (materialDataRef.current) {
      const shouldHideTooltip = !isTextOverflowing(materialDataRef.current);
      setHideMaterialDataTooltip(shouldHideTooltip);
    }
  }, [learningMaterials]);

  const removeMaterial = (index: number) => {
    setLearningMaterials((prev: any) => prev.filter((item: any, i: number) => i !== index));
    displayMessage(dispatch, translatr('web.group.main', 'MaterialDeletingSuccessfullyNotification'), 'success');
  }

  const materialData = (material: LearningMaterial) => material.type === 'link' ? material.data : material.data.filename;

  return (
    <div className="group-engagement-section_learning-materials-container">
      {learningMaterials.map((material: LearningMaterial, index: number) =>
        <div key={material.title} className="group-engagement-section_learning-materials-container-material">
          <div>
            <div className="group-engagement-section_learning-materials-container-material-data">
              <Tooltip message={material.title} hide={hideMaterialTitleTooltip}>
                <p ref={materialTitleRef} className="learning-material-text">{material.title}</p>
              </Tooltip>
              <Tooltip message={materialData(material)} hide={hideMaterialDataTooltip}>
                <p ref={materialDataRef} className="learning-material-text">{materialData(material)}</p>
              </Tooltip>
            </div>
            <Button
              color="secondary"
              onClick={() => {setOpenLearningMaterialForm(true); setMaterialIndex(index);}}
            >
              {translatr('web.group.main', 'MaterialEditLabel')}
            </Button>
          </div>
          <Button
            color="secondary"
            variant="borderless"
            size="large"
            onClick={() => removeMaterial(index)}
          >
            <span className="icon-trash" aria-hidden="true"></span>
          </Button>
        </div>
      )}
      {openLearningMaterialForm && (
        <LearningMaterialModal
          handleClose={() => setOpenLearningMaterialForm(false)}
          editedLearningMaterial={learningMaterials[materialIndex]}
          onSave={(formData: LearningMaterialFormData) => {
            setLearningMaterials((prev: LearningMaterial[]) =>
              learningMaterials[materialIndex]
                ? prev.map((item, index) => index === materialIndex ? formData : item)
                : [...prev, formData]
            );
            setOpenLearningMaterialForm(false);
          }}
        />
      )}
    </div>
  );
};

export default MaterialsContainer;
