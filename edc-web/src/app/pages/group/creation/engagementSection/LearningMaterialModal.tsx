import React, { useContext, useState } from 'react';
import { isEmpty } from 'lodash';
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from 'centralized-design-system/src/Modals';
import { Button } from 'centralized-design-system/src/Buttons';
import { translatr } from 'centralized-design-system/src/Translatr';
import FocusLock from 'react-focus-lock';
import { LearningMaterial, LearningMaterialFormData, LearningMaterialFormError } from './types';
import LearningMaterialForm from './LearningMaterialForm';
import { translate } from '../../manage_posting/utils';
import { CreateUpdateContext } from '../../creation/CreateUpdateProvider';
import { displayMessage } from '../../creation/util';

const obtainInitialFormData = (isEdit: boolean, editedLearningMaterial: LearningMaterial): LearningMaterialFormData => {
  if(isEdit) {
    return {
      id: editedLearningMaterial.id,
      title: editedLearningMaterial.title,
      description: editedLearningMaterial.description,
      type: editedLearningMaterial.type,
      data: editedLearningMaterial.data
    }
  }
  return {
    id: undefined,
    title: "",
    description: "",
    type: 'file',
    data: null
  }
}

interface LearningMaterialModalProps {
  handleClose(): void,
  editedLearningMaterial?: LearningMaterial,
  onSave(formData: LearningMaterialFormData): Promise<any>
}

const LearningMaterialModal: React.FC<LearningMaterialModalProps> = ({ handleClose, editedLearningMaterial, onSave }) => {
  const { dispatch } = useContext(CreateUpdateContext);
  const isEdit = !isEmpty(editedLearningMaterial);

  const [formData, setFormData] = useState<LearningMaterialFormData>(obtainInitialFormData(isEdit, editedLearningMaterial));
  const [errors, setErrors] = useState<LearningMaterialFormError>({});
  const [showErrors, setShowErrors] = useState<boolean>(false);

  const handleUpdateForm = (name: keyof LearningMaterial, newValue: any) => {
    const newData = {
      ...formData,
      [name]: newValue
    }

    setFormData(newData)
    setErrors(validateForm(newData));
  }

  const validateForm = (badgeFormData: LearningMaterialFormData): LearningMaterialFormError => {
    const errors: LearningMaterialFormError= {};
    return errors;
  }

  const focusOnFirstErroredField = () => {
    const errorFields = Object.keys(errors) as (keyof LearningMaterialFormError)[];
    for (const field of errorFields) {
      if (errors[field]) {
        const element = document.getElementById(field);
        if (element) {
          element.focus();
          break;
        }
      }
    }
  };

  const handleSave = () => {
    const errors = validateForm(formData);
    if(Object.keys(errors).length > 0) {
      setErrors(errors);
      setShowErrors(true);
      focusOnFirstErroredField();
      return;
    }
    onSave(formData);
    if (isEdit) {
      displayMessage(dispatch, translatr('web.group.main', 'MaterialUpdatingSuccessfullyNotification'), 'success');
    } else {
      displayMessage(dispatch, translatr('web.group.main', 'MaterialSavingSuccessfullyNotification'), 'success');
    }
  }

  return (
    <Modal size="small">
      <FocusLock>
        <ModalHeader
          title={isEdit ? translate('MaterialModalEditTitle') : translate('MaterialModalCreateTitle')}
          onClose={handleClose}
        />
        <ModalContent>
          <LearningMaterialForm
            data={formData}
            showErrors={showErrors}
            errors={errors}
            handleUpdateForm={handleUpdateForm}
          />
        </ModalContent>
        <ModalFooter>
          <div>
            <Button
              color="secondary"
              variant="ghost"
              onClick={handleClose}
            >
              {translatr('web.common.main', 'Cancel')}
            </Button>
            <Button color="primary" onClick={() => handleSave()}>
              {translate('Save')}
            </Button>
          </div>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

export default LearningMaterialModal;
