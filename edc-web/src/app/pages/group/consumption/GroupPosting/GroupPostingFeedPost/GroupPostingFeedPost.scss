@import '~centralized-design-system/src/Styles/_variables.scss';

.posting-feed-post {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 16px;
  background: var(--ed-body-bg-color);
  border-radius: var(--ed-border-radius-md);
  box-shadow: var(--ed-shadow-sm);
  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    & > .author {
      display: flex;
      flex-direction: row;
      gap: 8px;
      & > .ed-avatar {
        min-width: var(--ed-avatar-size-lg);
        height: var(--ed-avatar-size-lg);
        margin: 0;
        align-self: center;
        cursor: pointer;
      }
      & > .name-and-title {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-self: center;
        & > .name {
          font-size: var(--ed-font-size-sm);
          font-weight: var(--ed-font-weight-bold);
          color: var(--ed-black);
          line-height: 19px;
          cursor: pointer;
        }
        & > .title {
          font-size: var(--ed-font-size-sm);
          color: var(--ed-gray-7);
          line-height: 21px;
        }
      }
    }
    & > .actions {
      display: flex;
      align-items: center;

      & .ed-dropdown {
        margin: 0;
        & .dropdown-content {
          width: 219px;
          border-radius: 4px;
          padding: 8px;
          & li {
            margin: 0 0 4px 0 !important;
            &:last-child {
              margin: 0 !important;
            }
            & > button {
              color: var(--ed-gray-6);
              font-size: var(--ed-font-size-sm);
              padding: 5px 8px !important;
              line-height: 24px;
            }
          }
        }
      }
      & .icon-ellipsis-h {
        width: 24px;
        max-width: 24px;
        height: 24px;
        color: var(--ed-gray-6);
        font-size: 24px;
        font-weight: bold;
      }
      & .updatedBy {
        margin-right: 15px;
        font-size: var(--ed-font-size-sm);
        line-height: 21px;
      }
      & .createdAt {
        margin-right: 5px;
        font-size: var(--ed-font-size-sm);
        line-height: 21px;
      }
    }
  }
  &-content {
    line-height: 21px;
    font-size: var(--ed-font-size-sm);
    color: var(--ed-gray-6);
    overflow: hidden;
    overflow-wrap: break-word;
    & ol {
      list-style: decimal !important;
    }
    & ul {
      list-style: disc !important;
    }
    & a {
      color: var(--ed-state-active-color);
      text-decoration: underline;
      &:hover {
        color: var(--ed-state-active-color);
        background-color: var(--ed-input-hover-bg-color);
      }
    }
    & > p > .text-comment-at > .mention {
      color: var(--ed-gray-6);
      font-weight: var(--ed-font-weight-bold);
      text-decoration: none;
    }
  }
  &-attachments {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: space-between;
  }
  &-attachment {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    border: 1px solid var(--ed-gray-2);
    border-radius: var(--ed-border-radius-md);
    height: 100px;
    max-height: 344px;
    flex-basis: calc(50% - 4px);
    gap: 16px;
    cursor: pointer;
    &:hover {
      background: var(--ed-state-hover-bg-color-primary);
    }
    &:focus {
      outline: var(--ed-primary-darken-1) solid 2px;
      outline-offset: 0;
      border-radius: var(--ed-border-radius-md);
    }
    &-preview {
      flex-basis: 65% !important;
    }
    & > .post-info {
      display: flex;
      flex-direction: row;
      gap: 8px;
      text-align: start;
      align-items: center;
      & > img {
        width: var(--ed-avatar-size-lg);
        height: var(--ed-avatar-size-lg);
        border-radius: var(--ed-border-radius-md);
      }
      & > .name-and-type {
        display: flex;
        flex-direction: column;
        gap: 2px;
        & > .name {
          overflow-wrap: anywhere;
          font-weight: var(--ed-font-weight-black);
          color: var(--ed-black);
          line-height: 21px;
          @supports (-webkit-line-clamp: 2) {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: initial;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
        & > .type {
          font-size: var(--ed-font-size-sm);
          color: var(--ed-gray-6);
          line-height: 21px;
        }
      }
    }
    & > .preview {
      width: 100%;
    }
  }
  &-single-image-attachment {
    max-height: 495px;
    background: var(--ed-black);
  }
  &-stats {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    color: var(--ed-gray-6);
    line-height: 24px;
    font-size: var(--ed-font-size-sm);
    font-weight: var(--ed-font-weight-bold);
    & > span {
      display: inline-flex;
      gap: 4px;
    }
    & .like-icon {
      margin: 0 !important;
      line-height: 24px !important;
      min-width: var(--ed-avatar-size-sm);
      min-height: var(--ed-avatar-size-sm);
    }
    & .comments-and-shares {
      display: inline-flex;
      gap: 8px;
      margin-left: 20px;

      & > button {
        font-size: var(--ed-font-size-sm);
        font-weight: var(--ed-font-weight-bold);
        color: var(--ed-gray-6);
      }

      &__active {
        cursor: pointer;
      }
    }
    &__right-column {
      display: flex;
      align-items: center;
    }
  }
  &__topic-name {
    color: var(--ed-primary-base);
    overflow-wrap: anywhere;
  }
  &-actions {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    & > button {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      color: var(--ed-gray-6);
      font-weight: var(--ed-font-weight-normal);
      & > i {
        font-size: 19px;
        display: flex;
        align-content: center;
        width: var(--ed-avatar-size-md);
        height: var(--ed-avatar-size-md);
        margin: 0;
      }
      & > span {
        line-height: 24px;
        font-size: var(--ed-font-size-base);
      }
    }
  }
  &-separator {
    width: 100%;
    border-bottom: 1px solid var(--ed-gray-2);
  }
}
