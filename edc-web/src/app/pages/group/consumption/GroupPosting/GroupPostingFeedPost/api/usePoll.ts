import React, { useState } from 'react';

const submitAnswer = new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve({});
  }, 500);
});

export const usePoll = ({ onSuccess = () => {} }) => {
  const [pollSaving, setPollSaving] = useState(false);

  const submit = (answers) => {
    setPollSaving(true);
    submitAnswer.then((data) => {
      setPollSaving(false);
      onSuccess();
    }).catch((err: any) => {
      setPollSaving(false);
      console.error(`Error in teams._submitAnswer.func: ${err}`);
    });
  };

  return {
    submit,
    pollSaving
  }
};