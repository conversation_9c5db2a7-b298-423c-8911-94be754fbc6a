@import '~centralized-design-system/src/Styles/_variables.scss';

.group-posting-feed-post-questionnaire {
  &__question {
    margin-bottom: 20px;
  }

  &__option {
    display: flex;
    align-items: center;
    margin: 5px 0;
    padding-left: 20px;
    height: 60px;
    line-height: 50px;
    border: solid 1px var(--ed-gray-2);
    border-radius: var(--ed-border-radius-md);
    justify-content: space-between;

    &__checkable {
      cursor: pointer;
    }

    &__correct,
    &__incorrect {
      border-color: var(--ed-primary-base);
    }

    & i {
      font-size: 18px;
    }

    &__answer {
      display: flex;
      align-items: center;
    }

    &__result {
      display: flex;
      flex-direction: column;
      margin-right: 10px;

      & > div {
        line-height: 25px;
        text-align: right;
      }
    }
  }

  &__icon-container {
    display: flex;
    width: rem-calc(20);
    height: rem-calc(20);
    color: var(--ed-white);
    margin-right: 10px;

    & i.icon-check-circle-light {
      color: var(--ed-primary-base);
    }

    & i.icon-cross-circle {
      color: var(--ed-negative-2);
    }
  }
}
