import React, { useState } from 'react';
import { translatr } from "centralized-design-system/src/Translatr";
import { Button } from "centralized-design-system/src/Buttons";
import cn from 'classnames';
import { usePoll } from './api/usePoll';
import './GroupPostingFeedPostQuestionnaire.scss';

interface OptionStatusIconProps {
  iconName: string;
}

const OptionStatusIcon = ({ iconName }: OptionStatusIconProps) => (
  <span className="group-posting-feed-post-questionnaire__icon-container">
    <i className={iconName} />
  </span>
);

interface GroupPostingFeedPostQuestionnaireProps {
  questionnaire: any;
  quizEnabled: boolean;
}

const GroupPostingFeedPostQuestionnaire = ({ questionnaire, quizEnabled }: GroupPostingFeedPostQuestionnaireProps) => {
  const [checkedOptions, setCheckedOptions] = useState([]);
  
  const answeredQuestions = checkedOptions.reduce((acc, curr) => {
    const [questionId] = curr.split("_");
    return {
      ...acc,
      [questionId]: acc[questionId] ? acc[questionId] += 1 : 1
    };
  }, {});

  const isQuestionnaireDisabled = questionnaire.questions.length !== Object.keys(answeredQuestions).length;

  const { pollSaving, submit } = usePoll({});

  const handleOptionClick = (questionId: number, optionId: number) => {
    if (questionnaire?.answered) {
      return;
    }

    if (checkedOptions.includes(`${questionId}_${optionId}`)) {
      setCheckedOptions((prevOptions) => prevOptions.filter((itemId) => itemId !== `${questionId}_${optionId}`));
    } else {
      setCheckedOptions((prevOptions) => [
        ...prevOptions, `${questionId}_${optionId}`
      ]);
    }
  };

  const handleVote = () => {
    let answers = {};
    checkedOptions.forEach((item) => {
      const [questionId, optionId] = item.split("_");
      answers[questionId] = answers[questionId] ? [...answers[questionId], optionId] : [optionId];
    });

    submit(answers);
  }

  if (questionnaire === undefined) {
    return null;
  }

  return (
    <div className="group-posting-feed-post-questionnaire">
      {questionnaire.questions.map((questionItem) => (
        <div key={questionItem.id} className="group-posting-feed-post-questionnaire__question">
          {questionItem.question}
          {questionItem.options.map((option) => {
            const correctAnswer = quizEnabled && questionItem.attempts.includes(option.id) && option.correct;
            const incorrectAnswer = quizEnabled && questionItem.attempts.includes(option.id) && !option.correct;
            return (
              <div
                key={option.id}
                className={cn("group-posting-feed-post-questionnaire__option", {
                  "group-posting-feed-post-questionnaire__option__checkable": !questionnaire.answered,
                  "group-posting-feed-post-questionnaire__option__correct": correctAnswer,
                  "group-posting-feed-post-questionnaire__option__incorrect": incorrectAnswer,
                })}
                onClick={() => handleOptionClick(questionItem.id, option.id)}
              >
                <div className="group-posting-feed-post-questionnaire__option__answer">
                  {checkedOptions.includes(`${questionItem.id}_${option.id}`) &&
                    <OptionStatusIcon iconName="icon-check-circle-light" />
                  }
                  {correctAnswer && <OptionStatusIcon iconName="icon-check-circle-light" />}
                  {incorrectAnswer && <OptionStatusIcon iconName="icon-cross-circle" />}
                  {option.text}
                </div>
                {(questionnaire.answered && !quizEnabled) &&
                  <div className="group-posting-feed-post-questionnaire__option__result">
                    <div>{`${option.count/questionnaire.attemptsCount * 100}%`}</div>
                    <div>{option.count ?
                      translatr('web.common.main', 'CountVotes', { count: option.count }) :
                      translatr('web.common.main', 'NoVotesYet')}
                    </div>
                  </div>
                }
              </div>
            );
          })}
        </div>
      ))}
      <Button
        color="primary"
        size="large"
        onClick={handleVote}
        disabled={isQuestionnaireDisabled}
      >
        <span>{translatr("web.common.main", "Vote")}</span>
      </Button>
    </div>
  )
};

export default GroupPostingFeedPostQuestionnaire;
