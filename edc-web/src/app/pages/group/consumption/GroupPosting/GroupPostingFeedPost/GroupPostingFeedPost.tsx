import React, { useContext, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from 'react-router-dom';
import moment from 'moment-timezone';
import Avatar from "centralized-design-system/src/Avatar";
import { translatr } from "centralized-design-system/src/Translatr";
import { Button } from "centralized-design-system/src/Buttons";
import getStaticImgPath from "edc-web-sdk/helpers/getStaticImgPath";
// import addSecurity from "edc-web-sdk/helpers/filestackSecurity";
// import { FILESTACK_DEFAULT_EXPIRY } from "edc-web-sdk/config/envConstants";
import DropdownActions from "@components/ProjectCard/shared/DropdownActions";
import { GroupConsumptionContext } from "@pages/group/consumption/GroupConsumptionProvider";
import { Permissions } from "@utils/checkPermissions";
import { Post } from "../../../components/types";
import PostCreateEditModal from "../../../components/PostCreateEdit/PostCreateEditModal"
import { MIMETYPE_EXTENSION_THUMBNAIL_MAP } from "../utils";
import PostingFeedCommentsContainer from "../GroupPostingFeedComment/PostingFeedCommentsContainer";
import PdfAttachmentPreview from "./PdfAttachmentPreview";
import GroupPostingFeedPostContent from "./GroupPostingFeedPostContent";
import GroupPostingFeedPostQuestionnaire from "./GroupPostingFeedPostQuestionnaire";
import "./GroupPostingFeedPost.scss";

interface FeedPostAttachmentProps {
  name: string;
  mimetype: string;
  url: string;
  totalNumberOfAttachments?: number;
}
const FeedPostAttachment = ({ name, mimetype, url, totalNumberOfAttachments }: FeedPostAttachmentProps) => {
  const [type, fileType] = mimetype?.split("/");
  const thumbnailType = MIMETYPE_EXTENSION_THUMBNAIL_MAP[fileType] || MIMETYPE_EXTENSION_THUMBNAIL_MAP[type] || "default";
  const attachmentIcon = thumbnailType === "image" ? url : getStaticImgPath(`/i/images/${thumbnailType}-attachment-icon.png`);
  const withPdfPreview = totalNumberOfAttachments === 1 && fileType === "pdf";

  return (
    <a
      className={`posting-feed-post-attachment ${withPdfPreview ? "posting-feed-post-attachment-preview" : ""}`}
      href={url}
      download
    >
      <div className="post-info">
        <img src={attachmentIcon} alt="" />
        <div className="name-and-type">
          <span className="name" title={name}>{name}</span>
          <span className="type">{fileType?.toUpperCase()}</span>
        </div>
      </div>
      {withPdfPreview && (
        <>
          <div className="posting-feed-post-separator"></div>
          <div className="preview">
            <PdfAttachmentPreview url={url} title="" />
          </div>
        </>
      )}
    </a>
  );
};

interface FeedPostSingleImageAttachmentProps {
  url: string;
}
const FeedPostSingleImageAttachment = ({ url }: FeedPostSingleImageAttachmentProps) => {
  return (
    <div className="posting-feed-post-single-image-attachment">
      <img src={url} alt=""/>
    </div>
  );
};

const formatVotesNumber = (votes: number) => votes > 1000 ? `${(votes / 1000).toFixed(1)}k` : votes;

interface GroupPostingFeedPostProps {
  post: Post;
  removePost: () => void;
  likePost: () => void;
  bookmarkPost: () => void;
  sharePost: () => void;
  reportPost: () => void;
  onPostEdit: (post: Post) => void;
  onCommentsTotalChange: (commentsCount: number) => void;
}
const GroupPostingFeedPost = ({ post, removePost, likePost, bookmarkPost, sharePost, reportPost, onPostEdit, onCommentsTotalChange }: GroupPostingFeedPostProps) => {
  const navigate = useNavigate();

  const currentUser = useSelector((state: any) => state.currentUser?.toJS());
  const { groupDetails } = useContext(GroupConsumptionContext);

  const isAdmin = groupDetails?.isTeamAdmin || groupDetails?.isTeamSubAdmin || Permissions.has("ADMIN_ONLY");
  const isAuthor = currentUser?.id === `${post?.user?.id}`;
  const isGroupMember = groupDetails?.userBelongsToGroup;

  const canInteractWithLikes = isGroupMember && Permissions.has("LIKE_CONTENT");
  const canInteractWithComments = isGroupMember && Permissions.has("CREATE_COMMENT");
  const hasAnyInteractions = canInteractWithLikes || canInteractWithComments;

  const [menu, setMenu] = useState({
    loading: false,
  });
  const [editPostModalOpen, setEditPostModalOpen] = useState(false);

  const [authorsAvatarData, setAuthorsAvatarData] = useState({});
  useEffect(() => {
    setAuthorsAvatarData({
      name: post.user?.name,
      imgUrl: post.user?.avatarimages?.medium || post?.user?.avatar,
      handle: post.user?.handle
    });
  }, [post]);

  const [commentInputVisible, setCommentInputVisible] = useState(false);
  const [commentListVisible, setCommentListVisible] = useState(false);
  const commentInputRef = useRef();

  const onComment = () => {
    setCommentInputVisible(value => !value);
    setTimeout(() => {
      // @ts-ignore
      commentInputRef.current?.focus();
    });
  };

  const toggleCommentList = () => {
    if (post.commentsCount > 0) {
      setCommentListVisible(value => !value);
    }
  };

  const handleCloseEditPost = (post: Post) => {
    if (post) {
      onPostEdit(post);
    }
    setEditPostModalOpen(false);
  };

  const menuOptions = [
    ...(isAdmin || isAuthor ? [{
      id: "edit",
      label: translatr("web.common.main", "Edit"),
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        setEditPostModalOpen(true);
      },
    }] : []),
    {
      id: "report",
      label: translatr("web.common.main", "Report"),
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        reportPost();
      },
    },
    ...(isAdmin || isAuthor ? [{
      id: "delete",
      label: translatr("web.common.main", "Delete"),
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        setMenu(m => ({ ...m, loading: true }));
        removePost();
      },
    }] : [])
  ];

  // Mocked data for poll/quiz
  /* const answered = false;
  const questionnaire = {
    type: 'poll',
    answered,
    attemptsCount: 1,
    questions: [{
      id: '1',
      question: 'Question Test 1',
      attempts: answered ? [2] : [],
      options: [{ id: 1, text: 'Option 1', count: 1, correct: true }, { id: 2, text: 'Option 2', count: 0 }, { id: 3, text: 'Option 3', count: 0 }]
    }, {
      id: '2',
      question: 'Question Test 2',
      attempts: answered ? [3] : [],
      options: [{ id: 1, text: 'Option 21', count: 1 }, { id: 2, text: 'Option 22', count: 0, correct: true }, { id: 3, text: 'Option 23', count: 0, correct: true }]
    }, {
      id: '3',
      question: 'Question Test 3',
      attempts: answered ? [2] : [],
      options: [{ id: 1, text: 'Option 31', count: 1, correct: true }, { id: 2, text: 'Option 32', count: 0 }]
    }, {
      id: '4',
      question: 'Question Test 4',
      attempts: answered ? [2] : [],
      options: [{ id: 1, text: 'Option 41', count: 0, correct: true }, { id: 2, text: 'Option 42', count: 1, correct: true }, { id: 3, text: 'Option 43', count: 0 }, { id: 4, text: 'Option 44', count: 0 }]
    }]
  }; */

  return (
    <div className="posting-feed-post">
      <div className="posting-feed-post-header">
        <div className="author">
          <Avatar user={authorsAvatarData} blankAlt />
          <div className="name-and-title">
            <button className="name" onClick={() => navigate(`/${post.user?.handle}`)}>{post.user?.name}</button>
            <span className="title">{post.user?.jobTitle}</span>
          </div>
        </div>
        <div data-testid="post-actions" className="actions">
          {post.updatedBy && (
            <div className="updatedBy">{`${translatr('web.common.main', 'EditedBy')}: ${post.updatedBy.name}`}</div>
          )}
          <div className="createdAt">{moment(post.createdAt).format('DD MMM YYYY, HH:mm A')}</div>
          <DropdownActions
            actions={menuOptions}
            isLoading={menu.loading}
            loadingItemsNumber={menuOptions?.length}
          />
        </div>
      </div>
      {(post.questionnaire) ?
        <GroupPostingFeedPostQuestionnaire
          questionnaire={post.questionnaire}
          quizEnabled={post.questionnaire.type === "quiz"}
        /> :
        <GroupPostingFeedPostContent
          content={post.content}
          mentions={post.mentions}
        />
      }
      {/*<div className="posting-feed-post-attachments">*/}
      {/*  {post.filestack?.map(file => file.mimetype.startsWith("image/") && post.filestack?.length === 1 ?*/}
      {/*    <FeedPostSingleImageAttachment*/}
      {/*      key={file.handle}*/}
      {/*      url={addSecurity(file.url, FILESTACK_DEFAULT_EXPIRY, currentUser.id)}*/}
      {/*    />*/}
      {/*    :*/}
      {/*    <FeedPostAttachment*/}
      {/*      key={file.handle}*/}
      {/*      name={file.filename}*/}
      {/*      mimetype={file.mimetype}*/}
      {/*      url={addSecurity(file.url, FILESTACK_DEFAULT_EXPIRY, currentUser.id)}*/}
      {/*      totalNumberOfAttachments={post.filestack?.length}*/}
      {/*    />*/}
      {/*  )}*/}
      {/*</div>*/}
      {hasAnyInteractions && (
        <>
          <div className="posting-feed-post-separator"></div>
          <div className="posting-feed-post-stats">
            <div className="posting-feed-post__topic-name">{post?.communityTopic?.name}</div>
            <div className="posting-feed-post-stats__right-column">
              {canInteractWithLikes && (
                <div>
                  <span className="like-icon">
                    <i className="icon-thumbs-up"/>
                  </span>
                    {formatVotesNumber(post.votesCount)}
                </div>
              )}
              {canInteractWithComments && (
                <div className='comments-and-shares'>
                  <button className={`${post.commentsCount > 0 ? 'comments-and-shares__active' : ''}`} onClick={toggleCommentList}>
                    {`${post.commentsCount} ${translatr("web.common.main", "CommentsLowerCase")}`}
                  </button>
                  {/*<span>{`8 ${translatr("web.group.main", "Shares")}`}</span>*/}
                </div>
              )}
            </div>
          </div>
          <div className="posting-feed-post-separator"></div>
          <div className="posting-feed-post-actions">
            {canInteractWithLikes && (
              <Button data-testid={`like-post-${post.id}`} color={post.isUpvoted ? "primary" : "secondary"}
                      variant="borderless" size="medium" onClick={likePost}>
              {post.isUpvoted ? <i className="icon-thumbs-up-fill"/> : <i className="icon-thumbs-up"/>}
                <span>{translatr("web.common.main", post.isUpvoted ? "Liked" : "Like")}</span>
              </Button>
            )}
            {canInteractWithComments && (
              <Button data-testid={`add-comment-${post.id}`} color="secondary" variant="borderless" size="medium"
                      onClick={onComment}>
                <i className="icon-comment"/>
                <span>{translatr("web.common.main", "Comment")}</span>
              </Button>
            )}
            {/*<Button data-testid={`share-post-${post.id}`} color="secondary" variant="borderless" size="medium" onClick={sharePost}>*/}
            {/*  <i className="icon-share1" />*/}
            {/*  <span>{translatr("web.common.main", "Share")}</span>*/}
            {/*</Button>*/}
            {/*<Button color={post.isBookmarked ? "primary" : "secondary"} variant="borderless" size="medium" onClick={bookmarkPost}>*/}
            {/*  {post.isBookmarked ? <i className="icon-bookmark-fill" /> : <i className="icon-bookmark" />}*/}
            {/*  <span>{translatr("web.common.main", post.isBookmarked ? "Bookmarked" : "Bookmark")}</span>*/}
            {/*</Button>*/}
          </div>
        </>
      )}
      <PostingFeedCommentsContainer
        groupId={groupDetails.id}
        postId={post.id}
        commentInputVisible={commentInputVisible}
        setCommentInputVisible={setCommentInputVisible}
        commentListVisible={commentListVisible}
        setCommentListVisible={setCommentListVisible}
        onCommentsTotalChange={onCommentsTotalChange}
      />
      {editPostModalOpen &&
        <PostCreateEditModal
          postId={post.id}
          groupId={groupDetails.id}
          communityType={groupDetails.communityType}
          onClose={handleCloseEditPost}
        />
      }
    </div>
  );
};
export default GroupPostingFeedPost;
