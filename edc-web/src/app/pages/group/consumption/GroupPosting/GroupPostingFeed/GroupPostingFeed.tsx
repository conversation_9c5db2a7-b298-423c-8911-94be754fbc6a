import React, { memo, useContext, useEffect, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { dislikeComment, likeComment } from "edc-web-sdk/requests/cards";
import { useInfiniteScroll } from "centralized-design-system/src/Utils/hooks";
import { Translatr, translatr } from "centralized-design-system/src/Translatr";
import Modal from "centralized-design-system/src/Modals";
import InaccessibleContentMsg from "@components/common/InaccessibleContentMsg";
import NoContentAvailable from "@components/common/NoContentAvailable";
import Spinner from "@components/common/spinner";
import ReportModal from "@components/modals/ReportModal";
import ConfirmationDialog from "@components/modals/ConfirmationModalV2";
import isLmsProviderEnabledInstance from "@utils/isLmsProviderEnabledInstance";
import GroupContentCard from "../../../consumption/GroupConsumptionContent/GroupContentCard";
import { Post, PostId } from "../../../components/types";
import { POST_MODEL } from "../../../components/constants";
import FeaturedContainer from "../../GroupConsumptionContent/FeaturedContainer";
import LeaderBoardUserTileContainer from "../../GroupConsumptionContent/LeaderBoardUserTileContainer";
import { GroupConsumptionContext } from "../../GroupConsumptionProvider";
import GroupPostingFeedPost from "../GroupPostingFeedPost/GroupPostingFeedPost";
import GroupPostingFeedPoster from "../GroupPostingFeedPoster/GroupPostingFeedPoster";
import { usePosts } from "../api/usePosts";
import PostApprovalInfo from "./PostApprovalInfo/PostApprovalInfo";
import { useFocusedPost } from "./useFocusedPost";
import "./GroupPostingFeed.scss";

const POSTS_LIMIT_PER_FETCH = 10;

interface GroupPostingFeedProps {
}
const GroupPostingFeed = ({}: GroupPostingFeedProps) => {
  const {
    dispatch,
    groupTabs,
    groupDetails,
    isGroupEditable,
    showLeaderBoardToMember,
  } = useContext(GroupConsumptionContext);

  const [postToReport, setPostToReport] = useState<PostId>(null);
  const [postToRemove, setPostToRemove] = useState<Post>();
  const [postApprovalInfoVisible, setPostApprovalInfoVisible] = useState(false);

  const {
    pristine,
    loading,
    posts,
    updatePost,
    removePost,
    addNewPost,
    hasNextPage,
    total,
    loadMore,
    loadPost
  } = usePosts({ groupId: groupDetails.id, limit: POSTS_LIMIT_PER_FETCH, offset: 0 });
  const { focusedPost, focusedPostRef } = useFocusedPost({ posts, loadPost });

  const [sentryRef] = useInfiniteScroll({
    loading,
    hasNextPage,
    onLoadMore: loadMore,
    disabled: false,
    rootMargin: "50px 0px 200px 0px"
  });

  const displayPosts = focusedPost ? posts?.filter(post => Number(post.id) !== Number(focusedPost.id)) : posts;

  const isGroupMember = groupDetails?.userBelongsToGroup
  const feedInaccessibleToUser = !pristine && isLmsProviderEnabledInstance() && !posts?.length && total > 0;
  const noFeedAvailable = !pristine && !posts?.length && (!isLmsProviderEnabledInstance() || total === 0);
  const moreFeedToDisplay = posts?.length < total;

  const inaccessibleContentMsg = total > posts?.length ?
    translatr("web.common.main", "YouMayNotHaveAccessToContentClickOnShowMore")
    : translatr("web.group.main", "YouDoNotHaveAccessToContentInThisSection");

  const getPost = (id: PostId) => posts.find(p => p.id === id);

  const onPostCreated = (post: Post) => {
    if (groupDetails.isContentApproval && post.needApprove) {
      setPostApprovalInfoVisible(true);
      return;
    }
    addNewPost(post);
  };

  const onPostRemove = () => {
    removePost(postToRemove.id, postToRemove.model, groupDetails.id)
      .then(() => {
        setPostToRemove(null);
      })
  }

  const likePost = debounce((id: PostId) => {
    const post = getPost(id);
    if (!post) {
      return;
    }

    const { isUpvoted: hasBeenUpvoted, votesCount: previousVotesCount } = post;
    updatePost(id, {
      isUpvoted: !hasBeenUpvoted,
      votesCount: hasBeenUpvoted ? previousVotesCount - 1 : previousVotesCount + 1,
    });
    (hasBeenUpvoted ? dislikeComment : likeComment)(post.id, 'CommunityPost')
      .then(() => {})
      .catch(() => {//only if action has failed we revert current state
        updatePost(id, {
          isUpvoted: hasBeenUpvoted,
          votesCount: previousVotesCount,
        });
      });
  }, 250);

  const bookmarkPost = debounce((id: PostId) => {
    const post = getPost(id);
    if (!post) {
      return;
    }

    // const { isBookmarked: hasBeenBookmarked } = post;
    // updatePost(id, {
    //   isBookmarked: !hasBeenBookmarked,
    // });
    // (post.isBookmarked ? unBookmark : bookmark)(id, "")
    //   .then(() => {})
    //   .catch(() => {
    //     updatePost(id, {
    //       isBookmarked: hasBeenBookmarked,
    //     });
    //   });
  }, 250);

  const sharePost = (id: PostId) => {
  }

  const reportPost = (id: PostId) => {
    setPostToReport(id);
  }

  const updatePostCommentsTotal = (id: PostId, commentsCount: number) => {
    updatePost(id, {
      commentsCount
    });
  }

  const renderPost = (post: Post) =>
    post.model === POST_MODEL.COMMUNITY_POST ? (
      <GroupPostingFeedPost
        key={`post-${post.id}`}
        post={post}
        removePost={() => setPostToRemove(post)}
        likePost={() => likePost(post.id)}
        bookmarkPost={() => bookmarkPost(post.id)}
        sharePost={() => sharePost(post.id)}
        reportPost={() => reportPost(post.id)}
        onPostEdit={(data) => updatePost(post.id, data)}
        onCommentsTotalChange={(commentsCount) => updatePostCommentsTotal(post.id, commentsCount)}
      />
    ) : (
      <div className="group-posting-feed-card-wrapper">
        <GroupContentCard
          key={`post-${post.id}`}
          cardData={post}
          removeCard={() => removePost(post.id, post.model, groupDetails.id)}
          isShared
          blankAlt
        />
      </div>
    );

  return (
    <div className="justflex content-section">
      {groupTabs.length > 0 && (
        <>
          <aside className="left-section">
            <FeaturedContainer/>
            {showLeaderBoardToMember && <LeaderBoardUserTileContainer/>}
          </aside>
          <section className="right-section justflex flex-1 flex-column">
            {postApprovalInfoVisible && <PostApprovalInfo isCommunity={groupDetails.communityType !== null}/>}
            {isGroupMember && <GroupPostingFeedPoster onPostCreated={onPostCreated} />}
            <div className="group-posting-feed">
              <div ref={focusedPostRef}>
                {focusedPost && (
                  <>
                    {renderPost(focusedPost)}
                    <hr className="center-hr-text" />
                  </>
                )}
              </div>
              {displayPosts?.map(renderPost)}
              {feedInaccessibleToUser && (
                <InaccessibleContentMsg
                  msg={inaccessibleContentMsg}
                  additionalClasses="font-size-xxxl"
                />
              )}
              {noFeedAvailable && (
                <NoContentAvailable
                  icon="icon-file-alt"
                  redirectPath={`/teams/${groupDetails.slug}/edit/content-layout`}
                  //@ts-ignore
                  dispatch={dispatch}
                  isEditable={isGroupEditable}
                  type={translatr("web.common.main", "GroupSmallCase")}
                  description={translatr("web.group.main", "NoContentAvailableDefaultDescriptionGroup")}
                  editModalButtonLabel={translatr("web.group.main", "NoContentAvailableEditButtonLabelGroup")}
                  message={translatr("web.group.main", "NoContentGroupLabel")}
                />
              )}
              {(pristine || moreFeedToDisplay) && (
                <div className="make-center width-100" ref={sentryRef}>
                  <Spinner />
                </div>
              )}
            </div>
          </section>
          {postToReport && (
            <Translatr apps={["web.smartcard.reportmodal"]}>
              <ReportModal
                post={posts?.find(post => post.id === postToReport)}
                closeHandler={e => {
                  e?.stopPropagation();
                  setPostToReport(null);
                }}
                dispatch={dispatch}
              />
            </Translatr>
          )}
          {postToRemove && (
            <Modal size='small'>
              <ConfirmationDialog
                title={translatr('web.group.main', 'DeletePostConfirmationModalTitle')}
                message={translatr('web.group.main', 'DeletePostConfirmationModalDescription')}
                closeModal={() => setPostToRemove(null)}
                cancelClick={() => setPostToRemove(null)}
                callback={onPostRemove}
                confirmBtnTitle={translatr('web.common.main', 'Delete')}
                isNegativeValue
                noNeedClose
                isTranslated
              />
            </Modal>
          )}
        </>
      )}
    </div>
  );
}

export default GroupPostingFeed;
