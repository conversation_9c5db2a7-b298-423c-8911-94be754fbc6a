@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.theme-plare {
  #group-consumption {
    .group-details .info-container .right-section__button-container {
      white-space: nowrap;
      max-height: rem-calc(40);
    }
    .group-btn-dropdown .ed-dropdown .dropdown-content {
      box-shadow: var(--ed-shadow-base);
    }
  }
}

#group-consumption {
  width: rem-calc(1200);
  margin: auto;

  .group-details {
    &.mt-16 {
      margin-top: var(--ed-spacing-base);
    }

    .group-banner {
      height: rem-calc(174);

      .group-banner-image {
        object-fit: cover;
      }

      .default-group-banner-image {
        background: var(--ed-white);
      }
    }

    box-shadow: var(--ed-shadow-base);
    border-radius: var(--ed-border-radius-lg);
    background-size: cover;
    background-repeat: no-repeat;

    .group__actions {
      min-width: rem-calc(38);
      display: flex;
      height: rem-calc(38);
      top: rem-calc(16);
      right: rem-calc(16);
      background-color: var(--ed-white);
      color: var(--ed-text-color-supporting);
      padding: 0;
      margin-left: auto;

      i {
        font-size: rem-calc(22);
      }
    }

    .group-details__image {
      height: rem-calc(120);
      background: transparent;
    }

    .info-container {
      padding: var(--ed-spacing-base);
      background-color: var(--ed-white);

      .group__user--header {
        margin-bottom: var(--ed-spacing-xs);
      }

      .info-container--title {
        word-break: break-word;
        color: var(--ed-text-color-primary);
        font-size: var(--ed-font-size-2xl);

        i {
          margin-right: var(--ed-spacing-2xs);
          color: var(--ed-text-color-supporting);
        }
      }

      .info-container-users {
        display: flex;
        margin-top: 1rem;
        gap: var(--ed-spacing-base);

        .info-container-users__left {
          display: flex;
          gap: var(--ed-spacing-base);
        }
      }

      .info-container--member {
        margin-left: auto;
      }

      .right-section__button-container {
        max-height: rem-calc(50);
        margin-left: auto;
      }
    }

    .common-menu-styles {
      .border {
        border: var(--ed-border-size-sm) solid var(--ed-border-color);
      }
    }

    .positive-action {
      color: var(--ed-primary-base);
    }

    .negative-action {
      color: var(--ed-negative-2);
      text-decoration: underline;
    }

    .ed-btn {
      &.join-grp-btn {
        width: rem-calc(140);
      }

      &.joined-btn {
        text-align: end;
        border: none;
      }
    }
  }

  .content-section {
    .left-section {
      max-width: rem-calc(388);
      margin-right: var(--ed-spacing-base);

      .featured__container {
        padding: var(--ed-spacing-2xs);
        background-color: var(--ed-white);
        border-radius: var(--ed-border-radius-lg);
        box-shadow: var(--ed-shadow-base);
        width: 24.25rem;

        &--heading {
          padding: var(--ed-spacing-2xs);
          font-size: var(--ed-font-size-base);

          h2 {
            .icon-star-fill {
              margin: 0 var(--ed-spacing-2xs) 0 0;
              color: var(--ed-warning-2);
            }
          }

          a {
            color: var(--ed-text-color-supporting);
            text-decoration: underline;

            &:hover {
              color: var(--ed-text-color-success);
            }
          }
        }

        .featured__container--card {
          margin-bottom: var(--ed-spacing-2xs);
        }

        .featured__container--message {
          i {
            font-size: rem-calc(47);
            color: var(--ed-primary-base);
          }

          p {
            color: var(--ed-text-color-supporting);
          }
        }
      }

      .leaderboard__user__tile--conatainer {
        padding: var(--ed-spacing-base);
        background-color: var(--ed-white);
        border-radius: var(--ed-border-radius-lg);
        box-shadow: var(--ed-shadow-base);
      }
    }

    .right-section {
      width: 40%;

      .card-std-bigcard.ed-ui,
      .big-view-loader {
        width: 100%;
      }
    }

    .content-text {
      max-width: 13.5rem;
      border-radius: var(--ed-border-radius-lg);
      padding: var(--ed-spacing-base);
      width: 100%;
      text-align: left;

      &:hover {
        box-shadow: var(--ed-shadow-base);
      }
    }
  }

  .link {
    color: var(--ed-text-color-supporting);
    text-decoration: underline;

    &:hover {
      color: var(--ed-primary-base);
    }
  }

  .content__container {
    .card__layout {
      min-width: 16.25rem;
      min-height: 22rem;
    }
  }

  .group-btn-dropdown {
    .ed-dropdown {
      display: none;
    }
  }

  .show-btn {
    display: block;
  }

  .group-btn-dropdown {
    right: 1.125rem;
    margin-top: var(--ed-spacing-base);

    .ed-dropdown {
      padding: var(--ed-spacing-2xs) rem-calc(16);
      background: var(--ed-white);
      border: var(--ed-border-size-sm) solid var(--ed-border-color);
      border-radius: var(--ed-border-radius-lg);
      margin-left: rem-calc(3);
      display: block;

      .dropdown-content {
        right: 1.5rem;
        left: auto;
        min-width: rem-calc(192);
        top: rem-calc(40);

        li {
          button {
            padding: var(--ed-spacing-2xs) rem-calc(16);

            &:hover {
              background-color: var(--ed-input-hover-bg-color);
            }
          }
        }
      }

      .icon-ellipsis-v-alt,
      .icon-settings {
        font-size: var(--ed-font-size-lg);
        color: var(--ed-text-color-supporting);
      }
    }
  }

  .group-setting-dropdown {
    margin-left: auto;

    .ed-dropdown {
      padding: rem-calc(6) rem-calc(6);

      .dropdown-content {
        li {
          &.positive-action {
            &:hover {
              color: var(--ed-state-active-color);
            }
          }

          &.negative-action {
            &:hover {
              color: var(--ed-negative-2);
            }
          }
        }
      }
    }
  }

  .tile-view-loader {
    margin-right: var(--ed-spacing-base);

    &:last-child {
      margin-right: 0;
    }
  }
}

.ed-ui:not(.html-container) {
  .share-group-with {
    p {
      font-size: var(--ed-font-size-supporting);
    }
  }

  .user__tile--user__points {
    p {
      font-weight: var(--ed-font-weight-semibold);
    }
  }

  .user__tile-current-user {
    .user__tile--user__info {
      p {
        font-weight: var(--ed-font-weight-semibold);
      }
    }
  }
}

#group-consumption {
  &.ed-ui {
    .tab-bar.block {
      overflow-x: auto;
      overflow-y: hidden;
    }
  }
}

@media screen and (max-width: 1220px) {
  .ed-ui#group-consumption {
    width: 98%;

    .content-section .left-section {
      .featured__container {
        width: rem-calc(388);
      }
    }
  }
}

@media screen and (max-width: 992px) {
  .ed-ui#group-consumption {
    .content-section {
      .content-text {
        width: auto;
      }

      .right-section {
        .card-std-bigcard .picasso-card-metadata-wrapper .external-metadata-wrapper {
          max-width: 22rem;
        }
      }
    }
  }
}

@media screen and (max-width: 991px) and (min-width: 843px) {
  .ed-ui#group-consumption {
    .content-section .left-section {
      .featured__container,
      .leaderboard__user__tile--conatainer {
        width: rem-calc(388);
      }
    }
  }
}

@media screen and (max-width: 868px) {
  .ed-ui#group-consumption {
    .share-group-icons {
      margin-bottom: var(--ed-spacing-2xs);
    }

    .content-section {
      .content-text {
        flex-direction: column;
      }

      .right-section {
        .card-std-bigcard .picasso-card-metadata-wrapper .external-metadata-wrapper {
          max-width: 18rem;
        }
      }
    }
  }
}

@media screen and (max-width: 842px) and (min-width: 800px) {
  .ed-ui#group-consumption {
    .content-section {
      .left-section {
        .featured__container,
        .leaderboard__user__tile--conatainer {
          width: rem-calc(360);
        }
      }

      .right-section {
        width: calc(100% - 23.5rem);
      }
    }
  }
}

@media screen and (max-width: 799px) and (min-width: 700px) {
  .ed-ui#group-consumption {
    .content-section {
      .left-section {
        .featured__container,
        .leaderboard__user__tile--conatainer {
          width: rem-calc(300);
        }
      }

      .right-section {
        width: calc(100% - 19.75rem);

        .card-std-bigcard .picasso-card-metadata-wrapper .external-metadata-wrapper {
          max-width: 15rem;
        }
      }
    }
  }
}

@media screen and (max-width: 699px) and (min-width: 576px) {
  .ed-ui#group-consumption {
    .content-section {
      .left-section {
        margin-right: var(--ed-spacing-xs);

        .featured__container,
        .leaderboard__user__tile--conatainer {
          width: rem-calc(260);
        }
      }

      .right-section {
        width: calc(100% - 16.875rem);

        .card-std-bigcard .picasso-card-metadata-wrapper .external-metadata-wrapper {
          max-width: 10rem;
        }
      }
    }

    .user__tile--avatar {
      margin: 0 !important;
    }

    .user__tile {
      align-items: flex-start;
    }

    .user__tile-name-points {
      display: block;
    }

    .user__tile--user__points {
      text-align: left;
      margin-left: var(--ed-spacing-2xs);
      margin-top: var(--ed-spacing-2xs);
    }

    .user__tile-leaderboard {
      display: block;
      margin-bottom: var(--ed-spacing-2xs);
    }
  }
}

@media screen and (max-width: 600px) {
  .ed-ui#group-consumption {
    .content-section {
      .content-text {
        padding: var(--ed-spacing-2xs);
      }
    }

    .group-setting-dropdown {
      .ed-dropdown {
        margin-right: 0;
      }
    }

    .show-btn {
      display: none;
    }

    .group-info {
      display: block;
    }

    .group-info-header {
      display: block;

      .ed-btn {
        margin-left: 0;
        margin-right: var(--ed-spacing-xs);
      }

      .accept-decline-btn {
        margin-left: 0;
      }
    }

    .group-details {
      .info-container {
        .right-section__button-container {
          margin-top: rem-calc(15);
        }
      }
    }
  }
}

@media screen and (max-width: 575px) {
  .ed-ui#group-consumption {
    .content-section .left-section {
      display: none;
    }

    .info-container-users {
      flex-direction: column;

      .info-container-users__left {
        flex-direction: column;
      }

      .info-container--member {
        margin-left: unset;
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .ed-ui#group-consumption {
    .group-btn-dropdown {
      .ed-dropdown {
        .dropdown-content {
          top: rem-calc(54);
          right: rem-calc(56);
        }
      }
    }
  }
}

@media screen and (max-width: 425px) {
  .ed-ui#group-consumption {
    .content-section {
      .right-section {
        .card-std-bigcard .picasso-card-metadata-wrapper .external-metadata-wrapper {
          max-width: 14rem;
        }
      }
    }
  }
}

@media screen and (max-width: 360px) {
  .ed-ui#group-consumption {
    .content-section {
      .right-section {
        .ed-ui {
          &.card-std-bigcard {
            .author-std-details {
              max-width: rem-calc(200);
            }
          }
        }
      }
    }
  }
}

[dir='rtl'] {
  #group-consumption .group-btn-dropdown .ed-dropdown {
    margin-left: 0.1875rem;
  }
}
