import React, { useContext, useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import FeaturedContainer from './FeaturedContainer';
import { GroupConsumptionContext } from '../GroupConsumptionProvider';
import GroupContentCard from './GroupContentCard';
import NoContentAvailable from '@components/common/NoContentAvailable';
import { getTeamCards } from 'edc-web-sdk/requests/groups.v2';
import Spinner from '@components/common/spinner';
import LeaderBoardUserTileContainer from './LeaderBoardUserTileContainer';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Permissions } from '../../../../../app/utils/checkPermissions';
import { isCreateSmartCardButtonEnabled } from '@utils/isCreateSmartCardButtonEnabled';
import { showSmartCardLoaderByView } from '@utils/skeletonLoaderUtils';
import { GROUP_TO_SHARE_CONTENT } from '../../../../../app/constants/localStorageConstants';
import getAccessibleContent from '@utils/fetchAccessibleCards';
import { adjustOffsetInArgs } from '@utils/fetchAccessibleCards/helpers';
import ShowMoreBtn from '@components/common/ShowMore';
import isLmsProviderEnabledInstance from '@utils/isLmsProviderEnabledInstance';
import InaccessibleContentMsg from '@components/common/InaccessibleContentMsg';

const limit = 12;

const GroupConsumptionContent = () => {
  const navigate = useNavigate();

  const [isInitialFetchInProgress, setIsInitialFetchInProgress] = useState(true);
  const [isInProgress, setIsInProgress] = useState(false);
  const [sharedContent, setSharedContent] = useState([]);
  const [totalSharedContent, setTotalSharedContent] = useState(0);
  const [offset, setOffset] = useState(0);

  const {
    dispatch,
    groupTabs,
    isGroupEditable,
    groupDetails,
    showLeaderBoardToMember,
    filterByLanguage
  } = useContext(GroupConsumptionContext);

  const hasMoreCardsToFetch = totalSharedContent > offset;

  const isOrgAdmin = Permissions.has('ADMIN_ONLY');

  const isAdmin = groupDetails?.isTeamAdmin || groupDetails?.isTeamSubAdmin || isOrgAdmin;
  const canShareContent =
    isAdmin || (Permissions.has('MANAGE_CARD') && !groupDetails?.onlyAdminCanPost);
  const hasSmartCardPermissions =
    isCreateSmartCardButtonEnabled() ||
    Permissions.has('ADD_TO_PATHWAY') ||
    Permissions.has('CREATE_JOURNEY');
  const isGroupShareVisible =
    canShareContent && groupDetails?.userBelongsToGroup && hasSmartCardPermissions;

  useEffect(() => {
    getSharedContent();
  }, []);

  const removeCardHandler = cardId => {
    let updatedCards = sharedContent.filter(card => cardId !== card.id);
    setSharedContent(updatedCards);
  };
  const getSharedContent = useCallback(async () => {
    setIsInProgress(true);
    const payload = {
      limit,
      offset,
      type: 'shared',
      filter_by_language: filterByLanguage,
      show_who_was_sharing: true
    };
    try {
      // Below regex checks if url has exact '/teams/slug' indicating its a home tab
      const hasOnlyTeamsFollowedBySlugInUrl = /^\/teams\/[^/]+$/;
      const isMounted = () => hasOnlyTeamsFollowedBySlugInUrl.test(window.location.pathname);

      const response = await getAccessibleContent({
        funcToCall: getTeamCards,
        updateParentOffsetAfterOperation: latestOffset => setOffset && setOffset(latestOffset),
        extractOffsetFromArgs: (...args) => args[1].offset,
        numberOfRequiredContent: limit,
        isMounted,
        adjustOffsetInArgs
      })(groupDetails.id, payload);

      if (!isMounted()) {
        return;
      }

      setSharedContent(prevCards => [...prevCards, ...response.cards]);
      setTotalSharedContent(response.total);
      setOffset(prevOffset => prevOffset + limit);
      setIsInitialFetchInProgress(false);
      setIsInProgress(false);
    } catch (error) {
      console.error(
        `Error in GroupConsumptionContent.getSharedContent.getTeamCards function ${error}`
      );
    }
  }, [offset]);

  const updateContent = sharedCard => {
    const updatedCards = [...sharedContent];
    updatedCards.unshift(sharedCard);
    setSharedContent(updatedCards);
    setOffset(prevOffset => prevOffset + 1);
  };

  const openSmartCard = e => {
    e.stopPropagation();
    window.dispatchEvent(
      new CustomEvent('openSmartBiteModal', {
        detail: {
          open: true,
          groupToShareContent: groupDetails,
          updateSharedContent: updateContent
        }
      })
    );
  };

  const shareContent = contentType => {
    localStorage.setItem(
      GROUP_TO_SHARE_CONTENT,
      JSON.stringify({
        id: groupDetails.id,
        name: groupDetails.name,
        slug: groupDetails.slug
      })
    );
    contentType === 'pathway' ? navigate('/pathways/new') : navigate('/journeys/new');
  };

  const getInAccessibleContentMsg = () => {
    return hasMoreCardsToFetch
      ? translatr('web.common.main', 'YouMayNotHaveAccessToContentClickOnShowMore')
      : translatr('web.group.main', 'YouDoNotHaveAccessToContentInThisSection');
  };

  const cardsPresentButInAccessibleToUser =
    isLmsProviderEnabledInstance() && sharedContent.length === 0 && totalSharedContent > 0;

  return (
    <div className="justflex content-section">
      {groupTabs.length > 0 && (
        <>
          <aside className="left-section">
            <FeaturedContainer />
            {showLeaderBoardToMember && <LeaderBoardUserTileContainer />}
          </aside>
          <section className="right-section justflex flex-1 flex-column">
            {isGroupShareVisible && (
              <div className="block width-100 mb-16 share-group-with supporting-text">
                <p>{translatr('web.group.main', 'WhatDoYouWishToShareWithThisGroup')}</p>
                <div className="mt-16 flex-space-between m-margin-left m-margin-right">
                  {isCreateSmartCardButtonEnabled() && (
                    <button
                      className="content-text cursor-pointer justflex align-items-center"
                      onClick={e => openSmartCard(e)}
                    >
                      <span className="icon-smartcard s-margin-right share-group-icons"></span>
                      {translatr('web.group.main', 'Smartcard')}
                    </button>
                  )}
                  {Permissions.has('ADD_TO_PATHWAY') && (
                    <button
                      className="content-text cursor-pointer justflex align-items-center"
                      onClick={() => shareContent('pathway')}
                    >
                      <span className="icon-pathways s-margin-right share-group-icons"></span>
                      {translatr('web.group.main', 'Pathway')}
                    </button>
                  )}
                  {Permissions.has('CREATE_JOURNEY') && (
                    <button
                      className="content-text cursor-pointer justflex align-items-center"
                      onClick={() => shareContent('journey')}
                    >
                      <span className="icon-route s-margin-right share-group-icons"></span>
                      {translatr('web.group.main', 'Journey')}
                    </button>
                  )}
                </div>
              </div>
            )}
            {isInitialFetchInProgress ? (
              <>{showSmartCardLoaderByView(1, 'bigcard')}</>
            ) : (
              <>
                {sharedContent.length > 0 ? (
                  sharedContent.map(card => (
                    <GroupContentCard
                      key={card.id}
                      cardData={card}
                      removeCard={removeCardHandler}
                      isShared
                      blankAlt={true}
                    />
                  ))
                ) : cardsPresentButInAccessibleToUser ? (
                  <InaccessibleContentMsg
                    msg={getInAccessibleContentMsg()}
                    additionalClasses="font-size-xxxl"
                  />
                ) : (
                  <NoContentAvailable
                    icon="icon-file-alt"
                    redirectPath={`/teams/${groupDetails.slug}/edit/content-layout`}
                    dispatch={dispatch}
                    isEditable={isGroupEditable}
                    type={translatr('web.common.main', 'GroupSmallCase')}
                    description={translatr(
                      'web.group.main',
                      'NoContentAvailableDefaultDescriptionGroup'
                    )}
                    editModalButtonLabel={translatr(
                      'web.group.main',
                      'NoContentAvailableEditButtonLabelGroup'
                    )}
                    message={translatr('web.group.main', 'NoContentGroupLabel')}
                  />
                )}
                {!isInProgress && hasMoreCardsToFetch && (
                  <ShowMoreBtn loadMoreData={getSharedContent} classNames="m-margin-top" />
                )}
                {isInProgress && (
                  <div className="make-center width-100">
                    <Spinner />
                  </div>
                )}
              </>
            )}
          </section>
        </>
      )}
    </div>
  );
};

export default GroupConsumptionContent;
