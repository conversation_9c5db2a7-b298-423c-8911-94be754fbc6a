import React from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import './GroupConsumption.scss';
import '../../../../app/styles/common.picasso.scss';
import GroupConsumptionProvider, { GroupConsumptionContext } from './GroupConsumptionProvider';
import GroupConsumptionHeader from './GroupConsumptionHeader';
import GroupConsumptionTab from './GroupConsumptionTab';
import LanguageBanner from '@components/common/LanguageBanner';
import BackArrow from '@components/backarrow';
import UserManagementContainer from '../user_management/UserManagementContainer';
import InviteMembers from '../user_management/InviteMembers';
import TeamActivity from '../../home/<USER>/TeamActivity';
import DeclineGroupInviteModal from './DeclineGroupInviteModal';
import { Permissions } from '../../../../app/utils/checkPermissions';
import { translatr } from 'centralized-design-system/src/Translatr';
import { FIRST_LETTER_OF_WORD_REGEX } from '../../../../app/constants/regexConstants';
import PropTypes from 'prop-types';

const GroupConsumption = props => {
  const navigate = useNavigate();
  const location = useLocation();
  const { slug, tab } = useParams();

  const checkStringExist = text => {
    return tab?.includes(text);
  };

  const isUserManagementPage =
    checkStringExist('members') || checkStringExist('pending') || checkStringExist('bulk-removal');
  const isInvitePage = checkStringExist('group-invite');
  const isGroupAssignments =
    location.search.includes('deep_link_type') && location.search.includes('deep_link_id');

  const getUserManagementContent = groupDetails => {
    const { isTeamAdmin, isTeamSubAdmin, slug: slugId } = groupDetails;

    const isOrgAdmin = Permissions.has('ADMIN_ONLY');

    if (isOrgAdmin || isTeamAdmin || isTeamSubAdmin) {
      return <UserManagementContainer prefix={`/teams/${slug}`} />;
    } else {
      navigate(`/teams/${slugId}`);
    }
  };

  return (
    <GroupConsumptionProvider key={slug}>
      <main id="group-consumption" className="ed-ui">
        <GroupConsumptionContext.Consumer>
          {({ groupDetails, reloadTheContent, isTeamActivityVisible, filterByLanguage }) => {
            if (groupDetails?.name) {
              document.title = `${unescape(groupDetails.name)?.replace(
                FIRST_LETTER_OF_WORD_REGEX,
                match => match.toUpperCase()
              )} Team - ${props.orgName}`;
            }
            return (
              <>
                {!isGroupAssignments ? (
                  <>
                    <BackArrow label={translatr('web.team.main', 'Back')} />
                    {isInvitePage ? (
                      <InviteMembers />
                    ) : isUserManagementPage ? (
                      getUserManagementContent(groupDetails)
                    ) : (
                      <>
                        {isTeamActivityVisible &&
                          groupDetails?.enableActivityFeed &&
                          !groupDetails?.enablePostingFeed && (
                            <TeamActivity defaultTeamId={groupDetails.id} />
                          )}
                        <GroupConsumptionHeader />
                        <GroupConsumptionTab data={props} />
                        <LanguageBanner
                          languageFilterHandler={reloadTheContent}
                          filterByLanguage={filterByLanguage}
                        />
                      </>
                    )}
                  </>
                ) : (
                  navigate(`/teams/${groupDetails.id}/assignments`)
                )}
              </>
            );
          }}
        </GroupConsumptionContext.Consumer>
      </main>
      <DeclineGroupInviteModal />
    </GroupConsumptionProvider>
  );
};

const mapStateToProps = state => ({
  orgName: state.team.get('name')
});

GroupConsumption.propTypes = {
  orgName: PropTypes.string.isRequired
};

export default connect(mapStateToProps)(GroupConsumption);
