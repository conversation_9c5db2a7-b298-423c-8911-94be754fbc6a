import React, { useContext, useEffect } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { getUsers, UsersResponse } from '@pages/group/common';
import GroupUsersList from './GroupUsersList';
import { GroupConsumptionContext } from '../GroupConsumptionProvider';
import { Permissions } from '@utils/checkPermissions';
import { AssociatedUserListModalGetDataProps, UserType } from './types';

const initialState: UsersResponse = {
  users: [],
  total: 0
}

interface UserListPreviewProps {
  userIsPartOfGroup: boolean
}

const UserListPreview: React.FC<UserListPreviewProps> = ({ userIsPartOfGroup }) => {
  const {
    groupDetails: {
      isTeamAdmin,
      isTeamSubAdmin,
      isPrivate,
      slug
    },
    hideMembersGroupList,
    groupDetails
  } = useContext(GroupConsumptionContext);

  const [adminList, setAdminList] = React.useState<UsersResponse>(initialState)
  const [subAdminList, setSubAdminList] = React.useState<UsersResponse>(initialState)
  const [moderatorList, setModeratorList] = React.useState<UsersResponse>(initialState)
  const [userList, setUsersList] = React.useState<UsersResponse>(initialState)

  const loadUsers = (type: UserType, setter: React.Dispatch<React.SetStateAction<UsersResponse>>, limit = 12, offset = 0) => {
    return getUsers(groupDetails?.id, type, limit, offset)
      .then((response: UsersResponse) => setter(response))
      .catch(() => setter(initialState));
  }

  const loadMoreUsers = (userType: UserType, total: number, callbackProps: AssociatedUserListModalGetDataProps) => {
    const { setOffset, setIsLoading, setUserData, offset, limit } = callbackProps;
    setIsLoading(true);
    getUsers(groupDetails?.id, userType, limit, offset)
      .then((admins: UsersResponse) => {
        if(offset !== 0) {
          setUserData((prev: Array<any>) => [...prev, ...admins.users]);
        } else {
          setUserData(admins.users);
        }
        if(offset < total) {
          setOffset(offset + limit);
        }
      })
      .finally(() => {
        setIsLoading(false)
      });
  }

  useEffect(() => {
    loadUsers("admin", setAdminList);
    loadUsers("sub_admin", setSubAdminList);
    if (groupDetails.communityType !== null) {
      loadUsers("moderator", setModeratorList);
    }
    loadUsers(undefined, setUsersList, 10);
  }, [groupDetails?.id])

  const windowSizeObject = useWindowSize();
  const screenSize = windowSizeObject.width;
  const isMobileView = screenSize < 1000

  const isOrgAdmin = Permissions.has('ADMIN_ONLY');
  const isMemberListEnabled = hideMembersGroupList ? isTeamAdmin || isTeamSubAdmin : true;
  const shouldShowMembersPreview = isMemberListEnabled && (userIsPartOfGroup || !isPrivate) && userList?.users.length > 0;

  return (
    <div className="info-container-users">
      <div className="info-container-users__left">
        {adminList?.users?.length > 0 && (
          <div>
            <GroupUsersList
              userList={adminList.users}
              isOverlapped
              elementToShow={4}
              listLabel={translatr('web.group.main', 'GroupLeaders')}
              totalMembers={adminList.total}
              getNewData={(props: AssociatedUserListModalGetDataProps) =>
                loadMoreUsers("admin", adminList.total, props)}
            />
          </div>
        )}
        {subAdminList?.users?.length > 0 && (
          <div>
            <GroupUsersList
              userList={subAdminList.users}
              isOverlapped
              elementToShow={4}
              listLabel={translatr('web.group.main', 'GroupAdmins')}
              totalMembers={subAdminList.total}
              getNewData={(props: AssociatedUserListModalGetDataProps) =>
                loadMoreUsers("sub_admin", subAdminList.total, props)}
            />
          </div>
        )}
        {moderatorList?.users?.length > 0 && (
          <div>
            <GroupUsersList
              userList={moderatorList.users}
              isOverlapped
              elementToShow={4}
              listLabel={translatr('web.group.main', 'GroupModerators')}
              totalMembers={moderatorList.total}
              getNewData={(props: AssociatedUserListModalGetDataProps) =>
                loadMoreUsers("moderator", moderatorList.total, props)}
            />
          </div>
        )}
      </div>
      <div className="info-container--member">
        {shouldShowMembersPreview && (
            <GroupUsersList
              userList={userList?.users}
              isOverlapped={isMobileView}
              elementToShow={isMobileView ? 4 : 10}
              listLabel={translatr('web.group.main', 'GroupMembers')}
              redirectionUrl={`/teams/${slug}/members`}
              shouldRedirect={isOrgAdmin || isTeamAdmin || isTeamSubAdmin}
              totalMembers={userList.total}
              getNewData={(props: AssociatedUserListModalGetDataProps) => loadMoreUsers(undefined, userList.total, props)}
            />
          )}
      </div>
    </div>
  );
};

export default UserListPreview;
