import React, { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { object, bool, array } from 'prop-types';
import { Permissions } from '../../../../../app/utils/checkPermissions';
import { GroupConsumptionContext } from '../GroupConsumptionProvider';
import { translatr } from 'centralized-design-system/src/Translatr';
import capture from '../../../../../app/utils/datalayer';
import {
  joinToGroup,
  leaveFromGroup,
  updateGroup,
  acceptInviteToGroup
} from '../../../../../app/actions/groupsActionsV2';
import GroupSettings from './GroupSettings';
import LD from '../../../../../app/containers/LDStore';
import { openInviteV2UserModal } from '../../../../../app/actions/modalActions';
import { ADP_URL } from 'edc-web-sdk/requests/adp';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import Dropdown from 'centralized-design-system/src/Dropdown';
import { recordVisit } from 'edc-web-sdk/requests/analytics';
import classNames from 'classnames';
import Modal, {
  ModalContent,
  ModalFooter,
  ModalHeader
} from 'centralized-design-system/src/Modals';
import Loading from 'centralized-design-system/src/Loading';
import unescape from 'lodash/unescape';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import InfoPanel from 'centralized-design-system/src/InfoPanel/InfoPanel';
import UserListPreview from '@pages/group/consumption/GroupConsumptionHeader/UserListPreview';

const GroupConsumptionHeader = () => {
  const navigate = useNavigate();

  const {
    groupDetails,
    pendingUsersCount,
    dispatch,
    hideMembersGroupList,
    leaderboard,
    isAdpUser,
    isCurrentUserIsAdmin,
    declineGroupInvitationModalHandler,
    updateGroupDetails,
    showLeaderBoardToMember
  } = useContext(GroupConsumptionContext);

  const {
    name,
    isTeamAdmin,
    isTeamSubAdmin,
    isPrivate,
    slug,
    isPending,
    isMember,
    isTeamModerator,
    isDynamic,
    isMandatory,
    isEveryoneTeam,
    bannerimageAltText,
    bannerimageUrls = {},
    userBelongsToGroup
  } = groupDetails;

  const [openAdpModal, setOpenAdpModal] = useState(false);
  const [isUserIsPartOfGroup, setIsUserIsPartOfGroup] = useState(userBelongsToGroup);
  const [isButtonHover, updateButtonHoverState] = useState(false);
  const [isInProgress, setIsInProgress] = useState(false);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [isWidgetInfoVisible, setWidgetInfoVisible] = useState(true);

  const bulkUploadEnabled = LD.groupBulkUploadLD();
  const isMemberListEnabled = hideMembersGroupList ? isTeamAdmin || isTeamSubAdmin : true;

  const isGroupSubAdmin = isCurrentUserIsAdmin && isTeamSubAdmin;

  const title = groupDetails.name;

  const isImpersonator = ['user_dashboard', 'manager_dashboard'].includes(
    window?.__ED__?.proxyType
  );

  const checkConsentFromUrl = () => {
    const search = window.location.search;
    const params = new URLSearchParams(search);
    const consentManager = params.get('consentManager');
    return !!consentManager;
  };

  // To open adp invite modal
  useEffect(() => {
    const consentManager = checkConsentFromUrl();
    if (isAdpUser && consentManager) {
      openAdpInviteModal();
    }
  }, [isAdpUser]);

  useEffect(() => {
    // record group visited event
    if (groupDetails && groupDetails.id) {
      capture('Group Visited', groupDetails);
      recordVisit(groupDetails.id, 'Team');
    }
  }, []);

  const setButtonHoverState = () => {
    !(isInProgress || isMandatory) && updateButtonHoverState(hoverState => !hoverState);
  };

  const openLeaderboard = () => {
    navigate(`/leaderboard?group_id=${groupDetails.id}`);
  };

  const openAnalytics = () => {
    navigate(`/teams/${slug}/analytics`);
  };

  const openGroupEditModal = () => {
    navigate(`/teams/${slug}/edit`);
  };

  const stopEventPropagation = event => {
    event?.preventDefault();
    event?.stopPropagation();
  };

  const joinToGroupHandel = () => {
    if (isUserIsPartOfGroup) {
      setIsOpenModal(true);
    } else {
      if (!isInProgress) {
        setIsInProgress(true);
      }
      capture('Group Joined', groupDetails);
      dispatch(joinToGroup(groupDetails.id))
        .then(() => {
          setIsInProgress(false);
          setIsUserIsPartOfGroup(isInGroup => !isInGroup);
          updateGroupDetails();
        })
        .catch(error => {
          setIsInProgress(false);
          console.error(`Error in  joinToGroupHandel.joinToGroup: ${error}`);
        });
    }
  };

  const acceptInvitationGroup = () => {
    if (!isInProgress) {
      setIsInProgress(true);
    }
    capture('Group Invitation Accepted', groupDetails);
    dispatch(acceptInviteToGroup(groupDetails.id))
      .then(() => {
        setIsInProgress(false);
        setIsUserIsPartOfGroup(isInGroup => !isInGroup);
        updateGroupDetails();
      })
      .catch(error => {
        setIsInProgress(false);
        console.error(`Error in  joinToGroupHandel.joinToGroup: ${error}`);
      });
  };

  const declineBtnHandler = event => {
    stopEventPropagation(event);
    declineGroupInvitationModalHandler(groupDetails.id, groupDetails.name);
  };

  const leaveClick = () => {
    setIsInProgress(true);
    dispatch(leaveFromGroup(groupDetails.id))
      .then(response => {
        // if response is undefined that means it is not coming inside the catch block of leaveFromGroup function, so it is successfully returning the data
        if (response === undefined || response.ok) {
          if (isPrivate) updateGroup(groupDetails.id);
          setIsUserIsPartOfGroup(isInGroup => !isInGroup);
          updateGroupDetails();
        }
        setIsInProgress(false);
        setIsOpenModal(false);
      })
      .catch(error => {
        setIsOpenModal(false);
        setIsInProgress(false);
        console.error(`error at leaveGroup func in the GroupChannelCards ${error}`);
      });
  };

  const triggerInvite = () => {
    navigate({
      pathname: `/teams/${groupDetails.id}/group-invite`,
      state: {
        groupId: groupDetails.id,
        isGroupInvitePage: true
      }
    });
  };

  const openInviteModal = () => {
    dispatch(openInviteV2UserModal(isAdpUser));
  };

  const openAdpInviteModal = () => {
    if (isAdpUser) {
      const redirectUrl = `${ADP_URL}&successUri=${encodeURI(window.location)}?consentManager=true`;
      if (checkConsentFromUrl()) {
        //Removing consentManager from URL
        window.history.replaceState({}, '', `${location.pathname}`);
        openInviteModal();
        setOpenAdpModal(true);
      } else {
        if (openAdpModal) {
          openInviteModal();
        } else {
          location.href = redirectUrl;
        }
      }
    }
  };

  let buttonLabel = isUserIsPartOfGroup
    ? isButtonHover
      ? translatr('web.group.main', 'LeaveGroup')
      : translatr('web.group.main', 'Joined')
    : translatr('web.group.main', 'JoinGroup');

  const buttonClass = classNames('ed-btn', 'show-btn', {
    'join-grp-btn': !isUserIsPartOfGroup,
    'joined-btn': isUserIsPartOfGroup,
    'ed-btn-primary': !isUserIsPartOfGroup,
    'negative-action': isUserIsPartOfGroup && isButtonHover,
    'positive-action': isUserIsPartOfGroup && !isButtonHover
  });

  const positiveActionLabel = translatr('web.group.main', 'Joined');

  const handleModalClose = () => {
    setIsOpenModal(false);
  };

  const hasAccessToPendingMembers = !(isEveryoneTeam && isMember && isTeamModerator);

  const windowSizeObject = useWindowSize();
  const screenSize = windowSizeObject.width;

  const isTeamSubAdminAnalyticsEnabled =
    isTeamSubAdmin && Permissions['enabled'] && Permissions.has('MANAGE_GROUP_ANALYTICS');
  const isAnalyticsEnabled =
    LD.teamAnalyticsEnabled() && (isTeamAdmin || isTeamSubAdminAnalyticsEnabled);
  const isLeaderboardEnabled =
    Permissions['enabled'] !== undefined &&
    Permissions.has('GET_LEADERBOARD_INFORMATION') &&
    leaderboard;
  const couldUserJoinToTheGroup = !isUserIsPartOfGroup && !isPrivate && !isPending;

  const showButtonDropdown =
    screenSize <= 600 && (isAnalyticsEnabled || isLeaderboardEnabled || couldUserJoinToTheGroup);

  return (
    <>
      <div className="group-details overflow-hidden mb-8 relative mt-16">
        <div className="relative group-banner">
          <div className="justflex group-btn-dropdown position-absolute">
            <div className="group-setting-dropdown" data-testid="group-settings-dropdown">
              {hasAccessToPendingMembers && (
                <GroupSettings
                  pendingUsersCount={pendingUsersCount}
                  isMemberListEnabled={isMemberListEnabled}
                />
              )}
            </div>
            {showButtonDropdown && (
              <div className="group-setting-btn-dropdown">
                <Dropdown icon={<span className="icon-ellipsis-v-alt" />}>
                  <ul>
                    {isAnalyticsEnabled && (
                      <li className="no-padding">
                        <button
                          onClick={openAnalytics.bind(this)}
                          className="ed-text-color width-100 text-left pinter"
                        >
                          {translatr('web.group.main', 'Analytics')}
                        </button>
                      </li>
                    )}
                    {isLeaderboardEnabled && (
                      <li className="no-padding">
                        <button
                          onClick={openLeaderboard.bind(this)}
                          className="ed-text-color width-100 text-left pinter"
                        >
                          {translatr('web.group.main', 'Leaderboard')}
                        </button>
                      </li>
                    )}
                    {!isUserIsPartOfGroup && !isPrivate && !isPending && (
                      <li className="no-padding">
                        <button
                          onClick={joinToGroupHandel.bind(this)}
                          className="ed-text-color width-100 text-left pinter"
                        >
                          {translatr('web.group.main', 'JoinGroup')}
                        </button>
                      </li>
                    )}
                  </ul>
                </Dropdown>
              </div>
            )}
          </div>
          {bannerimageUrls?.medium ? (
            <img
              className="width-100 height-100 group-banner-image"
              src={bannerimageUrls.medium}
              alt={bannerimageAltText || ''}
            />
          ) : (
            <div className="width-100 height-100 default-group-banner-image"></div>
          )}
        </div>
        <div className="info-container">
          <div className="justflex width-100 group-info-header">
            <h3
              className="info-container--title overflow-hidden s-margin-right"
              test-group-id={groupDetails.id}
            >
              {isPrivate && <i className="icon-lock" />}
              {unescape(name)}
            </h3>
            <div className="right-section__button-container justflex">
              {isAnalyticsEnabled && (
                <button
                  className="ed-btn ed-btn-neutral show-btn"
                  onClick={openAnalytics.bind(this)}
                >
                  {translatr('web.group.main', 'Analytics')}
                </button>
              )}
              {showLeaderBoardToMember && isLeaderboardEnabled && (
                <button
                  className="ed-btn ed-btn-neutral show-btn"
                  onClick={openLeaderboard.bind(this)}
                >
                  {translatr('web.group.main', 'Leaderboard')}
                </button>
              )}

              {(isTeamAdmin || isGroupSubAdmin) &&
                !isDynamic &&
                !isImpersonator &&
                (isAdpUser ? (
                  <button className="ed-btn ed-btn-neutral" onClick={openAdpInviteModal.bind(this)}>
                    {translatr('web.group.main', 'InviteAdpEmployees')}
                  </button>
                ) : (
                  <button
                    className="ed-btn ed-btn-neutral"
                    onClick={bulkUploadEnabled ? triggerInvite : openInviteModal}
                  >
                    {translatr('web.group.main', 'InvitePeople')}
                  </button>
                ))}

              {isPending && !isImpersonator ? (
                <div className="justflex s-margin-left accept-decline-btn">
                  <button
                    aria-labelledby={`decline_${groupDetails.id}`}
                    aria-label={translatr('web.group.main', 'DeclineTitle', { title })}
                    className="ed-btn ed-btn-negative ed-btn-outline decline-btn"
                    onClick={declineBtnHandler.bind(this)}
                  >
                    {translatr('web.group.main', 'Decline')}
                  </button>
                  <button
                    aria-labelledby={`join_${groupDetails.id}`}
                    aria-label={translatr('web.group.main', 'JoinGroupTitle', { title })}
                    className="ed-btn ed-btn-primary flex-1"
                    onClick={acceptInvitationGroup.bind(this)}
                    disabled={isInProgress}
                  >
                    {translatr('web.group.main', 'Accept')}
                  </button>
                </div>
              ) : (
                (isUserIsPartOfGroup || !isPrivate) &&
                !isImpersonator &&
                !isEveryoneTeam && (
                  <button
                    aria-labelledby={`${buttonLabel.replace(/\s/g, '')}_${groupDetails.id}`}
                    aria-label={translatr('web.group.main', 'ButtonlabelTitle', {
                      buttonLabel,
                      title
                    })}
                    className={`${buttonClass} group-info`}
                    onClick={joinToGroupHandel.bind(this)}
                    disabled={isInProgress || isMandatory}
                  >
                    <div onMouseEnter={setButtonHoverState} onMouseLeave={setButtonHoverState}>
                      {positiveActionLabel === buttonLabel && (
                        <i className="icon-check no-icon-color" />
                      )}
                      {buttonLabel}
                    </div>
                  </button>
                )
              )}
            </div>
          </div>
          <UserListPreview userIsPartOfGroup={isUserIsPartOfGroup} />
          {groupDetails?.widget?.enabled ? (
            <div className="group-widget-container">
              <div
                className="group-widget-code"
                dangerouslySetInnerHTML={{ __html: safeRender(groupDetails.widget.code) }}
              ></div>
            </div>
          ) : (
            (isTeamAdmin || isGroupSubAdmin) &&
            isWidgetInfoVisible && (
              <div className="group-widget-info">
                <InfoPanel
                  title={translatr('web.group.main', 'GroupWidgetTitle')}
                  content={translatr('web.group.main', 'GroupWidgetDescription')}
                  buttonLabel={translatr('web.group.main', 'AddWidget')}
                  openChannelEditModal={openGroupEditModal}
                  setIsVisible={setWidgetInfoVisible}
                />
              </div>
            )
          )}
        </div>
      </div>
      {isOpenModal && (
        <Modal size="small">
          <ModalHeader
            title={translatr('web.group.main', 'LeaveGroup')}
            onClose={handleModalClose}
          />
          <ModalContent>
            {!isInProgress ? (
              <p>
                {translatr(
                  'web.group.main',
                  'YouWontBeAbleToAccessThisGroupDoYouReallyWantToLeave'
                )}
              </p>
            ) : (
              <div className="text-center">
                <Loading center={false} wide={false} />
              </div>
            )}
          </ModalContent>
          <ModalFooter>
            <button className="ed-btn ed-btn-neutral" onClick={handleModalClose}>
              {translatr('web.group.main', 'Cancel')}
            </button>
            <button className="ed-btn ed-btn-negative" onClick={leaveClick}>
              {translatr('web.group.main', 'LeaveGroup')}
            </button>
          </ModalFooter>
        </Modal>
      )}
    </>
  );
};

GroupConsumptionHeader.propTypes = {
  groupDetails: object,
  memberList: array,
  hideMembersGroupList: bool,
  leaderboard: bool,
  isCurrentUserIsAdmin: bool
};

export default GroupConsumptionHeader;
