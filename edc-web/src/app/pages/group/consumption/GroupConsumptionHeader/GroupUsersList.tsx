import React, { memo, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import { AWS_CDN_STATIC_ASSETS_HOST } from 'edc-web-sdk/config/envConstants';
import classNames from 'classnames';
import Tooltip from 'centralized-design-system/src/Tooltip';

import './GroupUsersList.scss';
import AssociatedUserListModal from '@components/modals/AssociatedUserListModal';
import UserAvatar from '@components/common/UserAvatar';
import { isRtl } from '../../../../../app/utils/locale';
import { AssociatedUserListModalGetDataProps } from './types';

const defaultUserImage = `${AWS_CDN_STATIC_ASSETS_HOST}/assets/anonymous-user.jpeg`;

interface GroupUsersListProps {
  userList: any[];
  listLabel: string;
  elementToShow: number;
  isOverlapped?: boolean;
  redirectionUrl?: string;
  totalMembers?: number;
  isLoadMore?: boolean;
  getNewData?: (props: AssociatedUserListModalGetDataProps) => void;
  position?: string;
  isCuratorList?: boolean;
  tooltipCardInlineCss?: Record<string, any>;
  scrollTooltipRelativeElement?: Record<string, any>;
  shouldRedirect?: boolean;
}


const GroupUsersList: React.FC<GroupUsersListProps> = ({
  userList = [],
  elementToShow,
  listLabel,
  isOverlapped = false,
  redirectionUrl,
  totalMembers,
  isLoadMore,
  getNewData,
  position = 'top',
  isCuratorList = false,
  tooltipCardInlineCss,
  scrollTooltipRelativeElement,
  shouldRedirect
}) => {
  const navigate = useNavigate();
  const currentUserHandle = useSelector((state: any) => state.currentUser.get('handle'));
  const [openModal, setOpenModal] = useState(false);

  useEffect(() => {
    if (isCuratorList) {
      const element = document.querySelector('.left-panel');
      if (element && element instanceof HTMLElement) {
        element.style.zIndex = openModal ? "9999" : "0";
      }
    }
  }, [openModal]);

  if (!totalMembers) {
    return null;
  }

  const isShowViewMore = totalMembers > elementToShow;
  const usersToShow = [...userList].splice(0, elementToShow);
  const listContainerClass = classNames('group__user--container justflex');

  const viewMoreClickHandler = (event: any) => {
    event?.stopPropagation();
    event?.preventDefault();
    if (redirectionUrl && shouldRedirect) {
      navigate(redirectionUrl);
      return;
    }
    setOpenModal(prevState => !prevState);
  };

  const navigateToUser = (handle: any) => {
    //Open the curators profile in new tab
    if (currentUserHandle === handle.substring(1)) {
      window.open('/me', '_blank', 'noopener,noreferrer');
    } else {
      // Open the me tab if curator is current user
      window.open(`/${handle}`, '_blank', 'noopener,noreferrer');
    }
  };

  const listClass = classNames('group__user--list-item relative pointer', {
    'right-space': !isOverlapped
  });

  return (
    <>
      <div className="flex-space-between">
        <label className="group__user--header">{`${listLabel} (${totalMembers})`}</label>
      </div>
      <ul className={listContainerClass}>
        {usersToShow.map((user, index) => {
          const curatorName = `${user.firstName} ${user.lastName}` || user.name;
          const isLast = isShowViewMore && index === elementToShow - 1;
          return (
            <li
              className={listClass}
              style={
                isOverlapped
                  ? { [isRtl() ? 'left' : 'right']: `${index * 15}px` }
                  : {}
              }
              key={user.id}
            >
              <UserAvatar
                avatarImage={user?.avatarimages?.small || user.photo || defaultUserImage}
                name={user.name || curatorName}
                handle={user.handle}
                newTab={isCuratorList}
                postion={position}
                id={user.id}
                tooltipCardInlineCss={tooltipCardInlineCss}
                scrollTooltipRelativeElement={scrollTooltipRelativeElement}
              />
              {isLast && (
                <button
                  className="more-user make-center pointer"
                  onClick={viewMoreClickHandler.bind(this)}
                  tabIndex={-1}
                >
                  <Tooltip message={translatr('web.group.main', 'ViewAll')}>
                    <i className="icon-ellipsis-h" />
                  </Tooltip>
                </button>
              )}
            </li>
          );
        })}
      </ul>
      {openModal && (
        <AssociatedUserListModal
          userList={userList}
          label={tr(listLabel)}
          totalUsers={totalMembers}
          onClose={viewMoreClickHandler}
          navigateToUser={navigateToUser}
          getData={getNewData}
          isLoadMore={isLoadMore}
        />
      )}
    </>
  );
};

export default memo(GroupUsersList);
