import { bool, number } from 'prop-types';
import React, { useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { translatr } from 'centralized-design-system/src/Translatr';
import Modal from 'centralized-design-system/src/Modals';
import Dropdown from 'centralized-design-system/src/Dropdown';
import { createDynamicSelectionGroup, deleteGroup } from 'edc-web-sdk/requests/groups.v2';
import { Permissions } from '@utils/checkPermissions';
import { confirmationPrivateCardModal } from '@actions/modalActions';
import { leaveFromGroup } from '@actions/groupsActionsV2';
import { requestUserIsGroupLeader } from '@actions/currentUserActions';
import ConfirmationDialog from '@components/modals/ConfirmationModalV2';
import capture from '../../../../../app/utils/datalayer';
import { open_v2 as openSnackBar } from '../../../../../app/actions/snackBarActions';
import { GroupConsumptionContext } from '../GroupConsumptionProvider';
import GroupDropdownElement from './GroupDropdownElement';

const ORG_GROUPS_PATH = '/org-groups';

const GroupSettings = ({ pendingUsersCount, isMemberListEnabled }) => {
  const navigate = useNavigate();

  const [deleteConfirmationVisible, setDeleteConfirmationVisible] = useState(false);
  const {
    groupDetails,
    dispatch,
    isCurrentUserIsSuperAdmin,
    isCurrentUserIsAdmin,
    showPlareUI
  } = useContext(GroupConsumptionContext);
  const {
    id: groupId,
    isTeamAdmin,
    isTeamSubAdmin,
    isMember,
    slug,
    isEveryoneTeam,
    isMandatory,
    isDynamic,
    isDynamicSelected,
    name,
    communityType,
    enablePostingFeed: enablePostingFeedInGroup,
    isTeamModerator,
    userBelongsToGroup
  } = groupDetails;

  const isOrgAdmin = Permissions.has('ADMIN_ONLY');
  const isInGroup = userBelongsToGroup;
  const isTeamAdminOrSubAdmin = isTeamAdmin || (isCurrentUserIsAdmin && isTeamSubAdmin);

  const couldEditGroup =
    isOrgAdmin || isTeamAdmin || isTeamAdminOrSubAdmin || isCurrentUserIsSuperAdmin;
  const showManageGroup = isOrgAdmin || isTeamAdmin || isTeamSubAdmin;
  const showManagePosting =
    enablePostingFeedInGroup && (isOrgAdmin || isTeamAdmin || isTeamSubAdmin || isTeamModerator);
  const showPendingMembers = !isMember && isMemberListEnabled && pendingUsersCount > 0;
  const showInsights = isTeamAdminOrSubAdmin && window.__edOrgData?.groupInsightsEnabled;
  const showDeleteGroup = couldEditGroup && (!isDynamic || isDynamicSelected);
  const showLeaveOption = isInGroup && !isEveryoneTeam && !isMandatory;

  const isGroupSettingsVisible =
    couldEditGroup ||
    showLeaveOption ||
    showManageGroup ||
    showPendingMembers ||
    showInsights ||
    showDeleteGroup;

  const navigateToEditPage = () => {
    navigate({
      pathname: `/teams/${slug}/edit`
    });
  };

  const groupLeaveConfirmation = group => {
    capture('Group Left', group);
    dispatch(leaveFromGroup(group.id));
    if (isTeamAdmin) {
      dispatch(requestUserIsGroupLeader());
    }
    navigate(ORG_GROUPS_PATH);
  };

  const leaveGroup = group => {
    if (group) {
      dispatch(
        confirmationPrivateCardModal(
          translatr('web.group.main', 'LeaveGroup'),
          translatr('web.group.main', 'YouWontBeAbleToAccessThisGroupDoYouReallyWantToLeave'),
          false,
          () => groupLeaveConfirmation(group),
          null,
          showPlareUI
        )
      );
    } else {
      groupLeaveConfirmation(group);
    }
  };

  const confirmRemoveGroup = () => {
    const handleSuccess = () => {
      dispatch(
        openSnackBar(translatr('web.group.main', 'DeleteGroupToastMessage', { group_name: name }))
      );
      navigate(ORG_GROUPS_PATH);
    };

    if (isDynamic) {
      const payload = {
        type: 'dynamic-selection-group',
        action: 'delete',
        payload: {
          groupId: groupId,
          groupTitle: name
        }
      };
      createDynamicSelectionGroup(payload)
        .then(handleSuccess)
        .catch(err => {
          console.error(
            `Error in GroupConsumptionHeader.GroupSettings.confirmRemoveGroup.createDynamicSelectionGroup.func: ${err}`
          );
        });
    } else {
      deleteGroup(groupId)
        .then(handleSuccess)
        .catch(err => {
          console.error(`Error in GroupConsumptionHeader.GroupSettings.deleteGroup.func : ${err}`);
        });
    }
  };

  return (
    <>
      {isGroupSettingsVisible && (
        <Dropdown
          ariaLabel={translatr('web.common.main', 'Settings')}
          icon={<i className="icon-settings" />}
        >
          <ul>
            <GroupDropdownElement
              show={couldEditGroup}
              onClick={navigateToEditPage}
              label={translatr('web.group.main', 'EditGroup')}
              positiveAction
            />
            <GroupDropdownElement
              show={showManagePosting}
              onClick={() =>
                navigate({
                  pathname: `/teams/${slug}/manage-post`,
                  state: {
                    groupId,
                    communityType: communityType
                  }
                })
              }
              label={translatr('web.group.main', 'ManagePostPageTitle')}
              positiveAction
            />
            <GroupDropdownElement
              show={showManageGroup}
              onClick={() => navigate(`/teams/${slug}/members`)}
              label={translatr('web.group.main', 'ManageGroup')}
              positiveAction
            />
            <GroupDropdownElement
              show={showPendingMembers}
              onClick={() => navigate(`/teams/${slug}/pending`)}
              label={`${translatr('web.group.main', 'PendingMembers')} (${pendingUsersCount})`}
              positiveAction
            />
            <GroupDropdownElement
              show={showInsights}
              onClick={() => navigate(`/teams/${slug}/analytics/insights`)}
              label={translatr('web.group.main', 'Insights')}
              positiveAction
            />
            <GroupDropdownElement
              show={showLeaveOption}
              onClick={leaveGroup.bind(this, groupDetails)}
              label={translatr('web.group.main', 'LeaveGroup')}
            />
            <GroupDropdownElement
              show={showDeleteGroup}
              onClick={() => setDeleteConfirmationVisible(true)}
              label={translatr('web.group.main', 'DeleteGroup')}
            />
          </ul>
        </Dropdown>
      )}

      {deleteConfirmationVisible && (
        <Modal size="small">
          <ConfirmationDialog
            message={
              communityType
                ? translatr('web.group.main', 'RemoveCommunityConfirmation')
                : translatr('web.group.main', 'RemoveGroupConfirmation', {
                    groupName: name
                  })
            }
            noNeedClose={true}
            closeModal={() => setDeleteConfirmationVisible(false)}
            cancelClick={() => setDeleteConfirmationVisible(false)}
            callback={() => confirmRemoveGroup()}
            confirmBtnTitle={translatr('web.group.main', 'DeleteGroup')}
            isNegativeValue={true}
            isTranslated
          />
        </Modal>
      )}
    </>
  );
};

GroupSettings.propTypes = {
  pendingUsersCount: number,
  isMemberListEnabled: bool
};

export default GroupSettings;
