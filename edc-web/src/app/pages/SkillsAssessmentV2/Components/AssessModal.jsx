import React, { useState, useEffect, useRef } from 'react';
import { array, func, string, object } from 'prop-types';
import FocusLock from 'react-focus-lock';
import { getPeerAssessment } from 'edc-web-sdk/requests/skills.v2';
import { translatr } from 'centralized-design-system/src/Translatr';
import Modal, {
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON>eader,
  ModalFooter
} from 'centralized-design-system/src/Modals';
import Avatar from 'centralized-design-system/src/Avatar';
import { Select, AreaInput } from 'centralized-design-system/src/Inputs';
import Loading from 'centralized-design-system/src/Loading';
import { Button } from 'centralized-design-system/src/Buttons';
import UserProfile from './UserProfile';
import ConfirmationModal from './ConfirmationModal';
import { processPeersSkills } from '../utils';
import Tooltip from 'centralized-design-system/src/Tooltip';
import truncateMessageText from '@utils/truncateMessageText';
import CommentClamp from './CommentClamp';

import './AssessModal.scss';

const AssessModal = ({ usr, submit, handleOpen, tab, assessments, levels }) => {
  const { fullName, isCompleted } = usr || {};
  const [isBtnEnabled, setIsBtnEnabled] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [data, setData] = useState({});
  const [openComments, setOpenComments] = useState({});
  const [showMore, setShowMore] = useState({});
  const [showMoreBtn, _setShowMoreBtn] = useState({});
  const [skills, setSkills] = useState([]);
  const [showConfirm, setShowConfirm] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [user, setUser] = useState({});
  const [updatedSkills, setUpdatedSkills] = useState([]);
  const showMoreBtnRef = useRef(showMoreBtn);
  const setShowMoreBtn = items => {
    showMoreBtnRef.current = { ...items };
    _setShowMoreBtn(items);
  };
  const currentUsrId = window.__ED__.id;

  useEffect(() => {
    setModalTitle(
      tab === 'peer'
        ? `${translatr('web.skills-assessments-v2.main', 'RateSkillsFor')} ${fullName}`
        : translatr('web.skills-assessments-v2.main', 'RateSkills')
    );
    if (tab === 'peer' && usr?.id) {
      fetchPeerAssessments(usr.id);
    }
  }, [usr]);

  useEffect(() => {
    setSkills(assessments || []);
  }, [assessments]);

  const fetchPeerAssessments = invite_id => {
    setLoading(true);
    getPeerAssessment(invite_id)
      .then(resp => {
        setData(resp);
        setSkills(processPeersSkills(resp.skills || []));
        setUser(resp.user);
        setLoading(false);
      })
      .catch(err => {
        let errMsg = err.response.body ? err.response.body.message : err.response.error.message;
        errMsg = errMsg || err.toString() || '';
        console.error(`Error in getPeerAssessment.func: ${errMsg}`);
        setLoading(false);
      });
  };

  const getPayload = () => {
    const updated = updatedSkills.map(item => {
      const { skill_node_id, skill_name, skill_type } = item.skill;
      if (tab === 'self') {
        return {
          proficiency_level: `${item.value}`,
          skill_node_id,
          skill_name,
          skill_type
        };
      } else {
        const { comment } = item;
        return {
          proficiency_level: `${item.value}`,
          skill_node_id,
          skill_name,
          id: item.skill.id,
          comment: comment || ''
        };
      }
    });

    return tab === 'self'
      ? { skills: [...updated] }
      : {
          id: data.skill_invitation_id,
          skills: [...updated]
        };
  };

  const handleConfirmation = status => {
    setShowConfirm(false);
    if (status) {
      submit({ ...getPayload() });
    }
  };

  const onLevelChange = (opt, skill) => {
    const skillId = skill.skill_node_id;

    if (!updatedSkills.length) {
      setIsBtnEnabled(true);
      setUpdatedSkills([{ ...opt, skillId, skill }]);
    } else {
      let updated = [];
      let find = false;
      updatedSkills.forEach(item => {
        if (item.skillId === skillId) {
          const theSkill = skills.find(s => s.skill_node_id === skillId);
          find = true;
          if (opt.id !== theSkill.current_skill_level) {
            updated.push({ ...opt, skillId, skill });
          }
        } else {
          updated.push(item);
        }
      });
      if (!find) {
        updated.push({ ...opt, skillId, skill });
      }
      setIsBtnEnabled(updated.length);
      setUpdatedSkills(updated);
    }
  };

  const updateComment = (skill, val) => {
    const skillId = skill.skill_node_id;
    setIsBtnEnabled(true);
    if (!updatedSkills.length) {
      setUpdatedSkills([{ ['comment']: val, skillId, skill }]);
    } else {
      let updated = [];
      let find = false;
      updatedSkills.forEach(item => {
        if (item.skillId === skillId) {
          find = true;
          item['comment'] = val;
        }
        updated.push(item);
      });
      if (!find) {
        updated.push({ ['comment']: val, skillId, skill });
      }
      setUpdatedSkills(updated);
    }
  };

  const handleOpenComments = (id, open = true) => {
    setOpenComments({ ...openComments, [id]: open });
  };

  const handleShowMore = (id, more) => {
    const it = { ...showMoreBtnRef.current };
    it[id] = more;
    setShowMoreBtn(it);
  };

  const onClickMore = id => {
    setShowMore({ ...showMore, [id]: !showMore[id] });
  };

  const renderWarningMessage = message => {
    return (
      <div className="warning" aria-label={message}>
        {message}
      </div>
    );
  };

  const disabledLevels = allLevels => {
    return allLevels
      .filter(level => level.disabled)
      .map(level => {
        return level.label;
      })
      .join(', ');
  };

  const renderHeaders = headers => {
    return (
      <div className="headers-row">
        {headers.map(hKey => {
          const hKeyLabel =
            hKey === 'Skills'
              ? translatr('web.skills-assessments-v2.main', hKey)
              : translatr('web.common.main', hKey);
          return (
            <span className="ed-base-text fw-600" key={hKey}>
              {hKeyLabel}
            </span>
          );
        })}
      </div>
    );
  };

  const obtainItems = () => {
    const skillLevels = skills
      .filter(skill => skill.skill_level)
      .map(skill => {
        return skill.skill_level;
      });
    return levels
      .map(level => {
        const hidden = !!level.hidden;
        const disabled = !!level.hidden && skillLevels.includes(level.value);
        return {
          ...level,
          hidden: hidden,
          disabled: disabled
        };
      })
      .filter(l => !l.hidden || (l.hidden && l.disabled));
  };

  const renderAssessSelf = () => {
    if (!skills.length) {
      return renderEmpty();
    }
    const determinedItems = obtainItems();
    const showDisabledLevelMessage = determinedItems.find(skillLevel => skillLevel.disabled);
    const headers = ['Skill', 'Level'];
    return (
      <div className="self-assess">
        {showDisabledLevelMessage &&
          renderWarningMessage(
            translatr('web.skills-assessments-v2.main', 'SelectedLevelIsDisabled', {
              skillLevelName: disabledLevels(determinedItems)
            })
          )}
        {renderHeaders(headers)}
        <div className="self-content">
          {skills.map(skill => {
            const skillName = truncateMessageText(skill.skill_name, 42, true);
            const hideTooltip = skill.skill_name?.length <= 42;
            return (
              <div className="skill-row" key={skill.skill_node_id}>
                <div>
                  <Tooltip message={skill.skill_name} pos="top" hide={hideTooltip}>
                    <span className="ed-base-text">{skillName}</span>
                  </Tooltip>
                </div>
                <div className="level-selector">
                  <Select
                    key={skill.skill_node_id}
                    items={determinedItems}
                    defaultValue={skill.skill_level}
                    translateDropDownOptions={false}
                    onChange={option => onLevelChange(option, skill)}
                    ariaLabel={`${translatr('web.common.main', 'Select')} ${
                      skill.skill_name
                    } ${translatr('web.skills-assessments-v2.main', 'Level')}`}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderSelfRating = () => {
    if (!skills.length) {
      return renderEmpty();
    }

    const headers = ['Skill', 'YourAssessment'];
    return (
      <div className="peer-assess">
        <div className="peer-profile">
          <UserProfile data={usr} disableEditingJobRoleAndFamily={true} />
        </div>
        {renderHeaders(headers)}
        <div className="peer-content">
          {skills.map(skill => {
            const { proficiency_level, comment, skill_node_id, skill_name } = skill;
            const translated_level =
              proficiency_level !== null ? proficiency_level.translated_level : 'not_assessed';
            const skillAriaLabel =
              translated_level === 'not_assessed'
                ? translatr('web.skills-assessments-v2.main', 'NoRatingGiven')
                : translated_level;

            const skillName = truncateMessageText(skill_name, 42, true);
            const hideTooltip = skill_name?.length <= 42;

            return (
              <div className="skill-row" key={skill_node_id} role="row">
                <div>
                  <Tooltip message={skill_name} pos="top" hide={hideTooltip}>
                    <label
                      className="ed-base-text"
                      role="cell"
                      aria-label={skill_name}
                      // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                      tabIndex={0}
                    >
                      <span aria-hidden>{skillName}</span>
                    </label>
                  </Tooltip>
                </div>
                {!comment?.length ? (
                  <div className="view-comment-section">
                    <label
                      className="ed-base-text text-overflow-ellipsis"
                      aria-label={skillAriaLabel}
                      role="cell"
                      // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                      tabIndex={0}
                    >
                      <span aria-hidden>
                        {translated_level === 'not_assessed' ? skillAriaLabel : translated_level}
                      </span>
                    </label>
                  </div>
                ) : (
                  <div className="view-comment-section">
                    <label
                      className="ed-base-text text-overflow-ellipsis"
                      aria-label={skillAriaLabel}
                      role="cell"
                      // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                      tabIndex={0}
                    >
                      <span aria-hidden>
                        {translated_level === 'not_assessed' ? skillAriaLabel : translated_level}
                      </span>
                    </label>
                    <CommentClamp
                      comment={comment}
                      id={skill_node_id}
                      handler={handleShowMore}
                      showMore={showMore[skill_node_id] || false}
                    />
                    {showMoreBtn[skill_node_id] && (
                      <button onClick={() => onClickMore(skill_node_id)} className="comment-btn">
                        {showMore[skill_node_id]
                          ? translatr('web.common.main', 'ShowLess')
                          : translatr('web.common.main', 'ShowMore')}
                      </button>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderAssessPeers = () => {
    if (!skills.length) {
      return renderEmpty();
    }

    const { id, avatarimages, manager } = user;
    const enableComments = currentUsrId === manager?.id;

    const headers = ['Skill', 'Level'];
    const determinedItems = obtainItems();
    const showDisabledLevelMessage = determinedItems.find(skillLevel => skillLevel.disabled);
    return (
      <div className="peer-assess">
        <div className="flex align-items-center">
          <span className="font-14" role="tab" tabIndex={0}>
            {`${translatr('web.skills-assessments-v2.main', 'FeedbackAbout')}: `}
          </span>
          <div className="peer-avatar" role="tab" tabIndex={0}>
            <Avatar
              user={{
                name: fullName,
                imgUrl: avatarimages?.small,
                id
              }}
              blankAlt={false}
            />
          </div>
          <span className="font-14" role="tab" tabIndex={0}>
            {fullName || ''}
          </span>
        </div>
        {showDisabledLevelMessage &&
          renderWarningMessage(
            translatr('web.skills-assessments-v2.main', 'SelectedLevelIsDisabled', {
              skillLevelName: disabledLevels(determinedItems)
            })
          )}
        {renderHeaders(headers)}
        <div className="peer-content">
          {skills.map(skill => {
            const { skill_level, comment, skill_node_id, skill_name } = skill;
            const skillAriaLabel =
              skill_level === 'not_assessed'
                ? translatr('web.skills-assessments-v2.main', 'NoRatingGiven')
                : skill_level;

            const skillName = truncateMessageText(skill_name, 42, true);
            const hideTooltip = skill_name?.length <= 42;

            return (
              <div className="skill-row" key={skill_node_id} role="row">
                <div>
                  <Tooltip message={skill_name} pos="top" hide={hideTooltip}>
                    <label
                      className="ed-base-text"
                      role="cell"
                      aria-label={skill_name}
                      // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                      tabIndex={0}
                    >
                      <span aria-hidden>{skillName}</span>
                    </label>
                  </Tooltip>
                </div>
                {isCompleted ? (
                  !enableComments ? (
                    <div className="view-comment-section">
                      <label
                        className="ed-base-text text-overflow-ellipsis"
                        aria-label={skillAriaLabel}
                        role="cell"
                        // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                        tabIndex={0}
                      >
                        <span aria-hidden>
                          {skill_level === 'not_assessed' ? '--' : skill_level}
                        </span>
                      </label>
                    </div>
                  ) : (
                    <div className="view-comment-section">
                      <label
                        className="ed-base-text text-overflow-ellipsis"
                        aria-label={skillAriaLabel}
                        role="cell"
                        // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                        tabIndex={0}
                      >
                        <span aria-hidden>
                          {skill_level === 'not_assessed' ? '--' : skill_level}
                        </span>
                      </label>
                      {comment?.length > 0 && (
                        <CommentClamp
                          comment={comment}
                          id={skill_node_id}
                          handler={handleShowMore}
                          showMore={showMore[skill_node_id] || false}
                        />
                      )}
                      {showMoreBtn[skill_node_id] && (
                        <button onClick={() => onClickMore(skill_node_id)} className="comment-btn">
                          {showMore[skill_node_id]
                            ? translatr('web.common.main', 'ShowLess')
                            : translatr('web.common.main', 'ShowMore')}
                        </button>
                      )}
                    </div>
                  )
                ) : (
                  <div className="level-selector">
                    <Select
                      key={skill_node_id}
                      items={determinedItems}
                      defaultValue={skill_level}
                      onChange={option => onLevelChange(option, skill)}
                      aria-label={`${translatr(
                        'web.common.main',
                        'Select'
                      )} ${skill_name} ${translatr('web.skills-assessments-v2.main', 'Level')}`}
                    />
                    {enableComments && renderMgrComment(skill)}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderEmpty = () => {
    return <div>{translatr('web.skills-assessments-v2.main', 'NoData')}</div>;
  };

  const renderMgrComment = skill => {
    const { skill_node_id, comment } = skill;
    return (
      <div className="manager-comment-section">
        {openComments[skill_node_id] ? (
          <AreaInput
            id={`comment-${skill_node_id}`}
            title={translatr('web.common.main', 'Comment')}
            defaultTextInput={comment}
            setValue={value => updateComment(skill, value)}
            placeholder={translatr('web.skills-assessments-v2.main', 'InputText')}
            maxLen={5000}
            shouldCheckForMaxChar={true}
            optional={true}
          />
        ) : !comment?.length ? (
          <button
            className="comment-btn ed-support-text"
            onClick={() => handleOpenComments(skill_node_id)}
          >
            + {translatr('web.common.main', 'Comment')}
          </button>
        ) : (
          <div className="edit-comment">
            <label
              className="ed-support-text text2-overflow-ellipsis"
              // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
              tabIndex={0}
            >
              {comment}
              {'  '}
            </label>
            <button
              className="comment-btn font14 active-color"
              onClick={() => handleOpenComments(skill_node_id)}
            >
              {translatr('web.common.main', 'Edit')}
            </button>
          </div>
        )}
      </div>
    );
  };

  const handleSubmit = () => {
    if (tab === 'peer') {
      setShowConfirm(true);
    } else {
      submit({ ...getPayload() });
    }
  };

  return (
    <Modal size="small" className="assess-modal">
      <FocusLock>
        <ModalHeader title={modalTitle} onClose={() => handleOpen(false)} />
        <ModalContent>
          {isLoading && <Loading />}
          {!isLoading && tab === 'view-rating' && renderSelfRating()}
          {!isLoading && tab === 'peer' && renderAssessPeers()}
          {!isLoading && tab === 'self' && renderAssessSelf()}
        </ModalContent>
        <ModalFooter>
          <Button color="secondary" variant="ghost" onClick={() => handleOpen(false)}>
            {translatr('web.common.main', 'Cancel')}
          </Button>
          <Button
            color="primary"
            className="ed-btn ed-btn-primary"
            disabled={!isBtnEnabled}
            onClick={() => handleSubmit()}
          >
            {translatr('web.common.main', 'Submit')}
          </Button>
        </ModalFooter>
        {showConfirm && (
          <Modal size="small">
            <ConfirmationModal confirm={handleConfirmation} name={fullName} />
          </Modal>
        )}
      </FocusLock>
    </Modal>
  );
};

AssessModal.propTypes = {
  assessments: array,
  levels: array,
  usr: object,
  tab: string.isRequired,
  submit: func.isRequired,
  handleOpen: func.isRequired
};
export default AssessModal;
