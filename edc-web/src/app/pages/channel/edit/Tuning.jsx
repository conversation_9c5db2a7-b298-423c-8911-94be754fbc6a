import { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import { NumberInput, AsyncSearchInput } from 'centralized-design-system/src/Inputs';
import MultiSelectDropdownWithCheckbox from 'centralized-design-system/src/Inputs/MultiSelectDropdownWithCheckbox';
import ChannelContext from '../ChannelContext';
import { saveChannelProgramming } from 'edc-web-sdk/requests/channels.v2';
import { open_v2 as openSnackBar } from '../../../../app/actions/snackBarActions';

import Spinner from '../../../../app/components/common/spinner';
import SettingsActionBar from './SettingsActionBar';
import fetchTuningData from '../utils/fetchTuningData';
import getSelectedEclSources from '../utils/getSelectedEclSources';
import generateEclSourcesArray from '../utils/generateEclSourcesArray';
import generateAndSetLanguages from '../utils/generateAndSetLanguages';
import generateAndSetContentType from '../utils/generateAndSetContentType';
import { func } from 'prop-types';

const Tuning = ({ setIsValueChanged = () => {} }) => {
  const navigate = useNavigate();

  const {
    channel,
    setChannel,
    dispatch,
    teamLanguageDetails,
    organizationStandardTypes,
    isEditPage
  } = useContext(ChannelContext);
  const {
    id: channelId,
    cardFetchLimit,
    slug,
    standardTypes: channelStandardTypes,
    language: channelLanguages,
    contentType: channelContentTypes,
    curateOnly
  } = channel;
  const [isLoading, setIsLoading] = useState(true);
  const [maxNumber, setMaxNumber] = useState(cardFetchLimit);
  const [channelTopics, setChannelTopics] = useState([]);
  const [eclSources, setEclSources] = useState([]);
  const [contentTypes, setContentTypes] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [selectedEclSources, setSelectedEclSources] = useState([]);
  const [selectedContentTypes, setSelectedContentTypes] = useState([]);
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    (async () => {
      const [
        channelSourcesAPIResp,
        channelTopicsAPIResp,
        eclSourcesAllAPIResp,
        existingLanguagesResp
      ] = await fetchTuningData(channelId);
      setIsLoading(false);
      const selectedEclSourcesArray = getSelectedEclSources(channelSourcesAPIResp);
      const theTopics = (channelTopicsAPIResp?.topics || []).map(topic => {
        return { ...topic, value: topic.label };
      });
      setSelectedEclSources(selectedEclSourcesArray);
      setChannelTopics(theTopics);
      const existingContentLanguageCodes = existingLanguagesResp.map(language => language.code);
      generateAndSetContentType(
        organizationStandardTypes,
        channelStandardTypes,
        setSelectedContentTypes,
        setContentTypes
      );
      generateAndSetLanguages(
        existingContentLanguageCodes,
        teamLanguageDetails,
        channelLanguages,
        setLanguages,
        setSelectedLanguages
      );
      setEclSources(generateEclSourcesArray(eclSourcesAllAPIResp, selectedEclSourcesArray));
      const newDiv = document.createElement('div');
      newDiv?.classList?.add('icon-angle-down-arrow');
      document
        .querySelector('.ed-multiselect-tree .react-dropdown-tree-select .dropdown-trigger ')
        ?.append(newDiv);
    })();
  }, []);

  const updateChannelTopics = () => {
    setIsUpdating(true);
    const maxNumberToUpdate = isNaN(maxNumber) || maxNumber === null ? null : Number(maxNumber);
    const selectedContentTypesArray = selectedContentTypes.map(type => type.slug);
    const selectedLanguagesArray = selectedLanguages.map(lang => lang.id);
    const channelPayload = {
      channel: {
        content_type: channelContentTypes,
        language: selectedLanguagesArray,
        standard_types: selectedContentTypesArray,
        card_fetch_limit: !curateOnly ? maxNumberToUpdate : null,
        tuning_topics: channelTopics.map(topic => {
          const { id, label } = topic;
          return { id: id, label: label };
        }),
        ecl_sources: selectedEclSources.map(source => {
          const { id, name } = source;
          return { source_id: id, display_name: name };
        })
      }
    };
    saveChannelProgramming(channelId, channelPayload)
      .then(() => {
        setChannel({
          ...channel,
          cardFetchLimit: maxNumberToUpdate,
          standardTypes: selectedContentTypesArray,
          language: selectedLanguagesArray
        });
        if (isEditPage) {
          setIsUpdating(false);
          dispatch(
            openSnackBar(
              translatr(
                'web.channel.main',
                'UpdateIsSuccessfulPleaseClickCancelbackButtonToVisitChannel'
              ),
              'success'
            )
          );
          setIsValueChanged(false);
        } else {
          dispatch(
            openSnackBar(translatr('web.channel.main', 'ChannelSavedSuccessfully'), 'success')
          );
          navigate(`/channel/${slug}`);
        }
      })
      .catch(err => {
        setIsUpdating(false);
        console.error(`Error in contentTuning.programChannel.func : ${err}`);
      });
  };

  const handleChannelTopicsChange = topics => {
    setChannelTopics(topics);
  };

  if (channelTopics.length > 0) {
    document.querySelector(' input.search')?.classList?.add('hide-placeholder');
  } else {
    document.querySelector(' input.search')?.classList?.remove('hide-placeholder');
  }
  const updateEclSelection = value => {
    setSelectedEclSources(value);
    isEditPage && setIsValueChanged(true);
  };
  const updateContentSelection = value => {
    setSelectedContentTypes(value);
    isEditPage && setIsValueChanged(true);
  };
  const updateLanguageSelection = value => {
    setSelectedLanguages(value);
    isEditPage && setIsValueChanged(true);
  };
  const updateMaxNumber = value => {
    setMaxNumber(value);
    isEditPage && setIsValueChanged(true);
  };
  return (
    <div className="block settings-container">
      <div className="inner-container">
        <h1 className="heading">{translatr('web.channel.main', 'Tuning')}</h1>
        {isLoading && (
          <div className="text-center mt-24 mb-24">
            <Spinner />
          </div>
        )}
        {!isLoading && (
          <>
            <div className="ed-multiselect-tree">
              <div className="ed-input-container">
                <AsyncSearchInput
                  id="skill_name"
                  title={translatr('web.channel.main', 'ContentTopics')}
                  placeholder={translatr('web.channel.main', 'ClickHereToSelectTopics')}
                  description={translatr(
                    'web.channel.main',
                    'ReceiveContentRecommendationsBasedOnTheSelectedTopics'
                  )}
                  extraPayload={{
                    fields: 'id,label,name'
                  }}
                  users={false}
                  groups={false}
                  channels={false}
                  topics={true}
                  multiselect={true}
                  onChange={handleChannelTopicsChange}
                  items={channelTopics || []}
                  isClearable={true}
                  optional="optional"
                />
              </div>
            </div>
            <br />
            <MultiSelectDropdownWithCheckbox
              title={translatr('web.common.main', 'ContentSources')}
              description={translatr(
                'web.common.main',
                'ReceiveContentRecommendationsFromSelectedSources'
              )}
              placeholder={translatr('web.common.main', 'ClickHereToSelectSources')}
              items={eclSources}
              disabled={eclSources.length === 0}
              defaultSelection={[]}
              updateItems={setEclSources}
              updateSelections={updateEclSelection}
              showTooltip
              screenReaderInstructions={translatr(
                'cds.common.main',
                'SelectedSourcesAddedAboveFormField'
              )}
            />
            <br />
            <MultiSelectDropdownWithCheckbox
              title={translatr('web.common.main', 'ContentTypes')}
              description={translatr(
                'web.common.main',
                'ReceiveContentRecommendationsOfTheSelectedTypes'
              )}
              placeholder={translatr('web.common.main', 'ClickHereToSelectContentTypes')}
              items={contentTypes}
              disabled={contentTypes.length === 0}
              defaultSelection={[]}
              updateItems={setContentTypes}
              updateSelections={updateContentSelection}
              screenReaderInstructions={translatr(
                'cds.common.main',
                'SelectedTypesAddedAboveFormField'
              )}
            />
            <br />
            <MultiSelectDropdownWithCheckbox
              title={translatr('web.channel.main', 'Languages')}
              description={translatr(
                'web.common.main',
                'ReceiveContentRecommendationsOnlyInTheSelectedLang'
              )}
              placeholder={translatr('web.common.main', 'ClickHereToSelectLanguages')}
              items={languages}
              disabled={languages.length === 0}
              defaultSelection={[]}
              updateItems={setLanguages}
              updateSelections={updateLanguageSelection}
              screenReaderInstructions={translatr(
                'cds.common.main',
                'SelectedLanguagesAddedAboveFormField'
              )}
            />
            <br />
            {!curateOnly && (
              <>
                <NumberInput
                  id="tuning-max-recommendation"
                  title={translatr('web.common.main', 'MaxNumberOfRecommendations')}
                  description={translatr(
                    'web.common.main',
                    'TheAiEngineWillProvideAMaximumNumberOfRecommendati'
                  )}
                  min={1}
                  max={500}
                  setValue={updateMaxNumber}
                  defaultValue={maxNumber}
                />
                <br />
              </>
            )}

            <SettingsActionBar
              isUpdating={isUpdating}
              onSave={updateChannelTopics}
              dispatch={dispatch}
              slug={slug}
            />
          </>
        )}
      </div>
    </div>
  );
};
Tuning.propTypes = {
  setIsValueChanged: func
};
export default Tuning;
