import React, { createContext, useContext, useState, useEffect, FunctionComponent, ReactNode } from 'react';
import { useDispatch } from 'react-redux';
import {AdminHomePageContext, Widget, LayoutList, CachedLayoutsMap} from '../types';
import { useBlocksAdminFactory } from '@edc-blocks/blocks-decorators';
import { useNavigate, useParams } from 'react-router-dom';
import {createAdminBlocks} from '../../HomeV2/helpers';
import {theming} from '../../HomeV2';
import { Blueprint } from 'edc-blocks/packages/blocks-render-library';
import { getAvailableWidgets, fetchAllLayouts } from 'edc-web-sdk/requests/blocks';
import { sortWidgets } from '../utils/sortWidgets';
import { useLazyApi } from '../utils/useLazyApi';
import { isSnapShotValid } from '../utils/isSnapshotValid';
import { getBlocksDataV2 } from 'edc-web-sdk/requests/blocks';
import EmptyState from 'centralized-design-system/src/EmptyState';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Button } from 'centralized-design-system/src/Buttons';
import {EmptyState as EmptyStateData} from '../../HomeV2/helpers';
import { open_v2 as openSnackBarV2 } from '../../../actions/snackBarActions';


const AdminHomeConfigContext = createContext<AdminHomePageContext>(null);

interface Props {
  children: ReactNode;
}


export const AdminHomeContextWrapper: FunctionComponent<Props> = ({ children }) => {
  let { layoutId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const [availableWidgets, setAvailableWidgets] = useState<null | Widget[]>(null);
  const [availableWidgetsError, setAvailableWidgetsError] = useState<boolean>(false);

  const [originalAvailableWidgets, setOriginalAvailableWidgets] = useState<null | Widget[]>(null);

  const [layoutList, setLayoutList] = useState<null | LayoutList[]>(null);

  const [originalLayout, setOriginalLayout] = useState<null | Blueprint>(null);

  const [cachedLayoutsMap, setCachedLayoutsMap] = useState<CachedLayoutsMap>({});

  const [currentLayoutId, setCurrentLayoutId] = useState(layoutId);

  const [fetchBlocksData, { data, loading, loaded, error }] = useLazyApi(getBlocksDataV2);

  const [showPreviousSessionModal, setShowPreviousSessionModal] = useState<boolean>(false);
  const [loadingFromCache, setLoadingFromCache] = useState<boolean>(false);

  const blocks = createAdminBlocks();

  const { 
    snapshot: currentLayout,
    setSnapshot: setCurrentLayout,
    deleteRow,
    moveWidgetLeft,
    moveWidgetRight,
    moveWidgetUp,
    moveWidgetDown,
    moveRowUp,
    moveRowDown,
    currentFocusedWidget,
    lastWidgetAdded,
    setLastWidgetAdded,
    openedFromHTMLElement,
    setOpenedFromHTMLElement,
    currentFocusedRow,
    deleteBlock,
    handleRowDistributionChange,
    updateRowWidth
  } = useBlocksAdminFactory(null);

  useEffect(() => {
    if(layoutId !== undefined && layoutId !== null)
      setCurrentLayoutId(layoutId);

    const previousSnapshot = window.localStorage.getItem(`admin_landing_page_layout_${layoutId}`);  
    
    if (previousSnapshot && isSnapShotValid(JSON.parse(previousSnapshot))) {
      setShowPreviousSessionModal(true);
    } 
    else if (cachedLayoutsMap.hasOwnProperty(layoutId) && isSnapShotValid(JSON.parse(JSON.stringify(cachedLayoutsMap[layoutId])))){
      setLoadingFromCache(true);
      setOriginalLayout(JSON.parse(JSON.stringify(cachedLayoutsMap[layoutId])));
      setCurrentLayout(JSON.parse(JSON.stringify(cachedLayoutsMap[layoutId])));
      setTimeout(() => {
        setLoadingFromCache(false);
      }, 500);
    }
    else {
      fetchBlocksData('landing', 'true', layoutId);
    }
  }, [layoutId]);

  useEffect(() => {
    getAvailableWidgets('landing')
    .then((response: any)=>{
      setOriginalAvailableWidgets(sortWidgets(response));
    })
    .catch((err:any)=>{
      console.error(err);
      setAvailableWidgetsError(true);
    });

    fetchAllLayouts().then((response: any)=>{
      setLayoutList(response.layouts);
    })
    .catch((err: any)=>{
      console.error(err);
    });
  }, []);

  useEffect(() => {
    if(!data)
      return;

    if(data && window.localStorage.getItem('layoutUpdatedSuccessfully') === 'true')
    {
      window.localStorage.removeItem('layoutUpdatedSuccessfully');
      dispatch(openSnackBarV2(translatr('web.landingpage.main', 'LayoutUpdatedSuccessToastMessage'), 'success'));
    }

    if(data && window.localStorage.getItem('layoutRestoredToDefault') === 'true')
    {
      window.localStorage.removeItem('layoutRestoredToDefault');
      dispatch(openSnackBarV2(translatr('web.landingpage.main', 'LayoutRestoredToDefaultToastMessage'), 'success'));
    }

    if(data && data.redirect)
    { 
      navigate('/configure/homepage/' + data.redirect.key);
      return;
    }

    if(data)
    {
      setCurrentLayoutId(data.key);
    }
    if (data && data.owner_type === 'default_organization') {
      setOriginalLayout(JSON.parse(JSON.stringify(EmptyStateData)));
      setCurrentLayout(JSON.parse(JSON.stringify(EmptyStateData)));
    } else {
      setCachedLayoutsMap({...cachedLayoutsMap, [layoutId]: JSON.parse(JSON.stringify(data))});
      setOriginalLayout(JSON.parse(JSON.stringify(data)));
      setCurrentLayout(JSON.parse(JSON.stringify(data)));
    }
  }, [data]);

  const providerObject = {
    availableWidgets,
    originalAvailableWidgets,
    originalLayout,
    setOriginalLayout,
    cachedLayoutsMap,
    setCachedLayoutsMap,
    currentLayoutId,
    deleteRow,
    deleteBlock,
    moveWidgetLeft,
    moveWidgetRight,
    moveWidgetUp,
    moveWidgetDown,
    moveRowUp,
    moveRowDown,
    currentFocusedWidget,
    lastWidgetAdded,
    setLastWidgetAdded,
    openedFromHTMLElement,
    setOpenedFromHTMLElement,
    currentFocusedRow,
    theming,
    handleRowDistributionChange,
    updateRowWidth,
    setAvailableWidgets,
    setOriginalAvailableWidgets,
    availableWidgetsError,
    setAvailableWidgetsError,
    currentLayout,
    setCurrentLayout,
    blocks,
    layoutList,
    fetchBlocksData,
    data: data,
    layoutLoading: loading,
    layoutLoaded: loaded,
    layoutError: error,
    showPreviousSessionModal,
    setShowPreviousSessionModal,
    loadingFromCache,
    setLoadingFromCache
  };

  if(error !== false)
    {
      return (
        <EmptyState
          title={translatr('web.landingpage.main', 'LayoutFetchErrorMessage')}
          icon="icon-exclamation-circle"
          button={
            <Button color={'primary'} variant="ghost" onClick={() => navigate('/')}>
              {translatr('web.landingpage.main', 'GoToHomeText')}
            </Button>
          }
        />
      )
  }

  return (
    <AdminHomeConfigContext.Provider value={providerObject}>
      {children}
    </AdminHomeConfigContext.Provider>
  );
};

export const useAdminHomeContext = () => useContext(AdminHomeConfigContext);