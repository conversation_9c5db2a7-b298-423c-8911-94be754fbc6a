import { translatr, omp } from 'centralized-design-system/src/Translatr';

export const WidgetCategoryMap = {
    'Platform': translatr('web.landingpage.main', 'PlatformCategoryLabel') || 'Platform',
    'Learning': translatr('web.landingpage.main', 'LearningCategoryLabel') || 'Learning',
    'Talent Marketplace': translatr('web.landingpage.main', 'TalentMarketplaceCategoryLabel') || 'Talent Marketplace',
    'Performance': translatr('web.landingpage.main', 'PerformanceCategoryLabel') || 'Performance',
}

export const WidgetsTranslationsMap = {
    'announcements': {
        title: translatr('web.landingpage.main', 'AnnouncementsWidgetTitle') || 'Announcements',
        short_description: translatr('web.landingpage.main', 'AnnouncementsWidgetShortDesc') || 'Broadcast important updates and news.',
        long_description: translatr('web.landingpage.main', 'AnnouncementsWidgetLongDesc') || 'Create, schedule, and display announcements to communicate important company news and updates directly from your home page. Announcements can be easily managed to ensure timely and relevant communication with employees by navigating to More > Admin > Settings > Announcements.'
    },
    'complete-your-profile': {
        title: translatr('web.landingpage.main', 'CompleteProfileWidgetTitle') || 'Profile completion guide',
        short_description: translatr('web.landingpage.main', 'CompleteProfileWidgetShortDesc') || 'Guide new users to provide key inputs for personalization.',
        long_description: translatr('web.landingpage.main', 'CompleteProfileWidgetLongDesc') || 'Assists new users in completing their profiles by providing a step-by-step guide. It enhances user engagement by ensuring important profile elements are filled promptly to personalize their recommendations for learning and growth.'
    },
    'mentorship-recommendations': {
        title: translatr('web.landingpage.main', 'MentorshipWidgetTitle') || 'Recommended Mentorships',
        short_description: translatr('web.landingpage.main', 'MentorshipWidgetShortDesc') || 'Surface personalized mentorship opportunities.',
        long_description: translatr('web.landingpage.main', 'MentorshipWidgetLongDesc') || 'Displays tailored mentor recommendations, enabling users to easily connect with potential mentors.  While viewing this widget, users can see a snapshot of the mentor, click to view more details, or send a mentorship request with a direct call-to-action.'
    },
    'new-openings': {
        title: translatr('web.landingpage.main', 'NewOpeningsWidgetTitle') || 'Recommended Open Jobs and Projects',
        short_description: translatr('web.landingpage.main', 'NewOpeningsWidgetShortDesc') || 'Highlight suggested job and project openings.',
        long_description: translatr('web.landingpage.main', 'NewOpeningsWidgetLongDesc') || "Encourages discovery of open jobs and projects personalized based on the employee's experience, skills, and career preferences. Users can easily access details about each opportunity with a simple click, or explore a wider range of options through the 'see all' button."
    },
    'recent-updates': {
        title: translatr('web.landingpage.main', 'RecentUpdatesWidgetTitle') || 'User notifications',
        short_description: translatr('web.landingpage.main', 'RecentUpdatesWidgetShortDesc') || 'Central hub for all user alerts.',
        long_description: translatr('web.landingpage.main', 'RecentUpdatesWidgetLongDesc') || 'Provides direct visibility to user notifications, aggregating alerts and updates in one accessible location. It keeps users informed about new interactions, updates, or any relevant activities, enhancing communication efficiency.'
    },
    'text': {
        title: translatr('web.landingpage.main', 'TextWidgetTitle') || 'Custom text display',
        short_description: translatr('web.landingpage.main', 'TextWidgetShortDesc') || 'Add personalized text blocks to your home page.',
        long_description: translatr('web.landingpage.main', 'TextWidgetLongDesc') || 'This widget allows adminis to place customizable text blocks within the page. It offers flexibility in text content and style, making it suitable for conveying specific messages or information directly to users.'
    },
    'your-learning-feed': {
        title: translatr('web.landingpage.main', 'LearningFeedWidgetTitle') || 'Learning Feed',
        short_description: translatr('web.landingpage.main', 'LearningFeedWidgetShortDesc') || 'Display curated learning content feeds.',
        long_description: translatr('web.landingpage.main', 'LearningFeedWidgetLongDesc') || 'Promotes discovery of personalized and curated learning content. Users can seamlessly interact with the content by clicking into details, sharing, or bookmarking for later review. Administrators have the flexibility to configure up to four distinct feeds, customize labels, and arrange the display order through the Admin Panel via Settings > Configuration > Feed.'
    },
    'your-next-career-milestone': {
        title: omp('tm_tm_aspirational_roles') || 'Aspirational roles',
        short_description: translatr('web.landingpage.main', 'NextCareerMilestoneWidgetShortDescConfigurable', {tm_aspirational_role: omp('tm_tm_aspirational_role')}) || 'Guide employees to find aspirational career paths.',
        long_description: translatr('web.landingpage.main', 'NextCareerMilestoneWidgetLongDescConfigurable', {tm_aspirational_role: omp('tm_tm_aspirational_role')}) || 'This widget guides employees in identifying and selecting career roles that match their interests and aspirations. By choosing aspirational roles, users can quickly access detailed job information, learning resources, and related opportunities, facilitating strategic career planning within the company.'
    },
    'bookmark-contents': {
        title: translatr('web.landingpage.main', 'BookmarksWidgetTitle') || 'Learning bookmarks',
        short_description: translatr('web.landingpage.main', 'BookmarksWidgetShortDesc') || 'Quick access to bookmarked learning.',
        long_description: translatr('web.landingpage.main', 'BookmarksWidgetLongDesc') || 'Offers users a convenient way to access and manage their bookmarked learning content. It displays a streamlined view of all saved items, allowing for quick retrieval and continued learning.'
    },
    'upcoming-meetings-mfe': {
        title: translatr('web.landingpage.main', 'UpcomingMeetingMFEWidgetTitle') || 'Upcoming Check-In Meetings',
        short_description: translatr('web.landingpage.main', 'UpcomingMeetingMFEWidgetShortDesc') || 'Quick access to view and launch check-in meetings.',
        long_description: translatr('web.landingpage.main', 'UpcomingMeetingMFEWidgetLongDesc') || "This widget facilitates performance and coaching discussions by providing a quick view of upcoming check-in meetings and direct access to launch the meetings. Users can also click 'see all' to open the full check-ins experience in a new tab, ensuring they are well-prepared for each discussion."
    },
    'leaderboard': {
        title: translatr('web.landingpage.main', 'LeaderboardWidgetTitle') || 'Learning leaderboard',
        short_description: translatr('web.landingpage.main', 'LeaderboardWidgetShortDesc') || 'Encourage healthy competition in learning.',
        long_description: translatr('web.landingpage.main', 'LeaderboardWidgetLongDesc') || "A dynamic leaderboard that ranks users based on points earned through various learning activities. It motivates learners by providing a visual representation of their progress relative to their peers, promoting a culture of continuous learning and healthy competition."
    },
    'in-progress-contents': {
        title: translatr('web.landingpage.main', 'InProgressContentWidgetTitle') || 'Learning in progress',
        short_description: translatr('web.landingpage.main', 'InProgressContentWidgetShortDesc') || 'View in progress learning at a glance.',
        long_description: translatr('web.landingpage.main', 'InProgressContentWidgetLongDesc') || "This widget is designed to help users keep track of their current learning activities. It displays in-progress courses and training, providing easy access to continue learning and monitor progress."
    },
    'assigned-learnings': {
        title: translatr('web.landingpage.main', 'AssignedLearningWidgetTitle') || 'Learning assignments',
        short_description: translatr('web.landingpage.main', 'AssignedLearningWidgetShortDesc') || 'Help users stay on top of their assigned learning.',
        long_description: translatr('web.landingpage.main', 'AssignedLearningWidgetLongDesc') || "Displays a list of pending learning assignments for the user.  It helps users manage their learning schedule efficiently, ensuring they are up-to-date with required trainings and development activities."
    },
    'custom-html': {
        title: translatr('web.landingpage.main', 'CustomHTMLWidgetTitle') || 'Custom HTML widget',
        short_description: translatr('web.landingpage.main', 'CustomHTMLWidgetShortDesc') || 'Embed custom HTML to design your own widget.',
        long_description: translatr('web.landingpage.main', 'CustomHTMLWidgetLongDesc') || "This widget unlocks the ability to add HTML code to create your own custom widget. It provides flexibility to surface unique content, such as custom messages, media embeds, or specialized information. The widget is responsive and integrates seamlessly into the page, ensuring a consistent and professional appearance."
    },
    'custom-carousel':{
        title: translatr('web.landingpage.main', 'CustomCarouselWidgetTitle') || 'Custom carousel',
        short_description: translatr('web.landingpage.main', 'CustomCarouselWidgetShortDesc') || 'Build your own custom carousel.',
        long_description: translatr('web.landingpage.main', 'CustomCarouselWidgetLongDesc') || "The Custom Carousel Widget allows administrators to build a static carousel that showcases selected items to all users. Admins can choose from various objects, including Learning, People, Channels, and Groups, to curate a collection of important content. By searching and selecting specific values, admins can customize the content displayed in the carousel and set the order of items. This widget provides a powerful tool for highlighting critical resources, people, and information, ensuring they are easily accessible and visible to all employees. Note that the carousel must be populated with items before it can be saved."
    },
    'channels-contents': {
        title: translatr('web.landingpage.main', 'ChannelsWidgetTitle') || 'Channels',
        short_description: translatr('web.landingpage.main', 'ChannelsWidgetShortDesc') || 'Quick access to channels.',
        long_description: translatr('web.landingpage.main', 'ChannelsWidgetLongDesc') || 'Offers users a convenient way to access and manage their channels. It displays a streamlined view of new channels, allowing for quick retrieval and continued learning.'
    },
    'groups-contents': {
        title: translatr('web.landingpage.main', 'GroupsWidgetTitle') || 'Groups',
        short_description: translatr('web.landingpage.main', 'GroupsWidgetShortDesc') || 'Quick access to groups.',
        long_description: translatr('web.landingpage.main', 'GroupsWidgetLongDesc') || 'Offers users a convenient way to access and manage their groups. It displays a streamlined view of groups, allowing for quick retrieval and continued learning.'
    },
    'my-goals': {
        title: translatr('web.landingpage.main', 'MyGoalsWidget') || 'My goals',
        short_description: translatr('web.landingpage.main', 'MyGoalsWidgetShortDesc') || 'Quick access to goals.',
        long_description: translatr('web.landingpage.main', 'MyGoalsWidgetLongDesc') || 'Offers users a convenient way to access and their goals.'
    },
  };
  