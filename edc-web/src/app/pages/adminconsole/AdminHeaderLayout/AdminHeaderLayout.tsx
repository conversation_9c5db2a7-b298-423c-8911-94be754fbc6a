import React, {useState} from 'react';
import { func } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import Dropdown from 'centralized-design-system/src/Dropdown';
import { useNavigate } from 'react-router-dom';
import { useAdminHomeContext } from '../context/AdminHomeContextWrapper';
import _ from 'lodash';
import WorkInProgressModal from '../Modals/WorkInProgressModal';
import EmptyStateWarningModal from '../Modals/EmptyStateWarningModal';
import LayoutUpdatedModal from '../Modals/LayoutUpdatedModal';
import RestoreToDefaultModal from '../Modals/RestoreToDefaultModal';
import { saveLandingPageLayout } from 'edc-web-sdk/requests/blocks';
import { connect } from 'react-redux';
import Loading from 'centralized-design-system/src/Loading';
import { open_v2 as openSnackBar } from '../../../actions/snackBarActions';
import {isLayoutEmpty, HOME_PAGE_ROUTE} from '../../HomeV2/helpers';
import { LayoutList } from '../types';

interface AdminHeaderProps {
  toast: Function
}

const AdminHeaderLayout : React.FC<AdminHeaderProps> = ({ toast }) => {
  const navigate = useNavigate();
  const {currentLayout, originalLayout, layoutList, currentLayoutId, fetchBlocksData} = useAdminHomeContext();
  const [showModal, setShowModal] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [layoutUpdatedModal, setLayoutUpdatedModal] = useState(false);
  const [savingChanges, setSavingChanges] = useState(false);
  const [isLayoutDropdownOpen, setLayoutDropdown] = useState(false);
  const [navigateToLayout, setNavigateToLayout] = useState<string | number | null | undefined>(null);

  const handleCancel = () => {
    if(!_.isEqual(currentLayout, originalLayout))
    { 
      setShowModal(true);
    }
    else
    {
      navigate(HOME_PAGE_ROUTE);
    }
  }

  const handleSave = () => {
    if(isLayoutEmpty(currentLayout))
    {
      setShowWarningModal(true);
    }
    else
    {
      submitLayout();
    }
  };

  const submitLayout = () => {
    setSavingChanges(true);
    saveLandingPageLayout('landing', currentLayout, currentLayoutId)
    .then((response)=>{
      window.localStorage.removeItem(`admin_landing_page_layout_${currentLayoutId}`);
      if(response.is_layout_updated)
      {
        setSavingChanges(false);
        fetchBlocksData('landing', true, currentLayoutId);
        window.localStorage.setItem('layoutUpdatedSuccessfully', 'true');
      }
      else if(response.invalid_iframe_sources_present) 
      {
        setSavingChanges(false);
        toast(translatr('web.landingpage.main', 'InvalidIframeSourcesinCustomHTMLWidget', {
                  link_tag: `<a href="/admin/content/embargo" target='__blank' rel='noopener'>`,
                  close_link_tag: '</a>',
        }), 'error');
      }
      else
      {
        setLayoutUpdatedModal(true);
        setSavingChanges(false);
      }
    })
    .catch((err)=>{
      setSavingChanges(false);
      toast(err?.response?.body?.message, 'error');
      console.error("Error", err);
    })
  }

  const checkIfTextExceedsTwoLines = (element: HTMLElement) => {
    const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
    const maxHeight = lineHeight * 2;
    return element.scrollHeight > maxHeight;
  };

  const handleLayoutClick = (layoutId : string | number) => {
    setLayoutDropdown(false);
    if(!_.isEqual(currentLayout, originalLayout))
    {
      setNavigateToLayout(layoutId);
      setShowModal(true);
    }
    else
    {
      setTimeout(() => navigate(`/configure/homepage/${layoutId}`), 0);
    }
  }

  if(layoutList === null || layoutList === undefined)
  {
    return (
      <Loading center = {true}/>
    );
  }

  const isDropdownButtonDisabled  = layoutList?.length <= 1;
  const TypedDropdown = Dropdown as React.FC<any>;

  return (
    <>
      {savingChanges && 
        <div className='screen-overlay'>
          <Loading center = {true}/>
        </div>
      }
      <div className="admin__lp__header-layout">
        <div className="admin__lp__header-layout-content">
          <div className='layout-dropdown-label'>
            <TypedDropdown
              label={layoutList.find((layout) => layout.key.toString() === currentLayoutId)?.label}
              customButtonIcon="icon-angle-down-arrow"
              ariaLabel={translatr('web.landingpage.main', 'SelectLayoutHeading')}
              openDropdown={isLayoutDropdownOpen}
              setOpenDropdown={setLayoutDropdown}
              buttonClass={'layout-dropdown-label-with-icon' + (isDropdownButtonDisabled ? ' dropdown-button-disabled' : '')}
              isButtonDisabled={isDropdownButtonDisabled}
              dataTestID="layout-dropdown-button"
            >
              <ul
                role="menu"
                aria-label={translatr('web.landingpage.main', 'SelectLayoutHeading')}
                className='layouts-dropdown-list'
              >
                <li className='layout-dropdown-presentation-item' role="presentation">
                  <span role="heading">
                    {translatr('web.landingpage.main', 'SelectLayoutHeading')?.toString().toUpperCase()}
                  </span>
                </li>
                {layoutList.map((layout : LayoutList) => (
                  <li role="none" className={`layout-dropdown-item ${layout.key.toString() === currentLayoutId ? 'selected' : ''}`} key={layout.key}>
                    <button
                      role="menuitem"
                      onClick={() => handleLayoutClick(layout.key)}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter' || event.key === ' ') {
                          setTimeout(() => {
                            (
                              document.querySelector(
                                `.layout-dropdown-label-with-icon`
                              ) as HTMLElement
                            )?.focus();
                          }, 100);
                        }
                      }}
                      aria-label={layout.label}
                      ref={(el) => {
                        if (el && checkIfTextExceedsTwoLines(el)) {
                          el.setAttribute('title', layout.label);
                        }
                      }}
                    >
                      {layout.label}
                    </button>
                  </li>
                ))}
              </ul>
            </TypedDropdown>
          </div>
          <div className="admin__lp__header-layout-buttons">
              <button className="ed-btn ed-btn-neutral" onClick={handleCancel}>
                {translatr('web.common.main', 'Leave')}
              </button>
              {currentLayout && currentLayout.owner_type === 'current_organization' &&
                <button className="ed-btn ed-btn-neutral" onClick={() => setShowRestoreModal(true)}>
                  {translatr('web.landingpage.main', 'RestoreToDefaultLayoutButtonTitle') || 'Restore'}
                </button>
              }
              <button className="ed-btn ed-btn-primary" onClick={handleSave}>
                {translatr('web.common.main', 'Save')}
              </button>
          </div>
        </div>
      </div>
      {showModal && <WorkInProgressModal setShow={setShowModal} navigateToLayout={navigateToLayout} setNavigateToLayout={setNavigateToLayout}/>}
      {showWarningModal && <EmptyStateWarningModal setShow={setShowWarningModal} submitLayout={submitLayout}/>}
      {layoutUpdatedModal && <LayoutUpdatedModal />}
      {showRestoreModal && <RestoreToDefaultModal setShow={setShowRestoreModal} toast={toast}/>}
    </>
  );
};

AdminHeaderLayout.propTypes = {
  toast: func,
};

export default connect(null, dispatch => ({
  toast: (message : string, type : string) => dispatch(openSnackBar(message, type))
}))(AdminHeaderLayout);
