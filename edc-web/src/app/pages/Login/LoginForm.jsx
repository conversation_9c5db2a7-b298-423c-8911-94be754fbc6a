import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import { tr } from 'edc-web-sdk/helpers/translations';
import { translatr } from 'centralized-design-system/src/Translatr';
import { useLocalStorageListener } from '@utils/hooks';
import TermsAndCondition from './TermsAndCondition';
import { newRequestLogin } from '../../../app/actions/currentUserActions';
import updateAppTranslations from '../../../app/utils/updateAppTranslations';
import { JSEncrypt } from 'jsencrypt';
import SsoSection from './SsoSection';
import { JS_PUBLIC_KEY } from 'edc-web-sdk/config/envConstants';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import LD from '../../../app/containers/LDStore';
import { LOGIN_WITH } from '../../../app/constants/localStorageConstants';
import { constructSubOrgData } from '@utils/subOrg';
import { USER_INFO_UPDATED } from '../../../app/constants/actionTypes';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
function LoginForm({
  acceptancePromptEnabledForLogin,
  promptChecked,
  isOrgHasEmailLogin,
  isShowSignUp,
  dispatch,
  teamConfig,
  isMultiOrg,
  theme,
  samlData
}) {
  const navigate = useNavigate();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [tcChecked, togglePromptChecked] = useState(promptChecked);
  const [loginErrorMessage, setLoginErrorMessage] = useState('');
  const [pending, setPending] = useState(false);
  const [errorFieldName, setErrorFieldName] = useState('');

  const isPlare = theme === ThemeId.PLARE;

  const isResetPasswordDisabled =
    window.__edOrgData.configs.find(item => item.name === 'reset_password_disabled')?.value ||
    false;

  useLocalStorageListener(['translatr_web_lastRequested', 'translatr_cds_lastRequested']);

  const goToSignUp = () => {
    navigate('/user/signup');
  };

  const changeEmail = emailText => {
    setEmail(emailText);
    clearErrorMessage();
  };

  const changePassword = passwordText => {
    setPassword(passwordText);
    clearErrorMessage();
  };

  const clearErrorMessage = () => {
    if (loginErrorMessage) {
      setLoginErrorMessage('');
    }
    if (errorFieldName) {
      setErrorFieldName('');
    }
  };

  useEffect(() => {
    if (loginErrorMessage) {
      document.getElementById('error-msg').focus();
    }
  }, [loginErrorMessage]);

  const submitLogin = e => {
    e.preventDefault();
    if (isPlare) {
      const requiredFields = {
        email,
        password
      };

      for (const [field, value] of Object.entries(requiredFields)) {
        if (!value) {
          setErrorFieldName(field);
          return;
        }
      }
    }

    let userPassword = password;
    window.localStorage.setItem(LOGIN_WITH, 'email');
    if (JS_PUBLIC_KEY) {
      let encrypt = new JSEncrypt();
      encrypt.setPublicKey(JS_PUBLIC_KEY);
      userPassword = encrypt.encrypt(password);
    }
    setPending(true);
    dispatch(newRequestLogin(email, userPassword, tcChecked))
      .then(res => {
        if (res.statusCode === 429) {
          setPending(false);
          setLoginErrorMessage(translatr('web.login.main', 'LoginAttemptsExceeded'));
          return null;
        }
        if (res.error) {
          if (res.error !== 'Your account is locked.') {
            setLoginErrorMessage(tr(res.error));
          } else {
            setLoginErrorMessage(
              `${res.error} ${translatr('web.login.main', 'AccountLockedContact')}`
            );
          }
          setPending(false);
        } else {
          landOnHomePage(res);
        }
      })
      .catch(error => {
        setPending(false);
        console.error(`Error in LoginSection.submitLogin.func ${error}`);
      });
  };

  const landOnHomePage = user => {
    // replace circle info in orgInfo
    if (isMultiOrg && user.circle_info) {
      constructSubOrgData(window.__edOrgData, user, dispatch);
      dispatch({ type: USER_INFO_UPDATED });
    }
    // end of circle info replacement
    updateAppTranslations(dispatch, user, teamConfig, navigate, samlData);
  };

  const toggleTcValue = () => {
    togglePromptChecked(!tcChecked);
  };

  const goToMicroOrgSso = () => {
    window.localStorage.setItem(LOGIN_WITH, 'name');
    navigate('/user/micro_org_login');
  };

  const ShowEmptyFieldError = () => (
    <div role="alert" aria-live="assertive" className="error-text font-size-l mt-4">
      {translatr('web.login.main', 'PleaseFillThisField')}
    </div>
  );

  return (
    <>
      <div className="login--section">
        {isOrgHasEmailLogin && (
          <form onSubmit={submitLogin} className="mb-24">
            <div className="login--field-container mb-16">
              <TextField
                title={translatr('web.login.main', 'Email')}
                placeholder={translatr('web.login.main', 'Email')}
                setValue={changeEmail}
                defaultValue={email}
                required={!isPlare}
                showAsterisk={isPlare}
                name="email"
                type="email"
                autocomplete="off"
                id="email"
                isTranslated
              />
              {errorFieldName === 'email' && ShowEmptyFieldError()}
            </div>
            <TextField
              title={translatr('web.login.main', 'Password')}
              placeholder={translatr('web.login.main', 'Password')}
              setValue={changePassword}
              defaultValue={password}
              showAsterisk={isPlare}
              required={!isPlare}
              name={translatr('web.login.main', 'Password')}
              type="password"
              className="mb-4"
              autocomplete="off"
              id="password"
              isTranslated
            />
            <div className="login--error-block justflex mb-16">
              {errorFieldName === 'password' && ShowEmptyFieldError()}
              {loginErrorMessage && (
                <div
                  role="alert"
                  aria-live="assertive" //It enables screen readers to read dynamic content updates in the "alert" element, improving accessibility without using tabIndex.
                  id="error-msg"
                  className="error-text font-size-l justflex flex-1"
                >
                  {loginErrorMessage}
                </div>
              )}
              {!isResetPasswordDisabled && (
                <div className="justflex flex-1 login--fg-block">
                  <a className="font-size-l pointer fg-text" href="/forgot_password">
                    {translatr('web.login.main', 'ForgotYourPassword')}
                  </a>
                </div>
              )}
            </div>
            <button
              className="ed-btn ed-btn-primary width-100 mb-8"
              type="submit"
              disabled={pending}
            >
              {pending
                ? translatr('web.login.main', 'PleaseWait')
                : translatr('web.login.main', 'Login')}
            </button>
            <TermsAndCondition
              promptChecked={tcChecked}
              handlePromptChange={toggleTcValue}
              acceptancePromptEnabledForLogin={acceptancePromptEnabledForLogin}
            />
            {LD.microOrgSSOEnabled() && (
              <button
                className="ed-btn ed-btn-neutral width-100 mb-16 ml-0"
                type="button"
                onClick={goToMicroOrgSso}
              >
                {translatr('web.login.main', 'LoginWithEnterpriseSso')}
              </button>
            )}
          </form>
        )}
      </div>
      <SsoSection />
      <div className="login--section signup-button">
        {isShowSignUp && !LD.preRegistration() && (
          <>
            <div className="supporting-text mb-16">
              {translatr(
                'web.login.main',
                'IfYouDontHaveAnAccountAlreadyClickTheButtonBelowToCreateOne'
              )}
            </div>

            <button
              className="ed-btn ed-btn-neutral width-100 mb-8"
              type="button"
              onClick={goToSignUp}
            >
              {translatr('web.login.main', 'SignUp')}
            </button>
          </>
        )}
      </div>
    </>
  );
}

LoginForm.propTypes = {
  loinButtonStyle: PropTypes.object,
  forgotPasswordColor: PropTypes.object,
  acceptancePromptEnabledForLogin: PropTypes.string,
  promptChecked: PropTypes.bool,
  microOrgSsoButtonStyle: PropTypes.object,
  signUpButtonStyle: PropTypes.object,
  isOrgHasEmailLogin: PropTypes.bool,
  isShowSignUp: PropTypes.bool,
  dispatch: PropTypes.func,
  teamConfig: PropTypes.object,
  isMultiOrg: PropTypes.bool,
  theme: PropTypes.string,
  samlData: PropTypes.object
};

const mapStateToProps = ({ team, theme, currentUser }) => {
  const teamConfig = team?.get('config');
  const isOrgHasEmailLogin = team?.get('hasEmailLogin');
  const acceptancePromptEnabledForLogin = teamConfig?.acceptancePromptEnabledForLogin;
  const promptChecked = !acceptancePromptEnabledForLogin;
  const isShowSignUp = teamConfig?.enable_self_signup && teamConfig?.AllowedEmailDomains !== 'none';
  const langs = window.translations || [];
  const isMultiOrg = teamConfig['multi-org'];
  const samlData = currentUser.toJS()?.samlRequestPayload;

  return {
    acceptancePromptEnabledForLogin,
    promptChecked,
    isOrgHasEmailLogin,
    isShowSignUp,
    teamConfig,
    langs, // Added to re-render after language file loads
    isMultiOrg,
    theme: theme.get('themeId') || '',
    samlData
  };
};

export default connect(mapStateToProps)(LoginForm);
