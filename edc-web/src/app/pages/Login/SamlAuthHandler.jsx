import React, { useEffect } from 'react';
import { useSearchParams, useNavigate, useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { SET_SAML_REQUEST_DATA } from '../../constants/actionTypes';
import withRouter from '@hocs/withRouter.js';
import { createSamlResponse } from 'edc-web-sdk/requests/saml.v2';
import Spinner from '@components/common/spinner';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { open_v2 as openSnackBar } from '../../actions/snackBarActions';

const SamlAuthHandler = ({ currentUserId, dispatch }) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { uuid } = useParams();

  const submitSamlResponse = (samlResponse, relayState, destination) => {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = destination;
    form.target = '_self';
    const samlInput = document.createElement('input');
    samlInput.type = 'hidden';
    samlInput.name = 'SAMLResponse';
    samlInput.value = samlResponse;

    const relayStateInput = document.createElement('input');
    relayStateInput.type = 'hidden';
    relayStateInput.name = 'RelayState';
    relayStateInput.value = relayState;

    form.appendChild(samlInput);
    form.appendChild(relayStateInput);
    document.body.appendChild(form);

    form.addEventListener('submit', () => {
      window.location.href = destination;
    });

    form.submit();
  };
  const handleSamlAuth = async () => {
    const samlRequest = searchParams.get('SAMLRequest');
    const relayState = searchParams.get('RelayState');

    if (!samlRequest) {
      console.error('SAML authentication failed: No SAMLRequest found in URL');
      dispatch(
        openSnackBar(translatr('web.common.main', 'SomethingWentWrongPleaseTryAgainLater'), 'error')
      );
      return;
    }

    if (!currentUserId) {
      dispatch({
        type: SET_SAML_REQUEST_DATA,
        payload: {
          samlRequest,
          relayState,
          uuid
        }
      });
      navigate('/user/login');
      return;
    }

    try {
      const response = await createSamlResponse(uuid, {
        SAMLRequest: samlRequest,
        RelayState: relayState
      });
      const data = response;
      if (data.saml_response) {
        submitSamlResponse(data.saml_response, data.relay_state, data.destination);
      }
    } catch (error) {
      console.error('SAML authentication failed:', error);
      dispatch(
        openSnackBar(translatr('web.common.main', 'SomethingWentWrongPleaseTryAgainLater'), 'error')
      );
    }
  };
  useEffect(() => {
    handleSamlAuth();
  }, []);

  return (
    <div className="centered-spinner">
      <Spinner />
    </div>
  );
};

SamlAuthHandler.propTypes = {
  currentUserId: PropTypes.string,
  dispatch: PropTypes.func.isRequired
};

const mapStateToProps = ({currentUser, samlRequestPayload}) => {
  return {
    currentUserId: currentUser.get('id'),
    samlRequestPayload: samlRequestPayload?.toJS()
  };
};

export default withRouter(connect(mapStateToProps)(SamlAuthHandler));
