@import '~centralized-design-system/src/Styles/_variables.scss';

.team-matrix {
  .matrix-table {
    font-size: var(--ed-font-size-base);
    .ed-table-wrapper {
      tbody tr:hover {
        & th:nth-child(2),
        td:nth-child(2) {
          background-color: var(--ed-table-row-hover-bg-color);
        }
        & th:nth-child(1),
        td:nth-child(1) {
          background-color: var(--ed-table-row-hover-bg-color);
        }
      }
      & th > div {
        display: flex;
        justify-content: center;
        border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
        border-top: var(--ed-border-size-sm) solid var(--ed-gray-2);
        height: 58px;
      }
      & th:first-child {
        z-index: 5 !important;
        min-width: 10vw;
      }

      & th:first-child > div,
      td:first-child > div {
        display: flex;
        justify-content: left;
        border-right-style: none;
        overflow: hidden;
      }
      & td > div {
        display: flex;
        justify-content: center;
        padding-top: 40px;
        padding-bottom: 40px;
        border-bottom-style: none;
      }

      & td div {
        min-height: auto;
      }

      & td:first-child {
        z-index: 5 !important;
        background-color: var(--ed-body-bg-color);
        min-width: 10vw;
        max-width: 237px;
      }
      & th:nth-child(2) > div,
      td:nth-child(2) > div {
        max-width: 20vw;
        display: flex;
        justify-content: center;
        border-right: 1px solid var(--ed-gray-2);
      }
      & th:nth-child(2),
      td:nth-child(2) {
        background-color: var(--ed-body-bg-color);
        position: sticky;
        left: 15rem;
        width: 10vw;
        z-index: 5 !important;
      }

      & th:nth-child(n + 3),
      td:nth-child(n + 3) {
        min-width: 9vw;
        z-index: 0 !important;
      }
    }
    .error-message {
      text-align: center;
    }
  }
  .team-matrix-table {
    font-size: var(--ed-font-size-base);
    .ed-table-wrapper {
      & th > div {
        display: flex;
        justify-content: center;
        border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
        border-top: var(--ed-border-size-sm) solid var(--ed-gray-2);
        height: 58px;
      }
      & th:first-child {
        z-index: 5 !important;
        min-width: 10vw;
        max-width: 237px;
      }

      & th:first-child > div,
      td:first-child > div {
        display: flex;
        justify-content: left;
        border-right-style: none;
        overflow: hidden;
      }
      & td > div {
        display: flex;
        justify-content: center;
        padding-top: 40px;
        padding-bottom: 40px;
        border-bottom-style: none;
      }

      & td div {
        min-height: auto;
      }

      & td:first-child {
        z-index: 5 !important;
        min-width: 10vw;
      }
      & th:nth-child(1) > div,
      td:nth-child(1) > div {
        border-right: 1px solid var(--ed-gray-2);
      }
      & th:nth-child(1),
      td:nth-child(1) {
        background-color: var(--ed-body-bg-color);
        position: sticky;
        z-index: 5 !important;
      }

      & th:nth-child(n + 2),
      td:nth-child(n + 2) {
        min-width: 9vw;
        z-index: 0 !important;
      }
    }
    .error-message {
      text-align: center;
    }
  }
  &.with-score {
    .ed-table-wrapper {
      & th:nth-child(1),
      td:nth-child(1) {
        min-width: 10vw !important;
      }
    }
  }
  .user-info {
    display: flex;
    flex-direction: row;
    justify-content: left;
    width: 100%;

    .user-avatar {
      .ed-avatar {
        width: 50px;
        height: 50px;
      }
    }

    .user-data {
      font-size: var(--ed-font-size-sm);
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      align-items: start;
      justify-content: center;
      width: 80%;
      & :first-child {
        font-weight: 900;
      }

      & :nth-child(1) {
        overflow-y: hidden;
      }

      & .ed-tooltip {
        justify-content: left;
        width: 99%; // force ellipsis to show if necessary
        height: auto;
        span {
          cursor: pointer;
          font-weight: unset;
        }
      }
    }
  }
  .skillscore-container {
    font-size: var(--ed-font-size-sm);

    display: flex;
    flex-direction: column;
  }

  .pagination {
    display: flex !important;
    justify-content: end;
    align-items: center;
  }
}

.team-skill-matrix-tabs-wrapper .tab-wrapper {
  .tab-bar.block {
    box-shadow: unset;
    border-radius: unset;
    border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
    .tab > button {
      text-transform: uppercase;
    }
  }
}
