import React, { useState, useEffect } from 'react';
import { string } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import Table from 'centralized-design-system/src/Table';
import Loading from 'centralized-design-system/src/Loading';
import Pagination from 'centralized-design-system/src/Pagination';
import { getUserSkills } from 'edc-web-sdk/requests/managerDashboard.v2';
import Tooltip from 'centralized-design-system/src/Tooltip';
import '../individual.scss';
import { truncateText } from '@utils/utils';

const IndividualProficiencySkillTable = ({ activeUser }) => {
  const [activeUserSkills, setActiveUserSkills] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState();
  const [currentPage, setCurrentPage] = useState(0);
  const OFFSET_AMOUNT = 10;

  const proficiencyLevels = window?.__edOrgData?.proficiencyLevels || [];
  const selectedLanguage =
    window?.__ED__?.profile?.language || window?.localStorage.getItem('selectedLanguage');

  const defaultHeaders = [{ skill_name: translatr('web.manager-dashboard-v2.main', 'Skill') }];

  let Headers = [...defaultHeaders];

  const getTranslatedHeaderLabel = (selectedLang, level) => {
    let label = level.name;
    level.languages.forEach(lang => {
      if (lang.code === selectedLang) {
        label = lang.name;
      }
    });
    return label;
  };

  proficiencyLevels?.forEach(level => {
    Headers = [...Headers, { [level.name]: getTranslatedHeaderLabel(selectedLanguage, level) }];
  });

  useEffect(() => {
    getActiveUserSkills(activeUser);
  }, [activeUser]);

  async function getActiveUserSkills(activeUserId, page = 1) {
    setLoading(true);
    let pageToRequest = page - 1;
    let q = { user_id: activeUserId, offset: pageToRequest * OFFSET_AMOUNT, limit: 10 };
    let response = await getUserSkills(q).catch(err =>
      console.error('Overview init.getActiveUserSkills.func', err)
    );

    if (response) {
      setActiveUserSkills(response.skills);
      setTotalCount(response.total_count);
      setCurrentPage(page);
      setLoading(false);
    }
  }

  const paginate = resp => {
    let activePage = currentPage;
    if (resp.event === 'next') {
      getActiveUserSkills(activeUser, activePage + 1);
    } else if (resp.event === 'prev') {
      getActiveUserSkills(activeUser, activePage - 1);
    } else {
      if (Number.isInteger(resp.event)) {
        getActiveUserSkills(activeUser, resp.event);
      }
    }
  };

  const isActiveLevel = (proficiency_level, Levelkey) => {
    let bool = false;
    if (proficiency_level?.level === Levelkey) {
      bool = true;
    }
    return bool;
  };

  const TableHeaders = Headers.map((header, i) => {
    const key = Object.keys(header)[0];
    return {
      label: header[key],
      id: `${key}-${i}`,
      align: key === 'skill_name' ? 'text-left' : 'text-center',
      sortable: false
      // onClick: getSortedData
    };
  });

  const userSkills = !!activeUserSkills?.length ? activeUserSkills : [];

  const TableData = userSkills.map((item, i) => {
    const { skill_name, proficiency_level } = item;
    const rows = [];
    let skillId;
    Headers.forEach((header, j) => {
      const key = Object.keys(header)[0];
      if (j == 0) {
        skillId = `${key}-${i}-${j}`;
        rows.push({
          id: skillId,
          children: (
            <Tooltip isHtmlIncluded message={skill_name} hide={skill_name?.length < 32} pos="right">
              {skill_name?.length > 32 ? truncateText(unescape(skill_name), 32, '...') : skill_name}
            </Tooltip>
          ),
          align: 'text-left'
        });
      } else {
        rows.push({
          id: `${key}-${i}-${j}`,
          children: isActiveLevel(proficiency_level, key) ? (
            <i className="icon-oval-fill active-level" />
          ) : (
            <i className="icon-oval" />
          ),
          align: 'text-center',
          ariaLabelledby: skillId
        });
      }
    });

    return rows;
  });

  return userSkills?.length ? (
    <>
      <div className="block mt-16">
        {loading && (
          <div className="table-loader">
            <Loading />
          </div>
        )}
        <Table
          className={`none-vertical-border md-v3-table ${loading ? 'hide-rows' : ''}`}
          headers={TableHeaders}
          rows={TableData}
        />
        {totalCount > 10 && (
          <div className="mt-16">
            <Pagination
              postPerPage={10}
              totalPosts={totalCount}
              paginate={paginate}
              activePage={currentPage}
              iconType={true}
            />
          </div>
        )}
      </div>
    </>
  ) : (
    <></>
  );
};

export default IndividualProficiencySkillTable;

IndividualProficiencySkillTable.propTypes = {
  activeUser: string
};
