import { useState, useEffect, useRef, useMemo } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  downloadIndividualReport,
  getUserLearning
} from 'edc-web-sdk/requests/managerDashboard.v2';
import CheckboxGroup from 'centralized-design-system/src/CheckboxGroup';
import DatePicker from 'centralized-design-system/src/DatePickers';
import Dropdown from 'centralized-design-system/src/Dropdown';
import { Button } from 'centralized-design-system/src/Buttons';
import Table from 'centralized-design-system/src/Table';
import { SearchInput } from 'centralized-design-system/src/Inputs';
import Pagination from 'centralized-design-system/src/Pagination';
import Loading from 'centralized-design-system/src/Loading';
import Tooltip from 'centralized-design-system/src/Tooltip';
import '../individual.scss';
import { debounce } from 'lodash';
import unescape from 'lodash/unescape';
import moment from 'moment';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { truncateText } from '@utils/utils';
import { processIndividualAssignments } from '../../manager_dashboard/tableUtil';
import { getMMDDYYYStr } from '../../manager_dashboard/utils';
import { DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT, MONTH_DAY_YEAR_FORMAT } from '@utils/constants';
const ASSIGNOR_OFFSET_AMOUNT = 8;
const ALL_TIME = translatr('web.common.main', 'AllTime');

const LearningTable = ({ activeUser, sendReminderModalHandler }) => {
  const [assignedDate, _setAssignedDate] = useState({});
  const [assignments, setAssignments] = useState();
  const [assignmentStatus, setAssignmentStatus] = useState([]);
  const [assignors, setAssignors] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const [dueDate, _setDueDate] = useState({});
  const [enableAssignorsApply, setEnableAssignorsApply] = useState(false);
  const [enableStatusApply, setEnableStatusApply] = useState(false);
  const [loading, setLoading] = useState(true);
  const [openAssignorsDropdown, setOpenAssignorsDropdown] = useState(false);
  const [openStatusDropdown, setOpenStatusDropdown] = useState(false);
  const [placeholderAssignedDate, setPlaceholderAssignedDate] = useState(ALL_TIME);
  const [placeholderDueDate, setPlaceholderDueDate] = useState(ALL_TIME);
  const [prevAssignors, setPrevAssignors] = useState({});
  const [prevStatus, setPrevStatus] = useState({});
  const [showClearDueDate, setShowClearDueDate] = useState(false);
  const [showClearAssignedDate, setShowClearAssignedDate] = useState(false);
  const [showViewMore, setShowViewMore] = useState(false);
  const [sortDir, setSortDir] = useState('desc');
  const [sortKey, setSortKey] = useState('card_title');
  const [tableData, setTableData] = useState([]);
  const [totalCount, setTotalCount] = useState();
  const [totalCompletedCount, setTotalCompletedCCount] = useState();
  const [shouldUpdate, setShouldUpdate] = useState(false);
  const OFFSET_AMOUNT = 10;
  const searchRef = useRef();
  const assignmentStatusRef = useRef();
  const assignorsListRef = useRef();
  const assignorsDataRef = useRef();
  const dueDateRef = useRef(dueDate);
  const assignedDateRef = useRef(assignedDate);

  const setAssignedDate = data => {
    assignedDateRef.current = { ...data };
    _setAssignedDate(data);
  };
  const setDueDate = data => {
    dueDateRef.current = { ...data };
    _setDueDate(data);
  };

  const SortMap = {
    card_title: 'card_title',
    assigned_by: 'assigned_by',
    assigned_on: 'assigned_date',
    due_at: 'due_at',
    completion_status: 'status'
  };

  const SortKeyMap = {
    card_title: 'card_title',
    assigned_by: 'assigned_by',
    assigned_date: 'assigned_on',
    due_at: 'due_at',
    status: 'completion_status'
  };

  const Headers = {
    card_title: translatr('web.manager-dashboard-v2.main', 'ContentName'),
    assigned_by: translatr('web.common.main', 'AssignedBy'),
    assigned_on: translatr('web.manager-dashboard-v2.main', 'AssignedOn'),
    due_at: translatr('web.manager-dashboard-v2.main', 'DueDate'),
    completion_status: translatr('web.common.main', 'StatusSkill'),
    send_reminder: translatr('web.manager-dashboard-v2.main', 'SendReminder')
  };
  const StatusDropdownItems = {
    completed: translatr('web.manager-dashboard-v2.main', 'Completed'),
    started: translatr('web.manager-dashboard-v2.main', 'InProgress'),
    assigned: translatr('web.manager-dashboard-v2.main', 'NotStarted')
  };
  const TableHeaders = Object.keys(Headers).map(header => {
    if (['completion_status', 'send_reminder'].includes(header)) {
      return {
        label: Headers[header],
        id: header,
        align: 'text-left',
        sortable: false,
        hidden: header === 'send_reminder'
      };
    }
    return {
      label: Headers[header],
      id: header,
      align: 'text-left',
      sortable: true,
      onClick: getSortedData
    };
  });

  function getSortedData(header) {
    const sd = header.id !== SortKeyMap[sortKey] ? 'desc' : sortDir === 'asc' ? 'desc' : 'asc';
    setSortDir(sd);
    setSortKey(SortMap[header.id]);
    getAssignments(1, activeUser, SortMap[header.id], sd);
  }

  function doSearch(query) {
    searchRef.current = query;
    getAssignments(1, activeUser);
  }

  const debouncedSearch = useMemo(() => debounce(doSearch, 500), []);

  const getPayload = (page, activeUserId = activeUser, sort_field, sort_direction) => {
    let payload = {
      user_id: activeUserId,
      sort_field,
      sort_direction
    };

    const theDueDate = dueDateRef.current || dueDate;
    const theAssignedDate = assignedDateRef.current || assignedDate;

    if (theDueDate?.fromDate && theDueDate?.toDate) {
      payload['due_at[]'] = [getMMDDYYYStr(theDueDate.fromDate), getMMDDYYYStr(theDueDate.toDate)];
    }

    if (theAssignedDate?.fromDate && theAssignedDate?.toDate) {
      payload['assigned_date[]'] = [
        getMMDDYYYStr(theAssignedDate.fromDate),
        getMMDDYYYStr(theAssignedDate.toDate)
      ];
    }

    if (page) {
      payload.offset = (page - 1) * OFFSET_AMOUNT;
      payload.limit = OFFSET_AMOUNT;
    }

    if (searchRef.current) {
      payload.q = searchRef.current;
    }

    if (assignmentStatusRef.current) {
      const theStatus = [...(assignmentStatusRef.current || [])]
        .filter(item => item.checked)
        .map(item => item.value);
      payload['status[]'] = theStatus;
    }

    if (assignorsListRef.current) {
      const theIds = [...(assignorsListRef.current || [])]
        .filter(item => item.checked)
        .map(item => item.value);
      if (theIds.length > 0) {
        payload['assignor_id[]'] = theIds;
      }
    }
    return payload;
  };

  async function getAssignments(
    page = 1,
    activeUserId = activeUser,
    sort_field = sortKey,
    sort_direction = sortDir
  ) {
    setLoading(true);
    const payload = getPayload(page, activeUserId, sort_field, sort_direction);
    let userAssignments = await getUserLearning(payload).catch(err => {
      const theAssignors = { data: [], totalCount: 0, page: 1 };
      assignorsListRef.current = [];
      assignorsDataRef.current = theAssignors;
      setAssignors(theAssignors);
      assignmentStatusRef.current = [];
      setAssignmentStatus([]);
      setShowViewMore(false);
      setTotalCount(0);
      setTotalCompletedCCount(0);
      setShouldUpdate(false);
      setLoading(false);
      console.error('Individual overview init.getUserLearningMatrix.func', err);
    });

    if (userAssignments) {
      initStatus(userAssignments.data);
      const countByAssignor = initAssignorsOpts(userAssignments.count_by_assignor || []);
      const theAssignors = {
        data: countByAssignor,
        totalCount: countByAssignor.length,
        page: 1
      };
      assignorsListRef.current = [...countByAssignor].slice(0, ASSIGNOR_OFFSET_AMOUNT);
      assignorsDataRef.current = theAssignors;
      setAssignors(theAssignors);
      countByAssignor?.length > ASSIGNOR_OFFSET_AMOUNT && setShowViewMore(true);

      setAssignments(processIndividualAssignments(userAssignments.data || []));
      setTotalCount(userAssignments.total_count);
      setTotalCompletedCCount(userAssignments.completed_count);
      setCurrentPage(page);
      setEnableAssignorsApply(false);
      setEnableStatusApply(false);
      setLoading(false);
    }
  }

  function renderStatus(obj) {
    if (obj['completion_percentage'] === 100 || obj['state'] === 'completed')
      return (
        <div className="double-cell-wrapper">
          <div className="ed-primary-color half-cell">
            {translatr('web.manager-dashboard-v2.main', 'Completed')}
          </div>
          <div className="ed-supporting-text ed-text-size-14 half-cell">{`${translatr(
            'web.manager-dashboard-v2.main',
            'On'
          )} ${moment(obj.completed_on, MONTH_DAY_YEAR_FORMAT).format(
            DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
          )}`}</div>
        </div>
      );

    if (obj['completion_percentage'] < 100 && obj['completion_percentage'] > 0) {
      return <div className="ed-text-color">{`${obj['completion_percentage']}%`}</div>;
    }

    if (obj['state'] === 'started') {
      return (
        <div className="ed-text-color">{translatr('web.manager-dashboard-v2.main', 'Started')}</div>
      );
    }

    return (
      <div className="ed-supporting-text">
        {translatr('web.manager-dashboard-v2.main', 'NotStarted')}
      </div>
    );
  }

  function renderDueAt(obj) {
    const nowDate = moment();
    const theDueDate = !!obj['due_at'] ? moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT) : null;
    const diff = !!theDueDate ? theDueDate.diff(nowDate, 'days') : null;

    if (Number.isInteger(diff)) {
      if (diff <= 14 && diff >= 0 && obj['completion_percentage'] != 100) {
        return (
          <span className="ed-text-color-warning">
            <span>
              {moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT).format(
                DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
              )}
            </span>
          </span>
        );
      }
      if (diff < 0 && obj['completion_percentage'] != 100) {
        return (
          <span className="ed-text-color-error">
            <span>
              {moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT).format(
                DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
              )}
            </span>
          </span>
        );
      }
      return (
        <span className="ed-text-color">
          <span>
            {moment(obj['due_at'], MONTH_DAY_YEAR_FORMAT).format(
              DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
            )}
          </span>
        </span>
      );
    }
    return <div className="ed-supporting-text ed-text-size-14"></div>;
  }

  function rowChildrenSelector(obj, key) {
    switch (key) {
      case 'card_title':
        return (
          <Tooltip isHtmlIncluded message={obj[key]} hide={obj[key]?.length < 32} pos="right">
            <div
              dangerouslySetInnerHTML={{
                __html: safeRender(
                  obj[key]?.length > 32 ? truncateText(unescape(obj[key]), 32, '...') : obj[key]
                )
              }}
            />
          </Tooltip>
        );
      case 'completion_status':
        return renderStatus(obj);
      case 'due_at':
        return renderDueAt(obj);
      case 'send_reminder':
        return obj.last_reminder_date &&
          obj['completion_percentage'] != 100 &&
          obj['state'] != 'completed' ? (
          <div className="double-cell-wrapper">
            <Button
              color="primary"
              variant="borderless"
              padding="xsmall"
              onClick={e => sendReminderModalHandler(e, obj)}
            >
              {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
            </Button>
            <div className="ed-supporting-text ed-text-size-14 half-cell">{`${translatr(
              'web.manager-dashboard-v2.main',
              'LastSent'
            )}: ${moment(obj.last_reminder_date, MONTH_DAY_YEAR_FORMAT).format(
              DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
            )}`}</div>
          </div>
        ) : obj['completion_percentage'] === 100 || obj['state'] === 'completed' ? (
          <Tooltip
            customClass="pmb-unset"
            message={translatr('web.manager-dashboard-v2.main', 'Completed')}
            isHtmlIncluded
            pos="left"
          >
            <Button color="primary" variant="borderless" padding="xsmall" disabled>
              {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
            </Button>
          </Tooltip>
        ) : (
          <Button
            color="primary"
            variant="borderless"
            padding="xsmall"
            onClick={e => sendReminderModalHandler(e, obj)}
          >
            {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
          </Button>
        );
      default:
        return <span className="short-text ed-text-color">{obj[key]}</span>;
    }
  }

  useEffect(() => {
    setDueDate({ fromDate: null, toDate: null });
    setAssignedDate({ fromDate: null, toDate: null });
    getAssignments();
  }, []);

  useEffect(() => {
    getTableData(assignments);
  }, [assignments]);

  useEffect(() => {
    if (!shouldUpdate) {
      return;
    }
    getAssignments();
    setShouldUpdate(false);
  }, [shouldUpdate]);

  const initStatus = data => {
    if (!data?.length) {
      assignmentStatusRef.current = [];
      setAssignmentStatus([]);
      setPrevStatus({});
      return;
    }

    let theKeys = [];
    data.forEach(item => {
      if (!theKeys.includes(item.state)) {
        theKeys.push(item.state);
      }
    });
    let prevData = {};
    const theStatus = theKeys.map(k => {
      const isChecked =
        [...(assignmentStatusRef.current || [])].find(item => item.value === k)?.checked || false;
      prevData[k] = isChecked;

      return {
        value: k,
        label: StatusDropdownItems[k],
        checked: isChecked,
        onChange: onStatusCheckboxChange
      };
    });

    setPrevStatus(prevData);
    assignmentStatusRef.current = [...theStatus];
    setAssignmentStatus(theStatus);
  };

  const initAssignorsOpts = data => {
    if (!data?.length) {
      setPrevAssignors({});
      return [];
    }
    const retData = data
      .filter(item => item?.length > 2)
      .map(item => {
        const ischecked =
          [...(assignorsListRef?.current || [])].find(d => d.value === item[1])?.checked || false;
        return {
          value: item[1],
          label: `${item[0]} (${item[2]})`,
          checked: ischecked,
          onChange: onAssinedByCheckboxChange
        };
      });

    let prevData = {};
    retData.forEach(item => {
      prevData[item.value] = item.checked;
    });
    setPrevAssignors(prevData);
    return retData;
  };

  const getTableData = data => {
    if (!Array.isArray(data)) {
      setTableData([]);
      return;
    }

    const translateMonth = str => {
      return str.replace(/\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b/g, match =>
        translatr('web.common.main', match)
      );
    };

    const TableData = assignments.map((assignment, index) => {
      let row = [];
      Object.keys(Headers).forEach((key, i) => {
        let obj = {
          ...assignment
        };
        if (key === 'assigned_on') {
          const assignedOnDate = moment(assignment[key], MONTH_DAY_YEAR_FORMAT).format(
            DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT
          );
          obj.assigned_on = !!assignment[key] ? translateMonth(assignedOnDate) : null;
        }
        row.push({
          children: rowChildrenSelector(obj, key),
          id: `${key}-${index}-${i}`,
          align: 'text-left'
        });
      });
      return row;
    });
    setTableData(TableData);
  };

  const paginate = resp => {
    let activePage = currentPage;
    if (resp.event === 'next') {
      getAssignments(activePage + 1, activeUser);
    } else if (resp.event === 'prev') {
      getAssignments(activePage - 1, activeUser);
    } else {
      if (Number.isInteger(resp.event)) {
        getAssignments(resp.event, activeUser);
      }
    }
  };

  const onAssinedByCheckboxChange = e => {
    const { value, checked } = e.target;
    let theAssignorsList = [...assignorsListRef.current];
    let findMe = theAssignorsList?.find(item => item.value == value);
    if (findMe) {
      findMe.checked = checked;
      setEnableAssignorsApply(true);
      assignorsListRef.current = theAssignorsList;
    }

    const theAssignors = { ...assignorsDataRef.current };
    const hasMore = theAssignors.totalCount > theAssignorsList.length;
    const myAssignors = {
      ...theAssignors,
      data: hasMore
        ? [...theAssignorsList, ...theAssignors.data.slice(theAssignorsList.length)]
        : [...theAssignorsList]
    };
    assignorsDataRef.current = myAssignors;
    setAssignors(myAssignors);
  };

  const onStatusCheckboxChange = e => {
    const { value, checked } = e.target;
    let theAssignmentStatus = [...assignmentStatusRef.current];
    let findMe = theAssignmentStatus?.find(item => item.value == value);
    if (findMe) {
      findMe.checked = checked;
      setEnableStatusApply(true);
    }
    setAssignmentStatus(theAssignmentStatus);
  };

  const onCancel = type => {
    if (!type) {
      let theList = [...assignorsListRef.current].map(item => {
        item.checked = prevAssignors[item.value];
        return item;
      });
      assignorsListRef.current = theList;

      const myAssignors = {
        ...assignorsDataRef.current,
        data: [...theList]
      };
      assignorsDataRef.current = myAssignors;
      setAssignors(myAssignors);
      setEnableAssignorsApply(false);
      setOpenAssignorsDropdown(false);
    } else {
      let theList = [...(assignmentStatusRef.current || [])].map(item => {
        item.checked = prevStatus[item.value];
        return item;
      });
      assignmentStatusRef.current = theList;
      setAssignmentStatus(theList);
      setEnableStatusApply(false);
      setOpenStatusDropdown(false);
    }
  };

  const onApplyfilter = () => {
    setOpenAssignorsDropdown(false);
    setOpenStatusDropdown(false);
    setShouldUpdate(true);
  };

  const onViewMore = () => {
    const thePage = assignors.page;
    const myList = [...assignorsListRef.current];
    const pos1 = thePage * ASSIGNOR_OFFSET_AMOUNT;
    const pos2 = (thePage + 1) * ASSIGNOR_OFFSET_AMOUNT;

    const theAssignorsList = [...myList, ...assignors.data.slice(pos1, pos2)];
    assignorsListRef.current = theAssignorsList;

    const hasMore = assignors.totalCount > theAssignorsList.length;
    const theAssignors = {
      ...assignors,
      data: hasMore
        ? [...theAssignorsList, ...assignors.data.slice(theAssignorsList.length)]
        : [...theAssignorsList],
      page: thePage + 1
    };
    assignorsDataRef.current = theAssignors;
    setAssignors(theAssignors);
    setShowViewMore(hasMore);
  };

  async function downloadReport(e) {
    let el = e.target;
    el.disabled = true;
    const payload = getPayload();
    const resp = await downloadIndividualReport(activeUser, payload).catch(err => {
      console.error(`Fail to download report downloadIndividualReport:func ${err.message || err}`);
    });

    el.removeAttribute('disabled');
    if (resp?.file_url) {
      let a = document.createElement('a');
      a.href = resp.file_url;
      a.setAttribute('download', `Individual-Report-${activeUser}.csv`);
      a.click();
    }
  }

  function onAssignedDateChange(newDate1, newDate2) {
    const fromDate = newDate1?.toLocalISOString() || null;
    const toDate = newDate2?.toLocalISOString() || null;
    setAssignedDate({ fromDate, toDate });
    setPlaceholderAssignedDate(null);
    setShowClearAssignedDate(true);
    setShouldUpdate(true);
  }

  function onDueDateChange(newDate1, newDate2) {
    const fromDate = newDate1?.toLocalISOString() || null;
    const toDate = newDate2?.toLocalISOString() || null;
    setDueDate({ fromDate, toDate });
    setPlaceholderDueDate(null);
    setShowClearDueDate(true);
    setShouldUpdate(true);
  }

  function onClearAssignedDate() {
    setPlaceholderAssignedDate(ALL_TIME);
    setShowClearAssignedDate(false);
    setAssignedDate({ fromDate: null, toDate: null });
    setShouldUpdate(true);
  }

  function onClearDueDate() {
    setPlaceholderDueDate(ALL_TIME);
    setShowClearDueDate(false);
    setDueDate({ fromDate: null, toDate: null });
    setShouldUpdate(true);
  }

  return (
    <>
      <div className="block mt-16">
        <>
          <div className="h6 ed-text-color">
            {Number.isInteger(totalCount) && Number.isInteger(totalCompletedCount) && (
              <span className="mr-10">
                {totalCompletedCount}/{totalCount}
              </span>
            )}
            {translatr('web.manager-dashboard-v2.main', 'AssignmentsCompleted')}
          </div>
          <div className="flx-space-btw mt-16">
            <div className="search-container">
              <SearchInput
                placeholder={translatr('web.common.main', 'SearchContent2')}
                onSearch={debouncedSearch}
                searchOnTextChange={true}
              />
            </div>
            <Button
              color="secondary"
              variant="ghost"
              onClick={downloadReport}
              disabled={totalCount < 1}
            >
              {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
            </Button>
          </div>
          <div className="flx-space-btw mt-8 mb-16">
            <div className="calendars-sec">
              <DatePicker
                singleDatePicker={false}
                preFix={translatr('web.manager-dashboard-v2.main', 'DueDateRange')}
                opens="right"
                placeHolder={placeholderDueDate}
                onChange={onDueDateChange}
                startDate={dueDate.fromDate ? new Date(dueDate.fromDate) : null}
                endDate={dueDate.toDate ? new Date(dueDate.toDate) : null}
                onClear={showClearDueDate ? onClearDueDate : null}
              />
              <DatePicker
                singleDatePicker={false}
                preFix={translatr('web.common.main', 'AssignedOn')}
                opens="left"
                placeHolder={placeholderAssignedDate}
                onChange={onAssignedDateChange}
                startDate={assignedDate.fromDate ? new Date(assignedDate.fromDate) : null}
                endDate={assignedDate.toDate ? new Date(assignedDate.toDate) : null}
                onClear={showClearAssignedDate ? onClearAssignedDate : null}
              />
            </div>
            <div className="dropdown-filters-sec">
              <div>
                <span>{translatr('web.common.main', 'StatusSkill')}</span>
                <Dropdown
                  icon={
                    <i
                      className="icon-filter"
                      role="button"
                      aria-label={translatr('web.manager-dashboard-v2.main', 'OpenStatusDropdown')}
                    />
                  }
                  openDropdown={openStatusDropdown}
                  setOpenDropdown={setOpenStatusDropdown}
                >
                  <CheckboxGroup
                    groupName={'md2-individual-due-date'}
                    className="flex-row"
                    items={assignmentStatus}
                    limit={15}
                    isTranslated={true}
                  />
                  <>
                    <div className="break-sec mt-8" />
                    <div className="filter-btns-sec">
                      <button onClick={() => onCancel('status')}>
                        {translatr('web.common.main', 'Cancel')}
                      </button>
                      <button
                        className="ed-btn ed-btn-primary"
                        disabled={!enableStatusApply}
                        onClick={() => onApplyfilter()}
                      >
                        {translatr('web.common.main', 'Apply')}
                      </button>
                    </div>
                  </>
                </Dropdown>
              </div>
              <div>
                <span>{translatr('web.common.main', 'AssignedBy')}</span>
                <Dropdown
                  icon={
                    <i
                      className="icon-filter"
                      role="button"
                      aria-label={translatr(
                        'web.manager-dashboard-v2.main',
                        'OpenAssignedByDropdown'
                      )}
                    />
                  }
                  openDropdown={openAssignorsDropdown}
                  setOpenDropdown={setOpenAssignorsDropdown}
                >
                  <CheckboxGroup
                    groupName={'md2-individual-assignors'}
                    className="flex-row"
                    items={assignorsListRef.current}
                    limit={20}
                    isTranslated={true}
                  />
                  <button
                    hidden={showViewMore ? false : true}
                    className="view-more"
                    onClick={() => onViewMore()}
                    aria-label={translatr('web.manager-dashboard-v2.main', 'ViewMoreAssignors')}
                  >
                    {translatr('web.common.main', 'ViewMore')}
                  </button>
                  <div className={`break-sec ${showViewMore ? '' : 'mt-8'}`} />
                  <div className="filter-btns-sec">
                    <button onClick={() => onCancel()}>
                      {translatr('web.common.main', 'Cancel')}
                    </button>
                    <button
                      disabled={!enableAssignorsApply}
                      className="ed-btn ed-btn-primary"
                      onClick={() => onApplyfilter()}
                    >
                      {translatr('web.common.main', 'Apply')}
                    </button>
                  </div>
                </Dropdown>
              </div>
            </div>
          </div>
        </>
        {loading && (
          <div className="table-loader">
            <Loading />
          </div>
        )}
        <Table
          className={`none-vertical-border ${loading ? 'hide-rows' : ''}`}
          headers={TableHeaders}
          rows={tableData}
        />
        {totalCount > 10 && (
          <div className="mt-16">
            <Pagination
              postPerPage={10}
              totalPosts={totalCount}
              paginate={paginate}
              activePage={currentPage}
              iconType={true}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default LearningTable;

LearningTable.propTypes = {
  activeUser: PropTypes.string,
  sendReminderModalHandler: PropTypes.func
};
