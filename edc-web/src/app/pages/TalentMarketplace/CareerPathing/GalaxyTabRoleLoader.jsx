import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { search } from 'edc-web-sdk/requests/extOpportunities';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { translatr } from 'centralized-design-system/src/Translatr';
import GalaxyTab from '../CareerPathing/GalaxyTab';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import PropTypes from 'prop-types';
import './GalaxyTab.scss';

const GalaxyTabRoleLoader = ({
  isCareerPathEnabled,
  currentUserLang,
  switchBackRoles,
  userLoading
}) => {
  const ROOT_CLASS = 'tm__galaxy-tab';
  const [loading, setLoading] = useState(true);
  const [galaxyBaseRole, setGalaxyBaseRole] = useState(undefined);
  const [roleLoadingError, setRoleLoadingError] = useState(false);
  const { baseRoleId } = useParams();

  useEffect(() => {
    if (baseRoleId) {
      setLoading(true);
      search({
        type: JOB_TYPE.ROLE,
        opportunityId: [baseRoleId],
        language: currentUserLang,
        isDismissedExcluded: false,
        pageSize: 1
      })
        .then(response => {
          if (response.values?.length) {
            const role = response.values[0];
            const fullLabel = `${role?.jobFamily?.title} - ${role.title}`;
            setGalaxyBaseRole({ id: null, label: fullLabel, roleId: role.id });
          } else {
            setRoleLoadingError(true);
            setGalaxyBaseRole({});
          }
          setLoading(false);
        })
        .catch(error => {
          console.error(`Error in fetching role detail for Galaxy View`, error);
          setRoleLoadingError(true);
          setLoading(false);
        });
    }
  }, [baseRoleId, currentUserLang]);

  return roleLoadingError ? (
    <div className={`${ROOT_CLASS}--placeholder text-center`}>
      <EmptyState
        icon="icon-error"
        title={`${translatr('web.talentmarketplace.main', 'SomethingWentWrong')}`}
        description={translatr('web.talentmarketplace.main', 'SorryPleaseTryAgainLater')}
      />
    </div>
  ) : (
    <GalaxyTab
      startingRole={galaxyBaseRole}
      userLoading={loading || userLoading}
      isCareerPathEnabled={isCareerPathEnabled}
      switchBackRoles={switchBackRoles}
    />
  );
};

const mapStoreStateToProps = ({ currentUser, team }) => {
  return {
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en'
  };
};

GalaxyTabRoleLoader.propTypes = {
  currentUserLang: PropTypes.string,
  isCareerPathEnabled: PropTypes.bool,
  userLoading: PropTypes.bool,
  switchBackRoles: PropTypes.array
};

export default connect(mapStoreStateToProps)(GalaxyTabRoleLoader);
