import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { Translatr, translatr, omp } from 'centralized-design-system/src/Translatr';
import GalaxyView from 'opportunity-marketplace/CareerPathing/GalaxyView/GalaxyView';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import PropTypes from 'prop-types';
import { loadAllOrganizations, orgVisibility, orgAssociation } from '@actions/organizationsActions';
import { getRolesByJobFamily } from 'edc-web-sdk/requests/extOpportunities';
import './GalaxyTab.scss';
import { tmSaveFiltersConfig } from '@actions/talentmarketplaceActions';
import {
  FILTER_IDS,
  FILTER_TYPE,
  TM_CARRERPATH_FILTER_BUCKET_NAME
} from 'opportunity-marketplace/shared/filters/Filters.constants';
import {
  cleanupFilterNames,
  prepareOrgFiltersForView
} from 'opportunity-marketplace/shared/filters/Filters.utils';
import { attachCountersToOrganizations } from './utils';
import { ICON_SET } from 'edc-web-sdk/helpers/matchIcons';
import { useMatchingConfig } from 'opportunity-marketplace/shared/hooks';
import { FilterHostname } from '@components/common/FilterHostname';
import { preventReadLiveRegions } from '../util';

const ROOT_CLASS = 'tm__galaxy-tab';

const GalaxyTab = ({
  startingRole,
  userLoading,
  currentUserLang,
  isCareerPathEnabled,
  saveFiltersConfig,
  searchFilters,
  switchBackRoles,
  organizations,
  loadOrganizations
}) => {
  const [galaxyLoading, setGalaxyLoading] = useState(true);
  const [galaxyLoadingFailure, setGalaxyLoadingFailure] = useState(false);
  const [galaxyData, setGalaxyData] = useState([]);
  const [orgFilters, setOrgFilters] = useState([]);
  const [filtersConfig, setfiltersConfig] = useState([]);
  const [organizationsCountersConfig, setOrganizationsCountersConfig] = useState(null);

  const matchingConfig = useMatchingConfig();
  const [iconSet, setIcon] = useState();
  const [hideMatchingDetails, setHideMatchingDetails] = useState(false);

  useEffect(() => {
    setIcon(matchingConfig.icons || ICON_SET.FACES);
  }, [matchingConfig.icons]);

  useEffect(() => {
    if (
      organizations.get('config')?.enable &&
      !organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER]
    ) {
      loadOrganizations(currentUserLang, orgVisibility.JOB_ROLES_FILTER, orgAssociation.JOB_ROLE);
    }
  }, [
    organizations.get('config')?.enable,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER]
  ]);

  useEffect(() => {
    setOrgFilters(
      prepareOrgFiltersForView({
        organizations,
        orgKeys:
          organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER] || [],
        orgVisibility: orgVisibility.JOB_ROLE_FILTER
      })
    );
  }, [
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER],
    organizations.get('levelForTypeAndVisibility')
  ]);

  const preparePayload = () => {
    const filters = cleanupFilterNames(searchFilters.filters[startingRole?.roleId]);

    let orgs = {};
    for (let key in filters) {
      if (key.startsWith('org')) {
        let payloadKey = key.split('org')[1];
        const fullySelected = filters[key].find?.(item => item.value === '0');
        if (!fullySelected) {
          orgs[payloadKey] = filters[key].map(item => item.value);
        }
      }
    }

    return {
      baseJobRoleId: startingRole?.roleId,
      jobFamilies: (filters[FILTER_IDS.JOBFAMILY] || []).map(f => f.value),
      organizations: orgs,
      language: currentUserLang
    };
  };

  useEffect(() => {
    if (startingRole) {
      if (isCareerPathEnabled === true) {
        setGalaxyLoading(true);
        getRolesByJobFamily(preparePayload())
          .then(data => {
            setGalaxyData(data?.data);
            if (data?.filters) {
              setOrganizationsCountersConfig(data.filters?.organizations);
              setfiltersConfig([
                {
                  id: FILTER_IDS.JOBFAMILY,
                  label: 'Job Family', //label will be translated in component
                  type: FILTER_TYPE.CUSTOM,
                  search: false,
                  customSearch: 'jobFamily',
                  data: data.filters?.jobFamilies?.map(filter => ({ value: filter.id, ...filter })),
                  available: true
                }
              ]);
            }
            if (data.context) {
              const { hideMatchingDetails: hideMatch } = data.context || {};
              setHideMatchingDetails(hideMatch);
            }

            setGalaxyLoading(false);
          })
          .catch(error => {
            setGalaxyLoading(false);
            setGalaxyLoadingFailure(true);
            console.error('Error in GET rolesByJobFamily', error);
          });
      } else {
        if (typeof isCareerPathEnabled !== 'undefined') {
          setGalaxyLoading(false);
        }
      }
    } else {
      if (startingRole === null) {
        setGalaxyLoading(false);
      }
    }
  }, [startingRole, isCareerPathEnabled, searchFilters.filters]);

  useEffect(() => {
    const newConfig = [...filtersConfig, ...orgFilters];

    const filtersWithCounters = attachCountersToOrganizations(
      newConfig,
      organizationsCountersConfig
    );

    saveFiltersConfig(filtersWithCounters);
  }, [filtersConfig, orgFilters]);

  const loading =
    userLoading || galaxyLoading || typeof isCareerPathEnabled === 'undefined' || !iconSet;

  useEffect(() => {
    if (loading) {
      document.title = FilterHostname(translatr('web.common.main', 'Loading'));
      preventReadLiveRegions();
    } else {
      document.title = FilterHostname(omp('tm_tm_career_path'));
    }
  }, [loading]);

  if (!loading && !startingRole?.label) {
    return (
      <Translatr apps={['web.talentmarketplace.career-path']}>
        <div className={`${ROOT_CLASS}--placeholder`}>
          <EmptyState
            icon="icon-file"
            headingLevel="h2"
            title={translatr('web.talentmarketplace.career-path', 'NoGalaxyDataPlaceholder', {
              role: omp('tm_job_role')
            })}
            buttonLabel={translatr('web.talentmarketplace.main', 'UpdateCurrentRole', {
              role: omp('tm_job_role')
            })}
            buttonLink="/settings"
          />
        </div>
      </Translatr>
    );
  }

  if (!loading && (galaxyLoadingFailure || isCareerPathEnabled === false)) {
    return (
      <div className={`${ROOT_CLASS}--placeholder text-center`}>
        <EmptyState
          icon="icon-file"
          headingLevel="h2"
          title={translatr('web.talentmarketplace.career-path', 'CareerPathsUnavailable')}
          description={translatr(
            'web.talentmarketplace.career-path',
            'CareerPathsUnavailableDescription',
            {
              pageName: omp('tm_tm_career_path')
            }
          )}
          buttonLabel={translatr('web.talentmarketplace.career-path', 'SeeAllRoles', {
            items: omp('tm_tm_job_roles')
          })}
          buttonLink="/career/job-roles"
        />
      </div>
    );
  }

  return (
    <Translatr apps={['web.talentmarketplace.career-path']}>
      <div className={ROOT_CLASS} role="region" aria-label={omp('tm_tm_career_path')}>
        <GalaxyView
          initialData={!loading ? galaxyData : []}
          startingRole={startingRole}
          currentUserLang={currentUserLang}
          switchBackRoles={switchBackRoles}
          isCareerPathEnabled={isCareerPathEnabled}
          loading={loading}
          hideMatchingDetails={hideMatchingDetails}
        />
      </div>
    </Translatr>
  );
};

const mapStoreStateToProps = ({ currentUser, team, talentmarketplaceReducer, organizations }) => {
  return {
    searchFilters: talentmarketplaceReducer.get(TM_CARRERPATH_FILTER_BUCKET_NAME),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations
  };
};

const mapDispatchToProps = dispatch => {
  return {
    saveFiltersConfig: newConfig =>
      dispatch(tmSaveFiltersConfig(newConfig, TM_CARRERPATH_FILTER_BUCKET_NAME)),
    loadOrganizations: (lang, currentVisibility, currentAssociation) =>
      dispatch(loadAllOrganizations(lang, currentVisibility, currentAssociation))
  };
};

GalaxyTab.propTypes = {
  startingRole: PropTypes.object,
  userLoading: PropTypes.bool,
  currentUserLang: PropTypes.string,
  isCareerPathEnabled: PropTypes.bool,
  openFiltersModal: PropTypes.func,
  saveFiltersConfig: PropTypes.func,
  searchFilters: PropTypes.object,
  switchBackRoles: PropTypes.array,
  organizations: PropTypes.object,
  loadOrganizations: PropTypes.func
};

export default connect(mapStoreStateToProps, mapDispatchToProps)(GalaxyTab);
