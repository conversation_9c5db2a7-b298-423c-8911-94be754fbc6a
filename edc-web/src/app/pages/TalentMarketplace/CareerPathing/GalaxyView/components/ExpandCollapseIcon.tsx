import { useTheme } from 'centralized-design-system/src/Theme/ThemeContext';
import React from 'react';

interface ExpandCollapseIconProps {
  isExpanded: boolean;
  size?: number;
  color?: string;
  strokeWidth?: number;
  className?: string;
}

export const ExpandCollapseIcon: React.FC<ExpandCollapseIconProps> = ({
  isExpanded,
  size = 32,
  color,
  strokeWidth = 2,
  className
}) => {
  const theme = useTheme();
  const strokeColor = color || theme.palette.gray[5];

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d={
          isExpanded
          ? 'M20 4V28M13.3333 20L9.33333 16L13.3333 12M6.66667 4H25.3333C26.8061 4 28 5.19391 28 6.66667V25.3333C28 26.8061 26.8061 28 25.3333 28H6.66667C5.19391 28 4 26.8061 4 25.3333V6.66667C4 5.19391 5.19391 4 6.66667 4Z'
          : 'M20.0001 4V28M10.6668 12L14.6668 16L10.6668 20M6.66676 4H25.3334C26.8062 4 28.0001 5.19391 28.0001 6.66667V25.3333C28.0001 26.8061 26.8062 28 25.3334 28H6.66676C5.194 28 4.00009 26.8061 4.00009 25.3333V6.66667C4.00009 5.19391 5.194 4 6.66676 4Z'
        }
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
