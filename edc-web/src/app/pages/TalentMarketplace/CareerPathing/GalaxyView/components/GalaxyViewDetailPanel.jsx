import React, { useEffect, forwardRef, useRef, useState, useContext } from 'react';
import PropTypes from 'prop-types';
import { SearchInput } from 'centralized-design-system/src/Inputs/index';
import { connect } from 'react-redux';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import { useParams } from 'react-router-dom';
import './GalaxyViewDetailPanel.scss';
import {
  FILTERS_DEFAULT_ASSOCIATION_ID,
  TM_CARRERPATH_FILTER_BUCKET_NAME
} from 'opportunity-marketplace/shared/filters/Filters.constants';
import { openCareerPathFiltersModal } from '@actions/modalActions';
import { Button } from 'centralized-design-system/src/Buttons';
import HorizontalRoleCard from '@components/HorizontalCard/RoleCard';
import { Link } from 'centralized-design-system/src/Links';
import { ExpandCollapseIcon } from './ExpandCollapseIcon';
import { toggleAspirationsFactory } from '@pages/TalentMarketplace/util';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import { useIsDevelopmentPlanEnabled } from '@pages/TalentMarketplace/DevelopmentPlan/hooks/useIsDevelopmentPlanEnabled';

const topSpace = 16;
const bottomSpace = 16;
const searchAndAccordionButtonHeight = 126;

const GalaxyViewDetailPanel = forwardRef(
  ({
    maxHeight,
    roles,
    isCareerPathEnabled,
    selected,
    isExpanded,
    setIsExpanded,
    setSelected,
    hideMatchingDetails,
    onSearch,
    searchStr,
    openFiltersModal,
    searchFilters,
    startingRole
  }) => {
    const { baseRoleId } = useParams();
    const containerRef = useRef(null);
    const roleListRef = useRef(null);
    const roleCardsRef = useRef([]);

    const [filtersCounter, setFiltersCounter] = useState(0);
    const [openedFromHtmlElement, setOpenedFromHtmlElement] = useState(null);

    const { isAspirationalRole, updateAspirations } = useContext(AspirationsContext);
    const { showDevelopmentPlan } = useIsDevelopmentPlanEnabled();

    const associationId = startingRole?.roleId;

    // computed styles
    const panelComputedStyles = {
      top: `${topSpace}px`,
      marginTop: `-${maxHeight - searchAndAccordionButtonHeight}px`
    };

    const scrollAreaComputedStyles = {
      height: `calc(100vh - ${topSpace + bottomSpace + searchAndAccordionButtonHeight}px)`
    };

    const onClose = () => {
      setIsExpanded(false);
      openedFromHtmlElement?.focus();
    };

    const escFunction = e => {
      if (e.key === 'Escape' && !e.target.closest('.dropdown-content')) {
        // don't close if dropdown is open
        onClose();
      }
    };

    const toggleAspirations = React.useMemo(
      () => toggleAspirationsFactory(updateAspirations, `GalaxyViewDetail`),
      [updateAspirations]
    );

    useEffect(() => {
      document.addEventListener('keydown', escFunction, false);

      return () => {
        document.removeEventListener('keydown', escFunction, false);
      };
    }, [escFunction]);

    const toggleExpand = () => {
      setTimeout(() => {
        setIsExpanded(!isExpanded);
      }, 150);
    };

    useEffect(() => {
      roleCardsRef.current = roleCardsRef.current.slice(0, roles.length + 1);
    }, [roles]);

    useEffect(() => {
      if (selected && roleCardsRef.current[selected]) {
        const title = roleCardsRef.current[selected].querySelector(
          '.galaxy-view-list__item .ed-h-card'
        );

        if (title) {
          setTimeout(() => {
            title?.focus();

            const selectedElementOnGraph = document.querySelector('.cp-role-pill--selected');
            setOpenedFromHtmlElement(selectedElementOnGraph?.closest('.cp_role-pill--clickable'));
          }, 100);
        }
      }
    }, [selected]);

    useEffect(() => {
      const filters = searchFilters?.filters[associationId || FILTERS_DEFAULT_ASSOCIATION_ID] || {};

      let count = 0;

      if (filters) {
        for (const key in filters) {
          if (Array.isArray(filters[key])) {
            count += filters[key].length;
          }
        }
      }

      setFiltersCounter(count);
    }, [searchFilters]);

    useEffect(() => {
      const focusableElements = containerRef?.current?.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
      );
      const focusableList = containerRef?.current?.querySelectorAll(
        '.galaxy-view-list__item .ed-h-card'
      );

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      const handleKeyDown = e => {
        const isChildOfMenu = e.target?.parentElement?.role === 'menuitem';

        if (e.key === 'Tab' && !isChildOfMenu) {
          if (focusableElements.length === 0) return;

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            }
          } else {
            if (document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
        } else if ((e.key === 'ArrowUp' || e.key === 'ArrowDown') && !isChildOfMenu) {
          e.preventDefault();
          const currentIndex = Array.from(focusableList).findIndex(
            el => el === document.activeElement.closest('.galaxy-view-list__item .ed-h-card')
          );
          let nextIndex = e.key === 'ArrowUp' ? currentIndex - 1 : currentIndex + 1;
          nextIndex = nextIndex < 0 ? focusableList.length - 1 : nextIndex % focusableList.length;
          const nextElement = focusableList[nextIndex];

          if (nextElement) {
            nextElement.focus();
          }
        }
      };

      if (isExpanded) {
        containerRef?.current?.addEventListener('keydown', handleKeyDown);
      } else {
        containerRef?.current?.removeEventListener('keydown', handleKeyDown);
      }

      return () => {
        containerRef?.current?.removeEventListener('keydown', handleKeyDown);
      };
    }, [isExpanded, roleCardsRef, selected, roles]);

    return (
      <div
        aria-modal="true"
        ref={containerRef}
        style={panelComputedStyles}
        className={`galaxy-view-list__panel ${isExpanded ? '--expanded' : '--collapsed'}`}
      >
        <div className="galaxy-view-list__panel-expand-button">
          <button
            className="galaxy-view-list__collapse-btn"
            onClick={toggleExpand}
            aria-expanded={isExpanded}
            aria-label={translatr('web.talentmarketplace.career-path', 'ListOfRoles', {
              roles: omp('tm_tm_job_roles')
            })}
          >
            <ExpandCollapseIcon isExpanded={isExpanded} />
          </button>
        </div>
        <div className="galaxy-view-list__panel-header">
          <span id="galaxy-view-list__collapse-btn-text">
            {translatr('web.talentmarketplace.career-path', 'ListOfRoles', {
              roles: omp('tm_tm_job_roles')
            })}
          </span>
          {isExpanded && (
            <span className="galaxy-view-list__panel-header-link">
              <Link
                color="primary"
                size="large"
                to="/career/job-roles"
                aria-describedby="galaxy-view-list__collapse-btn-text"
              >
                {translatr('web.common.main', 'ViewAll')}
              </Link>
            </span>
          )}
        </div>
        <div
          className="galaxy-view-list__panel-content"
          role="dialog"
          aria-labelledby="galaxy-view-list__collapse-btn-text"
        >
          <div className="galaxy-view-list__search">
            <SearchInput
              value={searchStr}
              placeholder={translatr('web.projects.career-path', 'SearchJobRole', {
                role: omp('tm_tm_job_role')
              })}
              onSearch={onSearch}
            />
            <Button
              color="secondary"
              variant="ghost"
              onClick={() => openFiltersModal(searchFilters, associationId)}
              aria-describedby="galaxy-view-filters-btn"
            >
              <span>{translatr('web.talentmarketplace.main', 'Filters')}</span>
              {filtersCounter > 0 && (
                <span className="galaxy-view-list__selected-filter-count" aria-hidden>
                  {filtersCounter}
                </span>
              )}
            </Button>
            <span className="sr-only" id="galaxy-view-filters-btn">
              {translatr('web.talentmarketplace.main', 'XFilterApplied', { nmb: filtersCounter })}
            </span>
          </div>
          <div aria-live="polite" className="sr-only">
            {!roles?.length
              ? translatr('web.common.main', 'NoResultsFound2')
              : translatr('web.common.main', 'TotalitemsResults', {
                  totalItems: roles?.length
                })}
          </div>
          <ul
            ref={roleListRef}
            className="galaxy-view-list__scrollable"
            style={scrollAreaComputedStyles}
          >
            {roles.length > 0 ? (
              roles.map((role, idx) => {
                const navUrl = baseRoleId
                  ? `/career/detail/${JOB_TYPE.ROLE}/${encodeURIComponent(role?.id)}/${baseRoleId}`
                  : `/career/detail/${JOB_TYPE.ROLE}/${encodeURIComponent(role?.id)}`;
                const urlData = {
                  to: navUrl,
                  state:
                    role.overallScoreStatus === 'UNKNOWN'
                      ? { hideMatchingDetails }
                      : {
                          overallScore: role.overallScore,
                          skillsGraphScore: role.skillsGraphScore,
                          hideMatchingDetails
                        }
                };
                return (
                  <li className="galaxy-view-list__item" key={`placeholder-${idx}`}>
                    <HorizontalRoleCard
                      key={`role_card_${role.id}`}
                      role={{
                        ...role,
                        title: role.name,
                        aspirational: isAspirationalRole(role.id),
                        jobFamily: {
                          title: role.jobFamilyName
                        }
                      }}
                      selected={selected === role.id}
                      ref={el => (roleCardsRef.current[role.id] = el)}
                      isCareerPathEnabled={isCareerPathEnabled}
                      link={urlData}
                      onClick={() => {
                        setSelected(role.id);
                      }}
                      onAspirationChange={toggleAspirations}
                      isMatchingEnabled={!hideMatchingDetails}
                      isSkillsEnabled
                      isDevelopmentPlanEnabled={showDevelopmentPlan}
                    />
                  </li>
                );
              })
            ) : (
              <li className="galaxy-view-list__empty">
                <h2>{translatr('web.talentmarketplace.career-path', 'NoResultsFound')}</h2>
                <p>
                  {translatr(
                    'web.talentmarketplace.career-path',
                    'CheckIfYouHaveAppliedAnyFiltersOrTryUsingDifferentWords'
                  )}
                </p>
              </li>
            )}
          </ul>
        </div>
      </div>
    );
  }
);

const mapStoreStateToProps = ({ talentmarketplaceReducer }) => {
  return {
    searchFilters: talentmarketplaceReducer.get(TM_CARRERPATH_FILTER_BUCKET_NAME)
  };
};

const mapDispatchToProps = dispatch => {
  return {
    openFiltersModal: (filtersState, config) =>
      dispatch(openCareerPathFiltersModal(filtersState, config))
  };
};

GalaxyViewDetailPanel.propTypes = {
  maxHeight: PropTypes.number,
  roles: PropTypes.array,
  isCareerPathEnabled: PropTypes.bool,
  setSelected: PropTypes.func,
  selected: PropTypes.string,
  setIsExpanded: PropTypes.func,
  isExpanded: PropTypes.bool,
  hideMatchingDetails: PropTypes.bool,
  onSearch: PropTypes.func,
  searchStr: PropTypes.string,
  openFiltersModal: PropTypes.func,
  saveFiltersConfig: PropTypes.func,
  loadOrganizations: PropTypes.func,
  searchFilters: PropTypes.object,
  startingRole: PropTypes.object
};

export default connect(mapStoreStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  GalaxyViewDetailPanel
);
