import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import UserRoleSvg from 'opportunity-marketplace/shared/CareerPathing/UserRoleSvg';
import RolePillSvg from 'opportunity-marketplace/shared/CareerPathing/RolePillSvg';
import GroupPillSvg from 'opportunity-marketplace/shared/CareerPathing/GroupPillSvg';
import { getUsrAvatar } from 'opportunity-marketplace/CareerPathing/SubwayView/helpers';
import { extractRoleName } from 'opportunity-marketplace/helpers';
import {
  USER_ROLE_PILL_WIDTH,
  USER_ROLE_PILL_HEIGHT,
  ROLE_PILL_WIDTH,
  ROLE_PILL_HEIGHT
} from './../helpers';
import { useMatchingConfig } from 'opportunity-marketplace/shared/hooks';
import GroupListSvg from 'opportunity-marketplace/shared/CareerPathing/GroupListSvg';

const isRoleInTheGroup = (group, roleId) => {
  const groupId = Object.entries(group).find(([, value]) =>
    value.value.items.some(role => role.id === roleId)
  );

  return groupId ? groupId[1]?.key : null;
};

const GalaxyViewRoleItemsSvg = ({
  roles,
  groups,
  setRoles,
  setGroups,
  centerPoint,
  startingRole,
  animatePill,
  setAnimatePill,
  selectedRoleId,
  setSelectedRoleId,
  switchBackRoles,
  detailPanelRef,
  hideMatchingDetails
}) => {
  // elements extracting before rendering (needed for svg top/bottom positioning)
  // it also supports moving hovered element on top ( PO requirement )
  const expandedRoles = roles.filter(r => !r.groupedOnMove && r.isExpanded);
  const dots = roles.filter(r => !r.groupedOnMove && !r.isExpanded);
  const expandedRoleElements = expandedRoles.filter(r => !r.onTop);
  const expandedRoleTopElement = expandedRoles.filter(r => r.onTop);
  const groupElements = groups.filter(g => !g.onTop);
  const groupTopElement = groups.filter(g => g.onTop);
  const numberOfElements = dots.length + expandedRoles.length + groups.length;
  const [selectedItem, setSelectedItem] = useState('');
  const [selectedGroup, setSelectedGroup] = useState({
    id: null,
    x: 0,
    y: 0
  });
  const [showRoleList, setShowRoleList] = useState(false);
  const { icons } = useMatchingConfig();
  const refs = useRef([]);
  const groupListRef = useRef(null);
  const selectedGroupRef = useRef(null);

  const roleHandleClick = id => {
    setSelectedItem(id);
    setSelectedRoleId(id);
    setShowRoleList(false);
    setSelectedGroup({
      id: null,
      x: 0,
      y: 0
    });
  };
  const groupHandleClick = (grpKey, rectProps) => {
    setSelectedItem(grpKey);
    setSelectedGroup({
      id: grpKey,
      x: rectProps.x,
      y: rectProps.y
    });

    if (grpKey === selectedGroup.id) {
      setShowRoleList(prevState => !prevState);
    } else {
      setShowRoleList(true);
    }
  };
  const roleHandleOver = id => {
    const updatedRoles = roles.map(role => {
      if (role.id === id) {
        return { ...role, onTop: true };
      } else {
        return { ...role, onTop: false };
      }
    });
    const updatedGroups = groups.map(grp => {
      return { ...grp, onTop: false };
    });
    setAnimatePill({ anim: false, animMode: '' });
    setGroups(updatedGroups);
    setRoles(updatedRoles);
  };
  const groupHandleOver = key => {
    const updatedGroups = groups.map(grp => {
      if (grp.key === key) {
        return { ...grp, onTop: true };
      } else {
        return { ...grp, onTop: false };
      }
    });
    const updatedRoles = roles.map(role => {
      return { ...role, onTop: false };
    });
    setAnimatePill({ anim: false, animMode: '' });
    setRoles(updatedRoles);
    setGroups(updatedGroups);
  };

  const handleFocusChange = (e, orderIndex) => {
    const indexOfNextItem = !!e.shiftKey ? orderIndex - 1 : orderIndex + 1;
    if (e.key === 'Tab') {
      e.preventDefault();

      if (indexOfNextItem in refs.current) {
        refs.current[indexOfNextItem].focus();
      } else if (indexOfNextItem > 0) {
        /*  Needed to avoid infinite focus loop in galaxy view */
        document.querySelector('.galaxy-view-list__collapse-btn')?.focus();
      }
    }
  };

  const groupHandleKeyDown = (e, grp, rectProps) => {
    handleFocusChange(e, grp.value.orderIndex);

    if (e.keyCode === 13 || e.keyCode === 32) {
      e.preventDefault();

      groupHandleClick(grp.key, rectProps);
    }
  };

  const roleHandleKeyDown = (e, role) => {
    handleFocusChange(e, role.orderIndex);

    if (e.keyCode === 13 || e.keyCode === 32) {
      e.preventDefault();

      roleHandleClick(role.id);
    }
  };

  const userRoleHandleKeyDown = e => {
    if (e.key === 'Tab' && !e.shiftKey && 1 in refs.current) {
      e.preventDefault();
      refs.current[1].focus();
    }
  };

  const startingRoleLabel = extractRoleName(startingRole);

  useEffect(() => {
    let group = groups && isRoleInTheGroup(groups, selectedRoleId);

    if (group) {
      setSelectedItem(group);
    } else {
      setSelectedItem(selectedRoleId);
    }
  }, [selectedRoleId, groups]);

  useEffect(() => {
    refs.current = refs.current.slice(0, numberOfElements + 1);
  }, [roles]);

  return (
    <>
      <g role="list">
        {/* lowest level on workspace - dots are always on bottom - so rendering as first group */}
        {dots.map((dot, idx) => (
          <RolePillSvg
            ref={el => (refs.current[dot.orderIndex] = el)}
            key={`role-${idx}`}
            name={dot.name}
            roleId={dot.id}
            x={dot.x}
            y={dot.y}
            width={ROLE_PILL_WIDTH}
            height={ROLE_PILL_HEIGHT}
            progress={dot.overallScore}
            progressStatus={dot.overallScoreStatus}
            isExpanded={dot.isExpanded}
            animate={animatePill}
            handleClick={() => roleHandleClick(dot.id)}
            handleKeyDown={e => roleHandleKeyDown(e, dot)}
            isSelected={dot.id === selectedItem}
            isLastElement={numberOfElements === dot.orderIndex}
            iconSet={icons}
            jobFamilyName={dot.jobFamilyName}
            noOfMoves={dot.noOfMoves}
            hideMatchingDetails={hideMatchingDetails}
          />
        ))}
        {/* second level - all expanded roles and groups - always over dots*/}
        {groupElements.map((grp, gidx) => (
          <GroupPillSvg
            ref={el => {
              if (grp.key === selectedItem) {
                selectedGroupRef.current = el;
              }
              return (refs.current[grp.value.orderIndex] = el);
            }}
            key={`group-${gidx}`}
            groupId={grp.key}
            groupData={grp.value}
            animate={animatePill}
            handleOver={() => groupHandleOver(grp.key)}
            handleClick={rectProps => groupHandleClick(grp.key, rectProps)}
            handleKeyDown={(e, rectProps) => groupHandleKeyDown(e, grp, rectProps)}
            isSelected={grp.key === selectedItem}
            isLastElement={numberOfElements === grp.value.orderIndex}
            iconSet={icons}
            hideMatchingDetails={hideMatchingDetails}
          />
        ))}
        {expandedRoleElements
          //.sort((a, b) => a.noOfMoves - b.noOfMoves)
          .map((expRole, idx) => (
            <RolePillSvg
              ref={el => (refs.current[expRole.orderIndex] = el)}
              key={`role-${idx}`}
              name={expRole.name}
              roleId={expRole.id}
              x={expRole.x}
              y={expRole.y}
              width={ROLE_PILL_WIDTH}
              height={ROLE_PILL_HEIGHT}
              progress={expRole.overallScore}
              progressStatus={expRole.overallScoreStatus}
              isExpanded={expRole.isExpanded}
              isAspirational={expRole.isAspirational}
              handleOver={() => roleHandleOver(expRole.id)}
              animate={animatePill}
              handleClick={() => roleHandleClick(expRole.id)}
              handleKeyDown={e => roleHandleKeyDown(e, expRole)}
              isSelected={expRole.id === selectedItem}
              isLastElement={numberOfElements === expRole.orderIndex}
              iconSet={icons}
              jobFamilyName={expRole.jobFamilyName}
              noOfMoves={expRole.noOfMoves}
              hideMatchingDetails={hideMatchingDetails}
            />
          ))}

        {/* Highest level - lifted up elements during mouseenter */}
        {expandedRoleTopElement.map((role, idx) => (
          <RolePillSvg
            ref={el => (refs.current[role.orderIndex] = el)}
            key={`role-${idx}`}
            name={role.name}
            roleId={role.id}
            x={role.x}
            y={role.y}
            width={ROLE_PILL_WIDTH}
            height={ROLE_PILL_HEIGHT}
            progress={role.overallScore}
            progressStatus={role.overallScoreStatus}
            isExpanded={role.isExpanded}
            isAspirational={role.isAspirational}
            handleOver={() => roleHandleOver(role.id)}
            animate={animatePill}
            handleClick={() => roleHandleClick(role.id)}
            handleKeyDown={e => roleHandleKeyDown(e, role)}
            isSelected={role.id === selectedItem}
            isLastElement={numberOfElements === role.orderIndex}
            iconSet={icons}
            jobFamilyName={role.jobFamilyName}
            noOfMoves={role.noOfMoves}
            hideMatchingDetails={hideMatchingDetails}
          />
        ))}
        {groupTopElement.map((grp, gidx) => (
          <GroupPillSvg
            ref={el => (refs.current[grp.value.orderIndex] = el)}
            key={`group-${gidx}`}
            groupId={grp.key}
            groupData={grp.value}
            animate={animatePill}
            handleOver={() => groupHandleOver(grp.key)}
            handleClick={rectProps => groupHandleClick(grp.key, rectProps)}
            handleKeyDown={(e, rectProps) => groupHandleKeyDown(e, grp, rectProps)}
            isSelected={grp.key === selectedItem}
            isLastElement={numberOfElements === grp.value.orderIndex}
            iconSet={icons}
            hideMatchingDetails={hideMatchingDetails}
          />
        ))}
      </g>
      <UserRoleSvg
        ref={el => (refs.current[0] = el)}
        usrImg={getUsrAvatar()}
        name={startingRoleLabel}
        roleId={startingRole.roleId}
        width={150}
        height={100}
        pillWidth={USER_ROLE_PILL_WIDTH}
        pillHeight={USER_ROLE_PILL_HEIGHT}
        x={centerPoint}
        y={centerPoint}
        roleSwitch={true}
        roleSwitchTarget="galaxy"
        detailPanelRef={detailPanelRef}
        switchBackRoles={switchBackRoles}
        handleKeyDown={userRoleHandleKeyDown}
      />
      <GroupListSvg
        ref={groupListRef}
        group={groups.find(grp => grp.key === selectedGroup?.id)}
        x={selectedGroup.x}
        y={selectedGroup.y}
        openedFromHtmlElement={selectedGroupRef.current}
        iconSet={icons}
        selected={selectedRoleId}
        onSelect={role => {
          setShowRoleList(false);
          setSelectedRoleId(role);
        }}
        isOpen={showRoleList}
        setIsOpen={setShowRoleList}
        hideMatchingDetails={hideMatchingDetails}
      />
    </>
  );
};

GalaxyViewRoleItemsSvg.propTypes = {
  roles: PropTypes.array,
  groups: PropTypes.array,
  startingRole: PropTypes.object,
  setRoles: PropTypes.func,
  setGroups: PropTypes.func,
  centerPoint: PropTypes.number,
  animatePill: PropTypes.object,
  setAnimatePill: PropTypes.func,
  selectedRoleId: PropTypes.string,
  setSelectedRoleId: PropTypes.func,
  switchBackRoles: PropTypes.array,
  detailPanelRef: PropTypes.object,
  hideMatchingDetails: PropTypes.bool
};

export default GalaxyViewRoleItemsSvg;
