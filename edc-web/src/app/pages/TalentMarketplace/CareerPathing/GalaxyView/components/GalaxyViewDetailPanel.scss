@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.galaxy-view-list__panel {
  overflow: visible;
  background-color: var(--ed-white);
  width: 420px;
  max-width: 100%;
  box-shadow: var(--ed-shadow-base);
  border-radius: var(--ed-border-radius-lg);
  position: sticky;
  transition-property: all;
  transition-timing-function: cubic-bezier(0, 0, 0.58, 1);
  transition-duration: 150ms;

  &.--collapsed {
    width: 20px;
    .galaxy-view-list__panel-expand-button ~ * {
      visibility: hidden;
    }
  }

  &-expand-button {
    position: absolute;
    top: 50%;
    transform: translate(-50%);
    border-radius: var(--ed-border-radius-md);
    background-color: var(--ed-white);
    right: -40px;
    z-index: 3;

    .galaxy-view-list__collapse-btn {
      padding: var(--ed-spacing-4xs);
      padding-left: var(--ed-spacing-5xs);
      cursor: pointer;
      svg {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }

  &-header {
    display: flex;
    justify-content: space-between;
    padding: var(--ed-spacing-base);
    white-space: nowrap;
    #galaxy-view-list__collapse-btn-text {
      font-size: var(--ed-font-size-lg);
      font-weight: var(--ed-font-weight-bold);
      color: var(--ed-black);
    }
  }

  &-content {
    padding: var(--ed-spacing-base);
    padding-top: 0;
  }

  .galaxy-view-list__item {
    border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
  }

  .galaxy-view-list__search {
    display: flex;
    gap: var(--ed-spacing-2xs);
    padding-bottom: var(--ed-spacing-4xs);

    .ed-search {
      flex-grow: 1;
    }

    .ed-btn-v2 {
      position: relative;

      .galaxy-view-list__selected-filter-count {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(25%, -25%);
        border-radius: var(--ed-border-radius-circle);
        background-color: var(--ed-primary-base);
        width: 1.25rem;
        height: 1.25rem;
        color: var(--ed-white);
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .galaxy-view-list__scrollable {
    margin: 0;
    background-color: var(--ed-white);
    overflow-x: visible;
    overflow-y: auto;
    position: relative;
    z-index: 1;
    padding: 0 var(--ed-spacing-5xs);
    @include modern-scrollbars(6px, var(--ed-neutral-6), transparent, 5px);

    .h-role-card__row {
      line-height: var(--ed-line-height-base);
    }
  }

  .galaxy-view-list__empty {
    display: flex;
    flex-direction: column;
    gap: var(--ed-spacing-base);
    text-align: center;
    padding: var(--ed-spacing-base);

    h2 {
      font-size: var(--ed-font-size-sm) !important;
      font-weight: var(--ed-font-weight-black);
      color: var(--ed-black);
    }

    p {
      font-size: var(--ed-font-size-base);
      color: var(--ed-gray-6);
    }
  }
}

@media screen and (max-width: $breakpoint-sm) {
  .galaxy-view-list__panel-expand-button ~ * {
    visibility: visible !important;
  }
}
