import { useState, useMemo, useEffect, useRef } from 'react';
import debounce from 'lodash/debounce';
import PropTypes from 'prop-types';
import GalaxyViewBackgroundSvg from './components/GalaxyViewBackgroundSvg';
import GalaxyViewRoleItemsSvg from './components/GalaxyViewRoleItemsSvg';
import GalaxyViewDetailPanel from './components/GalaxyViewDetailPanel';
import PanControls from './components/PanControls';
import ZoomControls from './components/ZoomControls';
import {
  // DEFAULT_EXPAND_FROM_LEVEL, currently not used due to TM-6846
  RADIAL_DIFFERENCE,
  MAX_GALAXYVIEW_WIDTH,
  RADIAL_DIFF_ZOOM_STEP,
  INNER_CIRCLE_RADIUS,
  INNER_CIRCLE_ZOOM_STEP,
  deg2rad,
  calculateAngleDifference,
  calculateArcDistance,
  MOVE_EXPANDED_LIMITS,
  CLUSTER_LIMIT
  //logCollisions
} from './helpers.js';
import { translatr } from 'centralized-design-system/src/Translatr';
import './GalaxyView.scss';

const initialAnimState = { anim: true, animMode: 'anim-mode--1' };

const GalaxyView = ({
  initialData,
  startingRole,
  currentUserLang,
  switchBackRoles,
  loading,
  isCareerPathEnabled,
  hideMatchingDetails
}) => {
  /* workspace */
  let initialSize =
    window?.innerWidth > MAX_GALAXYVIEW_WIDTH ? MAX_GALAXYVIEW_WIDTH : window?.innerWidth;
  const [svgSize, setSvgSize] = useState(initialSize);
  const [bgConfig, setBgConfig] = useState({});
  const [zoom, setZoom] = useState(0);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [animatePill, setAnimatePill] = useState(initialAnimState);
  const centerPoint = svgSize / 2;
  const detailPanelRef = useRef(null);
  const resizeHandler = debounce(() => {
    setSvgSize(
      window?.innerWidth > MAX_GALAXYVIEW_WIDTH ? MAX_GALAXYVIEW_WIDTH : window?.innerWidth
    );
  }, 300);

  const [isGalaxyViewDetailPanelExpanded, setIsGalaxyViewDetailPanelExpanded] = useState(true);

  const getMoveLineLabel = moveNr => {
    return moveNr === 1
      ? translatr('web.talentmarketplace.career-path', 'Move', { cnt: moveNr })
      : translatr('web.talentmarketplace.career-path', 'Moves', { cnt: moveNr });
  };

  /* roles states */
  const [rolesByJobFamily, setRolesByJobFamily] = useState(null);
  const [initialRoles, setInitialRoles] = useState(null);
  const [roles, setRoles] = useState([]); // computated data for rendering
  const [rolesSortedByMatching, setRolesSortedByMatching] = useState([]); // computated data for rendering
  const [groups, setGroups] = useState([]); // computated data for rendering
  const [selectedRoleId, setSelectedRoleId] = useState(null);

  const [searchStr, setSearchStr] = useState('');

  const [grouped, setGrouped] = useState(undefined);

  const onSelectedRoleChange = id => {
    setSelectedRoleId(id);

    if (id !== null) {
      setIsGalaxyViewDetailPanelExpanded(true);
    }
  };

  const handleSearch = debounce(str => {
    setSearchStr(str);
    setGrouped(false);
    setSelectedRoleId(null);
  }, 500);

  /* Galaxy View SVG size change onResize screen */
  useEffect(() => {
    window.addEventListener('resize', resizeHandler);
    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  }, []);

  /* Galaxy View Data recalculation after zoom and loading state changed (needed for ungrouping!)
   * on zoom action set initial data to make grouping/ungrouping again
   * same for loading data after filterchange
   **/
  useEffect(() => {
    setAnimatePill(initialAnimState);
    setInitialRoles(loading ? null : initialData);
    setGrouped(loading ? undefined : false);
  }, [loading]); // groups don't change on zoom after removing collision detection

  /* Main calculation part with data memoization */
  let memoizedData = useMemo(() => {
    if (loading) {
      return {
        computedRoles: [],
        jobFamilyRadialLines: [],
        radialDifference: RADIAL_DIFFERENCE
      };
    }

    let jobFamilyRadialLines = [];

    let computedRoles = [];
    let orderIndex = 0;
    const centralAngle = 360;

    // Computation for Role co-ordinates
    let currentMove = 0;
    let indexRoleInMove = 1;
    let indexRoleInGroup = 0;
    let groupOnMove = 0;
    computedRoles = computedRoles.concat(
      // Sort by noOfMoves, then in each move sort by overallScore and than by isAspirational
      // After sorting, assign index to each role in a given Move
      rolesByJobFamily
        .sort((a, b) => {
          // Sorting in descending order based on overallScore
          const overallScoreComparison = b.overallScore - a.overallScore;
          // If overallScore is different, return the comparison result for overallScore
          if (overallScoreComparison !== 0) {
            return overallScoreComparison;
          }
          // If overallScore is the same, sort based on isAspirational
          if (a.isAspirational && !b.isAspirational) {
            return -1;
          } else if (!a.isAspirational && b.isAspirational) {
            return 1;
          }
          // If both overallScore and isAspirational are the same, return 0
          return 0;
        })
        .sort((a, b) => a.noOfMoves - b.noOfMoves)
        .map(role => {
          if (!role.groupedOnMove || role.noOfMoves !== groupOnMove) {
            if (role.groupedOnMove) {
              groupOnMove = role.noOfMoves;
            }

            if (!role.groupedOnMove || !indexRoleInGroup) {
              ++orderIndex;
            }

            if (role.noOfMoves === currentMove) {
              ++indexRoleInMove;

              if (role.groupedOnMove) {
                ++indexRoleInGroup;
              }
            } else {
              currentMove = role.noOfMoves;
              indexRoleInMove = 1;
              indexRoleInGroup = 0;
            }
          }

          let isExpanded = true; // no restriction for isExpanded due to TM-6846
          // currently not used due to TM-6846
          // let isExpanded = false;
          // if (role.overallScore >= DEFAULT_EXPAND_FROM_LEVEL || role.isAspirational) {
          //   isExpanded = true;
          // }

          const allRolesOnMove = rolesByJobFamily.filter(el => el.noOfMoves === role.noOfMoves);
          const moveCount =
            allRolesOnMove.filter(el => !el.groupedOnMove).length < allRolesOnMove.length
              ? allRolesOnMove.filter(el => !el.groupedOnMove).length + 1
              : allRolesOnMove.length;

          // calculate angle
          let angle = Math.round(((indexRoleInMove - 1) * centralAngle) / moveCount);
          const angleOffset = (currentMove % 2 ? 1 : -1) * (10 - currentMove); // start point, +7 degrees on even moves and -7 for odd ones
          angle = angleOffset + angle;
          angle = 360 - angle; // flip the quadrants, move anti-clockwise

          return {
            orderIndex,
            ...role,
            jobFamilyName: role.jobFamily,
            angle,
            isExpanded
          };
        })
    );

    return {
      computedRoles,
      jobFamilyRadialLines,
      radialDifference: RADIAL_DIFFERENCE
    };
  }, [rolesByJobFamily, svgSize]); // Because this data will come from API eventually

  /* Effect after first data computation, checking for collisions, adding
   * Collision detection based on neighbours in predefinde angle range
   */
  useEffect(() => {
    const radialDifferenceWithZoom = memoizedData.radialDifference + zoom * RADIAL_DIFF_ZOOM_STEP;
    const innerCircleRadiusWithZoom =
      INNER_CIRCLE_RADIUS + (zoom < -2 ? -2 : zoom) * INNER_CIRCLE_ZOOM_STEP;

    // Calculation depends on Zoom
    let prevElement;
    let computedRoles = memoizedData.computedRoles.map(role => {
      const r = innerCircleRadiusWithZoom + radialDifferenceWithZoom * (role.noOfMoves - 1);

      let distanceOnMove = undefined;
      const roleX = centerPoint + Math.round(r * Math.cos(deg2rad(role.angle)));
      const roleY = centerPoint + Math.round(r * Math.sin(deg2rad(role.angle)));

      // count only once - check on previous
      if (
        prevElement &&
        role.jobFamilyName === prevElement.jobFamilyName &&
        role.noOfMoves === prevElement.noOfMoves
      ) {
        const angleDiff = calculateAngleDifference(role.angle, prevElement.angle);
        distanceOnMove = prevElement.distance || calculateArcDistance(angleDiff, r);
      }
      const newRole = {
        ...role,
        r,
        x: roleX,
        y: roleY,
        distance: distanceOnMove
      };
      prevElement = newRole;
      return newRole;
    });

    // Gather Groups Data
    const extractedGroups = new Map();
    computedRoles
      .filter(rl => rl.groupedOnMove)
      .forEach(r => {
        if (!extractedGroups.has(`${r.noOfMoves}`)) {
          extractedGroups.set(`${r.noOfMoves}`, {
            orderIndex: r.orderIndex,
            noOfMoves: r.noOfMoves,
            items: [r]
          });
        } else {
          const gr = extractedGroups.get(`${r.noOfMoves}`);
          gr.items.push(r);
        }
      });
    // Compute background circles data
    let bgFillArray = [];
    let moveLines = [];
    let moveLineStartX = centerPoint + 40;
    let moveLineEndX = centerPoint + 40 + innerCircleRadiusWithZoom;
    for (let i = 0; i < 4; i++) {
      bgFillArray.push({
        index: i,
        cx: centerPoint,
        cy: centerPoint,
        r: innerCircleRadiusWithZoom + radialDifferenceWithZoom * i
      });
      moveLines.push({
        x1: moveLineStartX,
        y1: centerPoint + 9,
        x2: moveLineEndX,
        y2: centerPoint + 9,
        text: getMoveLineLabel(i + 1),
        offset: i === 0 ? innerCircleRadiusWithZoom - 15 : radialDifferenceWithZoom - 15
      });
      moveLineStartX = moveLineEndX;
      moveLineEndX = moveLineEndX + radialDifferenceWithZoom;
    }
    bgFillArray.reverse(); // Reverse to put smaller circles on top of bigger circle

    setBgConfig({
      radialDifference: radialDifferenceWithZoom,
      bgFillArray,
      moveLines: moveLines.slice(0, 4)
    });
    setRoles(computedRoles);
    setRolesSortedByMatching(computedRoles.sort((a, b) => b.overallScore - a.overallScore));
    setGroups(Array.from(extractedGroups, ([key, value]) => ({ key, value })));

    /* collistions LOG */
    //logCollisions(clusteredRoles);
    /* collistions LOG */
  }, [zoom, svgSize, memoizedData.computedRoles]);

  useEffect(() => {
    if (grouped !== false || initialRoles === null) {
      return;
    }
    const computedRoles = initialRoles.filter(role => {
      if (searchStr === '') {
        return true;
      }
      return role.name.toLowerCase().includes(searchStr.toLowerCase());
    });
    let copiedArray = JSON.parse(JSON.stringify(computedRoles));
    const mapByNoOfMoves = computedRoles.reduce((acc, item) => {
      const { noOfMoves } = item;
      if (!acc[noOfMoves]) {
        acc[noOfMoves] = [];
      }
      acc[noOfMoves].push(item);
      return acc;
    }, {});

    const potentialGroups = new Map();
    const itemsToHide = [];
    for (let mv in mapByNoOfMoves) {
      const mvLength = mapByNoOfMoves[mv].length;
      for (let idx = 0; idx < mvLength; idx++) {
        const role = mapByNoOfMoves[mv][idx];
        let groupObj;
        if (
          !role.groupedOnMove &&
          mvLength > MOVE_EXPANDED_LIMITS[mv - 1] + 1 && // restriction for number of expanded element
          idx >= MOVE_EXPANDED_LIMITS[mv - 1] // start adding to group not expanded elements
        ) {
          if (potentialGroups.has(role.noOfMoves)) {
            if (potentialGroups.get(role.noOfMoves).length >= CLUSTER_LIMIT) {
              // restriction for cluster size
              itemsToHide.push(role.id);
            } else {
              potentialGroups.get(role.noOfMoves).push(role.id);
            }
          } else {
            groupObj = [role.id];
            potentialGroups.set(role.noOfMoves, groupObj);
          }
        }
      }
    }
    copiedArray = copiedArray.filter(item => !itemsToHide.includes(item.id)); // delete items over cluster size

    const pgItems = Array.from(potentialGroups.values()).flat();
    copiedArray.forEach((role, idx) => {
      if (pgItems.includes(role.id)) {
        copiedArray[idx] = {
          ...role,
          groupedOnMove: true
        };
      }
    });

    setRolesByJobFamily(copiedArray);
    setGrouped(true);
  }, [grouped, initialRoles, searchStr]);

  return (
    <div className="tm__galaxy-view" style={{ height: `${svgSize}px` }}>
      <svg
        width={svgSize}
        height={svgSize}
        viewBox={`${pan.x} ${pan.y} ${svgSize} ${svgSize}`}
        role="presentation"
      >
        <GalaxyViewBackgroundSvg
          centerPoint={centerPoint}
          svgSize={svgSize}
          bgConfig={bgConfig}
          sectionsConfig={loading ? [] : memoizedData.jobFamilyRadialLines}
          loading={loading}
          radialDifference={memoizedData.radialDifference}
          zoom={zoom}
        />
        {!loading && (
          <GalaxyViewRoleItemsSvg
            detailPanelRef={detailPanelRef}
            roles={roles}
            setRoles={setRoles}
            groups={groups}
            setGroups={setGroups}
            animatePill={animatePill}
            setAnimatePill={setAnimatePill}
            startingRole={startingRole}
            switchBackRoles={switchBackRoles}
            centerPoint={centerPoint}
            selectedRoleId={selectedRoleId}
            setSelectedRoleId={onSelectedRoleChange}
            hideMatchingDetails={hideMatchingDetails}
          />
        )}
      </svg>
      {/* Controls */}
      {!loading && (
        <>
          <div className="tm__galaxy-view--detail-panel" style={{ height: `${svgSize}px` }}>
            <GalaxyViewDetailPanel
              ref={detailPanelRef}
              roles={rolesSortedByMatching}
              selected={selectedRoleId}
              setSelected={setSelectedRoleId}
              startingRole={startingRole}
              maxHeight={svgSize}
              currentUserLang={currentUserLang}
              isCareerPathEnabled={isCareerPathEnabled}
              isExpanded={isGalaxyViewDetailPanelExpanded}
              setIsExpanded={setIsGalaxyViewDetailPanelExpanded}
              onSearch={handleSearch}
              searchStr={searchStr}
              hideMatchingDetails={hideMatchingDetails}
            />
          </div>
          <div className="tm__galaxy-view--controls">
            <PanControls x={pan.x} y={pan.y} onPan={panChange => setPan(panChange)}></PanControls>
            <ZoomControls
              zoom={zoom}
              onZoom={zoomChange => {
                setZoom(zoomChange);
              }}
            ></ZoomControls>
          </div>
        </>
      )}
    </div>
  );
};

GalaxyView.propTypes = {
  startingRole: PropTypes.object,
  initialData: PropTypes.array,
  currentUserLang: PropTypes.string,
  switchBackRoles: PropTypes.array,
  loading: PropTypes.bool,
  isCareerPathEnabled: PropTypes.bool,
  hideMatchingDetails: PropTypes.bool
};

export default GalaxyView;
