@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

:root {
  --ed-gray-2-rgb: #{derive-rgb-values-from-hex(#e6e6e6)};
}

@keyframes gv-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.tm__galaxy-view {
  position: relative;

  > svg {
    max-width: 100%;
    margin-bottom: -118px;
  }

  foreignObject {
    overflow: visible;
  }
  .galaxy-view-bg__main-ring {
    fill: transparent;
    stroke: white;
  }
  .galaxy-view-bg__ring-nr-0 {
    fill: rgba(var(--ed-gray-2-rgb));
    stroke: white;
  }
  .galaxy-view-bg__ring-nr-1 {
    fill: rgba(var(--ed-gray-2-rgb), 0.7);
    stroke: white;
  }
  .galaxy-view-bg__ring-nr-2 {
    fill: rgba(var(--ed-gray-2-rgb), 0.5);
    stroke: white;
  }
  .galaxy-view-bg__ring-nr-3 {
    fill: rgba(var(--ed-gray-2-rgb), 0.3);
    stroke: white;
  }
  .galaxy-view-bg-movieline__text {
    font-size: var(--ed-font-size-2xs);
    fill: var(--ed-text-color-supporting);
  }
  .galaxy-view-bg-sectionline {
    line {
      stroke: rgb(156, 157, 181);
      fill: transparent;
      stroke-width: 2;
      stroke-linecap: butt;
      stroke-linejoin: miter;
      stroke-dasharray: 3;
    }
    textPath {
      font-size: rem-calc(13);
    }
  }

  .--animate {
    animation: gv-rotate 7s linear infinite;
  }
  &--controls {
    position: sticky;
    left: 100%;
    bottom: 1rem;
    width: rem-calc(68);
    margin-top: -104px; //height of the controls container
  }
}

@media screen and (max-width: $breakpoint-sm) {
  .tm__galaxy-view {
    height: auto !important;
    > svg {
      display: none;
    }
    & &--controls {
      display: none;
    }
    & &--detail-panel {
      height: auto !important;
    }
    .galaxy-view-list__panel {
      position: static;
      width: 100%;
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      &-expand-button {
        display: none;
      }
      .galaxy-view-list__scrollable {
        height: auto !important;
      }
    }
  }
}
