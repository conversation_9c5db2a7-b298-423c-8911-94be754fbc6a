@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.me__projects {
  display: flex;
  gap: var(--ed-spacing-base);

  @include max-screen-width($breakpoint-sm - 1) {
    .left-navigation,
    .me__projects-container-header {
      display: none;
    }

    &-container {
      margin: 0 var(--ed-spacing-sm);
    }
  }

  .tm-opportunities__no-data {
    margin: 0 auto;
    align-self: center;

    i {
      font-weight: var(--ed-font-weight-normal);
    }
  }

  .left-navigation {
    flex: 0 0 rem-calc(266);
    height: fit-content;
  }

  &-create-project-btn {
    @include max-screen-width($breakpoint-xs - 1) {
      display: block;
      margin-top: var(--ed-spacing-base);
      width: 100%;
    }
  }

  &-container {
    margin-bottom: var(--ed-spacing-3xl);
    display: flex;
    gap: var(--ed-spacing-base);
    flex-direction: column;
    width: 0;
    flex: 1;
    height: 100%;
    max-width: 57.5rem;

    & .checkbox {
      padding-left: var(--ed-spacing-5xs);
    }

    &.block {
      .ed-carousel,
      .job-cards {
        gap: var(--ed-spacing-sm);
      }

      .tm__project-card {
        border: var(--ed-border-size-sm) solid var(--ed-gray-2);
        box-shadow: none;
      }
    }

    & .ed-filter-with-radio-select-radio-list {
      & label {
        margin-bottom: var(--ed-spacing-2xs);
        & > div {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    &-header {
      display: flex;
      justify-content: space-between;

      .ed-input-container {
        display: flex;
        align-items: center;

        label {
          margin-right: var(--ed-spacing-2xs);
        }

        select {
          width: fit-content;
        }
      }
    }

    &-section-title {
      font-size: var(--ed-font-size-base) !important;
      font-weight: var(--ed-font-weight-bold) !important;
      line-height: var(--ed-line-height-md) !important;
    }
  }

  &-project-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ed-spacing-base);
    justify-content: left;

    .tm__project-card {
      flex: 0 0 100%;
      width: 0;

      @include min-screen-width($breakpoint-md) {
        flex: 0 0 48%;
      }

      @include min-screen-width($breakpoint-lg) {
        flex: 0 0 32%;
      }
    }
  }

  &-no-data-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  &-approval-container {
    padding: var(--ed-spacing-2xs);
    background-color: var(--ed-white);
    box-shadow: var(--ed-card-shadow-base);
    border-radius: var(--ed-border-radius-lg);
    margin: 0;

    .approval-title {
      font-size: var(--ed-font-size-lg) !important;
      color: var(--ed-text-color-primary);
      margin-bottom: var(--ed-spacing-2xs);
    }

    .desc {
      font-size: var(--ed-font-size-sm) !important;
      color: var(--ed-text-color-supporting) !important;
      margin-bottom: rem-calc(24);
    }

    .comment-wrapper {
      & > div {
        padding: 0 1rem;
        margin-top: rem-calc(-4);
      }
      div {
        min-height: unset;
        height: auto;
        max-height: unset;
        border-right: 0 !important;
        display: block;
        padding-top: 0;
      }
      .view-comments {
        padding-left: rem-calc(48);
        padding-bottom: var(--ed-spacing-base);
        font-size: rem-calc(14) !important;
        flex-direction: column;
        justify-content: flex-start;
        &__btn {
          position: relative;
          cursor: pointer;
        }
        .title {
          color: var(--ed-text-color-supporting);
          font-weight: var(--ed-font-weight-bold);
          white-space: nowrap;

          &:hover {
            text-decoration: underline;
          }
        }
        .show-comments {
          overflow-wrap: break-word;
          padding-top: rem-calc(6);
          padding-bottom: rem-calc(3);
          padding-right: rem-calc(50);
        }
        .icon-wrapper {
          color: var(--ed-text-color-supporting);
          font-size: rem-calc(24);
          position: absolute;
          top: rem-calc(-3);
        }
      }
      .rotate {
        transform: rotate(180deg);
        display: inline-block;
      }
    }

    .filter-and-sort {
      display: flex;

      .ed-input-container {
        display: flex;
        align-items: center;
        margin-bottom: rem-calc(24);

        label {
          margin-right: var(--ed-spacing-2xs);
        }

        select {
          width: fit-content;
        }
      }
    }

    .ed-table-wrapper {
      height: auto !important;

      table {
        table-layout: fixed;
      }

      td {
        div {
          border-bottom: none !important;
        }
        border-bottom: 0.0625rem solid var(--ed-table-border-color);
        background-color: var(--ed-white);
        background-clip: padding-box;
        vertical-align: top;
      }
    }

    .approval-user-table {
      .approval-request-column {
        border-right: 0;
        padding-bottom: var(--ed-spacing-base);

        .label {
          color: var(--ed-text-color-primary);
        }
      }

      thead th:first-child {
        z-index: 15 !important;
      }

      tbody tr td div {
        align-items: flex-start !important;
        min-height: 100% !important;
        max-height: 100% !important;
        height: fit-content !important;

        .view-more {
          margin: 0 !important;
        }
      }

      th,
      td {
        &:first-child {
          > div {
            background-color: var(--ed-white);
          }
        }
      }

      .applicant-row {
        height: inherit;
        color: var(--ed-text-color-primary);
        z-index: 10;

        & > div {
          padding-top: rem-calc(14);
          padding-bottom: rem-calc(14) !important;
          border-right: none;
          border-right: none;
          min-height: rem-calc(130);
          max-height: rem-calc(170);
        }

        div {
          font-size: var(--ed-font-size-sm);
        }

        &.date > div {
          padding-bottom: rem-calc(19);
        }

        .project-meta {
          display: flex;
          flex-direction: column;
          font-size: var(--ed-font-size-sm);
          align-items: flex-start;
        }

        .project-meta-info {
          min-height: 100% !important;
          max-height: 100% !important;
          height: 100% !important;
        }

        .user-details {
          padding: 0;
          height: rem-calc(100);
          max-height: rem-calc(100);

          .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            a {
              color: var(--ed-text-color-primary);
            }
          }

          .user-profile {
            border-radius: 100%;
            display: inline-block;
            margin-right: var(--ed-spacing-2xs);

            .user-avatar {
              margin: 0 !important;
            }

            .user-name {
              font-weight: normal;
            }

            .ed-avatar {
              display: inline-block;
              width: rem-calc(40);
              height: rem-calc(40);
              border-radius: 100%;
              object-fit: cover;
              min-height: auto;
              background-color: transparent !important;

              img {
                max-width: 100% !important;
                height: auto !important;
              }
            }
          }
        }
      }

      .om__sidebar-meta div {
        max-height: rem-calc(14) !important;
      }

      .supporting-text {
        color: var(--ed-text-color-supporting);
        font-size: var(--ed-font-size-sm);
      }
    }
  }

  .table-body {
    min-height: rem-calc(375);

    .stats-skelton {
      margin: rem-calc(20) auto;
    }

    .approval-skelton {
      display: flex;
      column-gap: rem-calc(20);

      div {
        flex: 1;
      }
    }
  }

  .loading-skelton {
    background-color: var(--ed-white);
    padding: var(--ed-spacing-base);
    border-radius: var(--ed-border-radius-lg);

    &--btn {
      padding: var(--ed-spacing-xs);
      width: rem-calc(55);
    }
  }

  .text-wrapper {
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .no-data-section {
    margin-bottom: rem-calc(12);
    margin-top: var(--ed-spacing-xl);
  }

  a,
  a:hover {
    color: var(--ed-text-color-primary) !important;
  }
}

.reload-data-btn {
  color: var(--ed-text-color-info);
  text-decoration: underline !important;

  &:hover {
    cursor: pointer;
  }
}
