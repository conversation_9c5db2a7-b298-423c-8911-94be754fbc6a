@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-career-wizard {
  .flex-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-column {
    display: flex;
    flex-direction: column;
    gap: var(--ed-spacing-2xs);
  }

  .mt-8 {
    margin-top: var(--ed-spacing-2xs);
  }

  .mt-16 {
    margin-top: var(--ed-spacing-base);
  }

  .mb-24 {
    margin-bottom: var(--ed-spacing-xl);
  }

  .pt-30 {
    padding-top: rem-calc(30);
  }

  .pt-20 {
    padding-top: var(--ed-spacing-lg);
  }

  .ed-font-size-base {
    font-size: var(--ed-font-size-base);
  }

  .ed-text-color {
    color: var(--ed-text-color-primary);
  }

  .ed-supporting-color {
    color: var(--ed-text-color-supporting);
  }

  .capitalize-first-letter {
    text-transform: capitalize;
  }

  .text-center {
    text-align: center;
  }

  .closable-error,
  .closable-warning {
    max-width: unset !important;
  }

  .career-tabs {
    display: flex;
    gap: var(--ed-spacing-xs);
  }

  .ed-career-wizard-header {
    padding: var(--ed-spacing-lg);
    pointer-events: all;
    border-bottom: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    > div {
      display: flex;
      justify-content: space-between;
      > label {
        margin-bottom: 0 !important;
        font-size: var(--ed-font-size-xl);
        font-weight: var(--ed-font-weight-bold);
        color: var(--ed-text-color-primary);
      }

      .ed-career-wizard-header-close-button {
        font-size: 34px;
        color: var(--ed-text-color-supporting);
        width: 34px;
        cursor: pointer;
        // For touch screen and to make focus box a square
        width: rem-calc(44);
        overflow: hidden;
        &:hover,
        &:focus {
          color: var(--ed-negative-darken-3);
        }
      }
    }

    .desc {
      font-size: var(--ed-font-size-supporting);
      color: var(--ed-alert-close-button-color);
    }
  }

  .empty-container {
    display: flex;
    height: rem-calc(100);
    align-items: center;
    justify-content: center;
  }

  .experience-container {
    .work-history {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: var(--ed-spacing-lg);
      gap: var(--ed-spacing-4xs);

      > div {
        flex: 1;
        .left-hr-text:after {
          font-size: var(--ed-font-size-2xs);
        }
        .left-hr-text:before {
          background-color: var(--ed-alert-close-button-color);
        }

        @include mobile() {
          width: 85%;
        }
      }

      > button {
        height: rem-calc(38);
        font-size: var(--ed-font-size-supporting);
        color: var(--ed-text-color-primary);
      }

      .web-btn {
        @include mobile() {
          display: none;
        }
      }
      .mobile-btn {
        display: none;
        @include mobile() {
          display: flex;
          font-size: var(--ed-font-size-3xl);
          justify-content: center;
          align-items: center;
          min-width: unset;
        }
      }
    }

    .work-histories-container {
      padding-top: var(--ed-spacing-lg);
      @include mobile() {
        padding-top: 0;
      }
    }
  }

  .aspirations-dropdown-container {
    width: 100%;
    height: auto;
    margin-bottom: var(--ed-spacing-lg);
    padding: 0;

    legend {
      padding-bottom: var(--ed-spacing-3xs);
    }

    &.preferences-multiselect-component {
      margin-top: var(--ed-spacing-base);
    }

    .ed-title {
      font-size: var(--ed-font-size-base);
      font-weight: var(--ed-font-weight-bold);
      color: var(--ed-text-color-primary);
      display: inline-block;
      margin-bottom: 0 !important;
    }

    .ed-optional {
      font-size: var(--ed-font-size-base);
      color: var(--ed-text-color-supporting);
    }

    .relocation {
      display: flex;
      gap: rem-calc(290);
      margin-top: var(--ed-spacing-lg);
      text-align: center;
    }
    .aspitarions-options-list {
      &.option-list-scrollable {
        height: 13rem;
        overflow-y: auto;
        border-bottom: var(--ed-border-size-sm) solid
          color-mix(in srgb, var(--ed-border-color), #fff 25%);
        border-top: var(--ed-border-size-sm) solid
          color-mix(in srgb, var(--ed-border-color), #fff 25%);
      }
    }
    .options-list-subtitle {
      color: var(--ed-text-color-supporting);
      font-size: var(--ed-font-size-sm);
      margin-bottom: var(--ed-spacing-xs);
    }
  }

  .ed-dialog-modal-footer {
    justify-content: space-between !important;
    border-top: none !important;

    @include mobile() {
      flex-direction: column;
    }

    > div {
      @include mobile() {
        display: flex;
        justify-content: space-between;
        margin-top: var(--ed-spacing-base);
      }

      > button {
        @include mobile() {
          min-width: 48%;
        }
      }
    }
  }

  .ed-dialog-modal-header {
    background-color: var(--ed-border-color-light);
  }

  .wk-history-resume-upload {
    .content .ed-dialog-modal-header {
      height: 4rem;
      background-color: var(--ed-body-bg-color);

      .ed-dialog-modal-header-close-button {
        font-size: 34px;
      }
    }

    .drag-drop-zone {
      display: flex;
      justify-content: center;
      flex-direction: column;
      border-radius: var(--ed-border-radius-md);
      border: var(-ed-border-size-sm) dashed var(--ed-border-color);
      text-align: center;

      .info {
        margin: 0.925rem;
        border: var(--ed-border-size-sm) solid var(--ed-info-5);
        background-color: #f4f1f9;
        color: var(--ed-info-5);
        border: var(-ed-border-size-sm) solid var(--ed-info-5);
        max-width: none;
      }

      .icon-upload {
        margin-top: rem-calc(54);
        margin-bottom: var(--ed-spacing-base);
        font-size: var(--ed-font-size-3xl);
        color: var(--ed-text-color-supporting);
      }

      .upload-resume-btn {
        display: flex;
        flex-direction: column;
        align-items: center;

        > label {
          color: var(--ed-text-color-supporting);

          &:first-of-type {
            margin-top: var(--ed-spacing-4xs);
          }
          &:last-of-type {
            margin: var(--ed-spacing-base) 0;
          }
        }

        > button {
          margin-bottom: rem-calc(54);
          margin-left: rem-calc(5);
        }
      }

      .replace-file-container {
        justify-content: center;
        height: rem-calc(293);

        > div {
          align-items: center;
          gap: 0;

          label {
            color: var(--ed-text-color-primary);

            &:not(:first-of-type) {
              color: var(--ed-negative-1);
            }
          }

          .icon-noun-replace-file::before {
            padding-right: var(--ed-spacing-2xs);
            font-size: var(--ed-font-size-xl);
          }
          button {
            word-spacing: var(--ed-spacing-4xs);
          }
        }
      }

      .icon-x-mark-Close {
        &::before {
          font-weight: var(--ed-font-weight-bold);
        }
        &:hover {
          color: var(--ed-state-active-color);
          cursor: pointer;
        }
      }
    }
    .footer {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: var(--ed-spacing-base);
      padding-top: var(--ed-spacing-lg);
    }
  }

  .skills-container {
    .add-skills-you-have-label {
      color: var(--ed-text-color-primary);
      font-size: var(--ed-font-size-lg);
      margin-bottom: var(--ed-spacing-base);
    }

    .skill-with-proficiency-level {
      margin-top: var(--ed-spacing-sm);

      &__title {
        margin-bottom: var(--ed-spacing-4xs);
        color: var(--ed-text-color-primary);
        font-size: var(--ed-font-family-base);
      }

      &__hint {
        font-size: var(--ed-font-size-supporting);
        margin-bottom: var(--ed-spacing-3xs);
        margin-top: var(--ed-spacing-3xs);
      }
    }

    .ed-multi-select {
      &__control--is-focused {
        border-color: var(--ed-text-color-primary) !important;
      }

      .ed-multi-select__multi-value {
        align-items: center;

        .ed-multi-select__multi-value__label {
          text-transform: capitalize;
          padding: 0.1rem 0.4rem 0.15rem 0.6rem;
        }

        .ed-multi-select__multi-value__remove {
          padding: 0 0.6rem 0 0;

          svg {
            cursor: pointer;
            color: var(--ed-text-color-supporting);
            border-radius: var(--ed-border-radius-circle);
            border: 0.0125rem solid var(--ed-text-color-supporting);
          }

          svg:hover {
            color: var(--ed-negative-darken-3);
            border-color: var(--ed-negative-darken-3);
          }
        }
      }
    }
    .skills-without-level {
      &__title {
        color: var(--ed-text-color-primary);
        margin-bottom: var(--ed-spacing-4xs);
      }

      &__content {
        border: var(--ed-border-size-sm) solid var(--ed-border-color);
        border-radius: var(--ed-border-radius-lg);
        padding: var(--ed-spacing-5xs) var(--ed-spacing-2xs);
        display: flex;
        flex-wrap: wrap;

        &__tag-wrapper {
          display: flex;
          align-items: center;
          margin: 0 var(--ed-spacing-5xs);
          text-transform: capitalize;

          .ed-tag-container {
            display: flex;
            align-items: center;
            padding: 0.1rem 0.6rem 0.15rem 0.6rem;
            border-color: var(--ed-border-color);

            .tag-close-icon {
              display: flex;
              padding: 0;
              margin: 0 0 0 0.4rem;
              border-radius: var(--ed-border-radius-circle);
              border: 0.0125rem solid var(--ed-text-color-supporting);

              .icon-x-mark-Close {
                font-size: 0.65rem;
                font-weight: var(--ed-font-weight-bold);
                border-radius: var(--ed-border-radius-circle);
                padding: 0.1rem;
                margin: 0;
                color: var(--ed-text-color-supporting);
              }

              .icon-x-mark-Close:hover {
                color: var(--ed-negative-darken-3);
              }
            }

            .tag-close-icon:hover {
              border-color: var(--ed-negative-darken-3);
            }
          }
        }
      }
    }

    .search-select-container {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding-bottom: var(--ed-spacing-3xl);
      gap: var(--ed-spacing-base);

      @include mobile() {
        display: block;
      }

      .level-btn-container {
        display: flex;
        align-items: flex-end;
        gap: var(--ed-spacing-base);

        @include mobile() {
          margin-top: var(--ed-spacing-base);
        }

        > button {
          width: rem-calc(100);
          height: rem-calc(38);
          @include mobile() {
            width: 50%;
          }
        }
      }

      .ed-input-container {
        &:first-of-type {
          flex: 3;
          @include mobile() {
            width: 100%;
          }
        }
        &:last-of-type {
          flex: 2;
          @include mobile() {
            flex: 1;
          }
        }
      }
    }

    .level-skills-container {
      display: flex;
      margin-top: var(--ed-spacing-base);
      margin-bottom: var(--ed-spacing-3xl);

      @include mobile() {
        display: block;
      }

      > div {
        width: 33%;
        @include mobile() {
          width: 100%;
        }

        .level {
          display: flex;
          justify-content: center;
          gap: var(--ed-spacing-4xs);

          @include mobile() {
            justify-content: left;
          }

          > span:last-of-type {
            font-size: var(--ed-font-size-supporting);
            color: var(--ed-text-color-primary);
            padding-left: var(--ed-spacing-2xs);
          }
          .icon-oval-fill::before {
            font-size: var(--ed-spacing-4xs);
            color: var(--ed-state-active-color);
          }

          .icon-oval::before {
            font-size: var(--ed-spacing-4xs);
          }
        }

        .selected-skills {
          text-align: center;
          min-height: rem-calc(100);
          padding: var(--ed-spacing-base) var(--ed-spacing-4xs) 0 var(--ed-spacing-4xs);

          @include mobile() {
            display: flex;
            flex-wrap: wrap;
            min-height: rem-calc(50);
          }

          .ed-tooltip {
            width: 96%;
            @include mobile() {
              width: auto;
            }
          }

          .ed-tag-container:hover {
            background-color: var(--ed-neutral-7);
            border-color: var(--ed-negative-1);
            .icon-x-mark-Close {
              color: var(--ed-negative-1);
            }
          }
        }
      }
      > div {
        &:first-of-type {
          @include mobile() {
            margin-bottom: var(--ed-spacing-2xs);
            padding-bottom: var(--ed-spacing-2xs);
            border-bottom: var(--ed-border-size-sm) dashed var(--ed-border-color);
          }
        }

        &:nth-of-type(2) {
          border-left: var(--ed-border-size-sm) dashed var(--ed-border-color);
          border-right: var(--ed-border-size-sm) dashed var(--ed-border-color);
          @include mobile() {
            &:nth-of-type(2) {
              border-left: none;
              border-right: none;
              padding-bottom: var(--ed-spacing-2xs);
              margin-bottom: var(--ed-spacing-2xs);
              border-bottom: var(--ed-border-size-sm) dashed var(--ed-border-color);
            }
          }
        }
      }
    }

    .unknown-level-skills-container {
      padding: 0;

      > label:first-of-type {
        margin-bottom: 0;
      }

      .selected-unlevel-skills {
        padding: var(--ed-spacing-base) var(--ed-spacing-4xs) 0 var(--ed-spacing-4xs);

        .ed-tag-container:hover {
          background-color: var(--ed-neutral-7);
          border-color: var(--ed-negative-1);
          .icon-x-mark-Close {
            color: var(--ed-negative-1);
          }
        }
      }
    }
  }
}

.upload-cvs {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--ed-spacing-2xs);
  min-height: rem-calc(40);
  border: var(--ed-border-size-sm) solid var(--ed-info-5);
  background-color: #f4f1f9;
  color: var(--ed-info-5);
  border-radius: var(--ed-border-radius-md);
  margin-bottom: var(--ed-spacing-lg);

  span {
    @include mobile() {
      padding: var(--ed-spacing-2xs);
    }

    margin: var(--ed-spacing-2xs);
  }

  &__button {
    color: var(--ed-info-5);
    text-decoration: underline;
    margin: var(--ed-spacing-2xs);

    @include mobile() {
      min-width: 6rem;
    }

    &:hover {
      cursor: pointer;
    }
  }
}

.career-profile-warning-container {
  .alert-body {
    padding-top: var(--ed-spacing-2xs) !important;
  }

  h5 {
    margin-bottom: 0;
  }
}
