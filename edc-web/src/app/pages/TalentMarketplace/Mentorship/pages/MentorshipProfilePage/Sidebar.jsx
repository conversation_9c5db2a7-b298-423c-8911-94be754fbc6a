import React, { useEffect } from 'react';
import { array, bool, func, object, string } from 'prop-types';
import { connect } from 'react-redux';
import _ from 'lodash';

import { MENTORSHIP_STATUS } from 'edc-web-sdk/requests/careerOportunities.v2';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';

import ApplicationStatusLabel from '@components/MentorCard/shared/ApplicationStatusLabel';
import { openRequestMentorshipModal } from 'actions/modalActions';
import { SectionTitle } from 'opportunity-marketplace/shared/Section';
import Meta from 'opportunity-marketplace/shared/SidebarMeta';
import { ROOT_CLASS } from './types';
import { orgVisibility } from '../../../../../actions/organizationsActions';
import { sortByIndex } from '../../../../Projects/ProjectForm/helpers';
import SidebarOrgMeta from '../../../shared/SidebarOrgMeta';
import {
  LOCATION_ASSOCIATION,
  LOCATION_FIELDS,
  LOCATION_USAGE_OPTIONS,
  isLocationFieldVisible,
  showDurations,
  showTypes
} from '../../../helpers';

const Sidebar = ({
  mentorData,
  mentorId,
  mentorName,
  approvalStatus,
  isSelf,
  languages,
  loading,
  location,
  openRequestModal,
  timeZone,
  visibleOrgs,
  orgTypes,
  isOrgEnabled,
  divisions = [],
  types = [],
  durations = [],
  locationsEnabled,
  locationFieldAssociation,
  locationFieldVisibility
}) => {
  const [status, setStatus] = React.useState(approvalStatus);
  const onRequestMentorship = () =>
    openRequestModal(mentorId, mentorName, () => setStatus(MENTORSHIP_STATUS.PENDING), mentorData);

  useEffect(() => {
    setStatus(approvalStatus);
  }, [approvalStatus]);

  const renderOrg = () => {
    const visibleOrgTypes = sortByIndex(orgTypes.filter(org => visibleOrgs.includes(org.id)));

    return (
      <>
        {visibleOrgTypes.map(item => {
          const data = divisions.find(division => division?.orgType === item.id);
          const divisionHierarchy = [
            ...(data?.allParentDetail?.map(division => division.title) ?? []),
            data?.title
          ];
          if (data) {
            return (
              <SidebarOrgMeta
                key={item.id}
                icon="tree-graph"
                loading={loading}
                items={divisionHierarchy}
                child={data.title}
                title={item.label}
              />
            );
          }

          return null;
        })}
      </>
    );
  };

  return (
    <div className={`${ROOT_CLASS}--sidebar block`}>
      {loading ? (
        <div className="meta-detail">
          <Skeleton className="m-margin-bottom" width={150} height={33} />
          <Skeleton height={120} />
        </div>
      ) : (
        <>
          <SectionTitle className="m-margin-bottom">
            {translatr('web.talentmarketplace.main', 'MentorDetails', {
              tm_tm_mentor: omp('tm_tm_mentor')
            })}
          </SectionTitle>
          {locationsEnabled &&
            !_.isEmpty(location) &&
            locationFieldAssociation.includes(LOCATION_ASSOCIATION.USER) &&
            isLocationFieldVisible(
              locationFieldVisibility,
              LOCATION_FIELDS.NAME,
              LOCATION_USAGE_OPTIONS.MENTOR_PROFILE
            ) && (
              <Meta
                icon="pin-on-street-square"
                label={location?.title}
                title={translatr('web.talentmarketplace.main', 'Location')}
              />
            )}
          <Meta
            icon="globe-2"
            label={timeZone}
            title={translatr('web.talentmarketplace.main', 'TimeZone')}
          />
          {isOrgEnabled && renderOrg()}
          <Meta
            icon="globe"
            items={languages}
            title={translatr('web.talentmarketplace.main', 'Language')}
            maxItems={5}
          />
          {showTypes && types?.length > 0 && (
            <Meta
              icon="hand-holding-wave"
              label={types.map(type => type.translatedLabel).join(', ')}
              title={translatr('web.talentmarketplace.main', 'MentorshipType', {
                tm_tm_mentorship: omp('tm_tm_mentorship')
              })}
            />
          )}
          {showDurations && durations?.length > 0 && (
            <Meta
              icon="hourglass-half"
              label={durations.map(duration => duration.translatedLabel).join(', ')}
              title={translatr('web.talentmarketplace.main', 'MentorshipDuration', {
                tm_tm_mentorship: omp('tm_tm_mentorship')
              })}
            />
          )}
          {!isSelf && (
            <div className={`${ROOT_CLASS}--sidebar-footer m-margin-top`}>
              {status ? (
                <ApplicationStatusLabel status={status} />
              ) : (
                <button className="ed-btn ed-btn-primary" onClick={onRequestMentorship}>
                  {translatr('web.talentmarketplace.main', 'RequestMentorship', {
                    tm_tm_mentorship: omp('tm_tm_mentorship')
                  })}
                </button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

Sidebar.propTypes = {
  mentorData: object,
  mentorId: string,
  mentorName: string,
  approvalStatus: string,
  isSelf: bool,
  languages: array,
  loading: bool,
  location: string,
  openRequestModal: func.isRequired,
  timeZone: string,
  isOrgEnabled: bool,
  visibleOrgs: array,
  orgTypes: array,
  divisions: array,
  types: array,
  durations: array,
  locationsEnabled: bool,
  locationFieldVisibility: object,
  locationFieldAssociation: array
};

export default connect(
  ({ locationsConfiguration, organizations }) => ({
    isOrgEnabled: organizations.get('config')?.enable || false,
    visibleOrgs: organizations.get('organizationsByVisibility')[orgVisibility.MENTOR_PROFILE] || [],
    orgTypes: organizations.get('orgTypes') || [],
    locationsEnabled: locationsConfiguration.get('enable'),
    locationFieldAssociation: locationsConfiguration.get('association'),
    locationFieldVisibility: locationsConfiguration.get('visibility')
  }),
  dispatch => ({
    openRequestModal: (mentorId, mentorName, onSuccess, mentorData) =>
      dispatch(openRequestMentorshipModal(mentorId, mentorName, onSuccess, mentorData))
  })
)(Sidebar);
