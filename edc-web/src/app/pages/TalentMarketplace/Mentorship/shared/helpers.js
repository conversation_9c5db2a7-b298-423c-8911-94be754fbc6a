import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';

export const mentorshipOptions = [
  {
    value: 'all_mentors',
    label: translatr('web.talentmarketplace.main', 'AllMentors', {
      tm_tm_mentors: omp('tm_tm_mentors')
    })
  },
  {
    value: 'mentees',
    label: translatr('web.talentmarketplace.main', 'MyMentees', {
      tm_tm_mentees: omp('tm_tm_mentees')
    })
  },
  {
    value: 'mentors',
    label: translatr('web.talentmarketplace.main', 'MyMentors', {
      tm_tm_mentors: omp('tm_tm_mentors')
    })
  }
];

export const SORT_OPTIONS = {
  ASCENDING: 'ascending',
  DESCENDING: 'descending'
};

export const defaultSortOptions = [
  {
    value: SORT_OPTIONS.DESCENDING,
    label: translatr('web.talentmarketplace.main', 'NewestFirst')
  },
  {
    value: SORT_OPTIONS.ASCENDING,
    label: translatr('web.talentmarketplace.main', 'OldestFirst')
  }
];

export const sortMentorshipInAscendingOrder = mentorship =>
  mentorship.sort((a, b) => new Date(a.selectedDate) - new Date(b.selectedDate));

export const sortMentorshipInDescendingOrder = mentorship =>
  mentorship.sort((a, b) => new Date(b.selectedDate) - new Date(a.selectedDate));

export const MENTORSHIP_STATUS = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  INPROGRESS: 'INPROGRESS',
  APPLIED: 'APPLIED',
  COMPLETED: 'COMPLETED',
  FUTURE_MENTORS: 'FUTURE_MENTORS',
  CURRENT_MENTORS: 'CURRENT_MENTORS'
};

const statusDateKeyByApprovalStatus = {
  [MENTORSHIP_STATUS.PENDING]: 'appliedOn',
  [MENTORSHIP_STATUS.APPROVED]: 'actionedOn',
  [MENTORSHIP_STATUS.APPLIED]: 'appliedOn',
  [MENTORSHIP_STATUS.CURRENT_MENTORS]: 'actionedOn',
  [MENTORSHIP_STATUS.FUTURE_MENTORS]: 'actionedOn',
  [MENTORSHIP_STATUS.INPROGRESS]: 'actionedOn',
  [MENTORSHIP_STATUS.REJECTED]: 'actionedOn',
  [MENTORSHIP_STATUS.COMPLETED]: 'completedOn'
};

export const mentorshipDateMapper = (user, status) => {
  const dateMapper = statusDateKeyByApprovalStatus[status];
  return user[dateMapper];
};
