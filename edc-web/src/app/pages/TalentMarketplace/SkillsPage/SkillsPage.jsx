import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { connect } from 'react-redux';
import Loading from 'centralized-design-system/src/Loading';
import Carousel from 'centralized-design-system/src/Carousel';
import { JOB_TYPE, OPPORTUNITY_TYPE, search } from 'edc-web-sdk/requests/careerOportunities.v2';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { track } from '@analytics/TrackWrapper';
import PropTypes from 'prop-types';
import { getJobMatchDetail as fetchVacancyDetail } from 'edc-web-sdk/requests/extOpportunities';
import {
  MAX_LIMIT_MENTORS,
  MAX_LIMIT_PROJECTS,
  shouldShowTMMentorship,
  shouldShowTMProject,
  shouldShowTMJobRole,
  shouldShowTMJobVacancy
} from 'opportunity-marketplace/util';
import { mapJobVacancy } from '../Api';
import Layout from '../Layout';
import TMNotFound from '@components/TMNotFound/TMNotFound';
import { CardProvider } from '@components/cardStandardization/context/CardContext';
import ContentRecommendationWrapper from './ContentRecommendationWrapper';
import { TrackEvents } from '@analytics/TrackEvents';
import './SkillsPage.scss';
import ProjectRecommendationsCarousel from 'opportunity-marketplace/shared/ProjectRecommendationsCarousel';
import { useLazyApi } from '@utils/hooks';
import {
  FilterCategory,
  FilterContext,
  transformFiltersToSearchPayload
} from 'opportunity-marketplace/ProjectsTab/helpers';
import {
  TM_MENTORSHIP_FILTER_BUCKET_NAME,
  TM_PROJECT_FILTER_BUCKET_NAME,
  FILTERS_DEFAULT_ASSOCIATION_ID
} from 'opportunity-marketplace/shared/filters/Filters.constants';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { tmSaveSearchFilters } from 'actions/talentmarketplaceActions';
import MentorsRecommendationsCarousel from 'opportunity-marketplace/shared/MentorRecommendationsCarousel';
import { Chip } from 'centralized-design-system/src/ChipsV2';
import { FilterHostname } from '@components/common/FilterHostname';

const SkillsPage = ({ saveFilters, projectsFilters, mentorsFilters, currentUserLang }) => {
  const location = useLocation();
  const { slug } = useParams();
  const navigate = useNavigate();
  const type = location.pathname.split('/')[3];
  const queryParams = new URLSearchParams(location.search);
  const config = { card: { id: `job-skills-${slug}`, isPublic: true }, tooltipPosition: 'top' };
  const [isLoading, setIsLoading] = useState(true);
  const [jobDataFetchIssue, setJobDataFetchIssue] = useState(false);
  const [jobData, setJobData] = useState({});
  const [skills, setSkills] = useState([]);
  const [activeSkill, setActiveSkill] = useState({});
  const activeSkillParam = queryParams.get('active');
  const skillsParam = queryParams.get('skills');
  const [getProjects, { data: projectsData, loading: projectsLoading }] = useLazyApi(search);
  const [getMentors, { data: mentorsData, loading: mentorsLoading }] = useLazyApi(search);

  const activeFilters = useMemo(
    () => ({
      filters: {
        [FILTERS_DEFAULT_ASSOCIATION_ID]: {
          [`00-${FilterCategory.SKILL}`]: [
            { name: activeSkill.label || activeSkill.name, value: activeSkill?.id }
          ]
        }
      }
    }),
    [activeSkill]
  );

  const updateActiveSkill = useCallback(
    active => {
      setActiveSkill(active);
      const skillsArray = skillsParam ? `&skills=${skillsParam}` : '';
      navigate(`${location.pathname}?active=${active.id}${skillsArray}`, { replace: true });
    },
    [history]
  );

  useEffect(() => {
    if (jobData?.title) {
      document.title = FilterHostname(
        translatr('web.talentmarketplace.main', 'RecommendationsFor', { job: jobData.title })
      );
    }
  }, [jobData?.title]);

  useEffect(() => {
    setJobData({});
    setIsLoading(true);
    setJobDataFetchIssue(false);

    if (
      (type === JOB_TYPE.ROLE && !shouldShowTMJobRole()) ||
      (type === JOB_TYPE.VACANCY && !shouldShowTMJobVacancy())
    ) {
      setJobDataFetchIssue(true);
      setIsLoading(false);
    } else {
      fetchVacancyDetail(slug, type, {
        language: currentUserLang
      })
        .then(data => {
          const jobVacancy = mapJobVacancy(data, type);
          const skillData = [
            ...jobVacancy.skillsNotHave,
            ...jobVacancy.skillsMightHave,
            ...jobVacancy.skillsHave
          ];
          setJobData(jobVacancy);

          if (skillsParam) {
            setSkills(JSON.parse(skillsParam) || []);
          } else {
            setSkills(skillData);
          }

          setIsLoading(false);
          track(TrackEvents.OMP.SKILLS_DETAILS_PAGE);

          const activeSkillParamNotFound =
            !activeSkillParam || skillData.findIndex(el => el.id === activeSkillParam) === -1;

          if (activeSkillParamNotFound) {
            if (shouldShowTMProject() || shouldShowTMMentorship()) {
              return updateActiveSkill({
                id: FilterCategory.ROLE,
                value: type === JOB_TYPE.VACANCY ? jobVacancy.linkedRoles : [jobVacancy.id]
              });
            }

            if (skillsParam) {
              return updateActiveSkill(JSON.parse(skillsParam)[0]);
            }
            if (skillData[0]) {
              return updateActiveSkill(skillData[0]);
            }
          }

          if (skillsParam) {
            return updateActiveSkill(JSON.parse(skillsParam)[0]);
          }

          updateActiveSkill(skillData.find(el => el.id === activeSkillParam));
        })
        .catch(error => {
          setJobDataFetchIssue(true);
          setIsLoading(false);
          console.error('Error in fetchVacancy', error);
        });
    }
  }, [slug]);

  useEffect(() => {
    if (!activeSkill.id) return;

    const rolePayload =
      activeSkill?.id === FilterCategory.ROLE
        ? {
            filters: {
              [FILTERS_DEFAULT_ASSOCIATION_ID]: {
                [`00-${FilterCategory.ROLE}`]: activeSkill.value?.map(skillId => ({
                  name: activeSkill.id,
                  value: skillId
                }))
              }
            }
          }
        : {};

    if (shouldShowTMProject()) {
      const searchPayload = transformFiltersToSearchPayload(
        FilterContext.PROJECT,
        OPPORTUNITY_TYPE.PROJECT,
        { ...projectsFilters, ...activeFilters, ...rolePayload, pageSize: MAX_LIMIT_PROJECTS }
      );
      getProjects(searchPayload);
    }

    if (shouldShowTMMentorship()) {
      const searchPayload = transformFiltersToSearchPayload(
        FilterContext.MENTORSHIP,
        OPPORTUNITY_TYPE.MENTORSHIP,
        { ...mentorsFilters, ...activeFilters, ...rolePayload, pageSize: MAX_LIMIT_MENTORS }
      );
      getMentors(searchPayload);
    }
  }, [activeSkill]);

  const navigateTo = useCallback(
    (link, filters) => e => {
      const filtersToPass = { ...activeFilters };
      const filterData = filtersToPass?.filters?.[FILTERS_DEFAULT_ASSOCIATION_ID] || {};
      for (let key in filterData) {
        if (filterData.hasOwnProperty(key)) {
          filterData[key] = filterData[key].filter(obj => obj.value !== FilterCategory.ROLE);
        }
      }
      saveFilters({ ...filters, ...filtersToPass });
      navigate(link);
      e.preventDefault();
    },
    [saveFilters, activeSkill]
  );

  const getViewAllLink = useCallback((url, condition, filters) => {
    if (condition) {
      return {
        link: url,
        handler: navigateTo(url, filters)
      };
    }
    return null;
  });

  return isLoading ? (
    <div className="ledger-loader text-center">
      <Loading />
    </div>
  ) : (
    <CardProvider value={config}>
      <Layout backButton>
        {jobDataFetchIssue && <TMNotFound />}
        {!jobDataFetchIssue && (
          <div className="skills-page">
            <div className="skills-page-header">
              <div className="job-title block">
                <div className="justflex">
                  <span className="flex-7">
                    {type === JOB_TYPE.VACANCY ? omp('tm_job_vacancy') : omp('tm_job_role')}
                  </span>
                </div>
                <h1>
                  {translatr('web.talentmarketplace.main', 'RecommendationsFor', {
                    job: jobData.title
                  })}
                </h1>
              </div>
            </div>
            <div className="skills-page-filters">
              <Carousel
                ariaLabelPrefix={translatr('web.talentmarketplace.main', 'Skills')}
                className="job-skill__skills"
                role="tablist"
                scrollToLastVisible
              >
                {(shouldShowTMProject() || shouldShowTMMentorship()) && (
                  <li>
                    <Chip
                      role="tab"
                      label={translatr('web.talentmarketplace.main', 'BasedOn', {
                        value:
                          type === JOB_TYPE.VACANCY ? omp('tm_tm_job_roles') : omp('tm_job_role')
                      })}
                      value={{
                        id: FilterCategory.ROLE,
                        value: type === JOB_TYPE.VACANCY ? jobData.linkedRoles : [jobData.id]
                      }}
                      isSelected={activeSkill?.id === FilterCategory.ROLE}
                      aria-selected={activeSkill?.id === FilterCategory.ROLE}
                      onClick={updateActiveSkill}
                    />
                  </li>
                )}

                {skills.length > 0 && (
                  <li className="skills-page-filters__chips-header">
                    {translatr('web.talentmarketplace.main', 'BasedOnSkill')}:
                  </li>
                )}

                {skills.length > 0 ? (
                  skills.map(skill => (
                    <li>
                      <Chip
                        role="tab"
                        key={skill.id}
                        label={skill.label}
                        value={skill}
                        isSelected={activeSkill?.id === skill.id}
                        aria-selected={activeSkill?.id === skill.id}
                        onClick={updateActiveSkill}
                      />
                    </li>
                  ))
                ) : (
                  <li className="job-skill__skills--no-skills">
                    <span>{translatr('web.common.main', 'NoSkillsGap')}</span>
                  </li>
                )}
              </Carousel>
            </div>
            <div className="skills-page-content">
              {activeSkill?.id !== FilterCategory.ROLE && (
                <ContentRecommendationWrapper skill={activeSkill} lang={currentUserLang} />
              )}
              {shouldShowTMProject() && (
                <ProjectRecommendationsCarousel
                  className="l-margin-bottom block"
                  title={omp('tm_tm_projects')}
                  loading={projectsLoading}
                  recommendations={projectsData?.searchResult || []}
                  viewAll={getViewAllLink(
                    `/career/${OPPORTUNITY_TYPE.PROJECT}`,
                    activeSkillParam !== FilterCategory.ROLE,
                    projectsFilters
                  )}
                  showIcon={false}
                  noDataComponent={() => (
                    <EmptyState
                      icon="icon-file"
                      title={
                        activeSkillParam === FilterCategory.ROLE
                          ? translatr(
                              'web.common.main',
                              'WeAreSorryButThereAreCurrentlyNoProjectsAvailableForThisOpportunityType',
                              {
                                tm_tm_projects: omp('tm_tm_projects'),
                                opportunityType:
                                  type === JOB_TYPE.VACANCY
                                    ? omp('tm_job_vacancy')
                                    : omp('tm_job_role')
                              }
                            )
                          : translatr(
                              'web.common.main',
                              'WeAreSorryButThereIsCurrentlyNoProjectsAvailableForThisSkill',
                              { tm_tm_projects: omp('tm_tm_projects') }
                            )
                      }
                    />
                  )}
                />
              )}
              {shouldShowTMMentorship() && (
                <div className="row-container">
                  <MentorsRecommendationsCarousel
                    className="l-margin-top block"
                    title={omp('tm_tm_mentors')}
                    loading={mentorsLoading}
                    recommendations={mentorsData?.searchResult || []}
                    viewAll={getViewAllLink(
                      `/career/${OPPORTUNITY_TYPE.MENTORSHIP}`,
                      mentorsData?.totalCount > MAX_LIMIT_MENTORS &&
                        activeSkillParam !== FilterCategory.ROLE,
                      mentorsFilters
                    )}
                    showIcon={false}
                    noDataComponent={() => (
                      <EmptyState
                        icon="icon-file"
                        title={
                          activeSkillParam === FilterCategory.ROLE
                            ? translatr(
                                'web.common.main',
                                'WeAreSorryButThereAreCurrentlyNoMentorsAvailableForThisOpportunityType',
                                {
                                  tm_tm_mentors: omp('tm_tm_mentors'),
                                  opportunityType:
                                    type === JOB_TYPE.VACANCY
                                      ? omp('tm_job_vacancy')
                                      : omp('tm_job_role')
                                }
                              )
                            : translatr(
                                'web.common.main',
                                'WeAreSorryButThereIsCurrentlyNoMentorsAvailableForThisSkill',
                                { tm_tm_mentors: omp('tm_tm_mentors') }
                              )
                        }
                      />
                    )}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </Layout>
    </CardProvider>
  );
};

const mapStoreStateToProps = ({ talentmarketplaceReducer, currentUser, team }) => {
  return {
    projectsFilters: talentmarketplaceReducer.get(TM_PROJECT_FILTER_BUCKET_NAME),
    mentorsFilters: talentmarketplaceReducer.get(TM_MENTORSHIP_FILTER_BUCKET_NAME),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en'
  };
};

const mapDispatchToProps = dispatch => {
  return {
    saveFilters: newFilters => dispatch(tmSaveSearchFilters(newFilters))
  };
};

SkillsPage.propTypes = {
  location: PropTypes.shape({
    pathname: PropTypes.string.isRequired,
    search: PropTypes.string
  }),
  saveFilters: PropTypes.func,
  projectsFilters: PropTypes.object.isRequired,
  mentorsFilters: PropTypes.object.isRequired,
  currentUserLang: PropTypes.string
};

export default connect(mapStoreStateToProps, mapDispatchToProps)(SkillsPage);
