import { useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { CONTENT_LIMIT } from 'opportunity-marketplace/Api';
import { fetchRecommendedCards } from 'edc-web-sdk/requests/recommended';
import { isDefaultSourceEnabled } from 'edc-web-sdk/requests/search';
import { getCardsInfo as fetchCardsInfo } from 'edc-web-sdk/requests/cards';
import { checkContentAvailability } from 'edc-web-sdk/requests/cards.v2';
import Carousel from 'centralized-design-system/src/Carousel';
import CardWrapper from '@components/cardStandardization/CardWrapper';
import './SkillsPage.scss';
import { translatr } from 'centralized-design-system/src/Translatr';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { filterOutNotAccessibleCards } from '../DetailPage/components/TransitionPlan/utils';
import ContentCarouselLoader from './ContentCarouselLoader';

const CONTENT_REQUEST_LIMIT = 50;

const ContentCarousel = ({ skill, lang, onCounterUpdate, title }) => {
  const [cards, setCards] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (window.localStorage.getItem('isDefaultSourceEnabled') === null) {
        await isDefaultSourceEnabled();
      }
      try {
        const cardsResp = await getCourses();
        setCards(cardsResp);
        onCounterUpdate(cardsResp?.length || null);
      } catch (err) {
        console.error(`Error in getCourse.func: ${err}`);
        onCounterUpdate(null);
      }
      setIsLoading(false);
    };
    fetchData().catch(err => {
      console.error(`Error in fetchData.func: ${err}`);
      setIsLoading(false);
    });
  }, [skill]);

  const getUpdatedCards = useCallback(
    async recommendedCards => {
      const cardsDataECLIDMap = {};
      const cardsDataSociativeIDMap = {};
      const cardsData = await fetchCardsInfo({
        'ecl_ids[]': recommendedCards.map(card => card.id)
      });

      cardsData.forEach(card => {
        cardsDataECLIDMap['ECL-' + card.eclId] = card;
        if (card.externalId) {
          cardsDataSociativeIDMap['ECL-' + card.externalId] = card;
        }
      });
      return recommendedCards.map(card => {
        const cardData = cardsDataECLIDMap[card.id] || cardsDataSociativeIDMap[card.id];
        if (cardData) {
          card.forcedUpdateMarkAsCompleteButton = true;
          return {
            ...card,
            ...cardData,
            markFeatureDisabledForSource: card.mark_feature_disabled || false
          };
        } else {
          return {
            ...card,
            markFeatureDisabledForSource: card.mark_feature_disabled || false
          };
        }
      });
    },
    [fetchCardsInfo]
  );

  const getCourses = useCallback(async () => {
    const isECLSourceEnabled =
      window.localStorage.getItem('isDefaultSourceEnabled') === 'true' ? true : false;
    try {
      let resp;
      let offset = 0;
      let totalNumberOfCourses = 0;
      let cardsFiltered = [];

      do {
        const payload = {
          taxo_kind: 'topics',
          content_kind: isECLSourceEnabled ? ['org', 'open'] : ['org'],
          node_id: skill.id,
          org_id: window.__edOrgData.id,
          limit: CONTENT_REQUEST_LIMIT,
          offset,
          lang: lang,
          sort: 'ts',
          type: [],
          domain: [],
          source: [],
          since: 1000,
          query: ''
        };

        resp = await fetchRecommendedCards(payload);
        const { data } = resp || {};

        const cardsAvailabilityPayload = {
          cards: data
            .filter(({ external_id }) => external_id)
            .map(card => ({
              external_id: card.external_id,
              ecl_id: card.id,
              source_id: card.source_id
            }))
        };

        const availabilityResp = cardsAvailabilityPayload.cards.length
          ? await checkContentAvailability(cardsAvailabilityPayload)
          : {};

        cardsFiltered = [
          ...cardsFiltered,
          ...filterOutNotAccessibleCards(data, availabilityResp.cards)
        ].slice(0, CONTENT_LIMIT);
        totalNumberOfCourses = resp?.total || 0;
        offset += CONTENT_REQUEST_LIMIT;
      } while (cardsFiltered.length < CONTENT_LIMIT && offset < totalNumberOfCourses);

      if (resp.data?.length > 0) {
        return await getUpdatedCards(cardsFiltered);
      }
      return [];
    } catch (err) {
      console.error(`Error in fetchRecommendedCards.func: ${err}`);
    }
  }, [skill, fetchRecommendedCards, getUpdatedCards]);

  return (
    <>
      {isLoading ? (
        <ContentCarouselLoader />
      ) : cards?.length > 0 ? (
        <Carousel key="content-carousel" ariaLabelPrefix={title}>
          {[...Array(1)].map(() => (
            <section key={'section-content-carousel-cards-key'} className="content-carousel-cards">
              {cards.map(card => {
                return (
                  <CardWrapper
                    key={card.id}
                    card={card.assignable || card}
                    dueAt={card.dueAt || card.assignment?.dueAt}
                    assignedAt={card.createdAt}
                    assignedBy={card.assignor?.name}
                    assignorId={card.assignor?.id}
                    startDate={card.startDate || card.assignment?.startDate}
                    isAssignmentPage={true}
                    showPersonalizedContent={true}
                    {...card}
                  />
                );
              })}
            </section>
          ))}
        </Carousel>
      ) : (
        <EmptyState
          icon="icon-file"
          title={translatr(
            'web.talentmarketplace.main',
            'WereSorryButThereIsCurrentlyNoContentAvailableForThisSkill'
          )}
        />
      )}
    </>
  );
};

ContentCarousel.propTypes = {
  skill: PropTypes.object,
  lang: PropTypes.string,
  onCounterUpdate: PropTypes.func,
  title: PropTypes.string
};

export default ContentCarousel;
