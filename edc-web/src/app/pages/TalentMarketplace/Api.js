import { CAPABILITY_STATUS, JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import moment from 'moment';
import { MATCHING_LEVEL } from '@components/MatchComponent/util';

const lang = window.__ED__.profile.language;

const checkForHtml = text => {
  const htmlTagRegex = /<[a-z][\s\S]*>/i;
  const htmlEntitiesRegex = /&(\w+;|#\d+;)/i;
  return htmlTagRegex.test(text) || htmlEntitiesRegex.test(text);
};

export function getSkills(capabilities, group) {
  const capabilitiesArr = capabilities || [];
  if (group === 'ALL') {
    return capabilitiesArr;
  }
  return capabilitiesArr.filter(cap => (cap.status || '').toUpperCase() === group);
}

export const getPrimaryRecrutierId = (recruiters = []) => {
  const primaryRecruiter = recruiters.length ? recruiters[0] : null;
  return primaryRecruiter?.visible ? primaryRecruiter.id : '';
};

export const ROLE_TYPE = {
  MANAGEMENT: 'MANAGEMENT',
  INDIVIDUAL_CONTRIBUTOR: 'INDIVIDUAL_CONTRIBUTOR'
};

export const RECOMMENDED_LIMIT = 15;
export const CONTENT_LIMIT = 12;
export const mapJobVacancy = (vacancyData = {}, type = JOB_TYPE.VACANCY) => {
  if (vacancyData.mapped) return vacancyData; // data was mapped before
  const {
    actionPlan,
    id,
    title,
    skillsGraphScore,
    overallScore,
    overallScoreStatus,
    locations,
    capabilities = [],
    descriptions = [],
    description: desc,
    jobDescription = '',
    otherDescription = '',
    company,
    salary,
    level,
    division,
    contractType,
    schedule,
    referenceNumber,
    createdDateTimestamp,
    jobFamilies = [],
    jobFamily,
    jobFunction,
    remote,
    referralURL,
    jobDescriptionURL,
    applyURL,
    applyDate,
    applicationStatus,
    linkedRoles,
    linkedRolesExt = [],
    isLinkedRolesPredefined,
    recruitersExternal = [],
    grade,
    type: opportunityType,
    isManagementPosition,
    skillsValence,
    commonCareerMoveValence,
    isBookmarked,
    isDismissed,
    isApplied,
    appliedCount,
    careerTrack = '',
    organizationsByType,
    organizations,
    startDateTime,
    endDateTime
  } = vacancyData;
  const descriptionsArr = descriptions || [];
  const defaultDescription = descriptionsArr.find(({ isDefault }) => !!isDefault) || {};
  const description =
    descriptionsArr.find(({ language }) => language.indexOf(lang) === 0) || defaultDescription;
  const finalDescription = desc || description.description;
  return {
    actionPlan,
    id,
    title: title || description.title,
    skillsGraphScore,
    overallScore,
    overallScoreStatus,
    locations,
    skillsNotHave: getSkills(capabilities, CAPABILITY_STATUS.MISSING),
    skillsHave: getSkills(capabilities, CAPABILITY_STATUS.DECLARED),
    skillsMightHave: getSkills(capabilities, CAPABILITY_STATUS.DETECTED),
    allSkills: capabilities,
    description: finalDescription,
    jobDescription,
    otherDescription,
    plainTextDescription: !checkForHtml(finalDescription),
    plainTextJobDescription: !checkForHtml(jobDescription),
    plainTextOtherDescription: !checkForHtml(otherDescription),
    company,
    salary,
    level,
    department: division,
    type: contractType,
    schedule,
    referenceNumber,
    postingDate:
      createdDateTimestamp && type === JOB_TYPE.VACANCY
        ? moment(createdDateTimestamp).format('DD/MM/YYYY')
        : undefined,
    area: (jobFamilies || []).join(', '),
    mode: remote,
    referralURL,
    applyURL,
    applyDate,
    applicationStatus,
    jobDescriptionURL,
    linkedRolesExt: linkedRolesExt.map(role => ({
      id: role.internalId,
      externalId: role.externalId,
      title: role.title,
      parentRoleTitle: role.parentRoleTitle,
      status: role.linkedRoleStatus
    })),
    linkedRoles: linkedRoles || linkedRolesExt.map(role => role.internalId),
    isLinkedRolesPredefined,
    primaryRecruiterId: getPrimaryRecrutierId(recruitersExternal || []),
    seniority: grade,
    opportunityType,
    management: isManagementPosition
      ? ROLE_TYPE.MANAGEMENT
      : isManagementPosition === false
      ? ROLE_TYPE.INDIVIDUAL_CONTRIBUTOR
      : isManagementPosition,
    skillsValence: skillsValence || MATCHING_LEVEL.NEUTRAL,
    commonCareerMoveValence: commonCareerMoveValence || MATCHING_LEVEL.NEUTRAL,
    bookmarked: isBookmarked,
    dismissed: isDismissed,
    applied: isApplied,
    appliedCount,
    jobFamily,
    jobFunction,
    careerTrack,
    organizationsByType,
    organizations: organizations || [],
    startDateTime,
    endDateTime,
    mapped: true
  };
};

const mapJobRole = (jobRoleData = {}) => {
  if (jobRoleData.mapped) return jobRoleData; // data was mapped before
  const {
    actionPlan,
    id,
    role,
    skillsGraphScore,
    overallScore,
    overallScoreStatus,
    capabilities = [],
    title,
    jobFamilies,
    jobFamily,
    jobFunction,
    level,
    grade,
    isManagementPosition,
    skillsValence,
    commonCareerMoveValence,
    categories,
    isBookmarked,
    isDismissed,
    isAspirational,
    type: opportunityType,
    hasOpenJobs: hasJobVacancy,
    careerTrack = '',
    remote,
    locations,
    hasTransitionPlan,
    hasRecommendationFeedback
  } = jobRoleData;
  return {
    actionPlan,
    id,
    title: title || role?.title || '',
    area: (role?.jobFamilies || jobFamilies || []).join(', '),
    skillsGraphScore,
    overallScore,
    overallScoreStatus,
    level: level || role?.level || null,
    skillsNotHave: getSkills(capabilities, CAPABILITY_STATUS.MISSING),
    skillsHave: getSkills(capabilities, CAPABILITY_STATUS.DECLARED),
    skillsMightHave: getSkills(capabilities, CAPABILITY_STATUS.DETECTED),
    allSkills: capabilities,
    grade,
    isManagementPosition,
    categories,
    bookmarked: isBookmarked,
    dismissed: isDismissed,
    aspirational: isAspirational,
    management: isManagementPosition,
    skillsValence: skillsValence || MATCHING_LEVEL.NEUTRAL,
    commonCareerMoveValence: commonCareerMoveValence || MATCHING_LEVEL.NEUTRAL,
    opportunityType,
    hasJobVacancy,
    jobFamily,
    jobFunction,
    careerTrack,
    mode: remote,
    locations,
    hasTransitionPlan,
    hasRecommendationFeedback,
    mapped: true
  };
};

export const mapJobVacancies = (data = []) => data.map(mapJobVacancy);
export const mapJobRoles = (data = []) => data.map(mapJobRole);
