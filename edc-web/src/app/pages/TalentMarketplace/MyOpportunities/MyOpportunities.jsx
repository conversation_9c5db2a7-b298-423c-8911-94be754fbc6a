import React, { useContext, useCallback, useEffect } from 'react';
import { useNavigate, useLocation, useParams, Route } from 'react-router-dom';
import PropTypes from 'prop-types';
import LeftNavigationTabs from 'centralized-design-system/src/NavigationTabs/LeftNavigationTabs';
import { omp, translatr, Translatr } from 'centralized-design-system/src/Translatr';
import tabs from './tabs';
import './MyOpportunities.scss';
import AspirationsContext from '../shared/AspirationsContext';
import { connect } from 'react-redux';
import {
  getOrgConfiguration,
  loadAllOrganizations,
  orgAssociation
} from '@actions/organizationsActions';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { RootRoutes } from '../../../Router';
import { Select } from 'centralized-design-system/src/Inputs';
import { getNotificationConfigForUser } from '@actions/currentUserActions';
import { useAlertPermission } from '../shared/OpportunitiesSavedFilters/hooks';
import { TM_VACANCY_FILTER_BUCKET_NAME } from '../shared/filters/Filters.constants';

const MyOpportunities = ({ type, dispatch, organizations, currentUserLang }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { filter } = useParams();
  const [activeTab, setActiveTab] = React.useState(filter);
  const urlType = location.pathname.split('/')[2];

  const hasJobAlertPermission = useAlertPermission(TM_VACANCY_FILTER_BUCKET_NAME);
  const isTabVisible = tab => {
    if (tab.key === 'my-job-alerts') {
      return hasJobAlertPermission;
    }
    return true;
  };

  const opportunityTabs = tabs
    .get(type)
    .filter(isTabVisible)
    .map(tab => ({ ...tab, link: `/career/${urlType}/${tab.key}` }));
  const mobileDropdownTabs = opportunityTabs.map(tab => ({
    value: tab.key,
    label: tab.label
  }));

  const { getAspirationsResponse } = useContext(AspirationsContext);
  const getAspirations = useCallback(
    (_, order, __, preferencesKeys) => getAspirationsResponse(order, preferencesKeys),
    [getAspirationsResponse]
  );

  useEffect(() => {
    setActiveTab(filter);
  }, [filter]);

  useEffect(() => {
    if (!organizations.get('config')) {
      dispatch(getOrgConfiguration());
    }
    dispatch(getNotificationConfigForUser());
  }, []);

  const orgAssocKey = type === JOB_TYPE.ROLE ? orgAssociation.JOB_ROLE : orgAssociation.JOB_VACANCY;
  const menuAriaLabel =
    type === JOB_TYPE.ROLE ? omp('tm_tm_job_roles') : omp('tm_tm_job_vacancies');
  useEffect(() => {
    if (
      organizations.get('config')?.enable &&
      !organizations.get('organizationsByAssociation')[orgAssocKey]
    ) {
      dispatch(loadAllOrganizations(currentUserLang, '', orgAssocKey));
    }
  }, [
    type,
    organizations.get('config'),
    organizations.get('organizationsByAssociation')[orgAssocKey]
  ]);

  const onTabChange = tab => {
    setActiveTab(tab.value);
    navigate(`/career/${urlType}/${tab.value}`);
  };

  return (
    <div className="my-opportunities">
      <Translatr apps={['web.talentmarketplace.main']}>
        <LeftNavigationTabs
          tabs={opportunityTabs}
          activeTab={activeTab}
          ariaLabel={menuAriaLabel}
        />
        <div className="my-opportunities__container">
          <div className="my-opportunities__mobile-dropdown">
            <Select
              id="my-opportunities__dropdown-mobile"
              ariaLabelledBy="my-opportunities__dropdown-mobile"
              defaultValue={activeTab}
              onChange={onTabChange}
              items={mobileDropdownTabs}
              title={translatr('web.talentmarketplace.main', 'Display')}
              isTranslated={true}
            />
          </div>
          <RootRoutes>
            {opportunityTabs.map(
              ({
                key,
                getOpportunities,
                noDataMessage,
                hasPagination,
                component: Component,
                cardComponent,
                label,
                pageSize = 12,
                sortType,
                ...rest
              }) => (
                <Route
                  key={key}
                  path={`/career/${urlType}/${key}`}
                  element={
                    <Translatr
                      key={key}
                      apps={[
                        'web.myprofile.main',
                        'web.common.main',
                        'cds.common.main',
                        'web.talentmarketplace.main'
                      ]}
                    >
                      <Component
                        type={type}
                        getOpportunities={
                          key === 'aspirational' ? getAspirations : getOpportunities
                        }
                        tab={key}
                        noDataMessage={noDataMessage}
                        hasPagination={hasPagination}
                        cardComponent={cardComponent}
                        label={label}
                        pageSize={pageSize}
                        sortType={sortType}
                        {...rest}
                      />
                    </Translatr>
                  }
                />
              )
            )}
          </RootRoutes>
        </div>
      </Translatr>
    </div>
  );
};

MyOpportunities.propTypes = {
  type: PropTypes.string,
  dispatch: PropTypes.func,
  organizations: PropTypes.object,
  currentUserLang: PropTypes.string
};

function mapStoreStateToProps({ currentUser, team, organizations }) {
  return {
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations
  };
}

export default connect(mapStoreStateToProps)(MyOpportunities);
