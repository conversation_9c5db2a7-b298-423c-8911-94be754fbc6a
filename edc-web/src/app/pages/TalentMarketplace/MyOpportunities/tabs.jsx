import { translatr, omp } from 'centralized-design-system/src/Translatr';
import {
  OPPORTUNITY_TYPE,
  getSharedOpportunitiesForCurrentUser
} from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  fetchDismissed,
  fetchApplied,
  fetchBookmarked
} from 'edc-web-sdk/requests/extOpportunities';
import OpportunityTab from './OpportunityTab';
import SharedOpportunityTab from './SharedOpportunityTab';
import SharedRoleCard from '@components/roleCard/SharedRoleCard';
import SharedJobCard from '@components/JobCard/SharedJobCard';
import SimpleOpportunityCard from '@components/JobCard/SimpleOpportunityCard';

import { OMP_URL_BY_TYPE } from '../util';
import OpportunityTabHorizontal from './OpportunityTabHorizontal';
import JobVacancyTab from '../JobVacancyTab/JobVacancyTab';
import JobRolesTab from '../JobRolesTab/JobRolesTab';
import { openCompleteYourProfileModal } from '@actions/modalActions';
import MyAlertsPage from '../shared/OpportunitiesSavedFilters/MyAlertsPage/MyAlertsPage';
import { TM_VACANCY_FILTER_BUCKET_NAME } from '../shared/filters/Filters.constants';

const tabs = new Map([
  [
    OPPORTUNITY_TYPE.JOB_ROLE,
    [
      {
        key: 'all',
        label: translatr('web.talentmarketplace.main', 'AllOpportunities', {
          opportunities: omp('tm_tm_job_roles')
        }),
        component: JobRolesTab,
        setCareerModalOpen: openCompleteYourProfileModal
      },
      {
        label: translatr('web.talentmarketplace.main', 'MarkedAsAspirationalRoleConfigurable', {
          tm_aspirational_role: omp('tm_tm_aspirational_role')
        }),
        key: 'aspirational',
        noDataMessage: translatr(
          'web.talentmarketplace.main',
          'NoMarkedAsAspirationalMessageConfigurable',
          {
            roles: omp('tm_tm_job_roles'),
            tm_aspirational_role: omp('tm_tm_aspirational_role')
          }
        ),
        component: OpportunityTab,
        hasPagination: false
      },
      {
        label: translatr('web.talentmarketplace.main', 'SharedWithMe'),
        key: 'shared-with-me',
        getOpportunities: getSharedOpportunitiesForCurrentUser,
        noDataMessage: translatr('web.talentmarketplace.main', 'NoSharedOpportunitiesMessage', {
          opportunities: omp('tm_tm_job_roles')
        }),
        component: SharedOpportunityTab,
        cardComponent: SharedRoleCard
      },
      {
        label: translatr('web.talentmarketplace.main', 'Dismissed'),
        key: 'dismissed',
        getOpportunities: fetchDismissed,
        noDataMessage: translatr('web.talentmarketplace.main', 'NoDismissedOpportunitiesMessage', {
          opportunities: omp('tm_tm_job_roles')
        }),
        component: OpportunityTab,
        hasPagination: true
      }
    ]
  ],
  [
    OPPORTUNITY_TYPE.JOB_VACANCY,
    [
      {
        key: 'all',
        label: translatr('web.talentmarketplace.main', 'AllOpportunities', {
          opportunities: omp('tm_tm_job_vacancies')
        }),
        component: JobVacancyTab,
        setCareerModalOpen: openCompleteYourProfileModal
      },
      {
        label: translatr('web.talentmarketplace.main', 'MyOpportunities', {
          opportunities: omp('tm_tm_job_vacancies')
        }),
        key: 'applied',
        getOpportunities: fetchApplied,
        noDataMessage: translatr('web.talentmarketplace.main', 'NoAppliedOpportunitiesMessage', {
          opportunities: omp('tm_tm_job_vacancies')
        }),
        component: OpportunityTabHorizontal,
        cardComponent: SimpleOpportunityCard,
        hasPagination: true,
        sortType: 'APPLICATION_DATE',
        pageSize: 25
      },
      {
        label: translatr('web.talentmarketplace.main', 'Bookmarked'),
        key: 'bookmarked',
        getOpportunities: fetchBookmarked,
        noDataMessage: translatr('web.talentmarketplace.main', 'NoBookmarkedOpportunitiesMessage', {
          opportunities: omp('tm_tm_job_vacancies')
        }),
        component: OpportunityTab,
        hasPagination: true
      },
      {
        label: translatr('web.talentmarketplace.main', 'SharedWithMe'),
        key: 'shared-with-me',
        getOpportunities: getSharedOpportunitiesForCurrentUser,
        noDataMessage: translatr('web.talentmarketplace.main', 'NoSharedOpportunitiesMessage', {
          opportunities: omp('tm_tm_job_vacancies')
        }),
        component: SharedOpportunityTab,
        cardComponent: SharedJobCard
      },
      {
        label: translatr('web.talentmarketplace.main', 'Dismissed'),
        key: 'dismissed',
        getOpportunities: fetchDismissed,
        noDataMessage: translatr('web.talentmarketplace.main', 'NoDismissedOpportunitiesMessage', {
          opportunities: omp('tm_tm_job_vacancies')
        }),
        component: OpportunityTab,
        hasPagination: true
      },
      {
        label: translatr('web.talentmarketplace.main', 'MyAlerts'),
        key: 'my-job-alerts',
        bucketName: TM_VACANCY_FILTER_BUCKET_NAME,
        component: MyAlertsPage
      }
    ]
  ]
]);

export const tabPaths = type =>
  tabs.get(type).map(tab => `/career/${OMP_URL_BY_TYPE[type]}/${tab.key}`);

export default tabs;
