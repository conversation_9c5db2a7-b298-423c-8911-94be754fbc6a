import { useState, useEffect, useCallback, useContext, useMemo } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import './styles.scss';
import {
  buttonLabelMapping,
  OMP_URL_BY_TYPE,
  sortOrderOptions,
  defaultSortOptions,
  GET_SHARED_OPPORTUNITY_REQUEST_LIMIT
} from './helpers.js';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import { SkillsContextProvider } from 'opportunity-marketplace/shared/SkillsContext';
import { open_v2 as openSnackBar } from 'actions/snackBarActions';
import AspirationsContext from '../../shared/AspirationsContext';
import { useCountries } from '../../shared/hooks.js';

const SharedOpportunityTab = ({
  type,
  getOpportunities,
  noDataMessage,
  toast,
  cardComponent: CardComponent,
  tab
}) => {
  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState(sortOrderOptions.DESCENDING);
  const navigate = useNavigate();
  const { aspirations, isAspirationsInitLoading, isAspirationalRole } = useContext(
    AspirationsContext
  );
  const [aspirationsRoles, setAspirationsRoles] = useState([]);
  const countries = useCountries();

  useEffect(() => {
    setAspirationsRoles(aspirations);
  }, [aspirations]);

  const removeOpportunity = id => {
    setOpportunities(opportunities.filter(opportunity => opportunity.id !== id));
  };

  const updateAspirationsRoles = id => {
    if (aspirationsRoles.find(asp => asp.id === id)) {
      setAspirationsRoles(aspirationsRoles.filter(asp => asp.id !== id));
    } else {
      const newAspirationalRole = opportunities.find(opportunity => opportunity.id === id);
      setAspirationsRoles([...aspirationsRoles, newAspirationalRole]);
    }
  };

  const searchFilter = useMemo(
    () => ({
      type,
      order,
      limit: GET_SHARED_OPPORTUNITY_REQUEST_LIMIT
    }),
    []
  );

  useEffect(() => {
    setLoading(true);
    getOpportunitiesData(searchFilter, true);
  }, [order]);

  const getOpportunitiesData = (filter, reset = false) => {
    getOpportunities(filter)
      .then(response => {
        const nextOpportunities = reset ? [] : opportunities;
        if (!Array.isArray(response)) {
          setOpportunities([...nextOpportunities, ...response.values]);
        }
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        toast(
          translatr(
            'web.talentmarketplace.main',
            'ThereWasAnErrorWhileFetchingYourSharedOpportunitiesPleaseTryAgain'
          ),
          'error'
        );
      });
  };

  const navigateToAll = useCallback(
    e => {
      navigate(`/career/${OMP_URL_BY_TYPE[type]}`);
      e.preventDefault();
    },
    [history]
  );

  return (
    <div className="block">
      {!isAspirationsInitLoading && opportunities.length > 0 && (
        <div className="opportunity-tab-header">
          <Select
            id={`select-${tab}`}
            defaultValue={order}
            items={defaultSortOptions}
            onChange={option => setOrder(option.id)}
            title={translatr('web.talentmarketplace.main', 'SortBy') + ':'}
          />
        </div>
      )}
      {loading || isAspirationsInitLoading ? (
        <div className={`my-opportunities-opportunity-cards-container ${tab}-tab`}>
          {new Array(6).fill(null).map((_, i) => (
            <CardComponent key={i} loading />
          ))}
        </div>
      ) : opportunities.length > 0 ? (
        <>
          <div className={`my-opportunities-opportunity-cards-container ${tab}-tab`}>
            {opportunities.map(opportunity => (
              <SkillsContextProvider initSkills={opportunity.capabilities} key={opportunity.id}>
                <CardComponent
                  type={type}
                  id={opportunity.id}
                  title={opportunity.title}
                  overallScore={opportunity.overallScore}
                  skillsGraphScore={opportunity.skillsGraphScore}
                  level={opportunity.level}
                  contractType={opportunity.contractType}
                  mode={opportunity.remote}
                  isAspirational={isAspirationalRole(opportunity.id)}
                  isDismissed={opportunity.isDismissed}
                  isBookmarked={opportunity.isBookmarked}
                  hasOpenJobs={opportunity.hasOpenJobs}
                  locations={opportunity.locations}
                  jobFamily={opportunity.jobFamily}
                  sharedBys={opportunity.sharedBy}
                  applyDate={opportunity.applyDate}
                  loading={loading}
                  removeOpportunity={removeOpportunity}
                  aspirationsRoles={aspirationsRoles}
                  updateAspirationsRoles={updateAspirationsRoles}
                  countries={countries}
                />
              </SkillsContextProvider>
            ))}
          </div>
        </>
      ) : (
        <div className="my-opportunities-no-data-container">
          <EmptyState
            icon="icon-file"
            title={noDataMessage}
            buttonLabel={buttonLabelMapping[type]}
            onButtonClick={navigateToAll}
          />
        </div>
      )}
    </div>
  );
};

SharedOpportunityTab.propTypes = {
  type: PropTypes.string,
  getOpportunities: PropTypes.func,
  noDataMessage: PropTypes.string,
  cardComponent: PropTypes.object,
  toast: PropTypes.func,
  tab: PropTypes.string
};

export default connect(null, dispatch => ({
  dispatch,
  toast: (message, type = 'success') => dispatch(openSnackBar(message, type))
}))(SharedOpportunityTab);
