import React, { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { track } from '@analytics/TrackWrapper';
import PropTypes from 'prop-types';
import { OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { TrackEventProperties, TrackEvents } from '@analytics/TrackEvents';
import { mapJobRoles, mapJobVacancies } from '../Api';
import { POST_PER_PAGE } from '../shared/filters/Filters.constants';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import Results from '../shared/Results';
import { OMP_URL_BY_TYPE, CARDS_POST_OPERATION_RELOAD_DELAY } from '../util';
import { connect } from 'react-redux';
import { Select } from 'centralized-design-system/src/Inputs';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import _ from 'lodash';

const sortOrderOptions = {
  ASCENDING: 'ASC',
  DESCENDING: 'DESC'
};

const defaultSortOptions = [
  {
    id: sortOrderOptions.DESCENDING,
    value: translatr('web.talentmarketplace.main', 'NewestFirst')
  },
  {
    id: sortOrderOptions.ASCENDING,
    value: translatr('web.talentmarketplace.main', 'OldestFirst')
  }
];

const OpportunityTab = ({
  type,
  getOpportunities,
  tab,
  noDataMessage,
  hasPagination,
  currentUserLang
}) => {
  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [sortOrder, setSortOrder] = useState(sortOrderOptions.DESCENDING);

  const { COMPONENT } = TrackEventProperties.OMP;
  const { search: searchParam, pathname } = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(searchParam);
  const currentPage = queryParams.get('page') ? parseInt(queryParams.get('page'), 10) : 1;

  const setCurrentPage = useCallback(
    page => {
      navigate({
        pathname,
        search: `?page=${page}`
      });
    },
    [history]
  );

  const navigateToAll = useCallback(
    e => {
      navigate(`/career/${OMP_URL_BY_TYPE[type]}`);
      e.preventDefault();
    },
    [history]
  );

  const loadOpportunities = useCallback((order, page, delay = 0) => {
    setLoading(true);
    const payload = {
      pageNumber: page,
      pageSize: POST_PER_PAGE,
      sortType: 'ID',
      sortOrder: order,
      language: currentUserLang
    };
    _.delay(() => {
      getOpportunities(type, ...(hasPagination ? [payload] : [order, currentUserLang]))
        .then(response => {
          const { values, totalElements: total, totalPages: pages } = response;
          setTotalElements(total || 0);
          setTotalPages(pages || 0);
          setOpportunities(
            type === OPPORTUNITY_TYPE.JOB_ROLE ? mapJobRoles(values) : mapJobVacancies(values)
          );
          setLoading(false);
        })
        .catch(error => {
          if (error !== 'canceled') {
            setLoading(false);
          }
          console.error(`Error in fetching ${type}`, error);
        });
    }, delay);
  }, []);

  const updateOpportunities = useCallback(
    data => {
      if (totalPages === currentPage) {
        // last page
        const filtered = data.filter(opp => typeof opp[tab] === 'undefined' || opp[tab]);
        setOpportunities(filtered);
        setTotalElements(totalElements - 1);
        if (filtered.length === 0 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      } else {
        loadOpportunities(sortOrder, currentPage, CARDS_POST_OPERATION_RELOAD_DELAY);
      }
    },
    [totalPages, currentPage, totalElements, sortOrder]
  );

  useEffect(() => {
    if (hasPagination && !loading) setCurrentPage(1);
  }, [sortOrder]);

  useEffect(() => {
    loadOpportunities(sortOrder, currentPage);
  }, [sortOrder, currentPage]);

  useEffect(() => {
    track(
      TrackEvents.OMP[
        type === OPPORTUNITY_TYPE.JOB_ROLE
          ? 'MANAGE_JOB_ROLES_PAGE_VIEWED'
          : 'MANAGE_JOB_VACANCIES_PAGE_VIEWED'
      ],
      {
        [COMPONENT]: `OpportunityTab - ${tab}`
      }
    );
  }, [track]);

  return (
    <div className="opportunity-tab block">
      {opportunities.length > 0 && (
        <div className="opportunity-tab-header">
          <Select
            defaultValue={sortOrder}
            items={defaultSortOptions}
            onChange={option => setSortOrder(option.id)}
            title={translatr('web.talentmarketplace.main', 'SortBy') + ':'}
          />
        </div>
      )}
      <Results
        itemsPerPage={POST_PER_PAGE}
        loading={loading}
        opportunities={opportunities}
        setOpportunities={setOpportunities}
        reloadOpportunities={updateOpportunities}
        totalElements={totalElements}
        jobType={type}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        operationToHideCard={tab}
        dismissable={tab === 'dismissed'}
        source="/me"
        noDataComponent={() => (
          <EmptyState
            icon="icon-file"
            headingLevel="h2"
            title={noDataMessage}
            buttonLabel={translatr('web.talentmarketplace.main', 'ExploreCareerGrowthSection', {
              career_growth_section:
                type === OPPORTUNITY_TYPE.JOB_ROLE
                  ? omp('tm_tm_job_roles')
                  : omp('tm_tm_job_vacancies')
            })}
            onButtonClick={navigateToAll}
          />
        )}
      />
    </div>
  );
};

const mapStoreStateToProps = ({ currentUser, team }) => {
  return {
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en'
  };
};

OpportunityTab.propTypes = {
  type: PropTypes.string,
  getOpportunities: PropTypes.func,
  tab: PropTypes.string,
  noDataMessage: PropTypes.string,
  hasPagination: PropTypes.bool,
  currentUserLang: PropTypes.string
};

export default connect(mapStoreStateToProps)(OpportunityTab);
