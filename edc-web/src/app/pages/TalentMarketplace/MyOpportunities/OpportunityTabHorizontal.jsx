import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { mapJobRoles, mapJobVacancies } from '../Api';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import {
  buttonLabelMapping,
  OMP_URL_BY_TYPE,
  sortOrderOptions,
  defaultSortOptions
} from './SharedOpportunityTab/helpers';
import { connect } from 'react-redux';
import { Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import _ from 'lodash';
import { SkillsContextProvider } from '../shared/SkillsContext';
import './OpportunityTabHorizontal.scss';
import Pagination from 'centralized-design-system/src/Pagination';

const OpportunityTabHorizontal = ({
  type,
  getOpportunities,
  tab,
  noDataMessage,
  hasPagination,
  currentUserLang,
  label,
  pageSize,
  cardComponent: CardComponent,
  sortType = 'ID'
}) => {
  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalElements, setTotalElements] = useState(0);
  const [sortOrder, setSortOrder] = useState(sortOrderOptions.DESCENDING);

  const { search: searchParam, pathname } = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(searchParam);
  const currentPage = queryParams.get('page') ? parseInt(queryParams.get('page'), 10) : 1;

  const setCurrentPage = useCallback(
    page => {
      navigate({
        pathname,
        search: `?page=${page}`
      });
    },
    [history]
  );

  const paginate = ({ event }) => {
    if (event === 'next') {
      return setCurrentPage(currentPage + 1);
    }
    if (event === 'prev') {
      return setCurrentPage(currentPage - 1);
    }
    return setCurrentPage(event);
  };

  const loadOpportunities = useCallback((order, page, delay = 0) => {
    setLoading(true);
    const payload = {
      pageNumber: page,
      pageSize: pageSize,
      sortType: sortType,
      sortOrder: order,
      language: currentUserLang
    };
    _.delay(() => {
      getOpportunities(type, ...(hasPagination ? [payload] : [order, currentUserLang]))
        .then(response => {
          const { values, totalElements: total } = response;
          setTotalElements(total || 0);
          setOpportunities(
            type === OPPORTUNITY_TYPE.JOB_ROLE ? mapJobRoles(values) : mapJobVacancies(values)
          );
          setLoading(false);
        })
        .catch(error => {
          if (error !== 'canceled') {
            setLoading(false);
          }
          console.error(`Error in fetching ${type}`, error);
        });
    }, delay);
  }, []);

  useEffect(() => {
    if (hasPagination && !loading) setCurrentPage(1);
  }, [sortOrder]);

  useEffect(() => {
    loadOpportunities(sortOrder, currentPage);
  }, [sortOrder, currentPage]);

  return (
    <div className="opportunity-tab-horizontal block">
      {opportunities.length > 0 && (
        <div className="opportunity-tab-header">
          <Select
            id={`select-${tab}`}
            defaultValue={sortOrder}
            items={defaultSortOptions}
            onChange={option => setSortOrder(option.id)}
            title={translatr('web.talentmarketplace.main', 'SortBy') + ':'}
          />
        </div>
      )}
      {loading ? (
        <div className={`${tab}-tab opportunity-tab-horizontal-content`}>
          {new Array(6).fill(null).map((undefined, i) => (
            <CardComponent key={i} loading />
          ))}
        </div>
      ) : opportunities.length > 0 ? (
        <>
          <div className={`${tab}-tab opportunity-tab-horizontal-content`}>
            <h2 className="opportunity-tab-title">
              {label} ({totalElements})
            </h2>
            <div className="opportunity-tab-horizontal-cards">
              {opportunities.map(opportunity => (
                <SkillsContextProvider initSkills={opportunity.capabilities} key={opportunity.id}>
                  <CardComponent
                    applied={opportunity.appliedCount > 0}
                    status={opportunity.applicationStatus}
                    type={type}
                    id={opportunity.id}
                    title={opportunity.title}
                    mode={opportunity.mode}
                    locations={opportunity.locations}
                    applyDate={opportunity.applyDate}
                    loading={loading}
                  />
                </SkillsContextProvider>
              ))}
            </div>
          </div>
          {totalElements > opportunities.length && (
            <div className="opportunity-tab-horizontal-pagination">
              <Pagination
                postPerPage={pageSize}
                totalPosts={totalElements}
                paginate={paginate}
                activePage={currentPage}
                iconType
              />
            </div>
          )}
        </>
      ) : (
        <div className="my-opportunities-no-data-container">
          <EmptyState
            icon="icon-file"
            headingLevel="h2"
            title={noDataMessage}
            buttonLabel={buttonLabelMapping[type]}
            buttonLink={`/career/${OMP_URL_BY_TYPE[type]}`}
          />
        </div>
      )}
    </div>
  );
};

const mapStoreStateToProps = ({ currentUser, team }) => {
  return {
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en'
  };
};

OpportunityTabHorizontal.propTypes = {
  type: PropTypes.string,
  getOpportunities: PropTypes.func,
  tab: PropTypes.string,
  noDataMessage: PropTypes.string,
  hasPagination: PropTypes.bool,
  currentUserLang: PropTypes.string,
  label: PropTypes.string,
  cardComponent: PropTypes.object,
  pageSize: PropTypes.number,
  sortType: PropTypes.string
};

export default connect(mapStoreStateToProps)(OpportunityTabHorizontal);
