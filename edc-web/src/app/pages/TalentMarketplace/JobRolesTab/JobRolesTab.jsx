import React, { useEffect, useState, useCallback, useRef } from 'react';
import { connect } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { shouldShowTMJobRole, CARDS_POST_OPERATION_RELOAD_DELAY } from '../util';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { search } from 'edc-web-sdk/requests/extOpportunities';
import { mapJobRoles } from '../Api';
import Results from '../shared/Results';
import {
  transformFilters,
  prepareLovFiltersForView,
  prepareCustomFiltersForView,
  cleanupFilterNames,
  prepareOrgFiltersForView,
  isFiltersChanged,
  filterFullySelectedOrganizations
} from '../shared/filters/Filters.utils';
import {
  POST_PER_PAGE,
  TM_ROLE_FILTER_BUCKET_NAME,
  FILTER_TYPE,
  FILTERS_DEFAULT_ASSOCIATION_ID,
  TM_FILTER_SORT_BY
} from '../shared/filters/Filters.constants';
import OpportunitiesFilterPanel from '../shared/filters/OpportunitiesFilterPanel';
import OpportunitiesFilterActiveBar from '../shared/filters/OpportunitiesFilterActiveBar';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import SuggestedRolesCarousel from '@components/Suggested/SuggestedRolesCarousel';
import { bool, func, array, string, object } from 'prop-types';
import { track } from '@analytics/TrackWrapper';
import { TrackEvents, TrackEventProperties } from '@analytics/TrackEvents';
import { OMP_EVENTS_PAGE_NAMES, reportCareerGrowthTabVisit } from '@analytics/OmpEventRecorder';
import {
  tmSaveFiltersConfig,
  tmSaveSearchFilters
} from '../../../../app/actions/talentmarketplaceActions';
import _ from 'lodash';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { loadAllOrganizations, orgVisibility, orgAssociation } from '@actions/organizationsActions';
import { getAvailableFiltersCountersConfig as tmGetAvailableFiltersCountersConfig } from '@actions/availableFilterCountersActions';
import { LOCATION_USAGE_OPTIONS } from '../helpers';
import { useCountries, useJobFamilies } from '../shared/hooks';
import { useIsDevelopmentPlanEnabled } from '@pages/TalentMarketplace/DevelopmentPlan/hooks/useIsDevelopmentPlanEnabled';

const JobRolesTab = ({
  searchFilters,
  currentUser,
  currentUserLang,
  saveFiltersConfig,
  setCareerModalOpen,
  organizations,
  loadOrganizations,
  availableLocations,
  locationsEnabled,
  locationFieldVisibility,
  locationsUsageList,
  countriesLoading,
  isGeolocationEnabled,
  getAvailableFiltersCountersConfig,
  saveFilter,
  openCareerModal
}) => {
  const jobType = JOB_TYPE.ROLE;
  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingOrg, setLoadingOrg] = useState(false);
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hideMatchingDetails, setHideMatchingDetails] = useState(false);
  const [sortBy, setSortBy] = useState();
  const [sortOptions, setSortOptions] = useState(TM_FILTER_SORT_BY);
  const [notRefreshData, setNotRefreshData] = useState(false);
  const { showDevelopmentPlan } = useIsDevelopmentPlanEnabled();

  const dataFiltered =
    searchFilters.keyword ||
    (searchFilters.filters[FILTERS_DEFAULT_ASSOCIATION_ID] &&
      Object.keys(searchFilters.filters[FILTERS_DEFAULT_ASSOCIATION_ID]).length !== 0);
  const { search: searchParam, pathname } = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(searchParam);
  const currentPage = queryParams.get('page') ? parseInt(queryParams.get('page'), 10) : 1;
  const countries = useCountries();
  const jobFamilies = useJobFamilies();
  const searchFiltersRef = useRef({});

  const setCurrentPage = useCallback(
    page => {
      navigate({
        pathname,
        search: `?page=${page}`
      });
    },
    [history]
  );

  const loadOpportunities = useCallback((page, oppFilters, delay = 0) => {
    const filters = transformFilters(oppFilters);
    const isRecallCounters = isFiltersChanged(oppFilters, searchFiltersRef.current, false);
    filters.pageNumber = page;
    setLoading(true);
    _.delay(() => {
      const payload = {
        ...filters,
        organizationId: filterFullySelectedOrganizations(filters.organizationId),
        language: currentUserLang,
        isDismissedExcluded: false
      };
      if (isRecallCounters) {
        getAvailableFiltersCountersConfig(jobType, {
          ...payload,
          pageNumber: 1,
          isFilterAggregationEnabled: true
        });
      }

      search(payload)
        .then(response => {
          const { values, totalElements: total, totalPages: pages, context } = response;
          const { hideMatchingDetails: hideMatch, sortType, sortOrder } = context || {};
          setTotalElements(total || 0);
          setTotalPages(pages || 0);
          setOpportunities(mapJobRoles(values));
          setHideMatchingDetails(!!hideMatch);
          if (hideMatch && searchFilters?.sortBy?.id !== `${sortType}_${sortOrder}`) {
            setSortOptions(prevOptions => prevOptions.filter(f => !f.id.startsWith('MATCH_')));
            setSortBy(`${sortType}_${sortOrder}`);
            searchFiltersRef.current.sortBy = sortOptions.find(
              so => so.id === `${sortType}_${sortOrder}`
            );
            setNotRefreshData(true);
          }
          setLoading(false);
        })
        .catch(error => {
          if (error !== 'canceled') {
            setLoading(false);
          }
          console.error(`Error in fetching ${jobType}`, error);
        });
    }, delay);
  }, []);

  const updateOpportunities = useCallback(
    data => {
      if (totalPages === currentPage) {
        // last page
        setOpportunities(data);
        setTotalElements(totalElements - 1);
        if (data.length === 0 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      } else {
        loadOpportunities(currentPage, searchFilters, CARDS_POST_OPERATION_RELOAD_DELAY);
      }
    },
    [totalPages, currentPage, totalElements, searchFilters]
  );

  useEffect(() => {
    if (sortBy) {
      const newFilters = {
        ...searchFilters,
        sortBy: TM_FILTER_SORT_BY.find(item => item.id === sortBy.toUpperCase())
      };
      saveFilter(newFilters);
    }
  }, [sortBy]);

  useEffect(() => {
    reportCareerGrowthTabVisit(OMP_EVENTS_PAGE_NAMES.ROLES);
  }, []);

  useEffect(() => {
    if (organizations.get('config')?.enable) {
      if (!organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER]) {
        setLoadingOrg(true);
        loadOrganizations(currentUserLang, orgVisibility.JOB_ROLES_FILTER, orgAssociation.JOB_ROLE);
      } else {
        setLoadingOrg(false);
      }
    }
  }, [
    organizations.get('config')?.enable,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER]
  ]);

  useEffect(() => {
    const { SEARCH_FILTERS, SEARCH_KEYWORD, SEARCH_SORTBY } = TrackEventProperties.OMP;
    // if filters were changed and page is different than 1 set page to 1 (it will recall useEffect once again on change currentPage)
    if (
      !_.isEmpty(searchFiltersRef.current) &&
      isFiltersChanged(searchFilters, searchFiltersRef.current) &&
      currentPage !== 1
    ) {
      setCurrentPage(1);
      return;
    }
    if (notRefreshData) {
      setNotRefreshData(false);
      return;
    }
    loadOpportunities(currentPage, searchFilters);
    searchFiltersRef.current = searchFilters;
    track(TrackEvents.OMP.JOB_ROLES_PAGE_VIEWED, {
      [SEARCH_FILTERS]: cleanupFilterNames(searchFilters.filters[FILTERS_DEFAULT_ASSOCIATION_ID]),
      [SEARCH_KEYWORD]: searchFilters.keyword,
      [SEARCH_SORTBY]: searchFilters.sortBy
    });
  }, [searchFilters.filters, searchFilters.keyword, searchFilters.sortBy, currentPage]);

  useEffect(() => {
    const orgFilters = prepareOrgFiltersForView({
      organizations,
      orgKeys: organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER] || [],
      orgVisibility: orgVisibility.JOB_ROLE_FILTER
    });
    const lovFilters = prepareLovFiltersForView(jobType);
    const decoratedFilters = prepareCustomFiltersForView({
      currentUser: currentUser.toJS(),
      jobFamilies,
      customFilters: searchFilters.config.filter(
        itm => itm.type !== FILTER_TYPE.LOV && itm.type !== FILTER_TYPE.ORG
      ),
      locationsConfig: {
        locations: availableLocations,
        enabled: locationsEnabled,
        visibility: locationFieldVisibility,
        usageContext: LOCATION_USAGE_OPTIONS.JOB_ROLE_FILTER,
        usageList: locationsUsageList,
        countries: countries
      },
      isGeolocationEnabled
    });
    saveFiltersConfig([...lovFilters, ...decoratedFilters, ...orgFilters]);
  }, [
    currentUser,
    jobFamilies,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_ROLES_FILTER],
    organizations.get('levelForTypeAndVisibility'),
    availableLocations,
    locationsEnabled,
    locationFieldVisibility,
    locationsUsageList,
    isGeolocationEnabled
  ]);

  if (typeof countriesLoading === 'undefined') return null; // wait until redux store is informed that location fetch is started

  return (
    <div className="tm__roles-tab block">
      {/* filters section */}
      <OpportunitiesFilterPanel
        jobType={JOB_TYPE.ROLE}
        loading={loading || loadingOrg}
        locationUsageType={LOCATION_USAGE_OPTIONS.JOB_ROLE_FILTER}
        filtersState={searchFilters}
        opportunityTypeName={omp('tm_tm_job_roles')}
        sortOptions={sortOptions}
        mainLabel={translatr('web.common.main', 'Search')}
      />
      <OpportunitiesFilterActiveBar
        filtersState={searchFilters}
        opportunityTypeName={omp('tm_tm_job_roles')}
        counter={totalElements}
        loading={loading || loadingOrg}
      />

      {/* suggested section */}
      {!dataFiltered && shouldShowTMJobRole() && (
        <div className="row-container">
          <SuggestedRolesCarousel
            sortBy={searchFilters.sortBy}
            fullCard
            hideIfNoData={true}
            parentComponent="JobRolesTab"
            placeholderButtonAction={() => openCareerModal(setCareerModalOpen('work_experience'))}
            itemsOnCarousel={3}
            showDevelopmentPlan={showDevelopmentPlan}
          />
        </div>
      )}
      {/* all roles section */}
      <div className="all-roles-container">
        {!dataFiltered && (
          <h2 className="tab-heading m-margin-bottom m-margin-top omp-counter-field">
            {translatr('web.talentmarketplace.main', 'AllOpportunities', {
              opportunities: omp('tm_tm_job_roles')
            })}{' '}
            {totalElements > 0 && <span>({totalElements})</span>}
          </h2>
        )}
        <Results
          itemsPerPage={POST_PER_PAGE}
          loading={loading}
          opportunities={opportunities}
          setOpportunities={setOpportunities}
          reloadOpportunities={updateOpportunities}
          totalElements={totalElements}
          jobType={jobType}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          hideMatchingDetails={hideMatchingDetails}
          noDataComponent={() => (
            <EmptyState
              icon="icon-file"
              title={translatr(
                'web.talentmarketplace.main',
                'SorryNothingMatchesYourCriteriaTryDifferentKeywords'
              )}
            />
          )}
        />
      </div>
    </div>
  );
};

const mapDispatchToProps = dispatch => {
  return {
    saveFiltersConfig: newConfig =>
      dispatch(tmSaveFiltersConfig(newConfig, TM_ROLE_FILTER_BUCKET_NAME)),
    loadOrganizations: (lang, currentVisibility, currentAssociation) =>
      dispatch(loadAllOrganizations(lang, currentVisibility, currentAssociation)),
    getAvailableFiltersCountersConfig: (jobType, payload) =>
      dispatch(tmGetAvailableFiltersCountersConfig(jobType, payload)),
    saveFilter: newFilters => dispatch(tmSaveSearchFilters(newFilters)),
    openCareerModal: fn => dispatch(fn)
  };
};

const mapStoreStateToProps = ({
  talentmarketplaceReducer,
  currentUser,
  team,
  organizations,
  availableLocations,
  configService,
  locationsConfiguration
}) => {
  return {
    currentUser: currentUser,
    searchFilters: talentmarketplaceReducer.get(TM_ROLE_FILTER_BUCKET_NAME),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations,
    availableLocations: availableLocations.get('availableLocations'),
    locationsEnabled: locationsConfiguration.get('enable'),
    locationFieldVisibility: locationsConfiguration.get('visibility'),
    locationsUsageList: locationsConfiguration.get('usageList'),
    isGeolocationEnabled: configService.get('omp')?.['geolocation']?.['enable_geolocation'],
    countriesLoading: availableLocations.get('countriesLoading')
  };
};

JobRolesTab.propTypes = {
  searchFilters: object,
  currentUser: object,
  currentUserLang: string,
  saveFiltersConfig: func,
  setCareerModalOpen: func,
  organizations: object,
  loadOrganizations: func,
  availableLocations: array,
  locationsEnabled: bool,
  isGeolocationEnabled: bool,
  locationFieldVisibility: object,
  locationsUsageList: array,
  countriesLoading: bool,
  getAvailableFiltersCountersConfig: func,
  hideMatchingDetails: bool,
  saveFilter: func,
  openCareerModal: func
};

export default connect(mapStoreStateToProps, mapDispatchToProps)(JobRolesTab);
