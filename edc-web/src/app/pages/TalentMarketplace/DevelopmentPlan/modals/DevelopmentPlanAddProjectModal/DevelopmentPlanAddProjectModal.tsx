import React, { useCallback, useEffect, useState, useMemo } from 'react';
import <PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalFooter
} from 'centralized-design-system/src/Modals';
import _ from 'lodash';
import Loading from 'centralized-design-system/src/Loading';
import { Button } from 'centralized-design-system/src/Buttons';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import FocusLock from 'react-focus-lock';
import { SearchInput } from 'centralized-design-system/src/Inputs';
import {
  DevPlanProject,
  DevPlanStep,
  DurationEntry,
  ProjectFilters,
  ProjectFilterTypes
} from '../../constants';
import { useDispatch, useSelector } from 'react-redux';
import ProjectSearchResults from './components/ProjectSearchResults';
import './../common/DevelopmentPlanAddItemModal.scss';
import { addDevPlanProject } from '@actions/developmentPlanActions';
import { AppState } from 'src/app/types/AppState/AppState';
import { useEscapeKey } from '@pages/Sourcing/hooks/useEscapeKey';
import { addDevPlanProjects } from 'edc-web-sdk/requests/developmentPlan';
import { shouldEditDevPlanOnFly } from '../../utils';
import { getTimeZone } from '@actions/timeZonesActions';
import { fetchProjects, handleSearchDebounced } from './utils';
import ProjectSearchFilter from './components/ProjectSearchFilter';
import { durationRanges } from '@pages/TalentMarketplace/shared/filters/Filters.constants';
import { getAvailableLocations } from '@actions/availableLocationsActions';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import {
  formatLocationByFields,
  isLocationVisible,
  LOCATION_USAGE_OPTIONS
} from '@pages/TalentMarketplace/helpers';

interface DevelopmentPlanAddProjectModalProps {
  closeModal: () => void;
  currentStepData: DevPlanStep;
}

const DevelopmentPlanAddProjectModal: React.FC<DevelopmentPlanAddProjectModalProps> = ({
  closeModal,
  currentStepData
}) => {
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [projects, setProjects] = useState<DevPlanProject[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [isFlyoutOpen, setFlyoutOpen] = useState(false);
  const [recommendedProjects, setRecommendedProjects] = useState<DevPlanProject[]>([]);
  const [recommendationsLoaded, setRecommendationsLoaded] = useState<boolean>(false);
  const [appliedFilters, setAppliedFilters] = useState<ProjectFilters>({
    [ProjectFilterTypes.SKILLS]: [],
    [ProjectFilterTypes.TIMEZONES]: [],
    [ProjectFilterTypes.LANGUAGES]: [],
    [ProjectFilterTypes.DURATION]: [],
    [ProjectFilterTypes.LOCATION]: []
  });
  const [languages, setLanguages] = useState([]);
  const [locations, setLocations] = useState([]);
  const durations: Array<DurationEntry> = useMemo(
    () => durationRanges.map(item => [item[0], item[1]]),
    []
  );

  const focusReturnElement = useSelector((state: AppState) =>
    state.developmentPlan.get('focusReturnElement')
  );
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const currentUserLang = useSelector(
    (state: AppState) =>
      state.currentUser.get('profile')?.get?.('language') ||
      state.currentUser.get('profile')?.language ||
      'en'
  );
  const availableLocations = useSelector((state: AppState) =>
    state.availableLocations.get('availableLocations')
  );
  const locationsEnabled = useSelector((state: AppState) =>
    state.locationsConfiguration.get('enable')
  );
  const locationFieldAssociation = useSelector((state: AppState) =>
    state.locationsConfiguration.get('association')
  );
  const locationFieldVisibility = useSelector((state: AppState) =>
    state.locationsConfiguration.get('visibility')
  );

  const timeZone = useSelector((state: AppState) => state.timeZones.get('timeZones'));
  const currentUserId = useSelector((state: AppState) => state.currentUser.get('id'));
  const dispatch: any = useDispatch();

  const nmbOfFiltersApplied = useMemo(
    () =>
      appliedFilters[ProjectFilterTypes.SKILLS].length +
      appliedFilters[ProjectFilterTypes.TIMEZONES].length +
      appliedFilters[ProjectFilterTypes.LANGUAGES].length +
      appliedFilters[ProjectFilterTypes.DURATION].length +
      appliedFilters[ProjectFilterTypes.LOCATION].length,
    [appliedFilters]
  );

  const handleSearch = useCallback(
    handleSearchDebounced((query: string) => {
      setSearchTerm(query);
      setSelectedProjects([]);
      fetchProjects(
        query,
        appliedFilters[ProjectFilterTypes.SKILLS],
        appliedFilters[ProjectFilterTypes.TIMEZONES],
        appliedFilters[ProjectFilterTypes.LANGUAGES],
        appliedFilters[ProjectFilterTypes.DURATION],
        appliedFilters[ProjectFilterTypes.LOCATION],
        currentStepData,
        dispatch,
        setProjects,
        setLoading,
        recommendationsLoaded,
        setRecommendedProjects,
        setRecommendationsLoaded,
        currentUserId
      );
    }, 300),
    [currentStepData, appliedFilters]
  );

  const handleItemSelection = (id: string) => {
    setSelectedProjects(prevSelectedItems =>
      prevSelectedItems.includes(id)
        ? prevSelectedItems.filter(item => item !== id)
        : [...prevSelectedItems, id]
    );
  };

  const showAddSuccessSnackBar = () => {
    dispatch(
      openSnackBar(
        translatr('web.talentmarketplace.development-plan', 'OpportunityAddedToPlan', {
          opportunity: omp('tm_tm_project')
        }),
        'success'
      )
    );
  };

  const handleAddProject = async () => {
    if (shouldEditDevPlanOnFly(currentStepData)) {
      try {
        const response: DevPlanProject[] = await addDevPlanProjects(
          currentStepData.roleId,
          selectedProjects.map(id => ({ id }))
        );
        response.forEach(project => {
          dispatch(addDevPlanProject(currentStepData.roleId, project));
        });
        showAddSuccessSnackBar();
      } catch (e) {
        console.error('Error adding project:', e);
      }
    } else {
      selectedProjects.forEach(projectId => {
        dispatch(
          addDevPlanProject(
            currentStepData.roleId,
            _.unionBy(projects, recommendedProjects, 'id').find(
              (p: DevPlanProject) => p.id === projectId
            )
          )
        );
      });
      showAddSuccessSnackBar();
    }
    closeModal();
  };

  const handleApplyFilters = (filters: ProjectFilters) => {
    setAppliedFilters(filters);
    setSelectedProjects([]);
    fetchProjects(
      searchTerm,
      filters[ProjectFilterTypes.SKILLS],
      filters[ProjectFilterTypes.TIMEZONES],
      filters[ProjectFilterTypes.LANGUAGES],
      filters[ProjectFilterTypes.DURATION],
      filters[ProjectFilterTypes.LOCATION],
      currentStepData,
      dispatch,
      setProjects,
      setLoading,
      recommendationsLoaded,
      setRecommendedProjects,
      setRecommendationsLoaded,
      currentUserId
    );
    setFlyoutOpen(false);
  };

  const handleEscKey = () => {
    if (!isFlyoutOpen) {
      closeModal();
    }
  };

  useEscapeKey(handleEscKey);

  useEffect(() => {
    try {
      const languageConfig = window?.__edOrgData?.languages || {};

      if (languageConfig && typeof languageConfig === 'object') {
        const languageOptions = Object.entries(languageConfig).map(([lang, label]) => [
          lang,
          label
        ]);

        setLanguages(languageOptions);
      } else {
        setLanguages([]);
      }
      handleSearch('');
    } catch (error) {
      console.error('Error fetching languages:', error); // Log error for debugging
      setLanguages([]);
    }
    if (!availableLocations) {
      dispatch(getAvailableLocations(currentUserLang));
    }
    if (!timeZone) {
      dispatch(getTimeZone());
    }
  }, []);

  useEffect(() => {
    if (availableLocations.length > 0) {
      const isLocationEnabled =
        locationsEnabled &&
        isLocationVisible(locationFieldVisibility, LOCATION_USAGE_OPTIONS.PROJECT_FILTER);
      const locOptions =
        availableLocations?.map((loc: any) => [
          formatLocationByFields(
            loc,
            locationFieldVisibility,
            LOCATION_USAGE_OPTIONS.PROJECT_FILTER
          ),
          loc.id
        ]) || [];
      setLocations(isLocationEnabled ? locOptions : []);
    }
  }, [availableLocations, locationsEnabled, locationFieldVisibility, locationFieldAssociation]);

  useEffect(() => {
    return () => {
      handleSearch.cancel(); // Cleanup debounce on unmount
    };
  }, [handleSearch]);

  const allSectionProjects = nmbOfFiltersApplied
    ? projects
    : _.differenceBy(projects || [], recommendedProjects || [], 'id');

  return (
    <>
      {/* @ts-ignore */}
      <Modal size="small" className="devplan-additem-modal">
        <FocusLock
          onDeactivation={() => {
            focusReturnElement?.focus();
          }}
        >
          {/* @ts-ignore */}
          <ModalHeader
            title={translatr(
              'web.talentmarketplace.development-plan',
              'AddOpportunityItemModalTitle',
              { opportunityType: omp('tm_tm_project')?.toLowerCase() }
            )}
            onClose={closeModal}
          />
          <ModalContent>
            <div className="devplan-additem-modal__content">
              <label htmlFor="devplan-projectsearch__input">
                {translatr('web.talentmarketplace.development-plan', 'SearchOpportunityItem', {
                  opportunityType: omp('tm_tm_project')?.toLowerCase()
                })}
              </label>
              <div className="devplan-additem-modal__search-container">
                <SearchInput
                  //@ts-ignore
                  ariaLabel={translatr(
                    'web.talentmarketplace.development-plan',
                    'SearchOpportunityItem',
                    {
                      opportunityType: omp('tm_tm_project')
                    }
                  )}
                  value={searchTerm}
                  placeholder={translatr('web.talentmarketplace.development-plan', 'TypeHere')}
                  onSearch={(query: string) => handleSearch(query)}
                  disabled={loading}
                />
                <Button
                  ref={filterButtonRef}
                  color="secondary"
                  variant="ghost"
                  padding="xsmall"
                  aria-label={translatr('web.common.main', 'Filters')}
                  aria-describedby="filters-counter-btn"
                  aria-disabled={loading}
                  onClick={() => setFlyoutOpen(!isFlyoutOpen)}
                >
                  <i className="filter-button icon-filter" />
                  <span className="sr-only" id="filters-counter-btn">
                    {translatr('web.talentmarketplace.main', 'XFilterApplied', {
                      nmb: nmbOfFiltersApplied
                    })}
                  </span>
                  {nmbOfFiltersApplied > 0 && (
                    <div className="filter-counter">{nmbOfFiltersApplied}</div>
                  )}
                </Button>
              </div>

              <div className="devplan-additem-modal__search-results mt-8">
                {loading && <Loading center />}
                {!nmbOfFiltersApplied &&
                  !searchTerm &&
                  recommendedProjects?.length > 0 &&
                  !loading && (
                    <ProjectSearchResults
                      headerTitle={
                        allSectionProjects?.length
                          ? translatr('web.common.main', 'Recommended')
                          : null
                      }
                      loading={loading}
                      projectList={recommendedProjects}
                      selectedProjects={selectedProjects}
                      toggleProjectSelection={handleItemSelection}
                      saving={false}
                      loadingError={false}
                      currentStepData={currentStepData}
                      searchTerm={searchTerm}
                    />
                  )}
                {(allSectionProjects?.length > 0 ||
                  nmbOfFiltersApplied ||
                  searchTerm ||
                  (!allSectionProjects?.length && !recommendedProjects?.length)) &&
                  !loading && (
                    <ProjectSearchResults
                      headerTitle={
                        nmbOfFiltersApplied || searchTerm || !recommendedProjects?.length
                          ? null
                          : translatr('web.common.main', 'All')
                      }
                      loading={loading}
                      projectList={allSectionProjects}
                      selectedProjects={selectedProjects}
                      toggleProjectSelection={handleItemSelection}
                      saving={false}
                      loadingError={false}
                      currentStepData={currentStepData}
                      searchTerm={searchTerm}
                    />
                  )}
              </div>
            </div>
            <ProjectSearchFilter
              isFlyoutOpen={isFlyoutOpen}
              setFlyoutOpen={setFlyoutOpen}
              applyFilters={handleApplyFilters}
              skillList={currentStepData?.missingSkills.map(i => i.skill) || []}
              timezoneList={timeZone}
              languageList={languages}
              durationList={durations}
              locationList={locations}
              onClose={() => {
                setFlyoutOpen(false);
                setTimeout(() => {
                  filterButtonRef.current?.focus();
                }, 50);
              }}
            />
          </ModalContent>
          <ModalFooter className="justify-end">
            <span className="devplan-additem-modal__counter">
              {translatr('web.talentmarketplace.development-plan', 'SelectedItems')}{' '}
              <span>{selectedProjects.length}</span>
            </span>
            <Button
              color="caution"
              variant="borderless"
              padding="xsmall"
              disabled={!selectedProjects.length}
              onClick={() => setSelectedProjects([])}
            >
              {translatr('web.talentmarketplace.development-plan', 'Deselect')}
            </Button>
            <Button color="primary" onClick={handleAddProject} disabled={!selectedProjects.length}>
              {translatr('web.talentmarketplace.development-plan', 'AddNew')}
            </Button>
          </ModalFooter>
        </FocusLock>
      </Modal>
    </>
  );
};

export default DevelopmentPlanAddProjectModal;
