import { debounce } from 'lodash';
import { FilterCategory, FilterContext } from 'opportunity-marketplace/ProjectsTab/helpers';
import { search } from 'edc-web-sdk/requests/careerOportunities.v2';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { translatr } from 'centralized-design-system/src/Translatr';
import { DevPlanProject, DevPlanStep } from '../../constants';
import { getDevPlanProjectRecommendations } from 'edc-web-sdk/requests/developmentPlan';

const PAGE_SIZE = 36; // Define a constant for page size

const buildProjectSearchPayload = (
  searchTerm: string,
  skillIds: string[],
  timezones: string[],
  languages: string[],
  durations: string[],
  locations: string[]
) => ({
  filters: [
    {
      filterCategory: FilterCategory.SEARCH_TEXT,
      filterData: [{ filterValue: searchTerm }]
    },

    ...(skillIds.length
      ? [
        {
          filterCategory: FilterCategory.SKILL,
          filterData: skillIds.map(skillId => ({ filterValue: skillId }))
        }
      ]
      : []),
    ...(timezones.length
      ? [
        {
          filterCategory: FilterCategory.TIMEZONE,
          filterData: timezones.map(tz => ({ filterValue: tz }))
        }
      ]
      : []),
    ...(languages.length
      ? [
        {
          filterCategory: FilterCategory.LANGUAGE,
          filterData: languages.map(lang => ({ filterValue: lang }))
        }
      ]
      : []),
    ...(durations.length
      ? [
        {
          filterCategory: FilterCategory.DURATION,
          filterData: durations.map(duration => ({ filterValue: duration }))
        }
      ]
      : []),
    ...(locations.length
      ? [
        {
          filterCategory: FilterCategory.LOCATION,
          filterData: locations.map(location => ({ filterValue: location }))
        }
      ]
      : [])
  ],
  pageNumber: 1,
  pageSize: PAGE_SIZE,
  searchContext: FilterContext.PROJECT,
  sortCriteria: 'RELEVANCE',
  type: 'project'
});

export const fetchProjects = async (
  searchTerm: string,
  skillIds: string[],
  timezones: string[],
  languages: string[],
  durations: string[],
  locations: string[],
  currentStepData: DevPlanStep,
  dispatch: any,
  setProjects: (projects: any[]) => void,
  setLoading: (loading: boolean) => void,
  recommendationsLoaded: boolean,
  setRecommendedProjects: React.Dispatch<React.SetStateAction<DevPlanProject[]>>,
  setRecommendationsLoaded: React.Dispatch<React.SetStateAction<boolean>>,
  currentUserId?: string
) => {
  setLoading(true);
  const payload = buildProjectSearchPayload(
    searchTerm,
    skillIds,
    timezones,
    languages,
    durations,
    locations
  );

  try {
    const MAX_RECOMMENDATIONS = 5;
    const internalPromise = search(payload);
    const recommendationPromise = recommendationsLoaded
      ? Promise.resolve(null)
      : getDevPlanProjectRecommendations(currentStepData?.roleId, {
        excludedIds: currentStepData?.projects
          ?.filter(({ status }) => status !== 'REMOVED')
          ?.map(({ id }) => id)
          ?.join(',')
      });

    const [internalResult, recommendationResult] = await Promise.allSettled([
      internalPromise,
      recommendationPromise
    ]);

    if (internalResult.status === 'fulfilled') {
      const { searchResult } = internalResult.value;
      if (!searchResult) {
        throw new Error('Bad project search data');
      }
      if (currentUserId) {
        setProjects(searchResult.filter((proj: any) => proj.creator !== currentUserId));
      } else {
        setProjects(searchResult);
      }
    } else {
      console.error('Error fetching projects internal search:', internalResult.reason);
      setProjects([]);
      dispatch(
        openSnackBar(
          translatr(
            'web.talentmarketplace.main',
            'ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain'
          ),
          'error'
        )
      );
    }

    if (recommendationResult.status === 'fulfilled' && !recommendationsLoaded) {
      const recommendations = recommendationResult.value;
      setRecommendedProjects(recommendations?.slice?.(0, MAX_RECOMMENDATIONS) || []);
      setRecommendationsLoaded(true);
    } else if (recommendationResult.status === 'rejected') {
      console.error('Error fetching project recommendations:', recommendationResult.reason);
    }
  } catch (error) {
    console.error('Unexpected error fetching projects:', error);
    setProjects([]);
    dispatch(
      openSnackBar(
        translatr(
          'web.talentmarketplace.main',
          'ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain'
        ),
        'error'
      )
    );
  } finally {
    setLoading(false);
  }
};

export const handleSearchDebounced = (callback: (query: string) => void, delay: number) =>
  debounce(callback, delay);
