import { debounce } from 'lodash';
import { FilterCategory, FilterContext } from 'opportunity-marketplace/ProjectsTab/helpers';
import { search } from 'edc-web-sdk/requests/careerOportunities.v2';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { translatr } from 'centralized-design-system/src/Translatr';
import { mapSearchedMentorsToDevPlanMentors } from '../../DevelopmentPlanMain/DevPlanMentor/utils';
import { getDevPlanMentorRecommendations } from 'edc-web-sdk/requests/developmentPlan';
import { DevPlanMentor, DevPlanStep } from '../../constants';

const PAGE_SIZE = 36; // Define a constant for page size

const buildMentorSearchPayload = (
  searchTerm: string,
  skillIds: string[],
  timezones: string[],
  languages: string[]
) => ({
  filters: [
    {
      filterCategory: FilterCategory.SEARCH_TEXT,
      filterData: [{ filterValue: searchTerm }]
    },
    ...(skillIds.length
      ? [
        {
          filterCategory: FilterCategory.SKILL,
          filterData: skillIds.map(skillId => ({ filterValue: skillId }))
        }
      ]
      : []),
    ...(timezones.length
      ? [
        {
          filterCategory: FilterCategory.TIMEZONE,
          filterData: timezones.map(tz => ({ filterValue: tz }))
        }
      ]
      : []),
    ...(languages.length
      ? [
        {
          filterCategory: FilterCategory.LANGUAGE,
          filterData: languages.map(lang => ({ filterValue: lang }))
        }
      ]
      : [])
  ],
  pageNumber: 1,
  pageSize: PAGE_SIZE,
  searchContext: FilterContext.MENTORSHIP,
  sortCriteria: 'RELEVANCE',
  type: 'mentorship'
});

export const fetchMentors = async (
  searchTerm: string,
  skillIds: string[],
  timezones: string[],
  languages: string[],
  currentStepData: DevPlanStep,
  dispatch: any,
  setMentors: (mentors: any[]) => void,
  setLoading: (loading: boolean) => void,
  recommendationsLoaded: boolean,
  setRecommendedMentors: React.Dispatch<React.SetStateAction<DevPlanMentor[]>>,
  setRecommendationsLoaded: React.Dispatch<React.SetStateAction<boolean>>,
  currentUserId?: string
) => {
  setLoading(true);
  const payload = buildMentorSearchPayload(
    searchTerm,
    skillIds,
    timezones,
    languages
  );
  const MAX_RECOMMENDATIONS = 5;

  try {
    const internalPromise = search(payload);

    const recommendationPromise = recommendationsLoaded
      ? Promise.resolve(null)
      : getDevPlanMentorRecommendations(currentStepData?.roleId, {
        excludedIds: currentStepData?.mentors
          ?.filter(({ status }) => status !== 'REMOVED')
          ?.map(({ id }) => id)
          ?.join(',')
      });

    const [internalResult, recommendationResult] = await Promise.allSettled([
      internalPromise,
      recommendationPromise
    ]);

    let searchResult: any = null;

    if (internalResult.status === 'fulfilled') {
      const { searchResult: result } = internalResult.value;
      if (!result) {
        throw new Error('Bad mentorship search data');
      }

      if (currentUserId) {
        searchResult = result.filter((mentor: any) => mentor.userId.id !== Number(currentUserId));
      } else {
        searchResult = result;
      }

      setMentors(mapSearchedMentorsToDevPlanMentors(searchResult));
    } else {
      console.error('Error fetching mentors internal search:', internalResult.reason);
      setMentors([]);
      dispatch(
        openSnackBar(
          translatr(
            'web.talentmarketplace.main',
            'ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain'
          ),
          'error'
        )
      );
    }

    if (recommendationResult.status === 'fulfilled' && !recommendationsLoaded) {
      const recommendations = recommendationResult.value;
      setRecommendedMentors(recommendations?.slice?.(0, MAX_RECOMMENDATIONS) || []);
      setRecommendationsLoaded(true);
    } else if (recommendationResult.status === 'rejected') {
      console.error('Error fetching mentor recommendations:', recommendationResult.reason);
    }
  } catch (error) {
    console.error('Unexpected error fetching mentors:', error);
    setMentors([]);
    dispatch(
      openSnackBar(
        translatr(
          'web.talentmarketplace.main',
          'ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain'
        ),
        'error'
      )
    );
  } finally {
    setLoading(false);
  }
};

export const handleSearchDebounced = (callback: (query: string) => void, delay: number) =>
  debounce(callback, delay);
