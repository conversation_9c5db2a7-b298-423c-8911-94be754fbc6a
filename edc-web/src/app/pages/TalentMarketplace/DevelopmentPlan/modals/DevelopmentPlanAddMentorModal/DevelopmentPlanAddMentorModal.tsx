import React, { useCallback, useEffect, useState, useMemo } from 'react';
import <PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalFooter
} from 'centralized-design-system/src/Modals';
import _ from 'lodash';
import Loading from 'centralized-design-system/src/Loading';
import { Button } from 'centralized-design-system/src/Buttons';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import FocusLock from 'react-focus-lock';
import { SearchInput } from 'centralized-design-system/src/Inputs';
import {
  DevPlanMentor,
  DevPlanSkill,
  DevPlanStep,
  MentorFilterTypes,
  MentorFilters
} from '../../constants';
import { useDispatch, useSelector } from 'react-redux';
import MentorSearchResults from './components/MentorSearchResults';
import { addDevPlanMentor } from '@actions/developmentPlanActions';
import { AppState } from 'src/app/types/AppState/AppState';
import { useEscapeKey } from '@pages/Sourcing/hooks/useEscapeKey';
import { addDevPlanMentors } from 'edc-web-sdk/requests/developmentPlan';
import { shouldEditDevPlanOnFly } from '../../utils';
import MentorSearchFilter from './components/MentorSearchFilter';
import { getTimeZone } from '@actions/timeZonesActions';
import { fetchMentors, handleSearchDebounced } from './utils';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import './../common/DevelopmentPlanAddItemModal.scss';

interface DevelopmentPlanAddMentorModalProps {
  closeModal: () => void;
  currentStepData: DevPlanStep;
}

const DevelopmentPlanAddMentorModal: React.FC<DevelopmentPlanAddMentorModalProps> = ({
  closeModal,
  currentStepData
}) => {
  const [selectedMentors, setSelectedMentors] = useState<string[]>([]);
  const [mentors, setMentors] = useState<DevPlanMentor[]>([]);
  const [recommendedMentors, setRecommendedMentors] = useState<DevPlanMentor[]>([]);
  const [recommendationsLoaded, setRecommendationsLoaded] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [isFlyoutOpen, setFlyoutOpen] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState<MentorFilters>({
    [MentorFilterTypes.SKILLS]: [],
    [MentorFilterTypes.TIMEZONES]: [],
    [MentorFilterTypes.LANGUAGES]: []
  });
  const [languages, setLanguages] = useState([]);
  const focusReturnElement = useSelector((state: AppState) =>
    state.developmentPlan.get('focusReturnElement')
  );
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const timeZone = useSelector((state: AppState) => state.timeZones.get('timeZones'));
  const currentUserId = useSelector((state: AppState) => state.currentUser.get('id'));

  const dispatch: any = useDispatch();

  const nmbOfFiltersApplied = useMemo(
    () =>
      appliedFilters[MentorFilterTypes.SKILLS].length +
      appliedFilters[MentorFilterTypes.TIMEZONES].length +
      appliedFilters[MentorFilterTypes.LANGUAGES].length,
    [appliedFilters]
  );

  /* Button handlers */
  const handleSearch = useCallback(
    handleSearchDebounced((query: string) => {
      setSearchTerm(query);
      setSelectedMentors([]);
      fetchMentors(
        query,
        appliedFilters[MentorFilterTypes.SKILLS],
        appliedFilters[MentorFilterTypes.TIMEZONES],
        appliedFilters[MentorFilterTypes.LANGUAGES],
        currentStepData,
        dispatch,
        setMentors,
        setLoading,
        recommendationsLoaded,
        setRecommendedMentors,
        setRecommendationsLoaded,
        currentUserId
      );
    }, 300),
    [currentStepData, appliedFilters]
  );

  useEffect(() => {
    return () => {
      handleSearch.cancel(); // Cleanup debounce on unmount
    };
  }, [handleSearch]);

  const handleApplyFilters = (selectedFilters: MentorFilters) => {
    setAppliedFilters(selectedFilters);
    setSelectedMentors([]);
    fetchMentors(
      searchTerm,
      selectedFilters[MentorFilterTypes.SKILLS],
      selectedFilters[MentorFilterTypes.TIMEZONES],
      selectedFilters[MentorFilterTypes.LANGUAGES],
      currentStepData,
      dispatch,
      setMentors,
      setLoading,
      recommendationsLoaded,
      setRecommendedMentors,
      setRecommendationsLoaded,
      currentUserId
    );
    setFlyoutOpen(false);
  };

  const handleAddMentor = async () => {
    try {
      if (shouldEditDevPlanOnFly(currentStepData)) {
        const response: DevPlanMentor[] = await addDevPlanMentors(
          currentStepData.roleId,
          selectedMentors.map(id => ({ id }))
        );
        response.forEach(mentor => {
          dispatch(addDevPlanMentor(currentStepData.roleId, mentor));
        });
      } else {
        selectedMentors.forEach(mentorId => {
          dispatch(
            addDevPlanMentor(
              currentStepData.roleId,
              _.unionBy(mentors, recommendedMentors, 'id').find(
                (m: DevPlanMentor) => m.id === mentorId
              )
            )
          );
        });
      }
      dispatch(
        openSnackBar(
          translatr('web.talentmarketplace.development-plan', 'OpportunityAddedToPlan', {
            opportunity: omp('tm_tm_mentor')
          }),
          'success'
        )
      );
      closeModal();
    } catch (error) {
      console.error('Error adding mentor:', error); // Log error for debugging
    }
  };

  const handleItemSelection = (id: string) => {
    setSelectedMentors(prevSelectedItems =>
      prevSelectedItems.includes(id)
        ? prevSelectedItems.filter(item => item !== id)
        : [...prevSelectedItems, id]
    );
  };

  const allMissingSkillsList: DevPlanSkill[] =
    currentStepData?.missingSkills.map(i => i.skill) || [];

  const handleEscKey = () => {
    if (!isFlyoutOpen) {
      closeModal();
    }
  };

  useEscapeKey(handleEscKey);

  useEffect(() => {
    try {
      const languageConfig = window?.__edOrgData?.languages || {};

      if (languageConfig && typeof languageConfig === 'object') {
        const languageOptions = Object.entries(languageConfig).map(([lang, label]) => [
          lang,
          label
        ]);

        setLanguages(languageOptions);
      } else {
        setLanguages([]);
      }

      handleSearch('');
    } catch (error) {
      console.error('Error fetching languages:', error); // Log error for debugging
      setLanguages([]);
    }
  }, []);

  useEffect(() => {
    if (!timeZone) {
      dispatch(getTimeZone());
    }
  }, [timeZone, dispatch]);

  const allSectionMentors = nmbOfFiltersApplied
    ? mentors
    : _.differenceBy(mentors || [], recommendedMentors || [], 'id');

  return (
    <>
      {/* @ts-ignore */}
      <Modal size="small" className="devplan-additem-modal">
        <FocusLock
          onDeactivation={() => {
            focusReturnElement?.focus();
          }}
        >
          {/* @ts-ignore */}
          <ModalHeader
            title={translatr(
              'web.talentmarketplace.development-plan',
              'AddOpportunityItemModalTitle',
              { opportunityType: omp('tm_tm_mentor')?.toLowerCase() }
            )}
            onClose={closeModal}
          />
          <ModalContent>
            <div className="devplan-additem-modal__content">
              <label htmlFor="devplan-mentorsearch__input">
                {translatr('web.talentmarketplace.development-plan', 'SearchOpportunityItem', {
                  opportunityType: omp('tm_tm_mentor')?.toLowerCase()
                })}
              </label>
              <div className="devplan-additem-modal__search-container">
                <SearchInput
                  //@ts-ignore
                  ariaLabel={translatr(
                    'web.talentmarketplace.development-plan',
                    'SearchOpportunityItem',
                    {
                      opportunityType: omp('tm_tm_mentor')
                    }
                  )}
                  value={searchTerm}
                  placeholder={translatr('web.talentmarketplace.development-plan', 'TypeHere')}
                  onSearch={(query: string) => handleSearch(query)}
                  disabled={loading}
                />
                <Button
                  ref={filterButtonRef}
                  color="secondary"
                  variant="ghost"
                  padding="xsmall"
                  aria-label={translatr('web.common.main', 'Filters')}
                  aria-describedby="filters-counter-btn"
                  aria-disabled={loading}
                  onClick={() => setFlyoutOpen(!isFlyoutOpen)}
                >
                  <i className="filter-button icon-filter" />
                  <span className="sr-only" id="filters-counter-btn">
                    {translatr('web.talentmarketplace.main', 'XFilterApplied', {
                      nmb: nmbOfFiltersApplied
                    })}
                  </span>
                  {nmbOfFiltersApplied > 0 && (
                    <div className="filter-counter">{nmbOfFiltersApplied}</div>
                  )}
                </Button>
              </div>
              <div className="devplan-additem-modal__search-results mt-8">
                {loading && <Loading center />}
                {!nmbOfFiltersApplied &&
                  !searchTerm &&
                  recommendedMentors?.length > 0 &&
                  !loading && (
                    <MentorSearchResults
                      headerTitle={
                        allSectionMentors?.length
                          ? translatr('web.common.main', 'Recommended')
                          : null
                      }
                      loading={loading}
                      mentorList={recommendedMentors}
                      selectedMentors={selectedMentors}
                      toggleMentorSelection={handleItemSelection}
                      saving={false}
                      loadingError={false}
                      currentStepData={currentStepData}
                      searchTerm={searchTerm}
                    />
                  )}
                {(allSectionMentors?.length > 0 ||
                  nmbOfFiltersApplied ||
                  searchTerm ||
                  (!allSectionMentors?.length && !recommendedMentors?.length)) &&
                  !loading && (
                    <MentorSearchResults
                      headerTitle={
                        nmbOfFiltersApplied || searchTerm || !recommendedMentors?.length
                          ? null
                          : translatr('web.common.main', 'All')
                      }
                      loading={loading}
                      mentorList={allSectionMentors}
                      selectedMentors={selectedMentors}
                      toggleMentorSelection={handleItemSelection}
                      saving={false}
                      loadingError={false}
                      currentStepData={currentStepData}
                      searchTerm={searchTerm}
                    />
                  )}
              </div>
            </div>
            <MentorSearchFilter
              isFlyoutOpen={isFlyoutOpen}
              setFlyoutOpen={setFlyoutOpen}
              applyFilters={handleApplyFilters}
              skillList={allMissingSkillsList || []}
              timezoneList={timeZone}
              languageList={languages}
              onClose={() => {
                setFlyoutOpen(false);
                setTimeout(() => {
                  filterButtonRef.current?.focus();
                }, 50);
              }}
            />
          </ModalContent>
          <ModalFooter className="justify-end">
            <span className="devplan-additem-modal__counter">
              {translatr('web.talentmarketplace.development-plan', 'SelectedItems')}{' '}
              <span>{selectedMentors.length}</span>
            </span>
            <Button
              color="caution"
              variant="borderless"
              padding="xsmall"
              disabled={!selectedMentors.length}
              onClick={() => setSelectedMentors([])}
            >
              {translatr('web.talentmarketplace.development-plan', 'Deselect')}
            </Button>
            <Button color="primary" onClick={handleAddMentor} disabled={!selectedMentors.length}>
              {translatr('web.talentmarketplace.development-plan', 'AddNew')}
            </Button>
          </ModalFooter>
        </FocusLock>
      </Modal>
    </>
  );
};

export default DevelopmentPlanAddMentorModal;
