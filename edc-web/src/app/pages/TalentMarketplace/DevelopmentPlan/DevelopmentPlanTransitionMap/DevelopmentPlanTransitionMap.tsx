import { useEffect, useState } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Avatar from 'centralized-design-system/src/MUIComponents/Avatar';
import { useProfileContainerContext } from '@pages/MyProfile/Common/ProfileContainerContext';
import './DevelopmentPlanTransitionMap.scss';
import { useTheme } from 'centralized-design-system/src/Theme/ThemeContext';
import {
  DevPlanData,
  DevPlanSkill,
  DevPlanSkillStatus,
  DevPlanStep,
  DevPlanStepStatus
} from '../constants';
import { updateDevPlanCurrentStepId } from '@actions/developmentPlanActions';
import { useDispatch } from 'react-redux';
import { getDefaultCurrentStepId } from '../utils';
import IconLocked from './IconLocked';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import IconCompleted from './IconCompleted';

interface DevelopmentPlanTransitionMapProps {
  pathData: Partial<RoleInPath>[];
  currentStep: DevPlanStep;
  data?: DevPlanData;
}

const DevelopmentPlanTransitionMap: React.FC<DevelopmentPlanTransitionMapProps> = ({
  pathData = [],
  data,
  currentStep
}) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const { currentUser } = useProfileContainerContext();
  const [progressWidth, setProgressWidth] = useState('0%');
  const mostAdvancedStepId = getDefaultCurrentStepId(data);
  const mostAdvancedStepDecimal = pathData?.findIndex(({ id }) => id === mostAdvancedStepId);
  const currentStepDecimal = pathData?.findIndex(({ id }) => id === currentStep?.roleId);
  const labelActionPlanLocked = translatr(
    'web.talentmarketplace.development-plan',
    'ThisActionPlanIsLocked'
  );
  const windowSizeObject = useWindowSize();
  const screenWidth = windowSizeObject.width;
  const calculateStepProgress = () => {
    const totalSteps = Math.max(pathData?.length - 1, 1);
    const baseIndex = Math.max(mostAdvancedStepDecimal - 1, 0);
    const base = baseIndex * 100;

    const mostAdvancedStep = data?.steps?.find(({ roleId }) => roleId === mostAdvancedStepId);
    if (!mostAdvancedStep) return 0;

    const isStepDone =
      mostAdvancedStep.status === DevPlanStepStatus.COMPLETED ||
      mostAdvancedStep.status === DevPlanStepStatus.NO_SKILL_GAPS;

    const allSkills = [
      ...(mostAdvancedStep?.phases?.flatMap(phase => phase.skills) || []),
      ...mostAdvancedStep.missingSkills?.filter(({ isAlreadyInStep }) => !isAlreadyInStep)
    ];
    const completedSkillsLength = (allSkills as DevPlanSkill[]).filter(
      ({ status }) => status === DevPlanSkillStatus.COMPLETED
    ).length;
    const allSkillsLength = allSkills.length;
    let currentStepProgress = 0;

    const MIN_PROGRESS = 15;
    if (isStepDone && allSkillsLength === 0) {
      currentStepProgress = 100;
    } else if (allSkillsLength > 0) {
      const isSingleNotCompletedSkill = allSkillsLength - completedSkillsLength === 1;
      const calculatedProgress = MIN_PROGRESS + (completedSkillsLength / allSkillsLength) * 90;
      currentStepProgress = isSingleNotCompletedSkill
        ? Math.min(calculatedProgress, 90)
        : calculatedProgress;
    } else {
      currentStepProgress = MIN_PROGRESS;
    }

    return Math.min((base + currentStepProgress) / totalSteps, 100);
  };

  const stepPercentage = calculateStepProgress();
  const avatar = currentUser.get('avatar');

  useEffect(() => {
    const timeout = setTimeout(() => {
      setProgressWidth(`${stepPercentage}%`);
    }, 100);
    return () => clearTimeout(timeout);
  }, [stepPercentage]);

  if (pathData?.length > 0) {
    return (
      <div className="development-plan-transition-map">
        {avatar && (
          <Avatar
            className="ed-avatar"
            user={{
              avatar: avatar,
              name: currentUser.get('name')
            }}
            size="25"
            style={{ left: -2 }}
          />
        )}
        <div className="progress-track">
          <div className="progress-line">
            <div className="progress-line-filled" style={{ width: progressWidth }} />
          </div>
          <div className="progress-steps" role="list">
            {pathData.map((step, index) => {
              const selectedStep = data?.steps[index - 1];
              const isCompleted = selectedStep?.status === DevPlanStepStatus.COMPLETED;
              const currentStepTarget = index === Math.ceil(currentStepDecimal);
              const isFirstNode = index === 0;
              const isLast = index === pathData.length - 1;
              const isClickable =
                (isCompleted || index <= Math.ceil(mostAdvancedStepDecimal)) && index > 0;
              const isLocked = selectedStep?.status === DevPlanStepStatus.LOCKED && index > 0;

              return (
                <div className="progress-step-background" key={index + step.name} role="listitem">
                  <div
                    className={`progress-step ${currentStepTarget ? 'closest-target' : ''} ${
                      isCompleted ? 'completed' : ''
                    } ${isClickable ? 'clickable' : ''} ${isLast ? 'target' : ''} ${
                      isFirstNode ? 'start-role' : ''
                    }`}
                    role={index > 0 ? 'button' : null}
                    aria-describedby={`extraInfo-${index}`}
                    aria-label={
                      index === 0
                        ? step.name
                        : `${step.name} ${
                            isCompleted ? `, ${translatr('web.common.main', 'Completed')}` : ''
                          } ${translatr('web.common.main', 'StepXOfTotalSteps', {
                            num: index,
                            totalSteps: pathData?.length - 1
                          })}`
                    }
                    aria-disabled={isLocked}
                    aria-current={currentStepTarget ? 'step' : undefined}
                    tabIndex={isClickable && !isLocked ? 0 : -1}
                    onClick={() => isClickable && dispatch(updateDevPlanCurrentStepId(step.id))}
                    onKeyDown={e => {
                      if (isClickable && (e.key === 'Enter' || e.key === ' ')) {
                        e.preventDefault();
                        dispatch(updateDevPlanCurrentStepId(step.id));
                      }
                    }}
                  >
                    {!isCompleted &&
                      !isLocked &&
                      selectedStep?.status !== DevPlanStepStatus.NO_SKILL_GAPS && (
                        <div className={`progress-node progress-node--not-completed`}> </div>
                      )}
                    {(isCompleted || selectedStep?.status === DevPlanStepStatus.NO_SKILL_GAPS) && (
                      <div className="progress-node progress-node--completed">
                        <IconCompleted />
                      </div>
                    )}
                    {isLocked &&
                      !isCompleted &&
                      selectedStep?.status !== DevPlanStepStatus.NO_SKILL_GAPS && (
                        <>
                          <Tooltip message={labelActionPlanLocked} pos="top">
                            <div className="progress-node" aria-disabled>
                              <IconLocked backgroundColor={theme.palette.primaryBase} />
                            </div>
                          </Tooltip>

                          <span id={`extraInfo-${index}`} className="sr-only">
                            {labelActionPlanLocked}
                          </span>
                        </>
                      )}
                    <div className={`progress-node-name ${pathData?.length > 2 ? '' : '--wide'}`}>
                      {screenWidth < 768 && pathData?.length > 2 ? (
                        <Tooltip message={step.name} pos="top">
                          {step.name}
                        </Tooltip>
                      ) : (
                        step.name
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  } else {
    return null;
  }
};

export default DevelopmentPlanTransitionMap;
