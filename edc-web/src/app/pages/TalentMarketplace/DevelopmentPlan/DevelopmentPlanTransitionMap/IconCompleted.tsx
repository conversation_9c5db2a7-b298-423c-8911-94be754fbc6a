const IconCompleted: React.FC = () => {
  return (
    <svg
      className="progress-node__icon-completed"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.2368 2.26709C14.5671 2.40805 16.7702 3.40307 18.4194 5.0708L18.7407 5.41162C20.2985 7.15037 21.1655 9.40702 21.1655 11.7524C21.1655 14.2544 20.1786 16.6551 18.4194 18.4341C16.7702 20.1018 14.5671 21.0968 12.2368 21.2378L11.769 21.2554C10.5123 21.2694 9.26525 21.0332 8.1001 20.562C6.93489 20.0908 5.87467 19.3931 4.98096 18.5093C4.19895 17.736 3.55787 16.8338 3.08545 15.8433L2.89307 15.4136C2.40887 14.2537 2.15967 13.0093 2.15967 11.7524L2.17139 11.2817C2.22575 10.1857 2.46939 9.10613 2.89307 8.09131L3.08545 7.66162C3.55787 6.67111 4.19896 5.76891 4.98096 4.99561C5.87466 4.11185 6.93491 3.4141 8.1001 2.94287L8.54053 2.77686C9.57696 2.41634 10.6694 2.2382 11.769 2.25049L12.2368 2.26709Z"
        fill="#14B872"
        stroke="#BDD6FF"
        stroke-width="3"
        className="progress-node__icon-completed-first-path"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.488 11.832L10.576 13.66L15.084 8.25L16.237 9.21L10.745 15.8L7.5 12.962L8.488 11.832Z"
        fill="white"
      />
    </svg>
  );
};

export default IconCompleted;
