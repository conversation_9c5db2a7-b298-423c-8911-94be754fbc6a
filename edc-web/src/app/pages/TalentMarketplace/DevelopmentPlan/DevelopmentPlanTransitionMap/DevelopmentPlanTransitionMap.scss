@import '~centralized-design-system/src/Styles/_variables.scss';

.development-plan-transition-map {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: var(--ed-spacing-lg);
  margin-bottom: var(--ed-spacing-3xl);

  .progress-track {
    position: relative;
    width: calc(100% - 12px);
    display: flex;
    flex-direction: column;

    .progress-line {
      position: relative;
      width: calc(100% - 12px);
      height: 2px;
      border-bottom: 2px dashed var(--ed-gray-2);
      margin-bottom: 10px;

      &-filled {
        height: 2px;
        background-color: var(--ed-primary-base);
        position: absolute;
        top: 0;
        left: 0;
        transition: width 1.5s ease-in-out;
        width: 0%;
      }
    }

    .progress-steps {
      position: relative;
      display: flex;
      width: calc(100% - 12px);
      justify-content: space-between;
      margin-top: -21px;
      .progress-step-background {
        padding: var(--ed-spacing-4xs);
        transform: translate(calc(-1 * var(--ed-spacing-4xs)), calc(-1 * var(--ed-spacing-4xs)));
        border-radius: 100%;
        background-color: var(--ed-white);
        &:has(.progress-step.target) {
          transform: translate(
            calc(50% + (-1 * var(--ed-spacing-4xs))),
            calc(-1 * var(--ed-spacing-4xs))
          );
        }
        &:has(.target.progress-step.completed) {
          transform: translate(50%, calc(-1 * var(--ed-spacing-3xs)));
          background: transparent;
          & .progress-step {
            background: var(--ed-white);
          }
        }
      }
      .progress-step {
        display: flex;
        align-items: center;
        position: relative;
        width: 16px;
        height: 16px;

        .progress-node {
          width: 22px;
          height: 16px;
          position: relative;
          z-index: 1;
          &--not-completed {
            border-radius: var(--ed-border-radius-circle);
            border: 4px solid var(--ed-primary-base);
            transform: translateY(2px);
          }
          &--completed {
            align-self: baseline;
            .progress-node__icon-completed {
              transform: translate(-4px, -1px);
              .progress-node__icon-completed-first-path {
                stroke: var(--ed-white);
              }
              &:hover .progress-node__icon-completed-first-path {
                stroke: var(--ed-primary-5);
              }
            }
          }
        }
        .icon-check-circle-fill {
          display: block !important;
          color: var(--ed-success-2);
          width: 16px;
          margin: 2px 0 0 0;
          padding: 0;
          border-radius: 50%;
        }
        .progress-node-name {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 25px;
          font-size: var(--ed-font-size-2xs);
          line-height: var(--ed-line-height-2xs);
          text-align: center;
          white-space: nowrap;
          width: rem-calc(140);
          .ed-tooltip {
            overflow: hidden;
            display: block;
            text-overflow: ellipsis;
          }
          display: none;
          @include min-screen-width($breakpoint-xs) {
            width: rem-calc(85);
            display: block;
          }
          @include min-screen-width($breakpoint-sm) {
            white-space: unset;
            width: rem-calc(110);
          }
        }

        &.closest-target .progress-node--not-completed {
          border-radius: 50%;
          box-shadow: 0 0 0 3px var(--ed-primary-4);
        }
        &:hover {
          .progress-node--not-completed {
            border-radius: 50%;
            box-shadow: 0 0 0 3px var(--ed-primary-5);
          }
        }
        &.closest-target {
          .progress-node-name {
            display: block;
          }
          .progress-node__icon-completed {
            .progress-node__icon-completed-first-path {
              stroke: var(--ed-primary-4);
              stroke-width: 3;
            }
          }
        }
        &.target {
          .progress-node {
            &--completed {
              transform: translate(2px, 2px);
            }
          }
          .progress-node-name {
            transform: none;
            left: auto;
            right: 0;
            text-align: right;
            @include min-screen-width($breakpoint-xxs) {
              transform: translateX(15%);
              &.--wide {
                transform: translateX(0);
                width: rem-calc(140);
                white-space: unset;
              }
            }
            @include min-screen-width($breakpoint-sm) {
              transform: translateX(5%);
              &.--wide {
                transform: translateX(0);
                white-space: unset;
                width: rem-calc(140);
              }
            }
          }
        }
        &.start-role {
          .progress-node-name {
            @include min-screen-width($breakpoint-xxs) {
              right: auto;
              left: 0;
              text-align: left;
              transform: translateX(-25%);
            }
            @include min-screen-width($breakpoint-xs) {
              transform: none;
            }
          }
        }
        &.clickable {
          cursor: pointer;
        }
      }
    }
  }

  .ed-avatar {
    width: 25px;
    height: 25px;
    position: absolute;
    z-index: 2;
    top: -12.5px;
    transition: left 0.3s ease;
    border: var(--ed-border-size-md) solid var(--ed-white);
    box-shadow: var(--ed-shadow-md);
  }
}
