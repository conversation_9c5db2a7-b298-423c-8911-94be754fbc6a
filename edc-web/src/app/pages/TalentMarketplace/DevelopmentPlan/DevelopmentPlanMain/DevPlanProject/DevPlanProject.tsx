import { translatr, omp } from 'centralized-design-system/src/Translatr';
import Accordion from 'centralized-design-system/src/Accordion';
import { useDevPlan } from '../../DevelopmentPlanProvider';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { HorizontalDetailsProjectCard as ProjectCard } from '@components/ProjectCard';
import { useSelector } from 'react-redux';
import { removeProjectFromDevPlan, toggleAddProjectModal } from '@actions/developmentPlanActions';
import { deleteDevPlanProject } from 'edc-web-sdk/requests/developmentPlan';
import { AppState } from 'src/app/types/AppState/AppState';
import DevelopmentPlanAddProjectModal from '../../modals/DevelopmentPlanAddProjectModal/DevelopmentPlanAddProjectModal';
import { useGetCurrentStep } from '../../hooks/useGetCurrentStep';
import { DevPlanStep } from '../../constants';
import { useState } from 'react';
import { shouldEditDevPlanOnFly, isStepAvailableToEdit } from '../../utils';

const DevPlanProject: React.FC = () => {
  const { isDraft, data, dispatch } = useDevPlan();
  const currentStep: DevPlanStep = useGetCurrentStep(data);
  const [isOperationPending, setIsOperationPending] = useState(false);
  const isProjectModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isAddProjectModalOpen')
  );

  const shouldDisableActions = !isStepAvailableToEdit(isDraft, currentStep);

  const removeProjectWithMessage = (roleId: string, projectId: string, dispatch: any) => {
    dispatch(removeProjectFromDevPlan(roleId, projectId));
    dispatch(
      openSnackBar(
        translatr('web.talentmarketplace.development-plan', 'OpportunityRemovedFromPlan', {
          opportunity: omp('tm_tm_project')
        }),
        'success'
      )
    );
  };

  const handleRemoveProject = async (projectId: string) => {
    setIsOperationPending(true);
    if (shouldEditDevPlanOnFly(currentStep)) {
      try {
        await deleteDevPlanProject(currentStep?.roleId, projectId);
        removeProjectWithMessage(currentStep?.roleId, projectId, dispatch);
        setIsOperationPending(false);
      } catch (e) {
        setIsOperationPending(false);
        dispatch(openSnackBar(e?.toString?.(), 'error'));
      }
    } else {
      removeProjectWithMessage(currentStep?.roleId, projectId, dispatch);
      setIsOperationPending(false);
    }
  };

  const menuList = [
    {
      id: 1,
      label: translatr('web.talentmarketplace.development-plan', 'MenuAddProject', {
        project: omp('tm_tm_project')
      }),
      action: (e: React.ChangeEvent<HTMLButtonElement>) =>
        dispatch(toggleAddProjectModal(e.currentTarget))
    }
  ];

  const notRemovedProjects = currentStep?.projects?.filter(({ status }) => status !== 'REMOVED');

  return (
    <div className={`dev-plan__project ${isOperationPending ? 'dev-plan--operation-pending' : ''}`}>
      <Accordion
        defaultOpenIndex={0}
        items={[
          {
            title: `${
              notRemovedProjects?.length > 1 ? omp('tm_tm_projects') : omp('tm_tm_project')
            } (${notRemovedProjects?.length || 0})`,
            expandable: !!notRemovedProjects?.length,
            headingLevel: 'h3',
            content: (
              <ul style={{ margin: 0 }}>
                {notRemovedProjects?.map(project => (
                  <li>
                    <ProjectCard
                      key={`project-${project?.id}`}
                      className="dev-plan__project_card"
                      id={project?.id}
                      title={project?.title}
                      thumbnail={project?.thumbnail || {}}
                      positionsFilled={project.positionsFilled}
                      applicationStatus={project?.status}
                      maxPositions={project?.maxPositions}
                      isRemotePossible={project?.isRemoteWorkPossible}
                      isApplicationRequired={project?.isApplicationRequired}
                      locations={project?.locations}
                      oldLocationsEnabled={true}
                      shouldFetchActions={false}
                      showPrimaryAction={false}
                      customActions={
                        shouldDisableActions
                          ? {}
                          : [
                              {
                                id: 'REMOVE',
                                label: translatr('web.common.main', 'Remove'),
                                onClick: () => handleRemoveProject(project?.id)
                              }
                            ]
                      }
                    />
                  </li>
                ))}
              </ul>
            ),
            className: `dev-plan__accordion ${!notRemovedProjects?.length &&
              'dev-plan__accordion--empty'}`,
            headerComponents: shouldDisableActions
              ? notRemovedProjects?.length
                ? null
                : []
              : notRemovedProjects?.length > 0
              ? [
                  {
                    key: 'menu',
                    props: { buttonAriaText: omp('tm_tm_project'), menuList }
                  },
                  { key: 'divider' },
                  { key: 'arrow' }
                ]
              : [
                  {
                    key: 'button',
                    props: {
                      text: translatr('web.talentmarketplace.development-plan', 'MenuAddProject', {
                        project: omp('tm_tm_project')
                      }),
                      onKeyDown: (e: React.KeyboardEvent<HTMLButtonElement>) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          dispatch(toggleAddProjectModal(e.currentTarget));
                        }
                      },
                      onClick: (e: React.ChangeEvent<HTMLButtonElement>) =>
                        dispatch(toggleAddProjectModal(e.currentTarget))
                    }
                  }
                ]
          }
        ]}
      />
      {isProjectModalOpen && (
        <DevelopmentPlanAddProjectModal
          currentStepData={currentStep}
          closeModal={() => dispatch(toggleAddProjectModal(null))}
        />
      )}
    </div>
  );
};

export default DevPlanProject;
