import { omp, translatr } from 'centralized-design-system/src/Translatr';
import Accordion from 'centralized-design-system/src/Accordion';
import { useDevPlan } from '../../DevelopmentPlanProvider';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { useSelector } from 'react-redux';
import HorizontalMentorshipCard from '@components/MentorCard/HorizontalMentorshipCard';
import { removeMentorFromDevPlan, toggleAddMentorModal } from '@actions/developmentPlanActions';
import { deleteDevPlanMentor } from 'edc-web-sdk/requests/developmentPlan';
import { AppState } from 'src/app/types/AppState/AppState';
import DevelopmentPlanAddMentorModal from '../../modals/DevelopmentPlanAddMentorModal/DevelopmentPlanAddMentorModal';
import { useGetCurrentStep } from '../../hooks/useGetCurrentStep';
import { DevPlanStep } from '../../constants';
import { useState } from 'react';
import { isStepAvailableToEdit, shouldEditDevPlanOnFly } from '../../utils';
import { MENTORSHIP_STATUS } from 'edc-web-sdk/requests/careerOportunities.v2';

const DevPlanMentor: React.FC = () => {
  const { isDraft, data, dispatch } = useDevPlan();
  const currentStep: DevPlanStep = useGetCurrentStep(data);
  const [isOperationPending, setIsOperationPending] = useState(false);
  const isMentorModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isAddMentorModalOpen')
  );
  const shouldDisableActions = !isStepAvailableToEdit(isDraft, currentStep);

  const removeMentorWithMessage = (roleId: string, mentorId: string, dispatch: any) => {
    dispatch(removeMentorFromDevPlan(roleId, mentorId));
    dispatch(
      openSnackBar(
        translatr('web.talentmarketplace.development-plan', 'OpportunityRemovedFromPlan', {
          opportunity: omp('tm_tm_mentor')
        }),
        'success'
      )
    );
  };

  const handleRemoveMentor = async (mentorId: string) => {
    setIsOperationPending(true);
    if (shouldEditDevPlanOnFly(currentStep)) {
      try {
        await deleteDevPlanMentor(currentStep?.roleId, mentorId);
        removeMentorWithMessage(currentStep?.roleId, mentorId, dispatch);
        setIsOperationPending(false);
      } catch (e) {
        setIsOperationPending(false);
        dispatch(openSnackBar(e?.toString?.(), 'error'));
      }
    } else {
      removeMentorWithMessage(currentStep?.roleId, mentorId, dispatch);
      setIsOperationPending(false);
    }
  };

  const menuList = [
    {
      id: 1,
      label: translatr('web.talentmarketplace.development-plan', 'MenuAddMentor', {
        mentor: omp('tm_tm_mentor')
      }),
      action: (e: React.ChangeEvent<HTMLButtonElement>) =>
        dispatch(toggleAddMentorModal(e.currentTarget))
    }
  ];

  const notRemovedMentors = currentStep?.mentors?.filter(({ status }) => status !== 'REMOVED');

  const STATUS_MAP: { [key: string]: string } = {
    APPLIED: MENTORSHIP_STATUS.PENDING,
    INPROGRESS: MENTORSHIP_STATUS.ACCEPTED,
    RECOMMENDED: null,
    ...MENTORSHIP_STATUS
  };

  return (
    <div className={`dev-plan__mentor ${isOperationPending ? 'dev-plan--operation-pending' : ''}`}>
      <Accordion
        defaultOpenIndex={0}
        items={[
          {
            title: `${
              notRemovedMentors?.length > 1 ? omp('tm_tm_mentors') : omp('tm_tm_mentor')
            } (${notRemovedMentors?.length || 0})`,
            expandable: !!notRemovedMentors?.length,
            headingLevel: 'h3',
            content: (
              <ul style={{ margin: 0 }}>
                {notRemovedMentors?.map(mentor => (
                  <li>
                    <HorizontalMentorshipCard
                      key={`mentor-${mentor?.id}`}
                      className="dev-plan__mentor_card"
                      userId={mentor?.id}
                      id={mentor?.id}
                      avatarUrl={mentor?.avatarImage}
                      name={mentor?.fullName}
                      position={mentor?.jobName && decodeURIComponent(mentor?.jobName)}
                      skills={mentor?.skillsDetail}
                      mentorCard={true}
                      status={STATUS_MAP[mentor?.status]}
                      customActions={
                        shouldDisableActions
                          ? {}
                          : [
                              {
                                id: 'REMOVE',
                                label: translatr('web.common.main', 'Remove'),
                                onClick: () => handleRemoveMentor(mentor?.id)
                              }
                            ]
                      }
                    />
                  </li>
                ))}
              </ul>
            ),
            className: `dev-plan__accordion ${!notRemovedMentors?.length &&
              'dev-plan__accordion--empty'}`,
            headerComponents: shouldDisableActions
              ? notRemovedMentors?.length
                ? null
                : []
              : notRemovedMentors?.length > 0
              ? [
                  {
                    key: 'menu',
                    props: { buttonAriaText: omp('tm_tm_mentor'), menuList }
                  },
                  { key: 'divider' },
                  { key: 'arrow' }
                ]
              : [
                  {
                    key: 'button',
                    props: {
                      text: translatr('web.talentmarketplace.development-plan', 'MenuAddMentor', {
                        mentor: omp('tm_tm_mentor')
                      }),
                      onKeyDown: (e: React.KeyboardEvent<HTMLButtonElement>) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          dispatch(toggleAddMentorModal(e.currentTarget));
                        }
                      },
                      onClick: (e: React.ChangeEvent<HTMLButtonElement>) =>
                        dispatch(toggleAddMentorModal(e.currentTarget))
                    }
                  }
                ]
          }
        ]}
      />
      {isMentorModalOpen && (
        <DevelopmentPlanAddMentorModal
          currentStepData={currentStep}
          closeModal={() => dispatch(toggleAddMentorModal(null))}
        />
      )}
    </div>
  );
};

export default DevPlanMentor;
