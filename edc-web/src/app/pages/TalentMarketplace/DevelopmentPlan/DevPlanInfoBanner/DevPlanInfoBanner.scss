@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.dev-plan-info-banner {
  margin-top: calc(var(--ed-spacing-2xl));

  div.info {
    max-width: 100%;
    text-align: left;
  }

  &--action {
    text-decoration: underline;
    cursor: pointer;
    color: var(--ed-text-color-info);
    font-weight: var(--ed-font-weight-bold);
  }

  a {
    text-decoration: underline;
    font-weight: var(--ed-font-weight-bold);
  }
}

.dev-plan-info-banner-popover__container {
  max-width: rem-calc(279);
  max-height: 500px;
  overflow: auto;
  @include modern-scrollbars(6px, #b6b7cc, transparent, 5px);
  padding: var(--ed-spacing-base);
  font-size: var(--ed-font-size-sm);

  @include min-screen-width($breakpoint-xxs) {
    width: 100%;
  }
  @include min-screen-width($breakpoint-xs) {
    width: 300px;
  }
  @include min-screen-width($breakpoint-sm) {
    width: 500px;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    vertical-align: center;
    h2 {
      color: var(--ed-gray-5);
      line-height: rem-calc(21);
      font-size: var(--ed-font-size-sm);
      font-weight: var(--ed-font-weight-bold);
    }
    button {
      cursor: pointer;
      font-size: var(--ed-modal-close-button-size) !important;
      color: var(--ed-modal-close-button-color);
      transform: translate(50%, -50%);
    }
  }

  ul {
    list-style-type: none;
    margin: var(--ed-spacing-2xs) 0 0 0;
    li {
      a {
        color: var(--ed-text-color-info);
        cursor: pointer;
      }
      font-weight: var(--ed-font-weight-bold);
      line-height: var(--ed-spacing-xl);
    }
  }
}

.dev-plan-popover-arrow {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 13px solid rgba(0, 0, 0, 0.1);
  position: absolute;
  top: -13px;
  left: 50%;
  transform: translateX(-50%);
}

.dev-plan-popover-arrow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid var(--ed-white);
}
