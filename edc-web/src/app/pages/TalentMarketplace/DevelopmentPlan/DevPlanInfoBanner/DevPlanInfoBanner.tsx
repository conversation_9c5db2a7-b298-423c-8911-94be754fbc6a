import Popover from '@mui/material/Popover';
import { DevPlanStep } from '../constants';
import './DevPlanInfoBanner.scss';
import { translatr } from 'centralized-design-system/src/Translatr';
import { useRef, useState } from 'react';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { Link } from 'centralized-design-system/src/Links';
import { Button } from 'centralized-design-system/src/Buttons';

interface DevPlanInfoBannerProps {
  sharedActionPlans: DevPlanStep['sharedActionPlans'];
}

const DevPlanInfoBanner: React.FC<DevPlanInfoBannerProps> = ({ sharedActionPlans }) => {
  if (!sharedActionPlans?.length) {
    return null;
  }

  const [anchorEl, setAnchorEl] = useState(null);
  const closeButtonRef = useRef<HTMLButtonElement | null>(null);
  const isSharedMultipleTimes = sharedActionPlans?.length > 1;

  const onClose = () => setAnchorEl(null);

  const getJobRoleDetailLink = (roleId: string) =>
    `/career/detail/${JOB_TYPE.ROLE}/${encodeURIComponent(roleId)}`;

  return (
    <div className="dev-plan-info-banner">
      {isSharedMultipleTimes && (
        <>
          <Popover
            id="dev-plan-info-banner-popover"
            open={Boolean(anchorEl)}
            anchorEl={anchorEl}
            onClose={onClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center'
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center'
            }}
            disableScrollLock
            slotProps={{
              paper: {
                elevation: 2,
                style: {
                  overflow: 'visible',
                  marginTop: 15
                },
                role: 'dialog',
                'aria-modal': true
              }
            }}
          >
            <div className="dev-plan-info-banner-popover__container">
              <div className="dev-plan-info-banner-popover__container__header">
                <h2>{translatr('web.talentmarketplace.development-plan', 'SharingWith')}</h2>
                <button
                  autoFocus
                  onClick={onClose}
                  aria-label={translatr('web.talentmarketplace.main', 'Close')}
                  ref={closeButtonRef}
                >
                  &times;
                </button>
              </div>
              <ul>
                {sharedActionPlans.map(({ roleId, title }) => (
                  <li>
                    <Link
                      size="medium"
                      color="primary"
                      title={title}
                      to={getJobRoleDetailLink(roleId)}
                      target="_blank"
                      aria-describedby={`shared-job-external-link-desc-${roleId}`}
                    >
                      {title}
                    </Link>
                    <span id={`shared-job-external-link-desc-${roleId}`} className="sr-only">
                      {translatr('cds.common.main', 'OpensInNewTab')}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="dev-plan-popover-arrow"></div>
          </Popover>

          <div className="info">
            {translatr(
              'web.talentmarketplace.development-plan',
              'YouAreSharingMultipleActionPlansWith'
            )}{' '}
            <button
              className="dev-plan-info-banner--action"
              onClick={e => setAnchorEl(e.currentTarget)}
            >
              {translatr(
                'web.talentmarketplace.development-plan',
                'YouAreSharingMultipleActionPlansWithPart2',
                {
                  numberOfPlans: sharedActionPlans.length
                }
              )}
            </button>{' '}
            {translatr(
              'web.talentmarketplace.development-plan',
              'YouAreSharingMultipleActionPlansWithPart3'
            )}
          </div>
        </>
      )}

      {!isSharedMultipleTimes && (
        <div className="info">
          {translatr('web.talentmarketplace.development-plan', 'YouAreSharingThisActionPlanWith')}{' '}
          <Link
            size="medium"
            color="primary"
            to={getJobRoleDetailLink(sharedActionPlans?.[0]?.roleId)}
            target="_blank"
            aria-describedby={`shared-job-external-link-desc-${sharedActionPlans?.[0]?.roleId}`}
          >
            {sharedActionPlans?.[0]?.title}
          </Link>{' '}
          {translatr(
            'web.talentmarketplace.development-plan',
            'YouAreSharingThisActionPlanWithPart2'
          )}
          <span
            id={`shared-job-external-link-desc-${sharedActionPlans?.[0]?.roleId}`}
            className="sr-only"
          >
            {translatr('cds.common.main', 'OpensInNewTab')}
          </span>
        </div>
      )}
    </div>
  );
};

export default DevPlanInfoBanner;
