import React, { useContext, useMemo, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import JobSkillGroups from '@components/JobCard/JobSkillGroups';
import './JobVacancySkills.scss';
import {
  getRelatedSkills,
  sortSkills,
  limitByCharsLength,
  shouldAllowToAddSkillsToPassport
} from 'opportunity-marketplace/util';
import { connect, useSelector } from 'react-redux';
import SkillsContext from 'opportunity-marketplace/shared/SkillsContext';
import { openSkillsModal as handleOpenSkillsModal } from 'actions/modalActions';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { mapSkillsWithDefaultLevel } from '@components/modals/SkillsModal/helpers';
import { translateInterestsLabel } from '../../../../../app/components/modals/SkillsModal/helpers';
import { getEcsOpportunitiesInfo } from '../../../../../app/actions/teamActions';
import { ButtonLink, Button } from 'centralized-design-system/src/Buttons';
import { isNewProfileEnabled } from '@pages/MyProfileV2/utils';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import { isMfeEnabled, openSkillFlyout } from '@components/MfeSkillsFlyout';

const JobVacancySkills = ({
  type,
  openModal,
  labels,
  currentUserLang,
  ecsVacancyConfig,
  getEcsOpportunitiesConfig,
  footer = true,
  forceMergeSkills = false
}) => {
  const themeId = useSelector(state => state?.theme?.get('themeId'));
  const { slug } = useParams();
  const { skillsHave, skillsMightHave, skillsNotHave, updateSkillStatus } = useContext(
    SkillsContext
  );
  let skills = useMemo(() => getRelatedSkills(skillsHave, skillsMightHave, skillsNotHave), [
    skillsHave,
    skillsMightHave,
    skillsNotHave
  ]);

  const onSkillAddedToPassport = (skillId, status) => updateSkillStatus(skillId, status);

  const openSkillsModal = useCallback(
    ({ modalType, openedFromHtmlElement }) => {
      openModal({
        skills: sortSkills(skills),
        modalType,
        onSkillAddedToPassport,
        openedFromHtmlElement,
        skillsFrom: type
      });
    },
    [skills]
  );

  const showSkillsModal = useCallback(
    (e, modalType) => {
      e.stopPropagation();
      openSkillsModal({
        modalType,
        openedFromHtmlElement: e.target
      });
    },
    [skills, openSkillsModal]
  );

  const skillsWithDefaultLevel = mapSkillsWithDefaultLevel({
    skills,
    skillsFrom: type,
    defaultLevel: ecsVacancyConfig?.detected_skills_level,
    currentUserLang
  });

  useEffect(() => {
    if (!ecsVacancyConfig) {
      getEcsOpportunitiesConfig();
    }
  }, []);

  const chipsToShow = useMemo(() => {
    const maxCharsSpace = 88;
    const skillsLimitedByChars = limitByCharsLength(skills, maxCharsSpace);
    return skillsLimitedByChars.length || +!!skills.length;
  }, [skills]);

  const isAnySkillWithoutLevelDefined =
    type === JOB_TYPE.ROLE &&
    skills.some(({ level, proficiencyLevel }) => !level && !proficiencyLevel?.decimal);

  const interestsLabel = translateInterestsLabel(labels, currentUserLang);

  return (
    <div className="job-skills">
      <div className="job-skills-block">
        {skillsWithDefaultLevel?.length ? (
          <JobSkillGroups
            mergeSkills={isAnySkillWithoutLevelDefined || forceMergeSkills}
            chipToShow={chipsToShow}
            skills={skillsWithDefaultLevel || []}
            noSkillsText={translatr('web.talentmarketplace.main', 'NoSkills')}
            type={type}
            currentUserLang={currentUserLang}
            showSkillsModal={showSkillsModal}
            {...(isMfeEnabled() && { clickHandler: openSkillFlyout })}
          />
        ) : (
          translatr('web.talentmarketplace.main', 'NoSkills')
        )}
      </div>
      {skills.length > 0 && footer && (
        <div className="job-skills__action-footer">
          <ButtonLink
            color="primary"
            size={themeId === ThemeId.PICASSO ? 'large' : 'medium'}
            to={`/career/detail/${type}/${slug}/skills`}
          >
            {translatr('web.talentmarketplace.main', 'GrowYourSkills')}
          </ButtonLink>
          <Button
            color="secondary"
            variant="ghost"
            size={themeId === ThemeId.PICASSO ? 'large' : 'medium'}
            onClick={e => showSkillsModal(e, 'goals')}
          >
            {isNewProfileEnabled()
              ? translatr('web.myprofile.main', 'AddSkillsToDevelopModalTitle')
              : translatr('web.talentmarketplace.main', 'SetInterests', {
                  interests: interestsLabel
                })}
          </Button>
          {shouldAllowToAddSkillsToPassport() && (
            <Button
              color="secondary"
              variant="ghost"
              size={themeId === ThemeId.PICASSO ? 'large' : 'medium'}
              onClick={showSkillsModal}
            >
              {translatr('web.common.main', 'AddSkillsYouHave')}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

const mapDispatchToProps = dispatch => {
  return {
    openModal: ({ skills, modalType, onSkillAddedToPassport, skillsFrom, openedFromHtmlElement }) =>
      dispatch(
        handleOpenSkillsModal({
          skills,
          modalType,
          onSkillAddedToPassport,
          skillsFrom,
          openedFromHtmlElement
        })
      ),
    getEcsOpportunitiesConfig: () => dispatch(getEcsOpportunitiesInfo())
  };
};

const mapStateToProps = ({ team, currentUser }) => ({
  labels: team.get('OrgConfig')?.labels,
  currentUserLang:
    currentUser.get('profile')?.get?.('language') ||
    currentUser.get('profile')?.language ||
    team?.get('config')?.DefaultOrgLanguage ||
    'en',
  ecsVacancyConfig: team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY]
});

JobVacancySkills.propTypes = {
  type: PropTypes.string,
  labels: PropTypes.object,
  currentUserLang: PropTypes.string,
  ecsVacancyConfig: PropTypes.object,
  openModal: PropTypes.func,
  getEcsOpportunitiesConfig: PropTypes.func,
  footer: PropTypes.bool,
  forceMergeSkills: PropTypes.bool
};

export default connect(mapStateToProps, mapDispatchToProps)(JobVacancySkills);
