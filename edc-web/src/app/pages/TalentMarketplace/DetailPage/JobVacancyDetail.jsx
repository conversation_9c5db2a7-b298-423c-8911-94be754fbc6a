import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import Layout from '../Layout';
import Loading from 'centralized-design-system/src/Loading';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import PeopleInJobCarousel from '../shared/PeopleInJobCarousel';
import './JobVacancyDetail.scss';
import { mapJobVacancy, mapJobVacancies } from '../Api';
import VacancyDetails from '@components/VacancyDetails/VacancyDetails';
import JobCardWidget from '../shared/JobCardWidget';
import JobVacancySkills from './components/JobVacancySkills';
import JobVacancyDescGroup from './components/JobVacancyDescGroup';
import { CardProvider } from '@components/cardStandardization/context/CardContext';
import SkillsAssessmentModal from '@components/modals/SkillsAssessmentModal';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  search as fetchOpenVacancies,
  search as fetchSimilarJobs,
  getJobMatchDetail as fetchVacancyDetail
} from 'edc-web-sdk/requests/extOpportunities';
import TMNotFound from '@components/TMNotFound/TMNotFound';
import {
  toggleActionFactory,
  BOOKMARK_ACTION,
  DISMISS_ACTION,
  shouldAllowJobEdit,
  shouldShowTMJobVacancy,
  shouldShowTMJobRole
} from 'opportunity-marketplace/util';
import { SUBWAY_STATUS } from '../CareerPathing/SubwayView/SubwayView';
import moment from 'moment';
import formatDate from '@utils/formatDate';

import withAspirationsContext from '../shared/WithAspirationsContext';
import {
  TM_VACANCY_FILTER_BUCKET_NAME,
  FILTERS_DEFAULT_ASSOCIATION_ID
} from '../shared/filters/Filters.constants';

import { track } from '@analytics/TrackWrapper';
import { TrackEvents, TrackEventProperties } from '@analytics/TrackEvents';
import DropdownActions from '@components/ProjectCard/shared/DropdownActions';
import { getAvailableLocations } from 'actions/availableLocationsActions';
import { checkCareerPathEnabled } from 'actions/talentmarketplaceActions';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { SkillsContextProvider } from 'opportunity-marketplace/shared/SkillsContext';
import CareerPathsSection from './components/CareerPathsSection';
import { FilterHostname } from '@components/common/FilterHostname';
import { recordCarrerGrowthDetailPageVisitedEvent } from './utils';
import {
  loadAllOrganizations,
  orgVisibility,
  orgAssociation,
  getOrgConfiguration
} from '@actions/organizationsActions';
import { useMemoizedMatchingJobs } from '../hooks';
import RelatedRoles from './components/RelatedRoles/RelatedRoles';
import { Button } from 'centralized-design-system/src/Buttons';
import { capitalize } from '@utils/utils';
import DevelopmentPlanEntryBanner from '@pages/TalentMarketplace/DevelopmentPlan/DevelopmentPlanEntryBanner/DevelopmentPlanEntryBanner';
import { useIsDevelopmentPlanEnabled } from '@pages/TalentMarketplace/DevelopmentPlan/hooks/useIsDevelopmentPlanEnabled';
import { generateDirectPath } from '@pages/TalentMarketplace/DevelopmentPlan/utils';

const CAROUSELS_PAGE_SIZE = 15;
const DESCRIPTION_FIELD_MAX_HEIGHT = 260;

const JobVacancyDetail = ({
  vacancyFilterState,
  team,
  currentUser,
  currentUserLang,
  availableLocations,
  dispatch,
  isCareerPathEnabled,
  userSkills,
  organizations,
  loadOrganizations,
  getOrganizationsConfig,
  accessibility
}) => {
  const prevLocation = useRef(null);
  const prevType = useRef(null);
  const location = useLocation();
  const { slug, baseRoleId } = useParams();
  const type = location.pathname.split('/')[3];
  const config = { card: { id: `job-details-${slug}`, isPublic: true }, tooltipPosition: 'top' };
  const [jobData, setJobData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [showMore, setShowMore] = useState(null);
  const [initialShowMore, setInitialShowMore] = useState(false);
  const [isModalOpen, toggleModal] = useState(false);
  const [hideMatchingDetails, setHideMatchingDetails] = useState(false);

  const [jobDataFetchIssue, setJobDataFetchIssue] = useState('');
  const descriptionRef = useRef(null);
  const desciptionBlockRef = useRef(null);
  const [similarVacanciesFilter, setSimilarVacanciesFilter] = useState({});
  const [openVacanciesFilter, setOpenVacanciesFilter] = useState({});
  const [showUserCarousel, setShowUserCarousel] = useState(false);
  const [descCalculatedHeight, setDescCalculatedHeight] = useState(0);
  const [hasNextRole, setHasNextRole] = useState(true);
  const navigate = useNavigate();
  const [memoizedJob, memoizeJob] = useMemoizedMatchingJobs(type, slug);
  const { showDevelopmentPlan } = useIsDevelopmentPlanEnabled();

  const orgVisibilityForType =
    type === JOB_TYPE.ROLE ? orgVisibility.JOB_ROLES_DETAILS : orgVisibility.JOB_VACANCY_DETAILS;
  const orgAssociationForType =
    type === JOB_TYPE.ROLE ? orgAssociation.JOB_ROLE : orgAssociation.JOB_VACANCY;
  const stateObj = location.state || {};

  const actions = React.useMemo(() => {
    const a = [];
    if (shouldAllowJobEdit()) {
      a.push({
        id: 'drop-down-job',
        label: translatr('web.talentmarketplace.main', 'Edit'),
        onClick: () => navigate(`/career/detail/${type}/${slug}/edit`),
        isLink: true
      });
    }
    return a;
  }, [shouldAllowJobEdit, type, slug]);

  const trackUserPageView = (detailType, detailData) => {
    const { ROLE_ID, ROLE_TITLE, VACANCY_ID, VACANCY_TITLE } = TrackEventProperties.OMP;

    switch (detailType) {
      case JOB_TYPE.ROLE:
        track(TrackEvents.OMP.JOB_ROLE_DETAIL_PAGE_VIEWED, {
          [ROLE_ID]: detailData.id,
          [ROLE_TITLE]: detailData.title
        });
        break;
      case JOB_TYPE.VACANCY:
        track(TrackEvents.OMP.VACANCY_DETAIL_PAGE_VIEWED, {
          [VACANCY_ID]: detailData.id,
          [VACANCY_TITLE]: detailData.title
        });
        break;
      default:
    }
  };
  const isCustomStartingRole = baseRoleId && baseRoleId !== currentUser?.jobFamily?.roleId;

  useEffect(() => {
    if (jobData?.title) {
      document.title = FilterHostname(jobData.title);
    }
  }, [jobData?.title]);

  useEffect(() => {
    if (!organizations.get('config')) {
      getOrganizationsConfig();
    }
  }, []);

  useEffect(() => {
    if (
      organizations.get('config')?.enable &&
      !organizations.get('organizationsByVisibility')[orgVisibilityForType]
    ) {
      loadOrganizations(currentUserLang, orgVisibilityForType, orgAssociationForType);
    }
  }, [
    orgVisibilityForType,
    organizations.get('config'),
    organizations.get('organizationsByVisibility')[orgVisibilityForType]
  ]);

  useEffect(() => {
    const showPeople =
      team?.config?.talent_marketplace_config?.talent_marketplace?.opportunities
        ?.find(c => c.key === 'job_role')
        ?.business_rules?.find(c => c.key === 'show_user_carousel')?.value || false;
    setShowUserCarousel(showPeople);
  }, [team.config]);

  useEffect(() => {
    setIsLoading(!memoizedJob);
  }, [memoizedJob]);

  useEffect(() => {
    // handling situation when user is changing opportunityId in URL while page is still loading
    // this occurs only we use history back... we have to preserve url which was not loaded  and avoid loading another detail data
    if (isLoading && prevLocation.current && prevLocation.current !== slug) {
      navigate(`/career/detail/${prevType.current}/${prevLocation.current}`);
    } else {
      setJobData({});
      setIsLoading(!memoizedJob);
      setJobDataFetchIssue(false);
      if (!availableLocations) {
        dispatch(getAvailableLocations(currentUserLang));
      }
      if (
        (type === JOB_TYPE.ROLE && !shouldShowTMJobRole()) ||
        (type === JOB_TYPE.VACANCY && !shouldShowTMJobVacancy())
      ) {
        setJobDataFetchIssue(true);
        setIsLoading(false);
      } else {
        if (typeof isCareerPathEnabled === 'undefined') {
          dispatch(checkCareerPathEnabled());
        }

        if (memoizedJob) {
          setJobData(memoizedJob);
          if (stateObj.hideMatchingDetails) {
            setHideMatchingDetails(stateObj.hideMatchingDetails);
          }
        } else {
          fetchVacancyDetail(slug, type, {
            language: currentUserLang,
            overallScore: stateObj.overallScore,
            skillsGraphScore: stateObj.skillsGraphScore,
            hideMatchingDetails: stateObj.hideMatchingDetails
          })
            .then(data => {
              const jobVacancy = mapJobVacancy(data, type);
              if (/^j-v1-/.test(slug)) {
                // check if job id is in old format (DHL old jobs only)
                navigate(`/career/detail/${type}/${data.id}`, { replace: true });
              }
              memoizeJob(jobVacancy);
              if (data.context) {
                const { hideMatchingDetails: hideMatch } = data.context || {};
                setHideMatchingDetails(!!hideMatch);
              } else {
                setHideMatchingDetails(!!stateObj.hideMatchingDetails);
              }
              setJobData(jobVacancy);

              /* for Role Detail we have to prepare openVacanciesFilter and passed to proper carousels */
              if (type === JOB_TYPE.ROLE) {
                setOpenVacanciesFilter({
                  ...vacancyFilterState,
                  keyword: '',
                  filters: {
                    [FILTERS_DEFAULT_ASSOCIATION_ID]: {
                      '00-roleIds': [{ name: jobVacancy.title, value: slug }]
                    }
                  }
                });
              } else if (type === JOB_TYPE.VACANCY && jobVacancy.linkedRolesExt?.length) {
                const rolesFilters = jobVacancy.linkedRolesExt.map(role => {
                  return {
                    name: role.parentRoleTitle
                      ? `${role.parentRoleTitle} - ${role.title}`
                      : role.title,
                    value: role.id
                  };
                });
                setSimilarVacanciesFilter({
                  ...vacancyFilterState,
                  keyword: '',
                  filters: { [FILTERS_DEFAULT_ASSOCIATION_ID]: { '00-roleIds': rolesFilters } }
                });
              }
              setIsLoading(false);
              trackUserPageView(type, data);
            })
            .catch(error => {
              setJobDataFetchIssue(true);
              setIsLoading(false);
              console.error('Error in fetchVacancy', error);
            });
        }
      }
    }
    prevLocation.current = slug;
    prevType.current = type;
  }, [slug, baseRoleId, memoizedJob]);

  useEffect(() => {
    if (accessibility) {
      setInitialShowMore(false);
      setShowMore(null);
      if (descriptionRef.current) {
        descriptionRef.current.style.maxHeight = '';
      }
    }
  }, [accessibility]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      if (!descriptionRef.current) return;
      const { height } = descriptionRef.current
        .querySelector('#job-description-text')
        .getBoundingClientRect();
      const adjustedHeight = height + 25;
      setDescCalculatedHeight(adjustedHeight);
      if (!showMore && !accessibility) {
        descriptionRef.current.style.maxHeight = `${adjustedHeight}px`;
      }
      if (!accessibility && !initialShowMore && height > DESCRIPTION_FIELD_MAX_HEIGHT) {
        descriptionRef.current.style.maxHeight = `${DESCRIPTION_FIELD_MAX_HEIGHT}px`;
        setShowMore(true);
        setInitialShowMore(true);
      }
    });

    if (desciptionBlockRef.current) {
      resizeObserver.observe(desciptionBlockRef.current);
    }

    return () => {
      if (desciptionBlockRef.current) {
        resizeObserver.unobserve(desciptionBlockRef.current);
      }
    };
  }, [
    initialShowMore,
    showMore,
    jobData.description,
    jobData.jobDescription,
    jobData.otherDescription,
    setDescCalculatedHeight,
    accessibility
  ]);

  useEffect(() => {
    if (isLoading) return;
    setTimeout(() => {
      if (descriptionRef.current) {
        const container = descriptionRef.current;
        const links = container.querySelectorAll('a');

        links.forEach(link => {
          const linkRect = link.getBoundingClientRect();
          const containerRect = container.getBoundingClientRect();

          if (linkRect.top < containerRect.top || linkRect.bottom > containerRect.bottom) {
            link.setAttribute('tabindex', '-1');
          } else {
            link.removeAttribute('tabindex');
          }
        });
      }
    }, 800); // we need to wait until animation of expanding of container is finished (700 ms)
  }, [isLoading, showMore]);

  const toggleShowMore = nextState => {
    if (!descriptionRef.current) {
      setShowMore(null);
      return;
    }
    setShowMore(nextState);
    descriptionRef.current.classList.add('desc-animated');
    if (nextState) {
      descriptionRef.current.style.maxHeight = `${DESCRIPTION_FIELD_MAX_HEIGHT}px`;
    } else {
      descriptionRef.current.style.maxHeight = `${descCalculatedHeight}px`;
    }
  };
  const onPathsLoaded = pathsStatus => {
    const noNextRoleStatus = [SUBWAY_STATUS.NEXT_ROLE_NOT_DETECTED, SUBWAY_STATUS.USER_NOT_FOUND];

    if (noNextRoleStatus.includes(pathsStatus)) {
      setHasNextRole(false);
    }
  };
  const similarJobsApi = React.useCallback(() => {
    return new Promise((resolve, reject) => {
      if (jobData.linkedRoles?.length > 0) {
        return fetchSimilarJobs({
          pageSize: CAROUSELS_PAGE_SIZE,
          type: JOB_TYPE.VACANCY,
          roleId: jobData.linkedRoles,
          excludedIds: [decodeURIComponent(slug)],
          language: currentUserLang
        })
          .then(({ values: data, totalElements }) => {
            const mappedJobs = mapJobVacancies(data || []);
            // in that case we are sure that one item is always removed,
            // so to achive requirements coming from TM-1622, we need to increment counter by one,
            // but ONLY when we have more totalElements than can be displayed on carousel.
            // It means that counter will show real number of items which will be displayed for user after clicking on "view all" link
            const customCounter =
              totalElements > CAROUSELS_PAGE_SIZE ? totalElements + 1 : mappedJobs.length;
            resolve({
              data: mappedJobs,
              total: customCounter
            });
          })
          .catch(e => reject(e));
      } else {
        resolve(mapJobVacancies([]));
      }
    });
  }, [jobData.linkedRoles]);

  const openVacanciesApi = React.useCallback(
    () =>
      new Promise((resolve, reject) =>
        fetchOpenVacancies({
          pageSize: CAROUSELS_PAGE_SIZE,
          type: JOB_TYPE.VACANCY,
          roleId: [slug],
          language: currentUserLang
        })
          .then(({ values: data, totalElements }) => {
            resolve({ data: mapJobVacancies(data || []), total: totalElements });
          })
          .catch(e => reject(e))
      ),
    []
  );
  const toggleDismiss = React.useMemo(
    () => toggleActionFactory(type, DISMISS_ACTION, jobData, setJobData, `VacancyDetails`),
    [type, jobData]
  );
  const toggleBookmark = React.useMemo(
    () =>
      toggleActionFactory(JOB_TYPE.VACANCY, BOOKMARK_ACTION, jobData, setJobData, `VacancyDetails`),
    [type, jobData]
  );

  const publishedDate = React.useMemo(() => {
    if (jobData.startDateTime) {
      return moment(jobData.startDateTime)
        .clone()
        .locale(currentUserLang)
        .format('DD MMM YYYY');
    }
    return false;
  }, [jobData, currentUserLang]);

  const endDate = React.useMemo(() => {
    if (jobData.endDateTime) {
      return formatDate(jobData.endDateTime, true);
    }
    return false;
  }, [jobData, currentUserLang]);

  useEffect(() => {
    recordCarrerGrowthDetailPageVisitedEvent({ pageType: type, id: slug });
  }, [type]);

  useEffect(() => {
    setHasNextRole(true);
    setInitialShowMore(false);
    setShowMore(null);
  }, [`${baseRoleId}${jobData?.id}`]);

  return isLoading ? (
    <div className="ledger-loader text-center">
      <Loading />
    </div>
  ) : (
    <CardProvider value={config}>
      <Layout backButton>
        {jobDataFetchIssue && <TMNotFound />}
        {!jobDataFetchIssue && (
          <>
            <div className="job-vacancy-details-container">
              <div className="main-panel">
                <div className="job-title block">
                  <div className="justflex">
                    <span className="flex-7">
                      {type === JOB_TYPE.VACANCY ? omp('tm_job_vacancy') : omp('tm_job_role')}
                    </span>
                    {type === JOB_TYPE.VACANCY && actions.length > 0 && (
                      <DropdownActions
                        actions={actions}
                        ariaLabel={translatr('web.common.main', 'MoreOpportunityActions', {
                          opportunity: jobData.title
                        })}
                      />
                    )}
                  </div>

                  <h1>{jobData.title}</h1>
                  <div class="meta-info">
                    {publishedDate && (
                      <span className="published-date">
                        {/* <i className="icon-calendar-empty" /> */}
                        {translatr('web.common.main', 'PublishedOnDate', {
                          date: publishedDate
                        })}
                      </span>
                    )}
                    {type === JOB_TYPE.VACANCY && shouldShowTMJobRole() && (
                      <RelatedRoles linkedRoles={jobData?.linkedRolesExt || []} />
                    )}
                  </div>
                </div>
                {!!(jobData.description || jobData.jobDescription || jobData.otherDescription) && (
                  <div
                    id={`job-desc-${slug}`}
                    className="job-description block"
                    ref={desciptionBlockRef}
                  >
                    <h2 id="desc-header" className="section-subheader">
                      {translatr('web.talentmarketplace.main', 'Description')}
                    </h2>
                    <div className="wrapper" ref={descriptionRef}>
                      <JobVacancyDescGroup jobData={jobData} />
                    </div>
                    {(showMore === true || showMore === false) && (
                      <Button
                        color="primary"
                        variant="borderless"
                        size="large"
                        padding="xsmall"
                        aria-controls={`job-desc-${slug}`}
                        aria-expanded={showMore === false}
                        aria-labelledby="desc-header more-action-btn"
                        onClick={() => toggleShowMore(showMore === true ? false : true)}
                      >
                        <span id="more-action-btn">
                          {capitalize(
                            translatr(
                              'web.talentmarketplace.main',
                              showMore === true ? 'ReadMore' : 'ReadLess'
                            )
                          )}
                        </span>
                      </Button>
                    )}
                  </div>
                )}
              </div>
              <div className="right-panel">
                <VacancyDetails
                  actionPlan={jobData.actionPlan}
                  id={jobData.id}
                  type={type}
                  jobType={jobData.type}
                  schedule={jobData.schedule}
                  department={jobData.department}
                  locations={jobData.locations}
                  mode={jobData.mode}
                  company={jobData.company}
                  seniority={jobData.seniority}
                  referenceNumber={jobData.referenceNumber}
                  salary={jobData.salary}
                  postingDate={jobData.postingDate}
                  primaryRecruiterId={jobData.primaryRecruiterId}
                  skillsGraphScore={jobData.skillsGraphScore}
                  overallScore={jobData.overallScore}
                  overallScoreStatus={jobData.overallScoreStatus}
                  area={jobData.area}
                  level={jobData.level}
                  roleType={jobData.roleType}
                  title={jobData.title}
                  applyURL={jobData.applyURL}
                  referralURL={jobData.referralURL}
                  jobDescriptionURL={jobData.jobDescriptionURL}
                  capabilities={jobData.skillsNotHave}
                  dismissed={jobData.dismissed}
                  bookmarked={jobData.bookmarked}
                  applied={jobData.applied}
                  skillsValence={jobData.skillsValence}
                  commonCareerMoveValence={jobData.commonCareerMoveValence}
                  jobFamily={jobData.jobFamily}
                  jobFunction={jobData.jobFunction}
                  toggleBookmark={toggleBookmark}
                  toggleDismiss={toggleDismiss}
                  careerTrack={jobData.careerTrack}
                  organizationsByType={jobData.organizationsByType}
                  isCareerPathEnabled={isCareerPathEnabled}
                  hasNextRole={hasNextRole}
                  currentUserJobFamily={currentUser?.jobFamily}
                  currentUserLang={currentUserLang}
                  hideMatchingDetails={hideMatchingDetails}
                  onVacancyUpdated={memoizeJob}
                  endDate={endDate}
                />
              </div>
            </div>

            <div className="job-vacancy-details-wide-container">
              <div className="main-panel">
                {type === JOB_TYPE.ROLE &&
                  currentUser?.jobFamily?.roleId != jobData.id &&
                  !isCustomStartingRole &&
                  showDevelopmentPlan &&
                  (!isCareerPathEnabled || !hasNextRole) && (
                    <DevelopmentPlanEntryBanner
                      layout="wide"
                      hasDevelopmentPlan={jobData?.actionPlan?.hasTransitionPlan}
                      wasPathChanged={false}
                      firstStepRoleName={jobData?.title || ''}
                      selectedPathData={generateDirectPath(currentUser?.jobFamily, jobData)}
                    />
                  )}
                {type === JOB_TYPE.ROLE &&
                  currentUser?.jobFamily?.roleId != jobData.id &&
                  isCareerPathEnabled &&
                  hasNextRole && (
                    <CareerPathsSection
                      key={`${baseRoleId || '_'}_${jobData?.id}`}
                      role={jobData}
                      language={currentUserLang}
                      dismissed={jobData.dismissed}
                      type={type}
                      currentUserRole={currentUser?.jobFamily}
                      detailData={jobData}
                      userSkills={userSkills}
                      pathsLoadedCallback={onPathsLoaded}
                      baseRoleId={baseRoleId}
                      capabilities={jobData.skillsNotHave}
                    />
                  )}
                <div className="related-skills block" data-testid="omp-related-skills-section">
                  <div className="row flex-space-between ml-0">
                    <h2 className="section-subheader">
                      {translatr('web.talentmarketplace.main', 'RelatedSkills')}
                    </h2>
                    {type === JOB_TYPE.ROLE &&
                      jobData.skillsAssessment &&
                      jobData.skillsAssessment.length > 0 && (
                        <button className="ed-link-secondary" onClick={() => toggleModal(true)}>
                          {translatr('web.talentmarketplace.main', 'SkillsAssessment')}
                        </button>
                      )}
                  </div>
                  {memoizedJob?.id === jobData?.id && (
                    <SkillsContextProvider
                      initSkills={jobData?.allSkills}
                      initUserSkills={userSkills}
                    >
                      <JobVacancySkills type={type} />
                    </SkillsContextProvider>
                  )}
                </div>

                {type === JOB_TYPE.VACANCY && (
                  <div className="row-container container-carousels">
                    <JobCardWidget
                      title={translatr('web.talentmarketplace.main', 'SimilarOpportunities', {
                        opportunities: omp('tm_tm_job_vacancies')
                      })}
                      fetchApi={similarJobsApi}
                      viewAllFilter={similarVacanciesFilter}
                      viewAllTreshold={CAROUSELS_PAGE_SIZE}
                      parentComponent="JobVacancyDetail:Vacancy"
                      filterFunction={itm => !itm.dismissed}
                      dismissable={false}
                      hideMatchingDetails={hideMatchingDetails}
                    />
                  </div>
                )}
                {type === JOB_TYPE.ROLE && showUserCarousel && (
                  <div className="row-container block-no-padding container-carousels">
                    <PeopleInJobCarousel jobId={jobData.id} />
                  </div>
                )}
                {type === JOB_TYPE.ROLE && shouldShowTMJobVacancy() && (
                  <div className="row-container container-carousels">
                    <JobCardWidget
                      title={omp('tm_tm_job_vacancies')}
                      fetchApi={openVacanciesApi}
                      viewAllFilter={openVacanciesFilter}
                      viewAllTreshold={CAROUSELS_PAGE_SIZE}
                      parentComponent="JobVacancyDetail:Role"
                      filterFunction={itm => !itm.dismissed}
                      dismissable={false}
                      hideMatchingDetails={hideMatchingDetails}
                    />
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </Layout>
      {isModalOpen && (
        <SkillsAssessmentModal
          closeModal={() => toggleModal(false)}
          skills={jobData.skillsAssessment}
        />
      )}
    </CardProvider>
  );
};

JobVacancyDetail.propTypes = {
  userSkills: PropTypes.array,
  vacancyFilterState: PropTypes.object,
  team: PropTypes.object,
  currentUser: PropTypes.object,
  currentUserLang: PropTypes.string,
  availableLocations: PropTypes.array,
  dispatch: PropTypes.func,
  isCareerPathEnabled: PropTypes.bool,
  organizations: PropTypes.object,
  loadOrganizations: PropTypes.func,
  getOrganizationsConfig: PropTypes.func,
  locationsUsageList: PropTypes.array,
  accessibility: PropTypes.bool
};

const mapDispatchToProps = dispatch => {
  return {
    loadOrganizations: (lang, currentVisibility, currentAssociation) =>
      dispatch(loadAllOrganizations(lang, currentVisibility, currentAssociation)),
    getOrganizationsConfig: () => dispatch(getOrgConfiguration())
  };
};
const mapStoreStateToProps = ({
  talentmarketplaceReducer,
  team,
  currentUser,
  availableLocations,
  organizations,
  theme
}) => {
  return {
    userSkills:
      currentUser
        .get('profile')
        ?.get?.('expertTopics')
        ?.toJS?.() ||
      currentUser.get('profile')?.get?.('expertTopics') ||
      [],
    vacancyFilterState: talentmarketplaceReducer.get(TM_VACANCY_FILTER_BUCKET_NAME),
    isCareerPathEnabled: talentmarketplaceReducer.get('generalConfig')?.isCareerPathEnabled,
    team: team.toJS(),
    availableLocations: availableLocations.get('availableLocations'),
    currentUser: currentUser.toJS(),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations,
    accessibility: theme.get('accessibility')
  };
};

export default connect(
  mapStoreStateToProps,
  mapDispatchToProps
)(withAspirationsContext(JobVacancyDetail));
