import React, { useEffect, useState, useCallback, useContext, useRef } from 'react';
import { connect } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { shouldShowTMJobVacancy, CARDS_POST_OPERATION_RELOAD_DELAY } from '../util';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { search } from 'edc-web-sdk/requests/extOpportunities';
import { mapJobVacancies } from '../Api';
import Results from '../shared/Results';
import {
  transformFilters,
  prepareLovFiltersForView,
  prepareCustomFiltersForView,
  cleanupFilterNames,
  prepareOrgFiltersForView,
  isFiltersChanged,
  filterFullySelectedOrganizations
} from '../shared/filters/Filters.utils';
import {
  POST_PER_PAGE,
  TM_VACANCY_FILTER_BUCKET_NAME,
  FILTER_TYPE,
  TM_FILTER_SORT_BY,
  FILTERS_DEFAULT_ASSOCIATION_ID
} from '../shared/filters/Filters.constants';
import OpportunitiesFilterPanel from '../shared/filters/OpportunitiesFilterPanel';
import OpportunitiesFilterActiveBar from '../shared/filters/OpportunitiesFilterActiveBar';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import SuggestedVacanciesCarousel from '@components/Suggested/SuggestedVacanciesCarousel';
import { string, object, func, array, bool } from 'prop-types';
import _ from 'lodash';

import { track } from '@analytics/TrackWrapper';
import { TrackEvents, TrackEventProperties } from '@analytics/TrackEvents';
import { OMP_EVENTS_PAGE_NAMES, reportCareerGrowthTabVisit } from '@analytics/OmpEventRecorder';
import AspirationsContext from '../shared/AspirationsContext';
import { tmSaveFiltersConfig, tmSaveSearchFilters } from 'actions/talentmarketplaceActions';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { loadAllOrganizations, orgVisibility, orgAssociation } from '@actions/organizationsActions';
import { getAvailableFiltersCountersConfig as tmGetAvailableFiltersCountersConfig } from '@actions/availableFilterCountersActions';
import { LOCATION_USAGE_OPTIONS } from '../helpers';
import { useCountries } from '../shared/hooks';
import CreateAlert from '../shared/OpportunitiesSavedFilters/CreateAlert';
import { useSavedSearches } from '../shared/OpportunitiesSavedFilters/hooks';

const JobVacancyTab = ({
  searchFilters,
  currentUser,
  currentUserLang,
  saveFiltersConfig,
  setCareerModalOpen,
  availableLocations,
  organizations,
  loadOrganizations,
  saveFilter,
  locationsEnabled,
  locationFieldVisibility,
  locationsUsageList,
  isGeolocationEnabled,
  countriesLoading,
  getAvailableFiltersCountersConfig,
  openCareerModal
}) => {
  const jobType = JOB_TYPE.VACANCY;
  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingOrg, setLoadingOrg] = useState(false);
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hideMatchingDetails, setHideMatchingDetails] = useState(false);
  const [sortBy, setSortBy] = useState();
  const [sortOptions, setSortOptions] = useState(TM_FILTER_SORT_BY);
  const [notRefreshData, setNotRefreshData] = useState(false);
  const [isModifiedFromAlertFilters, setIsModifiedFromAlertFilters] = useState(false);
  const dataFiltered =
    searchFilters.keyword ||
    (searchFilters.filters[FILTERS_DEFAULT_ASSOCIATION_ID] &&
      Object.keys(searchFilters.filters[FILTERS_DEFAULT_ASSOCIATION_ID]).length !== 0);
  const { search: searchParam, pathname } = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(searchParam);
  const currentPage = queryParams.get('page') ? parseInt(queryParams.get('page'), 10) : 1;
  const savedSearchId = queryParams.get('filterId') || null;
  const [isSavedSearchesApplied, setIsSavedSearchesApplied] = useState(!savedSearchId);
  const { aspirations: aspirationalRoles } = useContext(AspirationsContext);
  const countries = useCountries();
  const searchFiltersRef = useRef({});
  const filtersKeys = Object.keys(
    searchFilters?.filters?.[FILTERS_DEFAULT_ASSOCIATION_ID] || {}
  ).sort();

  const { savedSearches, savedSearchLoaded, error: savedSearchesError } = useSavedSearches(
    searchFilters?.bucketName,
    !!savedSearchId
  );

  useEffect(() => {
    const srt = sortBy || queryParams.get('sortBy');
    if (srt) {
      const newFilters = {
        ...searchFilters,
        sortBy: TM_FILTER_SORT_BY.find(item => item.id === srt.toUpperCase())
      };
      saveFilter(newFilters);
    }
  }, [sortBy]);

  const setCurrentPage = useCallback(
    page => {
      navigate({
        pathname,
        search: `?page=${page}`
      });
    },
    [history]
  );

  const loadOpportunities = useCallback(
    (page, oppFilters, delay = 0, filtersToApply, savedSearchesApplied) => {
      if (!savedSearchesApplied) return;

      const filters = transformFilters(
        filtersToApply
          ? { ...oppFilters, keyword: filtersToApply.keyword, filters: filtersToApply.filters }
          : oppFilters
      );
      const isRecallCounters = isFiltersChanged(oppFilters, searchFiltersRef.current, false);
      filters.pageNumber = page;
      setLoading(true);
      _.delay(() => {
        const payload = {
          ...filters,
          organizationId: filterFullySelectedOrganizations(filters.organizationId),
          language: currentUserLang,
          isDismissedExcluded: false
        };
        if (isRecallCounters) {
          getAvailableFiltersCountersConfig(jobType, {
            ...payload,
            pageNumber: 1,
            isFilterAggregationEnabled: true
          });
        }

        search(payload)
          .then(response => {
            const { values, totalElements: total, totalPages: pages, context } = response;
            const { hideMatchingDetails: hideMatch, sortType, sortOrder } = context || {};
            setTotalElements(total || 0);
            setTotalPages(pages || 0);
            setOpportunities(mapJobVacancies(values));
            setHideMatchingDetails(!!hideMatch);
            if (hideMatch && searchFilters?.sortBy?.id !== `${sortType}_${sortOrder}`) {
              setSortOptions(prevOptions => prevOptions.filter(f => !f.id.startsWith('MATCH_')));
              setSortBy(`${sortType}_${sortOrder}`);
              searchFiltersRef.current.sortBy = sortOptions.find(
                so => so.id === `${sortType}_${sortOrder}`
              );
              setNotRefreshData(true);
            }
            setLoading(false);
          })
          .catch(error => {
            if (error !== 'canceled') {
              setLoading(false);
            }
            console.error(`Error in fetching ${jobType}`, error);
          });
      }, delay);
    },
    []
  );

  const updateOpportunities = useCallback(
    data => {
      if (totalPages === currentPage) {
        // last page
        setOpportunities(data);
        setTotalElements(totalElements - 1);
        if (data.length === 0 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      } else {
        loadOpportunities(
          currentPage,
          searchFilters,
          CARDS_POST_OPERATION_RELOAD_DELAY,
          null,
          isSavedSearchesApplied
        );
      }
    },
    [totalPages, currentPage, totalElements, searchFilters]
  );

  useEffect(() => {
    reportCareerGrowthTabVisit(OMP_EVENTS_PAGE_NAMES.VACANCIES);
  }, []);

  useEffect(() => {
    if (organizations.get('config')?.enable) {
      if (!organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER]) {
        setLoadingOrg(true);
        loadOrganizations(
          currentUserLang,
          orgVisibility.JOB_VACANCY_FILTER,
          orgAssociation.JOB_VACANCY
        );
      } else {
        setLoadingOrg(false);
      }
    }
  }, [
    organizations.get('config')?.enable,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER]
  ]);

  useEffect(() => {
    const orgFilters = prepareOrgFiltersForView({
      organizations,
      orgKeys:
        organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER] || [],
      orgVisibility: orgVisibility.JOB_VACANCY_FILTER
    });
    const lovFilters = prepareLovFiltersForView(jobType);
    const decoratedFilters = prepareCustomFiltersForView({
      currentUser: currentUser.toJS(),
      aspirationalRoles,
      customFilters: searchFilters.config.filter(
        itm => itm.type !== FILTER_TYPE.LOV && itm.type !== FILTER_TYPE.ORG
      ),
      locationsConfig: {
        locations: availableLocations,
        enabled: locationsEnabled,
        visibility: locationFieldVisibility,
        usageContext: LOCATION_USAGE_OPTIONS.JOB_VACANCY_FILTER,
        usageList: locationsUsageList,
        countries: countries
      },
      isGeolocationEnabled
    });
    saveFiltersConfig([...lovFilters, ...decoratedFilters, ...orgFilters]);
  }, [
    aspirationalRoles,
    currentUser,
    availableLocations,
    organizations.get('organizationsByVisibility')[orgVisibility.JOB_VACANCY_FILTER],
    organizations.get('levelForTypeAndVisibility'),
    locationsEnabled,
    locationFieldVisibility,
    locationsUsageList,
    isGeolocationEnabled,
    countries
  ]);

  useEffect(() => {
    // if filters were changed and page is different than 1 set page to 1 (it will recall useEffect once again on change currentPage)
    if (
      !_.isEmpty(searchFiltersRef.current) &&
      isFiltersChanged(searchFilters, searchFiltersRef.current) &&
      currentPage !== 1
    ) {
      setCurrentPage(1);
      return;
    }
    if (notRefreshData) {
      setNotRefreshData(false);
      return;
    }
    loadOpportunities(currentPage, searchFilters, null, null, isSavedSearchesApplied);
    searchFiltersRef.current = searchFilters;
  }, [searchFilters.filters, searchFilters.keyword, searchFilters.sortBy, currentPage]);

  useEffect(() => {
    if (savedSearchLoaded && Object.keys(savedSearches).length && !savedSearchesError) {
      const savedFilter = savedSearches?.values?.find?.(({ id }) => id === savedSearchId);
      if (!savedFilter) {
        setIsSavedSearchesApplied(true);
        return;
      }
      const savedFilters = savedFilter.filterCriteria?.[0] || {};
      const bucket = {
        ...searchFilters,
        keyword: savedFilters.keyword,
        filters: savedFilters.filters
      };

      const filters = transformFilters(bucket);
      const payload = {
        ...filters,
        organizationId: filterFullySelectedOrganizations(filters.organizationId),
        language: currentUserLang,
        isDismissedExcluded: false,
        pageNumber: 1,
        isFilterAggregationEnabled: true
      };

      saveFilter(bucket);
      setIsSavedSearchesApplied(true);
      loadOpportunities(currentPage, searchFilters, null, savedFilters, true);
      getAvailableFiltersCountersConfig(jobType, payload);
    }
  }, [savedSearchLoaded]);

  useEffect(() => {
    const {
      SEARCH_FILTERS,
      SEARCH_KEYWORD,
      SEARCH_SORTBY,
      TOTAL_JOBVACANCIES
    } = TrackEventProperties.OMP;
    if (!loading) {
      track(TrackEvents.OMP.JOB_VACANCIES_PAGE_VIEWED, {
        [SEARCH_FILTERS]: cleanupFilterNames(searchFilters.filters[FILTERS_DEFAULT_ASSOCIATION_ID]),
        [SEARCH_KEYWORD]: searchFilters.keyword,
        [SEARCH_SORTBY]: searchFilters.sortBy,
        [TOTAL_JOBVACANCIES]: totalElements
      });
    }
  }, [loading, opportunities]);

  if (typeof countriesLoading === 'undefined') return null; // wait until redux store is informed that location fetch is started

  return (
    <div className="tm__vacancy-tab block">
      {/* filters section */}
      <OpportunitiesFilterPanel
        jobType={jobType}
        loading={loading || loadingOrg}
        filtersState={searchFilters}
        locationUsageType={LOCATION_USAGE_OPTIONS.JOB_VACANCY_FILTER}
        opportunityTypeName={omp('tm_tm_job_vacancies')}
        sortOptions={sortOptions}
        mainLabel={translatr('web.common.main', 'Search')}
        onApplyFilters={() => setIsModifiedFromAlertFilters(true)}
      />
      <OpportunitiesFilterActiveBar
        filtersState={searchFilters}
        opportunityTypeName={omp('tm_tm_job_vacancies')}
        isModifiedFromAlertFilters={isModifiedFromAlertFilters}
        onRemoveFilter={() => setIsModifiedFromAlertFilters(true)}
        counter={totalElements}
        loading={loading || loadingOrg}
      />
      {/* suggested section */}
      {!dataFiltered && shouldShowTMJobVacancy() && !hideMatchingDetails && (
        <div className="row-container">
          <SuggestedVacanciesCarousel
            sortBy={searchFilters.sortBy}
            hideIfNoData
            parentComponent="JobVacancyTab"
            placeholderButtonAction={() => openCareerModal(setCareerModalOpen('work_experience'))}
            itemsOnCarousel={3}
          />
        </div>
      )}
      {/* all vacancy section */}
      <div className="all-vacancy-container">
        {!dataFiltered && (
          <h2 className="tab-heading m-margin-bottom m-margin-top omp-counter-field">
            {translatr('web.talentmarketplace.main', 'AllOpportunities', {
              opportunities: omp('tm_tm_job_vacancies')
            })}{' '}
            {totalElements > 0 && <span>({totalElements})</span>}
          </h2>
        )}
        <Results
          itemsPerPage={POST_PER_PAGE}
          loading={loading}
          opportunities={opportunities}
          currentPage={currentPage}
          setOpportunities={setOpportunities}
          reloadOpportunities={updateOpportunities}
          setCurrentPage={setCurrentPage}
          jobType={jobType}
          totalElements={totalElements}
          hideMatchingDetails={hideMatchingDetails}
          noDataComponent={() => (
            <div className="text-center">
              <EmptyState
                icon="icon-file"
                title={translatr(
                  'web.talentmarketplace.main',
                  'SorryNothingMatchesYourCriteriaTryDifferentKeywords'
                )}
                description={
                  savedSearchLoaded &&
                  savedSearchId &&
                  !isModifiedFromAlertFilters &&
                  translatr('web.talentmarketplace.main', 'AlertExistsForThisSearch')
                }
              />
              {(filtersKeys?.length > 0 || searchFilters?.keyword) &&
                searchFilters?.bucketName === TM_VACANCY_FILTER_BUCKET_NAME &&
                savedSearchLoaded &&
                savedSearchId &&
                isModifiedFromAlertFilters && (
                  <CreateAlert
                    filtersKeys={filtersKeys}
                    filtersState={searchFilters}
                    associationId={FILTERS_DEFAULT_ASSOCIATION_ID}
                    buttonVariant="standard"
                  />
                )}
            </div>
          )}
        />
      </div>
    </div>
  );
};

const mapDispatchToProps = dispatch => {
  return {
    saveFiltersConfig: newConfig =>
      dispatch(tmSaveFiltersConfig(newConfig, TM_VACANCY_FILTER_BUCKET_NAME)),
    loadOrganizations: (lang, currentVisibility, currentAssociation) =>
      dispatch(loadAllOrganizations(lang, currentVisibility, currentAssociation)),
    saveFilter: newFilters => dispatch(tmSaveSearchFilters(newFilters)),
    getAvailableFiltersCountersConfig: (jobType, payload) =>
      dispatch(tmGetAvailableFiltersCountersConfig(jobType, payload)),
    openCareerModal: fn => dispatch(fn)
  };
};

const mapStoreStateToProps = ({
  talentmarketplaceReducer,
  currentUser,
  team,
  availableLocations,
  organizations,
  configService,
  locationsConfiguration
}) => {
  return {
    currentUser: currentUser,
    searchFilters: talentmarketplaceReducer.get(TM_VACANCY_FILTER_BUCKET_NAME),
    availableLocations: availableLocations.get('availableLocations'),
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en',
    organizations,
    locationsEnabled: locationsConfiguration.get('enable'),
    locationFieldVisibility: locationsConfiguration.get('visibility'),
    locationsUsageList: locationsConfiguration.get('usageList'),
    isGeolocationEnabled: configService.get('omp')?.['geolocation']?.['enable_geolocation'],
    countriesLoading: availableLocations.get('countriesLoading')
  };
};

JobVacancyTab.propTypes = {
  searchFilters: object,
  availableLocations: array,
  currentUser: object,
  currentUserLang: string,
  saveFiltersConfig: func,
  setCareerModalOpen: func,
  organizations: object,
  loadOrganizations: func,
  saveFilter: func,
  locationsEnabled: bool,
  locationFieldVisibility: object,
  locationsUsageList: array,
  isGeolocationEnabled: bool,
  countriesLoading: bool,
  getAvailableFiltersCountersConfig: func,
  openCareerModal: func
};

export default connect(mapStoreStateToProps, mapDispatchToProps)(JobVacancyTab);
