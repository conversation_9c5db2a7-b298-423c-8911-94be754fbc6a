import React, { useMemo, useEffect, useState, useContext } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import './JobCardWidget.scss';
import JobCard from '@components/JobCard/JobCard';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import TMCarousel from '@components/Carousels/TM/TMCarousel';
import { JOB_TYPE, OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { BOOKMARK_ACTION, DISMISS_ACTION, toggleActionFactory } from '../util';
import AspirationsContext from './AspirationsContext';
import cn from 'classnames';
import { SkillsContextProvider } from './SkillsContext';
import { LOCATION_USAGE_OPTIONS, isLocationVisible } from '../helpers';
import { useCountries } from './hooks';
import { NextInteractiveSuggestionContextProvider } from './context/NextInteractiveSuggestionContext';
import { useGetRecommendationFeedback } from '@components/roleCard/RecommendationFeedbackCard/hooks';
import RecommendationFeedbackCard from '@components/roleCard/RecommendationFeedbackCard/RecommendationFeedbackCard';

const defaultFetchApi = () => new Promise(resolve => resolve([]));

const JobCardWidget = ({
  title = '',
  titleIcon = '',
  description,
  fetchApi = defaultFetchApi,
  hideShowAll = false,
  viewAllFilter = {},
  viewAllTreshold = 15,
  hideIfNoData = false,
  noDataLink = '/career/job-vacancies',
  parentComponent = '',
  customPlaceholder = '',
  customPlaceholderLoading = false,
  dismissable,
  filterFunction = () => {
    return true;
  },
  locationsEnabled,
  locationFieldVisibility,
  itemsOnCarousel = 4,
  hideMatchingDetails,
  showFeedbackCard
}) => {
  const navigate = useNavigate();
  const countries = useCountries();
  const [loading, setLoading] = useState(true);
  const [isActionPending, setIsActionPending] = useState(false);
  const [jobs, setJobs] = useState([]);
  const [total, setTotal] = useState(0);
  const [isFeedbackFilled, setIsFeedbackFilled] = useState(false);
  const [canUserFillFeedback] = useGetRecommendationFeedback(OPPORTUNITY_TYPE.JOB_VACANCY);
  const toggleBookmark = React.useMemo(
    () =>
      toggleActionFactory(
        JOB_TYPE.VACANCY,
        BOOKMARK_ACTION,
        jobs,
        setJobs,
        `JobCardWidget - ${parentComponent}`,
        setIsActionPending
      ),
    [jobs]
  );
  const toggleDismiss = React.useMemo(
    () =>
      toggleActionFactory(
        JOB_TYPE.VACANCY,
        DISMISS_ACTION,
        jobs,
        setJobs,
        `JobCardWidget - ${parentComponent}`,
        setIsActionPending
      ),
    [jobs]
  );
  const { isAspirationsInitLoading, hasAspirationalRole } = useContext(AspirationsContext);
  const viewAllLinkData = useMemo(
    () => ({
      label: translatr('web.talentmarketplace.main', 'ViewAll'),
      link: '/career/job-vacancies',
      filtersToApply: viewAllFilter
    }),
    [viewAllFilter]
  );
  const actualCount = jobs?.length ? jobs.filter(filterFunction).length : null;
  const isLocationShown =
    locationsEnabled &&
    isLocationVisible(locationFieldVisibility, LOCATION_USAGE_OPTIONS.JOB_VACANCY_CARD);

  useEffect(() => {
    setLoading(true);
    fetchApi()
      .then(data => {
        const jobsData = Array.isArray(data) ? data : data?.data || [];
        setTotal(data?.total || jobsData.length);
        setJobs(jobsData || []);
        setLoading(false);
      })
      .catch(error => {
        setLoading(false);
        console.error('Error in fething data for JobCardWidget', error);
      });
  }, [fetchApi]);

  if (hideIfNoData && !actualCount) {
    return false;
  }

  return (
    <div
      className={cn('job-card-widget__container', {
        'is-action-pending': isActionPending
      })}
    >
      <TMCarousel
        className={cn({
          'carousel-hidden': customPlaceholder && !actualCount
        })}
        title={title}
        titleIcon={titleIcon}
        description={description}
        loading={loading || customPlaceholderLoading || isAspirationsInitLoading}
        itemCount={actualCount}
        viewAll={!hideShowAll && total > viewAllTreshold ? viewAllLinkData : undefined}
        tooltip={''}
        displayedItems={itemsOnCarousel}
      >
        {!!actualCount &&
          jobs.filter(filterFunction).map((job, index) => {
            const detailUrl = `/career/detail/${JOB_TYPE.VACANCY}/${encodeURIComponent(job.id)}`;
            return (
              <SkillsContextProvider initSkills={job.allSkills} key={`prov_${job.id}`}>
                <NextInteractiveSuggestionContextProvider opportunities={jobs} opportunity={job}>
                  <li key={job.id}>
                    <JobCard
                      {...job}
                      key={job.id}
                      onClick={() =>
                        navigate(detailUrl, {
                          state: {
                            overallScore: job.overallScore,
                            skillsGraphScore: job.skillsGraphScore,
                            hideMatchingDetails
                          }
                        })
                      }
                      detailUrl={detailUrl}
                      onBookmark={toggleBookmark}
                      onDismiss={toggleDismiss}
                      hasAspirationalRole={hasAspirationalRole(job.linkedRoles)}
                      dismissable={dismissable}
                      showLocation={isLocationShown}
                      countries={countries}
                      isSuggestion
                      hideMatchingDetails={hideMatchingDetails}
                    />
                  </li>
                  {!isFeedbackFilled &&
                    canUserFillFeedback &&
                    showFeedbackCard &&
                    (index === 1 || jobs.length === 1) && (
                      <li>
                        <RecommendationFeedbackCard
                          className="job-card"
                          opportunities={jobs}
                          opportunityType={OPPORTUNITY_TYPE.JOB_VACANCY}
                          onSubmit={() => setIsFeedbackFilled(true)}
                        />
                      </li>
                    )}
                </NextInteractiveSuggestionContextProvider>
              </SkillsContextProvider>
            );
          })}
      </TMCarousel>
      {!loading && !customPlaceholderLoading && !actualCount && customPlaceholder}
      {!loading && !actualCount && !customPlaceholder && (
        <EmptyState
          icon="icon-file"
          title={translatr('web.talentmarketplace.main', 'NoSimilarOpportunitiesMessage', {
            opportunities: omp('tm_tm_job_vacancies')
          })}
          buttonLabel={
            noDataLink &&
            translatr('web.talentmarketplace.main', 'ExploreCareerGrowthSection', {
              career_growth_section: omp('tm_tm_job_vacancies')
            })
          }
          buttonLink={noDataLink}
        />
      )}
    </div>
  );
};

function mapStoreStateToProps({ locationsConfiguration }) {
  return {
    locationsEnabled: locationsConfiguration.get('enable'),
    locationFieldVisibility: locationsConfiguration.get('visibility')
  };
}

JobCardWidget.propTypes = {
  title: PropTypes.string.isRequired,
  titleIcon: PropTypes.string,
  description: PropTypes.string,
  fetchApi: PropTypes.func.isRequired,
  hideShowAll: PropTypes.bool,
  hideIfNoData: PropTypes.bool,
  noDataLink: PropTypes.string,
  viewAllFilter: PropTypes.object,
  viewAllTreshold: PropTypes.number,
  parentComponent: PropTypes.string,
  customPlaceholder: PropTypes.element,
  customPlaceholderLoading: PropTypes.bool,
  filterFunction: PropTypes.func,
  dismissable: PropTypes.bool,
  locationsEnabled: PropTypes.bool,
  locationFieldVisibility: PropTypes.object,
  itemsOnCarousel: PropTypes.number,
  hideMatchingDetails: PropTypes.bool,
  showFeedbackCard: PropTypes.bool
};

export default connect(mapStoreStateToProps)(JobCardWidget);
