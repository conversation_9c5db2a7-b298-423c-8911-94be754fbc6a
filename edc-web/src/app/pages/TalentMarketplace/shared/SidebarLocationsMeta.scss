@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.locations__sidebar-meta {
  display: flex;

  i {
    font-size: rem-calc(18);
    color: var(--ed-text-color-supporting);
    margin-right: rem-calc(14) !important;
  }

  button {
    margin-left: var(--ed-spacing-4xs);
    font-weight: var(--ed-font-weight-bold);
  }
  a {
    font-weight: var(--ed-font-weight-bold);
    max-width: 200px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
  }
  &--title {
    display: block;
    color: var(--ed-text-color-supporting);
    font-size: var(--ed-font-size-sm);
  }
  &--label {
    display: block;
    color: var(--ed-text-color-primary);
  }
  &--content {
    display: flex;
  }
  &:not(:last-of-type) {
    margin-bottom: var(--ed-spacing-xs);
  }
}
.project_locations-popover {
  &__container {
    padding: var(--ed-spacing-2xs) var(--ed-spacing-2xs) var(--ed-spacing-base)
      var(--ed-spacing-base);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__header-label {
    font-weight: var(--ed-font-weight-bold);
    font-size: var(--ed-font-size-base) !important;
    margin: 0 var(--ed-spacing-2xs) 0 0;
  }

  &__close-button {
    margin-top: var(--ed-spacing-4xs);
    margin-left: auto;
  }

  &__list {
    @include modern-scrollbars(6px, var(--ed-neutral-6), transparent, 5px);
    display: block;
    overflow-x: auto;
    overflow-y: auto;
    max-height: 250px;
    margin: 0;
    li {
      line-height: var(--ed-line-height-base);
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: var(--ed-spacing-3xs);
      color: var(--ed-gray-6);
      font-size: var(--ed-font-size-sm);
      font-weight: var(--ed-link-font-weight);
      margin: 0 0 var(--ed-spacing-2xs) 0;
    }
  }
}
