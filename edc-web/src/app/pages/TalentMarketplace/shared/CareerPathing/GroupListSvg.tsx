import React, {forwardRef, useEffect, useMemo, useRef} from "react";
import {translatr, omp} from 'centralized-design-system/src/Translatr';
import cn from "classnames";
import {Button} from "centralized-design-system/src/Buttons";
import {MATCH_LEVEL} from "edc-web-sdk/requests/talentMarketplaceSettings";
import {MATCHING_ICONS} from "edc-web-sdk/helpers/matchIcons";
import FocusLock from "react-focus-lock";
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';

interface GroupListSvgProps {
  group: Group;
  x: number,
  y: number,
  selected?: boolean;
  iconSet: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  hideMatchingDetails: boolean;
  openedFromHtmlElement: object;
}

interface Group {
  id: string,
  role: Role[]
}

interface Role {
  name: string,
  jobFamilyName: string,
  overallScoreStatus: string,
  overallScore: number,
  isAspirational: boolean,
}

const GROUP_LIST_WIDTH = 379;

const GroupListSvg: React.FC<GroupListSvgProps> = forwardRef(({
  selected,
  iconSet,
  x,
  y,
  onSelect,
  isOpen = false,
  setIsOpen,
  hideMatchingDetails,
  openedFromHtmlElement,
  group
}, ref) => {
  const roleListRef = useRef(null);
  const roles = group?.value?.items;

  //@ts-ignore
  const { isAspirationalRole } = React.useContext(AspirationsContext) as { isAspirationalRole: (id: string) => boolean};

  const escFunction = e => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  const handleClickOutside = (event) => {
    if (roleListRef.current && !roleListRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 100);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  const smileIcons = useMemo(
    () => roles && roles.map((role) => {
      const showMatchIcon = MATCH_LEVEL[role.overallScoreStatus] ? true : false;
      const svgWidth = showMatchIcon ? 25 : 1;
      const svgHeight = showMatchIcon ? 24 : 1;
      return `<svg width="${svgWidth}" height="${svgHeight}" fill="none" xmlns="http://www.w3.org/2000/svg">${showMatchIcon ? MATCHING_ICONS.get(iconSet).get(MATCH_LEVEL[role.overallScoreStatus]) : ''}</svg>`;
    }), [roles, iconSet]);

  return isOpen && group && (<g
    ref={ref}
    key={`group-pill-${group.id}`}
    role="listitem"
    aria-expanded={isOpen}
  >
    <foreignObject
      x={x - GROUP_LIST_WIDTH/2}
      y={y}
      width={GROUP_LIST_WIDTH}
      height={10}
      className="cp_role-group-pill__object"
    >
      <FocusLock returnFocus={() => {
        openedFromHtmlElement?.focus();
        return true;
      }}>
        <div ref={roleListRef} className="cp-role-list">
          <div className='cp-role-group-dropdown'>
            <div className='cp-role-group-header'>
              <div className='cp-role-group-title'>{translatr('web.talentmarketplace.career-path', 'RolesInTheGroup', {
                roles: omp('tm_tm_job_roles')
              })}</div>
              <button
                className="ed-role-group-header-close-button"
                onClick={()=>{setIsOpen(false)}}
                aria-label={translatr('cds.common.main', 'Close')}
              >
                &times;
              </button>
            </div>
            <div className='cp-role-group-content' role='list'>
              {roles?.map((role, idx) => (
                <div role='listitem' key={role.id} className={cn({
                  'cp-role-group-selected': selected === role.id
                })}>
                  <Button
                    color="secondary"
                    variant="borderless"
                    size="medium"
                    padding="xsmall"
                    onClick={() => onSelect(role.id)}
                  >
                  {!hideMatchingDetails && (
                    <span className={cn('cp-role-pill__icon', iconSet)}>
                      {smileIcons[idx] && (<img src={`data:image/svg+xml;utf8,${encodeURIComponent(smileIcons[idx])}`} alt="" />)}
                    </span>
                  )}
                  {isAspirationalRole(role.id) && (
                    <span className='cp-role-pull__aspirational'>
                      <i className={cn('icon icon-bullseye-arrow active')} aria-hidden="true" />
                    </span>
                  )}
                    <span>{role.name}</span>
                  </Button>
                </div>
              ))}
            </div>
          </div>
          <div className="cr-role-list-arrow"></div>
        </div>
      </FocusLock>
    </foreignObject>
  </g>);
});

export default GroupListSvg;
