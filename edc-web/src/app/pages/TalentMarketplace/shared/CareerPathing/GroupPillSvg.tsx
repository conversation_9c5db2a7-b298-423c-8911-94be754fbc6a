import React, { useMemo, forwardRef } from 'react';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { MATCH_LEVEL } from 'edc-web-sdk/requests/talentMarketplaceSettings';
import { MATCHING_ICONS, ICON_SET } from 'edc-web-sdk/helpers/matchIcons';
import TextClamp from '@components/TextClamp';
import cn from 'classnames';
import './RolePillSvg.scss';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';

interface GroupPillData {
  overallScoreStatus: string,
  overallScore: number,
  x: number,
  y: number
}

interface GroupPillSvgProps {
  groupId: string;
  groupData: { jobFamilyName: string, noOfMoves: number, items: GroupPillData[] };
  animate?: CareerPathingAnimConfig,
  handleOver?: (event: React.MouseEvent) => void;
  handleClick?: (event: React.MouseEvent) => void;
  handleKeyDown?: (event: React.KeyboardEvent) => void;
  isSelected?: boolean;
  isLastElement?: boolean
  iconSet: string,
  hideMatchingDetails?: boolean
};

const GroupPillSvg: React.FC<GroupPillSvgProps> = forwardRef(({
  groupId = '',
  groupData = { jobFamilyName: '', noOfMoves: 0, items: [] },
  animate = { anim: true, animMode: 'anim-mode--2' },
  handleOver,
  handleClick,
  handleKeyDown,
  isSelected = false,
  isLastElement = false,
  iconSet,
  hideMatchingDetails
}, ref) => {
  const counterLabel = omp('tm_tm_job_roles');
  const jobFamilyName = groupData.jobFamilyName;
  const noOfMoves = groupData.noOfMoves;
  const items = groupData.items;
  const itemsCnt = items.length;
  const firstItem = items && itemsCnt ? items[0] : null;

  const { isAspirationalRole } = React.useContext(AspirationsContext);

  if (!firstItem) {
    return null;
  }

  const smileIcons = useMemo(
    () => [
      ...new Map(
        items.map((item: GroupPillData) => {
          const showMatchIcon = !hideMatchingDetails && MATCH_LEVEL[item.overallScoreStatus] ? true : false;
          const svgWidth = showMatchIcon ? 25 : 1;
          const svgHeight = showMatchIcon ? 24 : 1;
          return [item.overallScoreStatus, `<svg width="${svgWidth}" height="${svgHeight}" fill="none" xmlns="http://www.w3.org/2000/svg">${showMatchIcon ? MATCHING_ICONS.get(iconSet || ICON_SET.FACES).get(MATCH_LEVEL[item.overallScoreStatus]) : ''}</svg>`];
        })
      ).values()
    ], [items, iconSet]);

  //@ts-ignore
  const containsAspirational = useMemo(() => items.some((item: GroupPillData) => isAspirationalRole(item?.id)), [items]);

  //GroupPill width calculations and limitations
  const baseSpace = 53 + smileIcons.length * 12 + (containsAspirational ? 4 : 0);
  const counterLabelSpace = 10 + counterLabel.length * 6;
  const width = baseSpace + (counterLabelSpace < 120 ? counterLabelSpace : 120); //max width for counterLabel
  const height = 40;

  //GroupPill position calculations
  const rectProps = {
    x: firstItem.x - width / 2,
    y: firstItem.y - height / 2
  };

  const getMoveLineLabel = (moveNr: number) => {
    return moveNr === 1
      ? translatr('web.talentmarketplace.career-path', 'Move', { cnt: moveNr })
      : translatr('web.talentmarketplace.career-path', 'Moves', { cnt: moveNr });
  }

  return (
    <g
      ref={ref}
      tabIndex={-1}
      key={`group-pill-${groupId}`}
      className={`g-button cp_role-pill--clickable ${isLastElement ? 'last-element' : ''}`}
      role="listitem"
      aria-label={`${itemsCnt} ${counterLabel} - ${omp('tm_job_family')}: ${jobFamilyName} - ${getMoveLineLabel(noOfMoves)}`}
      onMouseEnter={handleOver}
      onMouseDown={(event) => {handleClick({x: firstItem.x, y: firstItem.y - height / 2})}}
      onKeyDown={(event) => {handleKeyDown(event, {x: firstItem.x, y: firstItem.y - height / 2})}}
      aria-expanded="false"
    >
      <foreignObject
        x={rectProps.x}
        y={rectProps.y}
        width={width}
        height={height}
        className="cp_role-group-pill__object"
      >
        <div
          className={cn(`cp-role-group-pill__wrapper ${animate.animMode}`, {
            'cp-role-pill__animation': animate.anim,
            'cp-role-pill--selected': isSelected,
            'cp-role-pill--aspirational': containsAspirational
          })}
          style={{
            width: `${width}px`,
            height: `${height}px`
          }}
        >
          <div className="cp-role-group-pill__content">
            <div className="cp-role-group-pill__icons">
              {smileIcons.map((svgIcon, idx) => (
                <img
                  key={`cp-progress-icon-${idx}`}
                  src={`data:image/svg+xml;utf8,${encodeURIComponent(svgIcon)}`}
                  alt=""
                  style={{
                    zIndex: smileIcons.length - idx
                  }}
                />
              ))}
            </div>
            <span className="cp-role-group-pill__counter">
              <TextClamp
                tooltipPlacement={'top'}
                disableTooltip={counterLabelSpace < 120}
                line={1}
              >
                {itemsCnt} {counterLabel}
              </TextClamp>
            </span>
          </div>
        </div>
      </foreignObject>
    </g>
  );
});

export default GroupPillSvg;
