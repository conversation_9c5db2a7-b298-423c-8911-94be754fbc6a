import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { translatr, omp, ompLov } from 'centralized-design-system/src/Translatr';
import DetailsItem from '@components/VacancyDetails/DetailsItem';
import OrgListForDetailItem from '@components/VacancyDetails/OrgListForDetailItem';
import JobLocation from '@components/JobCard/JobLocation';
import { LOCATION_USAGE_OPTIONS, isLocationVisible } from 'opportunity-marketplace/helpers';
import { isLovAvailableForOpportunityType, countDays } from 'opportunity-marketplace/util';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { getUserBasicInfo } from 'edc-web-sdk/requests/users.v2';
import { useCountries } from 'opportunity-marketplace/shared/hooks';
import { lightGreyNavy } from 'centralized-design-system/src/MUIComponents/colors/colors';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import Avatar from 'centralized-design-system/src/Avatar';
import CareerTrack from '../../../../../app/icons/CareerTrack';
import './OpportunityDetailProperties.scss';
import { prepareJobTypeScheduleItem } from './utils';

const OpportunityDetailProperties = ({
  area,
  careerTrack,
  compactView = false,
  company,
  currentUserLang,
  department,
  jobFamily = {},
  jobFunction = {},
  jobType,
  level,
  locations,
  mode,
  organizationsByType,
  postingDate,
  primaryRecruiterId,
  referenceNumber,
  roleType,
  salary,
  schedule,
  type,
  endDate
}) => {
  const [userLoading, setUserLoading] = useState(false);
  const [primaryRecruiter, setPrimaryRecruiter] = useState(null);
  const navigate = useNavigate();
  const {
    isJobTypeSechduleEnabled,
    jobTypeScheduleValue,
    jobTypeScheduleLabel
  } = prepareJobTypeScheduleItem({ jobType, type, schedule });

  /* redux state data */
  const locationFieldVisibility = useSelector(state =>
    state.locationsConfiguration?.get('visibility')
  );
  const locationsEnabled = useSelector(state => state.locationsConfiguration?.get('enable'));

  const countries = useCountries();
  /* redux state data */

  /* location setup */
  const locationContext =
    type === JOB_TYPE.VACANCY
      ? LOCATION_USAGE_OPTIONS.JOB_VACANCY_DETAILS
      : LOCATION_USAGE_OPTIONS.JOB_ROLE_DETAILS;
  const isLocationShown =
    locationsEnabled && isLocationVisible(locationFieldVisibility, locationContext);

  /*user interaction handlers */
  const goToUserProfileHandler = () => navigate(`/${primaryRecruiter.handle}`);

  useEffect(() => {
    if (primaryRecruiterId) {
      setUserLoading(true);
      getUserBasicInfo(primaryRecruiterId)
        .then(({ id: userId, handle, name, avatarimages }) => {
          setPrimaryRecruiter({
            id: userId,
            name,
            imgUrl: avatarimages ? avatarimages.tiny : '',
            handle
          });
          setUserLoading(false);
        })
        .catch(err => {
          setUserLoading(false);
          console.error(`Error in getUserBasicInfo.func : ${err}`);
        });
    }
  }, [primaryRecruiterId]);

  return (
    <div className={`odp-infobox ${compactView ? '--compact-view' : ''}`}>
      {locations && isLocationShown && (
        <DetailsItem
          value={
            <JobLocation
              locations={locations}
              mode={mode}
              showMulitipleAsList={false}
              visibilityContext={locationContext}
              countries={countries}
              type={type}
            />
          }
          testId="location"
          label={translatr('web.common.main', 'Location')}
          icon={<span className="icon-pin-on-street-square" aria-hidden="true" />}
        />
      )}
      {(department || company) && (
        <DetailsItem
          value={company && department ? company + '; ' + department : company || department}
          testId={
            company && department ? 'company-department' : department ? 'department' : 'company'
          }
          label={
            company && department
              ? translatr('web.common.main', 'CompanyDepartment')
              : department
              ? translatr('web.common.main', 'Department')
              : translatr('web.common.main', 'Company')
          }
          icon={<span className="icon-department" aria-hidden="true" />}
        />
      )}
      <OrgListForDetailItem
        organizationsByType={organizationsByType}
        opportunityType={type}
        language={currentUserLang}
      ></OrgListForDetailItem>
      {referenceNumber && (
        <DetailsItem
          value={referenceNumber}
          testId="reference-number"
          label={translatr('web.common.main', 'ReferenceNumber')}
          icon={<span className="icon-hashtag" aria-hidden="true" />}
        />
      )}
      {isJobTypeSechduleEnabled && (
        <DetailsItem
          value={jobTypeScheduleValue}
          testId="schedule"
          label={jobTypeScheduleLabel}
          icon={<span className="icon-briefcase-thin" aria-hidden="true" />}
        />
      )}
      {/* hide salary for range 0-0 */}
      {salary && (salary.rangeFrom > 0 || salary.rangeTo > 0) && (
        <DetailsItem
          value={`${salary.rangeFrom}-${salary.rangeTo} ${salary.currency} ${
            salary.per ? '/ ' + salary.per : ''
          }`}
          testId="salary"
          label={translatr('web.common.main', 'Salary')}
          icon={<span className="icon-salary" aria-hidden="true" />}
        />
      )}
      {postingDate && (
        <DetailsItem
          value={countDays(postingDate) + ' days'}
          testId="active-for"
          label={translatr('web.common.main', 'ActiveFor')}
          icon={<span className="icon-calendar-empty" aria-hidden="true" />}
        />
      )}
      {primaryRecruiter && (
        <DetailsItem
          value={
            <button className="user-link" onClick={goToUserProfileHandler}>
              {primaryRecruiter.name}
            </button>
          }
          testId="your-recruiter"
          label={translatr('web.common.main', 'YourRecruiter')}
          icon={
            <button onClick={goToUserProfileHandler}>
              <Avatar user={primaryRecruiter} />
            </button>
          }
        />
      )}
      {primaryRecruiterId && userLoading && <Skeleton height={45} />}
      {type === JOB_TYPE.ROLE && jobFamily?.title && (
        <DetailsItem
          value={
            <>
              {jobFunction?.title ? (
                <>
                  {jobFunction.title}
                  <br />
                </>
              ) : null}
              {jobFamily.title}
            </>
          }
          testId="job-family"
          label={omp('tm_job_family')}
          icon={<span className="icon-department" aria-hidden="true" />}
        />
      )}
      {/* fallback solution: */}
      {type === JOB_TYPE.ROLE && !jobFamily?.title && !jobFunction?.title && area && (
        <DetailsItem
          value={area}
          testId="job-family"
          label={omp('tm_job_family')}
          icon={<span className="icon-department" aria-hidden="true" />}
        />
      )}
      {level && isLovAvailableForOpportunityType('level', type, level.id) && (
        <DetailsItem
          value={ompLov('level', level.id)}
          testId="level"
          label={omp('tm_level')}
          icon={<span className="icon-balance" aria-hidden="true" />}
        />
      )}
      {roleType && (
        <DetailsItem
          value={ompLov('job_role_type', roleType)}
          testId="role-type"
          icon={<span className="icon-address-card" aria-hidden="true" />}
        />
      )}
      {careerTrack && isLovAvailableForOpportunityType('job_role_type', type, careerTrack) && (
        <DetailsItem
          value={ompLov('job_role_type', careerTrack)}
          testId="career-track"
          label={ompLov('job_role_type')}
          icon={
            <CareerTrack
              style={{
                marginTop: compactView ? '0.2rem' : '0.625rem',
                marginLeft: compactView ? '0.4rem' : '0.25rem',
                marginRight: compactView ? '0.4rem' : '0.25rem',
                width: compactView ? '1rem' : '1.35rem',
                height: compactView ? '1rem' : '1.35rem',
                fill: lightGreyNavy
              }}
            />
          }
        />
      )}
      {endDate && (
        <DetailsItem
          value={endDate}
          testId="end-date"
          label={translatr('web.talentmarketplace.main', 'ApplicationDeadline')}
          icon={<span className="icon-calendar-x" aria-hidden="true" />}
        />
      )}
    </div>
  );
};

OpportunityDetailProperties.propTypes = {
  area: PropTypes.string,
  careerTrack: PropTypes.string,
  compactView: PropTypes.bool,
  company: PropTypes.string,
  countries: PropTypes.array,
  currentUserLang: PropTypes.string,
  department: PropTypes.string,
  jobFamily: PropTypes.object,
  jobFunction: PropTypes.object,
  jobType: PropTypes.string,
  level: PropTypes.shape({
    id: PropTypes.string
  }),
  locations: PropTypes.array,
  mode: PropTypes.string,
  organizationsByType: PropTypes.object,
  postingDate: PropTypes.string,
  primaryRecruiterId: PropTypes.string,
  referenceNumber: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  roleType: PropTypes.string,
  salary: PropTypes.shape({
    per: PropTypes.string,
    currency: PropTypes.string,
    rangeTo: PropTypes.number,
    rangeFrom: PropTypes.number
  }),
  schedule: PropTypes.string,
  type: PropTypes.string,
  endDate: PropTypes.string
};

export default OpportunityDetailProperties;
