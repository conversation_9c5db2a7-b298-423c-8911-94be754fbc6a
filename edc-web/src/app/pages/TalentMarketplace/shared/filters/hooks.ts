import { useState } from 'react';
import { searchOrgV2 } from 'edc-web-sdk/requests/organizations.js';
import { searchLocV2 } from 'edc-web-sdk/requests/hrData.v2';
import {
  orgDataToSearchResults,
  locDataToSearchResults
} from 'centralized-design-system/src/TreeView/dataMappers';

export const useOrgTreeViewSearch = ({ lang }: { lang: string }): [boolean, typeof onOrgSearch] => {
  const [isSearching, setIsSearching] = useState(false);

  const onOrgSearch = async ({
    orgType,
    query,
    minLevel,
    maxLevel
  }: {
    orgType: string;
    query: string;
    minLevel?: number;
    maxLevel: number;
  }) => {
    setIsSearching(true);
    try {
      const response = await searchOrgV2(
        {
          orgType,
          minLevel: minLevel || 1,
          maxLevel,
          searchText: query,
          pageSize: 2000,
          filterInactive: true,
          resolveParent: true,
          resolveAllParents: true,
          setHasChildTrueOnlyIfActivePresent: true
        },
        lang
      );

      if (!response?.divisions) {
        throw new Error('No data found');
      }

      const convertedData = orgDataToSearchResults(response.divisions, minLevel);
      setIsSearching(false);
      return Promise.resolve(convertedData);
    } catch (error) {
      setIsSearching(false);
      console.error('Error fetching data:', error);
    }
  };

  return [isSearching, onOrgSearch];
};

export const useLocTreeViewSearch = ({ lang }: { lang: string }): [boolean, typeof onLocSearch] => {
  const [isLocSearching, setIsLocSearching] = useState(false);

  const onLocSearch = async ({
    query,
    minLevel,
    maxLevel
  }: {
    query: string;
    minLevel?: number;
    maxLevel?: number;
  }) => {
    setIsLocSearching(true);
    try {
      const response = await searchLocV2(
        {
          minLevel: minLevel || 1,
          maxLevel: maxLevel || 99,
          searchText: query,
          pageSize: 2000,
          filterInactive: true,
          resolveParent: true,
          resolveAllParents: true,
          setHasChildTrueOnlyIfActivePresent: true
        },
        lang
      );
      if (!response?.locations) {
        throw new Error('No data found');
      }

      const convertedData = locDataToSearchResults(response.locations, minLevel);
      setIsLocSearching(false);
      return Promise.resolve(convertedData);
    } catch (error) {
      setIsLocSearching(false);
      console.error('Error fetching data:', error);
    }
  };

  return [isLocSearching, onLocSearch];
};
