import React, { useLayoutEffect, useRef, useState, useId } from 'react';
import PropTypes from 'prop-types';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { getOcgOrEgt } from '@utils/utils';
import LD from '../../../../containers/LDStore';
import { AsyncSearchInput } from 'centralized-design-system/src/Inputs';
import { connect } from 'react-redux';
import _ from 'lodash';
import { getNewTranslatedLabel } from '@components/common/TranslatedLabel';
import { translateInterestsLabel } from '@components/modals/SkillsModal/helpers';
import { FILTER_LIST_INITIAL_LENGTH } from './Filters.constants';
import './OpportunitiesFilterModalList.scss';

const OpportunitiesFilterModalListCustomSearch = ({
  filterCfg,
  items,
  ocg,
  egt,
  currentUserLanguage,
  getTopicsFromV3Domain,
  taxonomyDomain,
  onSearchSelected,
  labels,
  hideLegend = false,
  filterResults
}) => {
  const [selected, setSelected] = useState('');
  const [isExpanded, setIsExpanded] = React.useState(false);
  const ulRef = useRef();
  const listId = useId();
  const [ulMaxHeight, setUlMaxHeight] = React.useState('auto');
  const associatedItems = items?.filter(({ isAddedBySearch }) => !isAddedBySearch);
  const addedBySearchItems = items?.filter(({ isAddedBySearch }) => isAddedBySearch);

  useLayoutEffect(() => {
    if (!isExpanded) {
      setUlMaxHeight(ulRef.current?.clientHeight || 'auto');
    }
  }, [isExpanded]);

  const onChangeHandler = val => {
    if (val) {
      let newOption = [];
      setSelected(val);
      switch (filterCfg.customSearch) {
        case 'roleIds':
        case 'ROLE':
          const roleIdLabel = val.label.match(/-(.+)/)?.[1] || '';
          newOption = [roleIdLabel, val.id];
          break;
        case 'skills':
        case 'SKILL':
          newOption = [val.label, val.id];
          break;
        case 'locations':
          newOption = [val.label, val.value];
          break;
        case 'jobFamily':
          newOption = [val.label, val.value];
          break;
        case 'LANGUAGES':
          newOption = [val.label, val.id];
          break;
        default:
      }
      onSearchSelected(newOption);
    }
  };
  const customSearchProps = {};

  const mapToUserList = data =>
    data
      .filter(opt => opt.id)
      .map((item, idx) => <li key={`${filterCfg.title}-${idx}`}>{item.element}</li>);

  switch (filterCfg.customSearch) {
    case 'roleIds':
    case 'ROLE':
      customSearchProps.extraPayload = {
        fields: 'id,label,parent,name',
        'taxo_subtypes[]': getOcgOrEgt(ocg, egt, false),
        pageNumber: 1,
        pageSize: 50
      };
      customSearchProps.extraData = {
        currentUserLang: currentUserLanguage,
        isSettingsPage: false,
        filterResults
      };
      customSearchProps.roles = true;
      customSearchProps.skills = false;
      customSearchProps.topics = false;
      customSearchProps.locations = false;
      customSearchProps.topicValueField = undefined;
      customSearchProps.topicValueTranformFn = undefined;
      customSearchProps.jobFamily = false;
      break;
    case 'skills':
    case 'SKILL':
      customSearchProps.extraPayload = {
        fields: 'id,label,name'
      };
      customSearchProps.extraData = {
        taxonomyDomain,
        currentUserLang: currentUserLanguage,
        filterResults
      };
      customSearchProps.roles = false;
      customSearchProps.skills = getTopicsFromV3Domain;
      customSearchProps.topics = !getTopicsFromV3Domain;
      customSearchProps.locations = false;
      customSearchProps.topicValueField = 'name';
      customSearchProps.topicValueTranformFn = val => val.split('.').pop();
      customSearchProps.jobFamily = false;
      break;
    case 'locations':
      customSearchProps.extraData = {
        currentUserLang: currentUserLanguage,
        isSettingsPage: false
      };
      customSearchProps.roles = false;
      customSearchProps.skills = false;
      customSearchProps.topics = false;
      customSearchProps.locations = true;
      customSearchProps.topicValueField = undefined;
      customSearchProps.topicValueTranformFn = undefined;
      customSearchProps.jobFamily = false;
      break;
    case 'jobFamily':
      customSearchProps.extraPayload = {
        language: currentUserLanguage,
        sortByScore: true
      };
      customSearchProps.roles = false;
      customSearchProps.skills = false;
      customSearchProps.topics = false;
      customSearchProps.locations = false;
      customSearchProps.topicValueField = undefined;
      customSearchProps.topicValueTranformFn = undefined;
      customSearchProps.jobFamily = true;
      break;
    case 'TIMEZONE':
      customSearchProps.timezones = true;
      break;
    case 'LANGUAGES':
      customSearchProps.languages = true;
    default:
  }

  const skillsLabel = getNewTranslatedLabel({
    labelObj: window.__edOrgData.OrgConfig?.web?.labels['web/labels/expertise'],
    appName: 'web.common.main',
    profileLanguage: currentUserLanguage
  });

  const getSubHeaderTranslation = id => {
    if (['SKILL', 'skills'].includes(id)) {
      return translatr('web.talentmarketplace.main', 'YourSkillsAndGoals', {
        skills: skillsLabel,
        learningGoals: translateInterestsLabel(labels, currentUserLanguage)
      });
    }
    if (['ROLE', 'roleIds'].includes(id)) {
      return translatr('web.talentmarketplace.main', 'YourCurrentOrAspirationalRolesConfigurable', {
        tm_aspirational_roles: omp('tm_tm_aspirational_roles')
      });
    }
    return '';
  };

  const getPlaceholder = () => {
    let result;
    switch (filterCfg.customSearch) {
      case 'roleIds':
      case 'ROLE':
        result = translatr('web.talentmarketplace.main', 'SearchAllRoles', {
          tm_job_roles: omp('tm_tm_job_roles')
        });
        break;
      case 'skills':
      case 'SKILL':
        result = translatr('web.talentmarketplace.main', 'SearchAllSkills');
        break;
      default:
        result = translatr('web.talentmarketplace.main', 'SearchHere');
        break;
    }
    return result;
  };

  return (
    <fieldset className="opportunity-filter-modal-list">
      {(filterCfg.label || filterCfg.labelOmp) && (
        <legend
          id={listId}
          className={`filter-list__title font-weight-600 ${hideLegend ? 'sr-only' : ''}`}
        >
          {filterCfg.label ? translatr(...filterCfg.label) : omp(filterCfg.labelOmp)}
        </legend>
      )}
      <AsyncSearchInput
        isClearable={true}
        id={`custom-${filterCfg.customSearch}-filter`}
        key={`custom-${filterCfg.customSearch}-filter`}
        items={selected === '' ? [] : [selected]}
        defaultInputValue=""
        onChange={val => {
          onChangeHandler(val);
          _.delay(() => {
            setSelected('');
          }, 1);
        }}
        title={getPlaceholder()}
        hideTitle={true}
        placeholder={getPlaceholder()}
        users={false}
        groups={false}
        channels={false}
        multiselect={false}
        locations={customSearchProps.locations}
        roles={customSearchProps.roles}
        skills={customSearchProps.skills}
        topics={customSearchProps.topics}
        extraData={customSearchProps.extraData}
        extraPayload={customSearchProps.extraPayload}
        topicValueField={customSearchProps.topicValueField}
        topicValueTranformFn={customSearchProps.topicValueTranformFn}
        jobFamily={customSearchProps.jobFamily}
        timezones={customSearchProps.timezones}
        languages={customSearchProps.languages}
        excludeSkillsIds={[]}
      />
      <div className="filter-list-container">
        <ul key={`custom-${filterCfg.customSearch}-filter-list-from-search`}>
          {mapToUserList(addedBySearchItems)}
        </ul>

        {['SKILL', 'skills', 'ROLE', 'roleIds'].includes(filterCfg.id) &&
          associatedItems?.length > 0 && (
            <h4 className="supporting-text list-sub-header">
              {getSubHeaderTranslation(filterCfg.id)}
            </h4>
          )}
        <ul
          ref={ulRef}
          key={`custom-${filterCfg.customSearch}-filter-list`}
          style={{ maxHeight: isExpanded ? `${ulMaxHeight}px` : 'none' }}
        >
          {mapToUserList(
            isExpanded ? associatedItems : associatedItems.slice(0, FILTER_LIST_INITIAL_LENGTH)
          )}
        </ul>
      </div>
      {associatedItems?.length > FILTER_LIST_INITIAL_LENGTH && (
        <button
          aria-describedby={listId}
          aria-expanded={isExpanded}
          className="opportunity-filter-modal-list__show-more"
          aria-label={`${translatr(
            'web.common.main',
            isExpanded ? 'ShowLess' : 'ShowMore'
          )} ${(filterCfg.label || filterCfg.labelOmp) &&
            (filterCfg.label ? translatr(...filterCfg.label) : omp(filterCfg.labelOmp))}`}
          onClick={() => setIsExpanded(expanded => !expanded)}
        >
          {translatr('web.common.main', isExpanded ? 'ShowLess' : 'ShowMore')}
        </button>
      )}
    </fieldset>
  );
};

const mapStateToProps = state => {
  return {
    ocg: state.team?.get('isOcgEnabled') || window.__ED__.isOcgEnabled,
    egt: state.team?.get('isEgtEnabled'),
    labels: state.team.get('OrgConfig')?.labels,
    currentUserLanguage: state.currentUser.get('profile')?.get?.('language') || 'en',
    getTopicsFromV3Domain: ['v3', 'FS-onboarding'].includes(LD.onboardingVersion()),
    taxonomyDomain: state.team.get('config')?.taxonomy_domain
  };
};

OpportunitiesFilterModalListCustomSearch.propTypes = {
  loading: PropTypes.bool,
  currentUserLanguage: PropTypes.string,
  ocg: PropTypes.bool,
  egt: PropTypes.bool,
  getTopicsFromV3Domain: PropTypes.bool,
  taxonomyDomain: PropTypes.object,
  filterCfg: PropTypes.object,
  items: PropTypes.array,
  onSearchSelected: PropTypes.func,
  labels: PropTypes.object,
  hideLegend: PropTypes.bool,
  filterResults: PropTypes.func
};

export default connect(mapStateToProps)(OpportunitiesFilterModalListCustomSearch);
