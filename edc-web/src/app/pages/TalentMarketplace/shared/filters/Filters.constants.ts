import { translatr } from 'centralized-design-system/src/Translatr';
import {
  showDurations,
  showPrograms,
  showTopics,
  showTypes
} from 'opportunity-marketplace/helpers';
import GeolocationPreferencesFilter from '../../../../components/geolocation/filter/GeolocationPreferencesFilter';

export interface ISortingOption {
  id: string;
  value: string;
  i18n: [string, string];
}

export interface IFilter {
  id: string;
  label?: string[];
  labelOmp?: string;
  type: symbol;
  search?: boolean;
  customSearch?: string;
  data: Array<any>;
  available?: boolean;
}

export interface IFilterState {
  filters: Record<string, Record<string, Array<{ name: string; value: string }>>>;
  config: Array<IFilter>;
  bucketName: FilterBucketType;
  keyword: string;
  pageSize: number;
  jobType: string;
}

export const TM_ROLE_FILTER_BUCKET_NAME = 'tmRolesFilters';
export const TM_VACANCY_FILTER_BUCKET_NAME = 'tmVacanciesFilters';
export const TM_ROLE_FILTER_LXP_SEARCH_BUCKET_NAME = 'tmRolesFiltersLXP';
export const TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME = 'tmVacanciesFiltersLXP';
export const TM_PROJECT_FILTER_BUCKET_NAME = 'tmProjectsFilters';
export const TM_PROJECT_FILTER_LXP_SEARCH_BUCKET_NAME = 'tmProjectsFiltersLXP';
export const TM_MENTORSHIP_FILTER_BUCKET_NAME = 'tmMentorshipsFilters';
export const TM_MENTORSHIP_FILTER_LXP_SEARCH_BUCKET_NAME = 'tmMentorshipsFiltersLXP';
export const TM_CARRERPATH_FILTER_BUCKET_NAME = 'tmCareerPathFilters';
export const POST_PER_PAGE = 12;
export const POST_PER_PAGE_LXP = 20;
export const SCORE_RANGE_EXCELLENT = 'EXCELLENT';
export const SCORE_RANGE_GOOD = 'GOOD';
export const SCORE_RANGE_FAIR = 'FAIR';
export const SCORE_RANGE_LOW = 'LOW';

export type FilterBucketType =
  | typeof TM_ROLE_FILTER_BUCKET_NAME
  | typeof TM_VACANCY_FILTER_BUCKET_NAME
  | typeof TM_ROLE_FILTER_LXP_SEARCH_BUCKET_NAME
  | typeof TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME
  | typeof TM_PROJECT_FILTER_BUCKET_NAME
  | typeof TM_PROJECT_FILTER_LXP_SEARCH_BUCKET_NAME
  | typeof TM_MENTORSHIP_FILTER_BUCKET_NAME
  | typeof TM_MENTORSHIP_FILTER_LXP_SEARCH_BUCKET_NAME
  | typeof TM_CARRERPATH_FILTER_BUCKET_NAME;

export const TM_FILTER_SORT_BY: Array<ISortingOption> = [
  {
    id: 'MATCH_DESC',
    value: 'Excellent to Low Match',
    i18n: ['web.talentmarketplace.main', 'SortingOptionExcellentToLowMatch']
  },
  {
    id: 'MATCH_ASC',
    value: 'Low to Excellent Match',
    i18n: ['web.talentmarketplace.main', 'SortingOptionLowToExcellentMatch']
  },
  {
    id: 'DATE_DESC',
    value: 'Newest First',
    i18n: ['web.talentmarketplace.main', 'SortingOptionNewestFirst']
  },
  {
    id: 'DATE_ASC',
    value: 'Oldest First',
    i18n: ['web.talentmarketplace.main', 'SortingOptionOldestFirst']
  },
  {
    id: 'TITLE_ASC',
    value: 'Alphabetical: A-Z',
    i18n: ['web.talentmarketplace.main', 'SortingOptionAlphabeticalAZ']
  },
  {
    id: 'TITLE_DESC',
    value: 'Alphabetical: Z-A',
    i18n: ['web.talentmarketplace.main', 'SortingOptionAlphabeticalZA']
  }
];

// TODO: Make this generic
export const TM_PROJECT_FILTER_SORT_BY: Array<ISortingOption> = [
  {
    id: 'RELEVANCE',
    value: 'Relevance',
    i18n: ['web.talentmarketplace.main', 'SortingOptionRelevance']
  },
  {
    id: 'NEWTOOLD',
    value: 'Newest',
    i18n: ['web.talentmarketplace.main', 'SortingOptionNewestFirst']
  },
  {
    id: 'OLDTONEW',
    value: 'Oldest',
    i18n: ['web.talentmarketplace.main', 'SortingOptionOldestFirst']
  },
  {
    id: 'CAPACITY',
    value: 'Openings',
    i18n: ['web.talentmarketplace.main', 'SortingOptionOpenings']
  }
];

export const TM_MENTORSHIP_FILTER_SORT_BY: Array<ISortingOption> = [
  {
    id: 'RELEVANCE',
    value: 'Relevance',
    i18n: ['web.talentmarketplace.main', 'SortingOptionRelevance']
  },
  {
    id: 'TITLE_ASC',
    value: 'Alphabetical: A-Z',
    i18n: ['web.talentmarketplace.main', 'SortingOptionAlphabeticalAZ']
  },
  {
    id: 'TITLE_DESC',
    value: 'Alphabetical: Z-A',
    i18n: ['web.talentmarketplace.main', 'SortingOptionAlphabeticalZA']
  }
];

export const FILTER_TYPE = {
  REMOTE: Symbol('remote'),
  LOCAL: Symbol('local'),
  LOV: Symbol('lov'),
  CUSTOM: Symbol('custom'),
  ORG: Symbol('org'),
  CUSTOM_COMPONENT: Symbol('customComponent'),
  LOCATION: Symbol('loc')
};

export const FILTER_SCORE_RANGE = [
  [['web.talentmarketplace.main', 'Excellent'], SCORE_RANGE_EXCELLENT],
  [['web.talentmarketplace.main', 'Good'], SCORE_RANGE_GOOD],
  [['web.talentmarketplace.main', 'Fair'], SCORE_RANGE_FAIR],
  [['web.talentmarketplace.main', 'Low'], SCORE_RANGE_LOW]
];

export const STATIC_FILTERS: Map<FilterBucketType, Array<IFilter>> = new Map([
  [
    TM_ROLE_FILTER_BUCKET_NAME,
    [
      {
        id: 'skills',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'skills',
        data: [],
        available: true
      },
      {
        id: 'jobFamily',
        label: ['web.talentmarketplace.main', 'JobFamily'],
        type: FILTER_TYPE.LOCAL,
        search: true,
        customSearch: 'jobFamily',
        data: [],
        available: true
      },
      {
        id: 'locations',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        showTreeView: true,
        data: []
      }
    ]
  ],
  [
    TM_VACANCY_FILTER_BUCKET_NAME,
    [
      {
        id: 'geolocation',
        label: ['web.talentmarketplace.main', 'PreferredLocation'],
        type: FILTER_TYPE.CUSTOM_COMPONENT,
        data: [],
        component: GeolocationPreferencesFilter
      },
      {
        id: 'roleIds',
        labelOmp: 'tm_tm_job_roles',
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'roleIds',
        data: [],
        available: true
      },
      {
        id: 'skills',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'skills',
        data: [],
        available: true
      },
      {
        id: 'locations',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        showTreeView: true,
        data: []
      }
    ]
  ],
  [
    TM_ROLE_FILTER_LXP_SEARCH_BUCKET_NAME,
    [
      {
        id: 'skills',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'skills',
        data: [],
        available: true
      },
      {
        id: 'jobFamily',
        label: ['web.talentmarketplace.main', 'JobFamily'],
        type: FILTER_TYPE.LOCAL,
        search: true,
        customSearch: 'jobFamily',
        data: [],
        available: true
      },
      {
        id: 'locations',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        data: []
      }
    ]
  ],
  [
    TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME,
    [
      {
        id: 'geolocation',
        label: ['web.talentmarketplace.main', 'PreferredLocation'],
        type: FILTER_TYPE.CUSTOM_COMPONENT,
        data: [],
        component: GeolocationPreferencesFilter
      },
      {
        id: 'roleIds',
        labelOmp: 'tm_tm_job_roles',
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'roleIds',
        data: [],
        available: true
      },
      {
        id: 'skills',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'skills',
        data: [],
        available: true
      },
      {
        id: 'locations',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        data: []
      }
    ]
  ],
  [
    TM_PROJECT_FILTER_BUCKET_NAME,
    [
      {
        id: 'ROLE',
        labelOmp: 'tm_tm_job_roles',
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'ROLE',
        data: [],
        available: true
      },
      {
        id: 'SKILL',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'SKILL',
        data: [],
        available: true
      },
      {
        id: 'TIMEZONE',
        label: ['web.common.main', 'TimeZones'],
        type: FILTER_TYPE.LOCAL,
        search: true,
        data: [],
        available: true
      },
      {
        id: 'LANGUAGES',
        label: ['web.common.main', 'Languages'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'LANGUAGES',
        data: [],
        available: true
      },
      {
        id: 'LOCATION',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        showTreeView: true,
        data: []
      },
      {
        id: 'DURATION',
        label: ['web.common.main', 'Duration'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        customSearch: 'DURATION',
        data: [],
        available: true
      },
      {
        id: 'CAPACITY',
        label: ['web.common.main', 'Openings'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        customSearch: 'CAPACITY',
        data: [],
        available: true
      },
      {
        id: 'REMOTEPOSSIBLE',
        label: null,
        type: FILTER_TYPE.LOCAL,
        search: false,
        customSearch: 'REMOTEPOSSIBLE',
        data: [],
        available: true
      }
    ]
  ],
  [
    TM_PROJECT_FILTER_LXP_SEARCH_BUCKET_NAME,
    [
      {
        id: 'ROLE',
        labelOmp: 'tm_tm_job_roles',
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'ROLE',
        data: [],
        available: true
      },
      {
        id: 'SKILL',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'SKILL',
        data: [],
        available: true
      },
      {
        id: 'TIMEZONE',
        label: ['web.common.main', 'TimeZones'],
        type: FILTER_TYPE.LOCAL,
        search: true,
        data: [],
        available: true
      },
      {
        id: 'LANGUAGES',
        label: ['web.common.main', 'Languages'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'LANGUAGES',
        data: [],
        available: true
      },
      {
        id: 'LOCATION',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        data: []
      },
      {
        id: 'DURATION',
        label: ['web.common.main', 'Duration2'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        customSearch: 'DURATION',
        data: [],
        available: true
      },
      {
        id: 'CAPACITY',
        label: ['web.common.main', 'Openings'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        customSearch: 'CAPACITY',
        data: [],
        available: true
      },
      {
        id: 'REMOTEPOSSIBLE',
        label: ['web.projects.main', 'RemotePossible'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        customSearch: 'REMOTEPOSSIBLE',
        data: [],
        available: true
      }
    ]
  ],
  [
    TM_MENTORSHIP_FILTER_BUCKET_NAME,
    [
      {
        id: 'ROLE',
        labelOmp: 'tm_tm_job_roles',
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'ROLE',
        data: [],
        available: true
      },
      {
        id: 'SKILL',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'SKILL',
        data: [],
        available: true
      },
      {
        id: 'TIMEZONE',
        label: ['web.common.main', 'TimeZones'],
        type: FILTER_TYPE.LOCAL,
        search: true,
        data: [],
        available: true
      },
      {
        id: 'LANGUAGES',
        label: ['web.common.main', 'Languages'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'LANGUAGES',
        data: [],
        available: true
      },
      {
        id: 'LOCATION',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        showTreeView: true,
        search: false,
        data: []
      },
      {
        ...(showPrograms
          ? {
              id: 'PROGRAM',
              label: ['web.talentmarketplace.main', 'Programs'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      },
      {
        ...(showTopics
          ? {
              id: 'TOPICS',
              label: ['web.talentmarketplace.main', 'Topics'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      },
      {
        ...(showTypes
          ? {
              id: 'TYPES',
              label: ['web.talentmarketplace.main', 'Types'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      },
      {
        ...(showDurations
          ? {
              id: 'DURATIONS',
              label: ['web.talentmarketplace.main', 'Durations'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      }
    ]
  ],
  [
    TM_MENTORSHIP_FILTER_LXP_SEARCH_BUCKET_NAME,
    [
      {
        id: 'ROLE',
        labelOmp: 'tm_tm_job_roles',
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'ROLE',
        data: [],
        available: true
      },
      {
        id: 'SKILL',
        label: ['web.common.main', 'Skills'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'SKILL',
        data: [],
        available: true
      },
      {
        id: 'TIMEZONE',
        label: ['web.common.main', 'TimeZones'],
        type: FILTER_TYPE.LOCAL,
        search: true,
        data: [],
        available: true
      },
      {
        id: 'LANGUAGES',
        label: ['web.common.main', 'Languages'],
        type: FILTER_TYPE.CUSTOM,
        search: false,
        customSearch: 'LANGUAGES',
        data: [],
        available: true
      },
      {
        id: 'LOCATION',
        label: ['web.talentmarketplace.main', 'Locations'],
        type: FILTER_TYPE.LOCAL,
        search: false,
        data: []
      },
      {
        ...(showPrograms
          ? {
              id: 'PROGRAM',
              label: ['web.talentmarketplace.main', 'Programs'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      },
      {
        ...(showTopics
          ? {
              id: 'TOPICS',
              label: ['web.talentmarketplace.main', 'Topics'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      },
      {
        ...(showTypes
          ? {
              id: 'TYPES',
              label: ['web.talentmarketplace.main', 'Types'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      },
      {
        ...(showDurations
          ? {
              id: 'DURATIONS',
              label: ['web.talentmarketplace.main', 'Durations'],
              type: FILTER_TYPE.LOCAL,
              search: true,
              data: [],
              available: true
            }
          : null)
      }
    ]
  ]
]);

export const FILTER_CONFIG = new Map([
  [TM_ROLE_FILTER_BUCKET_NAME, []],
  [TM_VACANCY_FILTER_BUCKET_NAME, []],
  [TM_PROJECT_FILTER_BUCKET_NAME, []],
  [TM_MENTORSHIP_FILTER_BUCKET_NAME, []],
  [TM_CARRERPATH_FILTER_BUCKET_NAME, []],
  [TM_MENTORSHIP_FILTER_LXP_SEARCH_BUCKET_NAME, []],
  [TM_PROJECT_FILTER_LXP_SEARCH_BUCKET_NAME, []],
  [TM_ROLE_FILTER_LXP_SEARCH_BUCKET_NAME, []],
  [TM_VACANCY_FILTER_LXP_SEARCH_BUCKET_NAME, []],
]);

export const FILTERS_DEFAULT_ASSOCIATION_ID = 'default';

export const FILTER_IDS = {
  JOBFAMILY: 'jobFamily'
};

export const FILTER_KEY_COUNTER_KEY_MAP = {
  skills: 'capabilities',
  levelId: 'level',
  roleIds: 'linkedRoleStatus'
};

export const FILTERS_WITH_SEARCH = new Set(['SKILL', 'skills', 'ROLE', 'roleIds']);

export const capacityOptions = [
  [translatr('web.talentmarketplace.main', '0OpeningsAvailable'), '0'],
  [translatr('web.talentmarketplace.main', 'OpeningsAvailable'), '1']
];

export const durationRanges = [
  [
    translatr('web.talentmarketplace.main', 'LessThan1Week') || 'Less than 1 week',
    'Less than 1 week'
  ],
  [
    translatr('web.talentmarketplace.main', 'LessThan1Month') || 'Less than 1 month',
    'Less than 1 month'
  ],
  [translatr('web.talentmarketplace.main', '1MonthPlus') || '1 month +', '1 month +'],
  [
    translatr('web.talentmarketplace.main', 'NMonthsPlus', { monthCount: 3 }) || '3 months +',
    '3 month +'
  ],
  [
    translatr('web.talentmarketplace.main', 'NMonthsPlus', { monthCount: 6 }) || '6 months +',
    '6 month +'
  ]
];

export const FILTER_LIST_INITIAL_LENGTH = 5;
