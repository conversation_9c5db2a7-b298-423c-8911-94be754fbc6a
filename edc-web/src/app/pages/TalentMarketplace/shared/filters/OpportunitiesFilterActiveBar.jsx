import PropTypes from 'prop-types';
import { Tags } from 'centralized-design-system/src/Tags';
import { translatr } from 'centralized-design-system/src/Translatr';
import { connect } from 'react-redux';
import {
  tmClearFilterFromGroup,
  tmClearAllFilters
} from '../../../../actions/talentmarketplaceActions';
import './OpportunitiesFilterActiveBar.scss';
import { FILTERS_DEFAULT_ASSOCIATION_ID, TM_VACANCY_FILTER_BUCKET_NAME } from './Filters.constants';
import CreateAlert from '../OpportunitiesSavedFilters/CreateAlert';

const OpportunitiesFilterActiveBar = ({
  counter,
  loading,
  dispatch,
  filtersState,
  associationId = FILTERS_DEFAULT_ASSOCIATION_ID,
  isModifiedFromAlertFilters,
  onRemoveFilter
}) => {
  const filtersKeys = Object.keys(filtersState.filters[associationId] || {}).sort();
  const clearFilter = (groupName, filterValue) => {
    dispatch(tmClearFilterFromGroup(filtersState, groupName, filterValue, associationId));
    onRemoveFilter?.();
  };
  const clearFilters = () => {
    dispatch(tmClearAllFilters(filtersState, associationId));
    onRemoveFilter?.();
  };

  if (!filtersState.keyword && filtersKeys.length === 0) {
    return null;
  }

  return (
    <div className="tm-filterbar__container m-margin-bottom">
      {filtersKeys.length > 0 && (
        <div className="tm-filterbar__activefilters">
          <span role="list">
            {filtersKeys.map(filterName =>
              filtersState.filters[associationId][filterName].map((item, idx) => {
                const tagLabel = Array.isArray(item.name) ? translatr(...item.name) : item.name;
                return (
                  <Tags
                    key={idx}
                    cb={() => clearFilter(filterName, item.value)}
                    name={tagLabel}
                    role="listitem"
                    ariaLabel={translatr('cds.common.main', 'RemoveName', {
                      name: `${tagLabel} ${translatr('web.common.main', 'Filter')}`
                    })}
                  />
                );
              })
            )}
          </span>
          <button
            title={translatr('web.talentmarketplace.main', 'ClearAllFilters')}
            onClick={clearFilters}
            className="tm-filterbar__button--remove"
            aria-label={translatr('web.talentmarketplace.main', 'ClearAllFilters')}
          >
            <i className="icon-clear-filter" aria-hidden="true" />
          </button>
        </div>
      )}
      {!loading && (
        <div className="tm-filterbar__results-heading">
          <h2 className="tab-heading m-margin-bottom m-margin-top omp-counter-field">
            {translatr('web.common.main', 'ResultsCount', { count: counter || 0 })}
          </h2>
          {/* it is avilable only for vacancy filter (for now) */}
          {counter > 0 &&
            filtersState?.bucketName === TM_VACANCY_FILTER_BUCKET_NAME &&
            (isModifiedFromAlertFilters ? (
              <CreateAlert
                filtersKeys={filtersKeys}
                filtersState={filtersState}
                associationId={associationId}
              />
            ) : (
              translatr('web.talentmarketplace.main', 'AlertExistsForThisSearch')
            ))}
        </div>
      )}
    </div>
  );
};

OpportunitiesFilterActiveBar.propTypes = {
  filtersState: PropTypes.object,
  counter: PropTypes.number,
  opportunityTypeName: PropTypes.string,
  loading: PropTypes.bool,
  associationId: PropTypes.string,
  isModifiedFromAlertFilters: PropTypes.bool,
  onRemoveFilter: PropTypes.func
};

export default connect()(OpportunitiesFilterActiveBar);
