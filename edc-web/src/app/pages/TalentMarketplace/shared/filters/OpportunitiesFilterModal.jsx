import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect, useSelector } from 'react-redux';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import Modal, {
  ModalContent,
  ModalFooter,
  ModalHeader
} from 'centralized-design-system/src/Modals';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import Checkbox from 'centralized-design-system/src/Checkbox';
import OptionListWithSearchFilter from 'centralized-design-system/src/OptionListWithSearchFilter';
import OpportunitiesFilterModalList from './OpportunitiesFilterModalList';
import OpportunitiesFilterModalListCustomSearch from './OpportunitiesFilterModalListCustomSearch';
import './OpportunitiesFilterModal.scss';
import {
  tmSaveSearchFilters,
  tmAddCustomFilterOption,
  tmClearUnusedFiltersFromConfig
} from '../../../../actions/talentmarketplaceActions';
import {
  FILTERS_DEFAULT_ASSOCIATION_ID,
  FILTERS_WITH_SEARCH,
  FILTER_TYPE
} from './Filters.constants';
import {
  appendActiveFiltersThatIsMissingInConfig,
  cleanupFilterNames,
  extractFiltersIds,
  mapOptionToOpportunityCounter,
  sortConfigBasedOnActiveFilters
} from './Filters.utils';
import { EXT_JOB_TYPE_MAPPED, OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  prepareGeolocationsForFilter,
  checkIfDistanceValueInGeolocationsIsInvalid,
  isGeolocationVisible
} from '@components/geolocation/utils';
import { LOCATION_USAGE_OPTIONS, LOCATION_ASSOCIATION } from '../../helpers';
import { useLocTreeViewSearch, useOrgTreeViewSearch } from './hooks';
import { getSelectionForInput } from 'centralized-design-system/src/TreeView/utils';
import { orgAssociation } from '../../../../actions/organizationsActions';
import FocusLock from 'react-focus-lock';
import Accordion from 'centralized-design-system/src/Accordion';
import { Flyout, FlyoutSide } from 'centralized-design-system/src/Flyout';
import { Button } from 'centralized-design-system/src/Buttons';
import OpportunitiesFilterTreeView from './OpportunitiesFilterTreeView';

const FlyoutWrapper = ({
  children,
  applyFiltersHandler,
  clearAllFilters,
  onClose,
  isError,
  isOpen
}) => (
  <Flyout
    isOpen={isOpen}
    side={FlyoutSide.RIGHT}
    onClose={() => {
      onClose();
    }}
    title={translatr('web.talentmarketplace.main', 'AllFilters')}
    width="38rem"
    footerButtons={
      <>
        <Button color="secondary" variant="borderless" onClick={clearAllFilters}>
          {translatr('web.common.main', 'ClearAllLabel')}
        </Button>
        <Button color="primary" onClick={applyFiltersHandler} disabled={isError}>
          {translatr('web.common.main', 'ShowResults')}
        </Button>
      </>
    }
  >
    <div className="opportunities-filter-flyout">{children}</div>
  </Flyout>
);

FlyoutWrapper.propTypes = {
  children: PropTypes.node,
  applyFiltersHandler: PropTypes.func,
  clearAllFilters: PropTypes.func,
  onClose: PropTypes.func,
  isError: PropTypes.bool,
  isOpen: PropTypes.bool
};

const ModalWrapper = ({ children, onClose, applyFiltersHandler, isError }) => (
  <FocusLock onDeactivation={onClose}>
    <Modal className="opportunities-filter-modal" onClose={onClose}>
      <ModalHeader
        title={translatr('web.talentmarketplace.main', 'AllFilters')}
        onClose={onClose}
      />
      <ModalContent>{children}</ModalContent>
      <ModalFooter>
        <button className="ed-btn ed-btn-neutral" onClick={onClose}>
          {translatr('web.talentmarketplace.main', 'Cancel')}
        </button>
        <button onClick={applyFiltersHandler} className="ed-btn ed-btn-primary" disabled={isError}>
          {translatr('web.talentmarketplace.main', 'Apply')}
        </button>
      </ModalFooter>
    </Modal>
  </FocusLock>
);

ModalWrapper.propTypes = {
  children: PropTypes.node,
  applyFiltersHandler: PropTypes.func,
  onClose: PropTypes.func,
  isError: PropTypes.bool
};

const AccordionWrapper = ({ title, children, selectedItems = 0, clearFilter, isOpen, onClick }) => {
  const isEmpty =
    !children ||
    (typeof children === 'string' && children.trim() === '') ||
    (Array.isArray(children) && children.filter(item => item !== false).length === 0);

  if (isEmpty) {
    return null;
  }

  return (
    <Accordion
      items={[
        {
          title,
          content: children,
          headingLevel: 'h3',
          headerComponents: [
            {
              key: 'tag',
              props: {
                text: `${selectedItems} ${translatr('web.common.main', 'Selected').toLowerCase()}`,
                show: selectedItems > 0,
                onClose: clearFilter
              }
            },
            { key: 'arrow' }
          ],
          isAccordionOpen: isOpen
        }
      ]}
      onAccordionClick={onClick}
    />
  );
};

AccordionWrapper.propTypes = {
  title: PropTypes.string,
  children: PropTypes.node,
  selectedItems: PropTypes.number,
  clearFilter: PropTypes.func,
  isOpen: PropTypes.bool,
  onClick: PropTypes.func
};

const OpportunitiesFilterModal = ({
  onClose,
  filtersState,
  saveFilters,
  addFilterOption,
  loading,
  jobType,
  locationsConfiguration,
  currentUserLang,
  isGeolocationEnabled,
  clearUnusedFiltersFromConfig,
  displayAsFlyout = false,
  isOpen,
  onApplyFilters
}) => {
  const [isError, setIsError] = useState(false);
  const [filters, setFilters] = useState(
    filtersState.filters[FILTERS_DEFAULT_ASSOCIATION_ID] || {}
  );
  const countersConfig = useSelector(
    state => state.availableFiltersCounters?.get('config')?.[jobType]
  );
  const isRoleOrJobType = Object.keys(EXT_JOB_TYPE_MAPPED).includes(jobType);
  const filtersConfig = appendActiveFiltersThatIsMissingInConfig(
    filters,
    filtersState.config || [],
    isRoleOrJobType
  );
  const filtersWithCustomComponent = filtersConfig.filter(
    filter => filter.type === FILTER_TYPE.CUSTOM_COMPONENT
  );

  const [openAccordion, setOpenAccordion] = useState([1, 2, 3]);
  let accordionIndex = 0;

  const defaultFiltersIds = extractFiltersIds(filters);

  const FilterWrapper = displayAsFlyout ? AccordionWrapper : React.Fragment;
  const ContentWrapper = displayAsFlyout ? FlyoutWrapper : ModalWrapper;

  const onChangeHandler = (ev, filterIdPrefix, filterId, option) => {
    const checked = ev.currentTarget.checked;
    markFilterOption(filterIdPrefix, filterId, option, checked);
  };

  const markFilterOption = (filterIdPrefix, filterId, option, checked, sortConfig) => {
    // prefixing the id to maintain the sameorder between chips and filters
    const prefixedId = `${filterIdPrefix}-${filterId}`;
    const [name, value] = option;

    setFilters(prevState => {
      const newFilters = Object.assign({}, prevState);

      // remove the old key first since the prefix can change
      const prevFilterKey = Object.keys(newFilters).find(key => key.includes(filterId));
      delete newFilters[prevFilterKey];

      newFilters[prefixedId] = [...(prevState[prevFilterKey] || [])];

      // cleaning filters so we can perform lookups by filter ids easier
      const cleanedNewFilters = cleanupFilterNames(newFilters);
      const existingFilter = cleanedNewFilters[filterId].find(fil => fil.value === value);
      if (existingFilter) {
        if (!checked) {
          newFilters[prefixedId] = cleanedNewFilters[filterId].filter(fil => fil.value !== value);
        } else {
          existingFilter.value = value;
        }
      } else {
        newFilters[prefixedId].unshift({
          name,
          value
        });
      }
      if (sortConfig) {
        sortConfigBasedOnActiveFilters(filtersConfig, newFilters);
      }
      return newFilters;
    });
  };

  const updateFilterStateWithCheckedItems = (
    filterIdPrefix,
    filterId,
    filterLabel,
    selectedIds
  ) => {
    const prefixedId = `${filterIdPrefix}-${filterId}`;
    setFilters(prevState => {
      const newFilters = Object.assign({}, prevState);

      const prevFilterKey = Object.keys(newFilters).find(key => key.includes(filterId));
      delete newFilters[prevFilterKey];
      const filterData = filtersState?.config?.find(({ id }) => id === filterId)?.data;
      const newSelection = getSelectionForInput(filterData, new Set(selectedIds));
      newFilters[prefixedId] = newSelection.map(id => ({
        name:
          id === '0'
            ? `${filterLabel} ${translatr('web.common.main', 'All')}`
            : filterData?.find(el => el.id === id)?.name || '',
        value: id
      }));

      return newFilters;
    });
  };

  const addCustomComponentFilter = (filterIdPrefix, filterId, option) => {
    const prefixedId = `${filterIdPrefix}-${filterId}`;
    setFilters(prevState => {
      const newFilters = Object.assign({}, prevState);
      newFilters[prefixedId] = option;
      return newFilters;
    });
  };

  const isChecked = (filterId, val) => {
    const cleanedFilters = cleanupFilterNames(filters);
    return !!(cleanedFilters[filterId] || []).find(fil => fil.value === val);
  };

  const getAmountOfChecked = filterId => {
    const cleanedFilters = cleanupFilterNames(filters);
    return (cleanedFilters[filterId] || []).length;
  };

  const applyFiltersHandler = () => {
    sortConfigBasedOnActiveFilters(filtersConfig, filters);
    clearUnusedFiltersFromConfig({ ...filtersState, filters }, FILTERS_DEFAULT_ASSOCIATION_ID);
    saveFilters({ ...filtersState, filters: { [FILTERS_DEFAULT_ASSOCIATION_ID]: filters } });
    onClose(true);
    onApplyFilters?.();
  };

  const clearFilter = (filterIdPrefix, filterId) => {
    const prefixedId = `${filterIdPrefix}-${filterId}`;
    setFilters(prevState => {
      const newFilters = Object.assign({}, prevState);
      delete newFilters[prefixedId];
      return newFilters;
    });
  };

  const clearAllFilters = () => {
    setFilters(() => {
      return {};
    });
  };

  useEffect(() => {
    sortConfigBasedOnActiveFilters(filtersConfig, filters);
  }, []);

  useEffect(() => {
    setFilters(filtersState.filters[FILTERS_DEFAULT_ASSOCIATION_ID]);
  }, [isOpen]);

  const prepareLabel = ({ count: optionCount, name, fromRequestCount }) => {
    if (isRoleOrJobType) {
      return (
        <span>
          {name}{' '}
          {countersConfig?.loading ? (
            <Skeleton inline width={16} height={16} />
          ) : (
            `(${fromRequestCount})`
          )}
        </span>
      );
    }

    return optionCount ? `${name} (${optionCount})` : name;
  };

  const shouldDisplayFilter = filter => {
    if (filter.type === FILTER_TYPE.ORG && filter.data.length) return true;
    return (filter.type === FILTER_TYPE.LOCAL || filter.type === FILTER_TYPE.LOV) && !filter.hide;
  };

  const addGeolocationFilter = async (geolocations, filter, filterIdPrefix) => {
    const isDistanceValid = checkIfDistanceValueInGeolocationsIsInvalid(geolocations);
    await setIsError(isDistanceValid);
    const geolocationsForFilters = prepareGeolocationsForFilter(geolocations);
    addFilterOption({
      filterId: filter.id,
      options: geolocationsForFilters,
      bucketName: filtersState.bucketName
    });
    addCustomComponentFilter(filterIdPrefix, filter.id, geolocationsForFilters);
  };

  const handleAccordionClick = index => {
    openAccordion.includes(index)
      ? setOpenAccordion(openAccordion.filter(i => i !== index))
      : setOpenAccordion([...openAccordion, index]);
  };

  const isAccordionOpen = index => {
    return openAccordion.includes(index);
  };

  return (
    <ContentWrapper
      isOpen={isOpen}
      onClose={onClose}
      isError={isError}
      applyFiltersHandler={applyFiltersHandler}
      clearAllFilters={clearAllFilters}
    >
      <div className="filter-content">
        {loading && (
          <div className="filter-list">
            {new Array(4).fill(null).map((_, i) => (
              <OpportunitiesFilterModalList
                key={`opportunities-filter-modal-list-loading-${i}`}
                loading
              />
            ))}
          </div>
        )}
        {!loading &&
          filtersWithCustomComponent.length > 0 &&
          isGeolocationVisible(
            isGeolocationEnabled,
            locationsConfiguration,
            LOCATION_ASSOCIATION.JOB_VACANCY,
            LOCATION_USAGE_OPTIONS.JOB_VACANCY_FILTER
          ) && (
            <div className="filter-list first-filter-list">
              {filtersWithCustomComponent.map((filter, filterIdx) => {
                const filterIdPrefix = (1e15 + filterIdx + '').slice(-2);
                const index = ++accordionIndex;
                return (
                  <FilterWrapper
                    key={filter.id}
                    selectedItems={getAmountOfChecked(filter.id)}
                    clearFilter={() => {
                      clearFilter(filterIdPrefix, filter.id);
                    }}
                    title={
                      filter.label
                        ? Array.isArray(filter.label)
                          ? translatr(...filter.label)
                          : filter.label
                        : omp(filter.labelOmp)
                    }
                    onClick={() => {
                      handleAccordionClick(index);
                    }}
                    isOpen={isAccordionOpen(index)}
                  >
                    {!displayAsFlyout && (
                      <h4 className="filter-list__title font-weight-600">
                        {translatr(...filter.label)}
                      </h4>
                    )}

                    <filter.component
                      currentGeolocations={cleanupFilterNames(filters)[filter.id]}
                      cb={geolocations =>
                        addGeolocationFilter(geolocations, filter, filterIdPrefix)
                      }
                    />
                  </FilterWrapper>
                );
              })}
            </div>
          )}
        {!loading && filtersConfig.length > 0 && (
          <div className="filter-list">
            {filtersConfig.map((filter, filterIdx) => {
              const filterIdPrefix = (
                1e15 +
                (filterIdx + filtersWithCustomComponent?.length) +
                ''
              ).slice(-2); // format number with leading zero
              const isOrgFilter = filter.type === FILTER_TYPE.ORG;
              const index = ++accordionIndex;
              const treeViewSearchHook = filter.orgId ? useOrgTreeViewSearch : useLocTreeViewSearch;

              return (
                <FilterWrapper
                  key={filter.id}
                  selectedItems={getAmountOfChecked(filter.id)}
                  clearFilter={() => {
                    clearFilter(filterIdPrefix, filter.id);
                  }}
                  title={
                    filter.label
                      ? Array.isArray(filter.label)
                        ? translatr(...filter.label)
                        : filter.label
                      : omp(filter.labelOmp)
                  }
                  onClick={() => {
                    handleAccordionClick(index);
                  }}
                  isOpen={isAccordionOpen(index)}
                >
                  {filter.type === FILTER_TYPE.CUSTOM && filter.available && (
                    <OpportunitiesFilterModalListCustomSearch
                      hideLegend={displayAsFlyout}
                      key={filter.id}
                      filterCfg={filter}
                      filterResults={option => {
                        return (
                          mapOptionToOpportunityCounter({
                            key: filter.id,
                            option: [option.label, option.id],
                            countersConfig
                          }) > 0
                        );
                      }}
                      onSearchSelected={newItem => {
                        if (
                          !FILTERS_WITH_SEARCH.has(filter.id) ||
                          !filtersState.config
                            ?.find(({ id }) => id === filter.id)
                            ?.data?.find(([_, id]) => id === newItem[1])
                        ) {
                          addFilterOption({
                            filterId: filter.id,
                            options: [
                              { name: newItem[0], value: newItem[1], isAddedBySearch: true }
                            ],
                            bucketName: filtersState.bucketName
                          });
                        }
                        markFilterOption(filterIdPrefix, filter.id, newItem, true, true);
                      }}
                      items={filter.data.map((option, idx) => {
                        const [name, value, count, isAddedBySearch] = option;
                        const fromRequestCount = mapOptionToOpportunityCounter({
                          key: filter.id,
                          option,
                          countersConfig
                        });
                        return {
                          id: name,
                          isAddedBySearch,
                          element: (
                            <Checkbox
                              className="break-all"
                              id={`${filter.id}-${idx}`}
                              key={`${filter.id}-${idx}`}
                              label={prepareLabel({ count, name, fromRequestCount })}
                              ariaLabelHidden={true}
                              disabled={
                                ((!fromRequestCount && isRoleOrJobType && countersConfig?.loaded) ||
                                  countersConfig?.loading) &&
                                !isChecked(filter.id, value)
                              }
                              value={value}
                              onChange={ev =>
                                onChangeHandler.call(
                                  undefined,
                                  ev,
                                  filterIdPrefix,
                                  filter.id,
                                  option
                                )
                              }
                              checked={isChecked(filter.id, value)}
                              isTranslated
                            />
                          )
                        };
                      })}
                    />
                  )}
                  {filter.type === FILTER_TYPE.REMOTE && <OptionListWithSearchFilter />}
                  {shouldDisplayFilter(filter) &&
                    (filter.showTreeView ? (
                      <OpportunitiesFilterTreeView
                        key={`key-${filter.id}`}
                        uniqueId={filter.id}
                        label={filter.label}
                        data={
                          [orgAssociation.JOB_ROLE, orgAssociation.JOB_VACANCY].includes(jobType)
                            ? filter.data.map(opt => ({
                                ...opt,
                                name:
                                  opt.id === '0'
                                    ? opt.name
                                    : `${opt.name} (${mapOptionToOpportunityCounter({
                                        key: isOrgFilter ? 'organizations' : filter.id,
                                        option: [opt.name, opt.id],
                                        countersConfig
                                      })})`
                              }))
                            : filter.data
                        }
                        currentUserLang={currentUserLang}
                        treeViewSearchHook={treeViewSearchHook}
                        defaultSelectedIds={defaultFiltersIds[filter.id]}
                        onSelect={props => {
                          updateFilterStateWithCheckedItems(
                            filterIdPrefix,
                            filter.id,
                            filter.label,
                            [
                              // eslint-disable-next-line react/prop-types
                              ...props.treeState.selectedIds
                            ]
                          );
                        }}
                        orgId={filter.orgId}
                        maxLevel={filter.maxLevel}
                      />
                    ) : (
                      <OpportunitiesFilterModalList
                        hideLegend={displayAsFlyout}
                        title={
                          filter.label
                            ? Array.isArray(filter.label)
                              ? translatr(...filter.label)
                              : filter.label
                            : omp(filter.labelOmp)
                        }
                        key={filter.id}
                        id={filter.id}
                        searchable={filter.search}
                        isShowMore
                        items={filter.data.map((option, idx) => {
                          const [rawName, value, count] = option;
                          const fromRequestCount = mapOptionToOpportunityCounter({
                            key: isOrgFilter ? 'organizations' : filter.id,
                            option,
                            countersConfig
                          });
                          const name = Array.isArray(rawName) ? translatr(...rawName) : rawName;
                          return {
                            id: name,
                            element: (
                              <Checkbox
                                id={`${filter.id}-${idx}`}
                                key={`${filter.id}-${idx}`}
                                label={prepareLabel({ count, name, fromRequestCount })}
                                ariaLabelHidden={true}
                                disabled={
                                  ((!fromRequestCount &&
                                    isRoleOrJobType &&
                                    countersConfig?.loaded) ||
                                    countersConfig?.loading) &&
                                  !isChecked(filter.id, value)
                                }
                                value={value}
                                onChange={ev =>
                                  onChangeHandler.call(
                                    undefined,
                                    ev,
                                    filterIdPrefix,
                                    filter.id,
                                    option
                                  )
                                }
                                checked={isChecked(filter.id, value)}
                                isTranslated
                              />
                            )
                          };
                        })}
                      />
                    ))}
                </FilterWrapper>
              );
            })}
          </div>
        )}
      </div>
    </ContentWrapper>
  );
};

const mapDispatchToProps = dispatch => {
  return {
    saveFilters: newFilters => dispatch(tmSaveSearchFilters(newFilters)),
    addFilterOption: payload => dispatch(tmAddCustomFilterOption(payload)),
    clearUnusedFiltersFromConfig: (filters, associationId) =>
      dispatch(tmClearUnusedFiltersFromConfig(filters, associationId))
  };
};

function mapStoreStateToProps({ currentUser, locationsConfiguration, configService }) {
  return {
    currentUserLang: currentUser.get('profile')?.get('language'),
    locationsConfiguration: {
      isLocationShown: locationsConfiguration.get('enable'),
      locationAssociation: locationsConfiguration.get('association'),
      locationFieldVisibility: locationsConfiguration.get('visibility')
    },
    isGeolocationEnabled: configService.get('omp')?.['geolocation']?.['enable_geolocation']
  };
}

OpportunitiesFilterModal.propTypes = {
  onClose: PropTypes.func,
  filtersState: PropTypes.shape({
    filters: PropTypes.object,
    config: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
    bucketName: PropTypes.string
  }),
  saveFilters: PropTypes.func,
  addFilterOption: PropTypes.func,
  loading: PropTypes.bool,
  jobType: PropTypes.oneOf(Object.values(OPPORTUNITY_TYPE)),
  locationsConfiguration: PropTypes.shape({
    isLocationShown: PropTypes.bool,
    locationAssociation: PropTypes.array,
    locationFieldVisibility: PropTypes.object
  }),
  isGeolocationEnabled: PropTypes.bool,
  clearUnusedFiltersFromConfig: PropTypes.func,
  currentUserLang: PropTypes.string,
  displayAsFlyout: PropTypes.bool,
  isOpen: PropTypes.bool,
  onApplyFilters: PropTypes.func
};

export default connect(mapStoreStateToProps, mapDispatchToProps)(OpportunitiesFilterModal);
