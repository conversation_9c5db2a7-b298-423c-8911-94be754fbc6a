@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.tm-filterbar__container {
  .tm-filterbar__keyword {
    word-break: break-all;
    margin: var(--ed-spacing-xs) 0;
    font-size: var(--ed-font-size-sm);
    span {
      font-weight: var(--ed-font-weight-semibold);
    }
  }

  .tm-filterbar__activefilters {
    font-size: var(--ed-font-size-supporting);
  }
  .tm-filterbar__button--remove,
  .tm-filterbar__button--clear {
    vertical-align: middle;
    font-size: var(--ed-font-size-lg);
    color: var(--ed-text-color-supporting);
    cursor: pointer;
    :hover {
      color: var(--ed-negative-2);
    }
  }
  .tm-filterbar__button--clear {
    font-size: var(--ed-font-size-base);
  }
  .tm-filterbar__results-heading {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    gap: var(--ed-spacing-xs);

    button {
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      align-items: center;
    }
  }
}
