import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { translatr } from 'centralized-design-system/src/Translatr';
import { InputWithButton, Select } from 'centralized-design-system/src/Inputs';
import { Button, ButtonLink } from 'centralized-design-system/src/Buttons';
import OpportunitiesFilterModal from './OpportunitiesFilterModal';
import { TM_FILTER_SORT_BY } from './Filters.constants';
import { tmSaveSearchFilters } from '../../../../actions/talentmarketplaceActions';
import './OpportunitiesFilterPanel.scss';

const OpportunitiesFilterPanel = ({
  dispatch,
  filtersState,
  manageLabel,
  manageLink,
  sortOptions,
  loading,
  jobType,
  callToAction,
  opportunityTypeName,
  mainLabel = '',
  onApplyFilters
}) => {
  const [searchTerm, setSearchTerm] = useState(filtersState.keyword);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const filterHandler = (filterName, value) => {
    const newFilters = { ...filtersState, [filterName]: value };
    dispatch(tmSaveSearchFilters(newFilters));
    onApplyFilters?.();
  };

  const onChangeSort = value => {
    filterHandler('sortBy', value);
  };

  const onChangeKeyword = value => {
    setSearchTerm(value);
  };

  const onSubmitKeyword = () => {
    filterHandler('keyword', searchTerm);
  };

  useEffect(() => {
    setSearchTerm(filtersState.keyword);
  }, [filtersState.keyword]);

  const sortingOptions = (sortOptions || TM_FILTER_SORT_BY).map(option => ({
    ...option,
    value: translatr(option.i18n[0], option.i18n[1])
  }));

  return (
    <>
      {mainLabel && (
        <label htmlFor="opportunity-search-input" className="ed-input-title ed-select-label ">
          {mainLabel}
        </label>
      )}
      <div className="tm__opportunities-filters-container">
        <InputWithButton
          id="opportunity-search-input"
          defaultValue={searchTerm}
          onChangeHandler={onChangeKeyword}
          onSubmitHandler={onSubmitKeyword}
          placeholder={translatr('web.talentmarketplace.main', 'SearchHere')}
          buttonContent={<i className="icon-search" aria-hidden={true} />}
          isDisabled={loading}
          buttonAriaLabel={translatr('web.common.main', 'Search')}
          isTranslated={true}
          ariaLabelValue={translatr('web.talentmarketplace.main', 'SearchTopic', {
            topic: opportunityTypeName
          })}
        />
        <Button
          id="ed-opportunities-filters"
          color="secondary"
          variant="ghost"
          onClick={() => setShowFilterModal(true)}
        >
          <i className="filter-button icon-filter" aria-hidden="true" />
          <span className="xl-font-size text-color">
            {translatr('web.talentmarketplace.main', 'Filters')}{' '}
          </span>
        </Button>

        <Select
          id="ed-opportunities-sort-by"
          ariaLabelledBy="ed-opportunities-sort-by"
          defaultValue={filtersState.sortBy.value}
          onChange={onChangeSort}
          items={sortingOptions}
          title={translatr('web.talentmarketplace.main', 'SortBy')}
          isTranslated={true}
        />

        {manageLabel && (
          <ButtonLink color="secondary" variant="ghost" to={manageLink}>
            <i className="icon-external-link" />
            <span className="xl-font-size text-color">{manageLabel} </span>
          </ButtonLink>
        )}
        {callToAction}
      </div>
      {showFilterModal && (
        <OpportunitiesFilterModal
          jobType={jobType}
          loading={loading}
          onClose={() => {
            setShowFilterModal(false);
            document.getElementById('ed-opportunities-filters').focus();
          }}
          filtersState={filtersState}
          onApplyFilters={onApplyFilters}
        />
      )}
    </>
  );
};

OpportunitiesFilterPanel.propTypes = {
  filtersState: PropTypes.object,
  setFiltersHandler: PropTypes.func,
  sortOptions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      value: PropTypes.string
    })
  ),
  manageLabel: PropTypes.string,
  manageLink: PropTypes.string,
  loading: PropTypes.bool,
  callToAction: PropTypes.node,
  jobType: PropTypes.oneOf(['job_vacancy', 'job_role']),
  opportunityTypeName: PropTypes.string,
  mainLabel: PropTypes.string,
  onApplyFilters: PropTypes.func
};

export default connect()(OpportunitiesFilterPanel);
