import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import _ from 'lodash';
import Spinner from '@components/common/spinner';
import { getCandidateProfileById } from 'edc-web-sdk/requests/sourcing';
import { getUserPassport } from 'edc-web-sdk/requests/users';
import CareerPreferences from './CareerPreferences';
import JobRoleWithAspirational from './JobRoleWithAspirational';
import OrgUnits from './OrgUnits';
import SkillsWithMatrix from './SkillsWithMatrix';
import TitleWithTag from './TitleWithTag';
import CandidateRoles from './CandidateRoles';
import WorkHistory from './WorkHistory';
import LayoutDivider from '../LayoutDivider/LayoutDivider';
import NoData from './NoData';
import { getAvailableLocations } from 'actions/availableLocationsActions';
import { translatr } from 'centralized-design-system/src/Translatr';
import './CandidateProfile.scss';
import {
  JOB_TYPE,
  OPPORTUNITY_TYPE,
  getMentorProfileByUserId
} from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  getOrgLabelAndValue,
  getPreferredOrgLabelAndValue
} from 'opportunity-marketplace/shared/CandidateListing/utils';
import { VISIBILITY_CONTEXT, getOrgUnitsById } from 'edc-web-sdk/requests/hrData.v2';
import unescape from 'lodash/unescape';
import { getEcsOpportunitiesInfo } from '@actions/teamActions';
import { TableContext } from '../CollapsibleTable/table-context';
import { getLocationsConfiguration } from '@actions/locationsConfigurationActions';
import { LOCATION_USAGE_OPTIONS } from 'opportunity-marketplace/helpers';
import { users } from 'edc-web-sdk/requests';
import classNames from 'classnames';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';

const CandidateProfile = ({
  candidateId,
  availableLocations,
  opportunityType,
  currentUserLanguage,
  ecsVacancyConfig,
  selectedProfileData = {},
  locationsEnabled,
  getLocationsConfig,
  getPreferredLocations,
  getEcsOpportunitiesConfig,
  locationsVisibility,
  skillPassportConfig,
  currentUserId,
  theme
}) => {
  const [profileData, setProfileData] = React.useState({});
  const [certificationsData, setCertificationsData] = React.useState([]);
  const [badgesData, setBadgesData] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const hideCareerPreferences = [OPPORTUNITY_TYPE.PROJECT];
  const { state } = React.useContext(TableContext);

  const getLocations = React.useCallback(() => {
    return !availableLocations
      ? getPreferredLocations(currentUserLanguage)
      : Promise.resolve(availableLocations);
  });

  const getLocationConfig = React.useCallback(() => {
    return !locationsVisibility
      ? getLocationsConfig(currentUserLanguage)
      : Promise.resolve(locationsVisibility);
  });

  const getEcsOpportunitiesConfiguration = React.useCallback(() => {
    return !ecsVacancyConfig ? getEcsOpportunitiesConfig() : Promise.resolve(ecsVacancyConfig);
  });

  const getCurrentUserSkills = async () => {
    const data = await users.getUserSkills(candidateId);
    return data.skills;
  };

  React.useEffect(() => {
    (async () => {
      const [
        locationResponse,
        candidateResponse,
        usersResponse,
        mentorProfileData,
        locationsVisibilityResponse,
        currentUserSkillResponse
      ] = await Promise.allSettled([
        getLocations(),
        getCandidateProfileById(
          candidateId,
          currentUserLanguage,
          selectedProfileData.shortlisted ? selectedProfileData.shortlistedBy || currentUserId : ''
        ),
        getUserPassport(candidateId),
        getMentorProfileByUserId(candidateId),
        getLocationConfig(),
        getCurrentUserSkills(),
        getEcsOpportunitiesConfiguration()
      ]);

      const newProfileData = {
        capabilities: [],
        workHistory: [],
        careerPreferences: {
          jobTypes: [],
          locationsV2: [],
          workplaceModels: [],
          levels: [],
          schedules: [],
          jobRoleTypes: [],
          careerGoals: [],
          orgUnits: [],
          openToOffers: []
        },
        userJobRole: {
          jobRoleName: ''
        },
        aspirationalRoles: [],
        currentManager: {
          name: '',
          jobTitle: ''
        },
        userRoles: [],
        learningGoals: [],
        orgUnits: [],
        userSkills: []
      };

      if (candidateResponse.status === 'fulfilled' && !_.isNull(candidateResponse.value)) {
        let orgDetails = { divisions: [] };
        const orgDetailsPayload = {
          filterInActive: true,
          ids: [],
          visibilityContext: VISIBILITY_CONTEXT.SOURCING,
          resolveParent: true,
          resolveAllParents: true
        };
        if (!_.isEmpty(candidateResponse.value['organizationUnits'])) {
          const orgPayload = {
            ...orgDetailsPayload,
            ids: candidateResponse.value['organizationUnits'] ?? []
          };
          orgDetails = await getOrgUnitsById(orgPayload, currentUserLanguage);
        }

        const { orgUserAssociation, orgCareerPrefAssociation, orgTypes } = state.orgUnitsMap;
        newProfileData.orgUnits = orgUserAssociation?.length
          ? orgUserAssociation.map(type => getOrgLabelAndValue(type, orgDetails, orgTypes))
          : [];

        _.forEach(candidateResponse.value, (value, key) => {
          if (!_.isNull(value) && !_.isEmpty(value)) {
            newProfileData[key] = value;
          }
        });

        const { careerPreferences } = candidateResponse.value;

        if (!_.isNull(careerPreferences)) {
          const processPreferredLocation = () => {
            if (!_.isEmpty(careerPreferences.locationsV2)) {
              newProfileData.careerPreferences.locationsV2 = careerPreferences.locationsV2.locations.map(
                location => locationResponse.value.find(loc => loc.id === location)?.location_name
              );
            }
          };

          if (
            locationsEnabled &&
            locationsVisibility &&
            locationsVisibility.location_name.includes(LOCATION_USAGE_OPTIONS.CAREER_PREFERENCES)
          ) {
            processPreferredLocation();
          } else if (
            locationsVisibilityResponse?.value?.enable &&
            locationsVisibilityResponse.value.location_name_visibility.includes(
              LOCATION_USAGE_OPTIONS.CAREER_PREFERENCES
            )
          ) {
            processPreferredLocation();
          } else {
            delete careerPreferences['locationsV2'];
          }

          let organizationUnits = { divisions: [] };
          if (!_.isEmpty(careerPreferences.organizationUnits)) {
            const orgPayload = {
              ...orgDetailsPayload,
              ids: careerPreferences.organizationUnits ?? []
            };
            organizationUnits = await getOrgUnitsById(orgPayload, currentUserLanguage);
          }
          newProfileData.careerPreferences.organizationUnits = orgCareerPrefAssociation.length
            ? orgCareerPrefAssociation.map(type =>
                getPreferredOrgLabelAndValue(type, organizationUnits, orgTypes)
              )
            : [];

          newProfileData.careerPreferences.openToOffers = [
            {
              openToOffer: (() => {
                if (!careerPreferences.openToOffers) {
                  return translatr('web.common.main', 'NotSpecified');
                }
                return careerPreferences.openToOffers === 'OPEN_TO_VACANCIES'
                  ? translatr('web.common.main', 'Yes')
                  : translatr('web.common.main', 'No');
              })()
            }
          ];
        }
      }

      if (usersResponse.status === 'fulfilled')
        newProfileData.userSkills = currentUserSkillResponse.value;

      let updatedProfileData = newProfileData;
      if (!_.isEmpty(mentorProfileData.value.data.result)) {
        updatedProfileData = {
          ...newProfileData,
          userRoles: [...newProfileData?.userRoles, { name: 'mentor' }]
        };
      }
      setProfileData(updatedProfileData);

      if (usersResponse.status === 'fulfilled') {
        setCertificationsData(
          usersResponse.value.certifications.map(({ certificationName, issuer }) => ({
            title: certificationName,
            description: issuer
          }))
        );
        setBadgesData(
          usersResponse.value.badges.map(({ badgeName, issuer }) => ({
            title: badgeName,
            description: issuer
          }))
        );
      }
      setLoading(false);
    })();
  }, []);

  const isNewDesignEnabled = theme === ThemeId.PLARE;

  return loading ? (
    <div className="make-center width-100">
      <Spinner />
    </div>
  ) : (
    <div className={classNames('candidate-profile-container', { plare: isNewDesignEnabled })}>
      {isNewDesignEnabled && <LayoutDivider />}
      <div className="candidate-row-1">
        <JobRoleWithAspirational
          jobRoleDto={profileData.userJobRole}
          aspirationalRoles={profileData.aspirationalRoles}
        />
      </div>
      <OrgUnits orgUnits={profileData.orgUnits} />
      <LayoutDivider />
      {!hideCareerPreferences.includes(opportunityType) && (
        <>
          <div className="candidate-row-2">
            <CareerPreferences
              careerPreferences={profileData.careerPreferences}
              language={currentUserLanguage}
            />
          </div>
          <LayoutDivider />
        </>
      )}
      <div className="candidate-row-3 align-items-stretch gap-20">
        <div className="flex-dir-column align-items-start-imp gap-20 restrict-div">
          <SkillsWithMatrix
            capabilities={profileData.capabilities}
            candidateId={candidateId}
            userSkills={profileData.userSkills}
            ariaLabel={selectedProfileData.name}
          />
          <TitleWithTag
            showCount={true}
            data={
              _.isEmpty(profileData.learningGoals)
                ? []
                : profileData.learningGoals.map(({ name }) => name)
            }
            initialMaxDisplayTags={5}
            variant="tag"
            title={`${translatr('web.sourcing.candidate-profile', 'LearningGoals')}`}
            ariaLabel={selectedProfileData.name}
          />
          {skillPassportConfig && skillPassportConfig.certificates && (
            <TitleWithTag
              showCount={false}
              data={certificationsData}
              initialMaxDisplayTags={2}
              variant="card"
              title={translatr('web.sourcing.candidate-profile', 'Certifications')}
              ariaLabel={selectedProfileData.name}
            />
          )}
          {skillPassportConfig && skillPassportConfig.badges && (
            <TitleWithTag
              showCount={false}
              data={badgesData}
              initialMaxDisplayTags={2}
              variant="card"
              title={translatr('web.sourcing.candidate-profile', 'Badges')}
              ariaLabel={selectedProfileData.name}
            />
          )}
          <CandidateRoles roles={profileData.userRoles} />
        </div>
        <div>
          <LayoutDivider align="vertical" />
        </div>
        <WorkHistory
          workHistory={profileData.workHistory}
          language={currentUserLanguage}
          ariaLabel={selectedProfileData.name}
        />
      </div>
      <LayoutDivider />
      <div className="candidate-row-4 align-items-start-imp">
        <div className="flex-dir-column align-items-start-imp">
          <p className="title-text-small">
            {translatr('web.sourcing.candidate-profile', 'CurrentManager')}
          </p>
          <div>
            <h6 className="title-txt-normal mb-zero m-margin-right">
              {_.isEmpty(profileData.currentManager.name) ? (
                <NoData />
              ) : (
                profileData.currentManager.name
              )}
            </h6>
          </div>
          <h4 className="title-text-small title-info-text-color">
            {_.isEmpty(profileData.currentManager.jobTitle) ? (
              <NoData />
            ) : (
              unescape(profileData.currentManager.jobTitle)
            )}
          </h4>
        </div>
        {profileData.recruiterProfileDto &&
          (profileData.recruiterProfileDto.name ||
            profileData.recruiterProfileDto.status === 'suspended') && (
            <div className="flex-dir-column align-items-start-imp shortlisted-by-section">
              <h4 className="title-text-small">
                {translatr('web.sourcing.candidate-profile', 'ShortlistedBy')}
              </h4>
              <div>
                <h6 className="title-txt-normal mb-zero m-margin-right">
                  {profileData.recruiterProfileDto.status === 'suspended'
                    ? translatr('web.sourcing.candidate-profile', 'InactiveUser')
                    : profileData.recruiterProfileDto.name}
                </h6>
              </div>
            </div>
          )}
      </div>
      {isNewDesignEnabled && <LayoutDivider />}
    </div>
  );
};

const mapStateToProps = state => ({
  availableLocations: state.availableLocations.get('availableLocations'),
  currentUserLanguage:
    state.currentUser.get('profile')?.get?.('language') ||
    state.team?.get('config')?.DefaultOrgLanguage ||
    'en',
  ecsVacancyConfig: state.team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY],
  locationsEnabled: state.locationsConfiguration.get('enable'),
  locationsVisibility: state.locationsConfiguration.get('visibility'),
  skillPassportConfig: state.team?.get('config')['skills_passport_options'],
  currentUserId: state.currentUser.get('id'),
  theme: state.theme.get('themeId')
});

const mapDispatchToProps = () => dispatch => ({
  getPreferredLocations: lang => dispatch(getAvailableLocations(lang)),
  getLocationsConfig: () => dispatch(getLocationsConfiguration()),
  getEcsOpportunitiesConfig: () => dispatch(getEcsOpportunitiesInfo())
});

CandidateProfile.propTypes = {
  candidateId: PropTypes.number,
  availableLocations: PropTypes.array,
  dispatch: PropTypes.func,
  opportunityType: PropTypes.string,
  currentUserLanguage: PropTypes.string,
  ecsVacancyConfig: PropTypes.object,
  selectedProfileData: PropTypes.object,
  locationsEnabled: PropTypes.bool,
  locationsVisibility: PropTypes.object,
  getLocationsConfig: PropTypes.func,
  getPreferredLocations: PropTypes.func,
  getEcsOpportunitiesConfig: PropTypes.func,
  skillPassportConfig: PropTypes.object,
  currentUserId: PropTypes.string,
  theme: PropTypes.string
};

export default connect(mapStateToProps, mapDispatchToProps)(CandidateProfile);
