import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { getFormattedDate } from '../utils';
import { translatr } from 'centralized-design-system/src/Translatr';
import NoData from './NoData';
import { Button } from 'centralized-design-system/src/Buttons';

const dateFormat = { month: 'short', year: 'numeric' };

const WorkHistory = ({ workHistory = [], language, ariaLabel }) => {
  const initMaxWorkHistory = 3;
  const [showMore, setShowMore] = React.useState(false);
  const [maxWorkHistoryDisplay, setMaxWorkHistoryDisplay] = React.useState(3);
  const totalWorkHistory = workHistory.length;
  const remainingWorkHistory = totalWorkHistory - initMaxWorkHistory;

  const showAllWorkHistory = () => {
    setShowMore(true);
    setMaxWorkHistoryDisplay(totalWorkHistory);
  };

  const hideAllWorkHistory = () => {
    setShowMore(false);
    setMaxWorkHistoryDisplay(initMaxWorkHistory);
  };

  const onKeyDownHandler = (e, functionName) => {
    if (e.key === 'Enter') {
      functionName(e);
    }
  };

  const sortedData = workHistory
    .sort((a, b) => {
      if (a.startDate > b.startDate) return -1;
    })
    .slice(0, maxWorkHistoryDisplay);

  return (
    <div className="flex-dir-column align-items-start-imp gap-10">
      <h3 className="section-heading mb-4 title-size-base">
        {translatr('web.sourcing.candidate-profile', 'WorkHistory')}
      </h3>
      <div className="flex-dir-column align-items-start-imp">
        {_.isEmpty(workHistory) ? (
          <NoData />
        ) : (
          sortedData.map(({ startDate, endDate, title, company }, index) => {
            const formattedStartDate = getFormattedDate(startDate, dateFormat, language);
            const formattedEndDate =
              endDate === null
                ? translatr('web.sourcing.candidate-profile', 'Present')
                : getFormattedDate(endDate, dateFormat, language);
            const displayDate = `${formattedStartDate} - ${formattedEndDate}`;

            return (
              <div className="align-items-start-imp gap-20">
                <div className="flex-dir-column s-margin-top gap-10">
                  <span className="icon-oval-fill line-connector mt-4" />
                  {sortedData.length - 1 !== index && <div className="vertical-line" />}
                </div>
                <p className="work-history-company-name-and-duration mt-5">{displayDate}</p>
                <div className="flex-dir-column align-items-start-imp">
                  <h4 className="section-heading mb-zero title-size-base">{title}</h4>
                  <p className="work-history-company-name-and-duration">{company}</p>
                </div>
              </div>
            );
          })
        )}
        {remainingWorkHistory > 0 && !showMore && (
          <Button
            color="primary"
            variant="borderless"
            onClick={showAllWorkHistory}
            onKeyDown={e => onKeyDownHandler(e, showAllWorkHistory)}
            tabIndex={0}
            padding="xsmall"
            aria-label={translatr('web.sourcing.candidate-profile', 'ShowMoreItems', {
              number: remainingWorkHistory,
              labelName: translatr('web.sourcing.candidate-profile', 'WorkHistory'),
              sectionName: ariaLabel
            })}
          >
            <span className="button-styles">
              +{remainingWorkHistory} {translatr('web.sourcing.candidate-profile', 'More')}
            </span>
          </Button>
        )}
        {remainingWorkHistory > 0 && showMore && (
          <Button
            color="primary"
            variant="borderless"
            onClick={hideAllWorkHistory}
            onKeyDown={e => onKeyDownHandler(e, hideAllWorkHistory)}
            tabIndex={0}
            padding="xsmall"
            aria-label={translatr('web.sourcing.candidate-profile', 'ShowLessItemsForType', {
              labelName: translatr('web.sourcing.candidate-profile', 'ShowLess'),
              sectionName: ariaLabel
            })}
          >
            <span className="button-styles">
              {translatr('web.sourcing.candidate-profile', 'ShowLess')}
            </span>
          </Button>
        )}
      </div>
    </div>
  );
};

WorkHistory.propTypes = {
  workHistory: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      title: PropTypes.string,
      level: PropTypes.shape({
        id: PropTypes.string,
        value: PropTypes.number
      }),
      details: PropTypes.string,
      company: PropTypes.string,
      startDate: PropTypes.string,
      endDate: PropTypes.string,
      isCurrent: PropTypes.bool,
      isManagementPosition: PropTypes.bool,
      contractType: PropTypes.string,
      industry: PropTypes.string,
      internal: PropTypes.bool,
      roleId: PropTypes.string,
      capabilities: PropTypes.any,
      eventDateTime: PropTypes.any,
      eventStatus: PropTypes.string
    })
  ),
  language: PropTypes.string,
  ariaLabel: PropTypes.string
};
export default WorkHistory;
