import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { translatr } from 'centralized-design-system/src/Translatr';
import NoData from './NoData';
import { extractData } from 'opportunity-marketplace/shared/CandidateListing/utils/career_preferences';

const CareerPreferences = ({ careerPreferences, language }) => {
  const data = extractData(careerPreferences, language);

  return (
    <>
      <h3 className="section-heading mb-20 title-size-base">
        {translatr('web.sourcing.candidate-profile', 'CareerPreferences')}
      </h3>
      <div className="width-100">
        {data.map(({ label, value }) => {
          return (
            <div className="flex-dir-column align-items-start-imp" key={label}>
              <h4 className="title-text-small title-info-text-color">{label}</h4>
              {_.isEmpty(value) ? (
                <NoData />
              ) : (
                value.map(desc => {
                  return (
                    <h6 className="title-txt-normal mb-zero" key={desc}>
                      {desc}
                    </h6>
                  );
                })
              )}
            </div>
          );
        })}
        {careerPreferences.organizationUnits.map(orgUnit => (
          <div className="flex-dir-column align-items-start-imp">
            <h4 className="title-text-small title-info-text-color">{orgUnit.label}</h4>
            <h6 className="title-txt-normal">
              {_.isEmpty(orgUnit.value) ? (
                <NoData />
              ) : (
                orgUnit.value.map(desc => {
                  return <h6 className="title-txt-normal mb-zero">{desc}</h6>;
                })
              )}
            </h6>
          </div>
        ))}
      </div>
    </>
  );
};

CareerPreferences.propTypes = {
  careerPreferences: PropTypes.object.isRequired,
  language: PropTypes.string
};
export default CareerPreferences;
