@import '~centralized-design-system/src/Styles/_variables.scss';

.candidate-profile-container {
  flex-direction: column;
  height: auto !important;
  gap: 15px;
  padding: var(--ed-spacing-xl) var(--ed-spacing-base) !important;
  & > * {
    width: 100%;
  }
  & > div {
    padding: 0 var(--ed-spacing-base);
  }
  &.plare {
    padding: 0rem var(--ed-spacing-base) !important;
  }
}

.candidate-row-1 {
  gap: var(--ed-spacing-lg);

  & > div:nth-child(2) {
    gap: 5px;
    & > span:last-child {
      color: var(--ed-primary-base);
    }
  }
}

.candidate-row-2 {
  flex-direction: column;
  align-items: start !important;

  & > div {
    align-items: flex-start !important;
    column-gap: 110px;
    row-gap: 30px;
    flex-wrap: wrap;
  }
}

.display-tag-candidate {
  padding: rem-calc(1) var(--ed-spacing-base) !important;
  height: rem-calc(26) !important;
  margin-right: var(--ed-spacing-2xs) !important;
  font-size: 13px !important;
}

.gap-10 {
  gap: var(--ed-spacing-xs);
}

.gap-20 {
  gap: var(--ed-spacing-lg);
}

.align-items-stretch {
  align-items: stretch !important;
}

.line-connector {
  color: var(--ed-gray-5);
  font-size: 0.375rem;
}

.vertical-line {
  border-left: var(--ed-border-size-sm) solid var(--ed-gray-5);
  height: 1.875rem;
  margin-top: var(--ed-spacing-4xs);
}

.restrict-div {
  flex-basis: 60%;
}

.tooltip-icon {
  border-radius: var(--ed-border-radius-circle);
  border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
  width: 2rem;
  height: 2rem;
}

.title-text-small {
  font-size: var(--ed-font-size-sm) !important;
  font-weight: var(--ed-font-weight-bold) !important;
}

.shortlisted-by-section {
  margin-left: 6.4375rem;
}

.icon-info-circle {
  font-size: var(--ed-font-size-base) !important;
  margin-top: 0.3125rem !important;
}

.title-size-base {
  font-size: var(--ed-font-size-base) !important;
}
