import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { translatr } from 'centralized-design-system/src/Translatr';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { TableContext } from '../CollapsibleTable/table-context';
import { openMatchingSkillsModal as handleMatchingSkillsModal } from 'actions/modalActions';
import { sortSkills } from 'opportunity-marketplace/util';
import { mapSkillsWithDefaultLevel } from '@components/modals/SkillsModal/helpers';
import { generateMatchingMatrixSkills } from '../utils';
import SkillSectionComponent from './SkillSectionComponent';
import { getEcsOpportunitiesInfo } from '@actions/teamActions';
import { Button } from 'centralized-design-system/src/Buttons';

const SkillsWithMatrix = ({
  capabilities,
  openMatchingSkillsModal,
  candidateId,
  userSkills,
  ecsVacancyConfig,
  currentUserLang,
  getEcsOpoConfig,
  ariaLabel
}) => {
  const { state } = React.useContext(TableContext);
  const { requiredSkills, showSkillsMatrix } = state;
  const [skills, setSkills] = useState([]);

  useEffect(() => {
    if (!ecsVacancyConfig) {
      getEcsOpoConfig();
    }
  }, []);

  useEffect(() => {
    if (!ecsVacancyConfig) return;
    const { detected_skills_level: detectedSkillsLevel } = ecsVacancyConfig;

    const skillsWithDefaultLevel = mapSkillsWithDefaultLevel({
      skills: sortSkills(requiredSkills),
      skillsFrom: JOB_TYPE.VACANCY,
      defaultLevel: detectedSkillsLevel,
      currentUserLang
    });
    setSkills(skillsWithDefaultLevel);
  }, [userSkills, ecsVacancyConfig]);

  const matchingMatrixSkills = useMemo(() => {
    if (skills && skills.length) {
      return generateMatchingMatrixSkills(capabilities, userSkills, requiredSkills, skills);
    } else {
      return null;
    }
  }, [skills, userSkills]);

  return (
    <div className="flex-dir-column align-items-start-imp">
      <h3 className="section-heading mb-4 title-size-base">
        {translatr('web.common.main', 'Skills')}
        {showSkillsMatrix && (
          <div className="mt-8">
            <Button
              color="primary"
              variant="borderless"
              onClick={e =>
                openMatchingSkillsModal({
                  skills: sortSkills(requiredSkills),
                  skillsFrom: JOB_TYPE.VACANCY,
                  userId: candidateId,
                  hideSkillsAdditionLink: true,
                  openedFromHtmlElement: e.target
                })
              }
              padding="xsmall"
            >
              <span className="button-styles">
                {translatr('web.sourcing.candidate-profile', 'ViewSkillsMatrix')}
              </span>
            </Button>
          </div>
        )}
      </h3>
      {matchingMatrixSkills && (
        <>
          <SkillSectionComponent
            title={translatr('web.sourcing.candidate-profile', 'MatchingSkills')}
            skills={matchingMatrixSkills.matchingSkills}
            requiredSkills={requiredSkills}
            showBothCounts
            ariaLabel={ariaLabel}
          />
          <SkillSectionComponent
            title={translatr('web.sourcing.candidate-profile', 'NearTargetSkills')}
            skills={matchingMatrixSkills.nearTargetSkills.filter(skill => !skill.isMatching)}
            requiredSkills={requiredSkills}
            allowExtend
            showBothCounts
            ariaLabel={ariaLabel}
          />
          <SkillSectionComponent
            title={translatr('web.sourcing.candidate-profile', 'AdditionalSkills')}
            skills={matchingMatrixSkills.additionalSkills}
            requiredSkills={requiredSkills}
            allowExtend
            ariaLabel={ariaLabel}
          />
          <SkillSectionComponent
            title={translatr('web.sourcing.candidate-profile', 'DetectedSkills')}
            skills={matchingMatrixSkills.detectedSkills}
            requiredSkills={requiredSkills}
            allowExtend
            ariaLabel={ariaLabel}
          />
        </>
      )}
    </div>
  );
};

const mapDispatchToProps = dispatch => ({
  openMatchingSkillsModal: ({ skills, userId, hideSkillsAdditionLink, openedFromHtmlElement }) =>
    dispatch(
      handleMatchingSkillsModal({
        skills,
        userId,
        hideSkillsAdditionLink,
        skillsFrom: JOB_TYPE.VACANCY,
        showCandidateLevelLabel: true,
        openedFromHtmlElement
      })
    ),
  getEcsOpoConfig: () => dispatch(getEcsOpportunitiesInfo())
});

const mapStateToProps = ({ currentUser, team }) => ({
  ecsVacancyConfig: team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY],
  currentUserLang:
    currentUser.get('profile')?.get?.('language') ||
    currentUser.get('profile')?.language ||
    team?.get('config')?.DefaultOrgLanguage ||
    'en'
});

SkillsWithMatrix.propTypes = {
  capabilities: PropTypes.array,
  openMatchingSkillsModal: PropTypes.func,
  candidateId: PropTypes.number,
  ecsVacancyConfig: PropTypes.object,
  userSkills: PropTypes.array,
  currentUserLang: PropTypes.string,
  getEcsOpoConfig: PropTypes.func,
  ariaLabel: PropTypes.string
};

export default connect(mapStateToProps, mapDispatchToProps)(SkillsWithMatrix);
