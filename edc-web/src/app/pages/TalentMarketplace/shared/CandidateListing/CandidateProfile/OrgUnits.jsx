import React from 'react';
import PropTypes from 'prop-types';
import Tooltip from 'centralized-design-system/src/Tooltip';
import NoData from 'opportunity-marketplace/shared/CandidateListing/CandidateProfile/NoData';

const OrgUnits = ({ orgUnits }) => {
  if (orgUnits.length === 0) return null;
  return (
    <div className="align-items-start-imp" style={{ gap: 110 }}>
      {orgUnits.map(orgUnit => (
        <div className="flex-dir-column align-items-start-imp" key={orgUnit.label}>
          <h4 className="title-text-small title-info-text-color">{orgUnit.label}</h4>
          <h6 className="title-txt-normal">
            {orgUnit.value ? (
              <Tooltip message={orgUnit.hierarchy.join(' > ') || orgUnit.label} pos="top">
                {orgUnit.value}
              </Tooltip>
            ) : (
              <NoData />
            )}
          </h6>
        </div>
      ))}
    </div>
  );
};

OrgUnits.propTypes = {
  orgUnits: PropTypes.array
};

export default OrgUnits;
