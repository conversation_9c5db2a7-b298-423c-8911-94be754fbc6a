import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import NoData from './NoData';
import LongArrow from '../../../../../../app/icons/LongArrow';
import { translatr, omp } from 'centralized-design-system/src/Translatr';

const JobRoleWithAspirational = ({ jobRoleDto, aspirationalRoles }) => {
  const aspRoles = _.isEmpty(aspirationalRoles) ? (
    <NoData />
  ) : (
    aspirationalRoles.map(({ role }) => role).join(' | ')
  );
  return (
    <>
      <div className="flex-dir-column align-items-start-imp">
        <h4 className="title-text-small title-info-text-color">
          {translatr('web.sourcing.candidate-profile', 'JobRole')}
        </h4>
        <h6 className="title-txt-normal">
          {_.isEmpty(jobRoleDto.jobRoleName) ? <NoData /> : jobRoleDto.jobRoleName}
        </h6>
      </div>
      <div>
        <span className="current-role__arrow icon-right-arrow-big arrow-icon">
          <LongArrow />
        </span>
        <span className="icon-bullseye-arrow font-size-xxxl" />
      </div>
      <div className="flex-dir-column align-items-start-imp">
        <h4 className="title-text-small title-info-text-color">
          {omp('tm_tm_aspirational_roles')}
        </h4>
        <h6 className="title-txt-normal">{aspRoles}</h6>
      </div>
    </>
  );
};

JobRoleWithAspirational.propTypes = {
  jobRoleDto: {
    jobRoleId: PropTypes.string,
    jobRoleName: PropTypes.string
  },
  aspirationalRoles: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      role: PropTypes.string
    })
  )
};

export default JobRoleWithAspirational;
