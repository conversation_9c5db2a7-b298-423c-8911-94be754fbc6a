import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import NoData from './NoData';
import TagsWithExpand from '../TagsWithExpand/TagsWithExpand';

const TitleWithTag = ({
  title,
  showCount,
  data = [],
  variant,
  initialMaxDisplayTags,
  ariaLabel
}) => (
  <div className="flex-dir-column align-items-start-imp">
    <h3 className="section-heading mb-4 title-size-base">
      {title} {showCount && `(${!_.isEmpty(data) ? data.length : '0'})`}
    </h3>
    {_.isEmpty(data) ? (
      <NoData />
    ) : (
      <TagsWithExpand
        tagsToDisplay={data}
        variant={variant}
        initialMaxDisplayTags={initialMaxDisplayTags}
        displayClass="display-tag-candidate"
        expandClass="font-size-l"
        ariaLabelAttr={{
          number: data.length - initialMaxDisplayTags,
          labelName: title,
          sectionName: ariaLabel
        }}
      />
    )}
  </div>
);

TitleWithTag.propTypes = {
  title: PropTypes.string.isRequired,
  showCount: PropTypes.bool.isRequired,
  data: PropTypes.array.isRequired,
  variant: PropTypes.oneOf(['tag', 'card']).isRequired,
  initialMaxDisplayTags: PropTypes.number.isRequired,
  ariaLabel: PropTypes.string
};
export default TitleWithTag;
