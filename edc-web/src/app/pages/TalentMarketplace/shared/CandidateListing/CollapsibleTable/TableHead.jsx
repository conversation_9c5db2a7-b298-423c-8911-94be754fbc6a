import React from 'react';
import PropTypes from 'prop-types';
import { tr } from 'edc-web-sdk/helpers/translations';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { translatr } from 'centralized-design-system/src/Translatr';
import { TableContext } from './table-context';
import Checkbox from 'centralized-design-system/src/Checkbox';

const TableHead = ({
  headers,
  sortKey,
  sortDir,
  onKeyDownHandler,
  selectableTableData,
  showShortlist
}) => {
  const { state, dispatch } = React.useContext(TableContext);
  const { allSelected } = state;

  return (
    <thead>
      <tr>
        <th width="5%">
          <div className="text-left" style={{ minWidth: 0 }}></div>
        </th>
        {headers.map((header, i) => {
          let classes = 'icon';
          let labelClasses = 'label';
          let handleSort = () => {};

          if (header.sortable && header.id === sortKey) {
            classes += ` sorted ${sortDir}`;
            labelClasses = 'sorted-label';
          }

          if (header.onClick) {
            handleSort = () => header.onClick(header, i);
          }

          return (
            <th key={header.id || header.label} width={header.width}>
              <div className={header.align || 'text-left'}>
                {i === 0 && (
                  <Checkbox
                    className="table-checkbox xl-margin-right heading"
                    checked={allSelected}
                    onChange={() =>
                      dispatch({
                        type: 'SELECT_ALL_ROWS',
                        rows: selectableTableData,
                        rowCount: selectableTableData.length
                      })
                    }
                    aria-label={translatr('web.sourcing.candidate-profile', 'SelectAllCandidates')}
                  />
                )}
                {!header.tooltip && (
                  <span className={labelClasses}>{header.children || tr(header.label)}</span>
                )}
                {header.tooltip && (
                  <Tooltip
                    message={header.tooltipMessage}
                    isHtmlIncluded
                    customClass="border-bottom-none"
                    tooltipCardCustomClass="match-score-tooltip"
                  >
                    <span
                      className={labelClasses}
                      aria-label={`${tr(header.label)}. ${header.tooltipMessage.replace(
                        /<[^>]*>/g,
                        ''
                      )}`}
                      role="button"
                      tabIndex="0"
                    >
                      {header.children || tr(header.label)}
                    </span>
                  </Tooltip>
                )}
                {header.sortable && (
                  <i
                    className={`${classes} icon-sort-arrow`}
                    onClick={handleSort}
                    onKeyDown={e => onKeyDownHandler(e, handleSort)}
                    role="button"
                    tabIndex={0}
                    aria-label={translatr('cds.common.main', 'SortLabel')}
                  />
                )}
              </div>
            </th>
          );
        })}
        <th width={showShortlist ? '10%' : '15%'}>
          <div className="text-left justify-center">
            <span className="label">{translatr('web.sourcing.candidate-profile', 'Actions')}</span>
          </div>
        </th>
      </tr>
    </thead>
  );
};

TableHead.propTypes = {
  headers: PropTypes.array.isRequired,
  sortKey: PropTypes.string || null,
  sortDir: PropTypes.string.isRequired,
  onKeyDownHandler: PropTypes.func.isRequired,
  selectableTableData: PropTypes.array.isRequired,
  onSelectedRowsChange: PropTypes.func,
  showShortlist: PropTypes.bool
};

export default TableHead;
