import React from 'react';
import PropTypes from 'prop-types';
import { LinearProgressBar } from 'centralized-design-system/src/Inputs';
import { getLevel } from '@components/MatchComponent/util';
import { translatr } from 'centralized-design-system/src/Translatr';

const MatchScore = ({ matchScore }) => {
  const { progressLabel, progressColor } = getLevel(matchScore);
  const [key, value] = progressLabel;

  return (
    <div className="match-score" aria-hidden="true">
      <span className="supporting-text no-padding title-txt-normal width-100">
        {translatr(key, value, { score: matchScore })}
      </span>
      <LinearProgressBar
        value={matchScore}
        hideLabel={true}
        addedStyles={{ backgroundColor: progressColor }}
      />
    </div>
  );
};

MatchScore.propTypes = {
  matchScore: PropTypes.number
};

export default MatchScore;
