import React, { useState } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import Pagination from 'centralized-design-system/src/Pagination';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import Tooltip from 'centralized-design-system/src/Tooltip';
import CollapsibleTable from './CollapsibleTable/CollapsibleTable';
import NoData from './CandidateProfile/NoData';
import EmptyState from '@pages/Sourcing/components/empty-state/empty-state';
import NameWithAvatar from './NameWithAvatar';
import MatchScore from './MatchScore';
import TableProvider from './CollapsibleTable/table-context';
import { translatr } from 'centralized-design-system/src/Translatr';
import './CandidateListing.scss';
import { getCandidatesListByVacancyBasedOnFilters } from 'edc-web-sdk/requests/sourcing';
import { JobVacancyContext } from '@pages/Sourcing/context/manage-page/job-vacancy-context';
import { EMPTY_STATE_TYPES } from '@pages/Sourcing/types/components/empty-state';
import { JOB_VACANCY_ACTION_TYPES } from '@pages/Sourcing/types/manage-page/context/job-vacancy';
import {
  UPPER_THRESHOLD,
  MIDDLE_THRESHOLD,
  LOWER_THRESHOLD
} from '@components/MatchComponent/util';
import { truncateText } from '@utils/utils';
import { DisplayTag } from 'centralized-design-system/src/Tags';

const CandidateListing = ({
  apiData,
  opportunityId,
  requiredSkills = [],
  showSkillsMatrix,
  opportunityType,
  showShortlist = false,
  fetchDataOnPagination,
  orgUnitColumnLabel,
  orgUnitsMap,
  orgDivisionsMap,
  showMatchingScore = true
}) => {
  if (_.isEmpty(apiData.values) || _.isEmpty(apiData)) return <EmptyState type={opportunityType} />;

  const [pageNumber, setPageNumber] = React.useState('');
  const [tableData, setTableData] = React.useState([]);
  const [sortDir, setSortDir] = React.useState('desc');
  const [sortKey, setSortKey] = React.useState(null);
  const postPerPage = 1;
  const candidatesPerPage = 25;
  const [currentPage, setCurrentPage] = React.useState(0);
  const [isPageDataLoading, setPageDataLoading] = useState(false);
  const [totalElementsForPagination, setTotalElementsForPagination] = React.useState(
    apiData.totalElements ? apiData.totalElements : 0
  );
  const { state, dispatch: jobVacancyDispatch } = React.useContext(JobVacancyContext) || {};

  const getMatchScoreTooltip = () => {
    const tooltipHeading = showShortlist
      ? translatr('web.sourcing.candidate-profile', 'BasedOnUserSkillsAndExperience')
      : translatr('web.sourcing.candidate-profile', 'MatchingBasedOnUsersSkills');

    return ` <ul>
        <li>${tooltipHeading}</li>
        <li>${translatr('web.sourcing.candidate-profile', 'ExcellentWithRange', {
          lowerRange: UPPER_THRESHOLD + 1,
          upperRange: 100
        })}</li>
        <li>${translatr('web.sourcing.candidate-profile', 'GoodWithRange', {
          lowerRange: MIDDLE_THRESHOLD + 1,
          upperRange: UPPER_THRESHOLD
        })}</li>
        <li>${translatr('web.sourcing.candidate-profile', 'FairWithRange', {
          lowerRange: LOWER_THRESHOLD + 1,
          upperRange: MIDDLE_THRESHOLD
        })}</li>
      </ul>`;
  };

  const shortlistColumn = {
    header: translatr('web.sourcing.candidate-profile', 'ApplicationStatus'),
    id: 'shortlist'
  };

  const appendShortlistColumn = (headers, ids, widths) => {
    if (showShortlist && !state.removeCandidateFromList) {
      headers.push(shortlistColumn.header);
      ids.push(shortlistColumn.id);
      return widths;
    }
    return widths;
  };

  const getColumns = () => {
    let columnHeaders = [],
      columnIds = [],
      columnWidth = [],
      columnTooltip = {};

    if (!_.isEmpty(orgUnitColumnLabel) && !showMatchingScore) {
      columnHeaders = ['Name', 'Location', orgUnitColumnLabel];
      columnIds = ['name', 'location', 'orgUnit'];
      columnWidth = appendShortlistColumn(
        columnHeaders,
        columnIds,
        showShortlist ? ['30%', '20%', '20%', '15%'] : ['40%', '20%', '20%']
      );
    }

    if (_.isEmpty(orgUnitColumnLabel) && showMatchingScore) {
      columnHeaders = ['Name', 'Location', 'Relevance'];
      columnIds = ['name', 'location', 'matching'];
      columnTooltip = {
        matching: getMatchScoreTooltip()
      };
      columnWidth = appendShortlistColumn(
        columnHeaders,
        columnIds,
        showShortlist ? ['30%', '20%', '20%', '15%'] : ['40%', '20%', '20%']
      );
    }

    if (!_.isEmpty(orgUnitColumnLabel) && showMatchingScore) {
      columnHeaders = ['Name', 'Location', orgUnitColumnLabel, 'Relevance'];
      columnIds = ['name', 'location', 'orgUnit', 'matching'];
      columnTooltip = {
        matching: getMatchScoreTooltip()
      };
      columnWidth = appendShortlistColumn(
        columnHeaders,
        columnIds,
        showShortlist ? ['30%', '15%', '15%', '20%', '5%'] : ['30%', '15%', '15%', '20%']
      );
    }

    if (_.isEmpty(orgUnitColumnLabel) && !showMatchingScore) {
      columnHeaders = ['Name', 'Location'];
      columnIds = ['name', 'location'];
      columnWidth = appendShortlistColumn(
        columnHeaders,
        columnIds,
        showShortlist ? ['50%', '20%', '10%'] : ['60%', '20%']
      );
    }
    return { columnHeaders, columnIds, columnWidth, columnTooltip };
  };

  React.useEffect(() => {
    setCurrentPage(1);
    const candidateListData = prepareTableData(apiData);
    setTableData(candidateListData);
  }, []);

  const columnWidths = React.useMemo(() => getColumns().columnWidth, []);

  const prepareTableData = candidateListData => {
    const filteredData = candidateListData.values.filter(
      ({ firstName, lastName }) => firstName && lastName
    );
    return filteredData.reduce((acc, curr) => {
      const {
        firstName,
        lastName,
        profileAttribute,
        matchScore,
        location,
        picture,
        name,
        handle,
        organizationUnits,
        shortlisted,
        overallScore
      } = curr;
      const locationName = typeof location === 'string' ? location : location?.locationName;

      const { jobTitle = '' } = profileAttribute;

      const rowData = [
        {
          children: (
            <NameWithAvatar
              firstName={firstName}
              lastName={lastName}
              avatarUrl={picture}
              jobTitle={jobTitle}
              handle={handle}
              sectionColumnWidth={columnWidths}
            />
          ),
          label: name?.toLowerCase(),
          id: 'name',
          sortable: true,
          selectedRowData: curr
        },
        {
          children: (
            <div className="supporting-text no-padding">
              {locationName && locationName.length > 50 ? (
                <Tooltip message={locationName} customClass={'width-300'} ariaLabel={locationName}>
                  {truncateText(locationName, 47, '...')}
                </Tooltip>
              ) : !locationName ? (
                <NoData />
              ) : (
                `${locationName}`
              )}
            </div>
          ),
          label: locationName ? locationName.toLowerCase() : '',
          id: 'location',
          sortable: true
        }
      ];

      if (showMatchingScore) {
        rowData.push({
          children:
            state?.payload?.filters && Object.keys(state.payload.filters).length > 0 ? (
              <Tooltip
                message={translatr('web.sourcing.candidate-profile', 'ExperienceScore', {
                  score: matchScore
                })}
                customClass={'match-score'}
              >
                <div
                  aria-label={`${translatr('web.sourcing.candidate-profile', 'OverallScore', {
                    score: overallScore
                  })} - ${translatr('web.sourcing.candidate-profile', 'ExperienceScore', {
                    score: matchScore
                  })}`}
                  role="button"
                  tabIndex={0}
                  className={'match-score'}
                >
                  <MatchScore matchScore={overallScore} />
                </div>
              </Tooltip>
            ) : (
              <MatchScore matchScore={matchScore} />
            ),
          label:
            state?.payload?.filters && Object.keys(state.payload.filters).length > 0
              ? overallScore
              : matchScore,
          id: 'matching',
          sortable: true
        });
      }
      if (!_.isEmpty(orgUnitColumnLabel)) {
        const orgData =
          !_.isEmpty(orgDivisionsMap) && !_.isEmpty(organizationUnits)
            ? orgDivisionsMap[organizationUnits.find(org => orgDivisionsMap[org])]
            : '';

        const orgUnit = {
          children: (
            <div className="supporting-text no-padding">
              {orgData ? (
                orgData.length > 35 ? (
                  <Tooltip message={orgData} customClass={'width-300'} ariaLabel={orgData}>
                    {truncateText(orgData, 35, '...')}
                  </Tooltip>
                ) : (
                  orgData
                )
              ) : (
                <NoData />
              )}
            </div>
          ),
          label: orgData?.length ? orgData : '',
          id: 'orgUnit',
          sortable: true
        };

        const idx = rowData.findIndex(data => data.id === 'location');
        rowData.splice(idx + 1, 0, orgUnit);
      }
      if (showShortlist && !state.removeCandidateFromList) {
        rowData.push({
          children: renderShortlistStatus(shortlisted, curr.id),
          label: shortlisted,
          id: 'shortlist',
          sortable: true
        });
      }

      return [...acc, rowData];
    }, []);
  };

  const paginate = async resp => {
    let activePage = currentPage;
    if (resp.event === 'next') {
      activePage++;
    } else if (resp.event === 'prev') {
      activePage--;
    } else if (Number.isInteger(resp.event)) {
      activePage = resp.event;
    }

    if (fetchDataOnPagination) {
      setPageDataLoading(true);
      const jobVacancyPayload = {
        ...state.payload,
        limit: candidatesPerPage,
        page: activePage
      };
      const candidateList = await getCandidatesListByVacancyBasedOnFilters(jobVacancyPayload);
      if (
        candidateList &&
        candidateList.pageNumber > 1 &&
        candidateList.values.length === 0 &&
        activePage !== 1
      ) {
        const previousPageCandidates = await getCandidatesListByVacancyBasedOnFilters({
          ...jobVacancyPayload,
          page: activePage - 1
        });
        const candidatePreparedData = prepareTableData(previousPageCandidates);
        setTablePageState(
          candidatePreparedData,
          activePage - 1,
          previousPageCandidates.totalElements
        );
      } else {
        const candidatePreparedData = prepareTableData(candidateList);
        setTablePageState(candidatePreparedData, activePage, candidateList.totalElements);
      }
    } else {
      setCurrentPage(activePage);
    }
  };

  function sortData(resp, position) {
    if (resp.sortable) {
      setSortDir(sortDir === 'desc' ? 'asc' : 'desc');
      setSortKey(resp.id);
      let data = tableData.sort((a, b) => {
        const aLabel = a[position].label;
        const bLabel = b[position].label;
        if (aLabel < bLabel) {
          return sortDir === 'desc' ? -1 : 1;
        }
        if (aLabel > bLabel) {
          return sortDir === 'desc' ? 1 : -1;
        }
        return 0;
      });
      setTableData([...data]);
    }
  }

  function getHeaders() {
    const { columnHeaders, columnIds, columnWidth, columnTooltip } = getColumns();

    return columnHeaders.reduce((acc, curr, index) => {
      const obj = {
        label: translatr('web.sourcing.candidate-profile', curr) || curr,
        id: columnIds[index],
        onClick: sortData,
        tooltip: !!columnTooltip[columnIds[index]],
        tooltipMessage: columnTooltip[columnIds[index]],
        sortable: true,
        width: columnWidth[index]
      };

      acc.push(obj);
      return acc;
    }, []);
  }

  const startIndex = (currentPage - 1) * candidatesPerPage;
  const endIndex = currentPage * candidatesPerPage;

  const updateShortlistStatus = (profileIds, status) => {
    const updatedData = tableData.map(subArray => {
      return subArray.map(item => {
        if (item.selectedRowData && profileIds.includes(item.selectedRowData.id)) {
          return { ...item, selectedRowData: { ...item.selectedRowData, shortlisted: status } };
        }
        if (item.id === 'shortlist' && profileIds.includes(subArray[0].selectedRowData.id)) {
          return {
            ...item,
            label: status,
            children: renderShortlistStatus(status, subArray[0].selectedRowData.id)
          };
        }
        return item;
      });
    });
    setTableData(updatedData);
  };

  const renderShortlistStatus = (shortlisted, candidateId) => {
    return (
      <div className="flex align-left justify-center status-tags">
        {shortlisted ? (
          <DisplayTag
            id={`shortlistStatus-${candidateId}`}
            tagName={translatr('web.sourcing.candidate-profile', 'Shortlisted')}
            displayClass="tag-display"
          />
        ) : (
          <NoData />
        )}
      </div>
    );
  };

  const deleteCandidateFromTable = async profileIds => {
    let isLatestData = false;
    setTableData(pre => {
      const updatedTableData = pre.filter(
        subArray =>
          !subArray.some(
            item => item.selectedRowData && profileIds.includes(item.selectedRowData.id)
          )
      );

      if (updatedTableData.length === 0) {
        isLatestData = true;
      }

      return updatedTableData;
    });

    if (isLatestData) {
      const page = currentPage - 1 >= 1 ? currentPage - 1 : 1;
      const jobVacancyPayload = {
        limit: candidatesPerPage,
        page,
        ...state.payload
      };
      setPageDataLoading(true);
      const previousPageCandidates = await getCandidatesListByVacancyBasedOnFilters({
        ...jobVacancyPayload,
        page
      });
      const candidatePreparedData = prepareTableData(previousPageCandidates);
      setTablePageState(candidatePreparedData, page, previousPageCandidates.totalElements);
    }
  };

  const setTablePageState = (candidatePreparedData, activePage, totalElements) => {
    setTableData(candidatePreparedData);
    setPageDataLoading(false);
    setCurrentPage(activePage);
    setTotalElementsForPagination(totalElements);
    if (state.removeCandidateFromList)
      jobVacancyDispatch({
        type: JOB_VACANCY_ACTION_TYPES.SET_SHORTLISTING_TAB_COUNT,
        shortlistTabCount: totalElements
      });
  };

  return (
    <TableProvider
      initState={{
        requiredSkills,
        showSkillsMatrix,
        orgUnitsMap
      }}
    >
      {_.isEmpty(tableData) && !isPageDataLoading ? (
        <EmptyState
          type={
            showShortlist && state.removeCandidateFromList
              ? EMPTY_STATE_TYPES.SHORTLISTING
              : opportunityType
          }
        />
      ) : (
        <>
          <CollapsibleTable
            className="table-candidate"
            tableClass="table-candidate-header"
            sortDir={sortDir}
            headers={getHeaders()}
            rows={fetchDataOnPagination ? tableData : tableData.slice(startIndex, endIndex)}
            fullTableData={tableData}
            currentPage={currentPage}
            sortKey={sortKey}
            opportunityId={opportunityId}
            opportunityType={opportunityType}
            showShortlist={showShortlist}
            isPageDataLoading={isPageDataLoading}
            updateShortlistStatus={updateShortlistStatus}
            deleteCandidateFromTable={deleteCandidateFromTable}
          />
          <div className="pagination-card">
            <Pagination
              postPerPage={postPerPage}
              totalPosts={
                fetchDataOnPagination
                  ? Math.ceil(totalElementsForPagination / candidatesPerPage)
                  : Math.ceil(tableData.length / candidatesPerPage)
              }
              paginate={paginate}
              activePage={currentPage}
              iconType={true}
            />
            {Math.floor(tableData?.length / candidatesPerPage) > 5 && (
              <TextField
                type="number"
                title={translatr('web.sourcing.candidate-profile', 'GoTo')}
                defaultValue={pageNumber}
                setValue={setPageNumber}
                onChangeCB={e => {
                  if (!_.isEmpty(e)) setCurrentPage(e);
                }}
              />
            )}
          </div>
        </>
      )}
    </TableProvider>
  );
};

CandidateListing.propTypes = {
  apiData: PropTypes.object,
  opportunityId: PropTypes.string,
  requiredSkills: PropTypes.array,
  showSkillsMatrix: PropTypes.bool,
  opportunityType: PropTypes.string,
  showShortlist: PropTypes.bool,
  fetchDataOnPagination: PropTypes.bool,
  orgUnitColumnLabel: PropTypes.string,
  orgDivisionsMap: PropTypes.object,
  orgUnitsMap: PropTypes.shape({
    orgUserAssociation: PropTypes.array,
    orgCareerPrefAssociation: PropTypes.array,
    orgUserCareerPrefAssociation: PropTypes.array,
    orgTypes: PropTypes.object,
    orgUnitsInFilter: PropTypes.array
  }),
  showMatchingScore: PropTypes.bool
};

export default CandidateListing;
