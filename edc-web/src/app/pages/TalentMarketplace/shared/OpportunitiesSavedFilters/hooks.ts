import { useState, useEffect, useCallback } from 'react';
import { getOpportunitySavedSearches } from 'edc-web-sdk/requests/opportunityAlerts';
import { OpportunitySavedSearch } from './types';
import {
  TM_PROJECT_FILTER_BUCKET_NAME,
  TM_VACANCY_FILTER_BUCKET_NAME
} from '../filters/Filters.constants';
import { useDispatch, useSelector } from 'react-redux';
import { FilterBucketName, saveSavedSearches } from '@actions/savedSearchesActions';

const LIMITS = {
  [TM_VACANCY_FILTER_BUCKET_NAME]: 3,
  [TM_PROJECT_FILTER_BUCKET_NAME]: 5 //example (to be used for project filter in future implementation)
};

export const useSavedSearches = (bucketType: FilterBucketName, isFetchEnabled: boolean = true) => {
  const dispatch = useDispatch();
  const [isSavedSearchesLoading, setIsSavedSearchesLoading] = useState(false);
  const [error, setError] = useState(false);
  const savedSearches: OpportunitySavedSearch = useSelector((state: any) =>
    state?.savedSearches.get(bucketType)
  );
  const loaded = !!Object.keys(savedSearches || {}).length;

  const fetchSavedSearches = useCallback(() => {
    setIsSavedSearchesLoading(true);
    getOpportunitySavedSearches()
      .then((res: OpportunitySavedSearch) => {
        dispatch(saveSavedSearches(bucketType, res));
      })
      .catch((err: any) => {
        setError(true);
        console.error('Error fetching saved searches', err);
      })
      .finally(() => {
        setIsSavedSearchesLoading(false);
      });
  }, [bucketType, dispatch]);

  useEffect(() => {
    if (isFetchEnabled && !loaded) {
      fetchSavedSearches();
    }
  }, [loaded, fetchSavedSearches, isFetchEnabled]);

  const refreshSavedSearches = () => {
    if (isFetchEnabled) {
      fetchSavedSearches();
    }
  };

  return {
    savedSearches,
    isSavedSearchesLoading,
    isLimitReached: savedSearches?.totalElements >= LIMITS[bucketType],
    limit: LIMITS[bucketType],
    savedSearchLoaded: loaded,
    error,
    refreshSavedSearches
  };
};

export const useAlertPermission = (filterBucket: FilterBucketName) => {
  let allowCreateAlert = false;

  const notificationConfig = useSelector((state: any) =>
    state.currentUser.get('notificationConfig')
  );

  switch (filterBucket) {
    case TM_VACANCY_FILTER_BUCKET_NAME:
      const newOpportunitiesMatchMyAlertConfig =
        notificationConfig
          ?.get('org_config')
          ?.get('options')
          ?.get('new_opportunities_match_my_alert') || null;

      const userConfigurable =
        newOpportunitiesMatchMyAlertConfig?.get('user_configurable') || false;
      const weeklyDigest =
        newOpportunitiesMatchMyAlertConfig
          ?.get('email')
          ?.some((item: any) => item.get('id') === 'digest_weekly' && item.get('selected')) ||
        false;

      allowCreateAlert = userConfigurable || weeklyDigest;
      break;
    case TM_PROJECT_FILTER_BUCKET_NAME:
      /* example for project filter bucket - currently inactive*/
      allowCreateAlert = false;
      break;
    default:
      allowCreateAlert = false;
      break;
  }

  return allowCreateAlert;
};
