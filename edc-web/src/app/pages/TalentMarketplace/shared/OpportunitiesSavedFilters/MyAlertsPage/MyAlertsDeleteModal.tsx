import { translatr } from 'centralized-design-system/src/Translatr';
import ConfirmationModal from '@components/modals/ConfirmationModalV2';
import Modal from 'centralized-design-system/src/Modals';
import { useEscapeKey } from '@pages/Sourcing/hooks/useEscapeKey';
import { useDispatch } from 'react-redux';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { deleteOpportunitySavedSearchById } from 'edc-web-sdk/requests/opportunityAlerts';
import { OpportunitySavedFilter } from '../types';

interface MyAlertsDeleteModalProps {
  selectedAlert: OpportunitySavedFilter;
  callback: () => void;
  closeModal: () => void;
}

const MyAlertsDeleteModal: React.FC<MyAlertsDeleteModalProps> = ({
  selectedAlert,
  callback,
  closeModal
}) => {
  useEscapeKey(() => closeModal());
  const dispatch = useDispatch();
  const filterName = selectedAlert?.filterName ? selectedAlert.filterName.replace(/[<>"'&]/g, '') : '';

  async function handleDeleteAlert() {
    try {
      if (!selectedAlert?.id) {
        throw new Error('Alert ID is not available');
      }

      await deleteOpportunitySavedSearchById(selectedAlert.id);
      dispatch(
        openSnackBar(
          translatr('web.talentmarketplace.main', 'AlertDeleted', {
            name: `"${filterName}"`
          })
        )
      );
      callback();
    } catch (e) {
      console.error('Error deleting alert:', e);
      dispatch(
        openSnackBar(translatr('web.talentmarketplace.main', 'SomethingWentWrong'), 'error')
      );
    }
  }

  return (
    // @ts-ignore
    <Modal size="small">
      <ConfirmationModal
        message={translatr('web.talentmarketplace.main', 'DeleteAlertModalDescription', {
          name: `"${filterName}"`
        })}
        confirmBtnTitle={translatr('web.common.main', 'Delete')}
        callback={handleDeleteAlert}
        cancelClick={() => closeModal()}
        closeModal={() => closeModal()}
        title={translatr('web.talentmarketplace.main', 'DeleteAlertModalTitle')}
        isNegativeValue={true}
      />
    </Modal>
  );
};

export default MyAlertsDeleteModal;
