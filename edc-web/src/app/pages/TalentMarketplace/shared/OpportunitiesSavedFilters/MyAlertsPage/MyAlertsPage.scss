.my-alerts__main {
  .my-alerts__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ed-spacing-base);
    .my-alerts__heading {
      font-size: var(--ed-font-size-lg) !important;
    }
  }

  .saved-searches-list__item {
    border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);
    margin-bottom: var(--ed-spacing-xs);
    &:last-child {
      border-bottom: none;
    }
    .saved-searches-list__item__filter-container {
      display: flex;
      gap: var(--ed-spacing-xs);

      .saved-searches-list__item__filter-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          font-size: var(--ed-font-size-sm);
          display: inline;
        }
        li::after {
          content: '|';
          padding: 0 var(--ed-spacing-2xs);
          color: var(--ed-neutral-6);
        }
        li:last-child::after {
          content: '';
        }
      }
    }
  }
}
