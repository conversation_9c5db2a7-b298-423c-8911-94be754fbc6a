export enum SearchFilterOpportunityType {
  JOB = 'job',
  ROLE = 'role',
  PROJECT = 'project',
  MENTOR = 'mentor'
}
export interface OpportunitySavedSearch {
  values: OpportunitySavedFilter[];
  totalElements: number;
}

export interface OpportunitySavedFilter {
  id: string;
  filterName: string;
  filterType: string;
  filterCriteria: Array<Record<string, unknown>>;
  filterCriteriaEmailDigest: Array<Record<string, unknown>>;
  permissions: {
    isPublic: boolean;
    users: string[];
  };
  filterOwner: string;
  filterStatus: string;
  notificationFrequency: string;
}

export interface FilterOption {
  name: string;
  value: string;
}

export interface FilterData {
  keyword?: string;
  filters?: Record<string, FilterOption[]>;
}
