import React, { useMemo, useState } from 'react';
import BellNotification from '../../../../icons/BellNotification';
import { Button } from 'centralized-design-system/src/Buttons';
import { useDispatch } from 'react-redux';
import { openCreateOpportunityAlertModal } from '@actions/modalActions';
import { translatr } from 'centralized-design-system/src/Translatr';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { useSavedSearches, useAlertPermission } from './hooks';

interface CreateAlertProps {
  filtersKeys: any;
  filtersState: any;
  associationId: string;
  buttonVariant: 'bell' | 'standard';
}

const CreateAlert: React.FC<CreateAlertProps> = ({
  filtersKeys,
  filtersState,
  associationId,
  buttonVariant = 'bell'
}) => {
  const dispatch = useDispatch();
  const filterBucket = filtersState?.bucketName;
  const allowCreateAlert = useAlertPermission(filterBucket);

  if (!allowCreateAlert) {
    return null;
  }
  const { isSavedSearchesLoading, isLimitReached, limit, refreshSavedSearches } = useSavedSearches(
    filterBucket
  );

  const translatedTagLabels = useMemo(() => {
    const groupedFilters = filtersKeys.map((filterName: string) => {
      return filtersState.filters[associationId][filterName]
        .map((item: any) => {
          return Array.isArray(item.name) ? translatr(...item.name) : item.name;
        })
        .join(', ');
    });

    return groupedFilters;
  }, [filtersKeys, filtersState, associationId]);

  const handleCreateAlertClick = () => {
    dispatch(
      openCreateOpportunityAlertModal({
        filtersList: translatedTagLabels,
        filtersState,
        onSave: () => refreshSavedSearches()
      })
    );
  };

  if (isSavedSearchesLoading) {
    return <Skeleton width={'80px'} height="26px" />;
  }

  return (
    <Tooltip
      pos="top"
      hide={!isLimitReached}
      message={
        isLimitReached
          ? translatr('web.talentmarketplace.main', 'AlertLimitReached', { limit: limit })
          : ''
      }
      isTranslated={true}
    >
      {buttonVariant === 'bell' && (
        <Button
          color="primary"
          variant="borderless"
          size="medium"
          padding="small"
          disabled={isLimitReached}
          onClick={handleCreateAlertClick}
        >
          <BellNotification disabled={isLimitReached} />
          {translatr('web.talentmarketplace.main', 'CreateAlertAction')}
        </Button>
      )}
      {buttonVariant === 'standard' && (
        <Button
          color="primary"
          variant="ghost"
          disabled={isLimitReached}
          onClick={handleCreateAlertClick}
        >
          {translatr('web.talentmarketplace.main', 'CreateAlertAction')}
        </Button>
      )}
    </Tooltip>
  );
};
export default CreateAlert;
