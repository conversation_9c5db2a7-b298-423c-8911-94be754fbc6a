import React, { useState } from 'react';
import { arrayOf, string } from 'prop-types';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { translatr } from 'centralized-design-system/src/Translatr';
import { Button } from 'centralized-design-system/src/Buttons';
import Tooltip from 'centralized-design-system/src/Tooltip';
import Popover from '@mui/material/Popover';
import { IconButton } from '@mui/material';
import CloseIcon from '../../../icons/fromMUI/Clear';
import './SidebarLocationsMeta.scss';

const ROOT_CLASS = 'locations__sidebar-meta';
const LOCATIONS_POPOVER_LIST = 'project_locations-popover__list';

const SidebarLocationsMeta = ({ icon, locationsData = [], title }) => {
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const smallScreen = useWindowSize()?.width < 500 ? true : false;
  const [firstElement = [], ...remainingElements] = locationsData;
  const moreLabel = `+ ${translatr('web.talentmarketplace.main', 'ViewRemainingMore', {
    remaining: remainingElements.length
  })}`;

  const onMoreClick = event => {
    setPopoverAnchorEl(event.currentTarget);
  };
  const onClose = () => {
    setPopoverAnchorEl(null);
  };
  return (
    <div className={ROOT_CLASS}>
      <i className={`icon-${icon}`} />
      <div>
        {title && <span className={`${ROOT_CLASS}--title`}>{title}</span>}
        <div id="project-locations-label" className="sr-only">
          {title}
        </div>
        <div className={`${ROOT_CLASS}--content`}>
          <span id="location-item-first" className="sr-only">
            {title} {firstElement.join(' parent of ')}
          </span>
          <Tooltip
            arialabelledby="location-item-first"
            message={firstElement.join(' > ')}
            tabIndex={0}
          >
            {firstElement[firstElement.length - 1]}
          </Tooltip>
          {remainingElements.length > 0 && (
            <>
              <Button
                id="project-locations-more-button"
                variant="borderless"
                color="secondary"
                padding="xsmall"
                size="medium"
                data-testid="omp-project-locations"
                aria-label={moreLabel.replace('+ ', '')}
                aria-controls={LOCATIONS_POPOVER_LIST}
                aria-expanded={Boolean(popoverAnchorEl)}
                aria-labelledby="project-locations-label project-locations-more-button"
                aria-haspopup="true"
                onClick={onMoreClick}
              >
                {moreLabel}
              </Button>
            </>
          )}
          <Popover
            id={`project_locations-popover`}
            open={Boolean(popoverAnchorEl)}
            anchorEl={popoverAnchorEl}
            onClose={onClose}
            role="dialog"
            anchorOrigin={{
              vertical: 'center',
              horizontal: smallScreen ? 'center' : 'right'
            }}
            transformOrigin={{
              vertical: 'center',
              horizontal: 'left'
            }}
            disableScrollLock
            slotProps={{
              paper: {
                elevation: 2,
                role: 'dialog',
                'aria-modal': 'true',
                style: {
                  overflow: 'visible',
                  width: smallScreen ? '100%' : 'unset'
                }
              }
            }}
          >
            <div className="project_locations-popover__container ed-ui">
              <div className="project_locations-popover__header">
                <h2 className="project_locations-popover__header-label">{title}</h2>
                <IconButton
                  data-testid="close-popover-button"
                  aria-label="close"
                  onClick={onClose}
                  size="small"
                  className="project_locations-popover__close-button"
                  disableFocusRipple
                  sx={{
                    '&:focus': {
                      outlineColor: 'var(--ed-state-focus-color)',
                      outlineOffset: 'none',
                      outlineWidth: 'var(--ed-border-size-sm)',
                      outlineStyle: 'auto'
                    }
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </div>
              <ul
                className="project_locations-popover__list"
                id={LOCATIONS_POPOVER_LIST}
                data-testid={LOCATIONS_POPOVER_LIST}
              >
                {remainingElements?.map((item, index) => (
                  <li key={item.join('-')}>
                    <span id={`location-item-${index}`} className="sr-only">
                      {item.join(' parent of ')}
                    </span>
                    <Tooltip
                      arialabelledby={`location-item-${index}`}
                      message={item.join(' > ')}
                      tabIndex={0}
                    >
                      {item[item.length - 1]}
                    </Tooltip>
                  </li>
                ))}
              </ul>
            </div>
          </Popover>
        </div>
      </div>
    </div>
  );
};

SidebarLocationsMeta.propTypes = {
  icon: string.isRequired,
  locationsData: arrayOf(arrayOf(string)),
  title: string
};

export default SidebarLocationsMeta;
