import React from 'react';
import { Routes, Route } from 'react-router-dom';
import * as Pages from '../app/pages';
import * as AppPages from '../app/containers/AppPages';
import { Translatr } from 'centralized-design-system/src/Translatr';
import PublicContainer from '../app/containers/PublicContainer';

export default function PublicOnlyRoutes() {
  return (
    <PublicContainer>
      <Routes>
        <Route
          path="/saml/auth/:uuid"
          key="/saml/auth/:uuid"
          element={
            <Translatr apps={['web.common.main']}>
              <AppPages.SamlAuthHandlerContainer />
            </Translatr>
          }
        />
        <Route path="/access-denied" element={<AppPages.AccessDenied />} />
        <Route path="/forgot_password" element={<AppPages.ForgotPasswordContainer />}>
          <Route
            index
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <AppPages.SendLinkContainer />
              </Translatr>
            }
          />
          <Route
            path="sent"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <AppPages.SentLinkContainer />
              </Translatr>
            }
          />
          <Route
            path="confirm/:token"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <AppPages.ConfirmPasswordContainer />
              </Translatr>
            }
          />
          <Route
            path="changed/:token"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <AppPages.ChangedPasswordContainer />
              </Translatr>
            }
          />
          <Route
            path="invalid"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <AppPages.InvalidPasswordContainer />
              </Translatr>
            }
          />
        </Route>
        <Route
          path="/access_denied"
          element={
            <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
              <AppPages.LoginSignupPage />
            </Translatr>
          }
        />
        <Route path="/user" element={<Pages.LoginSignupContainer />}>
          <Route
            path="login"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <Pages.LoginLayout />
              </Translatr>
            }
          />
          <Route
            path="signup"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <Pages.SignUpLayout />
              </Translatr>
            }
          />
          <Route
            path="pre_register"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <Pages.PreRegistrationForm />
              </Translatr>
            }
          />
          <Route
            path="micro_org_login"
            element={
              <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                <Pages.MicroOrgSsoSection />
              </Translatr>
            }
          />
        </Route>
        <Route
          path="/mobile-insights/:id"
          element={
            <Translatr apps={['web.common.main', 'cds.common.main']}>
              <AppPages.ScormContainer />
            </Translatr>
          }
        />
      </Routes>
    </PublicContainer>
  );
}
