import { OpportunitySavedSearch } from '@pages/TalentMarketplace/shared/OpportunitiesSavedFilters/types';
import * as actionTypes from '../constants/actionTypes';
import {
  TM_PROJECT_FILTER_BUCKET_NAME,
  TM_VACANCY_FILTER_BUCKET_NAME
} from '@pages/TalentMarketplace/shared/filters/Filters.constants';

export type FilterBucketName = typeof TM_VACANCY_FILTER_BUCKET_NAME | typeof TM_PROJECT_FILTER_BUCKET_NAME;

export type SavedSearchesActions = {
  type: string;
  bucketType: FilterBucketName;
  data: OpportunitySavedSearch;
};

export const saveSavedSearches = (
  bucketType: FilterBucketName,
  data: OpportunitySavedSearch
): SavedSearchesActions => ({
  type: actionTypes.SAVE_SAVED_SEARCHES,
  bucketType,
  data
});
