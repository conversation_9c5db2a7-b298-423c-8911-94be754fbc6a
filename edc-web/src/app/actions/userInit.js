import { setToken, JWT } from 'edc-web-sdk/requests/csrfToken';
import { Permissions } from '../utils/checkPermissions';
import {
  RECEIVE_INIT_USER_INFO,
  ERROR_INIT_USER,
  RECEIVE_INIT_ONBOARDING_STATE,
  SET_CURRENT_USER_INFO
} from '../constants/actionTypes';
import {
  requestUserIsGroupLeader,
  isLocalStorageSupported,
  isLocationContentURL,
  loadGuideMe,
  getSpecificUserInfo
} from './currentUserActions';
import { getOnboardingState, getUserJobRole } from 'edc-web-sdk/requests/users.v2';
import { getAccessibleCandidates, isDefaultSourceEnabled } from 'edc-web-sdk/requests/search';
import { setTokensInOnboarding } from '@utils/utils';
import { getConfigsAfterAuthorization } from './configActions';
import { LAUNCHDARKLY_ORG } from 'edc-web-sdk/config/envConstants';
import checkingUserInactivity from '../utils/checkingUserInactivity';
import LD from '../containers/LDStore';
import { checkMfaFlow } from '../utils';
import {
  AFTER_LOGIN_CONTENT_URL,
  AFTER_ONBOARDING_URL,
  LOGIN_WEB_SESSION_TIMEOUT
} from '../constants/localStorageConstants';
import { isConfigServiceEnabled } from '../../FeatureManagementClient';
import { TALENT_MARKETPLACE_ENABLED } from 'opportunity-marketplace/util';
import { getOmpConfigService } from '@actions/configServiceActions';

export default function initUserDataAsync(user, teamConfig, navigate, samlData) {
  const { 'app.config.okta.enabled': oktaEnabled = false } = teamConfig;
  const WEB_SESSION_TIMEOUT = localStorage.getItem(LOGIN_WEB_SESSION_TIMEOUT);
  if (!user || !user.csrfToken) {
    const DEEPLINK_URL = window.location.search
      ? window.location.pathname + window.location.search
      : window.location.pathname;

    if (isLocalStorageSupported() && isLocationContentURL()) {
      localStorage.setItem(AFTER_LOGIN_CONTENT_URL, DEEPLINK_URL);
      localStorage.setItem(AFTER_ONBOARDING_URL, DEEPLINK_URL);
    }
  }

  return async dispatch => {
    try {
      if (!user || !user.csrfToken) return;
      setToken(user.csrfToken);
      setTokensInOnboarding(user.csrfToken, user.jwtToken);
      Permissions.enabled = user.permissions;
      if ((WEB_SESSION_TIMEOUT && WEB_SESSION_TIMEOUT > Date.now()) || !WEB_SESSION_TIMEOUT) {
        /**
         * consume WEB_SESSION_TIMEOUT variable value from local storage if it exists.
         * Otherwise, use the value from sign_in.json API response to get the web session timeout value.
         **/
        JWT.token = user.jwtToken;
        let webSessionTimeout =
          user.organization?.configs && user.organization['web_session_timeout'];
        webSessionTimeout = webSessionTimeout ? webSessionTimeout * 60 * 1000 + Date.now() : 0;
        localStorage.setItem(LOGIN_WEB_SESSION_TIMEOUT, webSessionTimeout);
      } else if (WEB_SESSION_TIMEOUT === '0') {
        JWT.token = user.jwtToken;
      } else if (WEB_SESSION_TIMEOUT) {
        localStorage.removeItem(LOGIN_WEB_SESSION_TIMEOUT);
      }
      if (user.consumer_redirect_uri) {
        window.open(user.consumer_redirect_uri, '_self');
      }

      setInactiveWebSession(teamConfig);
      const configsAfterAuthorization = dispatch(getConfigsAfterAuthorization());
      dispatch(loadAfterAuthorization(configsAfterAuthorization));
      if (!user.consumer_redirect_uri) {
        if (user.onboardingCompleted) {
          dispatch(
            loginOnboardedUser(user, oktaEnabled, user?.passwordChangeable, navigate, samlData)
          );
        } else {
          dispatch(loginUnOnboardedUser(user, oktaEnabled, user?.passwordChangeable, navigate));
        }
      }
      if (isConfigServiceEnabled && TALENT_MARKETPLACE_ENABLED) {
        dispatch(getOmpConfigService());
      }
      await resetLdOnLogin(user);
      const personalizedSearchValue = LD.personalizeOpenContentsLD();
      getAccessibleCandidates({ personalize_by: personalizedSearchValue });
      isDefaultSourceEnabled();

      const isCareerGrowth = teamConfig?.hr_data_service_enablement;
      const userProfile = user.profile;
      const orgLang = window.__edOrgData?.configs?.find?.(a => a.name === 'DefaultOrgLanguage')
        ?.value;
      const currentUserLang = userProfile?.language || orgLang || 'en';

      getUserJobRole({ career_growth: isCareerGrowth, language: currentUserLang })
        .then(data => {
          const userData = { jobFamily: data };
          dispatch({
            type: SET_CURRENT_USER_INFO,
            userData
          });
        })
        .catch(({ err, resp }) => {
          console.error(`Error in currentUserActions.getUserJobRole: ${err}`);
          if (resp?.statusCode === 422) {
            // No Job Role record for user.
            const userData = { jobFamily: null };
            dispatch({
              type: SET_CURRENT_USER_INFO,
              userData
            });
          }
        });
    } catch (error) {
      console.error('ERROR!!! Error in userInit.initUserDataAsync.fnc : ', error.stack);
      dispatch({
        type: ERROR_INIT_USER,
        error
      });
    }
  };
}

export function loginOnboardedUser(user, oktaEnabled, passwordChangeable, navigate, samlData) {
  return dispatch => {
    user &&
      dispatch({
        type: RECEIVE_INIT_USER_INFO,
        user
      });
    dispatch(
      getSpecificUserInfo(['writableChannels', 'roles', 'rolesDefaultNames', 'followedTeams'])
    );
    dispatch(requestUserIsGroupLeader());
    // Check for SAML request in state and redirect if present
    if (samlData) {
      dispatch(redirectToSamlUrl(samlData, navigate));
    } else {
      dispatch(redirectToDeeplinkUrl(oktaEnabled, passwordChangeable, navigate));
    }
  };
}

export function loginUnOnboardedUser(user, oktaEnabled, passwordChangeable, navigate) {
  return async dispatch => {
    const onboardingState = await getOnboardingState();
    dispatch({
      type: RECEIVE_INIT_ONBOARDING_STATE,
      onboardingState
    });
    user &&
      dispatch({
        type: RECEIVE_INIT_USER_INFO,
        user
      });
    const isUserMfaNeeded = checkMfaFlow(navigate, oktaEnabled, passwordChangeable);
    if (!isUserMfaNeeded) {
      navigate('/onboarding');
    }
  };
}

export function redirectToDeeplinkUrl(oktaEnabled, passwordChangeable, navigate) {
  return async () => {
    const isUserMfaNeeded = checkMfaFlow(navigate, oktaEnabled, passwordChangeable);

    if (!isUserMfaNeeded) {
      if (localStorage.getItem(AFTER_LOGIN_CONTENT_URL)?.indexOf('/admin') >= 0) {
        window.location.href = localStorage.getItem(AFTER_LOGIN_CONTENT_URL);
      } else {
        navigate(localStorage.getItem(AFTER_LOGIN_CONTENT_URL) || '/');
      }
    }
  };
}

export function redirectToSamlUrl(samlData, navigate) {
  return async () => {
    if (samlData?.uuid && samlData?.samlRequest && samlData?.relayState) {
      navigate(
        `/saml/auth/${samlData.uuid}?SAMLRequest=${encodeURIComponent(samlData.samlRequest)}&RelayState=${encodeURIComponent(samlData.relayState)}`
      );
      } else {
        console.error('Invalid SAML data provided for redirect');
      } };
}

function loadAfterAuthorization(orgConfig) {
  return async () => {
    const configs = await orgConfig;
    if (configs) {
      loadGuideMe(configs);
    }
  };
}

function resetLdOnLogin(user) {
  if (!isConfigServiceEnabled) {
    return new Promise(async resolve => {
      const anonClient = {
        key: user.id,
        email: user.email,
        custom: {
          org: LAUNCHDARKLY_ORG || document.location.hostname.split('.')[0]
        }
      };

      await window.ldclient.identify(anonClient);

      resolve(true);
    });
  } else {
    return new Promise(resolve => {
      window.ldclient.initializeAuthorized();
      window.ldclient.on('secure-ready', () => {
        resolve(true);
      });
    });
  }
}

function setInactiveWebSession(teamConfig) {
  const {
    inactive_web_session_timeout: inactivityTimeout,
    single_logout_url: singleLogoutUrl,
    DefaultOrgLanguage: defaultOrgLanguage,
    OktaDefaultApplication: { okta_domain: oktaDomain = '' } = {},
    'app.config.okta.enabled': oktaEnabled = false,
    auth8_details: auth8Details = {}
  } = teamConfig;
  if (inactivityTimeout) {
    const oktaPayload = {
      oktaUrl: oktaDomain,
      oktaEnabled: oktaEnabled
    };
    checkingUserInactivity({
      timeout: inactivityTimeout * 60 * 1000,
      signOutLink: '',
      oktaPayload,
      singleLogoutUrl,
      defaultOrgLanguage,
      currentUserId: '',
      pollerFrequency: auth8Details.poller_frequency
    });
  }
}
