import find from 'lodash/find';
import { JWT } from 'edc-web-sdk/requests/csrfToken';
import { UNAUTHORIZED_ULR } from 'edc-web-sdk/requests/validationConstants';
import { NODE_ENV } from 'edc-web-sdk/config/envConstants';
import { getAccessibleCandidates, isDefaultSourceEnabled } from 'edc-web-sdk/requests/search';

import { initUserData, requestUserIsGroupLeader, loadGuideMe } from './currentUserActions';
import { saveMappedOrganizations } from './groupActions';
import { getSubAdminTeams } from './teamActivityAction';
import { initOnboardingStateSettings } from './onboardingActions';
import { getOrgInfo, updateTranslation } from './teamActions';
import { getConfigsAfterAuthorization } from './configActions';
import LD from '../containers/LDStore';
import { redirectToDeeplinkUrl } from './userInit';
import { checkMfaFlow } from '../utils';
import listMenu from '../utils/orderMenu';
import { Permissions } from '../utils/checkPermissions';
import { deleteCacheKeys, registerServiceWorker } from '@utils/serviceWorkerHelpers';
import {
  AFTER_LOGIN_CONTENT_URL,
  EDCAST_EMAIL,
  EDCAST_ID,
  IS_ONBOARDING_COMPLETED,
  ORG_SAML_ID
} from '../constants/localStorageConstants';
import { constructSubOrgData } from '@utils/subOrg';
import { USER_INFO_UPDATED } from '../constants/actionTypes';
import {
  isConfigServiceEnabled,
  initializeFeatureManagementClientSecure
} from '../../FeatureManagementClient';
import { saveThemeId, saveGroupThemeName } from './themeActions';
import { TALENT_MARKETPLACE_ENABLED } from 'opportunity-marketplace/util';
import { getOmpConfigService } from '@actions/configServiceActions';

const OPEN_PAGE = [
  '/unsubscribe-emails',
  '/access_denied',
  '/user/login',
  '/user/signup',
  '/user/pre_register',
  '/access-denied'
];

export function initApp(navigate) {
  return async dispatch => {
    const isOktaRedirect =
      window.location.search.includes('code=') || window.location.search.includes('error=');
    const isSmartSearchPage = ['/smartsearch', '/smartsearch/card'].includes(
      window.location.pathname
    );
    let orgInfo = dispatch(getOrgInfo());
    if (!!window.__edOrgData) {
      orgInfo = window.__edOrgData;
    } else {
      orgInfo = await orgInfo;
    }

    if (!orgInfo.languageDetails?.length) {
      orgInfo.languageDetails = [...getDetailsFromLanguages(orgInfo.languages || {})];
    }
    (orgInfo.languageDetails || [])
      .sort((a, b) => a.label.localeCompare(b.label))
      .forEach(orgLang => {
        const { label, native_name } = orgLang;
        const theName = (label.split('(')[0] || '').trim();
        const displayName =
          native_name && native_name.length > 0 ? `${theName} (${native_name})` : label;
        orgLang['displayName'] = displayName;
      });
    const user = await dispatch(initUserData(orgInfo, navigate));

    if (orgInfo && user) {
      // This updates the Home link to go from / => /feed or /home
      orgInfo.OrgConfig?.web?.topMenu && listMenu(orgInfo.OrgConfig.web.topMenu);

      // replace circle info in orgInfo
      const isMultiOrg = find(orgInfo.configs, { name: 'multi-org' });
      // This is seemingly never run in prod
      if (isMultiOrg && user.circle_info) {
        constructSubOrgData(orgInfo, user, dispatch);
      }
    }

    dispatch(saveThemeId(LD.themeName()));

    if (user && user.brandingTheme) {
      dispatch(saveGroupThemeName(user.brandingTheme.name));
    }

    if (isConfigServiceEnabled && JWT.token) {
      initializeFeatureManagementClientSecure();
      if (TALENT_MARKETPLACE_ENABLED) {
        dispatch(getOmpConfigService());
      }
    }
    // end of circle info replacement

    dispatch({ type: USER_INFO_UPDATED });

    isSmartSearchPage
      ? await dispatch(initSmartSrchAndSkillDir(orgInfo))
      : dispatch(initSmartSrchAndSkillDir(orgInfo));
    clearLocalStorage();

    if (!user && !UNAUTHORIZED_ULR.test(window.location.pathname)) {
      // Force SSO - EP-12361
      if (orgInfo) {
        const forceSSO = find(orgInfo.configs, { name: 'force_sso' });
        const forceSSOEnabled = forceSSO && forceSSO?.value;
        const { ssos } = orgInfo;
        const userLoggedOut = localStorage.getItem('userLoggedOut') == 'true';

        // Scenario 1: Redirect user to the IdP if only SSO is enabled.
        // Scenario 2: Redirect user to the IdP if SSO & email are enabled.
        !userLoggedOut &&
        forceSSOEnabled &&
        ((ssos.length == 1 && ssos[0].name != 'email') ||
          (ssos.length == 2 && !!~ssos.map(sso => sso.name)))
          ? redirectToSSO(ssos)
          : redirectToLogIn(navigate);
      } else {
        redirectToLogIn(navigate);
      }
    }

    const isOnboardingCompleted = !user || user.onboardingCompleted;
    if (user) {
      window.localStorage.setItem(EDCAST_ID, user.id);
      window.localStorage.setItem(EDCAST_EMAIL, user.email);
      if (isOnboardingCompleted) {
        await Promise.all([
          dispatch(requestUserIsGroupLeader(isOnboardingCompleted)),
          dispatch(getSubAdminTeams()),
          dispatch(saveMappedOrganizations())
        ]);
      }
      if (!isOnboardingCompleted) dispatch(initOnboardingStateSettings());
      const configsAfterAuthorization = dispatch(getConfigsAfterAuthorization());
      setTimeout(() => {
        dispatch(loadGuideMeAsyc(configsAfterAuthorization));
      }, 4000);
    }

    // we register service worker after 4s which is after the page is loaded so that we don't occupy main thread during page load time.
    setTimeout(() => {
      if (navigator?.serviceWorker) {
        registerServiceWorker();
      } else {
        (async () => await deleteCacheKeys())();
      }
    }, 4000);

    const okta = find(orgInfo.configs, { name: 'app.config.okta.enabled' });
    const oktaEnabled = okta?.value;
    dispatch(
      handleOnboarding(isOnboardingCompleted, oktaEnabled, user?.passwordChangeable, navigate)
    );
    dispatch(loadAfterOrgDetails(orgInfo, user));
    if (isOktaRedirect) {
      dispatch(redirectToDeeplinkUrl(oktaEnabled, user?.passwordChangeable, navigate));
    }
  };
}

function loadAfterOrgDetails(orgDetail, user) {
  return async dispatch => {
    if (orgDetail) {
      dispatch(loadTranslations(orgDetail, user));
    }
  };
}

function loadGuideMeAsyc(orgConfig) {
  return async () => {
    const configs = await orgConfig;
    if (configs) {
      loadGuideMe(configs);
    }
  };
}

function handleOnboarding(isOnboardingCompleted, oktaEnabled, passwordChangeable, navigate) {
  return () => {
    localStorage.setItem(IS_ONBOARDING_COMPLETED, isOnboardingCompleted);
    const isUserMfaNeeded = checkMfaFlow(navigate, oktaEnabled, passwordChangeable);
    // If user onboarding isn't completed, we redirect here
    if (!isOnboardingCompleted && !/^\/(onboard|@).*/.test(location.pathname) && !isUserMfaNeeded) {
      navigate('/onboarding');
    } else if (
      isOnboardingCompleted &&
      window.location.pathname === '/onboarding' &&
      !isUserMfaNeeded
    ) {
      navigate('/');
    }
  };
}

function initSmartSrchAndSkillDir(orgInfo) {
  return () => {
    window.ldclient.waitUntilReady
      .then(() => {
        const isSmartSearch = LD.smartSearchLD() !== 'disabled';
        window.multiLangFlag = window.ldclient.variation('multilingual-content', false);
        const skillsDirectoryConfig = find(orgInfo?.configs, {
          name: 'skills_directory_enablement'
        });
        const skillsDirectoryEnabled =
          (skillsDirectoryConfig?.value && Permissions.has('SHOW_SKILLS_DIRECTORY')) ||
          NODE_ENV === 'development';
        const personalizedSearchValue = LD.personalizeOpenContentsLD();
        (isSmartSearch || skillsDirectoryEnabled) && JWT.token
          ? getAccessibleCandidates({ personalize_by: personalizedSearchValue })
          : null;
        isSmartSearch && JWT.token ? isDefaultSourceEnabled() : null;
      })
      .catch(err => {
        console.error(`Error in initSmartSrchAndSkillDir.ldclient.waitUntilReady.func: ${err}`);
      });
  };
}
// helper functions

function clearLocalStorage() {
  localStorage.removeItem('userLoggedOut');
}

function redirectToSSO(ssos) {
  const updatedSsos = ssos.filter(item => item.name !== 'email');
  const sso = updatedSsos[0];

  // setting the SAML id such that while user logout we can Logout identity providers session
  sso.id && window.localStorage.setItem(ORG_SAML_ID, sso.id);

  const afterLoginContentURL = localStorage.getItem(AFTER_LOGIN_CONTENT_URL);
  const urlStr = afterLoginContentURL
    ? `&origin=https://${window.location.hostname}${afterLoginContentURL}`
    : '';
  const link = `${sso.webAuthenticationUrl}${urlStr}`;

  setTimeout(() => {
    window.location = link;
  }, 2000);
}

function redirectToLogIn(navigate) {
  if (
    !window.location.pathname.includes('/saml/auth') &&
    !window.location.pathname.includes('/mobile-insights/') &&
    !OPEN_PAGE.includes(window.location.pathname)
  ) {
    const auto_sso = window.location.pathname === '/' ? '?auto_sso=true' : '';
    localStorage.removeItem('userLoggedOut');

    navigate(`/user/login${auto_sso}`);
  }
}

function loadTranslations(orgInfo, userInfo) {
  return dispatch => {
    const orgLanguageObj = find(orgInfo.configs, { name: 'DefaultOrgLanguage' });
    const orgLanguage = (orgLanguageObj && orgLanguageObj?.value) || 'en';
    const lang = userInfo?.profile?.language || orgLanguage;
    dispatch(updateTranslation(lang));
  };
}

function getDetailsFromLanguages(orgLangs) {
  const details = [];
  for (const [key, value] of Object.entries(orgLangs)) {
    details.push({ label: key, code: value });
  }
  return details;
}
