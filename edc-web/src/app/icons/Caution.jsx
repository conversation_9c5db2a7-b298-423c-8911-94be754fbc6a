const Caution = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.7401 18.0069L13.7401 4.00692C13.5657 3.69913 13.3127 3.44311 13.007 3.26499C12.7013 3.08687 12.3539 2.99303 12.0001 2.99303C11.6463 2.99303 11.2988 3.08687 10.9932 3.26499C10.6875 3.44311 10.4345 3.69913 10.2601 4.00692L2.26009 18.0069C2.08377 18.3123 1.99132 18.6588 1.9921 19.0114C1.99288 19.364 2.08687 19.7102 2.26454 20.0147C2.44221 20.3193 2.69724 20.5715 3.00379 20.7458C3.31033 20.92 3.6575 21.0101 4.01009 21.0069H20.0101C20.361 21.0066 20.7056 20.9139 21.0094 20.7382C21.3131 20.5625 21.5653 20.31 21.7406 20.006C21.9159 19.7021 22.0081 19.3573 22.008 19.0064C22.008 18.6555 21.9155 18.3108 21.7401 18.0069Z"
        fill="#FFF8E0"
      />
      <path d="M12.0101 9.00692V13.0069V9.00692Z" fill="#FFF8E0" />
      <path d="M12.0101 17.0069H12.0201H12.0101Z" fill="#FFF8E0" />
      <path
        d="M12.0101 9.00692V13.0069M12.0101 17.0069H12.0201M21.7401 18.0069L13.7401 4.00692C13.5657 3.69913 13.3127 3.44311 13.007 3.26499C12.7013 3.08687 12.3539 2.99303 12.0001 2.99303C11.6463 2.99303 11.2988 3.08687 10.9932 3.26499C10.6875 3.44311 10.4345 3.69913 10.2601 4.00692L2.26009 18.0069C2.08377 18.3123 1.99132 18.6588 1.9921 19.0114C1.99288 19.364 2.08687 19.7102 2.26454 20.0147C2.44221 20.3193 2.69724 20.5715 3.00379 20.7458C3.31033 20.92 3.6575 21.0101 4.01009 21.0069H20.0101C20.361 21.0066 20.7056 20.9139 21.0094 20.7382C21.3131 20.5625 21.5653 20.31 21.7406 20.006C21.9159 19.7021 22.0081 19.3573 22.008 19.0064C22.008 18.6555 21.9155 18.3108 21.7401 18.0069Z"
        stroke="#8A5C00"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Caution;
