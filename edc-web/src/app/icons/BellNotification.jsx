import React from 'react';
import PropTypes from 'prop-types';

const BellNotification = ({ disabled = false }) => {
  return (
    <svg
      width="28"
      height="18"
      viewBox="0 3 28 18"
      fill={disabled ? 'var(--ed-checkbox-disabled-icon-color)' : 'var(--ed-primary-base)'}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M16.32 16.56H7.68v-6.48a4.32 4.32 0 0 1 8.64 0v6.48zm1.44 0v-6.48a5.76 5.76 0 0 0-11.52 0v6.48a.72.72 0 1 0 0 1.44h4.32a1.44 1.44 0 1 0 2.88 0h4.32a.72.72 0 1 0 0-1.44z" />
    </svg>
  );
};

BellNotification.propTypes = {
  disabled: PropTypes.bool
};

export default BellNotification;
