import { lightGreyNavy, darkGrey } from 'centralized-design-system/src/MUIComponents/colors/colors';
import addSecurity from 'edc-web-sdk/helpers/filestackSecurity';
import { setToken, JWT } from 'edc-web-sdk/requests/csrfToken';
import moment from 'moment';
import momentTimezone from 'moment-timezone';
import { translatr } from 'centralized-design-system/src/Translatr';
import stripHTMLTags from './stripHTMLTags';
import { Permissions } from './checkPermissions';
import LD from '../containers/LDStore';
import {
  FILE_MEDIA_TYPE,
  IMAGE_MEDIA_TYPE,
  ALLOWED_INLINE_PLAY_CONTENT_STATUS
} from '../components/cardStandardization/common/constants';
import { getAllAvailableProficiencyLevels } from 'centralized-design-system/src/Utils/proficiencyLevels';

export const nDaysBefore = (today, n = null) => {
  return n
    ? new Date(new Date(today).setDate(today.getDate() - n)).toLocalISOString()
    : new Date(today).toLocalISOString();
};

export const getSelectedDate = (key, n = null) => {
  const today = new Date();
  return window.sessionStorage.getItem(key) || nDaysBefore(today, n);
};

export const skillLevels = ['N', 'B', 'I', 'A'];

export const getSkillsLevels = data => {
  if (!data || data.length === 0) {
    return {};
  }

  const skills = {};
  data.map(skill => {
    if (skill.skill_category_name) {
      const {
        skill_name,
        skill_description,
        skill_id,
        required_skill_level,
        skill_assessment,
        skill_courses,
        current_skill_level,
        job_family_role_skill_id,
        id,
        learning_plan_activated
      } = skill;
      const key = `${skill.skill_category_name}`;

      const skillDetails = {
        skill_name,
        skill_description: skill_description || skill_courses[0]?.skill_description || null,
        skill_id,
        required_skill_level,
        current_skill_level: current_skill_level || '0',
        job_family_role_skill_id,
        id,
        skill_assessment,
        skill_courses,
        learning_plan_activated
      };

      if (skills.hasOwnProperty(key)) {
        skills[key].push(skillDetails);
      } else {
        skills[key] = [skillDetails];
      }
    }
  });
  return skills;
};

export const getAssessmentsProperties = () => {
  return [
    {
      name: translatr('web.common.main', 'RecommendedSkillLevel'),
      tooltip: translatr('web.common.main', 'TheMinimumRecommendedSkillLevelSetUpByYourCompany'),
      dataKey: 'recommended',
      fillOpacity: 0.1,
      fill: darkGrey,
      isShow: true
    },
    {
      name: translatr('web.common.main', 'AveragePeerAssessment'),
      tooltip: translatr('web.common.main', 'AssessmentOfYourSkillsByYoursPeers'),
      dataKey: 'peer',
      fillOpacity: 0.3,
      fill: lightGreyNavy,
      isShow: true
    },
    {
      name: translatr('web.common.main', 'SelfAssessedSkillLevel'),
      tooltip: translatr('web.common.main', 'TheSkillsLevelsProvidedByYou'),
      dataKey: 'self',
      fillOpacity: 0.6,
      style: { fill: 'var(--ed-primary-base)' },
      isShow: true
    }
  ];
};

export const getDynamicLimit = (container, cardWidth = 306) => {
  const cardsPerRow = container ? parseInt(container.clientWidth / cardWidth) : null;
  let limit = 9;
  if (cardsPerRow) {
    switch (cardsPerRow) {
      case 1:
        limit = cardsPerRow * 9;
        break;
      case 2:
        limit = cardsPerRow * 4;
        break;
      default:
        limit = cardsPerRow * 3;
    }
  }
  return limit;
};

export const formatData = (data, formatDate) => {
  return data.map(activityData => {
    let snippet;
    let type;
    let message = '';
    let linkUrl, linkPrefix, slug;
    let user = activityData.user.name;

    if (activityData.textSnippet) {
      snippet = activityData.textSnippet;
    }

    if (snippet?.length > 40) {
      snippet = snippet.slice(0, 40) + '...';
    }
    switch (activityData.action) {
      case 'created_livestream':
        message += ` ${translatr('web.common.main', 'Created')}`;
        break;
      case 'upvote':
        message += ` ${translatr('web.common.main', 'Liked')}`;
        break;
      case 'comment':
        message += ` ${translatr('web.common.main', 'CommentedOn')}`;
        break;
      case 'created_card':
        message += ` ${translatr('web.common.main', 'CreatedSmartcard')}:`;
        break;
      case 'created_pathway':
        message += ` ${translatr('web.common.main', 'CreatedPathway')}:`;
        break;
      case 'created_journey':
        message += ` ${translatr('web.common.main', 'CreatedJourney')}:`;
        break;
      case 'assignment_completed':
        message += ` ${translatr('web.common.main', 'CompletedJourney')}:`;
        break;
      case 'smartbite_completed':
        const completedMessages = {
          journey: 'CompletedJourney',
          collection: 'CompletedPathway',
          default: 'CompletedSmartcard'
        };

        const smartbiteCompletedMessage =
          completedMessages[activityData.linkable.type] || completedMessages.default;
        message += ` ${translatr('web.common.main', smartbiteCompletedMessage)}:`;
        break;
      case 'smartbite_uncompleted':
        const uncompletedMessages = {
          journey: 'UncompletedJourney',
          collection: 'UncompletedPathway',
          default: 'UncompletedSmartcard'
        };

        const smartbiteUncompletedMessage =
          uncompletedMessages[activityData.linkable.type] || uncompletedMessages.default;
        message += ` ${translatr('web.common.main', smartbiteUncompletedMessage)}:`;
        break;
      default:
        message += '';
        break;
    }

    switch (activityData.linkable.type) {
      case 'journey':
        type = 'journey';
        linkUrl = `journey/${activityData.linkable.id}`;
        slug = activityData.linkable.id;
        linkPrefix = 'journey';
        break;
      case 'collection':
        type = 'pathways';
        linkUrl = `pathways/${activityData.linkable.id}`;
        slug = activityData.linkable.id;
        linkPrefix = 'pathways';
        break;
      case 'card':
        type = 'insights';
        linkUrl = `insights/${activityData.linkable.id}`;
        slug = activityData.linkable.id;
        linkPrefix = 'insights';
        break;
      case 'video_stream':
        type = 'insights';
        linkUrl = `insights/${activityData.streamableId}`;
        slug = activityData.streamableId;
        linkPrefix = 'insights';
        break;
      default:
        type = '';
        break;
    }
    return {
      ...activityData,
      action: message,
      type: type,
      message: snippet,
      linkUrl: linkUrl,
      slug: slug,
      linkPrefix: linkPrefix,
      user: user,
      userObj: activityData.user,
      time: formatDate
        ? moment(activityData.createdAt).format('DD MMM YYYY, hh:mmA z')
        : activityData.createdAt
    };
  });
};

export const updatePageLastVisit = value => {
  if (!localStorage.getItem(value)) {
    localStorage.setItem(value, Math.round(new Date().getTime() / 1000));
  }
  // when we reload page we set new data
  if (window.performance && window.performance.navigation.type === 1) {
    localStorage.setItem(value, Math.round(new Date().getTime() / 1000));
  }
};

export const isBooleanValuePresent = value => value && typeof value === 'boolean';
export const getStadardType = value => (value && typeof value === 'object' ? value : null);

export const isOldTextCard = (cardType, hasCardTitle) => cardType === 'text' && !hasCardTitle;

export const stripHtmlTagsIfNotOldTextCard = (card, title) => {
  return isOldTextCard(card.cardType, !!card?.cardTitle) && !card?.filestack?.length // old text card without image.
    ? title
    : stripHTMLTags(title);
};

/**
 * Non UGC private content refers to Content coming from intergation/labs and is not generated by the user.
 */
export const isNonUgcContent = card => !card.author;

export const isUgcContent = card => card.author;

export const getOcgOrEgt = (isOcgEnabled, isEgtEnabled, isSettingsPage) => {
  if (isSettingsPage) {
    return isOcgEnabled ? ['OCG'] : ['EGT'];
  }
  if (isOcgEnabled && !isEgtEnabled) return ['OCG'];
  else if (!isOcgEnabled && isEgtEnabled) return ['EGT'];
  else return undefined;
};

export const isValidUrl = urlString => {
  try {
    // URL Constructor
    // If the string is a valid URL, a URL object is created, and true is returned
    return !!new URL(urlString);
  } catch (e) {
    // If the String is not a valid URL, a Tyeperror exception is thrown, and false is returned
    return false;
  }
};

export const updateObjectKeys = (object, updatedKeysObj) => {
  let updatedObj = {};
  Object.entries(updatedKeysObj).forEach(([key, value]) => {
    if (object[key]) {
      updatedObj[value] = object[key];
    }
  });
  return updatedObj;
};

export function truncateText(
  message,
  characterLimit = 140,
  ellipsisText = '',
  viewMoreTextLength = 0
) {
  let truncateMessage = '';
  if (message?.length + viewMoreTextLength > characterLimit) {
    truncateMessage =
      message.substr(0, characterLimit - ellipsisText.length - viewMoreTextLength) + ellipsisText;
  } else {
    truncateMessage = message;
  }
  return truncateMessage;
}

export const tooltipTextWrapper = tooltipText => {
  return `<p className="tooltip-text">${tooltipText}</p>`;
};

export const isInternetExplorer = () => {
  const ua = navigator.userAgent;
  /* MSIE used to detect old browsers and Trident used to newer ones*/
  return ua.indexOf('MSIE ') > -1 || ua.indexOf('Trident/') > -1;
};

export const isEdgeBrowser = () => {
  const ua = navigator.userAgent;
  /* MSIE used to detect old browsers and Trident used to newer ones*/
  return ua.indexOf('Edge/') > -1;
};

export const isAdminFunc = isCurrentUserAdmin => {
  return isCurrentUserAdmin && Permissions.has('ADMIN_ONLY');
};

// Returns string with the first letter of each word capitalized
export const capitalize = label => {
  return label?.charAt(0).toUpperCase() + label?.slice(1);
};

export const hasLMSWorkflow = cardData => {
  const lmsWorkflows = cardData?.lmsWorkflows;
  return lmsWorkflows && Object.keys(lmsWorkflows).length != 0;
};

export const isLmsWorkFlowTrue = cardData => {
  const {
    hasPrerequisites,
    hasRequiredPrework,
    requiresApprovalsForUser,
    hasRequiredPostwork,
    eSignRequiredForUser,
    hasRequiredEvaluations,
    isCompletionAcknowledgmentRequired,
    requiresCompletionApprovalsForUser
  } = getDestructuredData(cardData);

  return (
    hasLMSWorkflow(cardData) &&
    (hasPrerequisites ||
      hasRequiredPrework ||
      requiresApprovalsForUser ||
      hasRequiredPostwork ||
      eSignRequiredForUser ||
      hasRequiredEvaluations ||
      isCompletionAcknowledgmentRequired ||
      requiresCompletionApprovalsForUser)
  );
};

export const hasPostWorkActivity = cardData => {
  const {
    eSignRequiredForUser,
    hasRequiredEvaluations,
    isCompletionAcknowledgmentRequired,
    requiresCompletionApprovalsForUser,
    eSignPending,
    evaluationPending,
    completionAcknowledgementPending,
    completionApprovalPending
  } = getDestructuredData(cardData);

  return (
    hasCompletionAckActivity(
      cardData,
      isCompletionAcknowledgmentRequired,
      completionAcknowledgementPending
    ) ||
    hasCompletionApprovalActivity(
      cardData,
      requiresCompletionApprovalsForUser,
      completionApprovalPending
    ) ||
    ((eSignRequiredForUser || hasRequiredEvaluations) &&
      (eSignPending !== false || evaluationPending !== false))
  );
};

/*
 * If Content has completion Approval activity,
 * Ignore check if content is completed
 * Mark Approval Activity pending if any of completionApprovalPending or requiresCompletionApprovalsForUser is true
 * Refer EP-102536 for details
 */
const hasCompletionApprovalActivity = (
  cardData,
  requiresCompletionApprovalsForUser,
  completionApprovalPending
) => {
  if (isContentCompleted(cardData)) return false;

  return completionApprovalPending || requiresCompletionApprovalsForUser;
};

/*
 * If Content has completion Acknowledgement activity,
 * Ignore check if content is completed
 * Mark Approval Activity pending if any of completionAcknowledgementPending or isCompletionAcknowledgmentRequired is true
 * Refer EP-102536 for details
 */
const hasCompletionAckActivity = (
  cardData,
  isCompletionAcknowledgmentRequired,
  completionAcknowledgementPending
) => {
  if (isContentCompleted(cardData)) return false;

  return completionAcknowledgementPending || isCompletionAcknowledgmentRequired;
};

const isContentCompleted = ({ completionState, completedPercentage }) => {
  return completionState === 'COMPLETED' && completedPercentage === 100;
};

export const isContentStatusNotAllowedForInlinePlay = contentStatus => {
  return contentStatus === null || !ALLOWED_INLINE_PLAY_CONTENT_STATUS.includes(contentStatus);
};

export const hasPreActivity = cardData => {
  const { requiresApprovalsForUser, approvalPending, contentStatus } = getDestructuredData(
    cardData
  );

  return (
    hasIncompletePreReq(cardData) ||
    hasIncompletePreWork(cardData) ||
    (requiresApprovalsForUser && approvalPending !== false) ||
    (requiresApprovalsForUser && isContentStatusNotAllowedForInlinePlay(contentStatus))
  );
};

export const hasIncompletePreReq = card => {
  const {
    hasPrerequisites,
    userHasIncompletePrerequisites,
    prerequisitesPending,
    hasTranscript
  } = getDestructuredData(card);

  if (!hasPrerequisites) {
    return false;
  }

  return hasTranscript ? prerequisitesPending !== false : userHasIncompletePrerequisites !== false;
};

export const hasIncompletePreWork = card => {
  const {
    hasRequiredPrework,
    userHasIncompleteRequiredPreWork,
    requiredPreworkPending,
    hasTranscript
  } = getDestructuredData(card);

  if (!hasRequiredPrework) {
    return false;
  }

  return hasTranscript
    ? requiredPreworkPending !== false
    : userHasIncompleteRequiredPreWork !== false;
};

export const shouldShowLikeArticleView = cardData => {
  const {
    requiredPostworkPending,
    contentStatus,
    isMfeOnePlayerEnabled,
    launchInline
  } = getDestructuredData(cardData);

  const hasCompletedPayment = isPaymentCompleted(contentStatus);
  const hasPreCondition = hasPreActivity(cardData);
  const shouldLoadOnePlayerMfe = isMfeOnePlayerEnabled && !launchInline;
  return (
    hasPreCondition ||
    (isLmsPaidContent(cardData) && !hasCompletedPayment) ||
    shouldLoadOnePlayerMfe
  );
};

export const shouldHideMarkAsCompleted = (cardData, markAsCompleteLabel) => {
  const {
    hasRequiredPostwork,
    requiredPostworkPending,
    mediaType,
    contentStatus,
    isMfeOnePlayerEnabled
  } = getDestructuredData(cardData);

  const requirePostWorkActivity = hasPostWorkActivity(cardData);
  const requirePreActivity = hasPreActivity(cardData);
  const hasRequiredPostworkPending =
    requiredPostworkPending === undefined || Boolean(requiredPostworkPending);

  const meetsPaymentRequirements = isLmsPaidContent(cardData)
    ? isPaymentCompleted(contentStatus)
    : true;

  return markAsCompleteLabel == translatr('web.common.main', 'MarkAsCompleted')
    ? (isMediaTypeMeterial(mediaType) || isMfeOnePlayerEnabled) &&
        (requirePreActivity ||
          (hasRequiredPostwork && hasRequiredPostworkPending) ||
          requirePostWorkActivity ||
          !meetsPaymentRequirements)
    : false;
};

export const isMediaTypeMeterial = mediaType => {
  if (mediaType == 'course') {
    return false;
  } else if (
    FILE_MEDIA_TYPE.includes(mediaType) ||
    IMAGE_MEDIA_TYPE.includes(mediaType) ||
    mediaType === 'pdf' ||
    mediaType === 'video' ||
    mediaType === 'link'
  ) {
    return true;
  }
};

const getDestructuredData = cardData => {
  const {
    hasPrerequisites,
    userHasIncompletePrerequisites,
    hasRequiredPrework,
    userHasIncompleteRequiredPreWork,
    requiresApprovalsForUser,
    hasRequiredPostwork,
    eSignRequiredForUser,
    hasRequiredEvaluations,
    isCompletionAcknowledgmentRequired,
    requiresCompletionApprovalsForUser
  } = cardData?.lmsWorkflows ?? {};
  const {
    prerequisitesPending,
    requiredPreworkPending,
    approvalPending,
    requiredPostworkPending,
    eSignPending,
    evaluationPending,
    completionAcknowledgementPending,
    completionApprovalPending,
    hasTranscript,
    contentStatus
  } = cardData?.transcriptData ?? {};
  const { mediaType } = cardData?.contentInlinePlayConfiguration ?? {};
  const { isMfeOnePlayerEnabled, launchInline } = cardData ?? {};

  return {
    hasPrerequisites,
    userHasIncompletePrerequisites,
    hasRequiredPrework,
    userHasIncompleteRequiredPreWork,
    requiresApprovalsForUser,
    hasRequiredPostwork,
    eSignRequiredForUser,
    hasRequiredEvaluations,
    isCompletionAcknowledgmentRequired,
    requiresCompletionApprovalsForUser,
    prerequisitesPending,
    requiredPreworkPending,
    approvalPending,
    requiredPostworkPending,
    eSignPending,
    evaluationPending,
    completionAcknowledgementPending,
    completionApprovalPending,
    mediaType,
    hasTranscript,
    contentStatus,
    isMfeOnePlayerEnabled,
    launchInline
  };
};

export const isRegisterAndPlay = (card, isStandaloneLayout) => {
  const transcriptData = card?.transcriptData ?? {};
  const canPlayInline =
    transcriptData && Object.keys(transcriptData).length > 0
      ? !shouldShowLikeArticleView(card)
      : true;
  return (
    card?.launchInline &&
    card?.contentInlinePlayConfiguration?.mediaType == 'course' &&
    card?.resource?.embedHtml &&
    isStandaloneLayout &&
    LD.isEmbedHtmlPlayInlineEnabled() &&
    canPlayInline
  );
};

export const getTranslatedContentStatus = status => {
  const updatedStatus = status?.replace(/\s+/g, '');
  return (
    updatedStatus &&
    (translatr('web.common.main', updatedStatus) ||
      translatr('web.smartcard.standalone', updatedStatus) ||
      translatr('web.mylearningplan.main', updatedStatus))
  );
};

/**
+   * If the content is served via AWS CDN, add the security details to the filestack URL.
+   * Otherwise, just use the original filestack URL.
+   *
+   * @returns {string} The filestack URL with or without security details.
+   */
export const getFilestackUrlWithSecurity = ({ url, expireAfter, currentUserId, handle }) => {
  let filestackUrl;
  if (
    window.__edOrgData?.serveFsContentViaAwsCdnEnabled &&
    !!~url.indexOf(window.location.hostname + '/cdn/uploads/')
  ) {
    filestackUrl = addSecurity(
      `https://cdn.filestackcontent.com/${handle}`,
      expireAfter,
      currentUserId
    );
  } else {
    filestackUrl = url;
  }

  return filestackUrl;
};

export const setTokensInOnboarding = (csrfToken, jwtToken) => {
  setToken(csrfToken);
  JWT.token = jwtToken;
};

/**
 * Removes a specified key from a query string and returns the updated query string.
 *
 * @param {string} queryString - The query string to remove the key from.
 * @param {string} keyToRemove - The key to remove from the query string.
 * @return {string} The updated query string without the specified key. If the query string is empty after removal, the default string is returned.
 */
export const removeFilterQueryParam = (keyToRemove, queryString = window.location.search) => {
  // Parse the query string after converting %26 to & for proper parameter handling
  const params = new URLSearchParams(queryString.replace(/%26filters/, '&filters'));

  params.delete(keyToRemove);

  return params.toString() ? `?${params.toString()}` : queryString;
};

/*
  'Asia/Calcutta' has been removed from the timezone list of the translatr (sdk)
  as it refers to the same timezone as 'Asia/Kolkata'.
  However, since moment.tz.guess may still return 'Asia/Calcutta' in some browsers like Chrome,
  we're replacing it with 'Asia/Kolkata' here.
*/
export const guessUserTimezone = () => {
  const timezone = momentTimezone.tz.guess();
  return timezone === 'Asia/Calcutta' ? 'Asia/Kolkata' : timezone;
};

export function updateButtonOutlines(isKeyboardNavigation, ...buttons) {
  buttons.forEach(button => {
    if (button) {
      if (isKeyboardNavigation) {
        button.classList.remove('no-outline'); // Keyboard navigation: show outline
      } else {
        button.classList.add('no-outline'); // Mouse navigation: hide outline
      }
    }
  });
}

export const getLXMediaHubImage = card => {
  const mediaMimetype = card?.media?.mimetype;
  const thumbnailMimetype = card?.thumbnail?.mimetype;
  const isThumbnailImage = ~thumbnailMimetype?.indexOf('image/');
  const isMediaImage = ~mediaMimetype?.indexOf('image/');

  const LXMediaImageUrl =
    (isThumbnailImage && card.thumbnail?.url) || (isMediaImage && card.media?.url);

  const LXMediaAltText = card.thumbnail?.alt_text || card.media?.alt_text;
  return {
    img: LXMediaImageUrl,
    altText: LXMediaAltText,
    mimeType: mediaMimetype
  };
};

export const isLmsPaidContent = card => {
  return (
    card?.prices?.length > 0 &&
    card?.prices[0]?.amount > 0 &&
    card?.isContentFromInternalLms &&
    card.cardMetadatum?.plan === 'paid'
  );
};

export const hasTranscriptWithValidStatus = card => {
  const { transcriptData = {} } = card || {};
  const { contentStatus } = card?.transcriptData || {};
  return (
    Object.keys(transcriptData).length != 0 && contentStatus !== null && contentStatus !== undefined
  );
};

// contentStatus will be present in ALLOWED_INLINE_PLAY_CONTENT_STATUS
// only if the payment has been completed
export const isPaymentCompleted = contentStatus =>
  ALLOWED_INLINE_PLAY_CONTENT_STATUS.includes(contentStatus);

export const getUrlParam = (paramName = '') => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(paramName);
};

export const extractOriginFromUrl = (url = '') => {
  if (!url || typeof url !== 'string') {
    return '';
  }

  try {
    const urlObj = new URL(url);
    return urlObj.origin;
  } catch (error) {
    console.error(`Failed to extract origin from URL '${url}':`, error);
    return '';
  }
};

export const updateCredentialFileArray = (fileAttachment, credentialDetails = {}) => {
  if (!fileAttachment || typeof fileAttachment !== 'object') {
    credentialDetails.credential = [];
  } else if (fileAttachment?.key) {
    credentialDetails.credential = [fileAttachment];
  }
};

export const isProficiencyLevelValueNotSupported = (
  levelValue,
  proficiencyLevels = window.__edOrgData.proficiencyLevels
) => {
  return !getAllAvailableProficiencyLevels(proficiencyLevels)?.find(
    level => level.value === levelValue
  );
};
