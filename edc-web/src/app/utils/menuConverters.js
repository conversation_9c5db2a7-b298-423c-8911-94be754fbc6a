/**
 * Converts the unified_menus data structure to the GalaxyMenuItem interface format
 * @param {Array} unifiedMenus - The unified_menus array from the API
 * @returns {Array} - Array of GalaxyMenuItem objects
 */
export function convertToGalaxyMenuItems(unifiedMenus) {
  if (!unifiedMenus || !Array.isArray(unifiedMenus)) {
    return [[], {}];
  }

  let specialMenuItemsIndex = {};

  return [
    unifiedMenus.map(menu => {
      const menuItem = {
        id: menu.key,
        label: menu.label
      };

      if (menu.href) {
        menuItem.href = menu.href;
      }

      if (menu.target) {
        menuItem.target = menu.target;
      }

      // Handle special menu items with payload forms
      if (menu.payload && menu.target === '_blank') {
        specialMenuItemsIndex[menu.label] = menuItem;
      }

      // Convert sub_menus to submenu if they exist
      if (menu.sub_menus && menu.sub_menus.length > 0) {
        menuItem.submenu = menu.sub_menus.map(subMenu => {
          // handle special menu items with payload forms
          if (subMenu.payload && subMenu.target === '_blank') {
            specialMenuItemsIndex[subMenu.label] = subMenu;
          }
          return {
            id: subMenu.key,
            label: subMenu.label,
            href: subMenu.href || undefined,
            target: subMenu.target || undefined,
            // Handle payload forms if needed
            ...(subMenu.payload ? { payload: subMenu.payload } : {})
          };
        });
      }

      return menuItem;
    }),
    specialMenuItemsIndex
  ];
}
