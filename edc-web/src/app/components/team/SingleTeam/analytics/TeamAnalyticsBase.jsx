import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import Loadable from 'react-loadable';
import {
  getGraphData,
  getGroupDetail,
  topContent,
  topContributors
} from '../../../../actions/groupAnalyticActions';
import SmallGraphContainer from './SmallGraphContainer';
import TeamGraphStandalone from './TeamGraphStandalone';
import BackIcon from 'centralized-design-system/src/MUIComponents/icons/BackIcon';
import moment from 'moment';
import Spinner from '../../../common/spinner';
import { CSVLink } from 'react-csv';
import { snackBarOpenClose } from '../../../../actions/channelsActionsV2';
import { Permissions } from '../../../../utils/checkPermissions';
import LD from '../../../../containers/LDStore';
import withRouter from '@hocs/withRouter';
import { Button } from 'centralized-design-system/src/Buttons';

const TeamsLinearGraph = Loadable({
  loader: () => import('./TeamsLinearGraph'),
  loading() {
    return (
      <div style={{ marginLeft: '50%' }}>
        <Spinner />
      </div>
    );
  }
});

class TeamAnalyticsBase extends Component {
  constructor(props, context) {
    super(props, context);
    let startDateObj = moment
      .utc()
      .subtract(1, 'year')
      .startOf('month');
    let finishDateObj = moment
      .utc()
      .add(1, 'month')
      .startOf('month');

    this.state = {
      startDate: startDateObj.format('MM/DD/YYYY'),
      finishDate: finishDateObj.format('MM/DD/YYYY'),
      fromDate: startDateObj.format('YYYY-MM-DD'),
      toDay: finishDateObj.format('YYYY-MM-DD'),
      listOfScreens: ['top', 'graph', 'all'],
      currentScreen: 'all',
      period: 'year',
      graphLoading: true,
      currentTop: '',
      metricsForDownload: [],
      teamAnalyticsEnabled: LD.teamAnalyticsEnabled()
    };

    this.styles = {
      list: {
        padding: 0
      }
    };

    this.topContent = [];
    this.topContributors = [];
  }

  componentDidMount = () => {
    let slug = this.props.routeParams.slug;

    this.props
      .dispatch(getGroupDetail(slug))
      .then(group => {
        this.setState({ groupId: group.id });
        const { isTeamAdmin, isTeamSubAdmin } = group;
        const isTeamSubAdminAnalyticsEnabled =
          isTeamSubAdmin && Permissions['enabled'] && Permissions.has('MANAGE_GROUP_ANALYTICS');
        if (
          !!group &&
          this.state.teamAnalyticsEnabled &&
          (isTeamAdmin || isTeamSubAdminAnalyticsEnabled)
        ) {
          let opt = {
            start_date: this.state.fromDate,
            end_date: this.state.toDay,
            team_id: group.id,
            limit: 1000
          };

          let payload_for_top = {
            limit: 12,
            offset: 0
          };

          let teamId = group.id;
          this.props
            .dispatch(getGraphData(opt))
            .then(graphData => {
              this.setState({ graphLoading: false, graphData });
            })
            .catch(err => {
              this.setState({ graphLoading: false });
              console.error(`Error in TeamAnalyticsBase.getGraphData.func : ${err}`);
            });
          //Sending limit and offsets
          this.props
            .dispatch(topContent(teamId, payload_for_top))
            .then(data => {
              this.topContent =
                (data &&
                  data.topContent.map(item => {
                    delete item.id;
                    return item;
                  })) ||
                [];
            })
            .catch(err => {
              console.error(`Error in TeamAnalyticsBase.topContent.func : ${err}`);
            });
          this.props
            .dispatch(topContributors(teamId, payload_for_top))
            .then(data => {
              this.topContributors =
                (data &&
                  data.topContributors.map(item => {
                    delete item.id;
                    return item;
                  })) ||
                [];
            })
            .catch(err => {
              console.error(`Error in TeamAnalyticsBase.topContributors.func : ${err}`);
            });
        } else {
          if (
            group.isMember ||
            group.isTeamSubAdmin ||
            group.isTeamAdmin ||
            group.isTeamModerator
          ) {
            this.props.navigate(`/teams/${slug}`);
          } else {
            this.props.navigate(`/org-groups`);
          }
          this.props.dispatch(
            snackBarOpenClose(
              translatr('web.common.main', 'YouAreNotAuthorizedToAccessThisGroupAnalytics'),
              5000
            )
          );
        }
      })
      .catch(err => {
        this.props.dispatch(
          snackBarOpenClose(
            translatr('web.common.main', 'YouAreNotAuthorizedToAccessThisGroupAnalytics'),
            5000
          )
        );
        this.props.navigate(`/org-groups`);
        console.error(`Error in TeamAnalyticsBase.getGroupDetail.func : ${err}`);
      });
  };

  openAssignmentPerformance = () => {
    let slug = this.props.routeParams.slug;
    this.props.navigate(`/teams/${slug}/analytics/assignment`);
  };

  onGraphClick = currentGraph => {
    this.setState({ currentScreen: 'graph', currentGraph });
  };

  onBackClick = e => {
    e.preventDefault();
    if (this.state.currentScreen === 'all') {
      this.props.navigate(-1);
    } else {
      this.setState({ currentScreen: 'all' });
    }
  };

  showTopContent = () => {
    this.setState({ currentScreen: 'top', currentTop: 'content' });
  };

  showTopContributor = () => {
    this.setState({ currentScreen: 'top', currentTop: 'contributor' });
  };

  downloadMetrics = name => {
    this[name].children[0].click();
  };

  createDownloadSection = (dataForDownload, filename) => {
    return (
      <div className="download-button-container">
        <Button color="primary" onClick={this.downloadMetrics.bind(this, `_download_${filename}`)}>
          {translatr(
            'web.analytics-reports.main',
            this.state.isDownloading ? 'Downloading' : 'Download'
          )}
        </Button>
        <div className="hidden" ref={input => (this[`_download_${filename}`] = input)}>
          <CSVLink data={dataForDownload} target="_self" filename={`${filename}.csv`}>
            {translatr('web.common.main', 'DownloadCsv')}
          </CSVLink>
        </div>
      </div>
    );
  };

  render() {
    let groupAnalytics = this.props.groupAnalytics;
    const isClcEnable = LD.continousLearningCredits();
    return (
      <div className="team-analytics-base">
        <div className="row row-width">
          <div className="column small-6">
            <button
              aria-label={translatr('web.common.main', 'BackToPreviousSection')}
              className="breadcrumbBack"
              onClick={this.onBackClick}
            >
              <BackIcon style={{ width: '19px' }} color={'#454560'} />
            </button>
          </div>
          {!(this.state.currentScreen === 'assignment') && this.state.currentScreen === 'all' && (
            <div className="column small-6 clearfix btn-wrapper">
              <Button color="primary" onClick={this.openAssignmentPerformance}>
                {translatr('web.common.main', 'AssignmentPerformance')}
              </Button>
            </div>
          )}
        </div>

        {this.state.currentScreen === 'all' && (
          <div className="row row-width">
            <div
              className="column small-6 top-container"
              onClick={this.showTopContent}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  this.showTopContent();
                }
              }}
              role="button"
              tabIndex="0"
            >
              {this.props.groupAnalytics.topContent && (
                <TeamsLinearGraph
                  type={'content'}
                  viewStyle={'group'}
                  data={this.props.groupAnalytics.topContent.slice(0, 4)}
                  showTooltip={true}
                />
              )}
            </div>
            <div
              className="column small-6 top-container"
              onClick={this.showTopContributor}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  this.showTopContributor();
                }
              }}
              role="button"
              tabIndex="0"
            >
              {this.props.groupAnalytics.topContributors && (
                <TeamsLinearGraph
                  type={'contributors'}
                  viewStyle={'group'}
                  data={this.props.groupAnalytics.topContributors.slice(0, 4)}
                  showTooltip={true}
                />
              )}
            </div>
          </div>
        )}

        {this.state.currentScreen === 'top' && this.state.currentTop === 'content' && (
          <div className="row row-width">
            <div className="column small-12">
              {this.createDownloadSection(this.topContent, 'Top_Content')}
              <TeamsLinearGraph
                type={'content'}
                charLimit={30}
                viewStyle={'standalone'}
                data={this.props.groupAnalytics.topContent}
              />
            </div>
          </div>
        )}

        {this.state.currentScreen === 'top' && this.state.currentTop === 'contributor' && (
          <div className="row row-width">
            <div className="column small-12">
              {this.createDownloadSection(this.topContributors, 'Top_Contributors')}
              <TeamsLinearGraph
                type={'contributors'}
                charLimit={30}
                viewStyle={'standalone'}
                data={this.props.groupAnalytics.topContributors}
              />
            </div>
          </div>
        )}

        {groupAnalytics && this.state.currentScreen == 'all' && (
          <div className="row row-width graphs-container">
            {this.state.graphLoading && (
              <div className="small-12 text-center">
                <Spinner />
              </div>
            )}
            {!this.state.graphLoading && !this.state.graphData && <div></div>}
            {groupAnalytics.graphData &&
              !!groupAnalytics.graphData.length &&
              groupAnalytics.graphData.map(
                (graph, i) =>
                  (!(graph.name === 'clc') || isClcEnable) && (
                    <SmallGraphContainer
                      key={i}
                      onClick={this.onGraphClick.bind(this, graph)}
                      startDate={this.state.startDate}
                      finishDate={this.state.finishDate}
                      graphTitle={graph.title}
                      graphSubTitle={graph.subTitle}
                      graphValue={graph.graphValue}
                      period={this.state.period}
                    />
                  )
              )}
          </div>
        )}
        {this.state.currentScreen === 'graph' && (
          <TeamGraphStandalone
            startDate={this.state.startDate}
            finishDate={this.state.finishDate}
            graphTitle={this.state.currentGraph.title}
            graphSubTitle={this.state.currentGraph.subTitle}
            graphValue={this.state.currentGraph.graphValue}
            period={this.state.period}
            createDownloadSection={this.createDownloadSection}
          />
        )}
      </div>
    );
  }
}

TeamAnalyticsBase.propTypes = {
  groupAnalytics: PropTypes.any,
  routeParams: PropTypes.any,
  navigate: PropTypes.func
};

export default withRouter(
  connect(state => ({
    groupAnalytics: state.groupAnalytic.toJS()
  }))(TeamAnalyticsBase)
);
