import { useEffect, useRef, useState } from 'react';
import { getUserNavList } from 'edc-web-sdk/requests/users.v2';
import Notifications from 'centralized-design-system/src/UnifiedNav/Notifications/notifications';
import Search from 'centralized-design-system/src/Header/search';
import { AWS_CDN_STATIC_ASSETS_HOST } from 'edc-web-sdk/config/envConstants';
import { updateUser } from 'edc-web-sdk/requests/users';
import { sendCSVTranslationLog } from 'edc-web-sdk/requests/translation';
import { convertToGalaxyMenuItems } from '../../utils/menuConverters';
import './styles.scss';
import { translatr } from 'centralized-design-system/src/Translatr';
import LazyloadComponent from 'centralized-design-system/src/Utils/lazy';
import { LANDING_PAGE_V2_ENABLED } from '@pages/TalentMarketplace/util';
const CreateSmartcard = LazyloadComponent(() =>
  import('centralized-design-system/src/Modals/CreateSmartcard')
)();
import { useNavigate } from 'react-router-dom';

export default function Header() {
  const [navItems, setNavItems] = useState([]);
  const [profileItems, setProfileItems] = useState([]);
  const [specialMenuItemsIndex, setSpecialMenuItemsIndex] = useState({});
  const [, setLanguages] = useState([]);
  const [userDetails, setUserDetails] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const menuBarRef = useRef();
  const navigate = useNavigate();
  const profileMenuRef = useRef();

  useEffect(() => {
    init();
  }, []);

  const firstTabLink = tab => {
    if (tab.sub_menus && tab.sub_menus?.[0]?.href) {
      return tab.sub_menus[0].href;
    }
    return tab.href;
  };
  // Add event listener only for profile menu item clicks
  useEffect(() => {
    if (!isLoading && profileItems.length > 0) {
      // Handler for profile menu item clicks
      const handleProfileMenuItemClick = event => {
        // The event detail contains the clicked item
        const clickedItem = event.detail.item;
        if (clickedItem && clickedItem.id) {
          handleLanguageChange(clickedItem.id);
        }
      };

      // Add event listener to the profile menu
      const profileMenu = document.querySelector('galaxy-menu-button[variant="profile"]');

      if (profileMenu) {
        profileMenu.addEventListener('menu-item-clicked', handleProfileMenuItemClick);
      }

      // Clean up event listener
      return () => {
        if (profileMenu) {
          profileMenu.removeEventListener('menu-item-clicked', handleProfileMenuItemClick);
        }
      };
    }
  }, [isLoading, profileItems]);

  useEffect(() => {
    if (!isLoading && navItems.length > 0) {
      const menuBar = document.querySelector('galaxy-menu-bar');

      if (menuBar && menuBar.shadowRoot) {
        const observer = new MutationObserver(mutationsList => {
          for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
              const links = menuBar.shadowRoot.querySelectorAll('li.galaxy-menu-item');
              links.forEach(link => {
                link.addEventListener('click', handleMenuBarItemClick);
              });
            }
          }
        });

        observer.observe(menuBar.shadowRoot, { childList: true, subtree: true });

        // Clean up observer and event listeners
        return () => {
          observer.disconnect();
          const links = menuBar.shadowRoot.querySelectorAll('li.galaxy-menu-item');
          links.forEach(link => {
            link.removeEventListener('click', handleMenuBarItemClick);
          });
        };
      }
    }
  }, [isLoading, navItems]);

  const handleMenuBarItemClick = async event => {
    const clickedItem = event.target.closest('a.galaxy-menu-link');
    if (
      clickedItem.getAttribute('href') === '#' &&
      clickedItem.getAttribute('target') === '_blank'
    ) {
      event.preventDefault();
      event.stopPropagation();
      const labelText = clickedItem.querySelector('.galaxy-menu-link__label').textContent;
      const { payload } = specialMenuItemsIndex[labelText];
      const { element, type, name, id, value } = payload?.form[0];

      let form = document.createElement('form');
      form.method = 'POST';
      form.target = '_blank';
      form.style.display = 'none';
      form.action = payload?.action;

      // Create input element and set attributes
      const inputElement = document.createElement(element);
      inputElement.type = type;
      inputElement.name = name;
      inputElement.id = id;
      inputElement.value = value;

      // Append input element to form
      form.appendChild(inputElement);

      document.body.appendChild(form);
      form.submit();
      setTimeout(() => {
        form.remove();
      }, 500);
    }
  };

  const init = () => {
    setIsLoading(true);
    getUserNavList()
      .then(navList => {
        const { unified_menus, profile_menus, languages, user_details } = navList;
        const allTabs = unified_menus || [];

        // Convert the unified_menus to GalaxyMenuItem format
        const [convertedNavItems, specialItemsIndex] = convertToGalaxyMenuItems(allTabs);

        // Create language sub-menus
        const languageSubMenus = (languages || []).map((language, index) => ({
          key: language.key,
          label: language.label,
          href: null,
          position: index + 1,
          target: '_self'
        }));

        // Create the language selector menu item
        const languageSelector = {
          id: 'edcast|select_language',
          label: 'Select Language',
          href: null,
          position: 3, // Position it before Sign Out
          target: '_self',
          sub_menus: languageSubMenus
        };

        // Create a new array with profile menus and language selector
        let enhancedProfileMenus = [...(profile_menus || [])];

        // Find the sign out menu item
        const signOutIndex = enhancedProfileMenus.findIndex(item => item.key === 'edcast|sign_out');

        if (signOutIndex !== -1) {
          // Adjust the position of sign out if it exists
          enhancedProfileMenus[signOutIndex] = {
            ...enhancedProfileMenus[signOutIndex],
            position: 4 // Update position to be after language selector
          };

          // Insert language selector before sign out
          enhancedProfileMenus.splice(signOutIndex, 0, languageSelector);
        } else {
          // If sign out doesn't exist, just append language selector
          enhancedProfileMenus.push(languageSelector);
        }

        // Convert the enhanced profile menus to GalaxyMenuItem format
        const convertedProfileMenus = convertToGalaxyMenuItems(enhancedProfileMenus)?.[0];

        // Set Homepage to /feed or /home
        const homeTab = allTabs.find(t => t.href === '/');
        if (homeTab) {
          homeTab.href = LANDING_PAGE_V2_ENABLED ? '/home' : '/feed';
        }

        if (window.location.pathname === '/') {
          navigate(firstTabLink(allTabs[0]));
        }

        setSpecialMenuItemsIndex(specialItemsIndex);
        setNavItems(convertedNavItems);
        setProfileItems(convertedProfileMenus);
        setLanguages(languages || {});
        setUserDetails(user_details || []);
        setIsLoading(false);
      })
      .catch(error => {
        console.error(`ERROR!! on Header.init.getUserNavList: ${error}`);
        setIsLoading(false);
      });
  };

  const clearTranslatrStorage = () => {
    for (let i = 0; i < window.localStorage.length; i++) {
      let localKey = window.localStorage.key(i);
      if (localKey && localKey.includes('translatr')) {
        window.localStorage.removeItem(localKey);
        i = 0;
      }
    }
  };

  async function handleLanguageChange(lang) {
    let userObj = {
      profile_attributes: { language: lang }
    };
    let resp = await updateUser(window.__ED__.id, userObj).catch(err => {
      console.error('Error: Unable to update users language', err);
      alert('Unable to upate language');
    });
    if (resp) {
      // before change selected language and clear translations cache, we save the translation log.
      sendCSVTranslationLog();
      // Do not remove, clearing previous language translation json files.
      clearTranslatrStorage();
      window.localStorage.setItem('selectedLanguage', lang);
      window.location.reload();
    }
  }

  const isGlobalSearchOn = window?.__ED__?.isGlobalSearchEnabled || false;

  const defaultAvatarUrl = `${AWS_CDN_STATIC_ASSETS_HOST}/assets/new-anonymous-user-small.jpeg`;

  // Handler function for image error
  const handleImageError = e => {
    e.target.onerror = null;
    e.target.src = defaultAvatarUrl;
  };

  // Handler function for image load
  const handleProfileImageLoad = e => {
    e.target.style.display = 'inline-block';
    e.target.nextElementSibling.style.display = 'none';
  };

  // Handler function for trigger image load
  const handleTriggerImageLoad = e => {
    e.target.style.display = 'inline-block';
    e.target.nextElementSibling.style.display = 'none';
    e.target.parentElement.classList.add('profile-trigger__img');
  };

  return (
    <>
      <CreateSmartcard />
      <galaxy-header>
        <galaxy-logo slot="logo" href="/" alt={'logo'} src="images/csod-logo.png" />
        {!isLoading && navItems.length > 0 && (
          <galaxy-menu-bar
            ref={menuBarRef}
            slot="menu-bar"
            menuItems={JSON.stringify(navItems)}
            visibleItemsCount={4}
          />
        )}

        {isGlobalSearchOn && (
          <div className="mobile-screen-search">
            <Search />
          </div>
        )}
        <Notifications />

        {!isLoading && navItems.length > 0 && (
          <galaxy-menu-button
            ref={profileMenuRef}
            variant="profile"
            style={{ display: 'flex', placeContent: 'center' }}
            menuItems={JSON.stringify(profileItems)}
          >
            <div
              slot="header"
              class="profile-card"
              role="group"
              aria-labelledby="profile-card__name"
            >
              <img
                className="profile-card__image"
                src={userDetails?.[0]?.data}
                alt={translatr('cds.common.main', 'UserImage')}
                aria-hidden="true"
                onError={handleImageError}
                style={{ display: 'none' }}
                onLoad={handleProfileImageLoad}
              />
              <lego-icon
                shape="profile"
                size="56"
                style={{
                  display: 'inline-block',
                  marginRight: '12px',
                  flexShrink: 0,
                  width: '56px',
                  height: '56px'
                }}
              ></lego-icon>
              <div class="profile-card__info">
                <h2 id="profile-card__name" class="profile-card__name">
                  {window?.__ED__?.fullName || ''}
                </h2>
                <a class="profile-card__link" href="/me">
                  {translatr('web.common.main', 'MyProfile') || 'My Profile'}
                </a>
              </div>
            </div>
            <button class="profile-trigger" slot="trigger" aria-label="toggle profile button">
              <img
                className="profile-card__image"
                src={userDetails?.[0]?.data}
                alt={translatr('cds.common.main', 'UserImage')}
                aria-hidden="true"
                onError={handleImageError}
                style={{ display: 'none' }}
                onLoad={handleTriggerImageLoad}
              />
              <lego-icon
                shape="profile"
                size="24"
                style={{ display: 'inline-block', width: '24px', height: '24px' }}
              ></lego-icon>
            </button>
          </galaxy-menu-button>
        )}
      </galaxy-header>
    </>
  );
}
