import { Button } from 'centralized-design-system/src/Buttons';
import React from 'react';
import Downloadv2 from 'centralized-design-system/src/MUIComponents/icons/Downloadv2';
import { darkGrey } from 'centralized-design-system/src/MUIComponents/colors/colors';
import Card from 'centralized-design-system/src/Card/CardContainer';
import './FilePreview.scss';

interface FilePreviewProps {
  fileName: string;
  description?: string;
  onDownloadClick: () => void;
}

const styles = {
  downloadIcon: {
    width: '0.875rem',
    height: '0.75rem',
    padding: '0'
  }
};

const FilePreview = ({ fileName, description='', onDownloadClick }: FilePreviewProps) => {
  return (
    <Card additionalClasses={['file-preview-container']}>
      <div className="file-details">
        <div className="title">{fileName}</div>
        <div className="descrition">{description}</div>
      </div>
      <Button variant={'borderless'} onClick={onDownloadClick} color={'primary'}>
        <span
          tabIndex={-1}
          className={`hideOutline`}
        >
          <Downloadv2 style={styles.downloadIcon} color={darkGrey} fill={darkGrey} />
        </span>
      </Button>
    </Card>
  );
};

export default FilePreview;
