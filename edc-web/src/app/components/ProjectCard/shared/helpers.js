import { translatr } from 'centralized-design-system/src/Translatr';
import { Actions, ProjectStatus } from './types';
import { PROJECTS_CHANGE_STATUS_ON_START_DATE } from '@pages/TalentMarketplace/util';

export const getLabels = ({
  isApplicationRequired,
  isRemotePossible,
  locations,
  maxPositions,
  positionsFilled,
  divisions,
  visibleOrg
}) => {
  let openingsLabel = '';
  let locationsLabel = '';
  let remotePossibleLabel = '';
  let orgLabel = '';

  // openings label
  if (!isApplicationRequired) {
    openingsLabel = translatr('web.common.main', 'UnlimitedOpenings');
  } else {
    const openingsAvailable = maxPositions - positionsFilled;

    if (openingsAvailable === 1) {
      openingsLabel = translatr('web.projects.main', 'SingleOpening', {
        openings: openingsAvailable
      });
    } else {
      openingsLabel = translatr('web.projects.main', 'MultipleOpening', {
        openings: openingsAvailable
      });
    }
  }
  // location label
  if (locations && locations.length > 0) {
    if (locations.length === 1) {
      locationsLabel = locations[0].locationDisplayName ?? '';
    } else {
      locationsLabel = translatr('web.common.main', 'MultipleLocations');
    }
  }

  // remote possible label
  if (isRemotePossible) {
    remotePossibleLabel = translatr('web.common.main', 'RemotePossible');
  }

  //Organization label
  if (visibleOrg) {
    orgLabel = divisions?.find(item => item.orgType === visibleOrg)?.title;
  }

  return {
    locationsLabel,
    openingsLabel,
    remotePossibleLabel,
    orgLabel
  };
};

export const getPrimaryActionIdBasedOnStatus = (status, originalData) => {
  const projectStartDate = originalData?.meta?.startDate;
  const startTime = new Date(projectStartDate).setHours(0, 0, 0, 0);
  const currentTime = new Date().setHours(0, 0, 0, 0);
  switch (status) {
    case ProjectStatus.PENDING: {
      return Actions.WITHDRAW;
    }
    case ProjectStatus.APPROVED:
    case ProjectStatus.CONFIRMED: {
      if (projectStartDate && PROJECTS_CHANGE_STATUS_ON_START_DATE && startTime > currentTime)
        return;
      return Actions.START;
    }
    case ProjectStatus.INPROGRESS: {
      return Actions.COMPLETE;
    }
    case ProjectStatus.PENDINGAPPROVAL: {
      return Actions.WITHDRAW;
    }
    default: {
      return;
    }
  }
};
