import React, { useCallback, useEffect, useState } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import { withNoRenderErrorBoundary } from '@components/common/NoRenderErrorBoundary';
import TextClamp from '@components/TextClamp';
import IconButton from '@components/IconButton';
import ProjectThumbnail from '../shared/ProjectThumbnail';
import DropdownActions from '../shared/DropdownActions';
import { ComponentType, useCardActions, useLazyGetProjectActions } from '../shared/hooks';
import { Actions } from '../shared/types';
import Metadata from './Metadata';
import { getLabels } from '../shared/helpers';
import '../shared/styles.scss';
import './styles.scss';
import {
  LOCATION_ASSOCIATION,
  LOCATION_FIELDS,
  LOCATION_USAGE_OPTIONS,
  isLocationFieldVisible
} from '../../../pages/TalentMarketplace/helpers';
import { generateOpportuintyTitleId } from '@pages/TalentMarketplace/shared/helpers/helpers';

const ProjectCard = ({
  className,
  defaultBookmarked = false,
  defaultDismissed,
  dismissable,
  dispatch,
  filterActions = [],
  id,
  isApplicationRequired,
  isRemotePossible = false,
  loading,
  locations,
  maxPositions,
  onBookmark,
  onClose,
  onComplete,
  onDelete,
  onDismissSuccess,
  onWithdraw,
  originalData = {},
  positionsFilled = 0,
  useHistoryPush,
  title,
  thumbnail = {},
  divisions = [],
  organizations,
  locationsEnabled,
  locationsAssociation,
  locationsVisibility,
  cardRole
}) => {
  const navigate = useNavigate();
  const [visibleOrg, setVisibleOrg] = useState('');
  const [
    getProjectActionIds,
    { called: projectActionsCalled, loading: cardActionsLoading, data: projectActionIds }
  ] = useLazyGetProjectActions({ id, dismissable, dispatch });
  const isLocationShown =
    locations?.length > 0 &&
    locationsEnabled &&
    locationsAssociation.includes(LOCATION_ASSOCIATION.PROJECT) &&
    isLocationFieldVisible(
      locationsVisibility,
      LOCATION_FIELDS.NAME,
      LOCATION_USAGE_OPTIONS.PROJECT_CARD
    );

  useEffect(() => {
    if (organizations.get('config')?.enable) {
      setVisibleOrg(organizations.get('config')?.project_card);
    }
  }, [organizations.get('config')?.enable]);

  const { actions, bookmarked, completed, disabled, dismissed, setBookmarked } = useCardActions({
    id,
    bookmarked: defaultBookmarked,
    component: ComponentType.CARD,
    data: originalData,
    dismissed: defaultDismissed,
    dispatch,
    onBookmark,
    onClose,
    onComplete,
    onDelete,
    onDismissSuccess,
    onWithdraw,
    projectTitle: title,
    push: useHistoryPush ? navigate : null,
    isApplicationRequired
  });

  const { locationsLabel, openingsLabel, remotePossibleLabel, orgLabel } = getLabels({
    isApplicationRequired,
    isRemotePossible,
    locations,
    maxPositions,
    positionsFilled,
    divisions,
    visibleOrg
  });

  const handleActionCall = useCallback(() => {
    if (projectActionsCalled) {
      return;
    }

    getProjectActionIds();
  }, [projectActionsCalled, projectActionIds]);

  useEffect(() => {
    setBookmarked(defaultBookmarked);
  }, [defaultBookmarked]);

  const filteredActionIds = projectActionIds.filter(actionId => !filterActions.includes(actionId));
  let dropdownActions = filteredActionIds.map(actionId => actions[actionId]).filter(a => !!a);

  if (completed) {
    dropdownActions = dropdownActions.filter(action => action.id !== Actions.COMPLETE);
  }
  const onTitleKeyDown = event => {
    if (event.key === 'Enter' || event.key === ' ') {
      actions[Actions.VIEW].onClick();
    }
  };

  return (
    <>
      {/* eslint-disable jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}
      <div
        aria-labelledby={`tm__project-card-header-title-${id}`}
        className={cx(
          'tm__project-card',
          { 'cursor-pointer': !loading, loading, disabled },
          className
        )}
        onClick={!loading && !disabled ? actions[Actions.VIEW].onClick : undefined}
        onMouseOver={!loading ? handleActionCall : undefined}
        onFocus={!loading ? handleActionCall : undefined}
        role={cardRole}
      >
        {/* eslint-enable jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}
        <div>
          <div className={cx('tm__project-card-header', { loading })}>
            {loading ? (
              <Skeleton width="100%" />
            ) : (
              <>
                <a
                  href={`/career/detail/${OPPORTUNITY_TYPE.PROJECT}/${id}`}
                  onClick={e => e.stopPropagation()}
                  onKeyDown={onTitleKeyDown}
                  tabIndex={0}
                  id={generateOpportuintyTitleId(id)}
                >
                  <TextClamp Component="h3" className="tm__project-card-title" line={2}>
                    {title}
                  </TextClamp>
                </a>
                <DropdownActions
                  actions={dropdownActions}
                  disabled={disabled}
                  isLoading={cardActionsLoading}
                  ariaLabel={translatr('web.common.main', 'MoreOpportunityActions', {
                    opportunity: title
                  })}
                />
              </>
            )}
          </div>
          <ProjectThumbnail loading={loading} thumbnail={thumbnail} />
          <ul className="tm__project-card-metadata-container m-margin-top l-margin-bottom">
            <Metadata
              icon="users-audience"
              id="project-application-required"
              label={openingsLabel}
              loading={loading}
            />
            {orgLabel ? (
              <Metadata
                icon="tree-graph"
                id="project-custom-organization-unit"
                label={orgLabel}
                loading={loading}
              />
            ) : (
              isLocationShown &&
              locationsLabel && (
                <Metadata
                  icon="map-marker"
                  id="project-locations"
                  label={locationsLabel}
                  loading={loading}
                />
              )
            )}
            {remotePossibleLabel && (
              <Metadata
                icon="globe"
                id="project-remote-work-possible"
                label={remotePossibleLabel}
                loading={loading}
              />
            )}
          </ul>
        </div>
        <ul
          className="tm__project-card-actions-container"
          aria-label={`${omp('tm_tm_project')} ${translatr('web.projects.main', 'Actions')}`}
        >
          <li>
            <IconButton
              disabled={disabled}
              icon="share1"
              aria-label={translatr('web.projects.main', 'ShareTitle', { title })}
              loading={loading}
              title={translatr('web.common.main', 'Share')}
              onClick={actions[Actions.SHARE].onClick}
              size="large"
            />
          </li>
          <li>
            <IconButton
              disabled={disabled}
              icon={bookmarked ? 'bookmark-fill' : 'bookmark'}
              loading={loading}
              aria-label={
                bookmarked
                  ? translatr('web.projects.main', 'UnbookmarkTitle', { title })
                  : translatr('web.projects.main', 'BookmarkTitle', { title })
              }
              title={
                bookmarked
                  ? translatr('web.common.main', 'Unbookmark')
                  : translatr('web.common.main', 'Bookmark')
              }
              onClick={actions[Actions.BOOKMARK].onClick}
              size="large"
            />
          </li>
          {dismissable && (
            <li>
              <IconButton
                className={cx({ selected: dismissed })}
                disabled={disabled}
                icon="ban"
                loading={loading}
                aria-label={
                  dismissed
                    ? translatr('web.projects.main', 'UndismissTitle', { title })
                    : translatr('web.projects.main', 'DismissTitle', { title })
                }
                title={
                  dismissed
                    ? translatr('web.common.main', 'Undismiss')
                    : translatr('web.common.main', 'Dismiss')
                }
                onClick={actions[Actions.DISMISS].onClick}
                size="large"
              />
            </li>
          )}
        </ul>
      </div>
    </>
  );
};

ProjectCard.propTypes = {
  className: PropTypes.string,
  defaultBookmarked: PropTypes.bool,
  defaultDismissed: PropTypes.bool,
  dismissable: PropTypes.bool,
  dispatch: PropTypes.func,
  filterActions: PropTypes.arrayOf(PropTypes.string),
  id: PropTypes.string.isRequired,
  isApplicationRequired: PropTypes.bool,
  isRemotePossible: PropTypes.bool,
  loading: PropTypes.bool,
  locations: PropTypes.arrayOf(PropTypes.object),
  maxPositions: PropTypes.number,
  onBookmark: PropTypes.func,
  onClose: PropTypes.func,
  onComplete: PropTypes.func,
  onDelete: PropTypes.func,
  onDismissSuccess: PropTypes.func,
  onWithdraw: PropTypes.func,
  originalData: PropTypes.object,
  positionsFilled: PropTypes.number,
  useHistoryPush: PropTypes.bool,
  title: PropTypes.string.isRequired,
  thumbnail: PropTypes.shape({
    blur: PropTypes.string,
    url: PropTypes.string.isRequired
  }).isRequired,
  divisions: PropTypes.array,
  organizations: PropTypes.object,
  locationsEnabled: PropTypes.bool,
  locationsAssociation: PropTypes.array,
  locationsVisibility: PropTypes.object,
  cardRole: PropTypes.string
};

export default connect(
  ({ organizations, locationsConfiguration }) => ({
    organizations,
    locationsEnabled: locationsConfiguration.get('enable'),
    locationsAssociation: locationsConfiguration.get('association') || [],
    locationsVisibility: locationsConfiguration.get('visibility')
  }),
  dispatch => ({
    dispatch
  })
)(withNoRenderErrorBoundary(ProjectCard));
