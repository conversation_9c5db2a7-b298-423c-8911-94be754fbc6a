import { <PERSON><PERSON><PERSON>oa<PERSON> } from 'centralized-design-system/src/MfeLoader';

export function isMfeEnabled() {
  return (
    !!window.__edOrgData?.configs?.find?.(config => config.name === 'mfe_tms_config')?.value ||
    false
  );
}

export function openSkillFlyout(skillInfo) {
  const userId = window.csod?.context?.user;

  if (!userId) {
    console.log('No CSOD User ID');
    return false;
  }

  let sklId;
  try {
    if (skillInfo.topicExternalData) {
      sklId = skillInfo.topicExternalData.filter(o => o.externalSource === 'csx')[0].id;
    }
    if (skillInfo.topic_external_data) {
      sklId = skillInfo.topic_external_data.filter(o => o.external_source === 'csx')[0].id;
    }
    if (skillInfo.skill_external_data) {
      sklId = skillInfo.skill_external_data.filter(o => o.external_source === 'csx')[0].id;
    }
    if (skillInfo.external_data) {
      sklId = skillInfo.external_data.filter(o => o.external_source === 'csx')[0].id;
    }
    if (skillInfo.externalData) {
      sklId = skillInfo.externalData.filter(o => o.external_source === 'csx')[0].id;
    }
  } catch (err) {
    console.error('Unable to get SKL-ID', err);
  }

  if (!sklId) {
    console.log('No SKL-ID');
    return false;
  }

  try {
    window.csod._services.mfeEventBus.publish('perf.skillsprofile.mfe:open-capability-flyout', {
      capabilityId: sklId,
      userId: userId
    });
  } catch (err) {
    console.error('Unable to open Skills Mfe Flyout', err);
  }
}

const MfeSkillsFlyout = () => {
  const MfeEnabled = window.__edOrgData.configs.find(config => config.name === 'mfe_tms_config')
    ?.value;

  if (!MfeEnabled) {
    return null;
  }

  const ErrorRenderNull = () => <></>;

  return (
    <MfeLoader
      baseUrl="skills-details-widget"
      name="skills-details-widget"
      errorComponent={ErrorRenderNull}
    />
  );
};

export default MfeSkillsFlyout;
