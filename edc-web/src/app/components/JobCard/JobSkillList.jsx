import { useMemo } from 'react';
import { array, number, string, bool, object, func } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import cn from 'classnames';
import './JobSkillList.scss';
import { Button } from 'centralized-design-system/src/Buttons';
import TextClamp from 'centralized-design-system/src/TextClamp';
import { ClickableTag } from 'centralized-design-system/src/Tags';

const JobSkill = ({ tagName = 'li', active, skill, clickHandler, tooltip }) => {
  const CustomTag = `${tagName}`;
  const label = skill.label || skill.name || skill.id;

  if (clickHandler) {
    return (
      <CustomTag key={skill.id}>
        <ClickableTag callback={() => clickHandler(skill)} tagName={label} />
      </CustomTag>
    );
  }

  return (
    <CustomTag
      key={skill.id}
      data-test-id={`skill-tag-${skill.label || skill.name || skill.id}`}
      className={cn('job-skill__skills__chip', {
        'job-skill__skills__chip--active': active === skill.id
      })}
      {...(clickHandler && { onClick: () => clickHandler(skill) })}
    >
      {tooltip ? <TextClamp>{label}</TextClamp> : label}
    </CustomTag>
  );
};

JobSkill.propTypes = {
  tagName: string,
  active: string,
  skill: object,
  clickHandler: func,
  tooltip: bool
};

const JobSkillList = ({
  skills = [],
  chipToShow = 2,
  noSkillsText = null,
  textMode,
  active,
  isExpanded,
  jobTitle,
  id,
  clickHandler,
  onMoreClick
}) => {
  const lengthOfRemainingSkills = skills.length - chipToShow;
  const skillsList = isExpanded ? skills : skills.slice(0, chipToShow);
  const ulKey = `skills-list-${id}`;

  const moreButtonLabel = isExpanded
    ? translatr('web.common.main', 'ShowLess')
    : `+ ${translatr('web.common.main', 'LengthofremainingskillsMore', {
        lengthOfRemainingSkills
      })}`;
  const moreButtonAriaLabel = isExpanded
    ? `${translatr('web.common.main', 'ShowLessSkillsOpportunity', { opportunity: jobTitle })}`
    : `${translatr('web.common.main', 'MoreSkillsForOpportunity', {
        nmbOfSkills: lengthOfRemainingSkills,
        opportunity: jobTitle
      })}`;

  const skillsToShow = useMemo(() => {
    return skills.length ? (
      <>
        {skillsList.map(skill => (
          <JobSkill
            skill={skill}
            active={active}
            clickHandler={clickHandler}
            key={`job_skill_${skill.id}`}
            tooltip={!textMode}
          />
        ))}
        {lengthOfRemainingSkills > 0 && (
          <li className="job-skill__skills--remaining">
            <Button
              variant="borderless"
              color="primary"
              padding="xsmall"
              size={textMode ? 'medium' : 'large'}
              data-testid="omp-more-skills"
              aria-label={moreButtonAriaLabel}
              aria-controls={ulKey}
              aria-expanded={isExpanded}
              onClick={onMoreClick}
            >
              {moreButtonLabel}
            </Button>
          </li>
        )}
      </>
    ) : (
      <li className="job-skill__skills--no-skills">{noSkillsText}</li>
    );
  }, [isExpanded, skills, chipToShow, active]);

  return (
    <div>
      {textMode && <span>{translatr('web.common.main', 'Skills')}: </span>}
      <ul
        id={ulKey}
        className={cn('job-skill__skills', {
          'text-mode': textMode
        })}
      >
        {skillsToShow}
      </ul>
    </div>
  );
};

JobSkillList.propTypes = {
  skills: array,
  chipToShow: number,
  noSkillsText: string,
  textMode: bool,
  active: string,
  isExpanded: bool,
  jobTitle: string,
  id: string,
  clickHandler: func,
  onMoreClick: func
};

export default JobSkillList;
export { JobSkill };
