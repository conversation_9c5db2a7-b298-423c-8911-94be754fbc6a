@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.job-skill {
  &__skills {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 0;
    color: var(--ed-text-color-primary);
    gap: var(--ed-spacing-2xs);

    .ed-tag-container.clickable {
      margin-right: 0;
    }

    &__chip {
      display: flex;
      flex: 0;
      align-items: center;
      border: var(--ed-tag-border-size) solid var(--ed-state-disabled-color);
      border-radius: var(--ed-tag-border-radius);
      font-size: var(--ed-tag-font-size);
      line-height: var(--ed-line-height-sm);
      padding: var(--ed-tag-padding-y) var(--ed-tag-padding-x);
      height: fit-content;
      min-width: fit-content;
      word-break: break-word;
      background-color: var(--ed-tag-bg-color);
      font-weight: var(--ed-tag-font-weight);
      scroll-snap-align: start;
      &--active {
        color: var(--ed-tag-bg-color);
        background-color: var(--ed-primary-base);
        border-color: var(--ed-primary-base);
      }
      button {
        font-size: var(--ed-font-size-lg);
        width: rem-calc(2);
        height: rem-calc(20);
        cursor: pointer;
        margin-left: var(--ed-spacing-2xs);
        &:hover {
          color: var(--ed-primary-base);
        }
        i {
          margin: 0 !important;
          padding: 0 !important;
        }
      }
    }

    &--remaining,
    &--no-skills {
      align-self: center;
      color: var(--ed-text-color-supporting);
    }

    &--no-skills {
      font-style: italic;
    }

    &.text-mode {
      display: inline;
      color: var(--ed-text-color-supporting);

      .job-skill__skills--remaining,
      .job-skill__skills--no-skills,
      .job-skill__skills__chip {
        line-height: 1.2rem;
        display: inline;
        padding-left: 0;
        margin: 0;
        font-size: var(--ed-font-size-sm);
        font-weight: var(--ed-font-weight-normal);
      }
      .job-skill__skills__chip {
        background-color: transparent;
        border: 0;
        padding: 0 rem-calc(3) 0 0;
        &:not(:last-of-type):after {
          content: ', ';
        }
      }
    }

    div.job-skills-block & {
      margin-top: var(--ed-spacing-2xs);
    }
  }

  &__group {
    margin: rem-calc(14) 0 0 0;
    &__label {
      line-height: normal;
      color: var(--ed-neutral-3);
      font-size: var(--ed-font-size-base) !important;
      font-weight: var(--ed-font-weight-normal);
    }
  }
}

.job-skills {
  button.ed-link-secondary {
    color: var(--ed-text-color-supporting);
  }
}
