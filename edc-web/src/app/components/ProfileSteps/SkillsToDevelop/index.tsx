import React, { useEffect, useState } from "react";
import { useSelector } from 'react-redux';
import Loading from "centralized-design-system/src/Loading";
import { users } from "edc-web-sdk/requests";
import "./styles.scss";
import { mapLearningGoalsPayload, mapLearningTopics } from '@components/ProfileSteps/SkillsToDevelop/mapper';
import UserSkillsFrom from "@components/UserSkillsForm/UserSkillsFrom";
import { mapSkillsBeforeChange, mapSkillsBeforeDelete } from '@components/UserSkillsForm/skillsManipulation';
import { SkillsFormData } from '@components/UserSkillsForm/types';

interface SkillsToDevelopProps {
  setComponentData(componentData: any): void,
  setComponentsPayload(componentsPayload: any): void,
  setInitialComponentData?(initialComponentData: any): void,
  selectedSkillsLimit?: number,
  handleFocus?(): void,
  handleMenuClose?(): void
  setIsSaveButtonDisabled?(show: boolean): void
}

const SkillsToDevelop: React.FC<SkillsToDevelopProps> = ({
   setComponentData,
   setComponentsPayload,
   setInitialComponentData = () => {},
   selectedSkillsLimit = undefined,
   handleFocus,
   handleMenuClose,
   setIsSaveButtonDisabled
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [skillsData, setSkillsData] = useState<SkillsFormData>({ selected: {}});

  const userId = useSelector((state: any) => state.currentUser.get("id"));

  useEffect(() => {
    users.getUserById(userId)
      .then((res: { profile: { learningTopics: Array<any> } }) => {
        const learningGoals = mapLearningTopics(res?.profile?.learningTopics);
        setSkillsData({ ...skillsData, selected: learningGoals });
        setInitialComponentData((prev: any) => ({
          ...prev,
          learning_goals: learningGoals
        }));
      }).catch((err: any) => {
        console.error(err);
      }).finally(() => {
        setLoading(false)
    });
  }, []);

  useEffect(() => {
    setComponentData((prev: any) => ({ ...prev, learning_goals: skillsData?.selected }));
    setComponentsPayload((prev: any) => ({ ...prev, learning_goals: mapLearningGoalsPayload(skillsData?.selected) }));
  }, [skillsData]);

  const onSkillsChange = (skills: Array<any>) => {
    const { selected } = mapSkillsBeforeChange(skills, skillsData);
    return setSkillsData({ selected });
  };

  const onSkillDelete = (topicId: string, proficiencyLevel: string) => {
    const { selected } = mapSkillsBeforeDelete(skillsData, topicId, proficiencyLevel)
    return setSkillsData({ selected });
  };

  return (
    <div className="cyp-skills-to-develop">
      {loading ? (
        <Loading />
      ) : (
        <UserSkillsFrom
          skillsData={skillsData}
          onSkillsChange={onSkillsChange}
          onSkillDelete={onSkillDelete}
          selectedSkillsLimit={selectedSkillsLimit}
          handleFocus={handleFocus}
          handleMenuClose={handleMenuClose}
          setIsSaveButtonDisabled={setIsSaveButtonDisabled}
        />
      )}
    </div>
  );
};
export default SkillsToDevelop;
