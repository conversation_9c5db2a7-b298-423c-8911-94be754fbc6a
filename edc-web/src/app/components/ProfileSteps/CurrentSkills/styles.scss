@import '~centralized-design-system/src/Styles/_variables.scss';

.current-skills-container {
  margin-bottom: 40px;

  .skills-container {
    .add-skills-you-have-label {
      display: none;
    }

    .skill-with-proficiency-level {
      margin-top: var(--ed-spacing-sm);

      &__title {
        margin-bottom: var(--ed-spacing-4xs);
        color: var(--ed-text-color-primary);
        font-size: var(--ed-font-family-base);
      }

      &__hint {
        font-size: var(--ed-font-size-supporting);
        margin-bottom: var(--ed-spacing-3xs);
        margin-top: var(--ed-spacing-3xs);
      }
    }

    .ed-multi-select {
      &__control--is-focused {
        border-color: var(--ed-text-color-primary) !important;
      }

      .ed-multi-select__multi-value {
        align-items: center;

        .ed-multi-select__multi-value__label {
          text-transform: capitalize;
          padding: 0.1rem 0.4rem 0.15rem 0.6rem;
        }

        .ed-multi-select__multi-value__remove {
          padding: 0 0.6rem 0 0;

          svg {
            cursor: pointer;
            color: var(--ed-text-color-supporting);
            border-radius: var(--ed-border-radius-circle);
            border: 0.0125rem solid var(--ed-text-color-supporting);
          }

          svg:hover {
            color: var(--ed-negative-darken-3);
            border-color: var(--ed-negative-darken-3);
          }
        }
      }
    }
    .skills-without-level {
      &__title {
        color: var(--ed-text-color-primary);
        margin-bottom: var(--ed-spacing-4xs);
      }

      &__content {
        border: var(--ed-border-size-sm) solid var(--ed-border-color);
        border-radius: var(--ed-border-radius-lg);
        padding: var(--ed-spacing-5xs) var(--ed-spacing-2xs);
        display: flex;
        flex-wrap: wrap;

        &__tag-wrapper {
          display: flex;
          align-items: center;
          margin: 0 var(--ed-spacing-5xs);
          text-transform: capitalize;

          .ed-tag-container {
            display: flex;
            flex-direction: row !important;
            align-items: center;
            padding: 0.1rem 0.6rem 0.15rem 0.6rem;
            border-color: var(--ed-border-color);

            .tag-close-icon {
              display: flex;
              padding: 0;
              margin: 0 0 0 0.4rem;
              border-radius: var(--ed-border-radius-circle);
              border: 0.0125rem solid var(--ed-text-color-supporting);

              .icon-x-mark-Close {
                font-size: 0.65rem;
                font-weight: var(--ed-font-weight-bold);
                border-radius: var(--ed-border-radius-circle);
                padding: 0.1rem;
                margin: 0;
                color: var(--ed-text-color-supporting);
              }

              .icon-x-mark-Close:hover {
                color: var(--ed-negative-darken-3);
              }
            }

            .tag-close-icon:hover {
              border-color: var(--ed-negative-darken-3);
            }
          }
        }
      }
    }

    .search-select-container {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding-bottom: var(--ed-spacing-3xl);
      gap: var(--ed-spacing-base);

      @include mobile() {
        display: block;
      }

      .level-btn-container {
        display: flex;
        align-items: flex-end;
        gap: var(--ed-spacing-base);

        @include mobile() {
          margin-top: var(--ed-spacing-base);
        }

        > button {
          width: rem-calc(100);
          height: rem-calc(38);
          @include mobile() {
            width: 50%;
          }
        }
      }

      .ed-input-container {
        &:first-of-type {
          flex: 3;
          @include mobile() {
            width: 100%;
          }
        }
        &:last-of-type {
          flex: 2;
          @include mobile() {
            flex: 1;
          }
        }
      }
    }

    .level-skills-container {
      display: flex;
      margin-top: var(--ed-spacing-base);
      margin-bottom: var(--ed-spacing-3xl);

      @include mobile() {
        display: block;
      }

      > div {
        width: 33%;
        @include mobile() {
          width: 100%;
        }

        .level {
          display: flex;
          justify-content: center;
          gap: var(--ed-spacing-4xs);

          @include mobile() {
            justify-content: left;
          }

          > span:last-of-type {
            font-size: var(--ed-font-size-supporting);
            color: var(--ed-text-color-primary);
            padding-left: var(--ed-spacing-2xs);
          }
          .icon-oval-fill::before {
            font-size: 0.25rem;
            color: var(--ed-state-active-color);
          }

          .icon-oval::before {
            font-size: 0.25rem;
          }
        }

        .selected-skills {
          text-align: center;
          min-height: rem-calc(100);
          padding: var(--ed-spacing-base) var(--ed-spacing-4xs) 0 var(var(--ed-spacing-4xs));

          @include mobile() {
            display: flex;
            flex-wrap: wrap;
            min-height: rem-calc(50);
          }

          .ed-tooltip {
            width: 96%;
            @include mobile() {
              width: auto;
            }
          }

          .ed-tag-container:hover {
            background-color: var(--ed-neutral-7);
            border-color: var(--ed-negative-1);
            .icon-x-mark-Close {
              color: var(--ed-negative-1);
            }
          }
        }
      }
      > div {
        &:first-of-type {
          @include mobile() {
            margin-bottom: var(--ed-spacing-2xs);
            padding-bottom: var(--ed-spacing-2xs);
            border-bottom: var(--ed-border-size-sm) dashed var(--ed-border-color);
          }
        }

        &:nth-of-type(2) {
          border-left: var(--ed-border-size-sm) dashed var(--ed-border-color);
          border-right: var(--ed-border-size-sm) dashed var(--ed-border-color);
          @include mobile() {
            &:nth-of-type(2) {
              border-left: none;
              border-right: none;
              padding-bottom: var(--ed-spacing-2xs);
              margin-bottom: var(--ed-spacing-2xs);
              border-bottom: var(--ed-border-size-sm) dashed var(--ed-border-color);
            }
          }
        }
      }
    }

    .unknown-level-skills-container {
      padding: 0;

      > label:first-of-type {
        margin-bottom: 0;
      }

      .selected-unlevel-skills {
        padding: var(--ed-spacing-base) var(--ed-spacing-4xs) 0 var(--ed-spacing-4xs);

        .ed-tag-container:hover {
          background-color: var(--ed-neutral-7);
          border-color: var(--ed-negative-1);
          .icon-x-mark-Close {
            color: var(--ed-negative-1);
          }
        }
      }
    }

    .max-limit-skills-label {
      margin-top: var(--ed-spacing-base);
    }
  }
}
