import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import _ from 'lodash';
import { getUserPassport } from 'edc-web-sdk/requests/users';
import Loading from 'centralized-design-system/src/Loading';
import UserSkillsFrom from '@components/UserSkillsForm/UserSkillsFrom';
import {  mapSkillsBeforeChange, mapSkillsBeforeDelete } from '@components/UserSkillsForm/skillsManipulation';
import { getUserSkills } from '@components/UserSkillsForm/rest';
import {
  EMPTY_SKILLS_DATA,
  getSkillProficiencyLevels,
  getSkillProficiencyLevelWithNoLevel, isNoLevelSkillsVisible
} from '@components/UserSkillsForm/utils';
import { SkillsFormData, SkillsGroupedByLevel, ProficiencyLevel } from '@components/UserSkillsForm/types';
import { mapSkillsDataToPayload } from './helper';
import './styles.scss';

const mergeSkills = (locallySelectedSkills: SkillsGroupedByLevel, selectedAndSaved: SkillsGroupedByLevel ): SkillsGroupedByLevel => {
  const mergedSkills: SkillsGroupedByLevel = {};

  const skillLevels = isNoLevelSkillsVisible()
    ? getSkillProficiencyLevelWithNoLevel(!window?.__edOrgData?.displayNoLevelInProficiencyLevels).map((level: ProficiencyLevel) => level.value)
    : getSkillProficiencyLevels().map((level: ProficiencyLevel) => level.value);

  skillLevels.forEach((level: string) => {
    const combinedSkills = [...(locallySelectedSkills[level] || []), ...(selectedAndSaved[level] || [])];
    mergedSkills[level] = _.uniqBy(combinedSkills, 'topicId');
  });
  return mergedSkills;
}

interface CurrentSkillsProps {
  componentData: any,
  setComponentData(componentData: any): void,
  componentsPayload: any,
  setComponentsPayload(componentsPayload: any): void,
  setCtaLoading?(ctaLoading: boolean): void,
  setInitialComponentData?(initialComponentData: any): void,
  setIsSaveButtonDisabled?(buttonDisabled: boolean): void,
  selectedSkillsLimit?: number,
  handleFocus?(): void,
  handleMenuClose?(): void
}

const CurrentSkills: React.FC<CurrentSkillsProps> = ({
  setComponentData,
  componentData,
  setComponentsPayload,
  componentsPayload,
  setCtaLoading = () => {},
  setInitialComponentData,
  selectedSkillsLimit = undefined,
  handleFocus,
  handleMenuClose,
  setIsSaveButtonDisabled
}) => {
  const userId = useSelector((state: any) => state.currentUser.get("id"));

  const [loading, setLoading] = useState(true);
  const [skillsData, setSkillsData] = useState<SkillsFormData>(componentData['current_skills'] || EMPTY_SKILLS_DATA);
  const [persistedSkills, setPersistedSkills] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    getUserPassport(userId)
      .then((response: any) => {
        const { selected, persisted } = getUserSkills(response.skills);
        setPersistedSkills(persisted);
        const mergedSelected: SkillsGroupedByLevel = mergeSkills(skillsData.selected, selected)
        setSkillsData({ ...skillsData, selected: mergedSelected });
        setInitialComponentData((prev: any) => ({
          ...prev,
          current_skills: {
            ...EMPTY_SKILLS_DATA,
            selected
          }
        }));
      }).catch((error: any) => {
        console.error(error);
      }).finally(() => {
        setLoading(false);
    });
  }, []);

  useEffect(() => {
    setCtaLoading(loading);
  }, [loading]);

  useEffect(() => {
    setComponentData((prev: any) => ({ ...prev, current_skills: skillsData }));
    setComponentsPayload({ ...componentsPayload, current_skills: mapSkillsDataToPayload(skillsData)});
  }, [skillsData]);

  const onSkillsChange = (skills: Array<any>) => {
    const { selected, created, updated, deleted } = mapSkillsBeforeChange(skills, skillsData, persistedSkills);
    return setSkillsData({ selected, created, updated, deleted });
  };

  const onSkillDelete = (topicId: string, proficiencyLevel: string) => {
    return setSkillsData(mapSkillsBeforeDelete(skillsData, topicId, proficiencyLevel));
  };

  return (
    <div className="current-skills-container">
      {loading ? (
        <Loading />
      ) : (
        <UserSkillsFrom
          skillsData={skillsData}
          onSkillsChange={onSkillsChange}
          onSkillDelete={onSkillDelete}
          selectedSkillsLimit={selectedSkillsLimit}
          handleFocus={handleFocus}
          handleMenuClose={handleMenuClose}
          setIsSaveButtonDisabled={setIsSaveButtonDisabled}
        />
      )}
    </div>
  );
};

export default CurrentSkills;
