import React, { useContext } from 'react';
import RolesCarousel from '@components/RolesCarousel';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import './YourAspirationalRolesCarousel.scss';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { useSelector } from 'react-redux';
import { mapJobRoles } from '@pages/TalentMarketplace/Api';
import { useIsDevelopmentPlanEnabled } from '@pages/TalentMarketplace/DevelopmentPlan/hooks/useIsDevelopmentPlanEnabled';

const YourAspirationalRolesCarousel: React.FC<{}> = () => {
  const { aspirations, isAspirationsLoading } = useContext(AspirationsContext);
  const isCareerPathEnabled = useSelector(
    (state: any) => state.talentmarketplaceReducer.get('generalConfig')?.isCareerPathEnabled
  );
  const { showDevelopmentPlan } = useIsDevelopmentPlanEnabled();

  return (
    <div className="your-aspirational-roles-block">
      <RolesCarousel
        fullCard
        hideShowAll
        loading={isAspirationsLoading}
        dismissable={false}
        roles={mapJobRoles(aspirations)}
        title={translatr('web.talentmarketplace.main', 'YourAspirationalRolesConfigurable', {
          tm_aspirational_roles: omp('tm_tm_aspirational_roles')
        })}
        showCounter={false}
        showDevelopmentPlan={showDevelopmentPlan}
        showDevelopmentPlanLink={true}
        hideSocialButtons={true}
        customPlaceholder={
          <EmptyState
            icon="icon-file"
            title={translatr('web.talentmarketplace.main', 'NoAspirationalRoleConfigurable', {
              tm_aspirational_role: omp('tm_tm_aspirational_role')
            })}
            description={translatr(
              'web.talentmarketplace.main',
              'SelectAspirationalRoleDescription',
              {
                role: omp('tm_job_role')
              }
            )}
            buttonLabel={
              isCareerPathEnabled
                ? translatr('web.landingpage.main', 'CareerMilestoneWidgetFooterCTA')
                : translatr('web.talentmarketplace.career-path', 'SeeAllRoles', {
                    items: omp('tm_tm_job_roles')
                  })
            }
            buttonLink={isCareerPathEnabled ? '/career/career-path' : '/career/job-roles'}
          />
        }
      />
    </div>
  );
};

export default YourAspirationalRolesCarousel;
