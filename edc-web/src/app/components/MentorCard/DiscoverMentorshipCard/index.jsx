import React from 'react';
import { connect } from 'react-redux';
import cx from 'classnames';
import PropTypes from 'prop-types';
import Avatar from 'centralized-design-system/src/Avatar';
import { MENTORSHIP_STATUS, OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import TagsWithXmore from 'centralized-design-system/src/TagsWithXmore';
import { openRequestMentorshipModal, openViewCommentModal } from 'actions/modalActions';
import { withNoRenderErrorBoundary } from '@components/common/NoRenderErrorBoundary';
import DropdownActions from '@components/ProjectCard/shared/DropdownActions';
import TextClamp from '@components/TextClamp';
import { getActionIds, useCardActions } from '../helpers';
import MentorCardSkeleton from '../shared/MentorCardSkeleton';
import ApplicationStatusLabel from '../shared/ApplicationStatusLabel';

import '@components/ProjectCard/shared/styles.scss';
import './styles.scss';
import remCalc from 'centralized-design-system/src/Utils/remCalc';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { translatr, omp } from 'centralized-design-system/src/Translatr/utils';
import unescape from 'lodash/unescape';
import { openSkillFlyout, isMfeEnabled } from '@components/MfeSkillsFlyout';

const DiscoverMentorshipCard = ({
  avatarUrl,
  className,
  currentUserId,
  id,
  userId,
  name,
  position,
  comment,
  skills = [],
  status: initialStatus,
  handle,
  dispatch,
  loading,
  openModal,
  originalData,
  cardRole
}) => {
  const [status, setStatus] = React.useState(initialStatus);
  const requestMentorshipButtonRef = React.useRef();

  const modalMetaData = {
    id,
    userId,
    name: comment?.userId?.fullName,
    avatarUrl: comment?.userId?.avatarimages.medium,
    position: unescape(comment?.userId?.profile?.jobTitle),
    date: comment?.date,
    status,
    comment: comment?.comment
  };

  const { actions, onViewMentorProfile } = useCardActions({
    id,
    dispatch,
    handle,
    onViewComment: () => openModal(modalMetaData),
    data: originalData
  });

  const onRequestClick = React.useCallback(e => {
    e.stopPropagation();
    dispatch(
      openRequestMentorshipModal(
        id,
        name,
        () => setStatus(MENTORSHIP_STATUS.PENDING),
        originalData,
        requestMentorshipButtonRef.current
      )
    );
  }, []);

  const dropdownActions = getActionIds(status, true, comment?.comment)
    .map(actionId => actions[actionId])
    .filter(a => !!a);

  if (loading) {
    return <MentorCardSkeleton />;
  }

  const isMentoring = status === MENTORSHIP_STATUS.ACCEPTED;
  const isSelf = currentUserId === `${userId}`;
  const visibleSkills = skills?.filter(skill => !!skill?.label) || [];

  const screenSize = useWindowSize();

  if (!name) {
    return null;
  }

  const UserProfileLink = `/career/detail/${OPPORTUNITY_TYPE.MENTORSHIP}/${id}`;
  const onTitleKeyDown = event => {
    if (event.key === 'Enter' || event.key === ' ') {
      onViewMentorProfile();
    }
  };

  return (
    <>
      {/* eslint-disable jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}
      <div
        aria-labelledby={`tm__mentorship-card-name-${id}`}
        className={cx(' tm__project-card tm__mentorship-card cursor-pointer', className)}
        onClick={onViewMentorProfile}
        role={cardRole}
      >
        {/* eslint-enable jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}

        <div className="tm__mentorship-card-header">
          <DropdownActions
            actions={dropdownActions}
            ariaLabel={translatr('web.common.main', 'MoreOpportunityActions', {
              opportunity: name
            })}
          />
        </div>
        <div className="tm__mentorship-card-main">
          <div className="tm__mentorship-card-avatar">
            <Avatar user={{ imgUrl: avatarUrl, name }} blankAlt={true} />
            {isMentoring && (
              <span className="tm__mentorship-card-avatar-mentorship icon-hand-holding-person"></span>
            )}
          </div>
          <div id={`tm__mentorship-card-name-${id}`} className="tm__mentorship-card-name">
            <a
              href={UserProfileLink}
              onClick={e => e.preventDefault()}
              onKeyDown={onTitleKeyDown}
              tabIndex={0}
            >
              <TextClamp Component="div">{name}</TextClamp>
            </a>
          </div>
          <div className="tm__mentorship-card-meta">
            <span>{position ? position : <>&nbsp;</>} </span>
          </div>
          <div className="tm__mentorship-card-skills">
            <TagsWithXmore
              topics={visibleSkills}
              showTooltip
              tooltipCardInlineCss={screenSize.width < 450 && { 'max-width': remCalc(200) }}
              isClickableTag={isMfeEnabled()}
              onSkillClick={openSkillFlyout}
            />
          </div>
        </div>
        <div className="tm__mentorship-card-footer">
          {!isSelf ? (
            !status && !isMentoring ? (
              <button
                className="ed-btn ed-btn-primary"
                aria-label={translatr('web.common.main', 'MentorRequestMentorship', {
                  mentor: name
                })}
                onClick={onRequestClick}
                ref={requestMentorshipButtonRef}
              >
                {translatr('web.talentmarketplace.main', 'RequestMentorship', {
                  tm_tm_mentorship: omp('tm_tm_mentorship')
                })}
              </button>
            ) : (
              <ApplicationStatusLabel
                mentorCard
                className="tm__mentorship-card-footer-meta"
                status={status}
              />
            )
          ) : null}
        </div>
      </div>
    </>
  );
};

DiscoverMentorshipCard.propTypes = {
  avatarUrl: PropTypes.string,
  className: PropTypes.string,
  currentUserId: PropTypes.string,
  handle: PropTypes.string,
  id: PropTypes.string,
  userId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  name: PropTypes.string,
  position: PropTypes.string,
  skills: PropTypes.array,
  status: PropTypes.string,
  dispatch: PropTypes.func,
  loading: PropTypes.bool,
  comment: PropTypes.object,
  openModal: PropTypes.func,
  originalData: PropTypes.object,
  cardRole: PropTypes.string
};

export default connect(
  ({ currentUser }) => ({
    currentUserId: currentUser.get('id')
  }),
  dispatch => ({
    dispatch,
    openModal: modalMetaData => dispatch(openViewCommentModal(modalMetaData))
  })
)(withNoRenderErrorBoundary(DiscoverMentorshipCard));
