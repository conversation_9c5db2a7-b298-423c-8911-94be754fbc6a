import React from 'react';
import { Select } from 'centralized-design-system/src/Inputs';
import ContentFilter from './ContentFilter';
import ContentCreateButton from './ContentCreateButton';
import "./ContentFilterPanel.scss";
import { useContentFilter } from './ContentFilterProvider';
import { translate } from './utils';
import { SortOption } from './types';

interface ContentFilterPanelProps {
  showCreateButton: boolean;
  showFilters: boolean;
}

const ContentFilterPanel: React.FC<ContentFilterPanelProps> = ({ showCreateButton, showFilters }) => {
  const { state: { selectedSortOption }, dispatch } = useContentFilter();

  const sortingOptions: Array<{ id: SortOption, value: string }> = [
    { id: "creation_date_desc", value: translate("OrderByCreatingDateNewestFirstLabel") },
    { id: "creation_date_asc", value: translate("OrderByCreatingDateOldestFirstLabel") },
  ];

  return (
    <div className="my-content-search-container">
      {
        // TODO Search functionality will be implemented in ticket EP-100048
        // <div className="my-content-search">
        //  <ContentSearchInput />
        // </div>
      }
      <div className="my-content-list-panel">
        <div className="my-content-sort-by">
          <span id="sort-by-dropdown-label">{translate("SortByDropdownLabel")}</span>
          <Select
            ariaLabelledBy="sort-by-dropdown-label"
            defaultValue={sortingOptions.find(option => option.id === selectedSortOption)}
            items={sortingOptions}
            onChange={(sort: { id: SortOption, value: string }) => dispatch({ type: "SORT_CHANGED", sort: sort.id })}
          />
        </div>
        {showFilters && <ContentFilter />}
      </div>
      {showCreateButton && <ContentCreateButton />}
    </div>
  );
};

export default ContentFilterPanel;
