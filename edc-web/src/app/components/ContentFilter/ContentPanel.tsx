import ContentFilterPanel from './ContentFilterPanel';
import React from 'react';
import ContentList from './ContentList';
import { ContentFilterProvider } from './ContentFilterProvider';
import { SectionPath } from './types';

export interface ContentPanelProps {
  authorId: number,
  cardWrapperCallback?(): void,
  showCreateButton?: boolean,
  selectedSection?: SectionPath,
  showTitle?: boolean,
  publicView?: boolean,
}

const ContentPanel: React.FC<ContentPanelProps> = ({
   authorId,
   cardWrapperCallback,
   showCreateButton = false,
   selectedSection = "content",
   showTitle = false,
   publicView = false,
}) => {

  return (
    <ContentFilterProvider>
      <ContentFilterPanel showCreateButton={showCreateButton} showFilters={!publicView}/>
      <ContentList
        authorId={authorId}
        cardWrapperCallback={cardWrapperCallback}
        selectedSection={selectedSection}
        showTitle={showTitle}
        publicView={publicView}
      />
    </ContentFilterProvider>
  );
};

export default ContentPanel;
