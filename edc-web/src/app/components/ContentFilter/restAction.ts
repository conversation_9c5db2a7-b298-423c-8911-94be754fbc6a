import { cards } from 'edc-web-sdk/requests';
import { _agent } from 'edc-web-sdk/helpers/superagent-use';
import config from 'edc-web-sdk/requests/config';
import { assignmentCardFields, deletedCardFields } from '../../constants/cardFields';
import { FilterState, SectionPath, SortOption } from './types';

export const CARD_LIMIT_DEFAULT = 9;

const getSpecificPayload = (selectedSection: SectionPath, currentUserId: number) => {
  switch(selectedSection) {
    case 'content':
      return {
        author_id: currentUserId,
        'state[]': ['draft', 'published', 'processing', 'error']
      };
    case 'draft':
      return {
        author_id: currentUserId,
        'state[]': ['draft']
      };
    case "deleted":
      return {
        author_id: currentUserId,
        only_deleted: true,
        'card_type[]': null,
        last_access_at: null,
        fields: deletedCardFields
      };
    case "archived":
      return {
        author_id: currentUserId,
        only_archived: true,
        fields: deletedCardFields
      };
    case "assignedByMe":
      return {
        assigned_by_me: true
      };
    case "sharedByMe":
      return {
        shared_by_me: true
      };
    case "dismissed":
      return {
        'state[]': "dismissed",
        fields: assignmentCardFields,
      }
    case "purchased": return {}
    default:
      console.error("Invalid section!");
      return {};
  }
}

export const mapSelectedSortOption = (selectedSortOption: SortOption) => {
  switch(selectedSortOption) {
    case "creation_date_asc": return { field: "created", order: "ASC" }
    case "creation_date_desc":
    default:
      return { field: "created", order: "DESC" }
  }
};

export const mapFiltersToPayload = (filters: FilterState) => {
  let result: Array<string> = [];
  if (filters?.pathway) {
    result.push('pathway');
  }
  if (filters?.journey) {
    result.push('journey');
  }
  for (const key in filters?.smartcards) {
    if (filters?.smartcards[key]) {
      result.push(key);
    }
  }
  return result;
};
export const fetchCards = async (
  currentUserId: number,
  selectedSection: SectionPath,
  selectedSortOption: SortOption,
  filters: FilterState,
  searchQuery: string,
  offset = 0,
  cardLimit = CARD_LIMIT_DEFAULT,
  publicView: boolean
) => {
  const sort = mapSelectedSortOption(selectedSortOption);
  const appliedFilters = mapFiltersToPayload(filters);

  const payload = {
    limit: cardLimit,
    offset: offset,
    sort: sort.field,
    order: sort.order,
    q: searchQuery,
    "standard_type[]": appliedFilters,
    ...getSpecificPayload(selectedSection, currentUserId)
  }

  switch (selectedSection) {
    case 'purchased':
      return _agent
        .get(`${config.ApiHost || ''}/api/v2/cards/purchased`)
        .query(payload)
        .then((response: any) => {
          return {
            cards: response?.body?.cards,
            total: response?.body?.total
          }
        });
    case 'dismissed':
      return _agent
        .get(`${config.ApiHost || ''}/api/v2/assignments.json`)
        .query(payload)
        .then((response: any) => {
          return {
            cards: response?.body?.assignments,
            total: response?.body?.assignmentCount
          }
        });
    case 'content':
    case 'draft':
    case 'archived':
    case 'assignedByMe':
    case 'sharedByMe':
    default: {
      const publicPayload = {
        author_id: currentUserId,
        limit: cardLimit,
        offset: offset,
        sort: sort.field,
        order: sort.order,
      }

      return publicView
        ? cards.getCardsListing(publicPayload)
        : cards.getCards(payload, true);
    }
  }
};
