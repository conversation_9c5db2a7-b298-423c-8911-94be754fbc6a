import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import CardWrapper from '@components/cardStandardization/CardWrapper';
import { fetchCards } from './restAction';
import Loader from '@components/Loader/Loader';
import "./ContentList.scss";
import ContentEmptyState from './ContentEmptyState';
import { useContentFilter } from './ContentFilterProvider';
import { getListTitle } from './utils';
import { SectionPath } from './types';
import Card from 'centralized-design-system/src/Card/CardContainer';
import Pagination from 'centralized-design-system/src/Pagination';

interface ContentListProps {
  authorId: number,
  selectedSection: SectionPath,
  cardWrapperCallback?(): void,
  showTitle: boolean,
  publicView: boolean,
}

const CARDS_PER_PAGE = 12;

const ContentList: React.FC<ContentListProps> = ({ authorId, selectedSection, cardWrapperCallback, showTitle, publicView }) => {
  const location = useLocation();

  const users = useSelector((state: any) => state.users.toJS());
  const { state, dispatch } = useContentFilter();
  const { cards, total, loading, selectedSortOption, searchQuery, filters } = state;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageData, setPageData] = useState<{[key: number]: any[]}>({1: []});

  const paginate = (resp: any) => {
    let newPage = currentPage;

    if (resp.event === "next") {
      newPage = currentPage + 1;
    } else if (resp.event === "prev") {
      newPage = currentPage - 1;
    } else if (Number.isInteger(resp.event)) {
      newPage = resp.event;
    }

    if (!pageData[newPage]) {
      loadPageData(newPage);
    } else {
      dispatch({ type: "CONTENT_DATA_LOADED", data: pageData[newPage], total: total });
      setCurrentPage(newPage);
    }
  };

  const loadPageData = (page: number, shouldResetPages = false) => {
    dispatch({ type: "CONTENT_DATA_LOADING" });

    const offset = (page - 1) * CARDS_PER_PAGE;

    if (shouldResetPages) {
      setCurrentPage(1);
      setPageData({1: []});
    }

    return fetchCards(authorId, selectedSection, selectedSortOption, filters, searchQuery, offset, CARDS_PER_PAGE, publicView)
      .then((response) => {
        const fetchedCards = response?.cards || [];
        setPageData(prev => shouldResetPages ? {1: fetchedCards} : {...prev, [page]: fetchedCards});
        dispatch({ type: "CONTENT_DATA_LOADED", data: fetchedCards, total: response?.total});
        setCurrentPage(page);
        return response;
      })
      .catch((error) => {
        console.error('Error fetching cards:', error);
        dispatch({ type: "CONTENT_DATA_LOADING_FAILED"});
      });
  };

  useEffect(() => {
    let isActive = true;

    const fetchData = async () => {
      if (isActive) {
        await loadPageData(1, true);
      }
    };

    fetchData();

    return () => {
      isActive = false;
    };
  }, [selectedSection, selectedSortOption, searchQuery, filters, location.key]); // location.key -> workaround to reload page after adding new smartcard

  if(loading) {
    return <Loader center />;
  }

  return (
    <Card additionalClasses={["my-content-list-card-container"]}>
      {showTitle && <div className="my-content-card-header">
        <h3>{getListTitle(total, selectedSection)}</h3>
      </div>}
      {cards?.length
        ? <div className="my-content-list-container">
          {cards.map((card: any) => {
            const author = card && users.idMap[card.authorId || card.author?.id];
            return <div key={card.id + (card.isOfficial ? '-official' : '')} className="my-content-list-container_card-wrapper">
              <CardWrapper
                state="published"
                key={card.id + (card.isOfficial ? '-official' : '')}
                card={card.assignable || card}
                dueAt={card.dueAt || card.assignment?.dueAt}
                startDate={card.startDate || card.assignment?.startDate}
                user={author}
                author={author}
                removeCardFromList={(id) => dispatch({ type: "CARD_DELETED", cardId: id })}
                callbacks={{
                  onVote: cardWrapperCallback,
                  onShare: cardWrapperCallback,
                  onAssign: cardWrapperCallback,
                  onChangeAuthor: cardWrapperCallback,
                  onArchive: cardWrapperCallback,
                  onDelete: cardWrapperCallback,
                }}
              />
            </div>;
          })
          }
        </div>
        : <ContentEmptyState selectedSection={selectedSection} />
      }
      {cards?.length > 0 && total > CARDS_PER_PAGE &&
        <div className="content-list-pagination">
          <Pagination
            postPerPage={CARDS_PER_PAGE}
            totalPosts={total}
            paginate={paginate}
            activePage={currentPage}
            iconType={true}
          />
        </div>
      }
    </Card>
  );
};

export default ContentList;
