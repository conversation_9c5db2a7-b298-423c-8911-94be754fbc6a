@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui.skill-details {
  .arrow-mark {
    &::before {
      content: '';
      position: absolute;
      margin-top: rem-calc(-20);
      left: 0;
      border-style: solid;
      border-color: transparent transparent var(--ed-state-disabled-color) transparent;
      border-width: rem-calc(10);
    }
    &::after {
      content: '';
      position: absolute;
      margin-top: rem-calc(-18);
      border-style: solid;
      border-color: transparent transparent var(--ed-white) transparent;
      border-width: rem-calc(10);
      z-index: 1;
    }
  }
  .skill-view-details {
    height: rem-calc(0);
    border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    border-radius: var(--ed-border-radius-lg);
    visibility: hidden;
    overflow: hidden;
    transition: height 0.5s ease-out, visibility 0.5s;
    padding: var(--ed-spacing-base) 0 1rem 1rem;

    .image-container {
      width: 20%;
      border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
      border-radius: var(--ed-border-radius-lg);

      .img-wrapper {
        .pdf {
          width: 25%;
        }

        .pdf-name {
          width: 7rem;
          padding-left: 0.3125rem;
        }

        img {
          max-height: 8rem;
        }
        .verified {
          text-align: start;
          height: 15%;
          i {
            color: var(--ed-primary-base);
            font-size: rem-calc(22);
          }
          .height-85 {
            height: 85%;
          }
        }
        .icon-award-badge,
        .skill-icon-size {
          color: var(--ed-state-active-color);
          font-size: 3rem;
        }
      }

      .verified {
        color: var(--ed-state-active-color);
      }
    }

    .badge-details {
      width: 100%;
      .view-description {
        padding: 0.8rem 0;
        line-height: var(--ed-line-height-base);
      }
      .expandbutton {
        color: var(--ed-primary-base);
      }
      .bottom-details-container {
        justify-content: left;
        padding-bottom: var(--ed-spacing-2xs);

        .cursor-none {
          cursor: none;
        }

        .source-img {
          height: 2.5rem;
          width: 4.5rem;
        }
      }
      .share-button-icon {
        width: 20rem;
        height: 2.5rem;

        .share-block {
          width: 100%;
          padding: var(--ed-spacing-2xs);
          height: 100%;
        }
      }
    }

    .badge-details.wcag button.display-link {
      color: var(--ed-primary-base);
      text-decoration: underline;
    }

    .details-container {
      overflow-y: auto;
      overflow-x: hidden;
      justify-content: normal !important;

      &.badge-details .header-container {
        .header-actions-container {
          text-align: right;
        }
        & > div:first-child {
          margin-right: 1rem;
        }

        .header-title,
        .header-subtitle {
          width: unset;
          max-width: 35rem;
        }
      }

      .header-container {
        .header-title {
          color: var(--ed-text-color-primary);
          width: 18rem;
        }

        .header-subtitle {
          width: 13rem;
        }

        .header-actions-container {
          justify-content: right;

          button {
            max-height: 2.25rem;
            margin-bottom: 00.5rem;
          }
        }
      }

      .description {
        word-break: break-all;
      }

      .display-link {
        font-size: var(--ed-font-size-base);
        color: var(--ed-state-active-color);
      }

      .supporting-text {
        color: var(--ed-text-color-primary);
      }
    }
  }

  .details-container::-webkit-scrollbar {
    width: rem-calc(4);
  }
  .details-container::-webkit-scrollbar-thumb {
    border-radius: rem-calc(100);
    background-color: var(--ed-gray-4);
  }

  .add-transition {
    background: var(--ed-body-bg-color);
    box-shadow: var(--ed-shadow-base);
    visibility: visible;
    height: 13.688rem;
    transition: height 0.5s ease-in, visibility 0.5s;
  }
}

@media only screen and (min-width: #{$breakpoint-lg - 110px}) and (max-width: #{$breakpoint-lg}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .details-container {
        .header-container {
          .header-title {
            width: 16rem;
          }
          .header-subtitle {
            width: fit-content;
          }
        }
      }

      .badge-details {
        .share-button-icon {
          width: 20rem;
          height: 2.5rem;
          bottom: -1rem;
        }
      }
    }
  }
}

@media only screen and (min-width: #{$breakpoint-md - 17px}) and (max-width: #{$breakpoint-lg - 110px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .details-container {
        .header-container {
          .header-title {
            width: 11rem;
          }
          .header-subtitle {
            max-width: 15rem;
          }
        }
      }

      .badge-details {
        .share-button-icon {
          width: 20rem;
          height: 2.5rem;
          bottom: -1rem;
        }
      }
    }
  }
}

@media only screen and (min-width: #{0px}) and (max-width: #{$breakpoint-sm - 16px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .image-container {
        .img-wrapper {
          img {
            height: 100%;
            max-height: 4.5rem;
            margin-top: -0.938rem;
          }
        }
      }
    }
  }
}

@media only screen and (min-width: #{$breakpoint-sm - 16px}) and (max-width: #{$breakpoint-md - 17px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .image-container {
        .img-wrapper {
          img {
            height: 100%;
            max-height: 4.5rem;
          }
        }
      }
    }
  }
}

@media only screen and (min-width: #{$breakpoint-sm - 18px}) and (max-width: #{$breakpoint-md - 17px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .badge-details {
        .share-button-icon {
          .share-block {
            width: 7rem;
            height: fit-content;
            scale: 0.8;
            top: -0.75rem;
            right: -0.7rem;
          }
        }
      }
      .image-container {
        height: 6.75rem;
        width: 6.4rem;
      }
      .details-container {
        position: relative;
        .header-container {
          .header-actions-container button {
            border: none;
            padding: unset;
            min-width: fit-content;
          }
          .header-actions-container {
            margin-right: 7%;
          }
          .header-title {
            width: 21rem;
          }
          .header-subtitle {
            max-width: 13rem;
          }
        }
      }
    }
  }
}

@media only screen and (min-width: #{$breakpoint-xs - 76px}) and (max-width: #{$breakpoint-sm - 18px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .badge-details {
        .share-button-icon {
          .share-block {
            width: 7.2rem;
            height: fit-content;
            right: -0.8rem;
            scale: 0.7;
            top: -0.75rem;
          }
        }
      }
      .image-container {
        height: 5.75rem;
        width: 5.4rem;

        & > .img-wrapper {
          transform: scale(0.5);
          padding-bottom: var(--ed-spacing-base);
        }
        & .verified {
          font-size: 0.813rem;
        }
      }
      .details-container {
        position: relative;
        .header-container {
          .header-actions-container button {
            border: none;
            padding: unset;
            min-width: fit-content;
          }
          .header-actions-container {
            margin-right: 7%;
          }
          .header-title {
            width: 13rem;
          }
          .header-subtitle {
            max-width: 13rem;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-xs - 76px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .badge-details {
        overflow: auto;
        scale: 0.95;
        .share-button-icon {
          .share-block {
            width: 6.2rem;
            height: fit-content;
            scale: 0.6;
            right: -1.2rem;
            top: -0.75rem;
          }
        }
      }

      .image-container {
        height: 4.5rem;
        width: 4rem;
        padding-bottom: var(--ed-spacing-base);
        & > .img-wrapper {
          transform: scale(0.4);
          padding-bottom: var(--ed-spacing-base);
        }
        .no-padding.verified.justflex {
          scale: 0.7;
          margin-left: calc(-1 * var(--ed-spacing-base));
        }
      }
      .details-container {
        position: relative;
        .header-container {
          .header-actions-container button {
            border: none;
            padding: unset;
            margin-left: 0;
            min-width: fit-content;
          }
          .header-actions-container {
            margin-right: 7%;
          }
          .header-title {
            width: 4rem;
          }
          .header-subtitle {
            max-width: 13rem;
          }
        }
      }
    }
  }

  .ed-dialog-modal {
    .ed-dialog-modal-wrapper {
      .content {
        .ed-dialog-modal-header {
          display: -webkit-box !important;
        }
      }
    }
  }
  .ed-datepicker .form-control__button-content-wrapper {
    transform: scale(0.7);
    display: -webkit-box !important;
  }
}
@media only screen and (max-width: #{$breakpoint-md - 17px}) {
  .ed-ui.skill-details {
    & > .arrow-mark.position-absolute {
      display: none;
    }
    .skill-view-details {
      .badge-details {
        .share-button-icon {
          width: -webkit-fill-available;
          height: 2.5rem;
          bottom: calc(-1 * var(--ed-spacing-3xl));
          position: unset;

          & > .ed-btn {
            border: none;
            min-width: fit-content;
            padding: unset;
            position: absolute;
            top: 1%;
            right: 1%;
          }
        }
      }
    }
  }
}

@media only screen and (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-md}) {
  .ed-ui.skill-details {
    .skill-view-details {
      .badge-details {
        .share-button-icon {
          flex: 0;
        }
      }
    }
  }
}

@media only screen and (max-width: #{$breakpoint-xs - 76px}) {
  .ed-ui.skill-details {
    .skill-view-details {
      flex-direction: column;
      display: flex;

      .header-actions {
        display: inline-block;
        width: auto;
        position: absolute;
        right: var(--ed-spacing-lg);

        .ed-btn {
          padding: 0;
          min-width: fit-content;
          border: 0;
        }

        .share-button-icon {
          margin-left: 0.625rem;
          display: inline-flex;
          align-items: start;
          overflow: inherit;
          & > .ed-btn {
            border: none;
            min-width: fit-content;
            padding: unset;
          }
          .share-block {
            width: 5rem;
            height: fit-content;
            scale: 0.7;
            top: 0.4rem;
          }
        }
      }

      .details-container {
        .header-container {
          .header-title {
            width: 13rem;
          }
        }
      }
    }
  }
}
