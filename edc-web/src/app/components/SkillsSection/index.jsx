import React from 'react';
import { Tags } from 'centralized-design-system/src/Tags';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import { array, bool, string, func } from 'prop-types';
import './styles.scss';
import { skillsPreprocesorV2 } from './helper';
import classNames from 'classnames';
import { UNKNOWN_SKILL_LEVEL } from '@components/modals/SuggestedSkillsModal';
import { openSkillFlyout, isMfeEnabled } from '@components/MfeSkillsFlyout';

export const SkillsSection = ({ loading = false, items, onClose }) => {
  const skillItems = skillsPreprocesorV2(items.filter(item => item.level !== UNKNOWN_SKILL_LEVEL));
  return (
    <>
      {loading ? (
        <div className={`ed-skills-container`}>
          {Array(4)
            .fill(null)
            .map((_, i) => (
              <Skeleton key={i} width={60} height={26} borderRadius="1rem" />
            ))}
        </div>
      ) : (
        Object.keys(skillItems).map(
          key =>
            skillItems[key].length > 0 && (
              <SingleSkillSection key={key} title={key} items={skillItems[key]} onClose={onClose} />
            )
        )
      )}
    </>
  );
};

const SingleSkillSection = ({ title, items, onClose = null }) => {
  return (
    <div className="ed-ui ed-single-skill-section">
      {title && <h3 className="ed-input-title">{title}</h3>}
      <div
        // className="ed-skill-list"
        className={classNames('ed-skill-list', { 'ed-close-btn': !onClose })}
      >
        {items.map(item => {
          return (
            <Tags
              id={item.id}
              key={item.id}
              cb={onClose}
              name={item.label}
              changeOnHover={false}
              defaultIconClass={onClose ? 'icon-cross-circle' : ''}
              onLabelClick={isMfeEnabled() && (() => openSkillFlyout(item))}
            />
          );
        })}
      </div>
    </div>
  );
};

SkillsSection.propTypes = {
  loading: bool,
  items: array,
  onClose: func
};

SingleSkillSection.propTypes = {
  title: string,
  items: array,
  onClose: func
};
