import React, { Component } from 'react';
import { string, bool, object, func, number, array, shape } from 'prop-types';
import Content from './content/Content';
import CardPersonalizedContent from './personalizedContent/CardPersonalizedContent';
import CardHeader from './header/CardHeader';
import CardFooter from './footer/CardFooter';
import MetadataWrapper from './metadata/MetadataWrapper';
import handleCardClicked from './utils/handleCardClicked';
import getCardUrlOnRightClicked from './utils/getCardUrl';
import cardConfig from './config/cardConfig';
import './CardStandardization.scss';
import classNames from 'classnames';
import { connect } from 'react-redux';
import calculateExpireAfter from './utils/calculateExpireAfter';
import { Permissions } from '../../../app/utils/checkPermissions';
import { CardProvider } from './context/CardContext';
import isCardOwner from './utils/isCardOwner';
import LockedContentTileLayout from './private/LockedContentTileLayout';
import DisableCardClick from './common/DisableCardClick';
import { fetchCard } from 'edc-web-sdk/requests/cards';
import getUserBadge from './utils/getUserBadge';
import { saveConsumptionJourney } from '../../../app/actions/journeyActions';
import PathwayJourneyControls from './pathwayJourneyControls/PathwayJourneyControls';
import ListCard from './ListCard/ListCard';
import LD from '../../../app/containers/LDStore';
import { LinearProgressBar } from 'centralized-design-system/src/Inputs';
import withNavigate from '@hocs/withNavigate';
import { CARD_STATE } from './common/constants';

class CardWrapper extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      card: props.card
    };
    this.handleCardClicked = handleCardClicked.bind(this);
  }

  static getDerivedStateFromProps(nextProp, prevState) {
    // Checking of commentsCount (one of the key from card response in LXP) is done specifically to decide if we should rerender or not
    // In case of search response we get different Id than that of LXP, so no need to consider nextProp.card
    if (
      (nextProp.card?.id !== prevState.card?.id && !/^ECL-/.test(nextProp.card?.id)) ||
      (!('commentsCount' in prevState.card) && 'commentsCount' in nextProp.card) ||
      (nextProp.card?.completionState !== prevState.card?.completionState &&
        nextProp.card?.updateMarkAsCompleteButton !== prevState.card?.updateMarkAsCompleteButton) ||
      nextProp.isPathwayJourneyEditor ||
      nextProp.card?.locked !== prevState.card?.locked ||
      (nextProp.card?.isOptional !== undefined &&
        nextProp.card.isOptional !== prevState.card?.isOptional)
    ) {
      return {
        card: nextProp.card
      };
    }
    // Return null if the state hasn't changed
    return null;
  }

  updateCard = (card, isCompletionUpdate, completionData = null) => {
    const { rationales, explanation } = this.state.card;
    const isRationalesAvailable = rationales?.[0]?.type;
    if (card) {
      if (isRationalesAvailable) {
        card.rationales = rationales;
      }
      if (explanation) {
        card.explanation = explanation;
      }
      this.setState(
        {
          card
        },
        () => {
          if (this.props.journeyDetails) {
            this.setJourneyConsumptionData();
          }
        }
      );
    } else {
      //For SmartCard, Pathway and Journey we need to call this API, on edit as we don't get complete data from backend
      fetchCard(this.state.card.id)
        .then(data => {
          if (isRationalesAvailable) {
            data.rationales = rationales;
          }
          if (explanation) {
            data.explanation = explanation;
          }
          this.setState(
            {
              card: data
            },
            () => {
              if (this.props.journeyDetails) {
                this.setJourneyConsumptionData();
              }
            }
          );
        })
        .catch(err => {
          console.error(`Error in CardWrapper.updateCard.fetchCard.func : ${err}`);
        });
    }
    //Updating Pathway or Journey
    const { updateParentOnCompletionUpdate } = this.props;
    if (isCompletionUpdate) {
      // this is to show badge w.r.t mark as complete v2
      const userBadge = getUserBadge(completionData);
      updateParentOnCompletionUpdate?.(userBadge);
    }
  };

  setJourneyConsumptionData = () => {
    // Added for data consistency in journey consumption flow. this will update journey with updated card data.
    const { card } = this.state;
    const { journeySection } = this.props.journeyDetails;
    loopJourneySection: for (let i = 0; i < journeySection?.length; i++) {
      for (let j = 0; j < journeySection[i]?.cards?.length; j++) {
        if (journeySection[i].cards[j]?.id === card?.id) {
          journeySection[i].cards[j] = card;
          break loopJourneySection;
        }
      }
    }
    this.props.dispatch(saveConsumptionJourney(this.props.journeyDetails));
  };

  bookmarkUpdate = bookmarkUpdateStatus => {
    const { card } = this.state;
    card.isBookmarked = bookmarkUpdateStatus;
    card.bookmarkUpdatedFromOutside = !card.bookmarkUpdatedFromOutside;
    this.setState({
      card
    });
  };

  editPathwayJourneyCard = event => {
    this.props.openCardEdit && this.props.openCardEdit(event, this.state.card, this.props.index);
  };

  render() {
    const {
      type,
      orgConfigs,
      currentUserId,
      currentUserCountryCode,
      currentUserIsAdmin,
      currentUserIsGroupLeader,
      currentUserIsManagerWithReportees,
      isPartOfPathway,
      pathwayDetails,
      journeyDetails,
      isShowLockedCardContent,
      tooltipPosition,
      showPersonalizedContent,
      isPathwayJourneyEditor,
      isWeeklyBaseJourneyCard,
      dismissible,
      showTopicToggleClick,
      toggleChannelModal,
      openReasonReportModal,
      removeCardFromList,
      deleteSharedCard,
      channel,
      cardSectionName,
      removeCardFromCardContainer,
      removeDismissAssessmentFromList,
      dueAt,
      assignedAt,
      assignedBy,
      assignorId,
      isAssignmentPage,
      hideEdit,
      isFromGroupCarousel,
      index,
      enableSingleClick,
      toggleFeaturedStatus,
      isFeaturedCard,
      isPromotedCard,
      showFeaturedOption,
      leapAction,
      lockUnlockAction,
      dispatch,
      updateCard,
      hideCardEdit = '',
      journeyState,
      proficiencyLevels,
      currentUserLang,
      callbacks,
      // Used for card carousels
      scrollTooltipRelativeElement,
      classNameProp,
      showProgress,
      skill
    } = this.props;
    const { card } = this.state;
    const currentUserDetails = {
      currentUserId,
      currentUserCountryCode,
      currentUserIsAdmin,
      currentUserIsGroupLeader,
      currentUserIsManagerWithReportees
    };
    const assignmentDetails = {
      dueAt,
      isAssignmentPage,
      assignedAt,
      assignedBy,
      assignorId,
      currentUserId
    };

    // This is specifically needed for standalone
    const isActionsDisabled = false;
    const isCurateTab = this.props.isCurateTab;
    const config = cardConfig(
      type,
      card,
      orgConfigs,
      currentUserDetails,
      isActionsDisabled,
      false,
      isPathwayJourneyEditor,
      assignmentDetails,
      isCurateTab,
      {},
      enableSingleClick,
      false,
      proficiencyLevels,
      currentUserLang,
      isPromotedCard
    );
    const layoutType = type.toLowerCase();
    let unAuthorizedMessage = card.message;
    const isUnAuthorizedCard = card.accessible === false;
    const isCardDisabled = [CARD_STATE.DELETED, CARD_STATE.ARCHIVED].includes(card.state);
    const showHideEdit = hideEdit || !!hideCardEdit || isCardDisabled;
    if (isUnAuthorizedCard) {
      unAuthorizedMessage = 'You no longer have the permission to view this content';
    }
    const { cardType } = config;
    const isText = cardType === 'text';
    const cardWrapperClasses = classNames(`card-std-${layoutType}`, 'ed-ui', classNameProp, {
      pointer: layoutType !== 'list',
      'width-100': layoutType === 'list',
      'display-block': layoutType === 'bigcard',
      'card-std-unauthorized-bigcard': layoutType === 'bigcard' && isUnAuthorizedCard,
      'html-container': isText,
      'relative disabled-card': showHideEdit,
      'overflow-visible': !!hideCardEdit,
      'archived-card': card.state === CARD_STATE.ARCHIVED
    });
    const filestackExpireSeconds = calculateExpireAfter(
      orgConfigs.filestack_url_expire_after_seconds
    );
    const isOwner = isCardOwner(card, currentUserId);
    card.paidByUser = isOwner || card.paidByUser;
    const contextProvider = {
      card,
      updateCard: this.updateCard,
      handleCardClicked: this.handleCardClicked,
      tooltipPosition,
      isPartOfPathway,
      dismissible,
      showTopicToggleClick,
      isStandalone: false,
      cardUpdated: this.updateCard,
      openChannelModal: toggleChannelModal,
      openReasonReportModal,
      removeCardFromList,
      deleteSharedCard,
      channel,
      type,
      cardSectionName,
      removeCardFromCardContainer,
      removeDismissAssessmentFromList,
      pathwayId: pathwayDetails?.id,
      journeyId: journeyDetails?.id,
      bookmarkUpdate: this.bookmarkUpdate,
      toggleFeaturedStatus,
      isFeaturedCard,
      showFeaturedOption,
      scrollTooltipRelativeElement,
      isCardDisabled,
      ...(isPathwayJourneyEditor && { sectionId: this.props.sectionId })
    };
    const consumptionData = isPartOfPathway ? pathwayDetails || journeyDetails : false;
    const isConsumptionOwner = consumptionData && isCardOwner(consumptionData, currentUserId);
    const completionState = card.completionState === 'COMPLETED';
    const isLockedCard = (card.locked || card.isLocked) && !completionState;
    const lockContentForOwner = isLockedCard && isConsumptionOwner;
    let lockContentForOtherUsers = isLockedCard && !isConsumptionOwner && !isShowLockedCardContent;
    if (lockContentForOtherUsers && card.isLeapWithLockFunctionality) {
      lockContentForOtherUsers = false;
    }
    //Locked card for weekly based Journey
    if (isWeeklyBaseJourneyCard) {
      lockContentForOtherUsers = true;
    }
    const lockedContentForOwnerConfigObject = {
      configData: !isPathwayJourneyEditor && (lockContentForOwner || lockContentForOtherUsers),
      lockContentForOwner: lockContentForOwner
    };
    const unauthorizedObj =
      card.message === 'You are not authorized to access this card' || card.accessible === false
        ? { isUnauthorized: true, configData: true, unAuthorizedMessage }
        : {};
    const personalizedContent = config.content.personalizedContent;
    const { rationales } = card;
    const { assignmentConfigTile } = config.content;
    const showPersonalizedContentConfig =
      (showPersonalizedContent && personalizedContent) || rationales?.[0]?.type;
    const cardPersonalizedContentObject = {
      configData: type === 'BigCard' && (showPersonalizedContentConfig || assignmentConfigTile),
      personalizedContent,
      rationales,
      card,
      assignmentConfigTile
    };
    const openCardLink =
      isPathwayJourneyEditor && !isFromGroupCarousel
        ? this.editPathwayJourneyCard
        : this.handleCardClicked;

    const cardBlurLockedContent = classNames({
      'card-blur-locked-content':
        lockContentForOtherUsers && lockedContentForOwnerConfigObject.configData
    });

    const showProgressBar = typeof showProgress === 'number';

    return (
      <div
        className={cardWrapperClasses}
        onClick={openCardLink}
        role="button"
        tabIndex={0}
        onKeyDown={e => {
          if (e.key === 'Enter' && e.target?.classList?.contains('card-std-tile')) {
            openCardLink(e);
          }
        }}
      >
        {type !== 'List' ? (
          <>
            {index && <span className="pathway-card-index">{index}</span>}
            <CardProvider value={contextProvider}>
              <PathwayJourneyControls
                journeyState={journeyState}
                configData={config.header.headerControl.isPathwayJourneyEditor}
                card={card}
                leapAction={leapAction}
                lockUnlockAction={lockUnlockAction}
                dispatch={dispatch}
                type={type}
                lockCard={this.props?.lockCard}
                removeCardFromList={removeCardFromList}
                deleteSharedCard={deleteSharedCard}
                cardsList={this.props.cardsList}
                addToLeap={this.props.addToLeap}
                {...this.props}
                cardType={cardType}
                isOwner={isOwner}
                cardUrl={getCardUrlOnRightClicked(this.props)}
                updateCard={updateCard}
                hideEdit={hideCardEdit}
              />
              {!isUnAuthorizedCard && (
                <CardPersonalizedContent {...cardPersonalizedContentObject} />
              )}
              <LockedContentTileLayout
                {...lockedContentForOwnerConfigObject}
                {...unauthorizedObj}
              />
              <div className={cardBlurLockedContent}>
                <CardHeader
                  configData={config.header.configData}
                  configHeader={config.header}
                  filestackUrlExpire={filestackExpireSeconds}
                  currentUserId={currentUserId}
                  layout={layoutType}
                  deleteSharedCard={deleteSharedCard}
                  cardType={card.cardType}
                  blankAlt={this.props.blankAlt}
                  onAssignSuccess={callbacks?.onAssign}
                  onChangeAuthorSuccess={callbacks?.onChangeAuthor}
                  onArchive={callbacks?.onArchive}
                  onDelete={callbacks?.onDelete}
                />
                <Content
                  configContent={config.content}
                  type={type}
                  cardId={card.id}
                  filestackUrlExpire={filestackExpireSeconds}
                  currentUserId={currentUserId}
                  isText={isText}
                  cardUrl={getCardUrlOnRightClicked(this.props)}
                  blankAlt={this.props.blankAlt}
                  skill={skill}
                />
                <MetadataWrapper
                  configData={config.metadata.configData}
                  configMetadata={config.metadata}
                  showProgress={showProgressBar}
                />
                {showProgressBar && !isCardDisabled && <LinearProgressBar value={showProgress} />}
                {!isCardDisabled && (
                  <CardFooter
                    configFooter={config.footer}
                    configData={config.footer?.configData}
                    onShareSuccess={callbacks?.onShare}
                    onVoteSuccess={callbacks?.onVote}
                  />
                )}
              </div>
            </CardProvider>
            <DisableCardClick
              cardState={card.state}
              disableText={hideEdit || hideCardEdit}
              configData={showHideEdit}
              isPathwayJourneyEditor={isPathwayJourneyEditor}
            />
          </>
        ) : (
          <ListCard
            {...this.props}
            cardType={cardType}
            isOwner={isOwner}
            configHeader={config.header}
            configContent={config.content}
            filestackUrlExpire={filestackExpireSeconds}
            cardUrl={getCardUrlOnRightClicked(this.props)}
            updateCard={updateCard}
            hideEdit={hideCardEdit}
            journeyState={journeyState}
          />
        )}
      </div>
    );
  }
}

CardWrapper.propTypes = {
  type: string,
  card: object,
  currentUserId: string,
  orgConfigs: object,
  currentUserIsAdmin: bool,
  currentUserIsGroupLeader: bool,
  currentUserIsManagerWithReportees: bool,
  currentUserCountryCode: string,
  pathwayDetails: object,
  journeyDetails: object,
  toggleSearch: func,
  isFromTeamCardsContainer: bool,
  isPartOfPathway: bool,
  tooltipPosition: string,
  isShowLockedCardContent: bool,
  showPersonalizedContent: bool,
  navigationLink: string,
  isPathwayJourneyEditor: bool,
  updateParentOnCompletionUpdate: func,
  isWeeklyBaseJourneyCard: bool,
  dismissible: bool,
  showTopicToggleClick: func,
  toggleChannelModal: func,
  openReasonReportModal: func,
  removeCardFromList: func,
  deleteSharedCard: func,
  channel: object,
  cardSectionName: string,
  removeCardFromCardContainer: func,
  removeDismissAssessmentFromList: func,
  dueAt: string,
  assignedAt: string,
  assignedBy: string,
  isAssignmentPage: bool,
  openCardEdit: func,
  hideEdit: string,
  isFromGroupCarousel: bool,
  assignorId: number,
  index: number,
  enableSingleClick: bool,
  leapAction: func,
  lockUnlockAction: func,
  updateCard: func,
  hideCardEdit: bool,
  toggleFeaturedStatus: func,
  isFeaturedCard: bool,
  journeyState: string,
  isCurateTab: bool,
  cardsList: array,
  addToLeap: func,
  showFeaturedOption: bool,
  lockCard: array,
  scrollTooltipRelativeElement: object,
  proficiencyLevels: array,
  currentUserLang: string,
  blankAlt: bool,
  isPromotedCard: bool,
  altText: string,
  callbacks: shape({
    onVote: func,
    onShare: func,
    onAssign: func,
    onChangeAuthor: func,
    onArchive: func,
    onDelete: func
  }),
  // used in handleCardClicked
  navigate: func,
  classNameProp: string,
  showProgress: number,
  skill: string,
  sectionId: string
};

CardWrapper.defaultProps = {
  type: 'Tile'
};

function mapStoreStateToProps(state) {
  const {
    id: currentUserId,
    countryCode,
    isAdmin,
    isGroupLeader,
    hasReporters
  } = state.currentUser.toJS();

  const isManagerDashboardEnabled = LD.isManagerDashboardEnabled();

  return {
    orgConfigs: state.team.get('config'),
    currentUserId,
    currentUserCountryCode: countryCode || 'us',
    currentUserIsAdmin: isAdmin && Permissions.has('ADMIN_ONLY'),
    currentUserIsGroupLeader: isGroupLeader,
    currentUserIsManagerWithReportees: isManagerDashboardEnabled && hasReporters,
    proficiencyLevels: state.team.get('proficiencyLevels'),
    currentUserLang: state.currentUser.get('profile').get('language')
  };
}

export default withNavigate(connect(mapStoreStateToProps)(CardWrapper));
