import { confirmation } from '@actions/modalActions';
import {
  getActionStrings,
  redirectAfterAction,
  getActionFunc,
  checkCardForAssociation
} from './utils';

const removeContentClickHandler = async ({
  props,
  action,
  hideInsight,
  updateDismissed,
  navigate,
  successCallback = () => {},
  anchorRef
}) => {
  const { dispatch } = props;
  const actionStrings = getActionStrings(props, action);
  const actionFunc = getActionFunc(action);
  const [hideConfirmBtnForAssociatedCard, associatedCardbBodyMsg] = await checkCardForAssociation(
    props
  );

  actionStrings.hideConfirmBtn = hideConfirmBtnForAssociatedCard
    ? hideConfirmBtnForAssociatedCard
    : actionStrings.hideConfirmBtn;
  actionStrings.bodyMsg =
    associatedCardbBodyMsg !== '' ? associatedCardbBodyMsg : actionStrings.bodyMsg;

  document.body.style.overflow = '';

  dispatch(
    confirmation(
      actionStrings.confirmText,
      actionStrings.bodyMsg,
      () => {
        // On clicking confirm button
        actionFunc(props);
        successCallback();
        redirectAfterAction({
          props,
          snackBarMsg: actionStrings.snackBarMsg,
          hideInsight,
          updateDismissed,
          navigate
        });
      },
      false,
      actionStrings.confirmText,
      true,
      actionStrings.hideConfirmBtn,
      null, // cancelBtnTitle
      anchorRef
    )
  );
};

export default removeContentClickHandler;
