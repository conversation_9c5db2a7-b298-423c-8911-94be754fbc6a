import linkPrefix from '@utils/linkPrefix';
import getSmartCardId from '../../../../app/utils/getSmartCardId';
import { CARD_STATE } from '../common/constants';

const getCardUrlOnRightClicked = props => {
  const { card, isPartOfPathway, navigationLink, pathwayDetails, journeyDetails } = props;
  const linkPrefixValue = linkPrefix(card.cardType);

  if ([CARD_STATE.DELETED, CARD_STATE.ARCHIVED].includes(card?.state)) {
    return null;
  }

  // If coming from Smarsearch, we need to add a slug
  // Mutating state not an issue
  if (!card.slug) {
    card.slug = card.id;
  }
  if (navigationLink) {
    return navigationLink;
  } else if (isPartOfPathway) {
    const slug = pathwayDetails?.slug || journeyDetails?.slug;
    const urlLabel = linkPrefix(pathwayDetails?.cardType || journeyDetails?.cardType);
    const cardId = getSmartCardId(card);
    return `/${urlLabel}/${slug}/cards/${cardId}`;
  } else {
    return `/${linkPrefixValue}/${encodeURIComponent(card.slug || card.id)}`;
  }
};

export default getCardUrlOnRightClicked;
