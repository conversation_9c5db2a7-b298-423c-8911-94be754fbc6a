import React, { useContext, useState } from 'react';
import { useDispatch } from 'react-redux';
import configWrapper from '../../hoc/configWrapper';
import { translatr } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';
import PropTypes from 'prop-types';
import CardContext from '../../context/CardContext';
import debounce from 'lodash/debounce';
import { likeCard, dislikeCard } from 'edc-web-sdk/requests/cards';
import capture from '../../../../../app/utils/datalayer';
import { tooltipTextWrapper } from '@utils/utils';
import { UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID } from '@constants/actionTypes';

const handleLikeClicked = async ({
  card,
  callUpdateCard,
  stateObject,
  updateLikeStateForDiscoverCache,
  e,
  onSuccess = () => {}
}) => {
  e.preventDefault();
  e.stopPropagation();
  const cardData = { ...card };
  cardData.isUpvoted = !stateObject.isLiked;
  const votesUpdatedCount = Math.max(cardData.votesCount + (!stateObject.isLiked ? 1 : -1), 0);
  cardData.votesCount = votesUpdatedCount;
  callUpdateCard(cardData);
  stateObject.setCounterLike(stateObject.counterLike + 1);
  stateObject.updateLike(!stateObject.isLiked);

  handleLikeApiCalls({
    card,
    callUpdateCard,
    votesUpdatedCount,
    stateObject,
    updateLikeStateForDiscoverCache,
    onSuccess
  });
};

const handleLikeApiCalls = debounce(
  ({
    card,
    callUpdateCard,
    votesUpdatedCount,
    stateObject,
    updateLikeStateForDiscoverCache,
    onSuccess
  }) => {
    //To avoiding same API call
    if (stateObject.counterLike % 2 === 0) {
      if (!stateObject.isLiked) {
        capture('Card Liked', card);
        updateLikeStateForDiscoverCache(true);

        likeCard(card.id, card.cardType)
          .then(() => {
            stateObject.setVotesCount(votesUpdatedCount);
            onSuccess({ liked: true });
          })
          .catch(err => {
            errorHandlerForApiCalls({
              card,
              callUpdateCard,
              stateObject,
              functionName: 'likeCard',
              err,
              updateLikeStateForDiscoverCache
            });
          });
      } else {
        capture('Card Unliked', card);
        updateLikeStateForDiscoverCache(false);

        dislikeCard(card.id, card.cardType)
          .then(() => {
            stateObject.setVotesCount(votesUpdatedCount);
            onSuccess({ liked: false });
          })
          .catch(err => {
            errorHandlerForApiCalls({
              card,
              callUpdateCard,
              stateObject,
              functionName: 'dislikeCard',
              err,
              updateLikeStateForDiscoverCache
            });
          });
      }
    }
    stateObject.setCounterLike(0);
    //Because like API is very slow, we have added 500 as delay, will update once like API performace improved
  },
  500
);

const errorHandlerForApiCalls = ({
  card,
  callUpdateCard,
  stateObject,
  functionName,
  err,
  updateLikeStateForDiscoverCache
}) => {
  const cardData = { ...card };
  cardData.votesCount = stateObject.votesCount;
  callUpdateCard(cardData);
  stateObject.updateLike(stateObject.isLiked);
  updateLikeStateForDiscoverCache(stateObject.isLiked);

  console.error(`Error in LikeComponent.errorHandlerForApiCalls.${functionName}.func: ${err}`);
};

const LikeComponent = props => {
  const { showLabel, showDivItem, onVoteSuccess } = props;
  const {
    card,
    updateCard,
    tooltipPosition,
    type,
    isStandalone,
    scrollTooltipRelativeElement
  } = useContext(CardContext);
  const { id: cardId, isUpvoted, votesCount: votes } = card;
  const [isLiked, updateLike] = useState(isUpvoted);

  const dispatch = useDispatch();

  const updateLikeStateForDiscoverCache = (_isLiked = false) => {
    dispatch({
      type: UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID,
      payload: {
        id: cardId,
        updatedProperty: 'isUpvoted',
        updatedValue: _isLiked
      }
    });
  };

  // In search We are getting isUpvoted response later after initialization of state
  if (isLiked !== isUpvoted) {
    updateLike(isUpvoted);
  }

  const [counterLike, setCounterLike] = useState(0);
  const [votesCount, setVotesCount] = useState(votes);
  const stateObject = {
    isLiked,
    updateLike,
    counterLike,
    setCounterLike,
    votesCount,
    setVotesCount
  };
  const likeLabel = isLiked
    ? translatr('web.common.main', 'Liked')
    : translatr('web.common.main', 'Like');
  const likeComponentIds = `card-like-${cardId} card-description-${cardId} card-title-${cardId} card-holder-id-${cardId}`;
  const getLikeComponentData = () => {
    return (
      <>
        <button
          className="outline-revert ed-btn no-padding min-width-0"
          aria-label={likeLabel}
          onClick={e =>
            handleLikeClicked({
              card,
              callUpdateCard: updateCard,
              stateObject,
              updateLikeStateForDiscoverCache,
              e,
              onSuccess: onVoteSuccess
            })
          }
          id={`card-like-${cardId}`}
          aria-labelledby={likeComponentIds}
        >
          <Tooltip
            id={`card-description-${cardId}`}
            message={tooltipTextWrapper(translatr('web.common.main', 'Like'))}
            isHtmlIncluded
            pos={tooltipPosition}
            customClass="common-tooltip-setting inline-block"
            hide={type === 'BigCard' || isStandalone}
            // Used for Card carousels
            scrollTooltipRelativeElement={scrollTooltipRelativeElement}
            onScrollHideTooltipCard
          >
            <i
              className={`card-icon no-icon-color icon-thumbs-up${isLiked ? '-fill filled' : ''}`}
            />
          </Tooltip>
          {showLabel && <span className="social-activity-icon-label">{likeLabel}</span>}
        </button>
      </>
    );
  };

  return showDivItem ? (
    <div className={'footer-icon-wrapper'}>{getLikeComponentData()}</div>
  ) : (
    <li className={'footer-icon-wrapper'}>{getLikeComponentData()}</li>
  );
};

LikeComponent.propTypes = {
  showLabel: PropTypes.bool,
  showDivItem: PropTypes.bool,
  onVoteSuccess: PropTypes.func
};

export default configWrapper(LikeComponent);
