import React, { useState, useRef, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Translatr, translatr } from 'centralized-design-system/src/Translatr';
import { useNavigate } from 'react-router-dom';
import { isRtl } from '../../../../../app/utils/locale.js';

import WithContext from '../../hoc/WithContext';

import openPrivateWarning from '../../utils/header/insightDropdownActions/openPrivateWarning';
import editClickHandler from '../../utils/header/insightDropdownActions/editClickHandler';
import dismissAssignment from '../../utils/header/insightDropdownActions/dismissAssignment';
import addToPathwayClickHandler from '../../utils/header/insightDropdownActions/addToPathwayClickHandler';
import dismissClickHandler from '../../utils/header/insightDropdownActions/dismissClickHandler';
import promoteClickHandler from '../../utils/header/insightDropdownActions/promoteClickHandler';
import copyCardClickHandler from '../../utils/header/insightDropdownActions/copyCardClickHandler';
import openReasonReportModal from '../../utils/header/insightDropdownActions/openReasonReportModal';
import openVersionNameModal from '../../utils/header/insightDropdownActions/openVersionNameModal';
import openVersionHistoryModal from '../../utils/header/insightDropdownActions/openVersionHistoryModal';
import openMDPModalFunc from '../../utils/header/insightDropdownActions/openMDPModalFunc';
import handleOpenAssignModal from '../../utils/header/insightDropdownActions/handleOpenAssignModal';
import isAuthorFunc from '../../utils/header/insightDropdownActions/isAuthorFunc';
import onMenuItemClickHandler from '../../utils/header/insightDropdownActions/onMenuItemClickHandler';
import hideInsight from '../../utils/header/insightDropdownActions/hideInsight';
import showTopicsClickHandler from '../../utils/header/insightDropdownActions/showTopicsClickHandler';

import InsightDropdownButton from './InsightDropdownButton';
import * as menuConfig from '../../utils/header/insightDropdownActions/menuConfig';
import CardLikesModal from '@components/modals/CardLikesModal';
import CardStatisticsModal from '@components/modals/CardStatisticsModal/CardStatisticsModal';

import MDPModal from '@components/modals/MDPModal';
import ChangeAuthorModal from '@components/modals/ChangeAuthorModal';
import ReportModal from '@components//modals/ReportModal';
import CreateVersionModal from '@components/modals/CreateVersionModal';
import VersionHistoryModal from '@components/modals/VersionHistoryModal';
import MultiactionModal from '@components/modals/MultiActionModal/MultiactionModalV2';
import CardContext from '../../context/CardContext';
import getStandardizedTitle from '../../utils/content/getStandardizedTitle';

import LD from '../../../../../app/containers/LDStore';
import {
  cardActionMenuKeyDownHandler,
  closeCardActionDropdown,
  isCardContributor,
  toggleCardActionMenu
} from '@utils/smartCardUtils';
import removeContentClickHandler from '@components/cardStandardization/utils/header/insightDropdownActions/removeContentClickHandler';
import { INSIGHT_DROPDOWN_ACTIONS } from '@components/cardStandardization/utils/header/insightDropdownActions/constants';
import LazyloadComponent from '@components/LazyloadComponent.js';
import { ClickAwayListener, Grow, Paper, Popper, MenuItem, MenuList, Divider } from '@mui/material';
import NotifyEvent from '@components/cardStandardization/content/mkp/courseEvents/enrollements/NotifyEvent.jsx';

const BlockUser = LazyloadComponent(() => import('@components/modals/BlockUser/BlockUser'))();

const InsightDropdownActions = props => {
  const {
    isPartOfPathway,
    card,
    config,
    writableChannels,
    isCurrentUserAdmin,
    currentUserId,
    isSuperAdmin,
    isGroupLeader,
    hasReporters,
    author,
    providerCards,
    dispatch,
    reported_card_ids,
    showTopicToggleClick,
    removeCardFromList,
    removeCardFromCardContainer,
    cardSectionName,
    updateCard,
    onAssignSuccess,
    onChangeAuthorSuccess,
    onArchive = () => {},
    onDelete = () => {}
  } = props;

  const { isFeaturedCard, showFeaturedOption, toggleFeaturedStatus } = useContext(CardContext);
  const [showTopic, updateShowTopic] = useState(false);
  const [isOfficial, updateIsOfficial] = useState(card?.isOfficial);
  const [dismissed, updateDismissed] = useState(false);
  const [isAssigned, updateIsAssigned] = useState(card?.isAssigned);
  const [isSelfAssigned, updateIsSelfAssigned] = useState(card?.isSelfAssigned);
  const [openMDPModal, setOpenMDPModal] = useState(false);
  const [openLikeModal, setOpenLikeModal] = useState(false);
  const [openStatsmodal, setOpenStatsModal] = useState(false);
  const [openChangeAuthorModal, setOpenChangeAuthorModal] = useState(false);
  const [openReportModal, setOpenReportModal] = useState(false);
  const [openCreateVersionModal, setOpenCreateVersionModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [versionHistoryModal, setOpenVersionHistoryModal] = useState(false);
  const [openAssignModal, setOpenAssignModal] = useState(false);
  const [blockUser, setBlockUser] = useState(false);

  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);
  const prevOpen = useRef(open);
  const menuItemRef = useRef(null);
  const standardizedTitle = getStandardizedTitle(card);
  const previousModalStates = useRef({});

  const navigate = useNavigate();

  const isImpersonator = ['manager_dashboard'].includes(window?.__ED__?.proxyType);

  const isReadOnlyImpersonator = ['user_dashboard'].includes(window?.__ED__?.proxyType);

  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current.focus();
    }

    // we conditionally render the list item which causes issues in accessibility
    // Since the list item are rendered conditinally so we don't know the first item
    // So to manually make the first list item focus added this
    if (open) {
      menuItemRef.current?.firstChild?.focus();
    }

    prevOpen.current = open;
  }, [open]);

  useEffect(() => {
    const currentModalStates = {
      openMDPModal,
      openLikeModal,
      openStatsmodal,
      openChangeAuthorModal,
      openReportModal,
      openCreateVersionModal,
      versionHistoryModal,
      openAssignModal,
      openEditModal
    };

    Object.keys(currentModalStates).forEach(key => {
      if (previousModalStates.current[key] && currentModalStates[key] === false) {
        anchorRef?.current?.focus();
      }
    });

    previousModalStates.current = currentModalStates;
  }, [
    openMDPModal,
    openLikeModal,
    openStatsmodal,
    openChangeAuthorModal,
    openReportModal,
    openCreateVersionModal,
    versionHistoryModal,
    openAssignModal,
    openEditModal
  ]);

  if (dismissed) return null;

  const isAuthor = isAuthorFunc(author, currentUserId),
    isAdmin = menuConfig.isAdminFunc(isCurrentUserAdmin),
    isOwned = menuConfig.isOwnedFunc(isAuthor),
    isContributor = isCardContributor(card, currentUserId),
    showArchive = menuConfig.showArchiveFunc(card, isAuthor, isContributor, isAdmin, isSuperAdmin),
    showUnArchive = menuConfig.showUnArchiveFunc(card, isAuthor, isAdmin, isSuperAdmin),
    showDelete = menuConfig.showDeleteFunc(card, isAuthor, isContributor, isAdmin, isSuperAdmin),
    showBlock = menuConfig.showBlockUserFunc(card, isAuthor, isAdmin),
    showChangeAuthor = menuConfig.showChangeAuthorFunc(
      card,
      author,
      isAuthor,
      isContributor,
      isAdmin
    ),
    showMDP = menuConfig.showMDPFunc(card, config),
    showTopics = menuConfig.showTopicsFunc(props),
    showLikes = menuConfig.showLikesFunc(card, isAuthor, isContributor),
    showEdit = menuConfig.showEditFunc(card, isOwned, isAdmin, isContributor, isSuperAdmin),
    showFeatured = menuConfig.showFeaturedFunc(card, isAdmin, isAuthor),
    showAddToPathway = menuConfig.showAddToPathwayFunc(card),
    showAddToJourney = menuConfig.showAddToJourneyFunc(card),
    showDismiss = menuConfig.showDismissFunc(card, props.dismissible),
    showAssign = menuConfig.showAssignFunc(
      card,
      isGroupLeader,
      hasReporters,
      isAuthor,
      isContributor,
      isAdmin
    ),
    showAssignToMe = menuConfig.showAssignToMeFunc(card, isAssigned, isSelfAssigned),
    showChannelPost = menuConfig.showChannelPostFunc(
      card,
      writableChannels,
      isAuthor,
      isContributor,
      isAdmin
    ),
    showReportOption = menuConfig.showReportOptionFunc(card, config, isPartOfPathway),
    showCopyContent = menuConfig.showCopyContentFunc(card),
    showDismissFromAssignments = menuConfig.showDismissFromAssignmentsFunc(
      isAssigned,
      isSelfAssigned
    ),
    showCardStats = menuConfig.showCardStatsFunc(
      card,
      isOwned,
      isContributor,
      isAdmin,
      providerCards
    ),
    showCreateVersion = menuConfig.showCreateVersionFunc(card, isAuthor, isAdmin),
    showVersionHistory = menuConfig.showVersionHistoryFunc(card),
    showRemoveFromGroup = props.deleteSharedCard && !window.location.href.includes('assignments'),
    showUnpin = props.showUnPinOption,
    showNotifyEventToggle = menuConfig.showNotifyEventToggleFunc(card);

  const showDropdownButton =
    showFeaturedOption ||
    showAddToPathway ||
    showAddToJourney ||
    showChannelPost ||
    showTopics ||
    showLikes ||
    showAssignToMe ||
    showAssign ||
    showChangeAuthor ||
    showFeatured ||
    showCopyContent ||
    showMDP ||
    showDismissFromAssignments ||
    showEdit ||
    showDismiss ||
    showArchive ||
    showUnArchive ||
    showDelete ||
    showRemoveFromGroup ||
    showUnpin ||
    showReportOption ||
    showCardStats ||
    showCreateVersion ||
    showBlock ||
    showVersionHistory;

  const showDeleteOption = () => {
    return (
      showDelete &&
      !isImpersonator && (
        <MenuItem
          className="delete"
          onClick={e => {
            closeCardActionDropdown(e, anchorRef, setOpen);
            onMenuItemClickHandler(e, removeContentClickHandler, {
              action: INSIGHT_DROPDOWN_ACTIONS.DELETE,
              props,
              hideInsight,
              updateDismissed,
              navigate,
              successCallback: onDelete,
              anchorRef
            });
          }}
        >
          {translatr('web.common.main', 'Delete')}
        </MenuItem>
      )
    );
  };

  const showUnArchiveOption = () => {
    return (
      showUnArchive &&
      !isImpersonator && (
        <MenuItem
          className="delete"
          onClick={e => {
            closeCardActionDropdown(e, anchorRef, setOpen);
            onMenuItemClickHandler(e, removeContentClickHandler, {
              action: INSIGHT_DROPDOWN_ACTIONS.UNARCHIVE,
              props,
              hideInsight,
              updateDismissed,
              navigate,
              anchorRef
            });
          }}
        >
          {translatr('web.common.main', 'UnArchive')}
        </MenuItem>
      )
    );
  };

  const getDropDownOptions = () => {
    return (
      <>
        {showFeaturedOption && !isImpersonator && (
          <>
            <MenuItem
              className="addToPathway"
              onClick={e => {
                closeCardActionDropdown(e, anchorRef, setOpen);
                onMenuItemClickHandler(e, toggleFeaturedStatus, {
                  card,
                  isFeaturedCard
                });
              }}
            >
              {isFeaturedCard
                ? translatr('web.common.main', 'RemoveFromFeatured')
                : translatr('web.common.main', 'AddToFeatured')}
            </MenuItem>
            <Divider className="dropdown-divider" />
          </>
        )}
        {showAddToPathway && !isImpersonator && (
          <MenuItem
            className="addToPathway"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, addToPathwayClickHandler, {
                card,
                isAddingToJourney: false,
                dispatch,
                openedFromHtmlElement: anchorRef?.current
              });
            }}
          >
            {translatr('web.common.main', 'AddToPathway')}
          </MenuItem>
        )}
        {showAddToJourney && !isImpersonator && (
          <MenuItem
            className="addToPathway"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, addToPathwayClickHandler, {
                card,
                isAddingToJourney: true,
                dispatch,
                openedFromHtmlElement: anchorRef?.current
              });
            }}
          >
            {translatr('web.common.main', 'AddToJourney')}
          </MenuItem>
        )}
        {showChannelPost && !isImpersonator && (
          <MenuItem
            className="channel"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, openPrivateWarning, {
                card,
                dispatch,
                openedFromHtmlElement: anchorRef?.current
              });
            }}
          >
            {translatr('web.common.main', 'PostToChannel')}
          </MenuItem>
        )}
        {showTopics && !isImpersonator && (
          <MenuItem
            className="toggleTopics"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, showTopicsClickHandler, {
                showTopic,
                showTopicToggleClick,
                updateShowTopic
              });
            }}
          >
            {!showTopic
              ? translatr('web.common.main', 'ShowTags')
              : translatr('web.common.main', 'HideTags')}
          </MenuItem>
        )}
        {showLikes && !isImpersonator && (
          <MenuItem
            className="toggleTopics"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              e.stopPropagation();
              setOpenLikeModal(true);
            }}
          >{`${translatr('web.common.main', 'ShowLikes')} (${card.votesCount})`}</MenuItem>
        )}
        {showAssignToMe && !isImpersonator && (
          <MenuItem
            className="assign"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, handleOpenAssignModal, {
                selfAssign: true,
                dispatch,
                card,
                assignedStateChange: updateIsAssigned,
                selfAssignedStateChange: updateIsSelfAssigned,
                anchorRef
              });
            }}
          >
            {translatr('web.common.main', 'AssignToMe')}
          </MenuItem>
        )}
        {showAssign && (
          <MenuItem
            className="assign"
            onClick={e => {
              e?.stopPropagation();
              closeCardActionDropdown(e, anchorRef, setOpen);
              setOpenAssignModal(true);
            }}
          >
            {translatr('web.common.main', 'Assign')}
          </MenuItem>
        )}
        {showChangeAuthor && !isImpersonator && (
          <MenuItem
            className="assign"
            onClick={e => {
              e.stopPropagation();
              closeCardActionDropdown(e, anchorRef, setOpen);
              setOpenChangeAuthorModal(true);
            }}
          >
            {translatr('web.common.main', 'ChangeAuthor')}
          </MenuItem>
        )}
        {showFeatured && !isImpersonator && (
          <MenuItem
            className="promote"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, promoteClickHandler, {
                card,
                dispatch,
                isOfficial: isOfficial,
                updateIsOfficial: updateIsOfficial
              });
            }}
          >
            {!isOfficial
              ? translatr('web.common.main', 'Promote')
              : translatr('web.common.main', 'Unpromote')}
          </MenuItem>
        )}
        {showCopyContent && !isImpersonator && (
          <MenuItem
            className="complete"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, copyCardClickHandler, {
                cardId: card.id,
                dispatch
              });
            }}
          >
            {translatr('web.common.main', 'MakeACopy')}
          </MenuItem>
        )}
        {showMDP && !isImpersonator && (
          <MenuItem
            className="unpin"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, openMDPModalFunc, {
                card,
                dispatch,
                setOpenMDPModal
              });
            }}
          >
            {translatr('web.common.main', 'AddToMdp')}
          </MenuItem>
        )}
        {showVersionHistory && LD.cardVersioning() && card.cardRelation && !isImpersonator && (
          <MenuItem
            className="versionHistory"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, openVersionHistoryModal, {
                card,
                dispatch,
                setOpenVersionHistoryModal
              });
            }}
          >
            {translatr('web.common.main', 'VersionHistory')}
          </MenuItem>
        )}
        {showCreateVersion && LD.cardVersioning() && !isImpersonator && (
          <MenuItem
            className="createVersion"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, openVersionNameModal, {
                card,
                dispatch,
                setOpenCreateVersionModal
              });
            }}
          >
            {translatr('web.common.main', 'CreateNewVersion')}
          </MenuItem>
        )}
        <Divider className="dropdown-divider" />
        {showDismissFromAssignments && !isImpersonator && (
          <MenuItem
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, dismissAssignment, {
                props,
                assignedStateChange: updateIsAssigned,
                selfAssignedStateChange: updateIsSelfAssigned
              });
            }}
            className=" "
          >
            {translatr('web.common.main', 'DismissFromAssignments')}
          </MenuItem>
        )}
        {showEdit && !isImpersonator && (
          <MenuItem
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, props.editPathway || editClickHandler, {
                ...props,
                navigate,
                onClose: () => {
                  setOpenEditModal(false);
                }
              });
              setOpenEditModal(true);
            }}
            className="edit"
          >
            {translatr('web.common.main', 'Edit')}
          </MenuItem>
        )}
        {showDismiss && !isImpersonator && (
          <MenuItem
            className="dismiss"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, dismissClickHandler, {
                removeCardFromList: removeCardFromList,
                card,
                dispatch,
                hideInsight,
                updateDismissed,
                dismissible: props.dismissible
              });
            }}
          >
            {translatr('web.common.main', 'Dismiss')}
          </MenuItem>
        )}
        {showArchive && !isImpersonator && (
          <MenuItem
            className="delete"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, removeContentClickHandler, {
                action: INSIGHT_DROPDOWN_ACTIONS.ARCHIVE,
                props,
                hideInsight,
                updateDismissed,
                navigate,
                successCallback: onArchive,
                anchorRef
              });
            }}
          >
            {translatr('web.common.main', 'Archive')}
          </MenuItem>
        )}
        {showDeleteOption()}
        {showRemoveFromGroup && !isImpersonator && (
          <MenuItem
            className="delete"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, props.deleteSharedCard, null);
            }}
          >
            {translatr('web.common.main', 'RemoveFromGroup')}
          </MenuItem>
        )}
        {showUnpin && !isImpersonator && (
          <MenuItem
            className="unpin"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, props.unpinClickHandler, card);
            }}
          >
            {translatr('web.common.main', 'Unpin')}
          </MenuItem>
        )}
        {showReportOption && !isImpersonator && (
          <MenuItem
            className="unpin"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              onMenuItemClickHandler(e, openReasonReportModal, {
                card,
                reported_card_ids,
                dispatch,
                setOpenReportModal
              });
            }}
          >
            {translatr('web.common.main', 'ReportIt')}
          </MenuItem>
        )}
        {LD.isBlockUser() && showBlock && !isPartOfPathway && (
          <MenuItem
            className="block-user"
            onClick={e => {
              e.stopPropagation();
              closeCardActionDropdown(e, anchorRef, setOpen);
              setBlockUser(true);
            }}
          >
            {translatr('web.common.main', 'BlockUser')}
          </MenuItem>
        )}
        {showCardStats && !isImpersonator && (
          <MenuItem
            className="cardStats"
            onClick={e => {
              closeCardActionDropdown(e, anchorRef, setOpen);
              e.stopPropagation();
              setOpenStatsModal(true);
            }}
          >
            {translatr('web.common.main', 'CardInsights')}
          </MenuItem>
        )}
        {showNotifyEventToggle && !isImpersonator && (
          <MenuItem className="cardStats notify-event-toggle">
            <NotifyEvent isSwitch={true} cardExternalId={card.externalId} />
          </MenuItem>
        )}
      </>
    );
  };

  return (
    !!showDropdownButton &&
    !isReadOnlyImpersonator && (
      <span>
        <InsightDropdownButton
          cardId={encodeURIComponent(card.id)}
          cardAuthor={card.author?.fullName}
          cardTitle={standardizedTitle}
          toggleMenu={() => toggleCardActionMenu(setOpen)}
          isDisabled={card.deletedAt}
          ref={anchorRef}
          open={open}
        />
        <Popper
          className={'insight-dropdown-popper common-menu-styles'}
          open={open}
          anchorEl={anchorRef.current}
          placement={isRtl() ? 'bottom-start' : 'bottom-end'}
          role="dialog"
          aria-modal="true"
          transition
          disablePortal
        >
          {({ TransitionProps }) => {
            return (
              <Grow {...TransitionProps}>
                <Paper elevation={0} classes={{ root: 'border' }}>
                  <ClickAwayListener
                    onClickAway={e => closeCardActionDropdown(e, anchorRef, setOpen)}
                  >
                    <MenuList
                      ref={menuItemRef}
                      autoFocusItem={open}
                      id={`${card.id}-card-menu`}
                      onKeyDown={e => cardActionMenuKeyDownHandler(e, setOpen)}
                    >
                      {showUnArchive ? (
                        <>
                          {showUnArchiveOption()}
                          {showDeleteOption()}
                        </>
                      ) : (
                        getDropDownOptions()
                      )}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Grow>
            );
          }}
        </Popper>
        {openLikeModal && (
          <CardLikesModal
            card={card}
            onClose={e => {
              e.stopPropagation();
              setOpenLikeModal(!openLikeModal);
            }}
            typeOfModal="like"
            dispatch={dispatch}
          />
        )}
        {openStatsmodal && (
          <Translatr apps={['web.smartcard.insightsmodal']}>
            <CardStatisticsModal
              card={card}
              closeHandler={e => {
                e.stopPropagation();
                setOpenStatsModal(!openStatsmodal);
              }}
              dispatch={dispatch}
              uniqueCodeEnabled={config?.uniquecode_card_creation}
            />
          </Translatr>
        )}
        {openMDPModal && (
          <MDPModal card={card} closeMDPModal={() => setOpenMDPModal(false)} dispatch={dispatch} />
        )}
        {openChangeAuthorModal && (
          <ChangeAuthorModal
            card={card}
            closeHandler={() => setOpenChangeAuthorModal(false)}
            updateCard={updateCard}
            removeCardFromList={removeCardFromList}
            removeCardFromCardContainer={removeCardFromCardContainer}
            cardSectionName={cardSectionName}
            onChangeAuthorSuccess={onChangeAuthorSuccess}
          />
        )}
        {openReportModal && (
          <Translatr apps={['web.smartcard.reportmodal']}>
            <ReportModal
              card={card}
              closeHandler={e => {
                e?.stopPropagation();
                setOpenReportModal(false);
              }}
              dispatch={dispatch}
            />
          </Translatr>
        )}
        {openCreateVersionModal && (
          <Translatr apps={['web.smartcard.versionmodal']}>
            <CreateVersionModal
              card={card}
              closeHandler={e => {
                e?.stopPropagation();
                setOpenCreateVersionModal(false);
              }}
              dispatch={dispatch}
            />
          </Translatr>
        )}
        {versionHistoryModal && (
          <Translatr apps={['web.smartcard.versionmodal']}>
            <VersionHistoryModal
              card={card}
              closeHandler={e => {
                e?.stopPropagation();
                setOpenVersionHistoryModal(false);
              }}
              dispatch={dispatch}
            />
          </Translatr>
        )}
        {openAssignModal && (
          <Translatr apps={['web.smartcard.multiaction-modal']}>
            <MultiactionModal
              currentAction="assign"
              onClose={() => {
                setOpenAssignModal(!openAssignModal);
              }}
              card={card}
              onSuccess={onAssignSuccess}
            />
          </Translatr>
        )}
        {blockUser && (
          <Translatr apps={['web.user.block-user-modal']}>
            <BlockUser
              setBlockUser={setBlockUser}
              fullName={card?.author?.fullName || card?.author}
              id={card?.author?.id || card?.user_id}
              {...props}
            />
          </Translatr>
        )}
      </span>
    )
  );
};

InsightDropdownActions.defaultProps = {
  showUnPinOption: false,
  isPartOfPathway: false
};

InsightDropdownActions.propTypes = {
  isPartOfPathway: PropTypes.bool,
  card: PropTypes.object,
  channel: PropTypes.object,
  writableChannels: PropTypes.object,
  dismissible: PropTypes.bool,
  author: PropTypes.object,
  isStandalone: PropTypes.bool,
  isCompleted: PropTypes.bool,
  disableTopics: PropTypes.bool,
  editPathway: PropTypes.func,
  providerCards: PropTypes.bool,
  deleteSharedCard: PropTypes.func,
  removeCardFromList: PropTypes.func,
  unpinClickHandler: PropTypes.func,
  showTopicToggleClick: PropTypes.func,
  markAsComplete: PropTypes.func,
  type: PropTypes.string,
  cardSectionName: PropTypes.string,
  showUnPinOption: PropTypes.bool,
  reported_card_ids: PropTypes.object,
  config: PropTypes.object,
  languages: PropTypes.object,
  removeCardFromCardContainer: PropTypes.func,
  removeCardFromCarousel: PropTypes.func,
  removeDismissAssessmentFromList: PropTypes.func,
  isStandalonePage: PropTypes.bool,
  pathwayId: PropTypes.string,
  journeyId: PropTypes.string,
  isConsumptionFlow: PropTypes.bool,
  isGroupLeader: PropTypes.bool,
  hasReporters: PropTypes.bool,
  isCurrentUserAdmin: PropTypes.bool,
  isSuperAdmin: PropTypes.bool,
  isOrgAdmin: PropTypes.bool,
  currentUserId: PropTypes.string,
  updateCard: PropTypes.func,
  onAssignSuccess: PropTypes.func,
  onChangeAuthorSuccess: PropTypes.func,
  onArchive: PropTypes.func,
  onDelete: PropTypes.func
};

const mapStateToProps = state => ({
  reported_card_ids: state.users.get('reported_card_ids'),
  config: state.team.get('config'),
  languages: state.team.get('languages'),
  writableChannels: state.currentUser.get('writableChannels'),
  isGroupLeader: state.currentUser.get('isGroupLeader'),
  hasReporters: state.currentUser.get('hasReporters'),
  isSuperAdmin: state.currentUser.get('isSuperAdmin'),
  isOrgAdmin: state.currentUser.get('isOrgAdmin'),
  currentUserId: state.currentUser.get('id'),
  isCurrentUserAdmin: state.currentUser.get('isAdmin')
});

export default connect(mapStateToProps)(WithContext(InsightDropdownActions));
