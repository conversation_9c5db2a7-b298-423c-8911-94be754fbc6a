import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bool, func, object, string } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import classNames from 'classnames';

import handleMarkAsCompleteClicked from './clickHandler/handleMarkAsCompleteClicked';
import WithContext from '../../hoc/WithContext';
import configWrapper from '../../hoc/configWrapper';
import LD from '../../../../../app/containers/LDStore';
import getStandardizedTitle from '../../utils/content/getStandardizedTitle';
import { tooltipTextWrapper, shouldHideMarkAsCompleted } from '@utils/utils';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { isTileOrBigCardView } from '@utils/smartCardUtils';
import { CARD_STATE } from '../../common/constants';
import { Button } from 'centralized-design-system/src/Buttons';

class MarkAsCompleteComponent extends Component {
  constructor(props, context) {
    super(props, context);
    const { card, isLinkCardWithDisableFunctionality, isStandaloneLayout } = this.props;
    const isCompleted = card.completionState === 'COMPLETED';
    const isPathwayOrJourney = ['pack', 'journey'].includes(card.cardType);
    this.MARK_AS_COMPLETED = translatr('web.common.main', 'MarkAsCompleted');
    this.COMPLETED = translatr('web.common.main', 'Completed');

    this.state = {
      isCompleted,
      isPathwayOrJourney,
      counterCompletionState: 0,
      updateMarkAsCompleteButton: false,
      forcedUpdateMarkAsCompleteButton: false,
      markAsCompleteEnableForLink:
        isStandaloneLayout && isLinkCardWithDisableFunctionality && isCompleted,
      markAsCompleteLabel: isCompleted ? this.COMPLETED : this.MARK_AS_COMPLETED,
      showNegativeTextColor: false,
      disableMarkAsIncompleteAction: ['quiz', 'poll'].includes(card.cardType)
    };
  }

  // if there are zero cards with completionState===INITIALIZED  =>> AllCardsCompleted => true
  isAllCardsCompleted = cards =>
    !cards.filter(card => card.completionState == 'INITIALIZED').length;

  isAllCardsInsideJourneySectionCompleted = section => {
    if (section?.pack_cards?.length) {
      return this.isAllCardsCompleted(section?.pack_cards);
    }
  };

  isAllCardsInsidePathwayOrJourneyCompleted = card => {
    const isPathway = card.cardType === 'pack';
    if (isPathway) {
      return this.isAllCardsCompleted(card.packCards);
    } else {
      // if there are zero journeySection which contains any incomlete cards =>> AllCardsInsideJourneyCompleted => true
      return !card.journeySection.filter(
        section => !this.isAllCardsInsideJourneySectionCompleted(section)
      ).length;
    }
  };

  componentDidMount() {
    const {
      card,
      updateCard,
      autoCompleteBehaviourForPathwayOrJourney,
      isStandaloneLayout
    } = this.props;
    const { isCompleted, isPathwayOrJourney } = this.state;
    if (this.state.markAsCompleteEnableForLink) {
      card.markAsCompleteEnableForLink = true;
      updateCard(card);
    }
    if (
      isPathwayOrJourney &&
      autoCompleteBehaviourForPathwayOrJourney &&
      isCompleted &&
      isStandaloneLayout
    ) {
      const currentCompletionStateOfInternalCards = this.isAllCardsInsidePathwayOrJourneyCompleted(
        card
      );
      if (currentCompletionStateOfInternalCards) {
        this.setState({ disableMarkAsIncompleteAction: true });
      }
    }
  }

  static getDerivedStateFromProps(nextProp, prevState) {
    const { card } = nextProp;
    const isCompleted = card.completionState === 'COMPLETED';
    const markAsCompleteLabel = isCompleted
      ? translatr('web.common.main', 'Completed')
      : translatr('web.common.main', 'MarkAsCompleted');
    const updateMarkAsCompleteButton = !!card.updateMarkAsCompleteButton;
    const forcedUpdateMarkAsCompleteButton = !!card.forcedUpdateMarkAsCompleteButton;
    if (
      prevState.updateMarkAsCompleteButton !== updateMarkAsCompleteButton ||
      prevState.forcedUpdateMarkAsCompleteButton !== forcedUpdateMarkAsCompleteButton
    ) {
      return {
        updateMarkAsCompleteButton,
        isCompleted,
        forcedUpdateMarkAsCompleteButton,
        markAsCompleteLabel
      };
    } else if (
      card.markAsCompleteEnableForLink &&
      prevState.markAsCompleteEnableForLink !== card.markAsCompleteEnableForLink
    ) {
      const { markAsCompleteEnableForLink } = card;
      return {
        markAsCompleteEnableForLink
      };
    }
    // Return null if the state hasn't changed
    return null;
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      autoCompleteBehaviourForCardsInPathwayOrJourney,
      isStandaloneLayout,
      autoCompleteBehaviourForPathwayOrJourney
    } = this.props;
    const showMarkAsCompleteOnVisitLDFlag = LD.showMarkAsCompleteOnVisitLD();
    //This will called only for Link Card
    if (
      prevState.markAsCompleteEnableForLink !== this.state.markAsCompleteEnableForLink &&
      autoCompleteBehaviourForCardsInPathwayOrJourney &&
      !showMarkAsCompleteOnVisitLDFlag &&
      this.props.card.completionState !== 'COMPLETED'
    ) {
      handleMarkAsCompleteClicked(this, 'MarkAsCompleteTickMarkComponent');
    }
    if (
      this.props.card.state === CARD_STATE.DRAFT &&
      prevState.isCompleted !== this.state.isCompleted
    ) {
      this.resetNegativeStateOfButton(false);
    }
    if (this.state.isPathwayOrJourney && autoCompleteBehaviourForPathwayOrJourney) {
      if (prevState.isCompleted !== this.state.isCompleted) {
        if (this.state.isCompleted) {
          this.setState({ disableMarkAsIncompleteAction: true });
        } else {
          this.setState({ disableMarkAsIncompleteAction: false });
        }
      } else {
        if (this.state.isCompleted && isStandaloneLayout) {
          const currentCompletionStateOfInternalCards = this.isAllCardsInsidePathwayOrJourneyCompleted(
            this.props.card
          );
          const prevCompletionStateOfInternalCards = this.isAllCardsInsidePathwayOrJourneyCompleted(
            prevProps.card
          );
          if (prevCompletionStateOfInternalCards !== currentCompletionStateOfInternalCards) {
            if (currentCompletionStateOfInternalCards) {
              this.setState({ disableMarkAsIncompleteAction: true });
            } else {
              this.setState({ disableMarkAsIncompleteAction: false });
            }
          }
        }
      }
    }
  }

  resetNegativeStateOfButton = setComplete => {
    this.setState({
      markAsCompleteLabel: setComplete
        ? translatr('web.common.main', 'Completed')
        : translatr('web.common.main', 'MarkAsCompleted'),
      showNegativeTextColor: false
    });
  };

  render() {
    const {
      isStandaloneLayout,
      disabledMarkAsComplete,
      card,
      scrollTooltipRelativeElement,
      layout
    } = this.props;
    const {
      isCompleted,
      isPathwayOrJourney,
      markAsCompleteLabel,
      showNegativeTextColor,
      disableMarkAsIncompleteAction
    } = this.state;

    const disableLinkMessage = isPathwayOrJourney
      ? translatr('web.common.main', 'AllSmartcardFirst')
      : translatr('web.common.main', 'PleaseVisitLinkUrl');
    //Show toolTip for completed Pathway
    let disableLink = disabledMarkAsComplete && !isCompleted ? disableLinkMessage : '';
    disableLink =
      disabledMarkAsComplete && isPathwayOrJourney && isCompleted
        ? markAsCompleteLabel
        : disableLink;
    const tooltipLabel =
      disableLink ||
      (!isStandaloneLayout
        ? isCompleted
          ? translatr('web.common.main', 'Completed')
          : translatr('web.common.main', 'MarkAsCompleted')
        : '');
    const onMouseOverAllowed =
      isStandaloneLayout && isCompleted && !card.markFeatureDisabledForSource;
    const standardizedTitle = getStandardizedTitle(card);
    const cardAuthorName = card.author?.fullName
      ? `${translatr('web.common.main', 'By')} ` + card.author.fullName
      : '';
    const hideTooltip = !tooltipLabel;
    const shouldRemovedDisableBtnBg =
      disableMarkAsIncompleteAction || (disabledMarkAsComplete && !isStandaloneLayout);

    return (
      !shouldHideMarkAsCompleted(card, markAsCompleteLabel) && (
        <Tooltip
          message={tooltipTextWrapper(tooltipLabel)}
          isHtmlIncluded
          customClass={classNames(
            'card-std-button-tooltip vertical-center',
            { 'card-std-tickMark-button-tooltip': !isStandaloneLayout },
            { 'mark-as-complete-button-tooltip': isStandaloneLayout },
            {
              'mark-as-complete-button-pathway-tooltip':
                isStandaloneLayout && isPathwayOrJourney && !isCompleted && tooltipLabel
            }
          )}
          scrollTooltipRelativeElement={scrollTooltipRelativeElement}
          onScrollHideTooltipCard
          hide={hideTooltip}
          useOnPointerLeave={disabledMarkAsComplete || disableMarkAsIncompleteAction}
          tabIndex={
            !hideTooltip && (disabledMarkAsComplete || disableMarkAsIncompleteAction) ? 0 : -1
          }
          id={`mark-as-complete-tooltip-${card.id}`}
        >
          <div
            className={classNames('outline-revert picasso-mark-as-complete-button', {
              'ed-btn-outline no-border-btn':
                isStandaloneLayout && isCompleted && !showNegativeTextColor,
              'min-width-0': !isStandaloneLayout,
              'ed-btn-negative ed-btn-outline no-border-btn': showNegativeTextColor,
              cursorDefault:
                (!onMouseOverAllowed &&
                  markAsCompleteLabel === 'Completed' &&
                  !disableMarkAsIncompleteAction) ||
                (isPathwayOrJourney && isCompleted && isTileOrBigCardView(layout)),
              'new-design-completed-btn': isStandaloneLayout && isCompleted,
              'no-bg-disable-btn': shouldRemovedDisableBtnBg
            })}
          >
            <Button
              {...(!isCompleted &&
                isStandaloneLayout && {
                  color: 'primary',
                  size: 'large'
                })}
              padding={isStandaloneLayout ? 'medium' : 'xsmall'}
              aria-label={translatr(
                'web.common.main',
                'MarkascompletelabelstandardizedtitleCardauthorname',
                {
                  markAsCompleteLabel,
                  standardizedTitle,
                  cardAuthorName
                }
              )}
              aria-describedby={`mark-as-complete-tooltip-${card.id}`}
              onClick={handleMarkAsCompleteClicked.bind(
                this,
                this,
                'MarkAsCompleteTickMarkComponent',
                card.language
              )}
              disabled={disabledMarkAsComplete || disableMarkAsIncompleteAction}
              id={`card-markAsComplete-${card.id}`}
              onMouseEnter={() => {
                if (onMouseOverAllowed && !disableMarkAsIncompleteAction) {
                  this.setState({
                    markAsCompleteLabel: translatr('web.common.main', 'MarkAsIncomplete'),
                    showNegativeTextColor: true
                  });
                }
              }}
              onMouseLeave={() => {
                if (onMouseOverAllowed && !disableMarkAsIncompleteAction) {
                  this.setState({
                    markAsCompleteLabel: translatr('web.common.main', 'Completed'),
                    showNegativeTextColor: false
                  });
                }
              }}
              onFocus={() => {
                if (onMouseOverAllowed && !disableMarkAsIncompleteAction) {
                  this.setState({
                    markAsCompleteLabel: translatr('web.common.main', 'MarkAsIncomplete'),
                    showNegativeTextColor: true
                  });
                }
              }}
              onBlur={() => {
                if (onMouseOverAllowed && !disableMarkAsIncompleteAction) {
                  this.setState({
                    markAsCompleteLabel: translatr('web.common.main', 'Completed'),
                    showNegativeTextColor: false
                  });
                }
              }}
            >
              <i
                className={classNames('card-icon', {
                  'icon-check': isCompleted && !showNegativeTextColor && isStandaloneLayout,
                  'icon-check-circle-light': !isCompleted || !isStandaloneLayout,
                  'icon-check-circle-fill completed': isCompleted && !isStandaloneLayout
                })}
              />
              {isStandaloneLayout && (
                <span className="social-activity-icon-label">{markAsCompleteLabel}</span>
              )}
            </Button>
          </div>
        </Tooltip>
      )
    );
  }
}

MarkAsCompleteComponent.propTypes = {
  topMenu: object,
  isStandaloneLayout: bool,
  layout: string,
  disabledMarkAsComplete: bool,
  card: object,
  updateCard: func,
  isClcActive: bool,
  autoCompleteBehaviourForCardsInPathwayOrJourney: bool,
  isLinkCardWithDisableFunctionality: bool,
  autoCompleteBehaviourForPathwayOrJourney: bool,
  scrollTooltipRelativeElement: object
};

function mapStoreStateToProps({ team }) {
  const autoCompleteBehaviourForPathwayOrJourney =
    team?.get('OrgConfig')?.pathways?.['web/pathways/pathwaysCompletionBehaviour']?.defaultValue !==
    'manuallyCompletion';
  return {
    topMenu: team.get('config')?.OrgCustomizationConfig?.web?.topMenu,
    isClcActive: team.get('isClcActive'),
    autoCompleteBehaviourForPathwayOrJourney
  };
}

export default connect(mapStoreStateToProps)(configWrapper(WithContext(MarkAsCompleteComponent)));
