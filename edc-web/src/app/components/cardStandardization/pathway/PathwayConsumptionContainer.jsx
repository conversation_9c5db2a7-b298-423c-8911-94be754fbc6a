import React, { createRef, Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import CardConsumptionContainer from '@components/consumption/CardConsumptionContainer';
import { fetchPathwayInsideJourney } from 'edc-web-sdk/requests/journeys';
import find from 'lodash/find';
import Spinner from '@components/common/spinner';
import getCardType from '../../../../app/utils/getCardType';
import { startAssignment } from '../../../../app/actions/cardsActions';
import {
  markAsComplete,
  bulkMarkAsComplete,
  fetchCardCompletionStatus
} from 'edc-web-sdk/requests/cards.v2';
import { recordVisit } from 'edc-web-sdk/requests/analytics';
import { Translatr, translatr } from 'centralized-design-system/src/Translatr';

import {
  saveConsumptionPathway,
  removeConsumptionPathwayHistory
} from '../../../../app/actions/pathwaysActions';
import { Permissions } from '../../../../app/utils/checkPermissions';
import { hideMarkAsCompleteForVILTCardFunction } from '../../../../app/utils/hideMarkAsCompleteForVILTCardFunction';
import { markAsCompleteFunctionalityForProjectCard } from '../../../../app/utils/markAsCompleteFunctionalityForProjectCard';
import ContentUnAvailable from '@components/common/ContentUnAvailable';
import { getFields } from '../../../../app/constants/cardFields';
import filterCardsForBulkComplete from '../../../../app/utils/filterCardsForBulkComplete';
import fetchLeapCardIds from '../../../../app/utils/fetchLeapCardIds';
import capture from '../../../../app/utils/datalayer';
import getMarkAsCompleteV2Payload from '../../../../app/utils/getMarkAsCompleteV2Payload';
import getSmartCardId from '../../../../app/utils/getSmartCardId';
import ConsumptionHeader from '../common/consumptionHeader/ConsumptionHeader';
import CardNavigator from '../common/cardNavigator/CardNavigator';
import LD from '../../../../app/containers/LDStore';
import Backarrow from '@components/backarrow';
import withRouter from '@hocs/withRouter';
import { PATHWAY_CONSUMPTION_URL_MATCH_REGEX } from '../../../../app/constants/regexConstants';
import { CARD_STATUS } from '../../../constants/cardStatustypes';
import { updateButtonOutlines } from '@utils/utils';
import handleBackBtnClick from '../utils/handleBackBtnClick';
import { isCardCompleted } from '@utils/smartCardUtils';

class PathwayConsumptionContainer extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      unavailable: false,
      pathway: props.pathway && props.pathway.get('consumptionPathway'),
      slug: props.routeParams.slug,
      cardId: props.routeParams.cardId,
      smartBites:
        (props.pathway &&
          props.pathway.get('consumptionPathway') &&
          props.pathway.get('consumptionPathway').packCards) ||
        [],
      checkedCard: null,
      currentIndex: 0,
      loadChecked: true,
      fetchCard: true,
      autoComplete: true,
      pathwayStarted: false,
      isInProgressToComplete: false,
      showMarkAsComplete: LD.showMarkAsCompleteOnVisitLD(),
      loaderWithDirectionForPollAndQuizCard: false,
      isSubscriptionsAvailable: false,
      isKeyboardNavigation: false
    };
    this.leftPreviousBtnRef = createRef();
    this.rightNextBtnRef = createRef();
  }

  componentDidMount() {
    if (this.state.slug && this.state.cardId) {
      if (this.props.pathway && this.props.pathway.get('consumptionPathwayHistory')) {
        this.props.dispatch(removeConsumptionPathwayHistory());
      }
      this.fetchPathwayDetails();
    } else {
      this.setState({
        loadChecked: false,
        unavailable: true
      });
    }

    this.handleMouseDown = () => {
      this.setState({ isKeyboardNavigation: false });
    };

    this.handleKeyDown = event => {
      if (event.key === 'Tab' || event.key === 'Shift') {
        this.setState({ isKeyboardNavigation: true });
      }
    };
    document.addEventListener('mousedown', this.handleMouseDown);
    document.addEventListener('keydown', this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMouseDown);
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.location !== this.props.location &&
      this.props.navigationType === 'POP' &&
      PATHWAY_CONSUMPTION_URL_MATCH_REGEX.test(this.props.location.pathname)
    ) {
      this.arrowClick(-1, null, true);
    }

    updateButtonOutlines(
      this.state.isKeyboardNavigation,
      this.leftPreviousBtnRef.current,
      this.rightNextBtnRef.current
    );
  }

  setIsSubscriptionsAvailable = value => {
    this.setState({ isSubscriptionsAvailable: value });
  };

  focusBackButton = () => {
    if (this.state.currentIndex === 0) {
      document.getElementById('backLink')?.focus();
    } else {
      document.getElementById('left_previous_btn')?.focus();
    }
  };

  fetchPathwayDetails = (callbackCard = null) => {
    let packCardsFields = getFields('packCardsFields');
    let packFields = getFields('packFields');
    let payload = {
      // TODO: Remove filestack key from below code and add in packFields once picasso is enabled
      fields: packFields + ',filestack,pack_cards(' + packCardsFields + ')'
    };
    let pathwayData = this.props.pathway && this.props.pathway.get('consumptionPathway');
    fetchPathwayInsideJourney(this.state.slug, payload)
      .then(pathway => {
        if (pathway?.packCards?.length) {
          pathway.packCards.map(smartBite => {
            if (smartBite.resource === null) {
              delete smartBite.resource;
            }
            if (smartBite.videoStream === null) {
              delete smartBite.videoStream;
            }
            return smartBite;
          });
        }
        this.setState(
          {
            pathway: pathway,
            autoComplete: pathway.autoComplete !== undefined ? pathway.autoComplete : true
          },
          () => {
            if (pathwayData) {
              pathwayData.packCards = pathway.packCards;
            } else {
              pathwayData = pathway;
            }
            this.loadPackCards(callbackCard);
          }
        );
      })
      .catch(err => {
        console.error(`Error in PathwayConsumptionContainer.fetchPathway.func : ${err}`);
        this.setState({
          loadChecked: false,
          unavailable: true
        });
      });
  };

  loadPackCards = callbackCard => {
    if (this.state.pathway.packCards && this.state.pathway.packCards.length) {
      let smartBites = [];
      let countCard = 0;
      for (let i = 0; i < this.state.pathway.packCards.length; i++) {
        countCard++;
        if (
          this.state.pathway.packCards[i].message === 'You are not authorized to access this card'
        ) {
          this.state.pathway.packCards[i].isPrivate = true;
        }
        smartBites[i] = this.state.pathway.packCards[i];
        if (countCard === this.state.pathway.packCards.length) {
          smartBites.forEach((item, index) => {
            item.isLocked = this.state.pathway.packCards[index].locked;
          });
        }
        this.setState({ smartBites });
        if (countCard === this.state.pathway.packCards.length) {
          let pathwayDetails = this.props.pathway && this.props.pathway.get('consumptionPathway');
          if (!pathwayDetails) {
            pathwayDetails = Object.assign({}, this.state.pathway);
          }
          pathwayDetails.packCards = smartBites;
          this.props.dispatch(saveConsumptionPathway(pathwayDetails));
          let checkedCardId = callbackCard ? callbackCard.id : this.state.cardId;
          if (checkedCardId) {
            let checkedCurrentCardId =
              this.state.cardId === 'private-card' ? undefined : checkedCardId;

            let findCard = find(
              smartBites,
              el =>
                el.id == checkedCurrentCardId ||
                (checkedCurrentCardId === undefined && getSmartCardId(el) === 'private-card')
            );

            let index;
            //To check public card
            if (findCard) {
              index = smartBites.indexOf(findCard);
              this.setState({ checkedCardId: null }, this.chooseCard.bind(this, findCard, index));
            } else {
              this.setState({
                loadChecked: false,
                unavailable: true
              });
            }
          }
        }
        if (i === this.state.pathway.packCards.length - 1) {
          this.setState({ loadingPackCards: false });
        }
      }
    }
  };

  callRecordVisitAPICall = () => {
    try {
      const { checkedCard } = this.state;
      const isPrivateCard = checkedCard?.message === 'You are not authorized to access this card';
      //Added condition For not calling visit API for private card
      if (checkedCard?.id && !isPrivateCard) {
        recordVisit(checkedCard.id);
      }
    } catch (e) {}
  };

  chooseCard = (card, i, e) => {
    if (card.cardType === 'journey') {
      window.open('/journey' + `/${card.slug}`, '_blank', 'noopener,noreferrer');
      return;
    }
    if (e?.target && e.target.name === 'marked-link') {
      return;
    }
    capture('Card Viewed', card);

    this.setState(
      {
        currentIndex: i,
        loadChecked: false,
        checkedCard: card,
        fetchCard: true
      },
      () => {
        this.callRecordVisitAPICall();
        this.focusBackButton();
      }
    );
  };

  markAsComplete = (eachCard, resolve, pathwayId = null) => {
    try {
      this.setState({ isInProgressToComplete: true }, async () => {
        let payload = {};
        if (pathwayId) {
          payload = getMarkAsCompleteV2Payload(pathwayId, 'pathway');
        }
        payload['state'] = 'complete';
        try {
          let completedCardStatus;
          // For those cards which can not be marked as completed in LXP we need to fetch status of the card in LXP
          if (eachCard.markFeatureDisabledForSource) {
            const fetchCardCompletionPayload = {
              fields: 'id,completed_at,completion_state',
              'card_ids[]': eachCard.id
            };
            completedCardStatus = await fetchCardCompletionStatus(fetchCardCompletionPayload);
          } else {
            completedCardStatus = await markAsComplete(eachCard.id, payload);
          }
          this.setState(
            {
              completedCardStatus: eachCard.markFeatureDisabledForSource
                ? completedCardStatus?.cards
                : completedCardStatus,
              isInProgressToComplete: false
            },
            () => resolve && resolve(true)
          );
        } catch (error) {
          this.setState({
            completedCardStatus: null,
            isInProgressToComplete: false
          });
          console.error(`Error in PathwayConsumptionContainer.markAsComplete.func : ${error}`);
          if (resolve) resolve(true);
        }
      });
    } catch (error) {
      this.setState({
        completedCardStatus: null,
        isInProgressToComplete: false
      });
      console.error(`Error in PathwayConsumptionContainer.markAsComplete.func : ${error}`);
      if (resolve) resolve(true);
    }
  };

  bulkCompleteCards = (smartBites, oldIndex, nextIndex, direction) => {
    let cards;
    cards = filterCardsForBulkComplete(smartBites, direction, oldIndex, nextIndex);

    if (cards?.length) {
      let cardIds = cards.map(card => Number(card.id));
      let pathwayId = Number(this.state.pathway.id);

      this.setState({ isInProgressToComplete: true }, async () => {
        // EP-32831 don't wait on API to respond at the expense of user experience
        // 200ms is just enough time to appear like we're working on something
        setTimeout(() => {
          this.setState({ isInProgressToComplete: false });
        }, 200);
        let payload = { card_ids: cardIds, pathway_id: pathwayId };

        const markAsCompleteV2Payload = getMarkAsCompleteV2Payload(pathwayId, 'pathway');
        payload = { ...payload, ...markAsCompleteV2Payload };

        await bulkMarkAsComplete(payload)
          .then(completedCardStatus => {
            this.setState(
              {
                completedCardStatus: completedCardStatus,
                isInProgressToComplete: false
              },
              () => {
                if (completedCardStatus?.length) {
                  completedCardStatus.forEach((eachCard, ind) => {
                    this.changeCompleteStatus(eachCard.id, ind);
                  });
                }
              }
            );
          })
          .catch(error => {
            this.setState({
              completedCardStatus: null,
              isInProgressToComplete: false
            });
            console.error(`Error in PathwayConsumptionContainer.bulkCompleteCards.func : ${error}`);
          });
      });
    }
  };

  commonLeapLoop = (smartBites, index) => {
    let eachCard = smartBites[index];
    if (
      !(
        eachCard.completionState && eachCard.completionState.toUpperCase() === CARD_STATUS.COMPLETED
      )
    ) {
      this.changeCompleteStatus(eachCard.id, index);
    }
  };

  arrowClick = async (direction, event, isBackBtnCliked = false) => {
    event?.preventDefault();

    if (this.state.loaderWithDirectionForPollAndQuizCard !== false) {
      this.setState({
        loaderWithDirectionForPollAndQuizCard: direction,
        isInProgressToComplete: true
      });
      return;
    }
    this.setStartedReview();
    await this.checkToCompletedCard();
    let oldIndex = this.state.currentIndex;
    let pathwayDetails =
      this.props.pathway &&
      this.props.pathway.get('consumptionPathway') &&
      this.props.pathway.get('consumptionPathway').packCards;
    let smartBites = pathwayDetails || this.state.smartBites;
    let card = smartBites[oldIndex];
    let cardType = getCardType(card);
    const leapCardIds = fetchLeapCardIds(this.state.pathway);
    if (
      cardType === 'QUIZ' &&
      (card.hasAttempted || card.quiz?.hasAttempted) &&
      leapCardIds.length &&
      leapCardIds.includes(Number(card.id)) &&
      direction === 1
    ) {
      let result = card.quiz.passed;
      let inPathwayLeap = this.state.pathway.leaps.inPathways.find(
        item => item.pathwayId == this.state.pathway.id && card.id == item.cardId // Check cardId to support multiple leaps if can be created
      );
      let nextCardId = result ? inPathwayLeap.correctId : inPathwayLeap.wrongId;
      if (nextCardId) {
        let newIndex = smartBites.indexOf(smartBites.find(item => item.id == nextCardId));

        // If leap functionality is added then all cards in-between should be marked as complete
        if (newIndex > oldIndex) {
          this.bulkCompleteCards(smartBites, oldIndex, newIndex, 'forward');
          for (let index = oldIndex + 1; index < newIndex; index++) {
            this.commonLeapLoop(smartBites, index);
          }
        } else {
          // Reverse the loop for completing earlier cards
          this.bulkCompleteCards(smartBites, oldIndex, newIndex, 'reverse');
          for (let index = oldIndex - 1; index > newIndex; index--) {
            this.commonLeapLoop(smartBites, index);
          }
        }

        this.setState(
          {
            fetchCard: false
          },
          () => {
            this.setState(
              {
                currentIndex: newIndex,
                checkedCard: smartBites[newIndex],
                fetchCard: true
              },
              () => {
                capture('Card Viewed', smartBites[newIndex]);
                const cardId = getSmartCardId(smartBites[newIndex]);
                this.props.navigate(`/pathways/${this.state.slug}/cards/${cardId}`);
                this.callRecordVisitAPICall();
                this.focusBackButton();
              }
            );
          }
        );
      }
    } else if (isBackBtnCliked && direction === -1) {
      const indexOfCard = smartBites.findIndex(c => c.id == this.props.routeParams.cardId);

      this.setState(
        {
          fetchCard: false
        },
        () => {
          this.setState(
            {
              currentIndex: indexOfCard,
              checkedCard: smartBites[indexOfCard],
              cardId: this.props.routeParams.cardId,
              fetchCard: true
            },
            () => {
              capture('Card Viewed', smartBites[indexOfCard]);
              this.callRecordVisitAPICall();
              this.focusBackButton();
            }
          );
        }
      );
    } else {
      let newIndex = this.state.currentIndex + direction;
      if (newIndex >= 0 && newIndex <= smartBites.length - 1) {
        this.changeCompleteStatus(smartBites[oldIndex].id, oldIndex);
        this.setState(
          {
            fetchCard: false
          },
          () => {
            this.setState(
              {
                currentIndex: newIndex,
                checkedCard: smartBites[newIndex],
                fetchCard: true
              },
              () => {
                capture('Card Viewed', smartBites[newIndex]);
                const cardId = getSmartCardId(this.state.smartBites[newIndex]);
                this.props.navigate(`/pathways/${this.state.slug}/cards/${cardId}`);
                this.callRecordVisitAPICall();
                this.focusBackButton();
              }
            );
          }
        );
      }
    }
  };

  backToPathway = isPathwayConsumption => {
    if (window.history.length > 2 && isPathwayConsumption) {
      this.props.navigate(`/pathways/${isPathwayConsumption.slug}`);
    } else {
      this.props.navigate(`/pathways/${this.state.slug}`);
    }
  };

  updatePathwayConsumptionDetails = data => {
    let pathway = this.props.pathway && this.props.pathway.get('consumptionPathway');
    if (pathway?.packCards?.length) {
      if (data && Array.isArray(data)) {
        data.forEach(eachData => {
          let index = pathway.packCards.findIndex(card => card.id == eachData.id);
          pathway.packCards[index] = eachData;
        });
      } else {
        let index = pathway.packCards.findIndex(card => card.id == data.id);
        pathway.packCards[index] = data;
      }
      this.props.dispatch(saveConsumptionPathway(pathway));
    }
  };

  setStartedReview = () => {
    if (this.state.pathway.completionState === null && !this.state.pathwayStarted) {
      startAssignment(this.state.pathway.id)
        .then(userContentCompletion => {
          if (userContentCompletion && userContentCompletion.state === 'started') {
            this.setState({
              pathwayStarted: true
            });
          }
        })
        .catch(err => {
          console.error(`Error in PathwayConsumptionContainer.setStartedReview.func : ${err}`);
        });
    }
  };

  leapWithLockFunctionalityArrayProcessing = () => {
    let leapWithLockFunctionalityArray = [];
    let pathway = this.state.pathway;
    let pathwayDetails =
      this.props.pathway &&
      this.props.pathway.get('consumptionPathway') &&
      this.props.pathway.get('consumptionPathway').packCards;
    let smartBites = pathwayDetails || this.state.smartBites;
    if (pathway?.leaps?.inPathways) {
      pathway.leaps.inPathways.forEach(item1 => {
        smartBites.forEach(item2 => {
          if (item1.cardId == item2.id && item2.attemptedOption) {
            let lockCardId = '';
            if (item2.attemptedOption.isCorrect || item2.attemptedOption.is_correct) {
              lockCardId = item1.correctId;
            } else {
              lockCardId = item1.wrongId;
            }
            smartBites.forEach(item3 => {
              if (item3.id && item3.id == lockCardId && item3.isLocked) {
                leapWithLockFunctionalityArray.push(item3.id);
              }
            });
          }
        });
      });
    }
    let smartBitesBeforeCurrent = smartBites && smartBites.slice(0, this.state.currentIndex);

    const isCurrentCardCompleted = isCardCompleted(
      smartBites[this.state.currentIndex]?.completionState
    );

    return (
      this.state.currentIndex === 0 ||
      isCurrentCardCompleted || // Always show content for completed cards
      (smartBitesBeforeCurrent?.length &&
        smartBitesBeforeCurrent.every(
          item =>
            (item.completionState &&
              item.completionState.toUpperCase() === CARD_STATUS.COMPLETED) ||
            item.attemptedOption
        )) ||
      (smartBites[this.state.currentIndex].id !== undefined &&
        leapWithLockFunctionalityArray.indexOf(smartBites[this.state.currentIndex].id) !== -1)
    );
  };

  async checkToCompletedCard() {
    return new Promise(resolve => {
      let pathway = this.props.pathway && this.props.pathway.get('consumptionPathway');
      let checkedCardId = this.state.checkedCard && this.state.checkedCard.id;
      let currentCard = pathway.packCards.find(item => item.id == checkedCardId);
      let isCompleted =
        (currentCard?.completionState &&
          currentCard.completionState.toUpperCase() === CARD_STATUS.COMPLETED) ||
        (currentCard?.completionState &&
          currentCard.completionState.toUpperCase() === CARD_STATUS.INITIALIZED);
      let cardType = getCardType(currentCard);
      let isLinkCard =
        !this.state.showMarkAsComplete &&
        currentCard &&
        currentCard.resource &&
        (currentCard.resource.url ||
          currentCard.resource.description ||
          currentCard.resource.fileUrl) &&
        currentCard.resource.type !== 'Video' &&
        ((currentCard.readableCardType &&
          currentCard.readableCardType.toUpperCase() === 'ARTICLE') ||
          cardType === 'ARTICLE');
      const hideMarkAsCompleteForVILTCard = hideMarkAsCompleteForVILTCardFunction(
        cardType === 'VILT',
        currentCard
      );
      const isOwner = currentCard?.author && currentCard.author.id == this.props.currentUser.id;
      const disabledMarkAsCompleteForProjectCard =
        cardType === 'PROJECT' ? markAsCompleteFunctionalityForProjectCard(isOwner) : false;
      let isNotLockedCard = currentCard && !currentCard.isLocked;
      if (!isCompleted && this.state.autoComplete && currentCard && !isNotLockedCard) {
        isNotLockedCard = this.leapWithLockFunctionalityArrayProcessing();
      }
      if (
        (!isCompleted &&
          this.state.autoComplete &&
          currentCard &&
          !['poll', 'quiz'].includes(currentCard.cardType) &&
          isNotLockedCard &&
          !currentCard.isPrivate &&
          !isLinkCard &&
          !hideMarkAsCompleteForVILTCard &&
          !disabledMarkAsCompleteForProjectCard) ||
        currentCard.markFeatureDisabledForSource
      ) {
        this.markAsComplete(currentCard, resolve, pathway?.id);
      } else {
        resolve(true);
      }
    });
  }

  changeCompleteStatus = (id, i) => {
    let pathwayDetails = this.props.pathway && this.props.pathway.get('consumptionPathway');
    let isNotLockedCard = !this.state.checkedCard.locked;
    if (!isNotLockedCard) {
      isNotLockedCard = this.leapWithLockFunctionalityArrayProcessing();
    }
    let smartBites = pathwayDetails ? pathwayDetails.packCards : this.state.smartBites;

    if (this.state.completedCardStatus && Array.isArray(this.state.completedCardStatus)) {
      this.state.completedCardStatus.forEach(completedCard => {
        let index = smartBites.findIndex(
          card => card.id == (completedCard.completableId || completedCard.id)
        );
        // Update status to complete only if we found match in smartBites and latest status is COMPLETED
        if (index !== -1 && completedCard.completionState == CARD_STATUS.COMPLETED) {
          smartBites[index].isCompleted = true;
          smartBites[index].completionState = CARD_STATUS.COMPLETED;
        }
      });
      this.setState({ smartBites }, this.updatePathwayConsumptionDetails(smartBites));
    } else if (
      this.state.completedCardStatus &&
      this.state.completedCardStatus.completableId == id &&
      isNotLockedCard
    ) {
      smartBites[i].isCompleted = true;
      smartBites[i].completionState = CARD_STATUS.COMPLETED;
      this.setState({ smartBites }, this.updatePathwayConsumptionDetails(smartBites[i]));
    }
  };

  handleLoaderForPollAndQuizCard = () => {
    this.setState({
      loaderWithDirectionForPollAndQuizCard: true
    });
  };

  closeLoaderForPollAndQuizCard = () => {
    const { loaderWithDirectionForPollAndQuizCard, isInProgressToComplete } = this.state;
    this.setState(
      {
        loaderWithDirectionForPollAndQuizCard: false,
        isInProgressToComplete: false
      },
      () => {
        if (isInProgressToComplete) {
          this.arrowClick(loaderWithDirectionForPollAndQuizCard);
        }
      }
    );
  };

  render() {
    if (this.state.unavailable) {
      return <ContentUnAvailable />;
    } else if (this.state.loadChecked || this.state.isInProgressToComplete) {
      return (
        <div className="text-center centered-spinner">
          <Spinner />
        </div>
      );
    }
    let pathway = this.state.pathway;
    let leapWithLockFunctionalityArray = [];
    let pathwayDetails =
      this.props.pathway &&
      this.props.pathway.get('consumptionPathway') &&
      this.props.pathway.get('consumptionPathway').packCards;
    let smartBites = pathwayDetails || this.state.smartBites;
    let smartBitesBeforeCurrent = smartBites && smartBites.slice(0, this.state.currentIndex);
    let isLockedCard = false;
    if (smartBites[this.state.currentIndex].isLocked) {
      if (pathway?.leaps?.inPathways) {
        pathway.leaps.inPathways.forEach(item1 => {
          smartBites.forEach(item2 => {
            if (item1.cardId == item2.id && (item2.attemptedOption || item2.quiz?.hasAttempted)) {
              let lockCardId = item2.quiz?.passed ? item1.correctId : item1.wrongId;
              smartBites.forEach(item3 => {
                if (item3.id && item3.id == lockCardId && item3.isLocked) {
                  leapWithLockFunctionalityArray.push(item3.id);
                }
              });
            }
          });
        });
      }
      isLockedCard =
        smartBites[this.state.currentIndex].id !== undefined &&
        leapWithLockFunctionalityArray.indexOf(smartBites[this.state.currentIndex].id) !== -1;
    }

    const isCurrentCardCompleted = isCardCompleted(
      smartBites[this.state.currentIndex]?.completionState
    );

    let isShowLockedCardContent =
      this.state.currentIndex === 0 ||
      isCurrentCardCompleted || // Always show content for completed cards
      (smartBitesBeforeCurrent?.length &&
        smartBitesBeforeCurrent.every(
          item =>
            item.isOptional ||
            (item.completionState &&
              item.completionState.toUpperCase() === CARD_STATUS.COMPLETED) ||
            !!item.attemptedOption
        )) ||
      isLockedCard;

    let showDisprzUrl = false;
    let getDisprzUrl =
      this.state.checkedCard &&
      this.state.checkedCard.resource &&
      this.state.checkedCard.resource.url &&
      this.state.checkedCard.resource.url.indexOf('/api/v2/ilt/launch');
    if (getDisprzUrl && getDisprzUrl !== -1) {
      showDisprzUrl = true;
    }
    const nextCardAvailable = this.state.currentIndex + 1 !== this.state.smartBites.length;

    return (
      <div className="stand-alone">
        <div
          id="consumption-container-pathway"
          className="ed-ui justflex card-std-consumption-container position-relative"
        >
          <Backarrow
            label={translatr('web.common.main', 'Back')}
            useOwnLogic
            cb={() => handleBackBtnClick({ navigate: this.props.navigate })}
          />
          <ConsumptionHeader
            card={pathway}
            currentUserId={this.props.currentUserId}
            orgConfigs={this.props.orgConfigs}
            isPathway={true}
            details={this.state.smartBites.length}
            slug={this.state.slug}
            consumptionHistory={
              this.props.pathway && this.props.pathway.get('consumptionPathwayHistory')
            }
          />
          <CardNavigator
            navigationClickHandler={this.arrowClick}
            disablePrevArrow={this.state.currentIndex === 0}
            disableNextArrow={!nextCardAvailable}
            cardCount={this.state.smartBites.length}
            currentCard={this.state.currentIndex + 1}
            leftPreviousBtnRef={this.leftPreviousBtnRef}
            rightNextBtnRef={this.rightNextBtnRef}
          />

          {this.state.fetchCard && (
            <div className="flex pathway-consumption-main-container">
              <div className={classNames('width-100 min-width-0')}>
                <Translatr
                  apps={[
                    'web.smartcard.standalone',
                    LD.isSubscriptionFeatureEnabled() ? 'web.subscriptionpage.main' : ''
                  ]}
                >
                  <CardConsumptionContainer
                    card={this.state.checkedCard}
                    author={this.state.checkedCard && this.state.checkedCard.author}
                    pathwayData={pathway}
                    showComment={
                      Permissions['enabled'] !== undefined && Permissions.has('CREATE_COMMENT')
                    }
                    isShowLockedCardContent={isShowLockedCardContent}
                    showDisprzUrl={showDisprzUrl}
                    autoComplete={this.state.autoComplete}
                    handleLoaderForPollAndQuizCard={this.handleLoaderForPollAndQuizCard}
                    closeLoaderForPollAndQuizCard={this.closeLoaderForPollAndQuizCard}
                    isPartOfPathway={true}
                    backBtnTabIndex={-1}
                    isSubscriptionsAvailable={this.state.isSubscriptionsAvailable}
                  />
                </Translatr>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

PathwayConsumptionContainer.propTypes = {
  pathway: PropTypes.object,
  routeParams: PropTypes.object,
  currentUser: PropTypes.object,
  countryCode: PropTypes.string,
  edcastPricing: PropTypes.bool,
  wallet: PropTypes.bool,
  walletBalance: PropTypes.number,
  paymentGateways: PropTypes.object,
  currentUserId: PropTypes.number,
  orgConfigs: PropTypes.object,
  navigate: PropTypes.func,
  navigationType: PropTypes.string,
  location: PropTypes.object
};

function mapStoreStateToProps(state) {
  const currentUser = state.currentUser.toJS();
  const countryCode = currentUser.geoData?.country_code || 'US';
  const walletBalance = currentUser.walletBalance;
  const edcastPricing =
    state.team.get('config') && state.team.get('config').enable_smart_card_price_field;
  const wallet = state.team.get('config') && state.team.get('config').wallet;
  const paymentGateways = state.team.get('config') && state.team.get('config').payment_gateway;

  return {
    pathway: state.pathways ? state.pathways : {},
    currentUser: currentUser,
    countryCode,
    edcastPricing,
    wallet,
    walletBalance,
    paymentGateways,
    orgConfigs: state.team.get('config'),
    currentUserId: +currentUser.id
  };
}

export default withRouter(connect(mapStoreStateToProps)(PathwayConsumptionContainer));
