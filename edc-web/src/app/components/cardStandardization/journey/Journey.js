import React, { Component } from 'react';
import { object, func, bool, array, number, string } from 'prop-types';
import { connect } from 'react-redux';
import findIndex from 'lodash/findIndex';
import { translatr } from 'centralized-design-system/src/Translatr';
import capture from '../../../../app/utils/datalayer';
import {
  saveConsumptionJourneyOpenBlock,
  saveConsumptionJourneyPathwayPayload
} from '../../../../app/actions/journeyActions';
import { getSpecificUserInfo } from '../../../../app/actions/currentUserActions';
import ContentUnAvailable from '@components/common/ContentUnAvailable';
import Spinner from '@components/common/spinner';
import JourneySection from './JourneySection';
import { CardProvider } from '../context/CardContext';
import { Permissions } from '../../../../app/utils/checkPermissions';
import calculateExpireAfter from '../utils/calculateExpireAfter';
import cardConfig from '../config/cardConfig';
import commentSectionConfig from '../config/commentSection/commentSectionConfig';
import TitleWrapper from '../pathway/TitleWrapper';
import MetadataWrapper from '../metadata/MetadataWrapper';
import CommentListStd from '../comment/CommentListStd';
import consumptionJourney from './journeyFunctions/consumptionJourney';
import updateJourney from './journeyFunctions/updateJourney';
import startReview from './journeyFunctions/startReview';
import continueReview from './journeyFunctions/continueReview';
import fetchCardById from './journeyFunctions/fetchCardById';
import getCompleteCount from './journeyFunctions/getCompleteCount';
import allPathwayLockStatus from './journeyFunctions/allPathwayLockStatus';
import journeyCompleteCallBackShowBadge from './journeyFunctions/journeyCompleteCallBackShowBadge';
import { fetchPathwayCardsInsideJourney } from 'edc-web-sdk/requests/journeys';
import isWeekly from './journeyFunctions/isWeekly';
import times from 'lodash/times';
import showBadgeAndUpdateConsumptionData from '../utils/showBadgeAndUpdateConsumptionData';
import getStandardizedTitle from '../utils/content/getStandardizedTitle';
import { journeyStandaloneAndConsumptionAdditionalCardFields } from '../../../../app/constants/cardFields';
import '../CardStandardization.scss';
import ProgressBarContainer from './../common/ProgressBar/ProgressBarContainer';
import withRouter from '@hocs/withRouter';
import Backarrow from '@components/backarrow';
import { FIRST_LETTER_OF_WORD_REGEX } from '../../../../app/constants/regexConstants';
import stripHTMLTags from '@utils/stripHTMLTags';
import convertRichText from '@utils/convertRichText';

class Journey extends Component {
  constructor(props, context) {
    super(props, context);
    const { pathwaysCompletionBehaviour, routeParams } = props;
    this.state = {
      slug: routeParams.splat || routeParams.slug,
      defaultImage: '/i/images/courses/course' + (Math.floor(Math.random() * 48) + 1) + '.jpg',
      reviewStatusLabel: '',
      publishLabel: 'Publish',
      isCompleted: false,
      isUpvoted: false,
      commentsCount: 0,
      completeStatus: 0,
      votesCount: 0,
      clicked: false,
      journeySmartBites: [],
      journeyPathwayPayload: [],
      publishError: '',
      isLoaded: false,
      journey: {},
      arrIdsSections: [],
      viewPublishButton: false,
      isAllExpanded: false,
      openBlocks: [],
      journeyLoading: true,
      limit: 12,
      modalLimit: 1000,
      unavailable: false,
      unaccessible: false,
      pathwaysCompletionBehaviour,
      journeyConsumptionBehaviour: false
    };
    this.journeyConsumptionBehaviour = false;
    this.UNAUTHORIZED_MSG = 'You are not authorized to access this card';
  }

  componentDidMount() {
    const { isStandaloneModal } = this.props;

    const { currentUser } = this.props;
    const { slug } = this.state;
    const currentUserData = currentUser.toJS();
    this.props.dispatch(
      getSpecificUserInfo(
        [
          'followingChannels',
          'roles',
          'rolesDefaultNames',
          'writableChannels',
          'first_name',
          'last_name'
        ],
        currentUserData
      )
    );

    if (
      !isStandaloneModal &&
      this.props.consumptionJourney?.author &&
      this.props.consumptionJourney.slug === slug
    ) {
      const journeyConsumptionBehaviour = this.props.consumptionJourney.completedPercentage !== 98;
      this.setState(
        {
          journeyConsumptionBehaviour
        },
        () => {
          consumptionJourney(this, this.props.consumptionJourney);
          showBadgeAndUpdateConsumptionData(this, 'journey', this.props.consumptionJourney);
        }
      );
    } else {
      updateJourney(this, true, this.props.routeParams && this.props.routeParams.cardId);
    }
  }

  shouldComponentUpdate(nextProps, nextState) {
    if (this.props.currentUser !== nextProps.currentUser) {
      return true;
    } else if (nextState !== this.state) {
      return true;
    } else if (this.props.journey !== nextProps.journey) {
      return true;
    } else {
      return false;
    }
  }

  static getDerivedStateFromProps(nextProp, prevState) {
    const { routeParams } = nextProp;
    const slug = routeParams.splat || routeParams.slug;
    if (prevState.slug !== slug) {
      return {
        slug: nextProp.routeParams.slug
      };
    }
    // Return null if the state hasn't changed
    return null;
  }

  regimeStatusClick = () => {
    const { reviewStatusLabel } = this.state;
    if (reviewStatusLabel === 'Start') {
      startReview(this);
      capture('Journey Start', this.state.journey);
    } else if (reviewStatusLabel === 'Continue') {
      continueReview(this);
    }
  };

  openBlock = (index, pathway) => {
    let journeySmartBites = this.state.journeySmartBites;
    journeySmartBites[index].isOpenBlock = !this.state.journeySmartBites[index].isOpenBlock;
    let openBlocks = this.state.openBlocks;
    if (journeySmartBites[index].isOpenBlock) {
      openBlocks.push(index);
    } else {
      openBlocks = openBlocks.filter(el => +el !== +index);
    }
    if (!journeySmartBites[index].cards.length) {
      journeySmartBites[index].showLoader = true;
      this.setState({ journeySmartBites, openBlocks }, () => {
        this.props.dispatch(saveConsumptionJourneyOpenBlock(openBlocks));
        let payload = {
          pathway_id: pathway.id,
          is_standalone_page: true,
          limit: this.state.limit,
          offset: journeySmartBites[index].offset,
          fields: journeyStandaloneAndConsumptionAdditionalCardFields
        };
        fetchPathwayCardsInsideJourney(this.state.journey.id, payload)
          .then(data => {
            for (let i = 0; i < data.cards.length; i++) {
              if (!this.state.isOwner && isWeekly(this, index)) {
                data.cards[i] = { locked: true, isLocked: false };
              } else {
                if (data.cards[i] && data.cards[i].message === this.UNAUTHORIZED_MSG) {
                  data.cards[i].isPrivate = true;
                }
                data.cards[i].locked = false;
              }
            }
            let journeyPathwayPayload = this.state.journeyPathwayPayload;
            journeyPathwayPayload.push({ id: pathway.id, payload: payload });
            journeySmartBites[index].cards = data.cards;
            journeySmartBites[index].showLoader = false;
            journeySmartBites[index].offset = journeySmartBites[index].offset + this.state.limit;
            journeySmartBites[index].totalCount = data.totalCount;
            let setStateObject = {};
            setStateObject['journeySmartBites'] = journeySmartBites;
            setStateObject['journeyPathwayPayload'] = journeyPathwayPayload;
            this.state.journeySmartBites.map(journeySmartBite => {
              if (journeySmartBite.visible) {
                setStateObject['isAllExpanded'] = !!journeySmartBite.isOpenBlock;
              }
            });
            this.setState(setStateObject);
            this.props.dispatch(saveConsumptionJourneyPathwayPayload(journeyPathwayPayload));
          })
          .catch(err => {
            journeySmartBites[index].showLoader = false;
            let setStateObject = {};
            setStateObject['journeySmartBites'] = journeySmartBites;
            this.state.journeySmartBites.map(journeySmartBite => {
              if (journeySmartBite.visible) {
                setStateObject['isAllExpanded'] = !!journeySmartBite.isOpenBlock;
              }
            });
            this.setState(setStateObject);
            console.error(
              `Error in Journey.fetchPathwayCardsInsideJourney.openBlock.func : ${err}`
            );
          });
      });
    } else {
      let setStateObject = {};
      setStateObject['journeySmartBites'] = journeySmartBites;
      setStateObject['openBlocks'] = openBlocks;
      this.state.journeySmartBites.map(journeySmartBite => {
        if (journeySmartBite.visible) {
          setStateObject['isAllExpanded'] = !!journeySmartBite.isOpenBlock;
        }
      });
      this.setState(setStateObject);
      this.props.dispatch(saveConsumptionJourneyOpenBlock(openBlocks));
    }
  };

  loadMore = (index, pathway) => {
    let journeySmartBites = this.state.journeySmartBites;
    journeySmartBites[index].showLoader = true;
    this.setState({ journeySmartBites }, () => {
      let payload = {
        pathway_id: pathway.id,
        is_standalone_page: true,
        limit: this.state.limit,
        offset: journeySmartBites[index].offset,
        fields: journeyStandaloneAndConsumptionAdditionalCardFields
      };
      fetchPathwayCardsInsideJourney(this.state.journey.id, payload)
        .then(data => {
          for (let i = 0; i < data.cards.length; i++) {
            if (!this.state.isOwner && isWeekly(this, index)) {
              data.cards[i] = { locked: true, isLocked: false };
            } else {
              if (data.cards[i] && data.cards[i].message === this.UNAUTHORIZED_MSG) {
                data.cards[i].isPrivate = true;
              }
              data.cards[i].locked = false;
            }
          }
          let journeyPathwayPayload = this.state.journeyPathwayPayload;
          for (let j = 0; j <= journeyPathwayPayload.length - 1; j++) {
            if (journeyPathwayPayload[j].id === pathway.id) {
              journeyPathwayPayload[j] = { id: pathway.id, payload: payload };
            }
          }
          journeySmartBites[index].cards = [...journeySmartBites[index].cards, ...data.cards];
          journeySmartBites[index].showLoader = false;
          journeySmartBites[index].offset = journeySmartBites[index].offset + this.state.limit;
          journeySmartBites[index].totalCount = data.totalCount;
          this.setState({ journeySmartBites, journeyPathwayPayload });
          this.props.dispatch(saveConsumptionJourneyPathwayPayload(journeyPathwayPayload));
        })
        .catch(err => {
          journeySmartBites[index].showLoader = false;
          this.setState({ journeySmartBites });
          console.error(`Error in Journey.fetchPathwayCardsInsideJourney.loadMore.func : ${err}`);
        });
    });
  };

  findCheckedCardAtList = id => {
    fetchCardById(this, id)
      .then(async data => {
        let journeyCompletionState =
          this.state.journey.completionState &&
          this.state.journey.completionState.toUpperCase() === 'COMPLETED';
        let cardCompletionState =
          data?.completionState && data.completionState.toUpperCase() === 'COMPLETED';
        if (journeyCompletionState && !cardCompletionState && this.state.journey.id !== data.id) {
          await this.checkCompletionBehaviour();
        } else if (
          !journeyCompletionState &&
          cardCompletionState &&
          this.state.journey.id !== data.id
        ) {
          this.journeyConsumptionBehaviour = true;
        }
        let journeySmartBites = this.state.journeySmartBites;
        let findCardIndex;
        let findSectionIndex;
        for (let i = 0; i < journeySmartBites.length; i++) {
          let childIndex = findIndex(journeySmartBites[i].cards, card => {
            return card.id === id;
          });
          if (childIndex !== -1) {
            findCardIndex = childIndex;
            findSectionIndex = i;
          }
        }
        if (journeySmartBites[findSectionIndex]) {
          data.isLocked = journeySmartBites[findSectionIndex].cards[findCardIndex].isLocked;
          data.locked = journeySmartBites[findSectionIndex].cards[findCardIndex].locked;
          journeySmartBites[findSectionIndex].cards[findCardIndex] = data;
          this.setState({ journeySmartBites }, () => getCompleteCount(this));
        } else {
          getCompleteCount(this);
        }
      })
      .catch(err => {
        console.error(`Error in Journey.findCheckedCardAtList.fetchCardById.func : ${err}`);
      });
  };

  allPathwaysToggle = pathwayLockStatus => {
    const { journeySmartBites, limit, journey, isOwner, journeyPathwayPayload } = this.state;
    const isAllExpanded = !this.state.isAllExpanded;
    let openBlocks = isAllExpanded ? times(journeySmartBites.length) : [];
    for (let i = 0; i < journeySmartBites.length; i++) {
      let lockCurrentJourneySmartbite = pathwayLockStatus?.[journeySmartBites[i].id];
      journeySmartBites[i].isOpenBlock = lockCurrentJourneySmartbite ? false : isAllExpanded;
      if (
        !lockCurrentJourneySmartbite &&
        isAllExpanded &&
        !journeySmartBites[i].cards.length &&
        journeySmartBites[i].totalCount !== 0
      ) {
        journeySmartBites[i].showLoader = true;
        this.setState({ journeySmartBites, openBlocks }, () => {
          this.props.dispatch(saveConsumptionJourneyOpenBlock(openBlocks));
          let payload = {
            pathway_id: journeySmartBites[i].id,
            is_standalone_page: true,
            limit: limit,
            offset: journeySmartBites[i].offset,
            fields: journeyStandaloneAndConsumptionAdditionalCardFields
          };
          fetchPathwayCardsInsideJourney(journey.id, payload)
            .then(data => {
              for (let j = 0; j < data.cards.length; j++) {
                if (!isOwner && isWeekly(this, i)) {
                  data.cards[j] = { locked: true, isLocked: false };
                } else {
                  if (data.cards[j] && data.cards[j].message === this.UNAUTHORIZED_MSG) {
                    data.cards[j].isPrivate = true;
                  }
                  data.cards[j].locked = false;
                }
              }
              journeyPathwayPayload.push({ id: journeySmartBites[i].id, payload: payload });
              journeySmartBites[i].cards = data.cards;
              journeySmartBites[i].showLoader = false;
              journeySmartBites[i].offset = journeySmartBites[i].offset + limit;
              journeySmartBites[i].totalCount = data.totalCount;
              this.setState({ journeySmartBites, journeyPathwayPayload });
              this.props.dispatch(saveConsumptionJourneyPathwayPayload(journeyPathwayPayload));
            })
            .catch(err => {
              journeySmartBites[i].showLoader = false;
              this.setState({ journeySmartBites });
              console.error(
                `Error in Journey.fetchPathwayCardsInsideJourney.allPathwaysToggle.func : ${err}`
              );
            });
        });
      } else if (isAllExpanded && !journeySmartBites[i].cards.length) {
        this.props.dispatch(saveConsumptionJourneyOpenBlock(openBlocks));
        this.setState({ journeySmartBites, openBlocks });
      }
    }
    if (!isAllExpanded) {
      this.setState({ journeySmartBites, isAllExpanded, openBlocks });
    } else {
      this.setState({ isAllExpanded, openBlocks });
    }
    this.props.dispatch(saveConsumptionJourneyOpenBlock(openBlocks));
  };

  cardUpdated = id => {
    if (id !== this.state.journey.id) {
      this.findCheckedCardAtList(this, id);
    } else {
      updateJourney(this);
    }
  };

  updateCard = (journey, isCompletionUpdate, data) => {
    if (isCompletionUpdate) {
      updateJourney(this);
      if (data?.state === 'completed' && data?.userBadge) {
        journeyCompleteCallBackShowBadge(this, data?.userBadge);
      }
    } else {
      this.setState({ journey, commentsCount: journey.commentsCount });
    }
  };

  makeForcedUpdateMarkAsCompleteButtonTrue = () => {
    const journey = this.state.journey;
    journey.forcedUpdateMarkAsCompleteButton = true;
    return journey;
  };

  updateParentOnCompletionUpdate = (userBadge = null) => {
    if (userBadge?.id) {
      journeyCompleteCallBackShowBadge(this, userBadge);
    }
    updateJourney(this);
    const journey = this.makeForcedUpdateMarkAsCompleteButtonTrue();
    this.setState({ journey: journey });
  };

  componentDidUpdate(prevProps, prevState) {
    const orgName = this.props.orgName;

    const journeyTitle = Object.keys(this.state.journey).length
      ? `${getStandardizedTitle(this.state.journey)?.replace(FIRST_LETTER_OF_WORD_REGEX, match =>
          match.toUpperCase()
        )} Journey - ${orgName}`
      : `Journey - ${orgName}`;

    const sanitizedTitle = stripHTMLTags(convertRichText(journeyTitle));

    document.title = sanitizedTitle;

    if (prevState.slug && this.state.slug && prevState.slug !== this.state.slug) {
      updateJourney(this, true);
    }
  }

  changeEntityLanguageCard = payload => {
    this.setState({
      ...this.state,
      journey: {
        ...this.state.journey,
        cardTitle: payload.title,
        cardMessage: payload.message
      }
    });
  };

  render() {
    const {
      journey,
      completeStatus,
      reviewStatusLabel,
      commentsCount,
      isCompleted,
      unavailable,
      journeyLoading,
      unaccessible,
      journeySmartBites,
      isLoaded,
      isAllExpanded
    } = this.state;
    journey.completedPercentage = completeStatus;
    const updatedCard = { ...journey };
    const { dataCard } = this.props;

    updatedCard.commentsCount = commentsCount;
    updatedCard.isCompleted = isCompleted;
    //Required this for updating mark as complete button when going back to tile layout
    updatedCard.forcedUpdateMarkAsCompleteButton = !dataCard?.forcedUpdateMarkAsCompleteButton;
    const pathwayLockStatus = allPathwayLockStatus(this);
    const layoutType = 'Standalone';
    const {
      orgConfigs,
      currentUserId,
      currentUserCountryCode,
      currentUserIsAdmin,
      followingChannels,
      orgLanguages,
      proficiencyLevels,
      currentUserLang
    } = this.props;
    const currentUserDetails = {
      currentUserId,
      currentUserCountryCode,
      currentUserIsAdmin,
      followingChannels
    };
    const filestackExpireSeconds = calculateExpireAfter(
      orgConfigs.filestack_url_expire_after_seconds
    );
    if (unaccessible) {
      return (
        <ContentUnAvailable
          message={translatr('web.common.main', 'YouDoNotHavePermissionToAccessThisJourney')}
        />
      );
    }

    if (unavailable) {
      return <ContentUnAvailable />;
    }
    let assignmentDetails = {};
    if (journey.assigner) {
      const {
        assigner: { fullName: assignedBy, id: assignorId },
        dueAt,
        assignedAt
      } = journey;
      assignmentDetails = {
        assignedBy,
        assignedAt,
        dueAt,
        isAssignmentPage: true,
        isStandalone: true,
        assignorId,
        currentUserId
      };
    }
    const isPathwayJourneyEditor = false;
    const isPathwayOrJourneyAssigned = false;
    const config = cardConfig(
      layoutType,
      journey,
      orgConfigs,
      currentUserDetails,
      true,
      isPathwayOrJourneyAssigned,
      isPathwayJourneyEditor,
      assignmentDetails,
      false,
      orgLanguages,
      false,
      false,
      proficiencyLevels,
      currentUserLang
    );
    const contextProvider = {
      card: journey,
      updateCard: this.updateCard,
      cardUpdated: this.updateParentOnCompletionUpdate,
      cardStdType: 'JourneyStandAlone',
      changeEntityLanguageCard: this.changeEntityLanguageCard
    };
    const isActionsDisabled = false;
    const layout = 'Standalone';
    let commentSection = false;
    if (journey.id) {
      commentSection = commentSectionConfig(layout, journey, isActionsDisabled);
    }

    return (
      <div>
        {journeyLoading ? (
          <div>
            <div className="text-center">
              <Spinner />
            </div>
          </div>
        ) : (
          <>
            <h1 className="screen-reader-journey-title sr-only">{getStandardizedTitle(journey)}</h1>
            <div className="ed-ui journey-std-wrapper">
              <Backarrow label={translatr('web.common.main', 'Back')} />

              {journey.id && (
                <CardProvider value={contextProvider}>
                  <TitleWrapper
                    configContent={config}
                    cardId={journey.id}
                    filestackUrlExpire={filestackExpireSeconds}
                    currentUserId={currentUserId}
                    configFooter={config.footer}
                    source={'journey'}
                  />
                  <ProgressBarContainer
                    badges={journey.badging}
                    completedPercentage={journey.completedPercentage}
                    reviewStatusLabel={reviewStatusLabel}
                    regimeStatusClick={this.regimeStatusClick}
                    badgeTitle={config.header.title}
                  />
                  {journeySmartBites.length > 0 && isLoaded && (
                    <div className="expandCollapse m-margin-bottom font-size-xl text-right">
                      <button
                        className="pointer expand-collapse"
                        onClick={this.allPathwaysToggle.bind(this, pathwayLockStatus)}
                      >
                        {isAllExpanded
                          ? translatr('web.common.main', 'CollapseAll')
                          : translatr('web.common.main', 'ExpandAll')}
                        <i
                          className={`s-padding icon-angle-${
                            isAllExpanded ? 'up-arrow' : 'down-arrow'
                          }`}
                        ></i>
                      </button>
                    </div>
                  )}
                  {journeySmartBites?.map((obj, indexSection) => {
                    return (
                      <JourneySection
                        key={`${journey?.id || ''}-${indexSection}`}
                        journeySection={obj}
                        routeParams={this.props.routeParams}
                        indexSection={indexSection}
                        journey={journey}
                        currentSection={indexSection}
                        isCompleted={isCompleted}
                        findCheckedCardAtList={this.findCheckedCardAtList}
                        cardUpdated={this.cardUpdated.bind(this, journey.id)}
                        isStandaloneModal={this.props.isStandaloneModal}
                        openBlock={this.openBlock}
                        loadMore={this.loadMore}
                        lockJourneySection={pathwayLockStatus?.[obj.id]}
                        updateParentOnCompletionUpdate={this.updateParentOnCompletionUpdate}
                      />
                    );
                  })}
                  <div className="pathway-std-additional-metadata card-std-outline">
                    <MetadataWrapper
                      configData={config.metadata.configData}
                      configMetadata={config.metadata}
                      hideMarginTop={true}
                      skills={journey.userTaxonomyTopics}
                    />
                  </div>
                  <CommentListStd {...commentSection} />
                </CardProvider>
              )}
            </div>
          </>
        )}
      </div>
    );
  }
}

Journey.propTypes = {
  routeParams: object,
  isStandaloneModal: bool,
  cardUpdated: func,
  currentUser: object,
  currentUserId: number,
  currentUserCountryCode: string,
  currentUserIsAdmin: bool,
  followingChannels: array,
  orgConfigs: object,
  pathwaysCompletionBehaviour: string,
  redirectToMeLabel: string,
  journey: object,
  consumptionJourney: object,
  consumptionJourneyHistoryData: object,
  consumptionJourneyPathwayPayload: object,
  consumptionJourneyOpenBlock: array,
  dataCard: object,
  orgLanguages: object,
  orgName: string,
  proficiencyLevels: array,
  currentUserLang: string
};

function mapStoreStateToProps(state) {
  const currentUser = state.currentUser;
  const orgConfigs = state.team.get('OrgConfig');
  const orgLanguages = state.team.get('languages');
  const pathwaysCompletionBehaviour =
    orgConfigs?.pathways?.['web/pathways/pathwaysCompletionBehaviour']?.defaultValue ||
    'manuallyCompletion';
  const topMenuObj = orgConfigs?.topMenu;
  const redirectToMeLabel =
    topMenuObj?.['web/topMenu/me']?.label || topMenuObj?.['web/topMenu/me']?.defaultLabel || 'Me';
  return {
    currentUser,
    orgName: state.team.get('name'),
    currentUserId: +currentUser.get('id'),
    currentUserCountryCode: currentUser.get('countryCode') || 'us',
    currentUserIsAdmin: currentUser.get('isAdmin') && Permissions.has('ADMIN_ONLY'),
    followingChannels: currentUser.get('followingChannels'),
    journey: state.journey ? state.journey : {},
    orgConfigs: state.team.get('config'),
    orgLanguages,
    pathwaysCompletionBehaviour,
    redirectToMeLabel,
    consumptionJourney: state.journey?.get('consumptionJourney'),
    consumptionJourneyHistoryData: state.journey?.get('consumptionJourneyHistory'),
    consumptionJourneyPathwayPayload: state.journey?.get('consumptionJourneyPathwayPayload'),
    consumptionJourneyOpenBlock: state.journey?.get('consumptionJourneyOpenBlock'),
    proficiencyLevels: state.team.get('proficiencyLevels'),
    currentUserLang: state.currentUser.get('profile').get('language')
  };
}

export default withRouter(connect(mapStoreStateToProps)(Journey));
