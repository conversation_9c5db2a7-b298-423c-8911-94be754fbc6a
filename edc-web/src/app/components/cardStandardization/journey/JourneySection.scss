@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui.journey-std-wrapper {
  .expand-collapse {
    i {
      vertical-align: bottom;
      font-size: var(--ed-font-size-xl);
    }
    &:hover {
      color: var(--ed-primary-base);
    }
  }
  .journey-section-container {
    background-color: var(--ed-white);
    margin-bottom: var(--ed-spacing-base);
    box-shadow: var(--ed-shadow-base);
    @include max-screen-width($breakpoint-xs) {
      border-radius: 0;
    }
    border-radius: var(--ed-border-radius-lg);
    padding: var(--ed-spacing-2xs);

    .journey-section-header {
      background-color: var(--ed-white);
      min-height: rem-calc(41);

      &.journey-section-open {
        margin-bottom: var(--ed-spacing-base);
      }

      .icon-lock {
        font-size: var(--ed-font-size-lg);
        color: var(--ed-border-color);
      }

      .mark-as-complete-icon {
        margin-left: 0;
        margin-right: var(--ed-spacing-base);
        color: var(--ed-border-color);
        font-size: var(--ed-font-size-lg);

        &.icon-check-circle-light {
          &.in-progress {
            color: var(--ed-primary-base);
          }
        }
        &.icon-check {
          color: var(--ed-white);
          background-color: var(--ed-primary-base);
          width: rem-calc(20);
          height: rem-calc(20);
          font-size: var(--ed-font-size-2xs);
          border-radius: var(--ed-border-radius-circle);
          display: block !important;
          padding: rem-calc(5);
        }
      }

      .journey-section-card-std-header {
        width: 100%;
      }

      .section-label {
        font-size: var(--ed-font-size-base);
        color: var(--ed-text-color-supporting);
      }

      .section-title {
        font-size: var(--ed-font-size-base);
        color: var(--ed-text-color-primary);
      }

      .section-cards-count,
      .section-progress-label {
        color: var(--ed-text-color-supporting);
        font-size: var(--ed-font-size-supporting);
      }

      .journey-section-progress-wrapper {
        width: rem-calc(192);
        @include min-screen-width($breakpoint-xs) {
          width: rem-calc(169);
        }
        @include min-screen-width($breakpoint-sm) {
          width: rem-calc(300);
        }
        .progress-container .progress-value {
          margin-right: 2rem;
        }
      }

      .journey-section-collapse-button {
        width: rem-calc(38);
        height: rem-calc(38);
        padding: 0;
        i {
          font-size: var(--ed-font-size-3xl);
          color: var(--ed-text-color-supporting);
        }
      }
    }
    .smartbites-block {
      border-top: var(--ed-border-size-sm) solid var(--ed-border-color);
      flex-direction: column;
    }

    .picasso-pathway-journey-cards-container {
      @include max-screen-width(583px) {
        div {
          .card-std-tile.ed-ui {
            margin: 0 0.5rem 1rem;
          }
        }
      }
      @media screen and (min-width: 584px) and (max-width: 907px) {
        div:nth-child(2n + 1) {
          .card-std-tile.ed-ui {
            margin-left: 0;
            margin-right: var(--ed-spacing-2xs);
          }
        }

        div:nth-child(2n) {
          .card-std-tile.ed-ui {
            margin-right: 0;
            margin-left: var(--ed-spacing-2xs);
          }
        }
      }
      @media screen and (min-width: $breakpoint-md) and (max-width: 1199px) {
        div:nth-child(3n + 1) {
          .card-std-tile.ed-ui {
            margin-left: 0;
            margin-right: var(--ed-spacing-2xs);
          }
        }

        div:nth-child(3n) {
          .card-std-tile.ed-ui {
            margin-right: 0;
            margin-left: var(--ed-spacing-2xs);
          }
        }
      }
      @include min-screen-width($breakpoint-lg) {
        div {
          .card-std-tile.ed-ui {
            margin: 0 0.5rem 1rem;
          }
        }
        div:nth-child(4n + 1) {
          .card-std-tile.ed-ui {
            margin-left: 0;
            margin-right: var(--ed-spacing-2xs);
          }
        }

        div:nth-child(4n) {
          .card-std-tile.ed-ui {
            margin-right: 0;
            margin-left: var(--ed-spacing-2xs);
          }
        }
      }
      padding-top: rem-calc(16);
      margin: auto;
      @include min-screen-width($breakpoint-xxs) {
        width: rem-calc(292);
      }
      @include min-screen-width(584px) {
        width: rem-calc(568);
      }
      @include min-screen-width($breakpoint-md) {
        width: rem-calc(860);
      }
      @include min-screen-width($breakpoint-lg) {
        width: rem-calc(1152);
      }

      .pathway-card-index {
        left: rem-calc(-1);
      }

      .card-std-tile {
        border: var(--ed-border-size-sm) solid var(--ed-border-color);
        box-shadow: none;
      }
    }
    .center-spinner {
      margin: 0 auto;
    }
  }

  .tile-view-loader {
    border: var(--ed-border-size-sm) solid var(--ed-border-color);
    box-shadow: none;
    margin: 0 0.5rem 1rem;
  }
  @media screen and (min-width: $breakpoint-xs) and (max-width: 991px) {
    div:nth-child(2n + 1) {
      margin-left: 0;
    }

    div:nth-child(2n) {
      margin-right: 0;
    }
  }
  @media screen and (min-width: $breakpoint-md) and (max-width: 1199px) {
    .tile-view-loader:nth-child(3n + 1) {
      margin-left: 0;
    }

    .tile-view-loader:nth-child(3n) {
      margin-right: 0;
    }
  }
  @include min-screen-width($breakpoint-lg) {
    .tile-view-loader:nth-child(4n + 1) {
      margin-left: 0;
    }

    .tile-view-loader:nth-child(4n) {
      margin-right: 0;
    }
  }

  .journey-load-more {
    padding: rem-calc(5) rem-calc(10);
    text-align: center;
    margin: auto;
  }
}

.journey-std-section {
  color: var(--ed-text-color-primary);
}
.journey-std-section-button {
  outline: none;
}

/*
 * Hides journey titles visually while keeping them accessible to screen readers.
 * This improves accessibility by providing context to users of assistive technologies
 * without affecting the visual layout.
 */
.screen-reader-journey-title {
  width: rem-calc(10);
  clip: rect(0, 0, 0, 0);
}
