import React, { createRef, Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import CardConsumptionContainer from '@components/consumption/CardConsumptionContainer';
import { fetchJourney } from 'edc-web-sdk/requests/journeys';
import { translatr, Translatr } from 'centralized-design-system/src/Translatr';
import Spinner from '@components/common/spinner';
import getCardType from '../../../../app/utils/getCardType';
import { startAssignment } from '../../../../app/actions/cardsActions';
import {
  markAsComplete,
  bulkMarkAsComplete,
  fetchCardCompletionStatus
} from 'edc-web-sdk/requests/cards.v2';
import { recordVisit } from 'edc-web-sdk/requests/analytics';

import {
  saveConsumptionJourney,
  removeConsumptionJourneyHistory
} from '../../../../app/actions/journeyActions';
import { Permissions } from '../../../../app/utils/checkPermissions';
import { hideMarkAsCompleteForVILTCardFunction } from '../../../../app/utils/hideMarkAsCompleteForVILTCardFunction';
import { markAsCompleteFunctionalityForProjectCard } from '../../../../app/utils/markAsCompleteFunctionalityForProjectCard';
import unescape from 'lodash/unescape';
import some from 'lodash/some';
import ContentUnAvailable from '@components/common/ContentUnAvailable';
import filterCardsForBulkComplete from '../../../../app/utils/filterCardsForBulkComplete';
import fetchLeapCardIds from '../../../../app/utils/fetchLeapCardIds';
import getMarkAsCompleteV2Payload from '../../../../app/utils/getMarkAsCompleteV2Payload';
import getSmartCardId from '../../../../app/utils/getSmartCardId';
import { open_v2 as openSnackBar } from '../../../../app/actions/snackBarActions';
import processJourneyData from '@components/journey/JourneyConsumptionFunctions/processJourneyData';
import ConsumptionHeader from '../common/consumptionHeader/ConsumptionHeader';
import CardNavigator from '../common/cardNavigator/CardNavigator';
import LD from '../../../../app/containers/LDStore';
import Backarrow from '@components/backarrow';
import withRouter from '@hocs/withRouter';
import { JOURNEY_CONSUMPTION_URL_MATCH_REGEX } from '../../../../app/constants/regexConstants';
import findJourneySectionAndCardIndex from '../utils/findJourneySectionAndCardIndex';
import { CARD_STATUS } from '../../../constants/cardStatustypes';
import { updateButtonOutlines } from '@utils/utils';
import handleBackBtnClick from '../utils/handleBackBtnClick';
import { isCardCompleted } from '@utils/smartCardUtils';

class JourneyConsumptionContainer extends Component {
  constructor(props, context) {
    super(props, context);

    this.state = {
      unavailable: false,
      journey: props.journey && props.journey.get('consumptionJourney'),
      slug: props.routeParams.slug,
      cardId: props.routeParams.cardId,

      smartBites:
        (props.journey &&
          props.journey.get('consumptionJourney') &&
          props.journey.get('consumptionJourney').packCards) ||
        [],
      checkedCard: null,
      currentIndex: 0,
      loadChecked: true,
      fetchCard: true,
      autoComplete: true,
      journeyStarted: false,
      isInProgressToComplete: false,
      showMarkAsComplete: LD.showMarkAsCompleteOnVisitLD(),
      loaderWithDirectionForPollAndQuizCard: false,
      isKeyboardNavigation: false
    };

    let config = this.props.team.get('OrgConfig');
    this.completeMethodConf = config?.pathways?.['web/pathways/pathwaysComplete']?.defaultValue;
    this.journeySubTypes = ['self_paced', 'weekly', 'progressive_unlocking'];
    this.leftPreviousBtnRef = createRef();
    this.rightNextBtnRef = createRef();
  }

  componentDidMount() {
    if (this.state.slug && this.state.cardId) {
      if (this.props.journey && this.props.journey.get('consumptionJourneyHistory')) {
        this.props.dispatch(removeConsumptionJourneyHistory());
      }
      this.fetchJourneyDetails();
    } else {
      this.setState({
        loadChecked: false,
        unavailable: true
      });
    }

    this.handleMouseDown = () => {
      this.setState({ isKeyboardNavigation: false });
    };

    this.handleKeyDown = event => {
      if (event.key === 'Tab' || event.key === 'Shift') {
        this.setState({ isKeyboardNavigation: true });
      }
    };
    document.addEventListener('mousedown', this.handleMouseDown);
    document.addEventListener('keydown', this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMouseDown);
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.location !== this.props.location &&
      this.props.navigationType === 'POP' &&
      JOURNEY_CONSUMPTION_URL_MATCH_REGEX.test(this.props.location.pathname)
    ) {
      this.arrowClick(-1, null, true);
    }

    updateButtonOutlines(
      this.state.isKeyboardNavigation,
      this.leftPreviousBtnRef.current,
      this.rightNextBtnRef.current
    );
  }

  focusBackButton = () => {
    if (this.state.currentSection === 0 && this.state.currentIndex === 0) {
      document.getElementById('backLink')?.focus();
    } else {
      document.getElementById('left_previous_btn')?.focus();
    }
  };

  fetchJourneyDetails = (callbackCard = null) => {
    // skip api call if journey data already exist
    if (this.state.journey) {
      processJourneyData.call(this, callbackCard);
      return;
    }

    // call journey details api call
    let payload = { is_standalone_page: false };
    fetchJourney(this.state.slug, payload)
      .then(cardRes => {
        this.setState({ journey: cardRes }, () => {
          processJourneyData.call(this, callbackCard);
        });
      })
      .catch(err => {
        this.setState({
          loadChecked: false,
          unavailable: true
        });
        console.error(`Error in JourneyConsumptionContainer.fetchJourneyDetails.func: ${err}`);
      });
  };

  callRecordVisitAPICall = () => {
    try {
      const { checkedCard } = this.state;
      const isPrivateCard = checkedCard?.message === 'You are not authorized to access this card';
      //Added condition For not calling visit API for private card
      if (checkedCard?.id && !isPrivateCard) {
        recordVisit(checkedCard.id);
      }
    } catch (e) {}
  };

  arrowClickForProgressiveJourney = async (direction, pathwayIndex) => {
    let stopNavigation = false;
    if (direction === 1) {
      let payload = { is_standalone_page: true };
      try {
        const cardRes = await fetchJourney(this.state.slug, payload);

        if (cardRes.journeySection[pathwayIndex].completed_percentage !== 100) {
          this.props.navigate(`/journey/${cardRes.slug}`);
          stopNavigation = true;
        } else {
          stopNavigation = false;
        }
      } catch (err) {
        console.error(
          `Error in JourneyConsumptionContainer.arrowClickForProgressiveJourney.fetchJourney.func : ${err}`
        );
        stopNavigation = false;
      }
    }
    return stopNavigation;
  };

  bulkCompleteCards = async (section, oldIndex, nextIndex, direction) => {
    let cards;
    cards = filterCardsForBulkComplete(section.cards, direction, oldIndex, nextIndex);

    if (cards?.length) {
      let cardIds = cards.map(card => Number(card.id));
      let pathwayId = Number(section.id);
      let payload = { card_ids: cardIds, pathway_id: pathwayId };
      if (this.state?.journey?.id) {
        const journeyId = this.state.journey.id;
        const markAsCompleteV2Payload = getMarkAsCompleteV2Payload(journeyId, 'journey');
        payload = { ...payload, ...markAsCompleteV2Payload };
      }
      this.setState({ isInProgressToComplete: true }, async () => {
        await bulkMarkAsComplete(payload)
          .then(completedCardStatus => {
            this.setState(
              {
                completedCardStatus,
                isInProgressToComplete: false
              },
              () => {
                this.bulkChangeCompleteStatus(section);
              }
            );
          })
          .catch(error => {
            this.setState({
              completedCardStatus: null,
              isInProgressToComplete: false
            });
            console.error(`Error in JourneyConsumptionContainer.bulkCompleteCards.func : ${error}`);
          });
      });
    }
  };

  commonLeapLoop = (section, ind, nextCardId, newIndex, sectionInd) => {
    let eachCard = section.cards[ind];
    if (
      !(
        eachCard.completionState && eachCard.completionState.toUpperCase() === CARD_STATUS.COMPLETED
      )
    ) {
      this.changeCompleteStatus(nextCardId, newIndex, sectionInd);
    }
  };

  arrowClick = async (direction, event, isBackBtnCliked = false) => {
    event?.preventDefault();

    if (this.state.loaderWithDirectionForPollAndQuizCard !== false) {
      this.setState({
        loaderWithDirectionForPollAndQuizCard: direction,
        isInProgressToComplete: true
      });
      return;
    }
    this.setStartedReview();
    await this.checkToCompletedCard();
    let journeyDetails =
      this.props.journey &&
      this.props.journey.get('consumptionJourney') &&
      this.props.journey.get('consumptionJourney').journeySection;
    let section = this.state.currentSection;
    let index = this.state.currentIndex;
    let card = journeyDetails[section]?.cards[index];
    let cardType = getCardType(card);
    let oldIndex = this.state.currentIndex;
    const leapCardIds = fetchLeapCardIds(journeyDetails[section]);
    if (
      cardType === 'QUIZ' &&
      (card.hasAttempted || card.quiz?.hasAttempted) &&
      leapCardIds.length &&
      leapCardIds.includes(Number(card.id)) &&
      direction === 1
    ) {
      let result = card.quiz.passed;
      let inPathwayLeap = journeyDetails[section].leaps.inPathways.find(
        item => item.pathwayId == journeyDetails[section].id && card.id == item.cardId
      );
      let nextCardId = result ? inPathwayLeap.correctId : inPathwayLeap.wrongId;
      if (nextCardId) {
        let newIndex = journeyDetails[section].cards.indexOf(
          journeyDetails[section].cards.find(item => item.id == nextCardId)
        );

        // If leap functionality is added then all cards in-between should be marked as complete
        if (newIndex > oldIndex) {
          this.bulkCompleteCards(journeyDetails[section], oldIndex, newIndex, 'forward');
          for (let ind = oldIndex + 1; ind < newIndex; ind++) {
            this.commonLeapLoop(journeyDetails[section], ind, nextCardId, newIndex, section);
          }
        } else {
          // Reverse the loop for completing earlier cards
          this.bulkCompleteCards(journeyDetails[section], oldIndex, newIndex, 'reverse');
          for (let ind = oldIndex - 1; ind > newIndex; ind--) {
            this.commonLeapLoop(journeyDetails[section], ind, nextCardId, newIndex, section);
          }
        }

        this.setState(
          {
            fetchCard: false,
            isInProgressToComplete: false
          },
          () => {
            this.setState(
              {
                currentIndex: newIndex,
                checkedCard: journeyDetails[section].cards[newIndex],
                fetchCard: true
              },
              () => {
                const cardId = getSmartCardId(journeyDetails[section].cards[newIndex]);
                this.props.navigate(`/journey/${this.state.slug}/cards/${cardId}`);
                this.callRecordVisitAPICall();
                this.focusBackButton();
              }
            );
          }
        );
      }
    } else if (isBackBtnCliked && direction === -1) {
      const { indexOfCard, currentSection } = findJourneySectionAndCardIndex(
        journeyDetails,
        this.state.currentSection,
        this.props.routeParams.cardId
      );

      this.setState(
        {
          fetchCard: false
        },
        () => {
          this.setState(
            {
              currentIndex: indexOfCard,
              currentSection,
              checkedCard: journeyDetails[currentSection].cards[indexOfCard],
              cardId: this.props.routeParams.cardId,
              fetchCard: true
            },
            () => {
              this.callRecordVisitAPICall();
              this.focusBackButton();
            }
          );
        }
      );
    } else {
      let newIndex = this.state.currentIndex + direction;
      if (newIndex >= 0 && newIndex <= journeyDetails[section].cards.length - 1) {
        let prevCard = journeyDetails[this.state.currentSection].cards[oldIndex];
        this.changeCompleteStatus(prevCard.id, oldIndex, section);
        this.setState(
          {
            fetchCard: false,
            isInProgressToComplete: false
          },
          () => {
            this.setState(
              {
                currentIndex: newIndex,
                checkedCard: journeyDetails[section].cards[newIndex],
                fetchCard: true
              },
              () => {
                const cardId = getSmartCardId(journeyDetails[section].cards[newIndex]);
                this.props.navigate(`/journey/${this.state.slug}/cards/${cardId}`);
                this.callRecordVisitAPICall();
                this.focusBackButton();
              }
            );
          }
        );
      } else if (section !== (0 || journeyDetails.length)) {
        // Check if the journey type is progressive unlocking
        if (this.state.journey.cardSubtype === this.journeySubTypes[2]) {
          let stopNavigation = await this.arrowClickForProgressiveJourney(
            direction,
            this.state.currentSection
          );
          if (stopNavigation) {
            return;
          }
        }

        let nextSection = this.state.currentSection + direction;

        /* For private pathway its undefined,
           so increase or decrease nextSection value to navigate to next or previous section of journey */
        while (
          journeyDetails[nextSection] === undefined &&
          nextSection < journeyDetails.length &&
          nextSection >= 0
        ) {
          nextSection = nextSection + direction;
        }

        // To handle arrow click before loading all pathways
        if (!journeyDetails[nextSection] && !this.state.fetchedAllPathways) {
          const { journeySection } = this.state.journey;
          const isLastSection = journeySection?.length === nextSection;
          let tempCurrentSection = this.state.currentSection;
          let tempCurrentIndex = this.state.currentIndex;
          if (isLastSection) {
            nextSection = nextSection - 1;
          }
          const prevSectionData = journeySection[this.state.currentSection - 1];
          const nextSectionData = journeySection[nextSection];
          if (
            (nextSection === -1 && prevSectionData?.visible) ||
            (nextSection !== -1 && nextSectionData?.visible)
          ) {
            const currentSection = journeySection[this.state.currentSection];
            const isLastCard = currentSection?.cards?.length === currentSection?.pack_cards?.length;
            if (nextSection !== -1 && currentSection.cards?.length < currentSection.totalCount) {
              tempCurrentIndex = currentSection.cards?.length;
            } else {
              if (isLastCard && isLastSection) {
                this.backToJourney(
                  this.props.journey && this.props.journey.get('consumptionJourneyHistory')
                );
                return;
              }
              tempCurrentSection = nextSection === -1 ? this.state.currentSection - 1 : nextSection;
              tempCurrentIndex = nextSection === -1 ? prevSectionData.pack_cards?.length - 1 : 0;
            }
          }
          this.setState(
            {
              loadChecked: true,
              currentSection: tempCurrentSection,
              currentIndex: tempCurrentIndex
            },
            () => {
              this.focusBackButton();
            }
          );
          return;
        }

        // To handle next section card not available
        if (journeyDetails[nextSection] && !journeyDetails[nextSection]?.cards?.length) {
          this.props.dispatch(
            openSnackBar(translatr('web.common.main', 'SomethingWentWrongPleaseTryAgain'), 'error')
          );
          return;
        }

        let nextIndex = direction === -1 ? journeyDetails[nextSection].cards.length - 1 : 0;
        let nextCard = journeyDetails[nextSection].cards[nextIndex];
        this.changeCompleteStatus(nextCard.id, nextIndex, nextSection);
        this.setState(
          {
            fetchCard: false,
            isInProgressToComplete: false
          },
          () => {
            this.setState(
              {
                currentIndex: nextIndex,
                currentSection: nextSection,
                checkedCard: journeyDetails[nextSection].cards[nextIndex],
                fetchCard: true
              },
              () => {
                const cardId = getSmartCardId(journeyDetails[nextSection].cards[nextIndex]);
                this.props.navigate(`/journey/${this.state.slug}/cards/${cardId}`);
                this.callRecordVisitAPICall();
                this.focusBackButton();
              }
            );
          }
        );
      }
    }
  };

  backToJourney = isJourneyConsumption => {
    if (window.history.length > 2 && isJourneyConsumption) {
      this.props.navigate(`/journey/${isJourneyConsumption.slug}`);
    } else {
      this.props.navigate(`/journey/${this.state.slug}`);
    }
  };

  setStartedReview = () => {
    if (this.state.journey.completionState === null && !this.state.journeyStarted) {
      startAssignment(this.state.journey.id)
        .then(userContentCompletion => {
          if (userContentCompletion && userContentCompletion.state === 'started') {
            this.setState({
              journeyStarted: true
            });
          }
        })
        .catch(err => {
          console.error(`Error in JourneyConsumptionContainer.setStartedReview.func : ${err}`);
        });
    }
  };

  leapWithLockFunctionalityArrayProcessing = () => {
    let leapWithLockFunctionalityArray = [];
    let journeyDetails =
      this.props.journey &&
      this.props.journey.get('consumptionJourney') &&
      this.props.journey.get('consumptionJourney').journeySection;
    let journeySection = journeyDetails || this.state.journeySmartBites;
    let pathway = journeySection[this.state.currentSection];
    if (pathway?.leaps?.inPathways) {
      pathway.leaps.inPathways.forEach(item1 => {
        pathway.cards.forEach(item2 => {
          if (item1.cardId == item2.id && item2.attemptedOption) {
            let lockCardId = '';
            if (item2.attemptedOption.isCorrect || item2.attemptedOption.is_correct) {
              lockCardId = item1.correctId;
            } else {
              lockCardId = item1.wrongId;
            }
            pathway.cards.forEach(item3 => {
              if (item3.id && item3.id == lockCardId && item3.isLocked) {
                let currentCardObject = {
                  pathwayIndex: this.state.currentSection,
                  cardId: item3.id
                };
                leapWithLockFunctionalityArray.push(currentCardObject);
              }
            });
          }
        });
      });
    }
    let smartBitesBeforeCurrent =
      pathway?.cards?.length && pathway.cards.slice(0, this.state.currentIndex);
    let currentCardId = pathway.cards[this.state.currentIndex].id;
    let currentCardObject = { pathwayIndex: this.state.currentSection, cardId: currentCardId };

    const isCurrentCardCompleted = isCardCompleted(
      pathway.cards[this.state.currentIndex]?.completionState
    );

    return (
      this.state.currentIndex === 0 ||
      isCurrentCardCompleted || // Always show content for completed cards
      (smartBitesBeforeCurrent?.length &&
        smartBitesBeforeCurrent.every(
          item =>
            (item.completionState &&
              item.completionState.toUpperCase() === CARD_STATUS.COMPLETED) ||
            item.attemptedOption
        )) ||
      some(leapWithLockFunctionalityArray, currentCardObject)
    );
  };

  markAsComplete = (currentCard, resolve, journeyId = null) => {
    try {
      // Make sure the completedCardStatus state is cleared before marking the card as complete.
      this.setState({ isInProgressToComplete: true, completedCardStatus: null }, async () => {
        let markAsCompletePayload = {};
        let completedCardStatus;
        if (journeyId) {
          markAsCompletePayload = getMarkAsCompleteV2Payload(journeyId, 'journey');
        }
        markAsCompletePayload['state'] = 'complete';
        // For those cards which can not be marked as completed in LXP we need to fetch status of the card in LXP
        if (currentCard.markFeatureDisabledForSource) {
          const fetchCardCompletionPayload = {
            fields: 'id,completed_at,completion_state',
            'card_ids[]': currentCard.id
          };
          completedCardStatus = await fetchCardCompletionStatus(fetchCardCompletionPayload);
        } else {
          completedCardStatus = await markAsComplete(currentCard.id, markAsCompletePayload);
        }

        this.setState(
          {
            completedCardStatus: currentCard.markFeatureDisabledForSource
              ? completedCardStatus?.cards[0]
              : completedCardStatus,
            isInProgressToComplete: false
          },
          () => resolve && resolve(true)
        );
      });
    } catch (error) {
      this.setState({
        completedCardStatus: null,
        isInProgressToComplete: false
      });
      console.error(`Error in JourneyConsumptionContainer.checkToCompletedCard.func : ${error}`);
      if (resolve) resolve(true);
    }
  };

  async checkToCompletedCard() {
    return new Promise(resolve => {
      let journeyDetails =
        this.props.journey &&
        this.props.journey.get('consumptionJourney') &&
        this.props.journey.get('consumptionJourney').journeySection;
      let checkedCardId = this.state.checkedCard && this.state.checkedCard.id;

      let currentCard = {};
      loopJourneySection: for (let i = 0; i < journeyDetails.length; i++) {
        // For private pathway it is undefined
        if (journeyDetails[i]) {
          for (let j = 0; j < journeyDetails[i].cards.length; j++) {
            if (journeyDetails[i].cards[j].id == checkedCardId) {
              currentCard = journeyDetails[i].cards[j];
              break loopJourneySection;
            }
          }
        }
      }

      let isCompleted =
        (currentCard?.completionState &&
          currentCard.completionState.toUpperCase() === CARD_STATUS.COMPLETED) ||
        (currentCard?.completionState &&
          currentCard.completionState.toUpperCase() === CARD_STATUS.INITIALIZED);
      let cardType = getCardType(currentCard);
      let isNotLockedCard = currentCard && !currentCard.isLocked;
      if (!isCompleted && this.state.autoComplete && currentCard && !isNotLockedCard) {
        isNotLockedCard = this.leapWithLockFunctionalityArrayProcessing();
      }
      let isLinkCard =
        !this.state.showMarkAsComplete &&
        currentCard &&
        currentCard.resource &&
        (currentCard.resource.url ||
          currentCard.resource.description ||
          currentCard.resource.fileUrl) &&
        currentCard.resource.type !== 'Video' &&
        ((currentCard.readableCardType &&
          currentCard.readableCardType.toUpperCase() === 'ARTICLE') ||
          cardType === 'ARTICLE');

      const hideMarkAsCompleteForVILTCard = hideMarkAsCompleteForVILTCardFunction(
        cardType === 'VILT',
        currentCard
      );
      const isOwner = currentCard?.author && currentCard.author.id == this.props.currentUser.id;
      const disabledMarkAsCompleteForProjectCard =
        cardType === 'PROJECT' ? markAsCompleteFunctionalityForProjectCard(isOwner) : false;
      if (
        (!isCompleted &&
          this.state.autoComplete &&
          currentCard &&
          !['poll', 'quiz'].includes(currentCard.cardType) &&
          isNotLockedCard &&
          !currentCard.isPrivate &&
          !isLinkCard &&
          !hideMarkAsCompleteForVILTCard &&
          !disabledMarkAsCompleteForProjectCard) ||
        (currentCard.markFeatureDisabledForSource && !isCompleted)
      ) {
        this.markAsComplete(currentCard, resolve, this.state?.journey?.id);
      } else {
        // Make sure the completedCardStatus state is cleared
        this.setState({ completedCardStatus: null }, async () => resolve(true));
      }
    });
  }

  bulkChangeCompleteStatus = section => {
    let journey = this.props.journey && this.props.journey.get('consumptionJourney');

    if (this.state.completedCardStatus && Array.isArray(this.state.completedCardStatus)) {
      let sectionInd = journey.journeySection.findIndex(
        eachSection => eachSection.id == section.id
      );

      this.state.completedCardStatus.forEach(completedCard => {
        let index = journey.journeySection[sectionInd].cards.findIndex(
          card => card.id == completedCard.completableId
        );
        journey.journeySection[sectionInd].cards[index].isCompleted = true;
        journey.journeySection[sectionInd].cards[index].completionState = CARD_STATUS.COMPLETED;
      });
    }
  };

  changeCompleteStatus = (id, i, section) => {
    let journey = this.props?.journey?.get('consumptionJourney');
    let isNotLockedCard = !this.state.checkedCard.isLocked;
    if (!isNotLockedCard) {
      isNotLockedCard = this.leapWithLockFunctionalityArrayProcessing();
    }
    const isCompletionStateMatching =
      this.state.completedCardStatus?.completionState === CARD_STATUS.COMPLETED;

    if (
      this.state.completedCardStatus &&
      (this.state.completedCardStatus.completableId == id || isCompletionStateMatching) &&
      isNotLockedCard
    ) {
      journey.journeySection[section].cards[i].isCompleted = true;
      journey.journeySection[section].cards[i].completionState = 'COMPLETED';
    }
    journey.autoComplete = this.state.autoComplete;
    this.props.dispatch(saveConsumptionJourney(journey));
  };

  handleLoaderForPollAndQuizCard = () => {
    this.setState({
      loaderWithDirectionForPollAndQuizCard: true
    });
  };

  closeLoaderForPollAndQuizCard = () => {
    const { loaderWithDirectionForPollAndQuizCard, isInProgressToComplete } = this.state;
    this.setState(
      {
        loaderWithDirectionForPollAndQuizCard: false,
        isInProgressToComplete: false
      },
      () => {
        if (isInProgressToComplete) {
          this.arrowClick(loaderWithDirectionForPollAndQuizCard);
        }
      }
    );
  };

  render() {
    if (this.state.unavailable) {
      return <ContentUnAvailable />;
    } else if (this.state.loadChecked || this.state.isInProgressToComplete) {
      return (
        <div className="text-center centered-spinner">
          <Spinner />
        </div>
      );
    }
    let journey = this.state.journey;
    journey.autoComplete = this.state.autoComplete;
    this.props.dispatch(saveConsumptionJourney(journey));
    let journeyDetails =
      this.props.journey &&
      this.props.journey.get('consumptionJourney') &&
      this.props.journey.get('consumptionJourney').journeySection;
    let journeySection = journeyDetails || this.state.journeySmartBites;
    let pathway = journeySection[this.state.currentSection];
    let leapWithLockFunctionalityArray = [];
    let smartBitesBeforeCurrent =
      pathway?.cards?.length && pathway.cards.slice(0, this.state.currentIndex);
    let currentCardId = pathway.cards[this.state.currentIndex].id;
    let isLockedCard = false;
    if (pathway.cards[this.state.currentIndex].isLocked) {
      if (pathway?.leaps?.inPathways) {
        pathway.leaps.inPathways.forEach(item1 => {
          pathway.cards.forEach(item2 => {
            if (item1.cardId == item2.id && (item2.attemptedOption || item2.quiz?.hasAttempted)) {
              let lockCardId = item2.quiz?.passed ? item1.correctId : item1.wrongId;
              pathway.cards.forEach(item3 => {
                if (item3.id && item3.id == lockCardId && item3.isLocked) {
                  let currentCard = {
                    pathwayIndex: this.state.currentSection,
                    cardId: item3.id
                  };
                  leapWithLockFunctionalityArray.push(currentCard);
                }
              });
            }
          });
        });
      }
      let currentCardObject = { pathwayIndex: this.state.currentSection, cardId: currentCardId };
      isLockedCard =
        currentCardId !== undefined && some(leapWithLockFunctionalityArray, currentCardObject);
    }

    const isCurrentCardCompleted = isCardCompleted(
      pathway.cards[this.state.currentIndex]?.completionState
    );

    let isShowLockedCardContent =
      this.state.currentIndex === 0 ||
      isCurrentCardCompleted || // Always show content for completed cards
      (smartBitesBeforeCurrent?.length &&
        smartBitesBeforeCurrent.every(
          item =>
            item.isOptional ||
            (item.completionState &&
              item.completionState.toUpperCase() === CARD_STATUS.COMPLETED) ||
            !!item.attemptedOption
        )) ||
      isLockedCard;

    let showDisprzUrl = false;
    let getDisprzUrl =
      this.state.checkedCard &&
      this.state.checkedCard.resource &&
      this.state.checkedCard.resource.url &&
      this.state.checkedCard.resource.url.indexOf('/api/v2/ilt/launch');
    if (getDisprzUrl && getDisprzUrl !== -1) {
      showDisprzUrl = true;
    }
    const nextCardAvailable =
      this.state.currentIndex + 1 !== pathway?.totalCount ||
      this.state.currentSection + 1 !== journeySection.length ||
      !this.state.fetchedAllPathways;
    const enablePrevArrow = this.state.currentIndex !== 0 || this.state.currentSection !== 0;
    return (
      <div className="stand-alone">
        <div
          id="consumption-container-journey"
          className="ed-ui card-std-consumption-container justflex position-relative"
        >
          <Backarrow
            label={translatr('web.common.main', 'Back')}
            useOwnLogic
            cb={() => handleBackBtnClick({ navigate: this.props.navigate })}
          />
          <ConsumptionHeader
            card={journey}
            currentUserId={this.props.currentUserId}
            orgConfigs={this.props.orgConfigs}
            details={pathway ? unescape(pathway.block_title || pathway.block_message) : ''}
            slug={this.state.slug}
            consumptionHistory={
              this.props.journey && this.props.journey.get('consumptionJourneyHistory')
            }
          />
          <CardNavigator
            navigationClickHandler={this.arrowClick}
            disablePrevArrow={!enablePrevArrow}
            disableNextArrow={!nextCardAvailable}
            cardCount={pathway?.totalCount}
            currentCard={this.state.currentIndex + 1}
            leftPreviousBtnRef={this.leftPreviousBtnRef}
            rightNextBtnRef={this.rightNextBtnRef}
          />
          <div className="flex pathway-consumption-main-container">
            <div className="width-100">
              {this.state.fetchCard && (
                <Translatr
                  apps={[
                    'web.smartcard.standalone',
                    LD.isSubscriptionFeatureEnabled() ? 'web.subscriptionpage.main' : ''
                  ]}
                >
                  <CardConsumptionContainer
                    isPartOfPathway={true}
                    card={this.state.checkedCard}
                    author={this.state.checkedCard && this.state.checkedCard.author}
                    journeyData={journey}
                    showComment={
                      Permissions['enabled'] !== undefined && Permissions.has('CREATE_COMMENT')
                    }
                    currentSection={this.state.currentSection}
                    isShowLockedCardContent={isShowLockedCardContent}
                    showDisprzUrl={showDisprzUrl}
                    isJourney={true}
                    autoComplete={this.state.autoComplete}
                    handleLoaderForPollAndQuizCard={this.handleLoaderForPollAndQuizCard}
                    closeLoaderForPollAndQuizCard={this.closeLoaderForPollAndQuizCard}
                    backBtnTabIndex={-1}
                  />
                </Translatr>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

JourneyConsumptionContainer.propTypes = {
  journey: PropTypes.object,
  routeParams: PropTypes.object,
  team: PropTypes.object,
  currentUser: PropTypes.object,
  edcastPricing: PropTypes.bool,
  countryCode: PropTypes.string,
  wallet: PropTypes.bool,
  walletBalance: PropTypes.number,
  paymentGateways: PropTypes.object,
  pathwayIndex: PropTypes.number,
  currentUserId: PropTypes.number,
  orgConfigs: PropTypes.object,
  navigate: PropTypes.func,
  navigationType: PropTypes.string,
  location: PropTypes.object
};

function mapStoreStateToProps(state) {
  const currentUser = state.currentUser.toJS();
  const countryCode = currentUser.geoData?.country_code || 'US';
  const walletBalance = currentUser.walletBalance;
  const edcastPricing =
    state.team.get('config') && state.team.get('config').enable_smart_card_price_field;
  const wallet = state.team.get('config') && state.team.get('config').wallet;
  const paymentGateways = state.team.get('config') && state.team.get('config').payment_gateway;
  const pathwayIndex = state.journey?.get('pathwayIndex');
  return {
    journey: state.journey ? state.journey : {},
    team: state.team,
    edcastPricing,
    currentUser,
    countryCode,
    wallet,
    walletBalance,
    paymentGateways,
    pathwayIndex,
    orgConfigs: state.team.get('config'),
    currentUserId: +currentUser.id
  };
}

export default withRouter(connect(mapStoreStateToProps)(JourneyConsumptionContainer));
