@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.theme-plare {
  .no-bg-disable-btn {
    &.picasso-mark-as-complete-button {
      &.ed-btn:disabled {
        background-color: var(--ed-white);
      }
    }
  }
}

.container-padding-insight-v2 {
  &.ed-ui {
    padding: var(--ed-spacing-base) 2rem 2rem;
  }
}

.quiz-failed-warning {
  margin-left: auto;
  margin-right: auto;
  max-width: none !important;
  width: fit-content;
}

.card-std-outline {
  border: 0;
  background: var(--ed-white);
  border-radius: var(--ed-border-radius-lg);
}
.card-std-list.ed-ui {
  display: inline-block;
  padding: var(--ed-spacing-2xs);
  margin-bottom: var(--ed-spacing-base);
  position: relative;
  box-shadow: var(--ed-shadow-base);
  transition: box-shadow 0.5s ease;
  @extend .card-std-outline;

  &:hover {
    box-shadow: var(--ed-shadow-hover);
  }
}

.card-std-tile.ed-ui {
  width: rem-calc(276);
  display: inline-block;
  padding: var(--ed-spacing-2xs);
  margin: 0 0.5rem 1rem;
  position: relative;
  box-shadow: var(--ed-card-shadow-base);
  border: var(--ed-card-border-size) solid var(--ed-card-border-color);
  transition: box-shadow 0.5s ease;
  background-color: var(--ed-card-bg-color);
  @extend .card-std-outline;

  &:hover {
    box-shadow: var(--ed-card-shadow-hover);
  }
  .multiple-smartcard-layer-container {
    margin-top: rem-calc(5);
  }
  .multiple-smartcard-layer-1 {
    height: rem-calc(6);
    width: rem-calc(218);
  }
  .multiple-smartcard-layer-2 {
    height: rem-calc(6);
    width: rem-calc(228);
  }

  .number-card-btn {
    display: none;
  }

  .pathway-card-btn {
    min-width: rem-calc(20);
    height: rem-calc(20);
    position: absolute;
    top: 0;
    z-index: 12;
    opacity: 0.9;
  }

  .close-card-btn {
    right: 0;
  }

  .move-card-btn {
    right: 1.9rem;
  }

  .lock-leap-panel {
    opacity: 0.9;
    border-radius: rem-calc(2);
    position: absolute;
    width: 100%;
    height: rem-calc(47);
    background: var(--ed-black);
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 12;
    cursor: default;

    &.disable-edit {
      z-index: 10;
    }

    .lock-btn {
      span {
        padding-left: var(--ed-spacing-xs) !important;
        padding-right: var(--ed-spacing-xs) !important;
        letter-spacing: -0.3px !important;
        color: var(--ed-white);
        font-size: var(--ed-font-size-xs) !important;
        font-weight: var(--ed-font-weight-semibold) !important;
      }
    }
    .leap-btn {
      span {
        padding-left: var(--ed-spacing-xs) !important;
        letter-spacing: -0.3px !important;
        color: var(--ed-white);
        font-size: var(--ed-font-size-xs) !important;
        font-weight: var(--ed-font-weight-semibold) !important;
      }
    }
    span {
      color: var(--ed-white);
    }
  }

  .card-std-locked-card {
    width: 100%;
    left: 0;
    top: 0;
    height: 100%;
    padding: var(--ed-spacing-sm);
    border-radius: var(--ed-border-radius-lg);
  }

  // Override for tile cards
  .progress-container {
    margin-bottom: var(--ed-spacing-5xs);
    flex-direction: row-reverse;
  }
}

.card-std-bigcard.ed-ui {
  width: rem-calc(590);
  margin-bottom: var(--ed-spacing-base);
  padding: var(--ed-spacing-base);
  box-shadow: var(--ed-shadow-base);
  transition: box-shadow 0.5s ease;
  border-radius: var(--ed-border-radius-lg);
  @extend .card-std-outline;

  &:hover {
    box-shadow: var(--ed-shadow-hover);
  }
  .multiple-smartcard-layer-container {
    margin-top: var(--ed-spacing-2xs);
  }
  .multiple-smartcard-layer-1 {
    height: rem-calc(8);
    width: 96%;
  }
  .multiple-smartcard-layer-2 {
    height: rem-calc(7);
    width: 98%;
  }
  .card-std-locked-card {
    width: 34.25rem;
  }
}

.card-std-unauthorized-bigcard {
  height: rem-calc(440);
  .card-std-content-bigcard {
    display: none;
  }
  .card-std-locked-card {
    height: rem-calc(380) !important;
  }
}

.card-std-standalone-wrapper {
  width: rem-calc(1200);
  margin-left: -7.5rem;
  word-break: break-word;
  -ms-word-wrap: break-word;

  @media (max-width: 557px) {
    display: block !important;
  }
}

.card-std-standalone {
  width: rem-calc(1200);
  &.subscriptions-included {
    width: calc(100% - 19.188rem);
  }
  @extend .card-std-outline;
  .std-padding {
    padding: var(--ed-spacing-base);

    .gen-ai-info-section {
      font-size: var(--ed-font-size-base);
      border-top: var(--ed-border-size-sm) solid var(--ed-border-color);
      padding-bottom: var(--ed-spacing-base);

      .gen-ai-info-title {
        font-weight: var(--ed-font-weight-bold);
        padding: var(--ed-spacing-base) 0;
      }

      .gen-ai-insights {
        list-style-type: disc;
        overflow: initial;

        li {
          margin-left: var(--ed-spacing-2xs);
        }
      }
    }
  }
}

.card-std-standalone-pricing-wrapper,
.card-std-standalone-pricing-container {
  width: rem-calc(893);
  @media (max-width: $breakpoint-lg) {
    width: 100%;
  }
}

.ed-ui.card-std-consumption-container {
  margin: auto;
  max-width: rem-calc(1200);
  flex-flow: row wrap;
  margin-top: var(--ed-spacing-2xs);
  @include max-screen-width($breakpoint-xs) {
    > div,
    .card-std-standalone,
    .picasso-comment-list-container {
      border-radius: 0;
    }
    .breadcrumb-wrapper {
      margin-left: rem-calc(18);
    }
  }
  @media (max-width: $breakpoint-lg) {
    padding: 0 var(--ed-spacing-base) rem-calc(16);
  }
  @media (max-width: $breakpoint-xs) {
    padding: 0;
  }
  .card-std-standalone-wrapper {
    margin-left: 0;
    width: 100%;
    .card-std-standalone {
      width: 100%;
    }
  }

  .card-std-standalone-pricing-container {
    width: rem-calc(746);
  }
}

.mkp-events-pathway-journey {
  margin-left: 0.625rem;
}

.mkp-events-insight-standalone {
  margin-left: -7.5rem;
}

.proctor-events-pathway-journey {
  margin-left: 0.625rem;
}

.proctor-events-insight-standalone {
  margin-left: -7.5rem;
}

.ed-ui,
#portal {
  .after-separator {
    &:not(:last-child)::after {
      color: var(--ed-state-disabled-color);
      font-weight: var(--ed-font-weight-normal);
      content: '|';
      margin: 0 rem-calc(4);
      // Remove below css after journey/pathway page implementation
      background: transparent;
      width: auto;
      height: auto;
      border-radius: unset;
    }
  }
}
.after-separator-circular {
  &:not(:last-child)::after {
    border: var(--ed-border-size-sm) solid var(--ed-text-color-supporting);
    width: rem-calc(8);
    height: rem-calc(8);
    margin: rem-calc(2) rem-calc(13);
    display: inline-block;
    border-radius: var(--ed-border-radius-circle);
    content: ' ';
  }
}

.card-std-standalone-back {
  margin-right: rem-calc(11);
  &.subscriptions-included {
    column-gap: var(--ed-spacing-base);
    margin-right: 0;
    .breadcrumb-wrapper {
      width: calc(100% - 19.188rem);
    }
  }
}

.simple-v3 {
  &.feed-BigCard {
    width: rem-calc(590);
  }
}

.slick-slide,
.channel-card-v2,
.channel-card-wrapper-inner {
  .card-std-tile {
    margin: 0;
  }
  .avatar-box {
    z-index: 3;
  }
}

.card-std-private-card-standalone {
  height: rem-calc(620);
  .locked-card {
    @extend .card-std-outline;
    top: rem-calc(203);
    .locked-card-text {
      font-size: var(--ed-font-size-supporting);
      color: var(--ed-text-color-primary);
      font-weight: var(--ed-font-weight-semibold);
      position: relative;
      left: 0;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }
  }
}

.card-std-blur-background {
  background: rgba(255, 255, 255, 0.7) no-repeat;
  z-index: 11;
}
.card-std-locked-card-standalone-for-owner {
  height: auto;
  .locked-card {
    @extend .card-std-blur-background;

    &.unauthorised-card {
      background: var(--ed-white);
    }
  }
}

.common-tooltip-setting {
  margin-left: inherit !important;
}

.card-std-locked-card {
  @extend .card-std-blur-background;
}

.card-std-unauthorized-card-for-other-users {
  background: var(--ed-white);
}

.card-blur-locked-content {
  -webkit-filter: blur(0.2rem);
  -moz-filter: blur(0.2rem);
  -ms-filter: blur(0.2rem);
  -o-filter: blur(0.2rem);
  filter: blur(0.2rem);
}

.card-std-locked-card {
  .icon-lock {
    font-size: rem-calc(21);
  }

  .unauthorized-card-text {
    font-size: var(--ed-font-size-supporting);
    color: var(--ed-text-color-primary);
    font-weight: var(--ed-font-weight-semibold);
  }

  .card-lock-text-for-others {
    position: absolute;
    bottom: 2rem;
    left: 0;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    text-align: left;
  }
}

.ed-ui.pathway-std-wrapper,
.ed-ui.journey-std-wrapper {
  @include max-screen-width($breakpoint-xs) {
    > div {
      border-radius: 0;
    }
    .breadcrumb-wrapper {
      margin-left: rem-calc(18);
    }
  }
  @include max-screen-width(575px) {
    .card-title-wrapper {
      &.m-padding {
        padding: var(--ed-spacing-2xs);
      }
    }
  }
  margin: var(--ed-spacing-2xs) auto;
  @include min-screen-width($breakpoint-sm) {
    max-width: rem-calc(736);
  }
  @include min-screen-width($breakpoint-md) {
    max-width: rem-calc(960);
  }
  @include min-screen-width($breakpoint-lg) {
    max-width: rem-calc(1168);
  }
  @include min-screen-width($breakpoint-xlg) {
    max-width: rem-calc(1200);
  }
  .custom-card-container {
    padding-bottom: 0;
  }
  .likes {
    padding-right: 0;
  }
  .comment-text {
    margin-left: rem-calc(5);
  }
}
.ed-ui.pathway-std-wrapper {
  .smartbites-block {
    margin: auto;
    @include min-screen-width($breakpoint-xxs) {
      width: rem-calc(292);
    }
    @include min-screen-width($breakpoint-xs) {
      width: rem-calc(568);
    }
    @include min-screen-width($breakpoint-md) {
      width: rem-calc(860);
    }
    @include min-screen-width($breakpoint-lg) {
      width: rem-calc(1152);
    }
  }
}

.card-std-pathway-section {
  margin-bottom: 0;
}
.card-std-four-card {
  .card-std-tile:nth-child(4n-7) {
    margin-left: 0;
  }
  .card-std-tile:nth-child(4n) {
    margin-right: 0;
  }
}

.card-std-pricing-container {
  &.subscriptions-included {
    position: absolute;
    right: 0;
  }
  .insights-pricing-info,
  .promotions-details-container {
    margin-left: var(--ed-spacing-base);
    @media (max-width: 557px) {
      margin-left: 0;
    }
  }
}

.card-std-pricing-container,
.card-std-consumption-container {
  .promotions-details-container {
    &.subscriptions-container {
      width: rem-calc(291);
    }
  }
}

.picasso-pathway-journey-cards-container {
  .pathway-card-index {
    display: block;
  }
}

@media (max-width: $breakpoint-lg) {
  .ed-ui.card-std-standalone-wrapper {
    width: 100%;
    margin-left: 0;
  }
  .card-std-four-card {
    .card-std-tile:nth-child(4n-7) {
      margin-left: var(--ed-spacing-2xs);
    }
    .card-std-tile:nth-child(4n) {
      margin-right: var(--ed-spacing-2xs);
    }
  }
  .journey-load-more {
    margin: 0;
  }
  .mkp-events-insight-standalone {
    margin-left: 0;
  }
  .proctor-events-insight-standalone {
    margin-left: 0;
  }
}

.width-100 {
  width: 100%;
}

button.min-width-0 {
  min-width: 0;
}

.no-margin {
  margin: 0;
}

.no-border {
  border: none !important;
}

#channel-curation-feed,
.curate-container .card-std-metadata-standalone-wrapper.picasso-card-metadata-wrapper {
  padding: 0;
  border: none;
}

.card-std-create-card {
  height: rem-calc(396) !important;
}

.card-std-left-0 {
  left: 0;
}

.card-std-ckEditor-title .markdown-container .marked-text p {
  font-size: var(--ed-font-size-supporting);
  color: var(--ed-text-color-primary);
}

// Icons
.card-icon {
  font-size: var(--ed-font-size-lg);
  color: var(--ed-text-color-supporting);
  vertical-align: middle;
  &:hover,
  &.filled {
    color: var(--ed-primary-base);
  }
}

.ed-ui .standalone-metadata-wrapper {
  .external-metadata-wrapper {
    line-height: 2rem;
    font-size: var(--ed-font-size-base);
    color: var(--ed-text-color-primary);
    .card-metadata-icon {
      margin-right: var(--ed-spacing-2xs);
    }
    .multilang-card {
      .ed-input-container {
        display: inline-block;
      }
      .ed-select {
        font-size: var(--ed-font-size-sm);
        padding: 0 2em 0 0;
        border: none;
        border-radius: 0;
        height: auto;
      }
    }
  }
  .after-separator {
    &:not(:last-child)::after {
      margin: 0 0.5rem;
    }
  }
  .card-metadata-icon {
    color: var(--ed-text-color-supporting);
  }
}

.standalone-metadata-wrapper .picasso-mark-as-complete-button {
  .card-icon {
    color: inherit;
  }

  .icon-check-circle-light.completed {
    color: var(--ed-primary-base);
  }

  &.ed-btn-outline {
    border-color: transparent;
    &:hover {
      background-color: transparent;
      border-color: transparent;
    }
  }

  &.ed-btn-negative {
    text-decoration: underline;
  }

  &.new-design-completed-btn .ed-btn-v2 {
    color: var(--ed-primary-base);

    &:hover {
      .social-activity-icon-label {
        text-decoration: underline;
      }
    }
  }
}

//removed extra padding of tile view mark-as-complete button
.picasso-mark-as-complete-button .ed-btn-v2 {
  &.ed-btn-padding-xsmall {
    padding: 0 !important;
  }
}

.common-menu-styles {
  width: rem-calc(220);
  border-radius: var(--ed-border-radius-lg);
  box-shadow: var(--ed-shadow-base);

  .border {
    border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
  }

  li {
    font-family: 'Open Sans', sans-serif;
    font-size: var(--ed-font-size-base);
    line-height: 1.86;
    color: var(--ed-text-color-primary);
    min-height: 2rem;
    background-color: var(--ed-white);
    padding: 0 1rem;
    transition: background-color 0.5s ease;
    &:hover,
    &:focus {
      background-color: var(--ed-input-hover-bg-color);
    }
  }
}

.insight-dropdown-popper {
  z-index: 100; // 2100
  min-width: rem-calc(232);
  width: auto; // width will be as per the content width

  .dropdown-divider {
    background-color: var(--ed-state-disabled-color);
    margin: 0.3rem 1rem;

    &:last-child,
    &:first-child {
      display: none;
    }
  }
  // Styles for event toggle enable/disable for MKP courses in insights dropdown
  li.notify-event-toggle {
    .switch {
      span {
        transform: translateX(-2rem);
        margin-right: 0.5rem;
      }
    }
  }
}
.card-std-standalone-container {
  width: 100%;
}

.pathway-card-index {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 0.4rem;
  text-align: center;
  height: rem-calc(30);
  z-index: 12;
  background: var(--ed-text-color-primary);
  color: var(--ed-white);
  font-size: var(--ed-font-size-supporting);
  border-top-right-radius: 0.125rem;
  border-bottom-left-radius: var(--ed-border-radius-lg);
  display: none;
}

@media screen and (max-width: 884px) {
  .card-std-standalone-section {
    &.block-display {
      display: block;
    }
  }
}

.cursorDefault {
  cursor: default !important;
}

.stand-alone {
  .container-padding-insight-v2 {
    &.ed-ui {
      padding-top: 0;
    }
  }
}
