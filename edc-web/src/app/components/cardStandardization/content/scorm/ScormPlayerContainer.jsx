import React, { useEffect, useRef, useState, useCallback, useContext } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { connect } from 'react-redux';
import './styles.scss';
import {
  verifyLaunchToken,
  getScormContentInfo,
  callEventOnPreLaunch,
  getScormToc,
  getLaunchLinkForSpecificScormId,
  callEventOnLessonLaunch,
  callEventOnLessonExit,
  callEventOnPlayerClose
} from 'edc-web-sdk/requests/scorm';
import { open_v2 as openSnackBar } from '../../../../actions/snackBarActions';
import {
  getClickableOptions,
  getFormattedTime,
  getSequencer,
  getUpdatedTime,
  isContentScorm2004
} from './helpers';
import { NavigationCommand, NavigationState } from './constants';
import ScormExitOptions from './ScormExitOptions';
import DiscardAttemptMessage from './DiscardAttemptMessage';
import ScormContentContainer from './ScormContentContainer';
import ScormContentPlayer from './ScormContentPlayer';
import { useSearchParams } from 'react-router-dom';
import stripHTMLTags from '@utils/stripHTMLTags';
import CardContext from '../../context/CardContext';

const ScormPlayerContainer = ({
  contentId,
  onClose,
  toast,
  contentTitle,
  showScormModal = false
}) => {
  const [tocContent, setTocContent] = useState(null);
  const [contentInfo, setContentInfo] = useState(null);
  const [launchData, setLaunchData] = useState(null);
  const [currentToc, setCurrentToc] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openWindowRef, setOpenWindowRef] = useState(null);
  const showToc = contentInfo?.showTOC;
  const isMultiSco = contentInfo?.multiSco;
  let triggerCloseRef = useRef(null);
  let navigateHandler = null;
  const [isScorm2004, setIsScorm2004] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [isLMSInitializeInitiated, setIsLMSInitializeInitiated] = useState(false);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [showDiscardAttemptModal, setDiscardAttemptModal] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [searchParams] = useSearchParams();
  const regId = contentId || searchParams?.get('registrationId');
  const token = searchParams?.get('launchToken') || '';
  const title = contentTitle || stripHTMLTags(searchParams?.get('cardTitle')) || '';
  const [mobileAppExit, setMobileAppExit] = useState(false);
  const CardContextData = useContext(CardContext);
  const iframeRef = useRef(null);

  useEffect(() => {
    getInitialData();
  }, []);

  const getInitialData = async () => {
    if (token) {
      await verifyLaunchToken(token);
    }
    getScormContentInfo(regId)
      .then(async data => {
        setContentInfo(data);
        const scormType = isContentScorm2004(data);
        setIsScorm2004(scormType);
        await callEventOnPreLaunch(regId);
        handleContentNavigation(NavigationCommand.START, data, scormType);
      })
      .catch(error => {
        setIsLoading(false);
        if (error?.message) {
          toast(error?.message, 'error');
        }
        console.error('Error while fetching scorm content data', error);
      });
  };

  const getTocData = async id => {
    try {
      const data = await getScormToc(id);
      setTocContent(data);
      return data;
    } catch (error) {
      console.error('Error while fetching scorm toc data', error);
    }
  };

  const getNavigateHandler = data => {
    const [navigate] = getSequencer(regId, data);
    navigateHandler = navigate;
  };

  const onLessonClick = contentItem => {
    const request = '{target=' + contentItem.id + '}choice';
    getNextLesson(request);
  };

  const getNextLesson = command => {
    setIsLoading(true);
    handleContentNavigation(command);
  };

  const handleContentNavigation = async (request, contentInfoDetails = null, scorm2004 = false) => {
    try {
      const tocData = await getTocData(regId);
      const scormType = scorm2004 || isScorm2004;
      getNavigateHandler(contentInfo || contentInfoDetails);
      const data = {
        contentInfo: contentInfo || contentInfoDetails,
        tocData,
        currentToc: currentToc
      };
      const displayToc = showToc || contentInfoDetails?.showTOC;
      const resolvedData = await navigateHandler(request, data);
      if (scorm2004 || isScorm2004) {
        if (resolvedData?.state) {
          const completedState =
            resolvedData.state === NavigationState.COMPLETED ||
            resolvedData.state === NavigationState.EXIT_SESSION;
          const okOnExitState =
            (request === NavigationCommand.EXIT_ALL || request === NavigationCommand.SUSPEND_ALL) &&
            resolvedData.state === NavigationState.OK;

          if (completedState || okOnExitState) {
            modalClose();
            return false;
          }

          if (resolvedData.state === NavigationState.SEQ_ERROR && !showDiscardAttemptModal) {
            if (showExitModal) {
              setShowExitModal(false);
            }
            setDiscardAttemptModal(true);
            return false;
          }
        }
      } else {
        if (!resolvedData) {
          modalClose();
          return false;
        }
      }
      if (resolvedData.state !== NavigationState.OK) {
        setIsLoading(false);
        setCurrentActivity('dummy');
        setLaunchData({
          launchMessage: displayToc
            ? translatr('web.common.main', 'PleaseSelectLessonFromTableOfContents')
            : translatr('web.common.main', 'PleaseCloseContentByChoosingExitOption')
        });
        return false;
      }
      postNavigationAction(resolvedData, tocData, scormType);
    } catch (error) {
      setIsLoading(false);
      console.error('Error while fetching next launch details', error);
    }
  };

  const postNavigationAction = (response, tocData, scorm2004) => {
    const options = getClickableOptions(tocData?.contentItem);
    let content = response;
    if (scorm2004 && !response.activityId && response.state === NavigationState.OK) {
      setLaunchData({
        launchMessage: showToc
          ? translatr('web.common.main', 'PleaseSelectLessonFromTableOfContents')
          : translatr('web.common.main', 'PleaseCloseContentByChoosingExitOption')
      });
      setCurrentToc(null);
      if (currentActivity) {
        onLessonExitEvent(currentActivity);
      }
      setCurrentActivity('dummy');
      setIsLoading(false);
      return false;
    }

    if (scorm2004 && response?.activityId) {
      content = options?.find(value => value.contentItem?.id === response?.activityId);
    }

    launchActivity(content?.contentItem || content);
  };

  const launchActivity = async contentItem => {
    try {
      const prevToc = currentToc;
      const launchLessonDetails = await getLaunchLinkForSpecificScormId(regId, contentItem?.id);
      setStartTime(null);
      setCurrentToc(contentItem);
      setCurrentActivity(contentItem?.id);
      setLaunchData(launchLessonDetails);
      setIsLoading(false);
      onLessonLaunchEvent(contentItem?.id);
      if (prevToc || currentActivity) {
        !isScorm2004 ? onLessonExitEvent(prevToc?.id) : onLessonExitEvent(currentActivity);
      }
    } catch (error) {
      setIsLoading(false);
      if (error?.message) {
        toast(error?.message, 'error');
      }
      console.error('Error while launching the lesson', error);
    }
  };

  const onLessonLaunchEvent = async id => {
    try {
      const initialTime = getUpdatedTime();
      setStartTime(initialTime);
      await callEventOnLessonLaunch(regId, id);
    } catch {
      console.error('Error while calling lesson launch event');
    }
  };

  const onLessonExitEvent = async id => {
    try {
      const stopTime = getUpdatedTime();
      await callEventOnLessonExit(regId, id, { timeSpent: getFormattedTime(startTime, stopTime) });
    } catch {
      console.error('Error while calling lesson exit event');
    }
  };

  const handleOnExpand = useCallback(() => {
    setIsFullScreen(!isFullScreen);
  }, [isFullScreen]);

  const modalClose = async () => {
    openWindowRef?.close();
    triggerCloseRef.current = true;
    try {
      await onLessonExitEvent(currentActivity);
      await callEventOnPlayerClose(regId);
    } catch {
      console.error('Error while calling the player exit events');
    }
    setShowExitModal(false);
    if (showScormModal) {
      await CardContextData?.onScormModalClose();
      onClose();
    } else {
      handleOnExpand();
      setIsLoading(false);
      setMobileAppExit(true);
    }
  };

  const getScormProps = React.useCallback(() => {
    return {
      contentInfo,
      openWindowRef,
      triggerCloseRef,
      currentToc,
      setWindowRefval: setOpenWindowRef,
      launchActivity,
      modalClose,
      getNextLesson,
      setIsLMSInitializeInitiated
    };
  }, [currentToc, contentInfo, openWindowRef]);

  const closeContent = () => {
    isScorm2004 ? scorm2004PlayerExitProcess() : modalClose();
  };

  useEffect(() => {
    if (showExitModal && iframeRef.current) {
      setCurrentToc(null);
      setLaunchData({
        launchMessage: showToc
          ? translatr('web.common.main', 'PleaseSelectLessonFromTableOfContents')
          : translatr('web.common.main', 'PleaseCloseContentByChoosingExitOption')
      });
    }
  }, [showExitModal]);

  const scorm2004PlayerExitProcess = () => {
    if (!isMultiSco) {
      contentInfo?.useExitAll ? handleContentNavigation(NavigationCommand.EXIT_ALL) : modalClose();
    } else {
      if (contentInfo?.showExitOptions) {
        setShowExitModal(true);
      } else {
        if (contentInfo?.relaxSCORM2004 && isLMSInitializeInitiated) {
          handleContentNavigation(NavigationCommand.EXIT_ALL);
        } else {
          handleContentNavigation(NavigationCommand.SUSPEND_ALL);
        }
      }
    }
  };

  const setCurrentContentRef = value => {
    iframeRef.current = value;
  };

  const getScormContent = () => {
    return (
      <ScormContentContainer
        tocContent={tocContent}
        onLessonClick={onLessonClick}
        currentToc={currentToc}
        isScorm2004={isScorm2004}
        launchData={launchData}
        getScormProps={getScormProps}
        showToc={showToc}
        isLoading={isLoading}
        handleOnExpand={handleOnExpand}
        isFullScreen={isFullScreen}
        title={title}
        setCurrentContentRef={setCurrentContentRef}
      />
    );
  };

  return (
    <>
      <ScormContentPlayer
        title={title}
        closeContent={closeContent}
        getScormContent={getScormContent}
        showScormModal={showScormModal}
        isFullScreen={isFullScreen}
        mobileAppExit={mobileAppExit}
      />
      {isScorm2004 && isMultiSco && (
        <ScormExitOptions
          isLMSInitializeInitiated={isLMSInitializeInitiated}
          contentInfo={contentInfo}
          showExitModal={showExitModal}
          closeExitModal={setShowExitModal}
          handleNavigate={handleContentNavigation}
        />
      )}
      {isScorm2004 && (
        <DiscardAttemptMessage
          showDiscardAttemptModal={showDiscardAttemptModal}
          setDiscardAttemptModal={setDiscardAttemptModal}
          setErrorMessage={setLaunchData}
          regId={regId}
          handleNavigate={handleContentNavigation}
          setIsLoading={setIsLoading}
          modalClose={modalClose}
        />
      )}
    </>
  );
};

ScormPlayerContainer.propTypes = {
  contentId: PropTypes.string,
  onClose: PropTypes.func,
  toast: PropTypes.func,
  contentTitle: PropTypes.string,
  showScormModal: PropTypes.bool
};

export default connect(null, dispatch => ({
  toast: (message, type) => dispatch(openSnackBar(message, type))
}))(ScormPlayerContainer);
