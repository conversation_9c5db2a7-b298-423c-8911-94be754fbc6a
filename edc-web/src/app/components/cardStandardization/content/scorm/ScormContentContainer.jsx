import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import TableOfContents from 'centralized-design-system/src/TableOfContents';
import ScormContent from './ScormContent';
import Spinner from 'centralized-design-system/src/MUIComponents/common/Spinner';
import classNames from 'classnames';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { tooltipTextWrapper } from '@utils/utils';

const ScormContentContainer = ({
  tocContent,
  onLessonClick,
  currentToc,
  isScorm2004,
  launchData,
  getScormProps,
  showToc,
  isLoading,
  isFullScreen,
  handleOnExpand,
  title,
  setCurrentContentRef
}) => {
  const videoPlayerContentClassNames = classNames('video-player-main-cont');

  return (
    <>
      {!isFullScreen && showToc && (
        <TableOfContents
          tocOptions={tocContent}
          tocTitle={translatr('web.common.main', 'TableOfContents')}
          callBack={onLessonClick}
          selectedToc={currentToc}
          checkCompletionStatus={isScorm2004}
          as="h3"
          title={title}
        />
      )}
      {!isLoading ? (
        <div className={videoPlayerContentClassNames}>
          <ScormContent
            setCurrentContentRef={setCurrentContentRef}
            launchData={launchData}
            {...getScormProps()}
          />
          {showToc && (
            <Tooltip
              message={tooltipTextWrapper(
                !isFullScreen
                  ? translatr('web.common.main', 'FullScreenOrMaximize')
                  : translatr('web.common.main', 'MinimizeScreen')
              )}
              isHtmlIncluded
              ariaLabel={
                !isFullScreen
                  ? translatr('web.common.main', 'FullScreenOrMaximize')
                  : translatr('web.common.main', 'MinimizeScreen')
              }
            >
              <button
                onClick={handleOnExpand}
                className={`icon-expand iframe-expand`}
                aria-label={translatr('web.common.main', 'ManageScormContentSize')}
              ></button>
            </Tooltip>
          )}
        </div>
      ) : (
        <div className="loading-container">
          <Spinner />
        </div>
      )}
    </>
  );
};

ScormContentContainer.propTypes = {
  tocContent: PropTypes.object,
  onLessonClick: PropTypes.func,
  currentToc: PropTypes.object,
  isScorm2004: PropTypes.bool,
  launchData: PropTypes.object,
  getScormProps: PropTypes.func,
  showToc: PropTypes.bool,
  isLoading: PropTypes.bool,
  isFullScreen: PropTypes.bool,
  handleOnExpand: PropTypes.func,
  title: PropTypes.func,
  setCurrentContentRef: PropTypes.func
};

export default ScormContentContainer;
