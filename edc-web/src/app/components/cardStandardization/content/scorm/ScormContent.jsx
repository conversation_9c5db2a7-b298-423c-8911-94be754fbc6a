import React, { useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { NavigationCommand, NavigationEvent, contentFormats } from './constants';
import { compareNavigateRequest } from './helpers';

const ScormContent = ({
  launchData,
  openWindowRef,
  triggerCloseRef,
  contentInfo,
  setWindowRefval,
  getNextLesson,
  tocContent,
  currentToc,
  setIsLMSInitializeInitiated,
  setCurrentContentRef
}) => {
  const contentLaunchMessage = `
      ${translatr('web.common.main', 'LaunchScormContentMessage')}
  `;
  const selectedClasses = '.left-navigation--nav-item.selected';
  const iframeRef = useRef(null);
  let pollingInterval = null;

  useEffect(() => {
    const handleIframeLoad = () => {
      if (iframeRef.current) {
        setCurrentContentRef(iframeRef.current);
        iframeRef.current.focus();
        const iframeDocument =
          iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
        if (iframeDocument) {
          const iframeBody = iframeDocument.body;
          if (iframeBody) {
            iframeBody.setAttribute('tabindex', '-1');
            iframeBody.focus();
          }
        }
      }
    };

    iframeRef?.current?.addEventListener('load', handleIframeLoad);

    return () => {
      iframeRef?.current?.removeEventListener('load', handleIframeLoad);
    };
  }, []);

  // Handles the navigation for AICC inline and content opening in new window
  useEffect(() => {
    const selectedLesson = document.querySelector(selectedClasses);
    selectedLesson?.scrollIntoView({ behavior: 'smooth', block: 'end' });
    if (launchData?.launchURL) {
      if (launchData.launchMode === 'NEW_WINDOW') {
        openWindowRef?.close();
        triggerCloseRef.current = true;
        setWindowRefval(window.open(launchData.launchURL, '_blank', 'width=1200,height=700'));
      }

      if (
        [contentFormats.AICC, contentFormats.TINCAN].includes(
          contentInfo?.contentInventory?.contentFormat
        ) &&
        launchData.launchMode === 'INLINE'
      ) {
        openWindowRef?.close();
        if (iframeRef?.current?.contentWindow) {
          iframeRef.current.contentWindow.close = getNextLesson.bind(
            this,
            NavigationCommand.CONTINE
          );
        }
      }
    }
  }, [launchData]);

  // Handles the next launch activity when user closes the lesson launched in new window
  useEffect(() => {
    if (openWindowRef) {
      pollingInterval = setInterval(() => {
        if (openWindowRef?.closed) {
          if (!triggerCloseRef.current) {
            getNextLesson(NavigationCommand.CONTINE);
          }
          triggerCloseRef.current = false;
          clearInterval(pollingInterval);
        }
      }, 500);
    }

    return () => {
      triggerCloseRef.current = false;
      clearInterval(pollingInterval);
    };
  }, [openWindowRef]);

  const handleLaunchRequest = useCallback(
    event => {
      const command = event?.data?.request;
      const eventName = event?.data?.event;
      if (command?.toLowerCase().endsWith('jump') || compareNavigateRequest(command)) {
        getNextLesson(command);
      }
      if (eventName?.toLowerCase() === NavigationEvent.LMSINITIALIZEINITIATED.toLowerCase()) {
        setIsLMSInitializeInitiated(true);
      }
    },
    [tocContent, currentToc]
  );

  useEffect(() => {
    window.addEventListener('message', handleLaunchRequest);

    return () => window.removeEventListener('message', handleLaunchRequest);
  }, [handleLaunchRequest]);

  const getLaunchDetails = React.useCallback(() => {
    if (launchData && launchData?.launchMode === 'NEW_WINDOW') {
      return getLaunchMessage(contentLaunchMessage);
    }

    if (launchData?.launchMessage) {
      return getLaunchMessage(launchData?.launchMessage);
    }

    return launchData?.launchURL;
  }, [launchData]);

  const getLaunchMessage = message => {
    const text = `
    <div style='display: flex; justify-content: center; align-items: center; height: 100%; text-align: center;'>
      ${message}
    </div>`;
    return 'data:text/html,' + encodeURIComponent(text);
  };

  return (
    <iframe
      frameBorder="0"
      title={translatr('web.common.main', 'ScormContent')}
      className="video-player"
      ref={iframeRef}
      src={getLaunchDetails()}
      aria-label={translatr('web.common.main', 'ScormContent')}
      allowFullScreen
    />
  );
};

ScormContent.propTypes = {
  launchData: PropTypes.object,
  openWindowRef: PropTypes.object,
  triggerCloseRef: PropTypes.object,
  contentInfo: PropTypes.object,
  setWindowRefval: PropTypes.func,
  getNextLesson: PropTypes.func,
  tocContent: PropTypes.object,
  currentToc: PropTypes.object,
  setIsLMSInitializeInitiated: PropTypes.func,
  setCurrentContentRef: PropTypes.func
};

export default ScormContent;
