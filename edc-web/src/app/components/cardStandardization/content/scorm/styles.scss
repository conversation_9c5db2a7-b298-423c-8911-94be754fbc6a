@import '~centralized-design-system/src/Styles/_variables.scss';

.scorm-inline-container {
  margin: 4vh auto;
  max-width: 95%;

  .scorm-exit-app-container {
    display: flex;
    justify-content: center;
    background-color: var(--ed-white);
    min-height: 80vh;
    align-items: center;
    text-align: center;
    padding: 0.5rem;

    p {
      font-weight: var(--ed-font-weight-semibold) !important;
    }
  }

  &.scorm-full-screen-content {
    position: relative;
  }

  .scorm-header {
    display: flex;
    justify-content: space-between;
    .scorm-heading {
      margin-bottom: 0;
    }
    @media screen and (max-width: ($breakpoint-sm - 1)) {
      flex-direction: column;
      .scorm-heading {
        word-break: break-word;
        margin-bottom: var(--ed-spacing-2xs);
      }
      button {
        width: 40%;
      }
    }
  }

  .scorm-inline-content {
    display: flex;
    gap: var(--ed-spacing-base);
    margin-top: var(--ed-spacing-lg);
    min-height: 75vh;
    position: relative;

    .toc-wrapper .toc-navigation-wrapper {
      height: 100%;

      &.toc-nav-expand {
        background-color: var(--ed-white);
      }
    }
  }
}

.scorm-modal-wrapper,
.scorm-inline-container {
  &.scorm-full-screen-content {
    &.ed-dialog-modal .ed-dialog-modal-wrapper .content {
      .ed-dialog-modal-content {
        position: static;

        .toc-navigation-wrapper.toc-nav-expand {
          position: static;
        }
      }
    }

    .ed-dialog-modal-content,
    .scorm-inline-content {
      position: static;
      .toc-wrapper .toc-navigation-wrapper {
        position: static;
        height: inherit;
      }
    }
  }
  &.ed-dialog-modal {
    .ed-dialog-modal-wrapper {
      margin-bottom: 0;
      margin-top: 4vh;
      .content {
        margin-bottom: 4vh !important;
        max-width: 95%;

        & .ed-dialog-modal-header-close-button:hover,
        & .ed-dialog-modal-header-close-button:focus {
          color: var(--ed-modal-close-button-color);
        }

        .ed-dialog-modal-content {
          display: flex;
          gap: var(--ed-spacing-base);
          min-height: 80vh;
          position: relative;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .toc-wrapper {
    position: static;
    height: auto;

    .toc-navigation-wrapper {
      height: calc(100% - 2.5rem);
      margin-bottom: 0;
      position: absolute;

      &.toc-nav-expand {
        width: 18rem;
        .left-navigation {
          max-height: 80vh;
          overflow-y: auto;
        }
      }

      &.toc-nav-collapse {
        box-shadow: none;
      }
    }

    .toc-expand-wrapper.toc-expand-open {
      margin-left: 18rem;
    }

    @media screen and (max-width: ($breakpoint-sm - 1)) {
      .toc-navigation-wrapper {
        &.toc-nav-expand {
          height: 100%;
          top: 0;
          left: 0;
        }
      }

      .toc-expand-wrapper.toc-expand-open {
        margin-left: 0;
        left: 18rem;
      }
    }
  }

  .video-player-main-cont {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--ed-spacing-base);
    background-color: var(--ed-white);

    .ed-tooltip {
      width: min-content;
    }

    .video-player {
      height: 100%;
      width: 100%;
      text-align: center;
    }

    button {
      width: fit-content;
    }

    .iframe-expand {
      border: 1px dotted;
      border-radius: var(--ed-border-radius-circle);
      cursor: pointer;
      width: 1.87rem;
      height: 1.87rem;
      margin: 0 0 0 var(--ed-spacing-2xs);
      z-index: 2;

      &.mobile-exit-screen {
        display: none;
      }
    }

    @media screen and (max-width: ($breakpoint-sm - 1)) {
      .iframe-expand {
        z-index: 0;
      }
    }
  }
}

.toc-wrapper {
  position: static;
  height: auto;
  .toc-navigation-wrapper {
    position: absolute;
    height: calc(100% - 2.5rem);
    margin-bottom: 0;

    &.toc-nav-expand {
      @media screen and (max-width: ($breakpoint-sm - 1)) {
        height: 100%;
        top: 0;
        left: 0;
      }
      .left-navigation {
        max-height: 80vh;
        overflow-y: auto;
      }
    }

    &.toc-nav-collapse {
      box-shadow: none;
    }
  }
}

.content-exit-modal {
  .ed-btn-neutral {
    text-transform: uppercase;
  }
}
