@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui {
  .additional-metadata-project-card {
    .icon {
      color: var(--ed-text-color-supporting);
    }
    .link {
      color: var(--ed-text-color-primary) !important;
      cursor: pointer;
      &:hover {
        color: var(--ed-primary-base) !important;
        .link-text {
          color: var(--ed-primary-base);
          text-decoration: underline;
        }
        .icon {
          color: var(--ed-primary-base);
        }
      }
      .link-text {
        color: var(--ed-text-color-primary);
        max-width: $breakpoint-xs;
      }
    }
    .project-card-details {
      font-size: var(--ed-font-size-sm);
      color: var(--ed-text-color-supporting);
      margin-top: var(--ed-spacing-base);
      margin-bottom: var(--ed-spacing-base);
      .submission-count {
        color: var(--ed-text-color-primary);
        margin-left: rem-calc(5);
      }
      .after-separator {
        &:not(:last-child)::after {
          margin: rem-calc(1) var(--ed-spacing-xs);
        }
      }
      .active-details {
        text-decoration: underline !important;
      }
      .view-submissions-btn-wrapper {
        .view-submissions {
          width: rem-calc(193);
        }
        @media (max-width: 900px) {
          width: 100%;
          margin-top: rem-calc(18);
        }
      }
    }
    .project-card-metadata-attachment {
      font-size: var(--ed-font-size-base);
      .icon-download {
        margin: 0 var(--ed-spacing-2xs) 0 0;
      }
    }
    .project-card-metadata-submission {
      margin-bottom: var(--ed-spacing-base);
      .remarks-container {
        background: var(--ed-neutral-7);
        border-radius: rem-calc(10);

        .ed-tooltip {
          display: inline-block;
        }
      }
      .add-submission-btn {
        height: rem-calc(38);
      }
      .submissions-container {
        min-height: rem-calc(38);
        padding: var(--ed-spacing-2xs);
        font-size: var(--ed-font-size-base);
        margin-bottom: var(--ed-spacing-base);
        .projectfile-action-button {
          margin-left: var(--ed-spacing-xs);
          min-width: 0;
        }
        .url-wrapper {
          padding-right: var(--ed-spacing-base);
          .icon-link-light {
            font-size: var(--ed-font-size-lg);
            padding-left: 0;
            margin-left: 0;
            margin-right: var(--ed-spacing-3xs);
          }
        }
        .grade-container {
          justify-content: flex-end;
          color: var(--ed-text-color-supporting);
          font-size: rem-calc(18);
          .grade-wrapper {
            .grade-details {
              color: var(--ed-text-color-primary);
              font-weight: var(--ed-font-weight-semibold);
            }
          }
        }
      }
    }
    .close-container {
      margin-top: var(--ed-spacing-4xs);
    }
    .icon-x-mark-Close {
      margin: 0 0 0 var(--ed-spacing-lg);
      font-size: var(--ed-font-size-lg);
      &:hover {
        color: var(--ed-negative-2) !important;
      }
    }
  }

  .project-card-metadata-link-submission-layout {
    width: rem-calc(660);
    border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    border-radius: var(--ed-border-radius-md);
    background-size: cover;
    box-shadow: 0 rem-calc(2) rem-calc(4) rgba(0, 0, 0, 0.1);
    position: absolute;
    z-index: 5;
    margin-left: rem-calc(75);
    margin-top: calc(-1 * var(--ed-spacing-sm));
    background-color: var(--ed-white);
    @media (max-width: 850px) {
      margin-left: 0;
    }
    @media (max-width: 750px) {
      width: auto;
    }
    .project-card-header-container {
      height: rem-calc(63);
      padding-left: var(--ed-spacing-base);
      border-bottom: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
      font-size: var(--ed-font-size-lg);
      color: var(--ed-text-color-primary);
    }
    .file-container {
      .ed-upload-btn {
        width: max-content;
      }
    }
    .project-card-content {
      padding: var(--ed-spacing-lg);
      border-bottom: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);

      .ed-input-container {
        .input-field {
          &::placeholder {
            color: var(--ed-state-disabled-color);
          }
        }
      }
    }
    .error-text {
      margin-top: var(--ed-spacing-4xs);
      color: var(--ed-negative-2);
      font-size: var(--ed-font-size-sm);
    }
    .project-card-link-submission-action {
      .add-submission-btn {
        height: rem-calc(38);
      }
      .cancel-submission-btn {
        width: rem-calc(120);
      }
      padding: var(--ed-spacing-lg);
      height: rem-calc(78);
      text-align: center;
    }
    &.submit-layout-data {
      padding: rem-calc(90) rem-calc(150);
      .or-text {
        color: var(--ed-text-color-supporting);
      }
    }
  }
  .remarks-modal-sec {
    padding: rem-calc(30);
    .remarks-modal-text {
      text-align: left;
      color: var(--ed-text-color-primary);
      margin-bottom: var(--ed-spacing-2xs);
    }
  }
}

.project-card-metadata-user-layout {
  .metadata-card-user-layout {
    border-radius: var(--ed-border-radius-lg);
    box-shadow: var(--ed-shadow-base);
    stroke-width: 1;
    margin-top: var(--ed-spacing-5xs);
    border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    height: rem-calc(130);
    .card-user-table-container {
      width: rem-calc(320);
      height: rem-calc(130);
      margin-bottom: calc(-1 * var(--ed-spacing-xl));
      padding: var(--ed-spacing-base);
      .metadata-user-list-container {
        &:not(:last-child) {
          margin-bottom: var(--ed-spacing-base);
        }
        .img-blurred-container.avatar-box {
          display: block;
        }
        .user-name {
          font-size: var(--ed-font-size-base);
          color: var(--ed-text-color-primary);
        }
        .user-handle {
          margin-top: calc(-1 * var(--ed-spacing-5xs));
          font-size: var(--ed-font-size-sm);
          color: var(--ed-text-color-supporting);
        }
      }
      .not-found-message {
        font-size: var(--ed-font-size-2xs);
      }
    }
  }
}
