import React from 'react';
import PropTypes from 'prop-types';
import getFormattedDateTime from '../../../../../app/utils/getFormattedDateTime';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import classNames from 'classnames';

const SubmittedDataContainer = props => {
  const formattedDate = getFormattedDateTime(new Date(), 'DD MMM YYYY');
  const {
    submission,
    gradeStatus,
    media,
    projectFileAction,
    fileAction,
    cancelCardClickHandler
  } = props;
  const grade = gradeStatus
    ? gradeStatus === 'In Review'
      ? `${translatr('web.common.main', 'InReview')}`
      : gradeStatus
    : null;
  return (
    <div className="justflex submissions-container">
      <div className="flex-7 justflex align-items-center url-wrapper">
        <a
          className="link justflex"
          href={submission || media?.url}
          target="_blank"
          rel="noopener noreferrer"
        >
          <i
            className={classNames({ 'icon-link-light icon': submission, 'icon-download': media })}
          />
          <span className="link-text">{submission || media?.filename}</span>
        </a>
        {fileAction && (
          <button
            className="ed-btn ed-btn-neutral ml-5 projectfile-action-button s-padding"
            onClick={() => projectFileAction()}
          >
            <i className="icon-edit-light" />
            {translatr('web.common.main', fileAction)}
          </button>
        )}
      </div>
      <div className="flex-2 justflex align-items-center grade-container">
        {formattedDate && (
          <div className="grade-wrapper">
            {grade ? (
              <>
                {translatr('web.common.main', 'Grade')}:{' '}
                <span className="grade-details">{grade}</span>
              </>
            ) : (
              formattedDate
            )}
          </div>
        )}
      </div>
      {!gradeStatus && (
        <button className="pointer close-container" onClick={() => cancelCardClickHandler()}>
          <i className="icon-x-mark-Close icon" />
        </button>
      )}
    </div>
  );
};

SubmittedDataContainer.propTypes = {
  submission: PropTypes.string,
  gradeStatus: PropTypes.string,
  media: PropTypes.object,
  projectFileAction: PropTypes.func,
  fileAction: PropTypes.string,
  cancelCardClickHandler: PropTypes.func
};

export default SubmittedDataContainer;
