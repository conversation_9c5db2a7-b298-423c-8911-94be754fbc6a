@import '~styles/_base.scss';
@import '~styles/_common.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui .card-std-content-tile {
  &.poll-tile-container,
  &.quiz-tile-container {
    display: flex;
    flex-direction: column;
  }

  height: rem-calc(200);
  position: relative;

  &.poll-tile,
  &.quiz-tile {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: unset;

    .card-std-text {
      min-height: unset;
    }
  }

  overflow: hidden;

  .card-std-video-processing {
    height: rem-calc(146);
  }

  .card-std-tile-description-with-image {
    margin-top: rem-calc(-0.5);
  }

  .card-std-text {
    min-height: rem-calc(40);
  }

  .card-optional-status {
    top: 26%;
    left: var(--ed-spacing-xs);
    z-index: 3;
  }
}

.card-std-video-processing {
  width: 100%;
}

/*purpose of writing important here
without important, the image inside ckEditor takes inline styling generated by ckEditor
inline styling is in terms of px which distorts the UI.
hence writing important*/
.next-message-rte,
.previous-message-rte {
  .ckEditor-description {
    table {
      width: 100% !important;
    }

    img {
      height: auto !important;
    }
  }
}

.card-std-text * {
  font-size: var(--ed-font-size-supporting);
  line-height: rem-calc(20);
  margin-top: 0;
  margin-bottom: 0;
  word-wrap: break-word; // IE11
  word-break: break-word;

  .ckEditor-description > ul,
  li,
  ol,
  a,
  u,
  s,
  em,
  var,
  strong,
  ins {
    font-size: var(--ed-font-size-base);
  }
}

.card-overlay-black {
  position: absolute;
  background: rgba(0, 0, 0, 0.68);
  color: var(--ed-white);
  top: 0;
  text-align: center;
  z-index: 2;
  @extend .justify-center;
  @extend .flex-column;
  @extend .width-100;
  @extend .height-100;
}

.card-std-text {
  font-size: var(--ed-font-size-supporting);
  font-weight: var(--ed-font-weight-normal);
  line-height: rem-calc(20);
  letter-spacing: rem-calc(0.1);
  color: var(--ed-text-color-primary);

  .rich-text-read-only > p {
    margin: rem-calc(12) 0;

    &:first-child {
      margin-top: 0;
      font-size: var(--ed-font-size-supporting);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .rich-text-read-only > ul,
  ol {
    margin-top: rem-calc(6);
  }
}

.card-std-content-tile.text-without-thumbnail {
  padding-bottom: rem-calc(10);

  .rich-text-read-only {
    height: rem-calc(200);
    overflow: hidden;

    & > p,
    ul,
    ol {
      margin-top: rem-calc(20);
      margin-bottom: var(--ed-spacing-lg);

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.ed-ui .card-std-content-standalone {
  .rich-text-read-only > p,
  ul,
  ol {
    margin-top: rem-calc(20);
    margin-bottom: var(--ed-spacing-lg);

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-heading-text {
    font-size: rem-calc(18);
  }

  .card-std-description {
    & > {
      p,
      span {
        font-size: var(--ed-font-size-base);
      }
    }
  }

  .transcript-button {
    button:first-of-type {
      padding: 0;

      i {
        margin: 0;
      }
    }
  }

  .open-content-link {
    color: var(--ed-text-color-supporting);
  }
}

.card-std-tile-description-with-image {
  .rich-text-read-only > p {
    margin-bottom: rem-calc(14);
  }
}

.ed-multiple-smartcard-border {
  border-top-left-radius: var(--ed-border-radius-lg) !important;
  border-top-right-radius: var(--ed-border-radius-lg) !important;
}

.ed-ui .card-std-content {
  .multiple-smartcard-layer-1 {
    background: $fixed-overlay-color;
    @extend .ed-multiple-smartcard-border;
  }

  .multiple-smartcard-layer-2 {
    background: var(--ed-state-disabled-color);
    @extend .ed-multiple-smartcard-border;
  }

  .live-stream-msg-tile {
    height: rem-calc(146);
    margin: rem-calc(2) 0;
    background: rgba(0, 0, 0, 0.68);
    color: var(--ed-white);
    padding-top: rem-calc(90);
    letter-spacing: rem-calc(0.01);
    line-height: rem-calc(20);
    margin-bottom: var(--ed-spacing-xs);
  }

  .video-iframe {
    margin: 0;
    display: block;
    border-radius: var(--ed-border-radius-lg);
  }

  .video-stream-wrappper {
    width: 100%;
    background: var(--ed-black);
    position: relative;
    margin: rem-calc(2) 0;
    height: rem-calc(294);

    .video-player {
      width: 100%;
      height: rem-calc(294);
    }

    .end-of-stream-message {
      height: rem-calc(294);
      padding: rem-calc(135) 1rem 1rem;
    }
  }

  &.card-std-content-bigcard {
    .video-iframe {
      margin-top: rem-calc(12);
    }
  }

  &.card-std-content-standalone {
    &.card-std-resource-present {
      .video-iframe {
        margin: rem-calc(13) auto 0;

        & ~ .card-resource-wrapper {
          margin-bottom: rem-calc(13);
        }
      }
    }

    .video-iframe {
      margin: rem-calc(13) auto;
    }

    .video-stream-wrappper {
      height: rem-calc(500);

      .video-player {
        height: rem-calc(500);
      }

      .end-of-stream-message {
        height: rem-calc(500);
        padding: rem-calc(225) 1rem 1rem;
      }
    }
  }

  &.card-std-content-standalone {
    &.card-std-resource-present {
      .video-iframe {
        margin: rem-calc(13) 0 0 0;

        & ~ .card-resource-wrapper {
          margin-bottom: rem-calc(13);
        }
      }
    }

    .lms-msg-container {
      border: var(--ed-border-size-sm) solid $purple;
      border-radius: var(--ed-border-radius-md);
      padding: var(--ed-spacing-xs) 0.75rem;
      border-radius: var(--ed-border-radius-lg);
      text-align: center;
      background-color: #f4f1f9;
      line-height: var(--ed-line-height-base);
      color: var(--ed-info-5);
    }

    .video-iframe {
      margin: rem-calc(13) auto;
    }

    .embedHtml-wrapper {
      margin: 0.8125rem auto;

      &.with-fullScreen-btn {
        .video-iframe {
          margin: rem-calc(13) auto 0;
          border-bottom: none;
          border-bottom-left-radius: 0;
          border-bottom-right-radius: 0;
        }

        .fullScreen-btn-wrapper {
          background-color: var(--ed-black);
          opacity: 0.8;
          padding: var(--ed-spacing-2xs);
          display: flex;
          justify-content: flex-end;
          border-left: 0.063rem solid var(--ed-black);
          border-right: 0.063rem solid var(--ed-black);
          border-bottom-left-radius: var(--ed-border-radius-lg);
          border-bottom-right-radius: var(--ed-border-radius-lg);

          i {
            color: var(--ed-white);
          }
        }
      }

      .video-iframe {
        width: 100%;
      }
    }

    .card-std-special-design-for-standalone {
      margin: rem-calc(13) 0;
      @extend .justflex;

      .card-std-thumbnail {
        margin: 0;
      }

      @media (max-width: $breakpoint-sm) {
        flex-direction: column;
      }

      .card-std-thumbnail {
        @media (max-width: $breakpoint-xs) {
          flex: 1;
        }

        @media (min-width: $breakpoint-xxs) {
          flex: 1;
          height: rem-calc(280);
        }

        @media (min-width: $breakpoint-sm) {
          flex: rem-calc(320);
          height: rem-calc(180);
        }

        @media (min-width: $breakpoint-md) {
          flex: rem-calc(400);
          height: rem-calc(225);
        }
      }

      .card-resource-wrapper {
        @media (max-width: $breakpoint-sm) {
          flex: 1;
          margin-top: var(--ed-spacing-base);
          padding-left: 0;
        }

        @media (min-width: $breakpoint-sm) {
          flex: calc(100% - 20rem);
          padding-left: var(--ed-spacing-base);
          margin-top: 0;
        }

        @media (min-width: $breakpoint-md) {
          flex: calc(100% - 25rem);
        }
      }
    }
  }

  .scorm-error-msg {
    display: flex;
    @extend .card-overlay-black;
  }

  .waiting-video {
    margin: rem-calc(2) 0;
  }

  .play-icon {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(38, 39, 59, 0.5);
    z-index: 2;
    color: var(--ed-white);

    &:not(.register-play-button) {
      cursor: pointer;
    }

    .icon-play-circle {
      font-size: rem-calc(60);

      &.register-play-button {
        cursor: pointer;
      }
    }
  }

  .card-std-thumbnail {
    margin: rem-calc(2) 0;
    position: relative;
    border-radius: var(--ed-border-radius-lg);

    .card-std-skill {
      position: absolute;
      z-index: 98;
      top: 0.5rem;
      left: 0.5rem;
      color: $purple;
      background-color: #f5eeff;
      border: none;
      font-weight: 700;
      padding: 0.5rem;
    }
  }

  &.card-std-resource-present {
    .card-std-thumbnail {
      margin: rem-calc(11) 0 0 0;
    }
  }

  .card-std-thumbnail-tile {
    height: rem-calc(146) !important;
    margin: rem-calc(5) 0 rem-calc(8) 0;
    overflow: hidden;
  }

  .card-std-thumbnail-standalone.register-play-button {
    height: rem-calc(568);
  }

  .card-std-thumbnail-tile.card-std-multiple-smartcard-overlay {
    height: rem-calc(134) !important;
    width: rem-calc(238);
    margin-left: auto;
    margin-right: auto;
  }

  .card-std-thumbnail-bigcard {
    width: 100%;
    height: auto;
    margin: var(--ed-spacing-2xs) 0 rem-calc(2) 0;
    overflow: hidden;

    img {
      max-width: 100%;
      flex-shrink: 0; // This is for IE11 squishing images
    }
  }

  .card-std-multiple-smartcard-overlay {
    margin-top: 0;
  }

  .card-std-thumbnail-standalone {
    width: 100%;
    margin: rem-calc(12) 0 rem-calc(8);
    overflow: hidden;
  }

  .card-std-bookmark {
    height: 3.8125rem;
    width: 5.375rem;
  }

  .card-std-locked-content {
    .card-std-ie-image-fix {
      width: rem-calc(705);
    }
  }

  .card-std-locked-consumption-content {
    .card-std-ie-image-fix {
      width: rem-calc(713);
    }
  }

  .height-width-100 {
    height: 100%;
    width: 100%;
  }
}

.pathway-tag {
  position: absolute;
  z-index: 2;
  width: rem-calc(68);
  height: rem-calc(48);
  background: var(--ed-white);
  border: rem-calc(2) solid var(--ed-state-disabled-color);
  border-left: 0;
  box-shadow: var(--ed-shadow-base);
  border-top-right-radius: var(--ed-border-radius-lg);
  border-bottom-right-radius: var(--ed-border-radius-lg);
  margin-top: var(--ed-spacing-5xs);
  margin-left: rem-calc(-18);

  .icon-pathway {
    color: var(--ed-text-color-supporting);
  }

  div {
    font-size: var(--ed-font-size-2xs);
    color: var(--ed-text-color-primary);
  }
}

.blurred-thumbnail {
  // position: absolute;
  // left: 0;
  // top: 0;
  // z-index: 1;
  // overflow: hidden;
  // @extend .height-width-100;
  height: 100%;
  width: 100%;

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .card-blurred-background,
    .card-blurred-background svg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
    }

    img {
      z-index: 1;
      max-width: 100%;
      max-height: 100%;
      flex-shrink: 0; //IE11 fix
      border-radius: var(--ed-border-radius-lg);
    }
  }
}

.promoted-content-text {
  z-index: 2;
  top: rem-calc(45);
  left: 0;
  background-color: var(--ed-text-color-primary);
  color: var(--ed-white);
  border-top-left-radius: var(--ed-border-radius-lg);
  border-bottom-right-radius: var(--ed-border-radius-lg);
}

.card-std-content-bigcard {
  .card-std-text {
    max-height: 2.625rem;
    overflow: hidden;
    font-size: var(--ed-font-size-base);
  }

  .card-std-text:not(.html-container) * {
    font-size: var(--ed-font-size-base);
  }

  .card-std-text-without-thumbnails {
    max-height: rem-calc(446);
    padding-top: rem-calc(8);
  }

  .text-without-thumbnail {
    padding-bottom: rem-calc(6);

    .html-container {
      min-height: rem-calc(40);

      p {
        font-size: var(--ed-font-size-base);
      }
    }
  }
}

.card-std-content-standalone {
  .card-std-description {
    @extend .font-size-m;

    ul {
      list-style: disc;
    }

    ol {
      list-style: decimal;
    }
  }

  // to fix the float issue in ckeditor
  .clear-both {
    clear: both;
  }

  .poll-toggle {
    font-size: var(--ed-font-size-sm);
    cursor: pointer;
    color: $france;
  }
}

.card-std-bigcard.ed-ui .card-resource-wrapper {
  margin-top: rem-calc(5);

  .card-std-resource-title {
    .card-heading-text {
      font-size: rem-calc(13);
      color: var(--ed-text-color-supporting);
      font-weight: var(--ed-font-weight-normal);
    }

    p {
      font-size: rem-calc(13);
      color: var(--ed-text-color-supporting);
      font-weight: var(--ed-font-weight-normal);
    }
  }
}

.card-std-standalone-wrapper.ed-ui .card-resource-wrapper {
  & > .ed-link {
    margin-left: rem-calc(0);
    display: block;
  }

  .card-std-resource-title {
    margin-bottom: rem-calc(7);
    color: var(--ed-text-color-primary);

    p {
      font-size: rem-calc(18);
      font-weight: 500;
    }
  }

  .card-std-resource-description span {
    color: var(--ed-text-color-supporting);
    font-size: var(--ed-font-size-base);

    > p {
      color: var(--ed-text-color-supporting);
      line-height: inherit;
    }

    ul {
      list-style: disc;
    }

    ol {
      list-style: decimal;
    }
  }

  &.scorm-desc-wrapper {
    .card-std-resource-description {
      display: inline;

      p {
        color: inherit;

        &:only-child {
          display: inline;
        }
      }

      ol {
        list-style: decimal;
      }

      ul {
        list-style: disc;
      }
    }

    & > .ed-link {
      display: inline;
    }
  }
}

.project-card-std-user-layout {
  z-index: 2100;
}

.card-std-comment-layout {
  @extend .project-card-std-user-layout;
  max-height: 6rem;
}

.card-std-file-wrapper {
  flex-direction: row;
  align-items: stretch;
  max-height: 30rem;

  .transcript-display-container {
    max-width: rem-calc(380);
    margin-top: 0.3125rem;

    &.video-processing-margin {
      margin-bottom: var(--ed-spacing-xs);
    }
  }

  .transcript-header {
    border: var(--ed-border-size-sm) solid var(--ed-border-color);
    flex-direction: row;
    border-radius: var(--ed-border-radius-md) var(--ed-border-radius-md) 0 0;

    & h6 {
      margin-bottom: 0;
    }
  }

  .transcript-content {
    overflow-y: scroll;
    border: var(--ed-border-size-sm) solid var(--ed-border-color);
    border-top: none;
    border-radius: 0 0 var(--ed-border-radius-md) var(--ed-border-radius-md);

    pre {
      white-space: pre-wrap;
    }
  }
}

//For upload and podcast card
.ed-ui .card-std-file {
  width: 100%;
  margin: 0.3125rem 0 0.5rem;
  position: relative;
  text-align: center;

  audio {
    width: 100%;
  }

  &.tile {
    height: rem-calc(146);

    iframe,
    video {
      width: 100%;
      height: 100%;
    }
  }

  &.bigcard {
    margin-top: var(--ed-spacing-xs);

    iframe,
    video {
      width: 100%;
      height: rem-calc(292);
    }
  }

  &.standalone {
    max-width: rem-calc(800);
    margin: 0.3125rem auto 0.5rem;

    iframe,
    video {
      margin: 0 auto;
      border-radius: var(--ed-border-radius-lg);
      width: 100%;
    }

    &.application,
    &.video {
      iframe,
      .pdf-viewer-container {
        height: rem-calc(545);
      }
    }
  }

  .download-block {
    height: rem-calc(36);
    width: rem-calc(100);
    position: absolute;
    right: rem-calc(40);

    .download-button {
      width: rem-calc(36);
      height: rem-calc(36);
      border-radius: var(--ed-border-radius-lg);
      border-color: var(--ed-black) !important;
      color: var(--ed-text-color-primary);
      background-color: var(--ed-white);

      &:hover {
        background-color: var(--ed-input-hover-bg-color);
      }
    }
  }

  &.video .download-block {
    top: rem-calc(20);
  }

  &.application .download-block {
    top: rem-calc(80);
  }

  /* custom-audio-player-styles-start */
  .progress-bar,
  .volume-bar {
    --seek-before-width: 0;

    appearance: none;
    background: var(--ed-neutral-7);
    border: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    border-radius: var(--ed-border-radius-lg);
    position: relative;
    width: 100%;
    height: rem-calc(5);

    &::-moz-focus-outer {
      border: 0;
    }

    /* progress bar - chrome and safari */
    &::before {
      content: '';
      height: 100%;
      width: var(--seek-before-width);
      background-color: var(--ed-text-color-primary);
      border-top-left-radius: var(--ed-border-radius-lg);
      border-bottom-left-radius: var(--ed-border-radius-lg);
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      cursor: pointer;
    }

    /* progress bar - firefox */
    &::-moz-range-progress {
      background-color: var(--ed-text-color-primary);
      border-top-left-radius: var(--ed-border-radius-lg);
      border-bottom-left-radius: var(--ed-border-radius-lg);
      height: 100%;
    }

    /* knobby - chrome and safari */
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      height: rem-calc(15);
      width: rem-calc(15);
      border-radius: var(--ed-border-radius-circle);
      border: none;
      background-color: transparent;
      cursor: pointer;
      position: relative;
      margin: rem-calc(-2) 0 0 0;
      z-index: 3;
      box-sizing: border-box;
    }

    /* knobby while dragging - chrome and safari */
    &:active {
      &::-webkit-slider-thumb {
        transform: scale(1.2);
        background: transparent;
      }
    }

    /* knobby - firefox */
    &::-moz-range-thumb {
      height: rem-calc(15);
      width: rem-calc(15);
      border-radius: var(--ed-border-radius-circle);
      border: transparent;
      background-color: transparent;
      cursor: pointer;
      position: relative;
      z-index: 3;
      box-sizing: border-box;
    }

    /* knobby while dragging - firefox */
    &:active {
      &::-moz-range-thumb {
        transform: scale(1.2);
        background: transparent;
      }
    }
  }

  .volume-bar {
    --seek-before-volume-width: 100%;
    height: rem-calc(10);

    &::before {
      width: var(--seek-before-volume-width);
    }
  }

  .audio-player,
  .audio-controls,
  .audio-bar {
    align-items: center;
  }

  .progress-bar-sec {
    width: rem-calc(420);
  }

  .rewind,
  .forward,
  .mute-unmute-btn {
    margin-left: var(--ed-spacing-base);
    cursor: pointer;
  }

  .forward,
  .playpause {
    margin-right: var(--ed-spacing-base);
    cursor: pointer;
  }

  .current-time {
    margin-right: var(--ed-spacing-2xs);
  }

  .duration {
    margin-left: var(--ed-spacing-2xs);
  }

  .current-time,
  .duration {
    color: var(--ed-text-color-supporting);
    font-size: rem-calc(13);
  }

  .playpause {
    border-radius: var(--ed-border-radius-circle);
    height: rem-calc(40);
    width: rem-calc(40);
    background-color: var(--ed-text-color-primary);

    .card-icon {
      color: var(--ed-white);
      font-size: var(--ed-font-size-supporting);
      margin-left: var(--ed-spacing-3xs);
    }
  }

  .select-audio-speed {
    appearance: none;
    background: transparent;
    border: none;
    color: var(--ed-text-color-supporting);
    font-size: rem-calc(13);
    padding: rem-calc(5);
    max-width: rem-calc(46);
  }

  .mute-unmute-btn {
    position: relative;

    .volume-bar {
      display: none;
      transform: rotate(270deg);
      top: rem-calc(-65);
      left: rem-calc(-35);
      margin-top: var(--ed-spacing-xs);
      margin-bottom: var(--ed-spacing-xs);
      position: absolute;
      width: rem-calc(100);
    }

    &:hover {
      .volume-bar {
        display: block;
        z-index: 1;
      }
    }
  }
}

.card-std-file.tile.audio {
  display: flex;
  justify-content: center;
  align-items: center;

  audio {
    width: 95%;
  }

  .audio-player {
    display: block;
    padding: var(--ed-spacing-2xs);
  }

  .playpause {
    margin-right: var(--ed-spacing-2xs);
  }

  .progress-bar-sec {
    width: 50%;
  }

  .audio-controls {
    float: right;
  }

  .mute-unmute-btn {
    margin-right: var(--ed-spacing-2xs);
  }
}

.card-std-file.bigcard.audio {
  .progress-bar-sec {
    width: rem-calc(220);
  }
}

@media screen and (max-width: 880px) and (min-width: 830px) {
  .ed-ui {
    .card-std-file {
      .progress-bar-sec {
        width: rem-calc(380);
      }
    }
  }
}

@media screen and (max-width: 829px) and (min-width: 770px) {
  .ed-ui {
    .card-std-file {
      .progress-bar-sec {
        width: rem-calc(320);
      }
    }
  }
}

@media screen and (max-width: $breakpoint-md) {
  .card-std-file-wrapper {
    flex-direction: column;
    max-height: none;

    .transcript-display-container {
      max-width: rem-calc(800);
      width: 100%;
      margin: 0.3125rem auto 0.5rem;
    }
  }
}

@media screen and (max-width: $breakpoint-xs) and (min-width: $breakpoint-xxs) {
  .transcript-display-container {
    max-height: rem-calc(220) !important;
  }
}

@media screen and (max-width: 768px) {
  .ed-ui {
    .card-std-file {
      .progress-bar-sec {
        width: 60%;
      }

      .audio-player {
        display: block;
      }

      .audio-controls {
        justify-content: center;
      }
    }
  }
}

/* custom-audio-styles-end */

.scheduled-stream-time-wrapper {
  @extend .card-overlay-black;

  .calendar-wrapper .calendar-icon {
    height: rem-calc(30);
    margin-right: var(--ed-spacing-xs);
  }

  .message {
    text-align: left;
  }
}

.vilt-link {
  color: $darkerblue;

  &:hover,
  &:active,
  &:visited {
    color: $darkerblue !important;
  }
}

.ckEditor-description {
  ul,
  ol {
    margin-top: 0;
    margin-bottom: 0;
  }

  a {
    @extend .vilt-link;
  }
}

.ckEditor-text-card-description {
  width: calc(100% + 1rem);
  position: relative;
  left: -1rem;

  .ckEditor-description {
    padding-left: var(--ed-spacing-base);

    & > {
      ul,
      li,
      ol,
      u,
      s,
      em,
      var,
      strong,
      ins {
        font-size: var(--ed-font-size-base);
      }
    }

    div[data-oembed-url] {
      > div[style*='max-width:320px'] {
        max-width: none !important;
      }
    }
  }

  .ckEditor-h-tag {
    line-height: var(--ed-line-height-sm);
  }

  .ckEditor-font-weight-normal {
    font-weight: var(--ed-font-weight-normal);
  }

  h1 {
    font-size: 2.625rem;
    margin: 1.7587rem 0;
    @extend .ckEditor-font-weight-normal;
    @extend .ckEditor-h-tag;
  }

  h1 * {
    font-size: 2.625rem;
  }

  h2 {
    font-size: 2.25rem;
    margin: 1.8675rem 0;
    @extend .ckEditor-font-weight-normal;
    @extend .ckEditor-h-tag;
  }

  h2 * {
    font-size: 2.25rem;
  }

  h3 {
    font-size: var(--ed-font-size-2xl);
    margin: 1.75rem 0;
    @extend .ckEditor-font-weight-normal;
    @extend .ckEditor-h-tag;
  }

  h3 * {
    font-size: var(--ed-font-size-2xl);
  }

  pre {
    white-space: pre-wrap;
    -moz-tab-size: 4;
    tab-size: 4;
    font-size: var(--ed-font-size-sm);
    margin: 0.875rem 0;
  }

  blockquote {
    font-style: italic;
    padding: var(--ed-spacing-5xs) 0;
    border-style: solid;
    border-color: $lightgrey;
    border-width: 0;
    padding-left: var(--ed-spacing-lg);
    padding-right: var(--ed-spacing-2xs);
    border-left-width: rem-calc(5);
    margin: rem-calc(14) rem-calc(40);

    p {
      color: var(--ed-text-color-primary);
    }
  }

  .marker {
    background-color: Yellow;
  }

  big {
    font-size: larger;
  }

  small {
    all: unset;
    font-size: smaller;
  }

  code {
    all: unset;
    font-size: larger;
  }

  tt {
    font-family: monospace;
  }

  var {
    font-style: italic;
  }

  cite {
    font-style: italic;
    color: var(--ed-text-color-primary);
  }

  cite:before {
    content: none;
  }
}

.ed-ui .card-std-content {
  .card-std-thumbnail-journey,
  .card-std-thumbnail-pathway {
    height: rem-calc(171);

    @include min-screen-width($breakpoint-xs) {
      height: rem-calc(130);
    }

    @include min-screen-width($breakpoint-sm) {
      height: rem-calc(158);
    }

    @include min-screen-width($breakpoint-md) {
      height: rem-calc(219);
    }

    @include min-screen-width($breakpoint-lg) {
      height: rem-calc(225);
    }

    margin: 0;
    overflow: hidden;
    border-radius: var(--ed-border-radius-lg);
  }
}

@media (max-width: 1024px) {
  .card-std-content {
    &.card-std-content-standalone {
      .card-std-special-design-for-standalone {
        display: block;
        height: 100%;
      }
    }
  }
}

.poll-star-option {
  list-style: none;
  margin-left: 0;
}

.card-image-placeholder {
  width: 16.25rem;
  height: 9.125rem;
  border-radius: var(--ed-border-radius-lg) !important;
  padding-left: 5rem;
  padding-right: 5rem;
  padding-top: 1.438rem;
  padding-bottom: 1.438rem;
  fill: $darkgrey;

  .image-error {
    width: 6.25rem;
    height: 6.25rem;
    fill: $darkgrey;
  }
}

@media screen and (max-width: $breakpoint-lg) {
  .card-std-standalone-wrapper {
    .card-std-standalone {
      width: 100%;

      &.subscriptions-included {
        width: calc(100% - 19.188rem);
      }
    }
  }
}

@media screen and (max-width: 1000px) {
  .ed-ui {
    .card-std-content {
      .video-iframe {
        width: 100%;
      }
    }
  }
}

.fieldset-list {
  padding: 0;
  border: none;
  margin: 0;
}

.carousel-arrow-wrapper {
  &.slick-next,
  &.slick-prev {
    position: absolute;
    top: calc(50% - 1rem);
  }

  &.slick-next {
    right: 0;
    z-index: 1;
  }

  &.slick-prev {
    left: 0;
    z-index: 1;
  }

  .carousel-arrow {
    width: rem-calc(35);
    height: rem-calc(35);
    min-width: rem-calc(35);
    padding: 0;
    border-radius: var(--ed-border-radius-circle);
    background-color: var(--ed-white);
    border-color: var(--ed-state-disabled-color);
    box-shadow: var(--ed-button-shadow);
    cursor: pointer;

    &:hover {
      background-color: var(--ed-state-active-color);
      .icon-angle-left-arrow,
      .icon-angle-right-arrow {
        color: var(--ed-body-bg-color);
      }
    }

    &:disabled {
      display: none;
    }

    .icon-angle-left-arrow,
    .icon-angle-right-arrow {
      font-size: rem-calc(23);
      font-weight: var(--ed-font-weight-bold);
    }
  }
}

[dir='rtl'] {
  .carousel-arrow-wrapper .carousel-arrow {
    transform: rotate(180deg);
  }
}
