@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.h-role-card {
  margin: var(--ed-spacing-sm) 0;
  font-size: var(--ed-font-size-sm);

  .ed-h-card-title-metadata__flags {
    padding-bottom: var(--ed-spacing-2xs);
  }

  .ed-h-card-content__metadata-placeholder-row {
    display: flex;
    flex-direction: column;
    gap: var(--ed-spacing-2xs);

    button.ed-btn-v2 {
      padding-left: 0;
    }

    .project-owners {
      .project-owners-avatar {
        margin-top: 0 !important;
      }
      .project-owners-name-list {
        font-size: var(--ed-font-size-sm);
      }
    }
  }

  &__row-items {
    display: flex;
    gap: var(--ed-spacing-2xs);
    align-items: center;

    > span {
      &.items-in-row-1 {
        max-width: 100%;
      }
      &.items-in-row-2 {
        max-width: 49%;
      }
      &.items-in-row-3 {
        max-width: 33%;
      }
    }
  }
  &__selected .ed-h-card {
    outline: var(--ed-border-size-md) solid var(--ed-primary-base);
  }
}
