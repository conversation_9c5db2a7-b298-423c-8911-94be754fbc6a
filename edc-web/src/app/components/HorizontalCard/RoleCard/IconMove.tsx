import { useTheme } from 'centralized-design-system/src/Theme/ThemeContext';
import React from 'react';

interface IconMoveProps {
  size?: number;
  color?: string;
}

export const IconMove: React.FC<IconMoveProps> = ({
  size = 12,
  color
}) => {
  const theme = useTheme();
  const fill = color || theme.palette.primaryBase
  
  return (
    <svg 
        width={size} 
        height={size} 
        viewBox={`0 0 12 12`}
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
    >
        <path 
            d="M8.33329 8.17012V8.81324C9.63058 8.98866 10.5 9.30491 10.5 9.6672C10.5 10.2195 8.48517 10.6672 5.99996 10.6672C3.51475 10.6672 1.49996 10.2193 1.49996 9.66699C1.49996 9.30491 2.36933 8.98866 3.66663 8.81303V8.16991C1.89204 8.44074 0.666626 9.00845 0.666626 9.66699C0.666626 10.5874 3.05454 11.3337 5.99996 11.3337C8.94538 11.3337 11.3333 10.5874 11.3333 9.66699C11.3333 9.00845 10.1079 8.44074 8.33329 8.17012ZM4.33329 7.33366V9.00033C4.33329 9.55178 4.78183 10.0003 5.33329 10.0003H6.66663C7.21808 10.0003 7.66663 9.55178 7.66663 9.00033V7.33366C8.21808 7.33366 8.66663 6.88512 8.66663 6.33366V5.00033C8.66663 4.28095 8.206 3.6722 7.56621 3.43928C7.73204 3.16366 7.83329 2.84491 7.83329 2.50033C7.83329 1.48949 7.01079 0.666992 5.99996 0.666992C4.98913 0.666992 4.16663 1.48949 4.16663 2.50033C4.16663 2.84491 4.26788 3.16366 4.43371 3.43928C3.79392 3.6722 3.33329 4.28095 3.33329 5.00033V6.33366C3.33329 6.88512 3.78183 7.33366 4.33329 7.33366ZM5.99996 1.33366C6.64433 1.33366 7.16663 1.85595 7.16663 2.50033C7.16663 3.1447 6.64433 3.66699 5.99996 3.66699C5.35558 3.66699 4.83329 3.1447 4.83329 2.50033C4.83329 1.85595 5.35558 1.33366 5.99996 1.33366ZM3.99996 5.00033C3.99996 4.46366 4.42579 4.02783 4.95683 4.0047C5.2535 4.21095 5.61204 4.33366 5.99996 4.33366C6.38788 4.33366 6.74642 4.21095 7.04308 4.0047C7.57413 4.02783 7.99996 4.46366 7.99996 5.00033V6.33366C7.99996 6.51762 7.85017 6.66699 7.66663 6.66699H6.99996V9.00033C6.99996 9.18428 6.85017 9.33366 6.66663 9.33366H5.33329C5.14975 9.33366 4.99996 9.18428 4.99996 9.00033V6.66699H4.33329C4.14975 6.66699 3.99996 6.51762 3.99996 6.33366V5.00033Z" 
            fill={fill}
        />
    </svg>
  );
};
