import React from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import FocusLock from 'react-focus-lock';
import PropTypes from 'prop-types';
import Modal, { ModalHeader, ModalContent } from 'centralized-design-system/src/Modals';
import Carousel from 'centralized-design-system/src/Carousel';
import moment from 'moment';
import './SkillsPassportModal.scss';
import { DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT } from '@utils/constants';
import { openSkillFlyout } from '../../components/MfeSkillsFlyout';

const SkillsPassportModal = props => {
  const { closeHandler, userSkillsPassport, activeUserBasicInfo } = props;
  const { skills, badges, certifications } = userSkillsPassport;
  const { firstName } = activeUserBasicInfo;

  const MfeEnabled =
    (window.__edOrgData?.configs || []).find(config => config.name === 'mfe_tms_config')?.value ||
    false;

  const getSkillIconClass = level => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'icon-skill-beginner';
      case 'intermediate':
        return 'icon-skill-intermediate';
      case 'advanced':
        return 'icon-skill-advanced';
      default:
        return 'icon-skill-empty';
    }
  };

  const getLevelTranslationLabel = level => {
    switch ((level || '').toLowerCase()) {
      case 'advanced':
        return translatr('web.common.main', 'Advanced');
      case 'intermediate':
        return translatr('web.common.main', 'Intermediate');
      default:
        return translatr('web.common.main', 'Beginner');
    }
  };

  function renderSkills() {
    return skills.map((skill, i) => {
      const { level, skillName, issuer, experience, createdAt, verified } = skill;
      return (
        <div className="skill-carousel-item" key={i}>
          <div className="flx">
            <span className="icon-container-wrapper">
              <span className="relative">
                {verified && <i className="icon-badge-check position-absolute verified-badge"></i>}
              </span>
              <i className={`${getSkillIconClass(level?.toLowerCase() || '')}`} />
            </span>
            <div className="name-description-wrapper">
              {MfeEnabled ? (
                <button
                  className="name-label short-text"
                  onClick={() => {
                    openSkillFlyout(skill?.skills?.[0]);
                    closeHandler();
                  }}
                >
                  {`${skillName ? skillName : ''}`}
                </button>
              ) : (
                <div className="name-label short-text">{`${skillName ? skillName : ''}`}</div>
              )}
              <div className="description-label short-text">{`${issuer ? issuer : ''}`}</div>
            </div>
          </div>
          <div className="item-footer-wraper">
            <span className="exp-label short-text">
              {experience
                ? translatr('web.common.main', 'OfExperience', { value: experience })
                : ''}
            </span>
            <span className="date-label short-text">
              {createdAt ? moment(createdAt).format(DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT) : ''}
            </span>
          </div>
        </div>
      );
    });
  }
  function renderBadges() {
    return badges.map((badge, i) => {
      const { badgeId, badgeName, credential, issuer, issueDate, level, verified } = badge;
      return (
        <div className="badge-carousel-item" key={`${i}-${badgeId}`}>
          <div>
            <div className="title short-text">{`${badgeName ? badgeName : ''}`}</div>
            <div className="subtitle short-text">{`${issuer ? issuer : ''}`}</div>
          </div>
          <span className="relative">
            {verified && <i className="icon-badge-check position-absolute verified-badge"></i>}
          </span>
          <div className="badge-image">
            {credential?.url ? (
              <img src={credential.url} alt={translatr('web.common.main', 'BadgeImage')} />
            ) : (
              <i className="icon-award-badge" />
            )}
          </div>
          <div className="item-footer-wraper">
            <span className="exp-label capitalize">{getLevelTranslationLabel(level)}</span>
            <span className="date-label short-text">
              {issueDate ? moment(issueDate).format(DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT) : ''}
            </span>
          </div>
        </div>
      );
    });
  }

  function renderCertificates() {
    return certifications.map((certification, i) => {
      const {
        certificationId,
        certificationName,
        issuer,
        issueDate,
        level,
        verified
      } = certification;
      return (
        <div className="skill-carousel-item" key={`${i}-${certificationId}`}>
          <div className="flx">
            <span className="icon-container-wrapper">
              <span className="relative">
                {verified && <i className="icon-badge-check position-absolute verified-badge"></i>}
              </span>
              <i className="icon-file-certificate" />
            </span>
            <div className="name-description-wrapper">
              <div className="name-label short-text">
                {`${certificationName ? certificationName : ''}`}
              </div>
              <div className="description-label short-text">
                {`${issuer ? issuer : 'Empty description'}`}
              </div>
            </div>
          </div>
          <div className="item-footer-wraper">
            <span className="exp-label short-text capitalize">
              {getLevelTranslationLabel(level)}
            </span>
            <span className="date-label short-text">
              {issueDate ? moment(issueDate).format(DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT) : ''}
            </span>
          </div>
        </div>
      );
    });
  }

  const capitalize = string => {
    if (!!string?.length && typeof string === 'string')
      return string.charAt(0).toUpperCase() + string.slice(1);
    return string;
  };

  return (
    <>
      <div className="" onClick={e => e.stopPropagation()} role="presentation">
        <Modal className="skills-passport-modal" size="large">
          <FocusLock>
            <ModalHeader
              title={translatr('web.manager-dashboard-v2.main', 'UserSkillPassport', {
                userName: capitalize(firstName)
              })}
              onClose={() => closeHandler()}
            />
            <ModalContent>
              <div className="ed-ui">
                <div className="title short-text">{translatr('web.common.main', 'Skills')}</div>
                <p className="subtitle short-text">
                  {translatr('web.common.main', 'SkillPassportModalContentDesc')}
                </p>
                {!!skills?.length ? (
                  <Carousel>{renderSkills()}</Carousel>
                ) : (
                  <div className="empty-skill">
                    <p>{translatr('web.common.main', 'NoSkillsToShowCurrently')}</p>
                  </div>
                )}
              </div>

              <div className="title short-text">{translatr('web.common.main', 'Badges')}</div>
              <p className="subtitle short-text">
                {translatr(
                  'web.common.main',
                  'BadgesAwardedByCompletingContentOnThePlatformOrOutsideThePlatform'
                )}
              </p>
              {!!badges?.length ? (
                <Carousel>{renderBadges()}</Carousel>
              ) : (
                <div className="empty-badges">
                  <p>{translatr('web.common.main', 'NoBadgesToShowCurrently')}</p>
                </div>
              )}

              <div className="title short-text">{translatr('web.common.main', 'Certificates')}</div>
              <p className="subtitle short-text">
                {translatr(
                  'web.common.main',
                  'DigitalCopyOfTheSkillsOrKnowledgeGainedByCompletingACourse'
                )}
              </p>
              {!!certifications.length ? (
                <Carousel>{renderCertificates()}</Carousel>
              ) : (
                <div className="empty-certificates">
                  <p>{translatr('web.common.main', 'NoCertificatesToShowCurrently')}</p>
                </div>
              )}
            </ModalContent>
          </FocusLock>
        </Modal>
      </div>
    </>
  );
};

SkillsPassportModal.propTypes = {
  closeHandler: PropTypes.func,
  userSkillsPassport: PropTypes.object,
  activeUserBasicInfo: PropTypes.object
};

export default SkillsPassportModal;
