/* =============================================EXTERNAL LIBRARIES====================================================== */
import React, { useEffect, useState, useRef } from 'react';
import { connect } from 'react-redux';
import { any, string, func, object, bool, array } from 'prop-types';
import { map, throttle, xor, intersection, uniqBy } from 'lodash';

/* =============================================INTERNAL LIBRARIES====================================================== */
import { SearchInput } from 'centralized-design-system/src/Inputs';
import { DisplayTag, Tags } from 'centralized-design-system/src/Tags';
import Stack from 'centralized-design-system/src/Stack';
import Loading from 'centralized-design-system/src/Loading';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getUsersFields, searchForUsers } from 'edc-web-sdk/requests/users.v2';
import { getCompletedAssignment } from 'edc-web-sdk/requests/assignments.v2';

/* ============================================= UTILS ====================================================== */
import { useEffectAfterInitialMount } from '@utils/hooks';
import { Permissions } from '../../../../../app/utils/checkPermissions';

import TableRender from './TableRender';
import NoReassignmentsInfoMsg from '../NoReassignmentsInfoMsg';
import * as multiActions from '../../../../../app/actions/multiactionActions';
import AllFilters from './AllFiltersDropdown';
import ReassignContentCheckbox from '../components/ReassignContentCheckbox';
import {
  removeSelectedOption,
  emptySelectedOptionsArr,
  getRequestObj,
  getFilterTypeAndValuefromTagsId,
  areFiltersSelected,
  getPayloadForAssignAction,
  getPayloadForShareAction,
  getSelectedFilters
} from './IndividualsViewUtils';
import LD from '../../../../../app/containers/LDStore';
import { TABS, SHOW_COMPLETE_CARD_TYPES, CURRENT_ACTION } from '../constant';
import { truncateText } from '@utils/utils';
import SelectAllFilteredUsersCheckBox from './components/SelectAllFilteredUsersCheckBox';
import SelectedUserTags from './components/SelectedUserTags';
import Tooltip from 'centralized-design-system/src/Tooltip';

const IndividualsView = props => {
  const {
    contentMultiaction,
    selectAllFilteredUsersCheckBoxHandler,
    setIsSelectAllFilteredUsersChecked,
    isSelectAllFilteredUsersChecked,
    filtersList,
    setFiltersList,
    toggleReassignCompletedContent,
    isRecurringAssignmentChecked
  } = props;

  const [customFieldsList, setCustomFieldsList] = useState([]);
  const [defaultCustomList, setDefaultCustomList] = useState([]);
  const [initialDataLoading, setInitialDataLoading] = useState(true);
  const [currentFetchCount, setCurrentFetchCount] = useState(20);
  const [usersToShow, setUsersToShow] = useState([]);
  const [loadingMoreUsers, setLoadingMoreUsers] = useState(false);
  const [isLoadMoreCompleted, setIsLoadMoreCompleted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCheckedAll, setIsCheckedAll] = useState(false);

  const [searchValue, setSearchValue] = useState('');
  const [showAllFiltersBlock, setShowAllFiltersBlock] = useState(false);
  const [applyFilters, setApplyFilters] = useState(false);
  const [totalUsersCount, setTotalUsersCount] = useState(0);
  const [selectedUserTags, setSelectedUserTags] = useState(
    contentMultiaction?.actionWithUsers || []
  );
  const [assignmentCompletedUsersTags, setAssignmentCompletedUsersTags] = useState([]);

  const searchQuery = useRef({
    limit: 20,
    offset: 0,
    q: ''
  });

  const isCurrentActionAssign = props.currentAction === CURRENT_ACTION.ASSIGN;
  const isCurrentActionShare = props.currentAction === CURRENT_ACTION.SHARE;

  const MULTI_ASSIGNMENTS_FLOW = props.isMultiAssignmentsEnabled;

  const userstoShowRef = useRef(usersToShow);
  const searchTermRef = useRef(searchTerm);
  const delayedAPICall = useRef(null);
  const allFiltersButtonRef = useRef(null);

  const currentCard =
    (contentMultiaction?.currentCardId && contentMultiaction[contentMultiaction.currentCardId]) ||
    {};

  let filteredData = {
    custom_fields: [],
    'names_for_filtering[]': []
  };
  const FILTERING_VALUE = 'names_for_filtering[]';

  const showEmail = props.enableShowEmail || false;
  let customHeaders = customFieldsList.map(val => val.translatedDisplayName);
  let selectedUsers = contentMultiaction?.actionWithUsers || [];

  function countSelectedFilteredUsers(selectedUsersArray, filteredUsersArray) {
    if (!Array.isArray(selectedUsersArray)) {
      return 0;
    }

    const selectedIds = new Set(selectedUsersArray.map(user => user.id));
    const matchingUsers = filteredUsersArray.filter(user => selectedIds.has(user.id));
    return matchingUsers.length;
  }

  useEffect(() => {
    if (
      isCurrentActionAssign &&
      contentMultiaction?.assignmentCompletedUsers?.length &&
      MULTI_ASSIGNMENTS_FLOW
    ) {
      if (selectedUsers.length) {
        const assignmentCompletedUsersIds = contentMultiaction?.assignmentCompletedUsers.map(
          ({ id }) => id
        );
        const completedAssignmentUsers = [];
        const noneCompletedAssignmentUsers = [];
        selectedUsers.forEach(user => {
          // check if selected user id exist in assignmentCompletedUsersIds
          if (assignmentCompletedUsersIds.includes(user.id)) {
            completedAssignmentUsers.push(user);
          } else {
            noneCompletedAssignmentUsers.push(user);
          }
        });
        setSelectedUserTags(noneCompletedAssignmentUsers);
        setAssignmentCompletedUsersTags(completedAssignmentUsers);
      }
      if (!selectedUsers.length) {
        setSelectedUserTags([]);
        setAssignmentCompletedUsersTags([]);
      }
    } else {
      setSelectedUserTags(selectedUsers);
    }
  }, [contentMultiaction?.actionWithUsers]);

  useEffect(() => {
    searchQuery.current.offset = 0;
  }, [searchQuery.current.q]);

  useEffectAfterInitialMount(() => {
    clearTimeout(delayedAPICall.current);
    delayedAPICall.current = setTimeout(handleSearchDebounce, 300);
  }, [searchTerm]);

  // Select All Checkbox
  useEffect(() => {
    checkAllState();
  }, [contentMultiaction?.[props.currentAction], props.groupsV2, isSelectAllFilteredUsersChecked]);

  useEffectAfterInitialMount(() => {
    // UNCHECK SELECT ALL CHECK BOX WHEN LOADING MORE DATA
    isCheckedAll && setIsCheckedAll(false);
  }, [usersToShow]);

  useEffectAfterInitialMount(() => {
    const updatedFiltersList = customFieldsList.map(row => ({
      type: row.displayName,
      translatedLabel: row.translatedDisplayName,
      options: [],
      selectedOptions: []
    }));
    setFiltersList(updatedFiltersList);
  }, [customFieldsList]);

  useEffectAfterInitialMount(() => {
    if (applyFilters) {
      // when clicked on apply filters buttons
      filteredData = {
        custom_fields: JSON.stringify(getSelectedFilters(filtersList) || [])
      };

      const payload = { ...searchQuery.current, ...filteredData, q: searchTermRef.current };
      getUserSearchResult(payload);
      setApplyFilters(false);
    }
  }, [applyFilters]);

  useEffect(() => {
    // ON INITIAL MOUNT
    fetchAllCustomFields();
  }, []);

  const fetchAllCustomFields = () => {
    getUsersFields()
      .then(res => {
        setDefaultCustomList(res?.translatedFields.map(ele => ele.abbreviation) || []);
        setCustomFieldsList(res?.translatedFields || []);
        getUserSearchResult(searchQuery.current, false, res?.translatedFields);
      })
      .catch(err => {
        setInitialDataLoading(false);
        console.error(
          'An error had been occurred while calling getUserCustomFields.getUsersFields function\n',
          err
        );
      });
  };

  const getUserSearchResult = (payloadObj, isLoadMore, translatedFields = []) => {
    let payload = payloadObj.q
      ? { ...payloadObj }
      : { ...payloadObj, sort: 'created_at', order: 'desc' };
    if (!payloadObj.q) {
      delete payload.q;
    }

    if (isCurrentActionAssign) {
      payload['exclude_user_ids[]'] = [props.currentUserID];
    }

    isLoadMore ? setLoadingMoreUsers(true) : setInitialDataLoading(true);
    setIsLoadMoreCompleted(false);
    showAllFiltersBlock && setShowAllFiltersBlock(false);

    const { card, isUserAdmin, isUserGroupLeader, isUserManagerWithReportees } = props;

    if (!(isUserAdmin && Permissions.has('ADMIN_ONLY'))) {
      if (isCurrentActionAssign) {
        payload = getPayloadForAssignAction({ payload, card });
      } else if (isCurrentActionShare) {
        payload = getPayloadForShareAction({
          payload,
          card,
          isUserGroupLeader,
          isUserManagerWithReportees
        });
      }
    }

    const _customFieldsList = translatedFields.length ? translatedFields : customFieldsList;
    const customFields = _customFieldsList.map(b => b.abbreviation).join(',');
    payload.fields = `id,handle,avatarimages,name,is_following,custom_fields(${customFields}),custom_fields,${
      showEmail ? 'email' : ''
    }`;
    searchForUsers(payload)
      .then(response => {
        isLoadMore && setIsLoadMoreCompleted(response.users.length < payload.limit);
        isCurrentActionAssign &&
          props.dispatch(multiActions.addSearchedUsersCountForAssign(response.total));
        setTotalUsersCount(response.total);
        // check if current user exists in response.users
        if (isCurrentActionAssign && response.users.find(({ id }) => id == props.currentUserID)) {
          //set TotalUsersCount = response.total - 1(currentUser);
          setTotalUsersCount(response.total - 1);
          //removing currentUser from search result
          response.users = response.users.filter(({ id }) => id != props.currentUserID);
        }
        userstoShowRef.current = isLoadMore
          ? [...userstoShowRef.current, ...response.users]
          : [...response.users];
        const userIds = response?.users.map(({ id }) => id);
        const cardId = props.card.id;
        if (isCurrentActionAssign) {
          let fetchAllAssignedPayload = {
            content_id: cardId,
            search_type: 'user',
            'user_ids[]': userIds,
            limit: 20,
            offset: 0
          };

          if (MULTI_ASSIGNMENTS_FLOW) {
            fetchAllAssignedPayload['state[]'] = ['assigned', 'started'];
            getCompletedAssignment(cardId, { 'user_ids[]': userIds })
              .then(({ users }) => {
                const allUsers = [
                  ...(contentMultiaction?.assignmentCompletedUsers || []),
                  ...users
                ];
                props.dispatch(multiActions._addCompletedUser(uniqBy(allUsers, 'id')));
              })
              .catch(error => {
                console.error(
                  'Error in getCompletedAssignment in IndividualsView.getCompletedAssignment',
                  error
                );
              });
          }

          props
            .dispatch(multiActions._fetchAllAssigned(fetchAllAssignedPayload, 'user'))
            .then(() => {
              setUsersToShow(userstoShowRef.current);
              setCurrentFetchCount(response.users.length);
              setInitialDataLoading(false);
              isLoadMore && setLoadingMoreUsers(false);
              if (props.isRecurringAssignmentChecked) {
                props.dispatch(multiActions._clearAssignedState(TABS().INDIVIDUALS));
              }
            })
            .catch(e => {
              setInitialDataLoading(false);
              console.error('Error in _fetchAllAssigned in IndividualsView.getUserSearchResult', e);
            });
        } else {
          const sharedPayload = {
            id: cardId,
            search_type: 'user',
            'user_ids[]': userIds,
            limit: 20,
            offset: 0
          };
          props
            .dispatch(multiActions.fetchShared(sharedPayload, 'user'))
            .then(() => {
              setUsersToShow(userstoShowRef.current);
              setCurrentFetchCount(response.users.length);
              setInitialDataLoading(false);
              isLoadMore && setLoadingMoreUsers(false);
            })
            .catch(e => {
              setInitialDataLoading(false);
              console.error('Error in fetchSharedin IndividualsView.getUserSearchResult', e);
            });
        }
      })
      .catch(e => {
        console.error('Error in IndividualsView.getUserSearchResult', e);
      });
  };

  //Input Search
  const handleSearch = str => {
    searchTermRef.current = str;
    setSearchTerm(str);
  };

  const handleSearchDebounce = () => {
    searchQuery.current.offset = 0;
    const selectedFilters = filtersList.filter(obj => obj.selectedOptions?.length);
    getPreviewList(selectedFilters, searchValue, searchTerm);
  };

  const getPreviewList = (filterValue, searchingValue, searchingTerm) => {
    const filterData = {};
    let blank_custom_fields = [];
    const searchQueryParam = searchingTerm;
    setSearchValue(searchingValue);

    userstoShowRef.current = [];
    setUsersToShow(userstoShowRef.current);

    filteredData = {
      custom_fields: JSON.stringify(getSelectedFilters(filterValue) || []),
      'names_for_filtering[]': filterData[FILTERING_VALUE] || [],
      'missing_custom_fields[]': blank_custom_fields
    };

    searchQuery.current.offset = 0;
    searchQuery.current.q = '';

    if (searchQueryParam) {
      searchQuery.current.q = searchQueryParam;
    }
    const payload = {
      ...searchQuery.current,
      ...filteredData
    };
    getUserSearchResult(payload);
  };

  const handleScroll = e => {
    e.persist();
    if (isLoadMoreCompleted) return;
    throlledScrollFunc(e);
  };

  const throlledScrollFunc = throttle(
    e => {
      let scrollHeight = e.target.scrollHeight;
      e.target.scrollWidth > e.target.clientWidth &&
        (scrollHeight = e.target.scrollHeight + (e.target.offsetWidth - e.target.clientWidth));
      if (
        Math.trunc(e.target.offsetHeight + e.target.scrollTop) === Math.round(scrollHeight) ||
        Math.ceil(e.target.offsetHeight + e.target.scrollTop) === Math.round(scrollHeight) ||
        (Math.round(e.target.offsetHeight + e.target.scrollTop) === Math.round(scrollHeight) &&
          currentFetchCount === searchQuery.current.limit)
      ) {
        searchQuery.current.offset += searchQuery.current.limit;

        let customFieldsToPassInApi = [];
        for (const filterTypeObj of filtersList) {
          const requestObj = getRequestObj(filterTypeObj);
          if (requestObj.values.length) {
            customFieldsToPassInApi.push(requestObj);
          }
        }
        filteredData = {
          custom_fields: JSON.stringify(customFieldsToPassInApi || [])
        };
        const usersPayload = { ...searchQuery.current, q: searchTermRef.current, ...filteredData };
        getUserSearchResult(usersPayload, true);
      }
    },
    150,
    { leading: false }
  );

  const filterSelectedUsers = usersList => {
    const allUsers = [...selectedUsers, ...usersList];
    return uniqBy(allUsers, 'id');
  };

  const toggleAllCheckboxes = () => {
    const assignedUsers = contentMultiaction?.allAssignedUsers || [];
    const sharedUsers = currentCard?.usersWithAccess;

    let users = !!selectedUsers.length
      ? filterSelectedUsers([...selectedUsers, ...usersToShow])
      : filterSelectedUsers(usersToShow);

    let currentUsers = isCurrentActionAssign ? assignedUsers : sharedUsers;

    props?.dispatch(
      multiActions?.toggleIndividuals(isCheckedAll, currentUsers, users, props.currentAction)
    );
  };

  function checkAllState() {
    let users = usersToShow;
    let userDetails = map(users, 'id') || [];
    let addedUserDetails = map(contentMultiaction?.actionWithUsers || [], 'id');

    let removedAssignUserIds = map(contentMultiaction?.allAssignedUsers || [], 'id');
    let removedShareIds = currentCard.usersWithAccess ? map(currentCard.usersWithAccess, 'id') : [];

    let allAssigned = [...addedUserDetails, ...removedAssignUserIds];
    let allAdded = [...addedUserDetails, ...removedShareIds];

    let xorAssigned = xor(userDetails, allAssigned);
    let xorShared = xor(userDetails, allAdded);

    if (isCurrentActionAssign && users.length) {
      setIsCheckedAll(
        !contentMultiaction?.removeUsersFromList?.length &&
          !(xorAssigned.length && intersection(userDetails, xorAssigned).length)
      );
    } else if (isCurrentActionShare && users.length) {
      setIsCheckedAll(
        !contentMultiaction?.removeUsersFromList?.length &&
          !(xorShared.length && intersection(userDetails, xorShared).length)
      );
    }
  }

  const removeSelectedUser = (user, card, isSearch) => {
    props.dispatch(
      multiActions._removeUser(user, card, props.assignments, props.currentAction, isSearch)
    );
  };

  const onRemoveFilterTagClicked = ele => {
    const { filterType, optionValue } = getFilterTypeAndValuefromTagsId(ele);
    const list = removeSelectedOption(filtersList, filterType, optionValue);
    const allEmptySelectedOptions = filtersList.every(obj => obj.selectedOptions.length === 0);

    // Clearing all filters while keeping 'Select all users' checkbox selected prevents users from unchecking it, hence, it's necessary to clear all filters
    applyFilterCB(list, allEmptySelectedOptions);
  };

  const onResetIconClicked = () => {
    const list = emptySelectedOptionsArr(filtersList);
    applyFilterCB(list, true);
  };

  const applyFilterCB = (newList, isReset = false) => {
    searchQuery.current.offset = 0;
    const list = [...filtersList];
    newList.map(newListObj => {
      const listObj = list.find(obj => obj.type === newListObj.type);
      listObj.selectedOptions = newListObj.selectedOptions;
    });
    setFiltersList(list);
    setApplyFilters(true);
    isReset && isSelectAllFilteredUsersChecked && setIsSelectAllFilteredUsersChecked(false);
  };

  // We need this button in DOM irrespective of loading state to maintain focus management
  // When filters are applied in AllFiltersDropdown, focus returns to this button
  const shouldDisplayFiltersButton =
    (areFiltersSelected(filtersList) || !initialDataLoading) && customHeaders?.length > 0;

  return (
    <div className="indivisuals-tab" aria-live="off">
      <div className="justflex filterflex">
        <SearchInput
          value={searchTerm}
          onSearch={handleSearch}
          aria-label={translatr('web.common.main', 'SearchUsers')}
          placeholder={translatr('web.common.main', 'SearchUsers')}
          maxLen={50}
        />
      </div>

      <div className="all-filters-section-wrapper relative" aria-live="off">
        {/* aria-live='off' prevents screen reader announcements on re-renders since fetching table data trigger frequent updates */}
        <Stack className="align-items-center" spacing="small">
          {shouldDisplayFiltersButton && (
            <button
              className="all-filters-label font-size-xl"
              onClick={() => setShowAllFiltersBlock(show => !show)}
              ref={allFiltersButtonRef}
              aria-expanded={showAllFiltersBlock}
            >
              {translatr('web.common.main', 'AllFilters')}
              <i className="icon-new-filter" />
            </button>
          )}

          {/* SelectAllFilteredUsersCheckBox should depend on !initialDataLoading too */}
          {!initialDataLoading &&
            MULTI_ASSIGNMENTS_FLOW &&
            areFiltersSelected(filtersList) &&
            isCurrentActionAssign && (
              <SelectAllFilteredUsersCheckBox
                onChangeHandler={selectAllFilteredUsersCheckBoxHandler}
                isSelectAllFilteredUsersChecked={isSelectAllFilteredUsersChecked}
              />
            )}
        </Stack>

        {/* AllFilters block, depends on !initialDataLoading */}
        {!initialDataLoading && showAllFiltersBlock && (
          <AllFilters
            rowsList={filtersList}
            setOptionsOnInitialLoad={setFiltersList}
            closeHandlerCB={setShowAllFiltersBlock}
            onApplyFiltersCB={applyFilterCB}
            allFiltersButtonRef={allFiltersButtonRef}
          />
        )}

        {/* Selected filters section, depends on !initialDataLoading */}
        {!initialDataLoading && areFiltersSelected(filtersList) && (
          <div className="selected-filters-section justflex align-items-center flex-wrap l-padding-sides l-margin-bottom ">
            {filtersList.map(listObj =>
              listObj.selectedOptions.map(ele => (
                <Tags
                  key={`${listObj.type}-${ele}`}
                  id={`${listObj.type}%-%${ele}`}
                  name={ele}
                  cb={onRemoveFilterTagClicked}
                />
              ))
            )}
            <Tooltip message={translatr('web.common.main', 'ClearFilters')} pos="right">
              <button
                className="icon-clear-filter pointer font-size-xxl supporting-text-color"
                onClick={onResetIconClicked}
                aria-label={translatr('web.common.main', 'ClearFilters')}
              />
            </Tooltip>
          </div>
        )}
      </div>

      {initialDataLoading ? (
        <Loading />
      ) : (
        <div onScroll={e => handleScroll(e)}>
          <TableRender
            defaultCustomList={defaultCustomList}
            customHeaders={customHeaders}
            usersToShow={usersToShow}
            isCheckedAll={isCheckedAll || (isSelectAllFilteredUsersChecked && totalUsersCount > 0)}
            toggleAllCheckboxes={toggleAllCheckboxes}
            {...props}
            currentUserID={isCurrentActionAssign ? props.currentUserID : ''}
            isSelectAllFilteredUsersChecked={isSelectAllFilteredUsersChecked}
            usersScrollHandler={handleScroll}
          />
        </div>
      )}

      {loadingMoreUsers && <Loading />}
      {!!usersToShow.length && !initialDataLoading && (
        <div className="row">
          <div className="selection-count text-left" role="status" aria-live="polite">
            {translatr('web.common.main', 'CountOfTotalUsersSelected', {
              total: totalUsersCount,
              count:
                searchQuery.current.q !== ''
                  ? countSelectedFilteredUsers(selectedUsers, usersToShow)
                  : !!selectedUsers?.length
                  ? selectedUsers.length
                  : isCheckedAll || isSelectAllFilteredUsersChecked
                  ? usersToShow?.length
                  : 0
            })}
          </div>
        </div>
      )}
      {/* Separate aria-live region to announce "No matches found" since parent has aria-live="off" */}
      {!usersToShow.length && !initialDataLoading && (
        <span className="sr-only" aria-live="polite">
          {translatr('web.common.main', 'NoMatchesFound')}
        </span>
      )}

      {!!selectedUserTags.length && !initialDataLoading && (
        <>
          <SelectedUserTags
            tagsComp={userParam => (
              <Tags
                id={`${userParam.id}-id`}
                name={truncateText(userParam.name, 32, '...')}
                cb={() => {
                  removeSelectedUser(userParam, contentMultiaction[props.card.id], true);
                }}
                isTranslated
              />
            )}
            users={selectedUserTags}
          />
        </>
      )}

      {isSelectAllFilteredUsersChecked && !initialDataLoading && (
        <SelectedUserTags
          tagsComp={userParam => (
            <DisplayTag
              id={`${userParam.id}-id`}
              tagName={truncateText(userParam.name, 32, '...')}
            />
          )}
          users={usersToShow}
          remainingUsersCount={totalUsersCount - usersToShow.length}
        />
      )}

      {isCurrentActionAssign && !!assignmentCompletedUsersTags.length && MULTI_ASSIGNMENTS_FLOW && (
        <>
          <div className="italic-font m-margin-left completed-assignment-label">
            {translatr('web.common.main', 'TheFollowingUsersHaveAlreadyCompletedTheContent')}
          </div>
          <div className="justflex selected-grp m-padding-sides m-padding-bottom">
            {assignmentCompletedUsersTags.map(user => {
              return (
                <div className="display-grid" key={user.id}>
                  <Tags
                    id={`${user.id}-id`}
                    name={truncateText(user.name, 32, '...')}
                    cb={() => {
                      removeSelectedUser(user, contentMultiaction[props.card.id], true);
                    }}
                  />
                </div>
              );
            })}
          </div>

          {SHOW_COMPLETE_CARD_TYPES.includes(props.card?.cardType) && <NoReassignmentsInfoMsg />}
        </>
      )}

      {MULTI_ASSIGNMENTS_FLOW &&
        isCurrentActionAssign &&
        (!!assignmentCompletedUsersTags.length ||
          (!!selectedUserTags.length && !initialDataLoading)) && (
          <ReassignContentCheckbox
            toggleReassignCompletedContent={toggleReassignCompletedContent}
            isMultiAssignmentsEnabled={MULTI_ASSIGNMENTS_FLOW}
            isRecurringAssignmentChecked={isRecurringAssignmentChecked}
          />
        )}
    </div>
  );
};

IndividualsView.propTypes = {
  isUserAdmin: any,
  assignments: any,
  users: any,
  contentMultiaction: any,
  currentAction: string,
  card: any,
  groupsV2: any,
  getCurrentlyChecked: func,
  configs: object,
  currentUserID: any,
  isUserGroupLeader: bool,
  isUserManagerWithReportees: bool,
  enableShowEmail: bool,
  isMultiAssignmentsEnabled: bool,
  isRecurringAssignmentChecked: bool,
  selectAllFilteredUsersCheckBoxHandler: func,
  setIsSelectAllFilteredUsersChecked: func,
  isSelectAllFilteredUsersChecked: bool,
  filtersList: array,
  setFiltersList: func,
  toggleReassignCompletedContent: func
};

function mapStoreStateToProps(state) {
  const isManagerDashboardEnabled = LD.isManagerDashboardEnabled();

  return {
    isUserAdmin: state.currentUser.get('isAdmin'),
    isUserGroupLeader: state.currentUser.get('isGroupLeader'),
    isUserManagerWithReportees: isManagerDashboardEnabled && state.currentUser.get('hasReporters'),
    contentMultiaction: state.contentMultiaction.toJS(),
    groupsV2: state.groupsV2.toJS(),
    enableShowEmail: state.team.get('config')?.enable_show_email,
    currentUserID: state.currentUser.get('id'),
    isMultiAssignmentsEnabled: !!state.team.get('config')?.multi_assignments_flow
  };
}

export default connect(mapStoreStateToProps)(IndividualsView);
