import React, { useCallback, useEffect, useRef, useState } from 'react';
import { string, bool, any, object, func } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { deleteUsersAssignment } from 'edc-web-sdk/requests/assignments.v2';
import { assignTo, assignRecurringTo } from 'edc-web-sdk/requests/assignments';
import Modal, { <PERSON><PERSON>Header, ModalFooter } from 'centralized-design-system/src/Modals';
import Tab from 'centralized-design-system/src/TabBar/SimpleTabs';
import Checkbox from 'centralized-design-system/src/Checkbox';
import FocusLock from 'react-focus-lock';
import moment from 'moment';
import { map } from 'lodash';
import cx from 'classnames';

import LD from '../../../../app/containers/LDStore';
import * as multiActions from '../../../../app/actions/multiactionActions';
import { Permissions } from '../../../../app/utils/checkPermissions';
import IndividualsView from './Individuals/IndividualsView';
import DirectReports from './directReportsView';
import GroupsView from './GroupsView';
import CreateGroupForMultiaction from './CreateGroupForMultiaction';
import CurrentState from './CurrentState';
import RecurringAssignment from './RecurringAssignment/RecurringAssignment';
import RecurringAssignmentView from './RecurringAssignment/RecurringAssignmentView';
import { truncateText } from '@utils/utils';
import Radio from 'centralized-design-system/src/Radio';
import Tooltip from 'centralized-design-system/src/Tooltip';

import {
  CURRENT_ACTION,
  TABS,
  ASSIGNMENT_CREATION_TABS,
  RECURRING_ASSIGNMENT_EXCLUDED_CARD_TYPES,
  GROUPASSIGNMENTOPTIONS,
  ASSIGNMENTOPTION,
  GROUP_ASSIGNMENT_END_DATE_FAILURE_MESSAGE
} from './constant';

import { getCardTypeLabel, isNonUgcPrivateNonRestrictedContent } from '@utils/smartCardUtils';
import { getTabs, getFormattedDate, formatMessage } from './utils';
import RecurringAssignmentToggle from './RecurringAssignment/RecurringAssignmentToggle';
import { useMultiactionContext } from './context/MultiactionDataProvider';
import withMultiActionDataProvider from './hoc/withMultiActionDataProvider';

import {
  AssignModalDatesSection,
  AssignmentType,
  FooterActions,
  MessageAreaInput,
  PrivateContentAlert
} from './components';
import getPayload from './helpers/getPayload';
import { getSelectedFilters } from './Individuals/IndividualsViewUtils';

// Keep Me (stylesheet) at the end 😉
import './MultiactionModal.scss';

const MultiactionsModal = props => {
  const { card, currentAction, onClose, onSuccess = () => {} } = props;

  const {
    activeTab,
    setActiveTab,
    setButtonPending,

    startDate,
    setStartDate,
    endDate,
    setEndDate,

    dueDate,
    setDueDate,
    setStartDatePlaceholder,
    setDuedatePlaceholder,
    setEndDatePlaceholder,

    isRecurringAssignmentChecked,
    setIsRecurringAssignmentChecked,
    reassignmentFrequency,
    allowAssignmentConfig,
    selectedAssignmentType,

    areaInputMsg,
    setAreaInputMsg,

    enableRecurringAssignment,
    currentUser,

    dispatch,
    contentMultiaction,
    toast,
    shareCardWith,
    markAssignmentCountToBeUpdated,
    clearCardState,

    isMultiAssignmentsEnabled
  } = useMultiactionContext();

  const cardType = card.cardType;
  const tabs = getTabs({ currentAction, currentUser, enableRecurringAssignment, cardType });

  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [userListForNewGroup, setUserListForNewGroup] = useState(0);
  const [notifyUsers, setNotifyUsers] = useState(true);

  const [createGroup, setCreateGroup] = useState(false);
  const [dataCleared, setDataCleared] = useState(false);

  const [filtersList, setFiltersList] = useState([]);
  const [isSelectAllFilteredUsersChecked, setIsSelectAllFilteredUsersChecked] = useState(false);
  const [
    isDoNotReassignCompletedContentChecked,
    setisDoNotReassignCompletedContentChecked
  ] = useState(false);
  const [assignmentOption, setAssignmentOption] = useState(ASSIGNMENTOPTION.BOTH);
  const [disableAssignButton, setDisableAssignButton] = useState(false);

  const [selectedDay, setSelectedDay] = useState({
    startDate: null,
    dueDate: null
  });

  const [assignedstate, setAssignedstate] = useState({});
  const firstTabRef = useRef(null);

  const closeModalOnESC = e => {
    if (e.keyCode == 27) {
      onClose(e);
    }
  };

  useEffect(() => {
    // 5000ms delay ensures that screen readers announce the dialog title and close button first
    // before moving focus to the first tab, improving the accessibility experience
    const timer = setTimeout(() => {
      if (firstTabRef.current) {
        firstTabRef.current.focus();
      }
    }, 5000);
    window.addEventListener('keydown', closeModalOnESC);

    return () => {
      window.removeEventListener('keydown', closeModalOnESC);
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    const payload = { limit: 15, offset: 0, full_response: true };
    const usersPayload = { ...payload, ...{ sort: 'first_name', order: 'asc' } };

    dispatch(multiActions._fetchCard(card?.id))
      .then(() => {
        // list of groups for sharing -> writable groups of a user
        // list of groups for assigning -> groups the user is a leader in
        const groupKey = currentAction + 'Group';

        if (!(contentMultiaction && contentMultiaction[groupKey]?.length > 0)) {
          const payloadGroups = { limit: 15, offset: 0 };

          if (currentUser.isAdmin && Permissions.has('ADMIN_ONLY')) {
            dispatch(multiActions._fetchOrgGroups(payloadGroups, currentAction));
          } else {
            if (currentAction === CURRENT_ACTION.ASSIGN) {
              payloadGroups['role'] = 'admin';
              usersPayload['only_from_my_teams'] = true;
            } else {
              payloadGroups['writables'] = true;
            }
            dispatch(multiActions._fetchGroups(payloadGroups, currentAction));
          }
        }
      })
      .catch(error => {
        console.error(
          `An error has occurred while processing multiActions._fetchCard in Multiaction modal: ${error}`
        );
      });
  }, []);

  const setFocusOnSourceElement = useCallback(() => {
    let ele = null;
    switch (currentAction) {
      case CURRENT_ACTION.SHARE:
        ele = document.getElementById(`card-share-${card.id}`);
        break;
      case CURRENT_ACTION.ASSIGN:
        ele = document.getElementById(`card-insight-${card.id}`);
        break;
      default:
        break;
    }
    ele?.focus();
  }, [card.id, currentAction]);

  const selectAllFilteredUsersCheckBoxHandler = useCallback(() => {
    setIsSelectAllFilteredUsersChecked(v => !v);
    setCreateGroup(false);
    dispatch(multiActions.clearNewlySelectedUsers());
  }, []);

  const _openGroupModal = () => {
    if (
      activeTab === TABS().INDIVIDUALS ||
      activeTab === TABS().DIRECT_REPORTS ||
      activeTab === TABS().INDIRECT_REPORTS
    ) {
      let addedUsers = contentMultiaction?.actionWithUsers || [];
      if (contentMultiaction?.searchedUsers) {
        addedUsers = addedUsers.concat(contentMultiaction?.searchedUsers);
      }
      const usersArray = map(addedUsers, 'id') || [];
      setUserListForNewGroup(usersArray);
    }
    setShowCreateGroupModal(!dataCleared);
  };

  const _submitData = async data => {
    setButtonPending(true);
    const _startDate =
      activeTab === TABS().GROUPS
        ? assignmentOption === ASSIGNMENTOPTION.EXISTING || enableRecurringAssignment
          ? getFormattedDate(startDate)
          : ''
        : getFormattedDate(startDate);
    const _dueDate =
      activeTab === TABS().GROUPS
        ? assignmentOption === ASSIGNMENTOPTION.EXISTING || enableRecurringAssignment
          ? getFormattedDate(dueDate)
          : ''
        : getFormattedDate(dueDate);
    const _endDate = getFormattedDate(endDate);

    const message = areaInputMsg || '';
    let successMessage = '';

    let payload = getPayload({
      message,
      data,
      currentAction,
      notifyUsers,
      activeTab,
      selectedAssignmentType
    });

    if (Object.keys(payload).length > 0) {
      if (currentAction === CURRENT_ACTION.SHARE) {
        shareCardWith({
          card,
          payload,
          onClose,
          onSuccess,
          setFocusOnSourceElement
        });
      } else if (currentAction === CURRENT_ACTION.ASSIGN) {
        if (activeTab === TABS().CURRENTLY_ASSIGNED) {
          if (payload.hasOwnProperty('assignmentPriority')) {
            delete payload.assignmentPriority;
          }
          deleteUsersAssignment(card.id, payload)
            .then(async () => {
              dispatch(multiActions._clearState(card));
              setFocusOnSourceElement();
              markAssignmentCountToBeUpdated();

              successMessage = translatr(
                'web.smartcard.multiaction-modal',
                'AssignmentUpdateIsInProgress'
              );
              toast(successMessage, 'success');

              onClose();
            })
            .catch(() => {
              let msg = translatr(
                'web.smartcard.multiaction-modal',
                'ErrorOccuredWhileDeleteAssigning'
              );
              toast(msg, 'error');
            });
          return;
        }

        try {
          if (isRecurringAssignmentChecked) {
            const recurringPayload = {
              format: 'json',
              recurring_assignment: {
                card_id: data.currentCardId,
                recurring_assignment_type: payload.assignType,
                assignment_frequency: reassignmentFrequency,
                due_date: getFormattedDate(dueDate),
                start_date: getFormattedDate(startDate),
                ...(_endDate && _endDate !== '' && { end_date: _endDate }),
                team_ids: payload.team_ids,
                assignee_ids: payload.assignee_ids,
                skip_notifications: payload.skipNotifications,
                assignment_priority: payload.assignmentPriority,
                custom_message: message,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                ...(isSelectAllFilteredUsersChecked &&
                  getSelectedFilters(filtersList) && {
                    assign_to_all_filtered_users: isSelectAllFilteredUsersChecked,
                    custom_fields: getSelectedFilters(filtersList)
                  })
              }
            };
            await assignRecurringTo(recurringPayload);
          } else {
            let assignmentValues = {
              selectedStartDay:
                assignmentOption !== ASSIGNMENTOPTION.EXISTING ? selectedDay.startDate : null,
              selectedDueDay:
                assignmentOption !== ASSIGNMENTOPTION.EXISTING ? selectedDay.dueDate : null,
              assignmentOption: activeTab === TABS().GROUPS ? assignmentOption : null
            };
            await assignTo(
              data.currentCardId,
              'Card',
              payload.assignType,
              payload.team_ids,
              payload.assignee_ids,
              _dueDate,
              message,
              payload.selfAssign,
              _startDate,
              assignmentValues,
              payload.excludeUsers,
              payload.assignmentPriority,
              payload.skipNotifications,
              isSelectAllFilteredUsersChecked,
              getSelectedFilters(filtersList),
              !isDoNotReassignCompletedContentChecked,
              isMultiAssignmentsEnabled,
              _endDate
            );
          }

          setButtonPending(false);
          clearCardState(card);
          markAssignmentCountToBeUpdated();
          setFocusOnSourceElement();

          if (payload.assignee_ids.length) {
            const type = getCardTypeLabel(data[data.currentCardId]);
            let assignee = [...(data.actionWithUsers || []), ...(data.searchedUsers || [])];
            let first_user_name = assignee[0].fullName ? assignee[0].fullName : assignee[0].name;

            if (assignee.length > 1) {
              successMessage = formatMessage({
                action: 'assigned',
                name: truncateText(first_user_name, 32, '...'),
                count: assignee.length - 1,
                isGroup: false,
                cardType: translatr('web.common.main', type) || type
              });
            } else {
              successMessage = translatr(
                'web.smartcard.multiaction-modal',
                'YouHaveAssignedEntityTo',
                {
                  type: translatr('web.common.main', type) || type,
                  name: truncateText(first_user_name, 32, '...')
                }
              );
            }
          } else {
            successMessage = translatr(
              'web.common.main',
              'ContentAssignmentIsInProgressAndWillBeCompletedShortly'
            );
          }
          toast(successMessage, 'success', false);
          onSuccess();
          onClose();
        } catch (errWhileAssigning) {
          let msg = '';
          if (errWhileAssigning?.message === GROUP_ASSIGNMENT_END_DATE_FAILURE_MESSAGE) {
            msg = translatr(
              'web.smartcard.multiaction-modal',
              'ErrorGroupAssignmentEndDateAfterArchiveDate'
            );
          } else {
            msg = translatr('web.smartcard.multiaction-modal', 'ErrorOccuredWhileAssigningCard');
          }
          toast(msg, 'error');
          console.error(`Error while assigning in MultiactionsModal: ${errWhileAssigning}`);
        }
      }
    } else {
      const msg = translatr(
        'web.smartcard.multiaction-modal',
        activeTab === TABS().CURRENTLY_ASSIGNED ? 'NothingHasBeenChanged' : 'NoUserForCurrentFilter'
      );

      toast(msg, 'info', false);
    }
  };

  const toggleRecurringAssignment = val => {
    const isChecked = val?.target?.checked;
    setIsRecurringAssignmentChecked(isChecked);

    if (isChecked) {
      const { allAssignedUsers, allAssignedTeams } = contentMultiaction;

      if (
        [TABS().INDIVIDUALS, TABS().DIRECT_REPORTS, TABS().INDIRECT_REPORTS].includes(activeTab)
      ) {
        setAssignedstate({ ...assignedstate, allAssignedUsers: [...allAssignedUsers] });
      } else if (activeTab === TABS().GROUPS) {
        setAssignedstate({ ...assignedstate, allAssignedTeams: [...allAssignedTeams] });
      }
      dispatch(multiActions._clearAssignedState(activeTab));
    } else {
      const { actionWithUsers, actionWithGroups } = contentMultiaction;
      dispatch(
        multiActions?.toggleIndividuals(
          null,
          assignedstate?.allAssignedUsers || [],
          actionWithUsers
        )
      ); // To remove the already assigned users from the selected list
      dispatch(
        multiActions?.toggleDiffGroups(assignedstate?.allAssignedTeams || [], actionWithGroups)
      ); // To remove the already assigned groups from the selected list
      dispatch(multiActions._setAssignedState(assignedstate, activeTab));
    }
  };

  const toggleReassignCompletedContent = e => {
    setisDoNotReassignCompletedContentChecked(e?.target?.checked);
  };

  const getDateChangeHandler = (setDate, setDatePlaceHolder) => date => {
    setDate(date ? moment(date) : '');
    setDatePlaceHolder(date ? null : translatr('web.common.main', 'SelectDate'));
  };

  const onTabClick = tabClicked => {
    const hasTextArea = [
      TABS().INDIVIDUALS,
      TABS().GROUPS,
      TABS().DIRECT_REPORTS,
      TABS().INDIRECT_REPORTS
    ].includes(tabClicked);
    if (activeTab !== tabClicked && hasTextArea) {
      setActiveTab(tabClicked);
      setAreaInputMsg('');
    } else {
      setActiveTab(tabClicked);
    }

    if (isSelectAllFilteredUsersChecked) setIsSelectAllFilteredUsersChecked(false);
  };

  const currentActionButton = () => {
    _submitData(contentMultiaction);
    setDataCleared(prevDataCleared => !prevDataCleared);
  };

  const onModalClose = () => {
    clearCardState(contentMultiaction[contentMultiaction.currentCardId]);
    setDataCleared(!dataCleared);
    onClose();
  };

  const isCurrentlyActivatedTab = [
    TABS().INDIVIDUALS,
    TABS().GROUPS,
    TABS().DIRECT_REPORTS,
    TABS().INDIRECT_REPORTS
  ].includes(activeTab);

  let showCurrentState =
    (currentUser.isAdmin && Permissions.has('ADMIN_ONLY')) ||
    (LD.isManagerDashboardEnabled() && currentUser.hasReporters) ||
    currentUser.isGroupLeader;

  const checkCreateGroupModal =
    createGroup &&
    [TABS().INDIVIDUALS, TABS().DIRECT_REPORTS, TABS().INDIRECT_REPORTS].includes(activeTab)
      ? _openGroupModal
      : currentActionButton;

  const modalTitle =
    currentAction === CURRENT_ACTION.ASSIGN
      ? translatr('web.common.main', 'AssignContentTo ')
      : translatr('web.common.main', 'ShareContentWith ');

  const notifyMessage =
    activeTab === TABS().GROUPS
      ? translatr('web.common.main', 'NotifyGroupMembers')
      : translatr('web.common.main', 'NotifyIndividuals');

  const handleChange = e => {
    setAssignmentOption(e.target.value);
  };

  const showSecondaryManager = global?.__edOrgData?.configs?.find(
    f => f.name === 'md_configuration'
  )?.value?.show_secondary_manager;

  const showGroupPreferences =
    activeTab === TABS().GROUPS &&
    currentAction === CURRENT_ACTION.ASSIGN &&
    !isRecurringAssignmentChecked;

  return !showCreateGroupModal ? (
    <div onClick={e => e.stopPropagation()} role="presentation">
      <Modal ariaLabelledBy="multiaction-modal-title">
        <FocusLock>
          <ModalHeader title={modalTitle} onClose={onModalClose} id="multiaction-modal-title" />
          <div className="multiactions-modal">
            <Tab OnTabClickCB={onTabClick} isTranslated={true} firstTabRef={firstTabRef}>
              {tabs.map((tab, index) => {
                const tabTitle = JSON.stringify(tab).replaceAll('"', '');
                return (
                  <Tab.TabPane key={`Tab-${index}`} tab={tabTitle}>
                    {/* Wrap tab content in div with aria-live="off" to prevent screen reader
                        from automatically announcing all content when the tab is selected.
                        This improves the user experience by reducing excessive announcements.
                    */}
                    <div aria-live="off">
                      {currentAction === CURRENT_ACTION.ASSIGN &&
                        enableRecurringAssignment &&
                        !RECURRING_ASSIGNMENT_EXCLUDED_CARD_TYPES.includes(cardType) &&
                        ASSIGNMENT_CREATION_TABS().includes(activeTab) && (
                          <RecurringAssignmentToggle
                            toggleRecurringAssignment={toggleRecurringAssignment}
                          />
                        )}

                      {tabTitle === TABS().INDIVIDUALS && (
                        <IndividualsView
                          {...props}
                          currentTab={tabTitle}
                          currentAction={currentAction}
                          isRecurringAssignmentChecked={isRecurringAssignmentChecked}
                          isSelectAllFilteredUsersChecked={isSelectAllFilteredUsersChecked}
                          setIsSelectAllFilteredUsersChecked={setIsSelectAllFilteredUsersChecked}
                          selectAllFilteredUsersCheckBoxHandler={
                            selectAllFilteredUsersCheckBoxHandler
                          }
                          filtersList={filtersList}
                          setFiltersList={setFiltersList}
                          toggleReassignCompletedContent={toggleReassignCompletedContent}
                        />
                      )}

                      {tabTitle === TABS().DIRECT_REPORTS && (
                        <DirectReports
                          isIndirectReports={false}
                          currentAction={currentAction}
                          currentTab={tabTitle}
                          {...props}
                          contentMultiaction={contentMultiaction}
                          isRecurringAssignmentChecked={isRecurringAssignmentChecked}
                          toggleReassignCompletedContent={toggleReassignCompletedContent}
                        />
                      )}

                      {tabTitle === TABS().INDIRECT_REPORTS && showSecondaryManager && (
                        <DirectReports
                          isIndirectReports={true}
                          currentAction={currentAction}
                          currentTab={tabTitle}
                          {...props}
                          contentMultiaction={contentMultiaction}
                          isRecurringAssignmentChecked={isRecurringAssignmentChecked}
                          toggleReassignCompletedContent={toggleReassignCompletedContent}
                        />
                      )}

                      {tabTitle === TABS().GROUPS && (
                        <GroupsView
                          currentAction={currentAction}
                          currentTab={tabTitle}
                          dataCleared={dataCleared}
                          isNonUgcPrivateNonRestrictedContent={isNonUgcPrivateNonRestrictedContent(
                            card
                          )}
                          isRecurringAssignmentChecked={isRecurringAssignmentChecked}
                          toggleReassignCompletedContent={toggleReassignCompletedContent}
                          setDisableAssignButton={setDisableAssignButton}
                          {...props}
                        />
                      )}
                      {showCurrentState &&
                        [TABS().CURRENTLY_SHARED, TABS().CURRENTLY_ASSIGNED].includes(tabTitle) && (
                          <CurrentState
                            {...props}
                            currentAction={currentAction}
                            currentTab={tabTitle}
                          />
                        )}

                      {tabTitle === TABS().RECURRING_ASSIGNMENT && (
                        <RecurringAssignmentView card={card} />
                      )}
                    </div>
                  </Tab.TabPane>
                );
              })}
            </Tab>

            {isCurrentlyActivatedTab && <MessageAreaInput />}
            {showGroupPreferences && (
              <>
                <div className="group-assignment-label">
                  {translatr('web.common.main', 'GroupAssignmentPreferences')}
                  <span className="asterisk">*</span>
                  <Tooltip
                    message={translatr('web.common.main', 'GroupPreferencesTooltip')}
                    ariaLabel={translatr('web.common.main', 'GroupPreferencesTooltip')}
                    pos="right"
                  >
                    <i className="icon-info-circle" />
                  </Tooltip>
                </div>
                <Radio
                  groupName="groupAssignments"
                  items={GROUPASSIGNMENTOPTIONS()}
                  wrapperClass="mt-5 ml-16"
                  className="flex-row"
                  onChange={handleChange}
                />
              </>
            )}

            {allowAssignmentConfig &&
              isCurrentlyActivatedTab &&
              currentAction !== CURRENT_ACTION.SHARE && <AssignmentType />}

            {currentAction === CURRENT_ACTION.ASSIGN &&
              ASSIGNMENT_CREATION_TABS().includes(activeTab) &&
              (!isRecurringAssignmentChecked ? (
                <AssignModalDatesSection
                  getDateChangeHandler={getDateChangeHandler}
                  assignmentOption={assignmentOption}
                  activeTab={activeTab}
                  selectedDay={selectedDay}
                  setSelectedDay={setSelectedDay}
                />
              ) : (
                <RecurringAssignment
                  handleStartDateChange={getDateChangeHandler(
                    setStartDate,
                    setStartDatePlaceholder
                  )}
                  handleDueDateChange={getDateChangeHandler(setDueDate, setDuedatePlaceholder)}
                  handleEndDateChange={getDateChangeHandler(setEndDate, setEndDatePlaceholder)}
                />
              ))}
            <div>
              {isCurrentlyActivatedTab && (
                <div className="text-left s-margin-top skip-users">
                  <Checkbox
                    isTranslated={true}
                    checked={notifyUsers}
                    label={notifyMessage}
                    onChange={e => setNotifyUsers(e.target.checked)}
                  />
                </div>
              )}

              {Permissions.has('CREATE_GROUP') &&
                (activeTab === TABS().INDIVIDUALS ||
                  activeTab === TABS().DIRECT_REPORTS ||
                  activeTab === TABS().INDIRECT_REPORTS) && (
                  <div className="text-left s-margin-top create-group-sec skip-users marg">
                    <Checkbox
                      isTranslated={true}
                      checked={createGroup}
                      label={translatr('web.common.main', 'CreateNewGroupWithSelectedIndividuals')}
                      onChange={e => setCreateGroup(e.target.checked)}
                      disabled={isSelectAllFilteredUsersChecked}
                      className="create-group-checkbox"
                    />
                    <div
                      className={cx('group-info supporting-text', {
                        'disabled-color': isSelectAllFilteredUsersChecked
                      })}
                    >
                      {translatr(
                        'web.smartcard.multiaction-modal',
                        currentAction === CURRENT_ACTION.ASSIGN
                          ? 'AfterAssigningContentWillBeAskedForGrpDetails'
                          : 'AfterSharingContentWillBeAskedForGrpDetails'
                      )}
                    </div>
                  </div>
                )}
            </div>

            {[TABS().INDIVIDUALS, TABS().GROUPS].includes(activeTab) && !card.isPublic && (
              <PrivateContentAlert currentAction={currentAction} />
            )}
          </div>
          <ModalFooter>
            <FooterActions
              onModalClose={onModalClose}
              isCurrentlyActivatedTab={isCurrentlyActivatedTab}
              actionButtonName={translatr(
                'web.common.main',
                currentAction === CURRENT_ACTION.ASSIGN ? 'Assign' : 'Share'
              )}
              checkCreateGroupModal={checkCreateGroupModal}
              isSelectAllFilteredUsersChecked={isSelectAllFilteredUsersChecked}
              shouldDisabledAssignButton={disableAssignButton}
            />
          </ModalFooter>
        </FocusLock>
      </Modal>
    </div>
  ) : (
    <CreateGroupForMultiaction
      setShowCreateGroupModal={setShowCreateGroupModal}
      card={card}
      currentAction={currentAction}
      closeModal={onClose}
      userListForNewGroup={userListForNewGroup}
      setFocusOnSourceElement={setFocusOnSourceElement}
      notifyUsers={notifyUsers}
      isDoNotReassignCompletedContentChecked={isDoNotReassignCompletedContentChecked}
    />
  );
};

MultiactionsModal.propTypes = {
  currentAction: string,
  contentMultiaction: any,
  currentUser: any,
  card: object,
  dispatch: any,
  modal: any,
  assignments: object,
  groupsV2: object,
  onClose: func,
  enableRecurringAssignment: bool,
  toast: func,
  onSuccess: func
};

export default withMultiActionDataProvider(MultiactionsModal);
