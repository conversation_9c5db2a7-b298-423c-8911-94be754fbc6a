import React, { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { <PERSON><PERSON>Header, ModalFooter } from 'centralized-design-system/src/Modals';
import { close } from 'actions/modalActions';
import FocusLock from 'react-focus-lock';
import Table from 'centralized-design-system/src/Table';
import './MatchingSkillsModal.scss';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { getUserSkills, changeReceiveSkillsLoadedStatus } from '../../../actions/usersActions';
import Loader from '@components/Loader/Loader';
import {
  mapSkillsWithDefaultLevel,
  sortSkillsByUserSkills
} from '@components/modals/SkillsModal/helpers';
import { getEcsOpportunitiesInfo } from '../../../actions/teamActions';
import { getLevelByDecimal } from 'centralized-design-system/src/Utils/proficiencyLevels';
import { shouldAllowToAddSkillsToPassport } from 'opportunity-marketplace/util';
import { isMfeEnabled, openSkillFlyout } from '@components/MfeSkillsFlyout';
import { Button } from 'centralized-design-system/src/Buttons';

const onCloseModal = () => {
  return dispatch => {
    dispatch(changeReceiveSkillsLoadedStatus(false));
    dispatch(close());
  };
};

const MatchingSkillsModal = ({
  skills: skillsProvided = [],
  skillsFrom,
  currentUserId,
  isUserSkillsLoaded,
  userSkills,
  currentUserLang,
  getCurrentUserSkills,
  onOpenSkillsModal,
  closeModal,
  hideSkillsAdditionLink,
  showCandidateLevelLabel,
  ecsVacancyConfig,
  getEcsOpportunitiesConfig,
  openedFromHtmlElement
}) => {
  const [loading, setLoading] = useState(true);
  const [skills, setSkills] = useState(skillsProvided);

  const escFunction = e => {
    if (e.key === 'Escape') {
      closeModal();
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  useEffect(() => {
    if (!ecsVacancyConfig) {
      getEcsOpportunitiesConfig();
    }
    getCurrentUserSkills(currentUserId);
  }, []);

  useEffect(() => {
    if (!ecsVacancyConfig) return;
    if (isUserSkillsLoaded) {
      setLoading(false);
    }

    const { detected_skills_level: detectedSkillsLevel } = ecsVacancyConfig;

    const skillsWithDefaultLevel = mapSkillsWithDefaultLevel({
      skills,
      skillsFrom,
      defaultLevel: detectedSkillsLevel,
      currentUserLang
    });

    setSkills(skillsWithDefaultLevel);
  }, [userSkills, ecsVacancyConfig]);

  const tableHeaders = useMemo(
    () => [
      {
        children: <p>{translatr('web.common.main', 'Skill')}</p>
      },
      {
        children: (
          <p>
            {showCandidateLevelLabel
              ? translatr('web.common.main', 'CandidateLevel')
              : translatr('web.common.main', 'YourLevel')}
          </p>
        )
      },
      {
        children: (
          <p>
            {translatr('web.common.main', 'ValueTargetLevel', {
              value: skillsFrom === JOB_TYPE.VACANCY ? omp('tm_job_vacancy') : omp('tm_job_role')
            })}
          </p>
        )
      },
      {
        children: <p>{translatr('web.common.main', 'StatusSkill')}</p>
      }
    ],
    []
  );

  const findYourLevelAndStatus = (id, proficiencyLevelObj) => {
    const userSkillFound = userSkills.find(
      userSkill => id === userSkill?.taxonomyDetails?.topic_id
    );
    const userLevel = getLevelByDecimal(userSkillFound?.rawProficiencyLevel)?.level;
    const skillLevel = getLevelByDecimal(proficiencyLevelObj?.level)?.level;

    const noLevel = -1;
    const userLevelDecimal = parseFloat(userLevel || noLevel);
    const skillLevelDecimal = parseFloat(skillLevel || noLevel);
    const userLevelLabel =
      userSkillFound?.translatedLevel || translatr('web.common.main', 'NaUpperCase');

    const status =
      userLevelDecimal >= skillLevelDecimal
        ? {
            statusIcon: (
              <span className="status-label">
                <i className="card-icon icon-check-circle-light" />
                {translatr('web.common.main', 'OnTarget')}
              </span>
            ),
            statusLabel: 'OnTarget'
          }
        : {
            statusIcon: (
              <span className="status-label disabled">
                <i className="card-icon icon-check-circle-light" />
                {translatr('web.common.main', 'OffTarget')}
              </span>
            ),
            statusLabel: 'OffTarget'
          };

    return [userLevelLabel, status];
  };

  const existingUserSkills = userSkills?.filter(({ taxonomyDetails }) => !!taxonomyDetails);

  const tableRows = useMemo(
    () =>
      sortSkillsByUserSkills(skills, existingUserSkills).map(
        ({ label, name, id, level, proficiencyLevelObj, externalData }) => {
          const [userLevelLabel, status] = findYourLevelAndStatus(id, proficiencyLevelObj);
          return [
            {
              children: !isMfeEnabled() ? (
                <p>{label || name || id}</p>
              ) : (
                <Button
                  color="primary"
                  variant="borderless"
                  padding="small"
                  onClick={() => {
                    openSkillFlyout(externalData);
                  }}
                >
                  {label}
                </Button>
              )
            },
            {
              children: <p>{userLevelLabel}</p>
            },
            {
              children: <p>{proficiencyLevelObj?.label || level}</p>
            },
            {
              children: <p>{status.statusIcon}</p>
            }
          ];
        }
      ),
    [skills, userSkills]
  );

  const onTargetSkills = useMemo(
    () =>
      sortSkillsByUserSkills(skills, existingUserSkills).filter(
        ({ label, name, id, proficiencyLevelObj }) => {
          const [, status] = findYourLevelAndStatus(id, proficiencyLevelObj);
          if (status.statusLabel === 'OnTarget') {
            return label || name || id;
          }
        }
      ),
    [skills, userSkills, isUserSkillsLoaded]
  );

  useEffect(() => {
    if (!hideSkillsAdditionLink) {
      // Add Skill button
      const addSkillsLink = document.createElement('button');
      addSkillsLink.classList.add(
        'ed-btn-v2',
        'ed-btn-primary-v2',
        'ed-btn-primary-borderless-v2',
        'ed-btn-padding-xsmall'
      );
      addSkillsLink.innerText = translatr('web.common.main', 'AddThemToYourSkillsPassport');

      addSkillsLink.onclick = () => {
        const modalOverlay = document.createElement('div');
        modalOverlay.classList.add('ed-skills-dialog-modal-overlay');
        document
          .querySelector('.ed-dialog-modal.scrollable-section')
          ?.parentElement.appendChild(modalOverlay);
        onOpenSkillsModal({ openedFromHtmlElement });
      };

      // Description
      const alreadyHaveSomeSkills = document.createElement('span');
      alreadyHaveSomeSkills.innerText = translatr('web.common.main', 'AlreadyHaveSomeSkills');

      // Sub-header
      const matchingSkillsSubHeader = document.createElement('span');
      matchingSkillsSubHeader.classList.add('matching-skills-sub-header');

      if (shouldAllowToAddSkillsToPassport()) {
        matchingSkillsSubHeader.appendChild(alreadyHaveSomeSkills);
        matchingSkillsSubHeader.appendChild(addSkillsLink);
      }

      document
        .getElementsByClassName('ed-dialog-modal-header')[0]
        .appendChild(matchingSkillsSubHeader);
    }
  }, []);

  return (
    <div className="matching-skills-modal">
      <FocusLock
        onDeactivation={() => {
          openedFromHtmlElement?.focus();
        }}
      >
        <ModalHeader
          title={translatr('web.common.main', 'MatchingSkills')}
          id="matching-skills-header"
          onClose={closeModal}
        />

        <div className="vertical-spacing-large">
          <div className="confirm-msg">
            {loading || !skills.length ? (
              <div className="spinner" role="alert" aria-live="polite">
                <Loader center />
              </div>
            ) : (
              <>
                {hideSkillsAdditionLink && (
                  <h3 className="font-size-xxl font-weight-700 mt-12">
                    {translatr('web.common.main', 'MatchedCandidateSkills', {
                      matchedSkills: onTargetSkills.length,
                      totalSkills: skillsProvided.length
                    })}
                  </h3>
                )}
                <Table headers={tableHeaders} rows={tableRows} />
              </>
            )}
          </div>
        </div>
        <ModalFooter>
          <button
            className="ed-btn ed-btn-neutral"
            onClick={closeModal}
            aria-label={translatr('web.common.main', 'Close')}
          >
            {translatr('web.common.main', 'Close')}
          </button>
        </ModalFooter>
      </FocusLock>
    </div>
  );
};

MatchingSkillsModal.propTypes = {
  skills: PropTypes.array,
  currentUserLang: PropTypes.string,
  skillsFrom: PropTypes.string,
  currentUserId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  isUserSkillsLoaded: PropTypes.bool,
  userSkills: PropTypes.array,
  getCurrentUserSkills: PropTypes.func,
  onOpenSkillsModal: PropTypes.func,
  closeModal: PropTypes.func,
  hideSkillsAdditionLink: PropTypes.bool,
  showCandidateLevelLabel: PropTypes.bool,
  ecsVacancyConfig: PropTypes.object,
  getEcsOpportunitiesConfig: PropTypes.func,
  openedFromHtmlElement: PropTypes.object
};

const mapStateToProps = ({ modal, currentUser, team }) => ({
  currentUserId: modal.get('userId') || currentUser?.get('id'),
  currentUserLang:
    currentUser.get('profile')?.get?.('language') ||
    currentUser.get('profile')?.language ||
    team?.get('config')?.DefaultOrgLanguage ||
    'en',
  skills: modal.get('skills'),
  skillsFrom: modal.get('skillsFrom'),
  isUserSkillsLoaded: currentUser.get('isUserSkillsLoaded'),
  userSkills: currentUser.get('userSkills')?.skills || [],
  onOpenSkillsModal: modal.get('onOpenSkillsModal'),
  hideSkillsAdditionLink: modal.get('hideSkillsAdditionLink') || false,
  showCandidateLevelLabel: modal.get('showCandidateLevelLabel') || false,
  ecsVacancyConfig: team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY],
  openedFromHtmlElement: modal.get('openedFromHtmlElement')
});

const mapDispatchToProps = dispatch => ({
  getCurrentUserSkills: currentUserId => dispatch(getUserSkills(currentUserId)),
  closeModal: () => dispatch(onCloseModal()),
  getEcsOpportunitiesConfig: () => dispatch(getEcsOpportunitiesInfo())
});

export default connect(mapStateToProps, mapDispatchToProps)(MatchingSkillsModal);
