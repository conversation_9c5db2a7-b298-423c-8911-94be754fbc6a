@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

@mixin flx-centered {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin empty-carousel {
  background-color: var(--ed-neutral-7);
  border-radius: var(--ed-border-radius-lg);
  margin: var(--ed-spacing-base) 0px;
  p {
    font-size: var(--ed-font-size-sm) !important;
    color: var(--ed-text-color-supporting) !important;
  }
}

.capitalize {
  text-transform: capitalize;
}

.skills-passport-modal {
  margin: 0 auto;
  font-size: var(--ed-font-size-sm);

  .ed-ui .ed-carousel-container {
    margin: var(--ed-spacing-base) 0px;
  }

  .modal-body {
    padding: 15px 15px 0 15px;
    box-shadow: 0px 7px 9px -7px rgba(0, 0, 0, 0.25);
  }

  .title {
    color: var(--ed-text-color-primary) !important;
    font-size: var(--ed-font-size-base) !important;
    font-weight: var(--ed-font-weight-normal) !important;
    text-transform: capitalize;
  }
  .subtitle {
    color: var(--ed-text-color-supporting) !important;
    font-size: var(--ed-font-size-sm) !important;
    text-transform: capitalize;
  }

  .item-footer-wraper {
    margin-top: var(--ed-spacing-base);
    display: flex;
    flex: 1;
    justify-content: space-between;
  }

  .short-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ed-ui .ed-carousel-container.right-scroll .scroll-btn.right {
    right: -1.19rem;
  }
  .ed-ui .ed-carousel-container.left-scroll .scroll-btn.left {
    left: -1.18rem;
  }

  .skill-carousel-item {
    display: block;
    min-width: 17.5rem;
    font-size: var(--ed-font-size-sm);
    color: var(--ed-text-color-supporting) !important;
    margin-right: var(--ed-spacing-lg);
    padding: var(--ed-spacing-base) !important;
    stroke-width: 1;
    border: var(--ed-border-size-sm) var(--ed-state-disabled-color) solid;
    border-radius: var(--ed-border-radius-lg);

    .icon-container-wrapper {
      @include flx-centered;
      border: var(--ed-border-size-sm) var(--ed-state-disabled-color) solid;
      border-radius: var(--ed-border-radius-lg);
      height: 2.875rem;
      width: 2.875rem;
      stroke-width: 1;

      i {
        color: var(--ed-state-active-color);
        font-size: 1.875rem !important;
        width: auto !important;
        margin: auto !important;
      }

      .check-icon {
        left: -0.8rem;
        top: -0.8rem;
        font-size: 1.375rem !important;
        background: var(--ed-white);
      }
    }

    .name-description-wrapper {
      flex: 1;
      min-width: 0;
      overflow: hidden;

      .name-label {
        color: var(--ed-text-color-primary);
        font-size: var(--ed-font-size-base);
        padding-left: var(--ed-spacing-base);
        text-transform: capitalize;
        &:hover {
          cursor: pointer;
        }
      }

      .description-label {
        padding-left: var(--ed-spacing-base);
        text-transform: capitalize;
      }
    }

    .exp-label {
      flex: 3;
      font-weight: var(--ed-font-weight-semibold);
    }

    .date-label {
      text-align: right;
      flex: 2;
    }
  }

  .badge-carousel-item {
    display: block;
    min-width: 17.5rem;
    font-size: var(--ed-font-size-sm);
    color: var(--ed-text-color-supporting) !important;
    margin-right: var(--ed-spacing-lg);
    padding: var(--ed-spacing-base) !important;
    stroke-width: 1;
    border: var(--ed-border-size-sm) var(--ed-state-disabled-color) solid;
    border-radius: var(--ed-border-radius-lg);

    .verified-badge {
      top: 1.6rem;
      left: 0;
      font-size: 1.375rem;
      color: var(--ed-state-active-color);
    }

    .badge-image {
      @include flx-centered;
      height: 3.75rem;
      width: 3.75rem;
      margin: 2rem auto;
      i {
        color: var(--ed-state-active-color);
        font-size: 3.75rem !important;
        width: auto !important;
        margin: auto !important;
      }
    }
    .exp-label {
      flex: 3;
      font-weight: var(--ed-font-weight-semibold);
    }
    .date-label {
      text-align: right;
      flex: 2;
    }
  }

  .empty-skill {
    @include flx-centered;
    @include empty-carousel;
    min-height: 7.063rem;
  }
  .empty-badges {
    @include flx-centered;
    @include empty-carousel;
    min-height: 13.438rem;
  }
  .empty-certificates {
    @include flx-centered;
    @include empty-carousel;
    min-height: 6.25rem;
  }
}
