@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.become-a-mentor-modal {
  &--encouragement-text {
    display: block;
    color: var(--ed-text-color-primary);
  }

  &--hint-text {
    display: block;
    font-size: var(--ed-font-size-sm);
    color: var(--ed-text-color-supporting);
  }

  &--user-meta-info {
    margin-top: rem-calc(30);

    img {
      width: rem-calc(80);
      height: rem-calc(80);
      border-radius: var(--ed-border-radius-circle);
    }

    &-header-container {
      display: flex;
      align-items: center;
      gap: var(--ed-spacing-sm);

      &-name {
        display: block;
        color: var(--ed-text-color-primary);
        font-size: var(--ed-spacing-xl);
        font-weight: var(--ed-font-weight-semibold);
      }

      &-position {
        display: block;
        color: var(--ed-text-color-supporting);
        font-size: var(--ed-font-size-lg);
      }
    }

    &-footer-container {
      display: flex;
      gap: rem-calc(30);
      margin-top: rem-calc(17);
    }
  }

  &--meta-detail {
    display: flex;

    i {
      font-size: rem-calc(18);
      color: var(--ed-text-color-supporting);
      margin-left: 0 !important;
    }

    a {
      color: var(--ed-text-color-primary);
      text-decoration: underline;
    }

    &-title {
      display: block;
      color: var(--ed-text-color-supporting);
      font-size: var(--ed-font-size-sm);
    }

    &-label {
      display: block;
      color: var(--ed-text-color-primary);
    }
  }

  &--path {
    width: 100%;
    border-top: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
  }

  .has-error {
    .ed-input-title,
    .asterisk {
      color: var(--ed-negative-1);
    }

    #mentorship-description,
    .Select-control {
      border: var(--ed-border-size-sm) solid var(--ed-negative-2) !important;
    }
  }

  .supporting-text {
    color: var(--ed-text-color-supporting) !important;
    padding-top: var(--ed-spacing-base) !important;
  }

  .text-field-meta-group {
    display: flex;
    justify-content: space-between;
    font-size: var(--ed-font-size-sm);

    &.input-error {
      * {
        color: var(--ed-negative-1);
      }
    }
  }

  .ed-dialog-modal-content {
    padding: var(--ed-spacing-xl) rem-calc(65) !important;
  }

  .ed-input-container {
    margin: 0 !important;
    max-width: 100%;
  }

  .ed-input-description {
    display: block;
    color: var(--ed-text-color-supporting) !important;
    font-size: var(--ed-font-size-sm) !important;
  }

  .ed-supporting-text {
    color: var(--ed-text-color-supporting);
  }

  .ckeditor-placeholder.inline {
    top: var(--ed-spacing-3xs) !important;
  }

  .Select-placeholder {
    color: var(--ed-text-color-supporting) !important;
  }

  .Select-input {
    padding-left: 0;
  }

  .ed-input-title {
    margin: 0;
  }

  .ed-single-skill-section {
    .ed-input-title {
      font-size: var(--ed-font-size-base);
      color: var(--ed-text-color-primary);
      font-weight: var(--ed-font-weight-normal);
      margin: 0;
    }

    display: flex;
    flex-direction: column;
    margin-bottom: var(--ed-font-size-lg);
  }
  .ed-mentor-modal-skill-list {
    .ed-tag-container {
      cursor: default !important;
      margin-left: 0;
      margin-right: var(--ed-spacing-2xs);
      padding: var(--ed-spacing-4xs) var(--ed-spacing-base);
    }
  }
  .closable-warning {
    max-width: 100% !important;
    margin-top: var(--ed-spacing-sm);

    & .alert-body {
      line-height: var(--ed-line-height-base);
    }
  }
  .selected-count-text {
    font-size: var(--ed-font-size-sm);
  }
  .disabled-programs-topics-items {
    display: flex;
    flex-direction: column;
  }
}
