import React, { useCallback, useEffect, useState } from 'react';
import { array, bool, func, object, string } from 'prop-types';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import every from 'lodash/every';
import values from 'lodash/values';
import unescape from 'lodash/unescape';
import FocusLock from 'react-focus-lock';
import { SkillsSection } from '@components/SkillsSection';

import {
  createMentorshipProfile,
  OPPORTUNITY_TYPE,
  updateMentorshipProfile
} from 'edc-web-sdk/requests/careerOportunities.v2';
import { getLocationById } from 'edc-web-sdk/requests/hrData.v2';
import { getUserBasicInfo } from 'edc-web-sdk/requests/users.v2';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { Modal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, Mo<PERSON>Header } from 'centralized-design-system/src/Modals';
import { MultiSelectSearch } from 'centralized-design-system/src/Inputs';
import TreeView from 'centralized-design-system/src/TreeView/TreeView';

import { open_v2 as openSnackBar } from 'actions/snackBarActions';
import { close, openBecomeAMentorConfirmationModal } from 'actions/modalActions';
import { transformLanguages } from '@utils/transformLanguages';
import { scrollToFirstError, announceErrorMessage } from '@utils/scrollToFirstError';
import LazyloadComponent from '@components/LazyloadComponent';

import { reportOpportunityPublished } from '@analytics/OmpEventRecorder';
import { getPrograms, getTopics, getDurations, getTypes } from '@actions/mentorProfileActions';

import UserMetaInfo from './components/UserMetaInfo';
import Field from './components/Field';
import { validate } from './helpers';
import { Fields, ROOT_CLASS } from './types';
import './styles.scss';

import { getUserPassport } from 'edc-web-sdk/requests/users';
import {
  isLocationFieldVisible,
  LOCATION_ASSOCIATION,
  LOCATION_FIELDS,
  LOCATION_USAGE_OPTIONS,
  MENTORSHIP_FIELDS,
  STANDARD_FIELDS_TYPES,
  convertDataToTreeView,
  searchPrograms,
  searchTopics,
  searchDurations,
  searchTypes,
  searchAdditionalLanguages,
  showSkills,
  showPrograms,
  showTopics,
  showDescription,
  skillsRequired,
  showDurations,
  showTypes,
  showAdditionalLanguages,
  programsRequired,
  topicsRequired,
  durationsRequired,
  typesRequired,
  additionalLanguagesRequired,
  descriptionRequired,
  getEnabledItems,
  getLanguageDetailsObj
} from 'opportunity-marketplace/helpers';
import { getGenAIConfig } from 'edc-web-sdk/requests/superAdmin';
import {
  withGenAiAssistantContextProvider,
  useGenAiAssistantContext
} from 'centralized-design-system/src/GenAiAssistant';
import { ENTITY } from 'centralized-design-system/src/constants/genAiConstants';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import { getWorkHistories } from 'edc-web-sdk/requests/talentMarket';
import { getWorkHistoryData } from 'opportunity-marketplace/CareerWizard/utils';
import { AreaAlert } from 'centralized-design-system/src/Alerts';
import Loading from 'centralized-design-system/src/Loading';

const Editor = LazyloadComponent(() => import('centralized-design-system/src/Inputs/ckeditor'))();

const skillStyle = {
  multiValue: styles => {
    return {
      ...styles,
      display: 'none'
    };
  }
};

const BecomeAMentorModal = ({
  closeModal,
  currentUserId,
  currentUser,
  editing,
  languages,
  profile,
  showConfirmationModal,
  toast,
  meta,
  onSuccessSaveProfile,
  locationsEnabled,
  locationFieldAssociation,
  locationFieldVisibility,
  dispatch,
  programs,
  topics,
  durations,
  types,
  languageDetails
}) => {
  const navigate = useNavigate();
  const [descriptionLoading, setDescriptionLoading] = useState(false);
  const [integrateGenAIAssistedDescription, setIntegrateGenAIAssistedDescription] = useState(false);
  const [workHistory, setWorkHistory] = useState();
  const [location, setLocation] = useState(meta?.userId?.location);

  const { updateGenAiContext, initializeContextForGenAi } = useGenAiAssistantContext();
  const [programsData, setProgramsData] = useState(null);
  const [additionalLanguagesData, setAdditionalLanguagesData] = useState(null);
  const [topicsData, setTopicsData] = useState(null);
  const [durationsData, setDurationsData] = useState(null);
  const [typesData, setTypesData] = useState(null);
  const [selectedPrograms, setSelectedPrograms] = useState([]);
  const [selectedTopics, setSelectedTopics] = useState([]);
  const [selectedDurations, setSelectedDurations] = useState([]);
  const [selectedTypes, setSelectedTypes] = useState([]);
  const [selectedAdditionalLanguages, setSelectedAdditionalLanguages] = useState([]);
  const [skillOptions, setSkillOptions] = useState();
  const [skillsLoading, setSkillsLoading] = useState(false);
  const [rendered, setRendered] = useState(false);
  const [dropdownList, setDropdownList] = useState([true, true, true, true, true]);
  const [fields, setFields] = useState({
    [Fields.DESCRIPTION]: meta?.description || '',
    [Fields.SKILLS]:
      meta?.skillDetails?.map(({ id, label, level, external_data = [] }) => ({
        id,
        label,
        value: id,
        level,
        external_data
      })) || [],
    [Fields.PROGRAMS]: meta?.programs || [],
    [Fields.TOPICS]: meta?.topics || [],
    [Fields.DURATIONS]: meta?.durations || [],
    [Fields.TYPES]: meta?.types || [],
    [Fields.ADDITIONAL_LANGUAGES]: meta?.languages || []
  });
  const [errors, setErrors] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [saving, setSaving] = useState(false);

  const [disabledPrograms, setDisabledPrograms] = useState([]);
  const [disabledTopics, setDisabledTopics] = useState([]);
  const [disabledDurations, setDisabledDurations] = useState([]);
  const [disabledTypes, setDisabledTypes] = useState([]);
  const languagesData = window.__edOrgData?.languages || {};

  useEffect(() => {
    if (showSkills) {
      setSkillsLoading(true);
      getUserPassport(currentUserId)
        .then(res => {
          const skillArray = [];
          res?.skills.forEach(skill => {
            skillArray.push({ ...skill.skills[0], level: skill.rawProficiencyLevel });
          });
          setSkillsLoading(false);
          setSkillOptions(skillArray);
        })
        .catch(err => {
          setSkillsLoading(false);
          console.log(err);
        });
    }
    setDescriptionLoading(true);
    getGenAIConfig()
      .then(res => {
        setIntegrateGenAIAssistedDescription(res?.enable_genai?.value);
        setDescriptionLoading(false);
      })
      .catch(err => {
        setDescriptionLoading(false);
        console.error('Error while fetching genAI config: ', err);
      });

    getWorkHistories({ limit: 0, language: 'en' })
      .then(res => {
        const rawHistoryData = getWorkHistoryData(res.result);
        const workHistoryTitle = [];
        Object.values(rawHistoryData)?.forEach(history => {
          if (history.period.split(' - ')[1] != 'present') {
            workHistoryTitle.push(`${history.title} at ${history.company}`);
          }
        });
        setWorkHistory(workHistoryTitle);
      })
      .catch(err => {
        console.error(err);
      });
    // API call to resolve location data in "Became a mentor" flow
    if (!editing && currentUser?.profile?.workLocationId) {
      getLocationById(currentUser.profile.workLocationId)
        .then(resp => {
          setLocation(resp?.data);
        })
        .catch(() => console.error('error getting location info from work location id'));
    }

    if (!programs && showPrograms) {
      dispatch(getPrograms());
    }

    if (!topics && showTopics) {
      dispatch(getTopics());
    }

    if (!durations && showDurations) {
      dispatch(getDurations(STANDARD_FIELDS_TYPES.DURATION));
    }

    if (!types && showTypes) {
      dispatch(getTypes(STANDARD_FIELDS_TYPES.TYPE));
    }

    if (showAdditionalLanguages && isAdditionalLanguagesPresent()) {
      const formateddLanguages = currentUser?.profile?.additionalLanguages
        ?.map(language => languageDetails[language] || null)
        .filter(Boolean);
      setAdditionalLanguagesData(
        convertDataToTreeView(formateddLanguages, MENTORSHIP_FIELDS.ADDITIONAL_LANGUAGES)
      );
    }
  }, []);

  useEffect(() => {
    if (programs && programs.length > 0)
      setProgramsData(convertDataToTreeView(programs, MENTORSHIP_FIELDS.PROGRAMS));

    if (topics && topics.length > 0)
      setTopicsData(convertDataToTreeView(topics, MENTORSHIP_FIELDS.TOPICS));

    if (durations && durations.length > 0)
      setDurationsData(convertDataToTreeView(durations, MENTORSHIP_FIELDS.DURATION));

    if (types && types.length > 0)
      setTypesData(convertDataToTreeView(types, MENTORSHIP_FIELDS.TYPES));
  }, [programs, topics, durations, types]);

  useEffect(() => {
    setRendered(!rendered);
    if (Array.isArray(meta.programs)) {
      const originalSelectedPrograms = getEnabledItems(meta.programs);
      const validSelectedPrograms = originalSelectedPrograms.map(program => program.id);
      setSelectedPrograms(validSelectedPrograms);
      setDisabledPrograms(meta.programs.filter(program => program.enabled === false));
    }

    if (Array.isArray(meta.topics)) {
      const originalSelectedTopics = getEnabledItems(meta.topics);
      const validSelectedTopics = originalSelectedTopics.map(topic => topic.id);
      setSelectedTopics(validSelectedTopics);
      setDisabledTopics(meta.topics.filter(topic => topic.enabled === false));
    }

    if (Array.isArray(meta.durations)) {
      const originalSelectedDurations = getEnabledItems(meta.durations);
      const validSelectedDurations = originalSelectedDurations.map(duration => duration.key);
      setSelectedDurations(validSelectedDurations);
      setDisabledDurations(meta.durations.filter(duration => duration.enabled === false));
    }

    if (Array.isArray(meta.types)) {
      const originalSelectedTypes = getEnabledItems(meta.types);
      const validSelectedTypes = originalSelectedTypes.map(type => type.key);
      setSelectedTypes(validSelectedTypes);
      setDisabledTypes(meta.types.filter(type => type.enabled === false));
    }

    if (Array.isArray(meta.languages)) {
      setSelectedAdditionalLanguages(meta.languages);
    }
  }, [meta.programs, meta.topics, meta.durations, meta.types, meta.languages]);

  useEffect(() => {
    updateFieldValue(Fields.PROGRAMS, selectedPrograms);
    updateFieldValue(Fields.TOPICS, selectedTopics);
    updateFieldValue(Fields.DURATIONS, selectedDurations);
    updateFieldValue(Fields.TYPES, selectedTypes);
    updateFieldValue(Fields.ADDITIONAL_LANGUAGES, selectedAdditionalLanguages);
  }, [
    selectedPrograms,
    selectedTopics,
    selectedDurations,
    selectedTypes,
    selectedAdditionalLanguages
  ]);

  useEffect(() => {
    setErrors(
      validate(
        { submitted },
        {
          description: fields[Fields.DESCRIPTION],
          skills: fields[Fields.SKILLS],
          programs: fields[Fields.PROGRAMS],
          topics: fields[Fields.TOPICS],
          durations: fields[Fields.DURATIONS],
          types: fields[Fields.TYPES],
          additionalLanguages: fields[Fields.ADDITIONAL_LANGUAGES],
          programsRequired: programs?.length > 0 && programsRequired,
          topicsRequired: topics?.length > 0 && topicsRequired,
          skillsRequired: skillsRequired,
          descriptionRequired: descriptionRequired,
          durationsRequired: durations?.length > 0 && durationsRequired,
          typesRequired: (types?.length > 0) & typesRequired,
          additionalLanguagesRequired:
            (additionalLanguagesData?.length > 0) & additionalLanguagesRequired
        }
      )
    );
  }, [fields, setErrors, submitted, programs, topics, durations, types, additionalLanguagesData]);

  useEffect(() => {
    if (JSON.stringify(errors) !== '{}') {
      setTimeout(() => {
        announceErrorMessage();
      }, 100);
    }
  }, [errors]);

  useEffect(() => {
    const additionalLanguagesLabels =
      meta?.languages?.map(lang =>
        Object.keys(languagesData).find(key => languagesData[key] === lang)
      ) || [];

    initializeContextForGenAi(
      {
        [ENTITY.TITLE]: 'Mentorship bio',
        [ENTITY.DESCRIPTION]: fields[Fields.DESCRIPTION],
        [ENTITY.MENTORSHIP_DESCRIPTION]: fields[Fields.DESCRIPTION],
        [ENTITY.SKILLS]: showSkills ? fields[Fields.SKILLS] : [],
        [ENTITY.MENTOR_NAME]: currentUser?.name,
        [ENTITY.MENTOR_JOB_ROLE]: currentUser?.jobFamily?.label,
        [ENTITY.MENTOR_WORK_HISTORY]: workHistory,
        [ENTITY.MENTORSHIP_PROGRAMS]: showPrograms
          ? meta?.programs
              ?.filter(program => program.enabled)
              ?.map(program => program.label)
              .join(', ')
          : '',
        [ENTITY.MENTORSHIP_TOPICS]: showTopics
          ? meta?.topics
              ?.filter(topic => topic.enabled)
              ?.map(topic => topic.label)
              .join(', ')
          : '',
        [ENTITY.MENTORSHIP_DURATIONS]: showDurations
          ? meta?.durations
              ?.filter(duration => duration.enabled)
              ?.map(duration => duration.label)
              .join(', ')
          : '',
        [ENTITY.MENTORSHIP_TYPES]: showTypes
          ? meta?.types
              ?.filter(type => type.enabled)
              ?.map(type => type.label)
              .join(', ')
          : '',
        [ENTITY.MENTORSHIP_ADDITIONAL_LANGUAGES]: showAdditionalLanguages
          ? additionalLanguagesLabels?.join(', ')
          : ''
      },
      getConfig('DefaultOrgLanguage', 'en')
    );

    // no need to attach this listener if user already has location
    if (!location) {
      document.addEventListener('visibilitychange', handleTabFocus);
      return () => document.removeEventListener('visibilitychange', handleTabFocus);
    }
  }, [workHistory, meta?.programs, meta?.topics, meta?.durations, meta?.types, meta?.languages]);

  useEffect(() => {
    if (showAdditionalLanguages && additionalLanguagesRequired) {
      const inputElement = document.getElementById(
        `ed-tree-view-search-input-mentors-additionalLanguages-${additionalLanguagesData?.length}-${rendered}-${dropdownList[0]}`
      );
      if (inputElement) {
        inputElement.required = true;
      }
    }

    if (showPrograms && programsRequired) {
      const inputElement = document.getElementById(
        `ed-tree-view-search-input-mentors-programs-${programsData?.length}-${rendered}-${dropdownList[1]}`
      );
      if (inputElement) {
        inputElement.required = true;
      }
    }

    if (showTopics && topicsRequired) {
      const inputElement = document.getElementById(
        `ed-tree-view-search-input-mentors-topics-${topicsData?.length}-${rendered}-${dropdownList[2]}`
      );
      if (inputElement) {
        inputElement.required = true;
      }
    }

    if (showTypes && typesRequired) {
      const inputElement = document.getElementById(
        `ed-tree-view-search-input-mentors-types-${typesData?.length}-${rendered}-${dropdownList[3]}`
      );
      if (inputElement) {
        inputElement.required = true;
      }
    }

    if (showDurations && durationsRequired) {
      const inputElement = document.getElementById(
        `ed-tree-view-search-input-mentors-durations-${durationsData?.length}-${rendered}-${dropdownList[4]}`
      );
      if (inputElement) {
        inputElement.required = true;
      }
    }
  }, [
    additionalLanguagesData,
    programsData,
    topicsData,
    typesData,
    durationsData,
    rendered,
    dropdownList
  ]);

  const isAdditionalLanguagesPresent = useCallback(() => {
    return (
      Array.isArray(currentUser?.profile?.additionalLanguages) &&
      currentUser.profile.additionalLanguages.length > 0
    );
  }, [currentUser.profile.additionalLanguages]);

  const updateFieldValue = useCallback(
    (fieldId, value) => {
      setFields(f => ({ ...f, [fieldId]: value }));
    },
    [setFields]
  );

  const onSkillDismiss = id => {
    const newSkills = fields[Fields.SKILLS].filter(skill => skill.id != id);
    updateFieldValue(Fields.SKILLS, newSkills);
    updateGenAiContext({
      key: ENTITY.SKILLS,
      value: newSkills
    });
  };

  const onSave = useCallback(() => {
    setSubmitted(true);

    const formErrors = validate(
      { submitted: true },
      {
        description: fields[Fields.DESCRIPTION],
        skills: fields[Fields.SKILLS],
        programs: fields[Fields.PROGRAMS],
        topics: fields[Fields.TOPICS],
        durations: fields[Fields.DURATIONS],
        types: fields[Fields.TYPES],
        additionalLanguages: fields[Fields.ADDITIONAL_LANGUAGES],
        programsRequired: programs?.length > 0 && programsRequired,
        topicsRequired: topics?.length > 0 && topicsRequired,
        skillsRequired: skillsRequired,
        descriptionRequired: descriptionRequired,
        durationsRequired: durations?.length > 0 && durationsRequired,
        typesRequired: (types?.length > 0) & typesRequired,
        additionalLanguagesRequired:
          (additionalLanguagesData?.length > 0) & additionalLanguagesRequired
      }
    );

    if (every(values(formErrors), v => !v)) {
      setSaving(true);

      const request = editing
        ? updateMentorshipProfile(meta.id, {
            description: fields[Fields.DESCRIPTION],
            ...(fields[Fields.SKILLS]?.length > 0 && {
              skillDetails: fields[Fields.SKILLS].map(skill => ({ id: skill.id }))
            }),
            ...(fields[Fields.PROGRAMS]?.length > 0 && { programs: fields[Fields.PROGRAMS] }),
            ...(fields[Fields.TOPICS]?.length > 0 && { topics: fields[Fields.TOPICS] }),
            ...(fields[Fields.TYPES]?.length > 0 && { types: fields[Fields.TYPES] }),
            ...(fields[Fields.DURATIONS]?.length > 0 && { durations: fields[Fields.DURATIONS] }),
            ...(fields[Fields.ADDITIONAL_LANGUAGES]?.length > 0 && {
              languages: fields[Fields.ADDITIONAL_LANGUAGES]
            }),
            status: meta.status
          })
        : createMentorshipProfile({
            description: fields[Fields.DESCRIPTION],
            ...(fields[Fields.SKILLS]?.length > 0 && {
              skillDetails: fields[Fields.SKILLS].map(skill => ({ id: skill.id }))
            }),
            ...(fields[Fields.PROGRAMS]?.length > 0 && { programs: fields[Fields.PROGRAMS] }),
            ...(fields[Fields.TOPICS]?.length > 0 && { topics: fields[Fields.TOPICS] }),
            ...(fields[Fields.TYPES]?.length > 0 && { types: fields[Fields.TYPES] }),
            ...(fields[Fields.DURATIONS]?.length > 0 && { durations: fields[Fields.DURATIONS] }),
            ...(fields[Fields.ADDITIONAL_LANGUAGES]?.length > 0 && {
              languages: fields[Fields.ADDITIONAL_LANGUAGES]
            })
          }).then(response => {
            reportOpportunityPublished(OPPORTUNITY_TYPE.MENTORSHIP, response.data.id);
            return response;
          });

      request
        .then(response => {
          setSaving(false);
          closeModal();

          if (onSuccessSaveProfile) {
            onSuccessSaveProfile(response.data);
          }

          if (!editing) {
            showConfirmationModal(response.data.id);
          }
        })
        .catch(() => {
          setSaving(false);
          toast(
            translatr(
              'web.common.main',
              'ThereWasAnErrorWhileTryingToCreateYourProfilePleaseTryAgain'
            ),
            'error'
          );
        });
    } else {
      setErrors(formErrors);
      scrollToFirstError();
    }
  }, [
    closeModal,
    fields,
    setErrors,
    setSaving,
    setSubmitted,
    toast,
    programsData,
    topicsData,
    skillOptions
  ]);

  const onAddSkillstoPassportClick = e => {
    e.preventDefault();
    navigate('/me/skills-passport');
  };

  const handleTabFocus = useCallback(() => {
    if (!document.hidden) {
      getUserBasicInfo(currentUserId)
        .then(response => {
          const workLocationId = response?.profile?.workLocationId;
          const focusEl = document.getElementById('mentorship-description');

          if (workLocationId) {
            getLocationById(workLocationId)
              .then(resp => {
                // handle refocus for a11y
                focusEl?.focus();
                setLocation(resp?.data);
                // remove listener since we don't need anymore
                document.removeEventListener('visibilitychange', handleTabFocus);
              })
              .catch(() =>
                console.error('error getting location info from work location id on tab focus')
              );
          }
        })
        .catch(() => console.error('error getting user info on tab focus'));
    }
  }, [currentUserId]);

  const hideOtherDropDowns = useCallback(
    except => {
      const updatedDropdownList = dropdownList.map((item, index) =>
        index !== except ? !item : item
      );
      setDropdownList(updatedDropdownList);
    },
    [dropdownList]
  );

  const hasExpertTopics = skillOptions?.length > 0;

  const disabledElementsAlertText = `${translatr(
    'web.talentmarketplace.main',
    'ElementsDisabledText'
  )} <br/>
    <div class='disabled-programs-topics-items'>
    ${
      showPrograms && disabledPrograms.length > 0
        ? `<div>${translatr('web.talentmarketplace.main', 'Programs')}: <b>${disabledPrograms
            .map(program => program.translatedLabel)
            .join(', ')}</b></div>`
        : ''
    }
    ${
      showTopics && disabledTopics.length > 0
        ? `<div>${translatr('web.talentmarketplace.main', 'Topics')}: <b>${disabledTopics
            .map(topic => topic.translatedLabel)
            .join(', ')}</b><div>`
        : ''
    }
    ${
      showTypes && disabledTypes.length > 0
        ? `<div>${translatr('web.talentmarketplace.main', 'Types')}: <b>${disabledTypes
            .map(type => type.translatedLabel)
            .join(', ')}</b><div>`
        : ''
    }
    ${
      showDurations && disabledDurations.length > 0
        ? `<div>${translatr('web.talentmarketplace.main', 'Durations')}: <b>${disabledDurations
            .map(duration => duration.translatedLabel)
            .join(', ')}</b><div>`
        : ''
    }
    </div>`;

  /**
   * From text editor, adding a link does not work with the focus lock. Need to come with a future accessible
   * solution but for now allowing adding links in favor of trap
   */
  return (
    <div className={ROOT_CLASS}>
      <FocusLock>
        <ModalHeader
          title={
            editing
              ? translatr('web.talentmarketplace.main', 'EditMentorProfile', {
                  tm_tm_mentor: omp('tm_tm_mentor')
                })
              : translatr('web.talentmarketplace.main', 'CreateMentorProfile', {
                  tm_tm_mentor: omp('tm_tm_mentor')
                })
          }
          onClose={closeModal}
        />
        <ModalContent>
          <span className={`${ROOT_CLASS}--encouragement-text`}>
            {translatr('web.common.main', 'YouAreAlmostDoneCreatingYourProfile')}
          </span>
          <span className={`${ROOT_CLASS}--hint-text xs-margin-top`}>
            {translatr('web.common.main', 'HintTextForBecomingAMentor', {
              tm_tm_mentor: omp('tm_tm_mentor')
            })}
          </span>
          <UserMetaInfo
            avatarUrl={profile.avatarUrl}
            handle={profile.handle}
            language={languages[profile.language]}
            location={location}
            name={profile.name}
            position={unescape(profile.jobTitle)}
            timeZone={profile.timeZoneDetails?.translatedName}
            isLocationsEnabled={
              locationsEnabled &&
              locationFieldAssociation.includes(LOCATION_ASSOCIATION.USER) &&
              isLocationFieldVisible(
                locationFieldVisibility,
                LOCATION_FIELDS.NAME,
                LOCATION_USAGE_OPTIONS.MENTOR_PROFILE
              )
            }
          />
          <div className={`m-margin-ends ${ROOT_CLASS}--path`} />
          {((showPrograms && disabledPrograms.length > 0) ||
            (showTopics && disabledTopics.length > 0) ||
            (showTypes && disabledTypes.length > 0) ||
            (showDurations && disabledDurations.length > 0)) && (
            <AreaAlert
              type="closable-warning"
              msg={
                <div dangerouslySetInnerHTML={{ __html: safeRender(disabledElementsAlertText) }} />
              }
              icon={true}
              ariaLabel={`${translatr('web.common.main', 'Close')} ${translatr(
                'cds.common.main',
                'Warning'
              )} ${translatr('cds.common.main', 'Message')}`}
              ariaRoleDescription={`${translatr('web.common.main', 'Close')} ${translatr(
                'cds.common.main',
                'Warning'
              )} ${translatr('cds.common.main', 'Message')}`}
            />
          )}

          <p className="supporting-text" role="presentation">
            {translatr('web.talentmarketplace.main', 'AsterikIndicatesRequired')}
          </p>

          {showSkills && skillsLoading && <Loading />}

          {showSkills && !skillsLoading && (
            <>
              <Field
                required={skillsRequired}
                description={translatr(
                  'web.common.main',
                  'SelectSkillsToBeHighlightedOnYourMentorProfile',
                  { tm_tm_mentor: omp('tm_tm_mentor') }
                )}
                error={errors[Fields.SKILLS]}
                hideCount={!hasExpertTopics}
                id="mentorship-skills"
                describedById="react-select-2-placeholder"
                length={fields[Fields.SKILLS].length}
                maxLength={skillOptions?.length}
                suffix="Skills"
                title={translatr('web.common.main', 'Skills')}
              >
                {hasExpertTopics ? (
                  <MultiSelectSearch
                    id="mentorship-skills"
                    aria-labelledby="mentorship-skills-label"
                    placeholder={translatr('web.common.main', 'SearchSkillsFromYourSkillsPassport')}
                    value={fields[Fields.SKILLS]}
                    onChange={val => {
                      updateFieldValue(Fields.SKILLS, val);
                      updateGenAiContext({
                        key: ENTITY.SKILLS,
                        value: val
                      });
                    }}
                    styles={skillStyle}
                    options={skillOptions.map(
                      ({ topicId, topicLabel, level, topicExternalData }) => ({
                        id: topicId,
                        value: topicId,
                        label: topicLabel,
                        level,
                        topicExternalData
                      })
                    )}
                    required={skillsRequired}
                  />
                ) : (
                  <>
                    <span className="ed-input-description s-margin-bottom">
                      {translatr(
                        'web.common.main',
                        'YouCurrentlyDoNotHaveAnySkillsInYourSkillsPassportGetStartedByAddingSkills',
                        { tm_tm_mentor: omp('tm_tm_mentor') }
                      )}
                    </span>
                    <a
                      className="ed-btn ed-btn-neutral"
                      href="/me/skills-passport"
                      onClick={onAddSkillstoPassportClick}
                    >
                      <i className="icon-external-link" color="inherit" />
                      {translatr('web.common.main', 'AddSkillsToSkillsPassport')}
                    </a>
                  </>
                )}
              </Field>
              <SkillsSection items={fields[Fields.SKILLS]} onClose={onSkillDismiss} />
            </>
          )}

          {showAdditionalLanguages && isAdditionalLanguagesPresent() && (
            <Field
              required={additionalLanguagesRequired}
              error={errors[Fields.ADDITIONAL_LANGUAGES]}
              hideCount={true}
              id={`ed-tree-view-search-input-mentors-additionalLanguages-${additionalLanguagesData?.length}-${rendered}-${dropdownList[0]}`}
              describedById="mentors-additionalLanguages-field"
              title={translatr('web.talentmarketplace.main', 'AdditionalLanguages')}
            >
              <>
                <TreeView
                  key={`mentors-additionalLanguages-${additionalLanguagesData?.length}-${rendered}-${dropdownList[0]}`}
                  uniqueId={`mentors-additionalLanguages-${additionalLanguagesData?.length}-${rendered}-${dropdownList[0]}`}
                  ariaDescribedBy="mentors-additionalLanguages-field"
                  onInputClick={() => hideOtherDropDowns(0)}
                  data={additionalLanguagesData}
                  variant="multi"
                  isLoading={false}
                  defaultSelectedIds={selectedAdditionalLanguages}
                  onSelect={data => {
                    const selectedIds = [...data.treeState?.selectedIds];
                    const selectedAdditionalLanguagesLabels = selectedIds
                      .map(id => additionalLanguagesData.find(lang => lang.id === id)?.name)
                      .filter(Boolean);
                    setSelectedAdditionalLanguages(selectedIds);
                    updateGenAiContext({
                      key: ENTITY.MENTORSHIP_ADDITIONAL_LANGUAGES,
                      value: selectedAdditionalLanguagesLabels.join(', ')
                    });
                  }}
                  onSearch={query => {
                    return searchAdditionalLanguages(
                      additionalLanguagesData,
                      selectedAdditionalLanguages,
                      query
                    );
                  }}
                  searchPlaceholder={
                    selectedPrograms?.length > 0
                      ? ' '
                      : translatr(
                          'web.talentmarketplace.main',
                          'SelectAdditionalLanguagesPlaceHolder'
                        )
                  }
                  ariaLabel={translatr('web.talentmarketplace.main', 'AdditionalLanguages')}
                />
                {!additionalLanguagesData && selectedAdditionalLanguages.length > 0 && (
                  <span className="selected-count-text">
                    {translatr('web.talentmarketplace.main', 'AdditionalLanguagesCountLabel', {
                      count: selectedPrograms.length
                    })}
                  </span>
                )}
              </>
            </Field>
          )}

          {(showPrograms || showTopics) && (
            <div className="programs-topics-container">
              {showPrograms && (programs == null || programsData?.length > 0) && (
                <Field
                  required={programsRequired}
                  error={errors[Fields.PROGRAMS]}
                  hideCount={true}
                  id={`ed-tree-view-search-input-mentors-programs-${programsData?.length}-${rendered}-${dropdownList[1]}`}
                  describedById="mentors-programs-input-field"
                  title={translatr('web.talentmarketplace.main', 'Program')}
                >
                  <>
                    <TreeView
                      key={`mentors-programs-${programsData?.length}-${rendered}-${dropdownList[1]}`}
                      uniqueId={`mentors-programs-${programsData?.length}-${rendered}-${dropdownList[1]}`}
                      onInputClick={() => hideOtherDropDowns(1)}
                      data={programsData}
                      variant="multi"
                      isLoading={programs == null}
                      defaultSelectedIds={selectedPrograms}
                      ariaDescribedBy="mentors-programs-input-field"
                      onSelect={data => {
                        const selectedIds = [...data.treeState?.selectedIds];
                        const selectedProgramLabels = selectedIds.map(
                          id => programsData.find(program => program.id === id)?.name
                        );
                        setSelectedPrograms(selectedIds);
                        updateGenAiContext({
                          key: ENTITY.MENTORSHIP_PROGRAMS,
                          value: selectedProgramLabels.join(', ')
                        });
                      }}
                      onSearch={query => {
                        return searchPrograms(programsData, selectedPrograms, query);
                      }}
                      searchPlaceholder={
                        selectedPrograms?.length > 0
                          ? ' '
                          : translatr('web.talentmarketplace.main', 'SelectProgramPlaceHolder')
                      }
                      ariaLabel={translatr('web.talentmarketplace.main', 'Program')}
                    />
                    {!programsData && selectedPrograms.length > 0 && (
                      <span className="selected-count-text">
                        {translatr('web.talentmarketplace.main', 'ProgramCountLabel', {
                          count: selectedPrograms.length
                        })}
                      </span>
                    )}
                  </>
                </Field>
              )}
              {showTopics && (topics == null || topicsData?.length > 0) && (
                <Field
                  required={topicsRequired}
                  error={errors[Fields.TOPICS]}
                  hideCount={true}
                  id={`ed-tree-view-search-input-mentors-topics-${topicsData?.length}-${rendered}-${dropdownList[2]}`}
                  describedById="mentors-topics-input-field"
                  title={translatr('web.talentmarketplace.main', 'Topic')}
                >
                  <>
                    <TreeView
                      key={`mentors-topics-${topicsData?.length}-${rendered}-${dropdownList[2]}`}
                      uniqueId={`mentors-topics-${topicsData?.length}-${rendered}-${dropdownList[2]}`}
                      ariaDescribedBy="mentors-topics-input-field"
                      onInputClick={() => hideOtherDropDowns(2)}
                      data={topicsData}
                      variant="multi"
                      isLoading={topics == null}
                      defaultSelectedIds={selectedTopics}
                      onSelect={data => {
                        const selectedIds = [...data.treeState?.selectedIds];
                        const selectedTopicLabels = selectedIds.map(
                          id => topicsData.find(topic => topic.id === id)?.name
                        );
                        setSelectedTopics(selectedIds);
                        updateGenAiContext({
                          key: ENTITY.MENTORSHIP_TOPICS,
                          value: selectedTopicLabels.join(', ')
                        });
                      }}
                      onSearch={query => {
                        return searchTopics(topicsData, selectedTopics, query);
                      }}
                      searchPlaceholder={
                        selectedTopics?.length > 0
                          ? ' '
                          : translatr('web.talentmarketplace.main', 'SelectTopicPlaceHolder')
                      }
                      ariaLabel={translatr('web.talentmarketplace.main', 'Topic')}
                    />
                    {!topicsData && selectedTopics.length > 0 && (
                      <span className="selected-count-text">
                        {translatr('web.talentmarketplace.main', 'TopicCountLabel', {
                          count: selectedTopics.length
                        })}
                      </span>
                    )}
                  </>
                </Field>
              )}
            </div>
          )}

          {showTypes && (types == null || typesData?.length > 0) && (
            <Field
              required={typesRequired}
              error={errors[Fields.TYPES]}
              hideCount={true}
              id={`ed-tree-view-search-input-mentors-types-${typesData?.length}-${rendered}-${dropdownList[3]}`}
              describedById="mentors-types-input-field"
              title={translatr('web.talentmarketplace.main', 'MentorshipType', {
                tm_tm_mentorship: omp('tm_tm_mentorship')
              })}
            >
              <>
                <TreeView
                  key={`mentors-types-${typesData?.length}-${rendered}-${dropdownList[3]}`}
                  uniqueId={`mentors-types-${typesData?.length}-${rendered}-${dropdownList[3]}`}
                  ariaDescribedBy="mentors-types-input-field"
                  onInputClick={() => hideOtherDropDowns(3)}
                  data={typesData}
                  variant="multi"
                  isLoading={types == null}
                  defaultSelectedIds={selectedTypes}
                  onSelect={data => {
                    const selectedIds = [...data.treeState?.selectedIds];
                    const selectedTypeLabels = selectedIds.map(
                      id => typesData.find(type => type.id === id)?.name
                    );
                    setSelectedTypes(selectedIds);
                    updateGenAiContext({
                      key: ENTITY.MENTORSHIP_TYPES,
                      value: selectedTypeLabels.join(', ')
                    });
                  }}
                  onSearch={query => {
                    return searchTypes(typesData, selectedTypes, query);
                  }}
                  searchPlaceholder={
                    selectedTypes?.length > 0
                      ? ' '
                      : translatr('web.talentmarketplace.main', 'SelectTypePlaceHolder')
                  }
                  ariaLabel={translatr('web.talentmarketplace.main', 'Type')}
                />
                {!typesData && selectedTypes.length > 0 && (
                  <span className="selected-count-text">
                    {translatr('web.common.main', 'TypeCountLabel', {
                      count: selectedTypes.length
                    })}
                  </span>
                )}
              </>
            </Field>
          )}

          {showDurations && (durations == null || durationsData?.length > 0) && (
            <Field
              required={durationsRequired}
              error={errors[Fields.DURATIONS]}
              hideCount={true}
              id={`ed-tree-view-search-input-mentors-durations-${durationsData?.length}-${rendered}-${dropdownList[4]}`}
              describedById="mentors-durations-input-field"
              title={translatr('web.talentmarketplace.main', 'MentorshipDuration', {
                tm_tm_mentorship: omp('tm_tm_mentorship')
              })}
            >
              <>
                <TreeView
                  key={`mentors-durations-${durationsData?.length}-${rendered}-${dropdownList[4]}`}
                  uniqueId={`mentors-durations-${durationsData?.length}-${rendered}-${dropdownList[4]}`}
                  ariaDescribedBy="mentors-durations-input-field"
                  onInputClick={() => hideOtherDropDowns(4)}
                  data={durationsData}
                  variant="multi"
                  isLoading={durations == null}
                  defaultSelectedIds={selectedDurations}
                  onSelect={data => {
                    const selectedIds = [...data.treeState?.selectedIds];
                    const selectedDurationLabels = selectedIds.map(
                      id => durationsData.find(duration => duration.id === id)?.name
                    );
                    setSelectedDurations(selectedIds);
                    updateGenAiContext({
                      key: ENTITY.MENTORSHIP_DURATIONS,
                      value: selectedDurationLabels.join(', ')
                    });
                  }}
                  onSearch={query => {
                    return searchDurations(durationsData, selectedDurations, query);
                  }}
                  searchPlaceholder={
                    selectedDurations?.length > 0
                      ? ' '
                      : translatr('web.talentmarketplace.main', 'SelectDurationPlaceHolder')
                  }
                  ariaLabel={translatr('web.talentmarketplace.main', 'Duration')}
                />
                {!durationsData && selectedDurations.length > 0 && (
                  <span className="selected-count-text">
                    {translatr('web.common.main', 'DurationCountLabel', {
                      count: selectedDurations.length
                    })}
                  </span>
                )}
              </>
            </Field>
          )}

          {showDescription && !descriptionLoading && (
            <Field
              required={descriptionRequired}
              error={errors[Fields.DESCRIPTION]}
              id="mentorship-description"
              describedById="cke_32"
              title={translatr('web.common.main', 'Description')}
            >
              <Editor
                defaultValue={fields[Fields.DESCRIPTION]}
                required={descriptionRequired}
                placeholder={{
                  msg: 'Provide a high level overview of your skills and capabilities as a mentor.'
                }}
                setValue={val => {
                  updateFieldValue(Fields.DESCRIPTION, val);
                  if (
                    integrateGenAIAssistedDescription &&
                    !window.CKEDITOR?.instances['mentorship-description']?.isGenAiGenerated
                  ) {
                    updateGenAiContext({
                      key: ENTITY.DESCRIPTION,
                      value: val
                    });
                    updateGenAiContext({
                      key: ENTITY.MENTORSHIP_DESCRIPTION,
                      value: val
                    });
                  }
                }}
                editorType="inline"
                name="mentorship-description"
                genAiAssistantProps={{
                  showGenAiAssistant: integrateGenAIAssistedDescription,
                  entity: 'mentorship_description'
                }}
              />
            </Field>
          )}
        </ModalContent>
        <ModalFooter>
          <button disabled={saving} className="ed-btn ed-btn-neutral" onClick={closeModal}>
            {translatr('web.common.main', 'Cancel')}
          </button>
          <button disabled={saving} className="ed-btn ed-btn-primary" onClick={onSave}>
            {saving
              ? translatr('web.common.main', 'Saving')
              : editing
              ? translatr('web.common.main', 'SaveChanges')
              : translatr('web.common.main', 'CreateProfile')}
          </button>
        </ModalFooter>
      </FocusLock>
    </div>
  );
};

BecomeAMentorModal.propTypes = {
  closeModal: func.isRequired,
  currentUserId: string.isRequired,
  currentUser: object,
  languages: object.isRequired,
  editing: bool,
  profile: object.isRequired,
  toast: func.isRequired,
  showConfirmationModal: func,
  meta: object,
  onSuccessSaveProfile: func,
  updateUserLocation: func,
  locationsEnabled: bool,
  locationFieldVisibility: object,
  locationFieldAssociation: array,
  programs: array,
  topics: array,
  durations: array,
  types: array,
  languageDetails: object
};

export default connect(
  ({ currentUser, modal, team, locationsConfiguration, mentorProfile }) => {
    const passportSkills = currentUser.get('userPassport');
    const profile = currentUser.get('profile').toJS();

    return {
      currentUser: currentUser.toJS(),
      currentUserId: currentUser.get('id'),
      meta: modal.get('meta'),
      editing: modal.get('editing'),
      onSuccessSaveProfile: modal.get('onSuccessSaveProfile'),
      languageDetails: getLanguageDetailsObj(team.toJS().languageDetails),
      languages: transformLanguages(team.toJS().languages),
      profile: {
        ...profile,
        avatarUrl: currentUser.get('avatar'),
        name: currentUser.get('name'),
        handle: currentUser.get('handle'),
        // sync updated passport skills when it is available
        expertTopics:
          passportSkills.skills === undefined
            ? profile.expertTopics
            : passportSkills.skills
                ?.map(skill => ({ ...skill.skills[0], level: skill.level }))
                .map(({ topicId: topic_id, topicLabel: topic_label, level }) => ({
                  topic_id,
                  topic_label,
                  level
                })) || []
      },
      locationsEnabled: locationsConfiguration.get('enable'),
      locationFieldAssociation: locationsConfiguration.get('association'),
      locationFieldVisibility: locationsConfiguration.get('visibility'),
      programs: mentorProfile.get('programs'),
      topics: mentorProfile.get('topics'),
      durations: mentorProfile.get('durations'),
      types: mentorProfile.get('types')
    };
  },
  dispatch => ({
    dispatch,
    closeModal: () => dispatch(close()),
    showConfirmationModal: mentorshipId =>
      dispatch(openBecomeAMentorConfirmationModal(mentorshipId)),
    toast: (message, type) => dispatch(openSnackBar(message, type))
  })
)(withGenAiAssistantContextProvider(BecomeAMentorModal));
