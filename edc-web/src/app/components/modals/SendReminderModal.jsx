import React, { useState, useEffect } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Table from 'centralized-design-system/src/Table';
import FocusLock from 'react-focus-lock';
import PropTypes from 'prop-types';
import <PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  ModalFooter
} from 'centralized-design-system/src/Modals';
import { AreaInput } from 'centralized-design-system/src/Inputs';
import './SendReminderModal.scss';
import { sendReminder } from 'edc-web-sdk/requests/assignments';
import {
  getTeamAssignmentDetails,
  getIndividualAssignmentsForManagers
} from 'edc-web-sdk/requests/managerDashboard.v2';
import Avatar from 'centralized-design-system/src/Avatar';
import Checkbox from 'centralized-design-system/src/Checkbox';
import Loading from 'centralized-design-system/src/Loading';
import Tab from 'centralized-design-system/src/TabBar/SimpleTabs';
import { getCardTypeLabel } from '@utils/smartCardUtils';

const SendReminderModal = props => {
  const MD_BLOCKS_ENABLED = window?.__edOrgData?.configs.find(a => a.name === 'md_configuration')
    ?.value?.galaxy_manager_dashboard;

  const { onClose, isMultiUser, indexedTeam } = props;
  const { assignment_id, card_id, user_id } = props.assignments;
  const Tabs =
    MD_BLOCKS_ENABLED && isMultiUser
      ? [
          translatr('web.manager-dashboard-v2.main', 'Overdue'),
          translatr('web.manager-dashboard-v2.main', 'DueIn2Weeks'),
          translatr('web.manager-dashboard-v2.main', 'NotStarted'),
          translatr('web.manager-dashboard-v2.main', 'InProgress')
        ]
      : [
          translatr('web.manager-dashboard-v2.main', 'NotStarted'),
          translatr('web.manager-dashboard-v2.main', 'InProgress')
        ];
  const [optionalMsg, setOptionalMsg] = useState('');
  const [loading, setLoading] = useState(true);
  const [userList, setUserList] = useState([]);
  const [startedList, setStartedList] = useState([]);
  const [notStaterdList, setNotStartedList] = useState([]);
  const [overdueList, setOverdueList] = useState([]);
  const [dueIn2WeeksList, setDueIn2WeeksList] = useState([]);
  const [allSelected, setAllSelected] = useState(false);
  const [selectedItemList, selSelectedItemList] = useState([]);
  const [activeTab, setActiveTab] = useState(
    MD_BLOCKS_ENABLED && isMultiUser ? 'Overdue' : 'Not started'
  );

  const handleClose = () => {
    if (onClose) onClose();
  };

  const optionalMsgHandler = inputValue => {
    setOptionalMsg(inputValue);
  };

  if (isMultiUser) {
    useEffect(() => {
      setLoading(true);
      getAssignmentDetails();
    }, [card_id, user_id]);
  }

  if (!isMultiUser && user_id) {
    useEffect(() => {
      setLoading(true);
      getAssignmentDetails();
    }, [card_id]);
  }

  async function getAssignmentDetails() {
    if (isMultiUser && card_id) {
      let assignmentDetails = await getTeamAssignmentDetails(card_id).catch(err =>
        console.error('Team overview init.getAssignmentDetails.func', err)
      );
      if (assignmentDetails) {
        setUserList(assignmentDetails.assignments);
        setOverdueList(assignmentDetails.assignments.filter(user => user.is_overdue === true));
        setDueIn2WeeksList(
          assignmentDetails.assignments.filter(user => user.is_due_in_two_weeks === true)
        );
        setStartedList(assignmentDetails.assignments.filter(user => user.state === 'started'));
        setNotStartedList(assignmentDetails.assignments.filter(user => user.state === 'assigned'));
      }
    }

    if (!isMultiUser && user_id && !assignment_id) {
      const data = await Promise.allSettled([
        getIndividualAssignmentsForManagers(user_id),
        getIndividualAssignmentsForManagers(user_id, 'started')
      ]).catch(err => {
        console.error('Unable to get individual assignments', err);
      });
      setStartedList(data[1]?.value?.data);
      setNotStartedList(data[0]?.value?.data);
    }

    setLoading(false);
  }

  async function handleSendReminder() {
    let payload = {
      assignment_ids: [assignment_id],
      content_id: card_id,
      message: optionalMsg
    };

    if (!isMultiUser && user_id && !assignment_id) {
      payload = {
        message: optionalMsg,
        content_ids: selectedItemList,
        user_ids: [user_id],
        state: activeTab === 'InProgress' ? 'started' : 'assigned'
      };
    }

    if (isMultiUser) {
      let assigmentIndex = {};
      userList.forEach(elem => {
        assigmentIndex = { ...assigmentIndex, [elem.user_id]: elem };
      });
      const assigmentList = selectedItemList.map(userId => assigmentIndex[userId].assignment_id);
      payload = { ...payload, assignment_ids: assigmentList };
    }

    let response = await sendReminder(payload).catch(err =>
      console.error('Manager dashboard send reminder modal init.handleSendReminder.func', err)
    );
    if (response) {
      onClose();
    }
  }

  function toggleCheckbox(e, item) {
    let list = [...selectedItemList];
    const id = item.user_id || item.id;
    if (!list.includes(id)) {
      selSelectedItemList([...list, id]);
    } else {
      list.splice(
        list.findIndex(elem => elem == id),
        1
      );
      selSelectedItemList(list);
    }
  }

  function toggleAllCheckboxes(currentList = null) {
    if (allSelected) {
      selSelectedItemList([]);
    } else {
      if (currentList) {
        selSelectedItemList(currentList.map(obj => obj.id));
      } else {
        let list = userList;
        selSelectedItemList(list.map(obj => obj.user_id));
      }
    }
    setAllSelected(!allSelected);
  }

  let currentList = [];
  switch (activeTab) {
    case 'Overdue':
      currentList = overdueList;
      break;
    case 'Due in 2 weeks':
      currentList = dueIn2WeeksList;
      break;
    case 'In Progress':
      currentList = startedList;
      break;
    case 'Not started':
      currentList = notStaterdList;
      break;
    default:
      break;
  }

  let Header = [];

  if (isMultiUser) {
    Header = [
      {
        children: (
          <Checkbox
            aria-label={translatr('web.common.main', 'SelectAllUsers')}
            label={translatr('web.common.main', 'TeamMembers')}
            checked={allSelected}
            isTranslated={true}
            onChange={() => {
              toggleAllCheckboxes();
            }}
          />
        )
      }
    ];
  }

  if (!isMultiUser && user_id && !assignment_id) {
    Header = [
      {
        children: (
          <Checkbox
            aria-label={translatr('web.common.main', 'SelectAll')}
            label={translatr('web.common.main', 'SelectAll') || 'Select all'}
            checked={allSelected}
            isTranslated={true}
            onChange={() => {
              toggleAllCheckboxes(currentList);
            }}
          />
        )
      }
    ];
  }

  let Rows = [];

  if (isMultiUser) {
    Rows = currentList.map((user, index) => {
      const isChecked = selectedItemList.includes(user.user_id);
      return [
        {
          children: (
            <div className="row-items-wrapper">
              <div className="check-box-avatar-wrapper">
                <Checkbox
                  label={
                    <>
                      <div className="flx-space-btw">
                        <div className="img-name-wrapper">
                          <div className="avatar-img">
                            <Avatar user={indexedTeam[user.user_id]} />
                          </div>
                          <div className="double-cell-wrapper name-handle-wrapper">
                            <div className="half-cell">{indexedTeam[user.user_id].name}</div>
                            <div className="half-cell ed-supporting-text">
                              {indexedTeam[user.user_id].handle}
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  }
                  aria-label={translatr('web.common.main', 'SelectUser')}
                  checked={isChecked}
                  disabled={false}
                  isTranslated={true}
                  onChange={e => {
                    toggleCheckbox(e, indexedTeam[user.user_id]);
                  }}
                />
              </div>
            </div>
          ),
          id: `${index}-${user.user_id}-${indexedTeam[user.user_id].name}`,
          align: 'text-left'
        }
      ];
    });
  }

  if (!isMultiUser && user_id) {
    Rows = currentList.map((content, index) => {
      const isChecked = selectedItemList.includes(content.id);
      const meta = [];
      if (content.skill_level) {
        meta.push(translatr('web.common.main', content.skill_level));
      }
      if (content.card_type) {
        meta.push(getCardTypeLabel(content.card_type));
      }
      if (content.display_duration) {
        meta.push(content.display_duration);
      }
      return [
        {
          children: (
            <div className="row-items-wrapper content-item">
              <div className="check-box-avatar-wrapper">
                <Checkbox
                  label={
                    <>
                      <div className="flx-space-btw">
                        <div className="img-name-wrapper">
                          <div className="avatar-img">
                            <img src={content.filestack?.[0]?.thumbnail} alt="" />
                          </div>
                          <div className="double-cell-wrapper name-handle-wrapper cell-stacked">
                            <div className="half-cell">
                              <strong>{content.title}</strong>
                            </div>
                            <div className="half-cell ed-supporting-text flx-col">
                              {content.author_full_name && (
                                <span>
                                  {translatr('web.common.main', 'By')} {content.author_full_name}
                                </span>
                              )}
                              <span>{meta.join(' | ')}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  }
                  aria-label={translatr('web.common.main', 'SelectAssignment')}
                  checked={isChecked}
                  disabled={false}
                  isTranslated={true}
                  onChange={e => {
                    toggleCheckbox(e, content);
                  }}
                />
              </div>
            </div>
          ),
          id: `${index}-${content.id}`,
          align: 'text-left'
        }
      ];
    });
  }

  const onTabChange = tab => {
    setActiveTab(tab);
  };

  return (
    <Modal size="small" className="md-send-reminder-modal">
      <FocusLock>
        <ModalHeader title={translatr('web.common.main', 'SendReminder')} onClose={handleClose} />
        <ModalContent>
          <div className="md-send-reminder-modal-contanier">
            {isMultiUser &&
              (loading ? (
                <div className="table-loader">
                  <Loading center={true} />{' '}
                </div>
              ) : (
                <Tab OnTabClickCB={onTabChange} isTranslated={true}>
                  {Tabs.map((tab, index) => {
                    const tabTitle = tab;
                    return (
                      <Tab.TabPane key={`Tab-${index}`} tab={tabTitle}>
                        {currentList.length > 0 && !loading ? (
                          <>
                            <div className="optional-msg">
                              <AreaInput
                                id={`Tab-${index}-optional-msg-area-input`}
                                title={translatr('web.common.main', 'IncludeMessage')}
                                setValue={optionalMsgHandler}
                                placeholder={translatr('web.common.main', 'WriteYourMessageHere')}
                                maxLen={1500}
                                shouldCheckForMaxChar={true}
                                optional={false}
                                isTranslated={true}
                              />
                            </div>
                            <div className="list-container">
                              <Table
                                className="none-vertical-border w-100"
                                headers={Header}
                                rows={Rows}
                              />
                            </div>
                            <div className="table-counter-display">
                              <div>
                                {translatr('web.common.main', 'TotalIndividualsSelected', {
                                  selected: selectedItemList.length,
                                  total: currentList.length
                                })}
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="empty-list-container">
                            <div className="empty-icon-container">
                              <i className="icon-users-fill" />
                            </div>
                            <div>
                              {translatr(
                                'web.common.main',
                                'CurrentlyThereAreNoUsersUnderThisStatus'
                              )}
                            </div>
                          </div>
                        )}
                      </Tab.TabPane>
                    );
                  })}
                </Tab>
              ))}
            {!isMultiUser &&
              user_id &&
              !assignment_id &&
              (loading ? (
                <div className="table-loader">
                  <Loading center={true} />{' '}
                </div>
              ) : (
                <Tab OnTabClickCB={onTabChange} isTranslated={true}>
                  {Tabs.map((tab, index) => {
                    const tabTitle = tab;
                    return (
                      <Tab.TabPane key={`Tab-${index}`} tab={tabTitle}>
                        {currentList.length > 0 && !loading ? (
                          <>
                            <div className="optional-msg">
                              <AreaInput
                                id={`Tab-${index}-optional-msg-area-input`}
                                title={translatr('web.common.main', 'IncludeMessage')}
                                setValue={optionalMsgHandler}
                                placeholder={translatr('web.common.main', 'WriteYourMessageHere')}
                                maxLen={1500}
                                shouldCheckForMaxChar={true}
                                optional={false}
                                isTranslated={true}
                              />
                            </div>
                            <div className="list-container">
                              <Table
                                className="none-vertical-border w-100"
                                headers={Header}
                                rows={Rows}
                              />
                            </div>
                            <div className="table-counter-display">
                              <div>
                                {translatr('web.common.main', 'TotalAssignmentsSelected', {
                                  selected: selectedItemList.length,
                                  total: currentList.length
                                })}
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="empty-list-container">
                            <div className="empty-icon-container">
                              <i className="icon-users-fill" />
                            </div>
                            <div>
                              {translatr(
                                'web.common.main',
                                'CurrentlyThereAreNoAssignmentsUnderThisStatus'
                              )}
                            </div>
                          </div>
                        )}
                      </Tab.TabPane>
                    );
                  })}
                </Tab>
              ))}
            {!isMultiUser && assignment_id && (
              <React.Fragment>
                <div className="optional-msg">
                  <AreaInput
                    id={`Not-multi-user-optional-msg-area-input`}
                    title={translatr('web.common.main', 'IncludeMessage')}
                    setValue={optionalMsgHandler}
                    placeholder={translatr('web.common.main', 'WriteYourMessageHere')}
                    maxLen={1500}
                    shouldCheckForMaxChar={true}
                    optional={false}
                    isTranslated={true}
                  />
                </div>
              </React.Fragment>
            )}
          </div>
        </ModalContent>
        <ModalFooter>
          <button
            className="ed-btn ed-btn-neutral"
            onClick={handleClose}
            aria-label={translatr('web.common.main', 'Cancel')}
          >
            {translatr('web.common.main', 'Cancel')}
          </button>
          <button
            className="ed-btn ed-btn-primary"
            onClick={() => handleSendReminder()}
            aria-label={translatr('web.common.main', 'SendReminder')}
            disabled={!optionalMsg || (isMultiUser && !selectedItemList.length)}
          >
            {translatr('web.common.main', 'SendReminder')}
          </button>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

SendReminderModal.propTypes = {
  onClose: PropTypes.func,
  assignments: PropTypes.object,
  isMultiUser: PropTypes.bool,
  indexedTeam: PropTypes.object
};

export default SendReminderModal;
