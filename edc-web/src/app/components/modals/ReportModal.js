import React, { useState } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import FocusLock from 'react-focus-lock';
import PropTypes from 'prop-types';
import { unescape } from 'lodash';
import Modal, { <PERSON><PERSON><PERSON>eader, ModalContent } from 'centralized-design-system/src/Modals';
import './CommonModalStyles.scss';
import { AreaInput } from 'centralized-design-system/src/Inputs';
import Radio from 'centralized-design-system/src/Radio';
import { reportCard } from 'edc-web-sdk/requests/cards.v2';
import { reportComment } from 'edc-web-sdk/requests/comments';
import { open_v2 as openSnackBar } from '../../actions/snackBarActions';
import { updateReportedCards, updateReportedComments } from '../../actions/usersActions';
import recordGenericEvents from '../../utils/recordGenericEvents';
import { OPEN_SNACKBAR_V2 } from '../../constants/actionTypes';
import { createCommunityPostReporting } from 'edc-web-sdk/requests/communityPostReportings';

const ReportModal = ({ closeHandler, card, dispatch, comment, post }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedReason, setSelectedReason] = useState('');
  const [otherReason, setOtherReason] = useState('');
  const [isTextValid, setIsTextValid] = useState(true);
  const [showAlert, setShowAlert] = useState(false);
  const [showInputError, setShowInputError] = useState(false);

  const isCard = !!card;
  const isPost = !!post;
  const cardTitle = unescape(card?.cardTitle ? card.cardTitle : card?.title);
  const cardCreator = card?.author?.fullName ? card.author.fullName : card?.author;
  const commentMaker = (comment || post)?.user?.name
    ? (comment || post).user.name
    : (comment || post)?.user;
  const isEntityContent = selectedReason && (isCard || isPost);
  const isEntityCommentWithOtherAsReason = !isCard && selectedReason && selectedReason === 'Other';
  const descriptionNotRequried = selectedReason !== 'Other';
  const showAreaTextToAddReason = (selectedReason && isCard) || isEntityCommentWithOtherAsReason;
  const displayName = isCard || isPost ? 'content' : 'comment';
  const contentType = card
    ? card.cardType === 'journey'
      ? 'Journey'
      : card.cardType === 'pack'
      ? 'Pathway'
      : 'SmartCard'
    : 'Comment';
  const reasons = [
    {
      key: 'technical-issue',
      value: 'technical-issue',
      label: translatr('web.smartcard.reportmodal', 'ReportTechnicalIssues', { displayName })
    },
    {
      key: 'Spam',
      value: 'Spam',
      label: translatr('web.smartcard.reportmodal', 'ReportItSpam')
    },
    {
      key: 'Inappropriate',
      value: 'Inappropriate',
      label: translatr('web.smartcard.reportmodal', 'ReportItInappropriate', { displayName })
    }
  ];
  if (isCard) {
    reasons.push({
      key: 'Outdated',
      value: 'Outdated',
      label: translatr('web.smartcard.reportmodal', 'ContentIsOutdated')
    });
  }
  reasons.push({
    key: 'Other',
    value: 'Other',
    label: translatr('web.common.main', 'Other')
  });

  const handleOtherReasonChange = value => {
    setOtherReason(value);
    showInputError && setShowInputError(false);
  };
  const reportCardHandler = () => {
    let description = otherReason.length ? otherReason : null;
    let payload = {
      card_id: card.id,
      reason: selectedReason,
      description
    };
    setIsLoading(true);
    reportCard(payload)
      .then(() => {
        dispatch(updateReportedCards(card.id));
        setIsLoading(false);
        closeHandler();
        dispatch(
          openSnackBar(
            translatr('web.smartcard.reportmodal', 'ContentTypeSnackbarMsg', {
              contentType: translatr('web.common.main', contentType) || contentType
            }),
            'info'
          )
        );

        recordGenericEvents('card_reported', 'cards', { card_id: card.id });
      })
      .catch(err => {
        setIsLoading(false);
        closeHandler();
        if (err && ~err.message.indexOf('Forbidden content')) {
          dispatch({
            type: OPEN_SNACKBAR_V2,
            message: translatr('web.smartcard.reportmodal', 'ForbiddenPostSnackbarError'),
            snackbarType: 'error',
            autoClose: true
          });
        } else {
          dispatch({
            type: OPEN_SNACKBAR_V2,
            message: err.message,
            snackbarType: 'error',
            autoClose: true
          });
        }
      });
  };

  const reportPostHandler = () => {
    let reason = selectedReason === 'Other' ? otherReason : selectedReason;
    let payload = {
      community_post_id: post.id,
      reason: reason
    };
    createCommunityPostReporting(payload)
      .then(() => {
        setIsLoading(false);
        closeHandler();
        dispatch(
          openSnackBar(
            translatr('web.smartcard.reportmodal', 'ThankYouForReportingPost'),
            'info',
            false
          )
        );
      })
      .catch(err => {
        setIsLoading(false);
        closeHandler();
        let message = err.message;
        if (message?.includes('has already been taken')) {
          message = translatr('web.smartcard.reportmodal', 'PostAlreadyReported');
        }
        dispatch(openSnackBar(message, 'error', false));
      });
  };

  const reportCommentHandler = () => {
    let reason = selectedReason === 'Other' ? otherReason : selectedReason;
    let payload = {
      comment_id: comment.id,
      reason: reason
    };
    reportComment(payload)
      .then(() => {
        dispatch(updateReportedComments(comment.id));
        setIsLoading(false);
        closeHandler();
        dispatch(
          openSnackBar(
            translatr('web.smartcard.reportmodal', 'CommentReportSnackbarMsg'),
            'info',
            false
          )
        );
      })
      .catch(err => {
        setIsLoading(false);
        closeHandler();
        let message = err.message;
        if (message?.includes('already reported')) {
          message = translatr('web.smartcard.reportmodal', 'CommentAlreadyReported');
        }
        dispatch(openSnackBar(message, 'error', false));
      });
  };

  const submitReason = e => {
    e.preventDefault();
    if (selectedReason) {
      if (selectedReason === 'Other' && !otherReason) {
        setShowInputError(true);
      } else {
        setIsLoading(true);
        if (card) {
          reportCardHandler();
        } else if (isPost) {
          reportPostHandler();
        } else {
          reportCommentHandler();
        }
      }
    } else {
      setShowAlert(true);
    }
  };

  const radioCheckHandler = e => {
    setSelectedReason(e.target.value);
    showAlert && setShowAlert(false);
    showInputError && setShowInputError(false);
  };

  const reportCardMsg = !!cardCreator
    ? translatr('web.common.main', 'YouAreAboutToReportCardtitleCreatedByCardcreator', {
        cardTitle,
        cardCreator
      })
    : translatr('web.common.main', 'YouAreAboutToReportCardtitle', { cardTitle });

  const reportCommentMsg = !!commentMaker
    ? translatr('web.smartcard.reportmodal', 'YouAreAboutToReportCommentMadeByCommenter', {
        commentMaker
      })
    : translatr('web.smartcard.reportmodal', 'YouAreAboutToReportCommentMentioned');

  const reportPostMsg = translatr(
    'web.smartcard.reportmodal',
    'YouAreAboutToReportPostMadeByCommenter',
    {
      commentMaker
    }
  );

  return (
    <div onClick={e => e.stopPropagation()} role="presentation">
      <Modal size="small">
        <FocusLock>
          <ModalHeader
            title={
              isPost
                ? translatr('web.smartcard.reportmodal', 'HeaderDisplayNamePost')
                : displayName === 'content'
                ? translatr('web.smartcard.reportmodal', 'HeaderDisplayNameContent')
                : translatr('web.smartcard.reportmodal', 'HeaderDisplayNameComment')
            }
            onClose={closeHandler}
          />
          <ModalContent>
            <div className="mb-16">
              {isCard ? reportCardMsg : isPost ? reportPostMsg : reportCommentMsg}
            </div>
            <Radio
              groupName="reportReason"
              items={reasons}
              wrapperClass="flex-row"
              legend={translatr('web.smartcard.reportmodal', 'HelpUsUnderstandTheIssue')}
              required={true}
              onChange={radioCheckHandler}
              isTranslated
            />
            {showAreaTextToAddReason && (
              <div className="mb-16">
                <AreaInput
                  setValue={handleOtherReasonChange}
                  title={isEntityContent ? translatr('web.common.main', 'Description') : null}
                  placeholder={translatr('web.common.main', 'PleaseDescribeYourReasonHere')}
                  setIsValid={setIsTextValid}
                  maxLen={1000}
                  optional={isEntityContent ? descriptionNotRequried : null}
                  shouldCheckForMaxChar={true}
                  isError={showInputError}
                  value={isTextValid}
                  isTranslated
                />
              </div>
            )}
            {showInputError && (
              <div className="errorMessage">
                {translatr('web.common.main', 'PleaseDescribeYourOtherReasonBeforeReporting')}
              </div>
            )}
          </ModalContent>
          <div className="custom-footer ed-ui">
            {showAlert && (
              <div className="error">
                <i className="icon-cross-circle" />
                &nbsp;
                {translatr(
                  'web.common.main',
                  'NoReasonSelectedPleaseProvideAReasonBeforeReporting'
                )}
              </div>
            )}
            <div className="justflex justify-center">
              {' '}
              <button className="ed-btn ed-btn-neutral" onClick={closeHandler}>
                {translatr('web.common.main', 'Cancel')}
              </button>
              <button disabled={isLoading} className="ed-btn ed-btn-primary" onClick={submitReason}>
                {translatr('web.common.main', 'Report')}
              </button>
            </div>
          </div>
        </FocusLock>
      </Modal>
    </div>
  );
};

ReportModal.propTypes = {
  closeHandler: PropTypes.func,
  card: PropTypes.object,
  comment: PropTypes.object,
  post: PropTypes.object,
  dispatch: PropTypes.func
};

export default ReportModal;
