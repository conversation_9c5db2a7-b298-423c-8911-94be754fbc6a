import React, { useEffect, useState } from 'react';
import { number, string, func, array, bool, object } from 'prop-types';
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON>ead<PERSON>, ModalFooter } from 'centralized-design-system/src/Modals';
import { translatr } from 'centralized-design-system/src/Translatr';
import Avatar from 'centralized-design-system/src/Avatar';
import classNames from 'classnames';
import Spinner from '../common/spinner';
import './AssociatedUserListModal.scss';
import unescape from 'lodash/unescape';

const AssociatedUserListModal = ({
  id,
  label,
  onClose,
  userList,
  getData,
  totalUsers,
  navigateToUser,
  currentUserId,
  isLoadMore
}) => {
  const [userData, setUserData] = useState(userList || []);
  const [isLoading, setIsLoading] = useState(false);
  const [offset, setOffset] = useState(12);

  // This code will be use full if data is not initially available.
  // useEffect(() => {
  //   if (!userData?.length) {
  //     setOffset(0);
  //     getData({ id, setUserData, setIsLoading, setOffset, offset: 0 });
  //   }
  // }, []);

  useEffect(() => {
    setUserData(userList);
    setIsLoading(isLoadMore);
  }, [userList, isLoadMore]);

  const captureScroll = event => {
    const element = event.target;
    if (isLoading) {
      return;
    }
    // Detect scroll end to make pagination api call

    if (
      (element.scrollHeight - Math.ceil(element.scrollTop) === element.offsetHeight ||
        element.scrollHeight - Math.floor(element.scrollTop) === element.offsetHeight) &&
      !isLoading &&
      totalUsers !== userData.length
    ) {
      getData({ id, setUserData, setIsLoading, setOffset, offset, limit: 12, isLoadMore: true });
    }
  };

  const headerLabel = `${label} (${totalUsers})`;

  return (
    <Modal size="small">
      <ModalHeader title={headerLabel} onClose={onClose.bind(this)} />
      <div className="justflex flex-wrap associated-user-modal" onScroll={captureScroll.bind(this)}>
        {userData?.map((user, index) => (
          <UserCard
            key={user.id}
            user={user}
            index={index}
            navigateToUser={navigateToUser}
            currentUserId={currentUserId}
          />
        ))}
        {isLoading && (
          <div className="make-center width-100">
            <Spinner />
          </div>
        )}
      </div>
      <ModalFooter>
        <button className="ed-btn ed-btn-neutral" onClick={onClose.bind(this)}>
          {translatr('web.common.main', 'Close')}
        </button>
      </ModalFooter>
    </Modal>
  );
};

AssociatedUserListModal.propTypes = {
  id: number,
  label: string,
  onClose: func,
  userList: array,
  getData: func,
  totalUsers: number,
  navigateToUser: func,
  currentUserId: string,
  isLoadMore: bool
};

export default AssociatedUserListModal;

const UserCard = ({ user, index, navigateToUser, currentUserId }) => {
  const userObject = {
    ...user,
    imgUrl: user.avatarimages?.medium || user.pictureUrl || user.photo
  };

  return (
    <div
      className={classNames('modal-user-card make-center text-center flex-column pointer', {
        'no-margin': index > 0 && (index + 1) % 4 === 0
      })}
      onClick={navigateToUser?.bind(this, user.handle)}
      onKeyDown={e => {
        if (e.key === 'Enter') {
          navigateToUser?.bind(this, user.handle);
        }
      }}
      role="button"
      tabIndex="0"
    >
      <Avatar user={userObject} currentUserId={currentUserId} />
      <p className="text-ellipsis text-wrapper">
        {user?.name ? user.name : user.fullName || user.firstName + ' ' + user.lastName}
      </p>
      <div className="supporting-text text-ellipsis text-wrapper">
        {unescape(user?.profile?.jobTitle)}
      </div>
    </div>
  );
};

UserCard.propTypes = {
  user: object,
  index: number,
  navigateToUser: func,
  currentUserId: string
};
