import React, { useEffect, useState } from 'react';
import <PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter
} from 'centralized-design-system/src/Modals/index';
import FocusLock from 'react-focus-lock';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import { Button } from 'centralized-design-system/src/Buttons';
import { close } from '../../../actions/modalActions';
import { useDispatch, useSelector } from 'react-redux';
import { open_v3 as openSnackBar } from '@actions/snackBarActions';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { transformFilters } from '@pages/TalentMarketplace/shared/filters/Filters.utils';
import { addOpportunitySavedSearches } from 'edc-web-sdk/requests/opportunityAlerts';
import './CreateOpportunityAlertModal.scss';
import {
  TM_PROJECT_FILTER_BUCKET_NAME,
  <PERSON>M_VACANCY_FILTER_BUCKET_NAME
} from '@pages/TalentMarketplace/shared/filters/Filters.constants';
import { SearchFilterOpportunityType } from '@pages/TalentMarketplace/shared/OpportunitiesSavedFilters/types';

const prepareOpportunityAlertPayload = (name: string, filterState: any) => {
  const filters = transformFilters(filterState);
  const excludedKeys = ['pageNumber', 'pageSize', 'sortOrder', 'sortType'];
  const filteredFilters = Object.fromEntries(
    Object.entries(filters).filter(([key]) => !excludedKeys.includes(key))
  );

  switch (filterState.bucketName) {
    case TM_VACANCY_FILTER_BUCKET_NAME:
      return {
        filterName: name,
        filterType: 'opportunity-filter',
        filterCriteria: [
          {
            type: SearchFilterOpportunityType.JOB,
            filters: filterState.filters,
            keyword: filterState.keyword,
            activeFilters: filterState?.config?.map((item: any) => {
              const label = item.label
                ? Array.isArray(item.label)
                  ? translatr(...item.label)
                  : item.label
                : omp(item.labelOmp);
              return { id: item.id, label };
            })
          }
        ],
        filterCriteriaEmailDigest: [{ ...filteredFilters, type: SearchFilterOpportunityType.JOB }]
      };
    case TM_PROJECT_FILTER_BUCKET_NAME:
      /* this is just an example for project filter bucket, currently inactive */
      return {
        filterName: name,
        filterType: 'opportunity-filter',
        filterCriteria: [{ type: SearchFilterOpportunityType.PROJECT }],
        filterCriteriaEmailDigest: [
          { ...filteredFilters, type: SearchFilterOpportunityType.PROJECT }
        ]
      };
    default:
      return null;
  }
};

const prepareLinkTarget = (filterState: any) => {
  switch (filterState.bucketName) {
    case TM_VACANCY_FILTER_BUCKET_NAME:
      return '/career/job-vacancies/my-job-alerts';
    case TM_PROJECT_FILTER_BUCKET_NAME:
      return '/career/project/saved-searches';
    default:
      return '/career/';
  }
};

const CreateOpportunityAlertModal: React.FC = () => {
  const modalData = useSelector((state: any) => state.modal?.get('data'));
  const filterList = modalData?.filtersList ? modalData?.filtersList.join(' | ') : '';
  const onSaveFnc = modalData?.onSave;
  const dispatch = useDispatch();
  const [opportunityAlertName, setOpportunityAlertName] = useState('');
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const closeModal = () => {
    dispatch<any>(close());
  };

  const handleSave = async () => {
    if (!opportunityAlertName.trim()) return;

    setIsSaving(true);

    try {
      const payload = prepareOpportunityAlertPayload(opportunityAlertName, modalData.filtersState);
      const link = prepareLinkTarget(modalData.filtersState);
      const response = await addOpportunitySavedSearches(payload);
      if (response.error) {
        throw new Error(response.error);
      }
      onSaveFnc?.();
      closeModal();
      dispatch(
        openSnackBar({
          message: translatr('web.talentmarketplace.main', 'AlertSavedForSearch', {
            link: `<a class="ed-alert-success" href="${link}">${translatr(
              'web.talentmarketplace.main',
              'AlertManageLink'
            )}</a>`
          }),
          snackbarType: 'success',
          translateMessage: false,
          closeHandler: null
        })
      );
    } catch (e) {
      if (e.status === 409) {
        setError(translatr('web.talentmarketplace.main', 'AlertAlreadyExistError'));
      } else {
        console.error('Failed to save opportunity alert:', e);
        setError(translatr('web.talentmarketplace.main', 'SomethingWentWrong'));
      }
      dispatch(
        openSnackBar({
          message: translatr('web.talentmarketplace.main', 'AlertNotSavedError'),
          snackbarType: 'error',
          translateMessage: false,
          closeHandler: null
        })
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (value: string) => {
    setOpportunityAlertName(value);
    setError('');
  };

  useEffect(() => {
    if (modalData?.filtersState && modalData?.filtersState?.keyword) {
      setOpportunityAlertName(modalData.filtersState.keyword);
    }
  }, [modalData?.filtersState?.keyword]);

  return (
    // @ts-ignore
    <Modal size="small">
      <FocusLock>
        {/* @ts-ignore */}
        <ModalHeader
          title={translatr('web.talentmarketplace.main', 'CreateAlertModalTitle')}
          onClose={() => closeModal()}
        />
        <ModalContent>
          <div className="create-opportunity-alert-modal__content">
            <div className="create-opportunity-alert-modal__content--description">
              {translatr('web.talentmarketplace.main', 'CreateAlertModalDescription')}
            </div>

            <TextField
              // @ts-ignore
              required
              id={'opportunity-alert-name'}
              title={translatr('web.common.main', 'Name')}
              placeholder={translatr('web.common.main', 'EnterName')}
              setValue={handleInputChange}
              defaultValue={opportunityAlertName}
              error={error}
            />
            <div className="create-opportunity-alert-modal__content--filter-list">{filterList}</div>
          </div>
        </ModalContent>
        <ModalFooter>
          <Button color="secondary" onClick={() => closeModal()}>
            {translatr('web.common.main', 'Close')}
          </Button>
          <Button
            color="primary"
            onClick={handleSave}
            disabled={!opportunityAlertName.trim() || isSaving}
          >
            {isSaving
              ? translatr('web.common.main', 'Saving')
              : translatr('web.common.main', 'Save')}
          </Button>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

export default CreateOpportunityAlertModal;
