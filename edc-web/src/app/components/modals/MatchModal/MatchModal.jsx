import React, { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import Mo<PERSON>, {
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>Footer,
  Mo<PERSON>Header
} from 'centralized-design-system/src/Modals';
import { MATCH_LEVEL, MATCH_LEVEL_CONFIG } from 'edc-web-sdk/requests/talentMarketplaceSettings.js';
import { MATCHING_ICONS, ICON_SET } from 'edc-web-sdk/helpers/matchIcons.js';
import Table from 'centralized-design-system/src/Table';
import { JOB_TYPE, getMatchingDetails } from 'edc-web-sdk/requests/careerOportunities.v2';
import { close } from 'actions/modalActions';
import FocusLock from 'react-focus-lock';
import { MATCH_STATUS, STATUS_DETAILS, TABLE_HEADERS, TAB_KEYS, getTabs } from './util';
import './MatchModal.scss';
import Spinner from 'centralized-design-system/src/MUIComponents/common/Spinner';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { noLevelPlaceholderOption } from 'centralized-design-system/src/Utils/proficiencyLevels';
import {
  LOCATION_FIELDS,
  LOCATION_USAGE_OPTIONS,
  formatLocationByFields
} from '@pages/TalentMarketplace/helpers';
import {
  useCountries,
  useMatchingConfig,
  useTabNavigation
} from '@pages/TalentMarketplace/shared/hooks';
import { saveEnabledCareerPreferenceOptions } from '../../../actions/careerPreferencesActions';
import { openCompleteYourProfileModalv2 } from '../../../actions/modalActions';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import { Button } from 'centralized-design-system/src/Buttons';
import { isMfeEnabled, openSkillFlyout } from '@components/MfeSkillsFlyout';

const ROOT_CLASS = 'tm__match-modal';

const MatchModal = ({
  jobId,
  jobType,
  scoreObject,
  currentUserLang,
  closeModal,
  preferences,
  isCareerPathEnabled,
  locationFieldVisibility,
  organizations,
  savePreferences,
  opencompleteYourProfileModal,
  locationsEnabled,
  openedFromHtmlElement
}) => {
  const navigate = useNavigate();

  const themeId = useSelector(state => state?.theme?.get('themeId'));

  const [activeTab, setActiveTab] = useState('');
  const [isLoading, setLoading] = useState(true);
  const [tabs, setTabs] = useState(new Map());
  const [responseData, setResponseData] = useState(null);
  const [matchDetails, setMatchDetails] = useState(new Map());
  const [overallScore, setOverallScore] = useState(null);
  const [skillsMatchedCnt, setSkillsMatchedCnt] = useState(0);
  const matchingConfig = useMatchingConfig();
  const [statusLevel, setStatusLevel] = useState();
  const [iconSet, setIcon] = useState();
  const [skillsCnt, setSkillsCnt] = useState(0);
  const tableHeaders = useMemo(
    () =>
      TABLE_HEADERS.get(activeTab)?.map(headr => ({ ...headr, label: translatr(...headr.label) })),
    [activeTab]
  );
  const countries = useCountries();
  const MatchModalTranslationArray = {
    skills: 'current_skills',
    preferences: 'career_preference',
    experience: 'work_experience'
  };

  const svgIcon = useMemo(() => {
    if (!iconSet) return;
    return `<svg width="25" height="24" fill="none" xmlns="http://www.w3.org/2000/svg">${MATCHING_ICONS.get(
      iconSet
    ).get(MATCH_LEVEL[overallScore?.status])}</svg>`;
  }, [iconSet, overallScore?.status]);

  const updateDataHandler = () => {
    if (tabs.get(activeTab).updateUrl) {
      navigate(tabs.get(activeTab).updateUrl[isCareerPathEnabled ? 0 : 1]);
    } else {
      opencompleteYourProfileModal({
        active_tab: MatchModalTranslationArray[activeTab],
        openedFromHtmlElement
      });
    }
  };

  const { handleKeyDown, tabRefs } = useTabNavigation(tabs, activeTab, setActiveTab);

  useEffect(() => {
    getMatchingDetails(jobId, jobType, currentUserLang, {
      skillsGraphScore: scoreObject.skillsGraphScore,
      overallScore: scoreObject.overallScore
    })
      .then(data => {
        setLoading(false);
        setOverallScore(data.overallScore);
        setResponseData(data);
        setTabs(getTabs(data, jobType));
        const defaultLabel = translatr(
          'web.common.main',
          MATCH_LEVEL_CONFIG.find(config => config.key === MATCH_LEVEL[data.overallScore.status])
            .defaultLabel
        );
        let translatedLevel;
        if (matchingConfig.translations) {
          translatedLevel =
            matchingConfig.translations.get(MATCH_LEVEL[data.overallScore.status])?.[
              currentUserLang
            ] || defaultLabel;
        } else {
          translatedLevel = defaultLabel;
        }
        setStatusLevel(translatedLevel);
        setIcon(matchingConfig.icons || ICON_SET.FACES);
      })
      .catch(err => {
        setLoading(false);
        console.error(`Error in getMatchingDetails.func : ${err}`);
      });
  }, [matchingConfig.translations, matchingConfig.icons]);

  useEffect(() => {
    if (tabs.size && responseData && preferences) {
      setActiveTab(tabs.keys().next().value);
      setMatchDetails(prepareTableData(responseData, tabs));
      setSkillsCnt(responseData?.skills?.items?.length || 0);
      setSkillsMatchedCnt(
        responseData?.skills?.items?.filter(skill => skill.status.value === MATCH_STATUS.MATCH)
          ?.length || 0
      );
    }
  }, [tabs, responseData, preferences]);

  useEffect(() => {
    if (!preferences && organizations.get('config') && typeof locationsEnabled !== 'undefined') {
      savePreferences(locationsEnabled, organizations.get('config')?.enable);
    }
  }, [preferences, organizations.get('config'), locationsEnabled]);

  const prepareTableData = (objData, availableTabs) => {
    const tableRows = new Map();
    availableTabs.forEach(({ key, jobType: type }) => {
      const data = (objData[key].items || [{ status: objData[key].status }])
        .map(a => {
          return a.key === 'organizations' ? a.values.map(e => ({ key: a.key, ...e })) : a;
        })
        .flat();
      const rendererFn = TABLE_ROW_RENDERER.get(key);
      tableRows.set(
        key,
        data.map((rowData, i) => rendererFn(rowData, key, i, type)).filter(l => !!l)
      );
    });
    return tableRows;
  };

  const renderStatusCell = (status, tab) => {
    return (
      <span
        className={`status-label status-${(status?.key || MATCH_STATUS.UNKNOWN).toLowerCase()}`}
      >
        <i className={`card-icon ${status.icon}`} />
        {status.label.get instanceof Function
          ? translatr(...status.label.get(tab))
          : translatr(...status.label)}
      </span>
    );
  };

  const sortLocFn = (param, x, y) => (x[param] === y[param] ? 0 : x[param] ? -1 : 1);

  const renderLocationsValues = locValues => {
    const sortedLocations = locValues
      .sort(sortLocFn.bind(undefined, 'matching'))
      .sort(sortLocFn.bind(undefined, 'primary'));
    const avLocations = sortedLocations.map(l => ({
      [LOCATION_FIELDS.NAME]: l.name,
      [LOCATION_FIELDS.STREET]: l.streetAddress,
      [LOCATION_FIELDS.ZIP]: l.postalCode,
      [LOCATION_FIELDS.CITY]: l.city,
      [LOCATION_FIELDS.COUNTRY]: l.country,
      [LOCATION_FIELDS.COUNTRY_CODE]: l.countryCode,
      [LOCATION_FIELDS.GEOLOCATION]: l.geolocation
    }));
    if (!avLocations?.length) return;
    const mapFormatFn = loc =>
      formatLocationByFields(
        loc,
        loc[LOCATION_FIELDS.GEOLOCATION] ? null : locationFieldVisibility, // visibility is not taken into consideration for geolocations
        LOCATION_USAGE_OPTIONS.CAREER_PREFERENCES,
        countries
      );
    const displayedLocations = avLocations.map(mapFormatFn).filter(l => !!l);
    const label = displayedLocations.shift();
    const tooltip = `<ul>${displayedLocations.map(el => '<li>' + el + '</li>').join('')}</ul>`;

    return (
      <span>
        {label}{' '}
        {displayedLocations.length > 0 && (
          <Tooltip
            message={tooltip}
            pos="top"
            isHtmlIncluded={true}
            customClass={`${ROOT_CLASS}--tooltip-locations`}
          >
            +{' '}
            <button className="ed-link-secondary">
              {translatr('web.common.main', 'LengthofremainingskillsMore', {
                lengthOfRemainingSkills: displayedLocations.length
              })}
            </button>
          </Tooltip>
        )}
      </span>
    );
  };

  const renderSkillsRow = (
    {
      status: { value: statusKey },
      name,
      profileLevel,
      opportunityLevel,
      missingDetected,
      externalData
    },
    tab,
    idx
  ) => {
    return [
      {
        id: `${tab}_${idx}_1`,
        children: !isMfeEnabled() ? (
          <div>
            {name}{' '}
            {missingDetected && (
              <Tooltip
                message={translatr('web.talentmarketplace.main', 'MatchesYourProfile')}
                pos="top"
                customClass={`${ROOT_CLASS}--tooltip`}
              >
                <span>
                  <i className="card-icon icon-star" />
                </span>
              </Tooltip>
            )}
          </div>
        ) : (
          <>
            <Button
              color="primary"
              variant="borderless"
              padding="small"
              onClick={() => {
                openSkillFlyout(externalData);
              }}
            >
              {name}{' '}
            </Button>
            {missingDetected && (
              <Tooltip
                message={translatr('web.talentmarketplace.main', 'MatchesYourProfile')}
                pos="top"
                customClass={`${ROOT_CLASS}--tooltip`}
              >
                <span>
                  <i className="card-icon icon-star" />
                </span>
              </Tooltip>
            )}
          </>
        )
      },
      {
        id: `${tab}_${idx}_2`,
        children: <p>{renderStatusCell(STATUS_DETAILS.get(statusKey), tab)}</p>
      },
      {
        id: `${tab}_${idx}_3`,
        children: (
          <p>
            {profileLevel?.label ||
              (profileLevel?.noLevel ? noLevelPlaceholderOption().label : '-')}
          </p>
        )
      },
      {
        id: `${tab}_${idx}_4`,
        children: (
          <p>
            {opportunityLevel?.label ||
              (opportunityLevel?.noLevel ? noLevelPlaceholderOption().label : '- ')}
          </p>
        )
      }
    ];
  };

  const renderOrganizationsValues = orgs => {
    if (!orgs?.length) return;
    const label = orgs.shift().title;
    const tooltip = `<ul>${orgs.map(el => '<li>' + el.title + '</li>').join('')}</ul>`;

    return (
      <span>
        {label}{' '}
        {orgs.length > 0 && (
          <Tooltip
            message={tooltip}
            pos="top"
            isHtmlIncluded={true}
            customClass={`${ROOT_CLASS}--tooltip-locations`}
          >
            +{' '}
            <button className="ed-link-secondary">
              {translatr('web.common.main', 'LengthofremainingskillsMore', {
                lengthOfRemainingSkills: orgs.length
              })}
            </button>
          </Tooltip>
        )}
      </span>
    );
  };

  const renderPreferencesValues = (key, value, values) => {
    switch (key) {
      case 'locations':
        return renderLocationsValues(values || []);
      case 'organizations':
        return renderOrganizationsValues(values);
      default:
        return preferences
          .get(jobType)
          .get(key)
          .data.get(value);
    }
  };

  const renderPreferences = (
    { key, value, values, status: { value: statusKey }, label },
    tab,
    idx
  ) => {
    // don't show preferences if it is not defined for opportunity or preferences are not available, i.e. locations
    if ((!value && (!values || values.length === 0)) || !preferences.get(jobType).get(key)) {
      return;
    }
    return [
      {
        id: `${tab}_${idx}_1`,
        children: (
          <p>
            <span className="cell-title">
              {label ||
                (Array.isArray(preferences.get(jobType).get(key)?.label)
                  ? translatr(...preferences.get(jobType).get(key)?.label)
                  : preferences.get(jobType).get(key)?.label)}
              :{' '}
            </span>
            {renderPreferencesValues(key, value, values)}
          </p>
        )
      },
      {
        id: `${tab}_${idx}_2`,
        children: <p>{renderStatusCell(STATUS_DETAILS.get(statusKey), tab)}</p>
      }
    ];
  };

  const renderExperience = ({ status: { value: statusKey } }, tab, idx) => {
    return [
      {
        id: `${tab}_${idx}_1`,
        children: (
          <p>
            {translatr('web.talentmarketplace.main', 'ExperienceExplanations', {
              opportunity: omp(`tm_${jobType}`)
            })}
          </p>
        )
      },
      {
        id: `${tab}_${idx}_2`,
        children: <p>{renderStatusCell(STATUS_DETAILS.get(statusKey), tab)}</p>
      }
    ];
  };

  const renderCareerPath = (
    { aspirationalRoles = [], status: { value: statusKey } },
    tab,
    idx,
    type
  ) => {
    return [
      {
        id: `${tab}_${idx}_1`,
        children:
          type === JOB_TYPE.VACANCY ? (
            <button className={`${ROOT_CLASS}--careerpath-cell`}>
              {translatr(
                'web.talentmarketplace.main',
                'CareerPathExplanationsVacancyConfigurable',
                {
                  tm_aspirational_roles: omp('tm_tm_aspirational_roles')
                }
              )}
              : {aspirationalRoles.map(role => role.roleName).join(', ')}
            </button>
          ) : (
            <p>{translatr('web.talentmarketplace.main', 'CareerPathExplanationsRole')}</p>
          )
      },
      {
        id: `${tab}_${idx}_2`,
        children: <p>{renderStatusCell(STATUS_DETAILS.get(statusKey), tab)}</p>
      }
    ];
  };

  const TABLE_ROW_RENDERER = new Map([
    [TAB_KEYS.SKILLS, renderSkillsRow],
    [TAB_KEYS.PREFERENCES, renderPreferences],
    [TAB_KEYS.EXPERIENCE, renderExperience],
    [TAB_KEYS.CARRERPATH, renderCareerPath]
  ]);

  return (
    <Modal className={ROOT_CLASS}>
      <FocusLock
        onDeactivation={() => {
          openedFromHtmlElement?.focus();
        }}
      >
        <ModalHeader
          title={translatr('web.talentmarketplace.main', 'MatchingAnalysis')}
          onClose={closeModal}
        />
        <ModalContent>
          {isLoading && (
            <div className={`${ROOT_CLASS}--loader`}>
              <Spinner />
            </div>
          )}
          {!isLoading && (
            <div>
              <h3 className={`${ROOT_CLASS}--description`}>
                <img src={`data:image/svg+xml;utf8,${encodeURIComponent(svgIcon)}`} alt="" />{' '}
                <span>{statusLevel}</span>
              </h3>
              <div
                className={`${ROOT_CLASS}-tabs`}
                role="tablist"
                tabIndex="-1"
                onKeyDown={handleKeyDown}
              >
                {Array.from(tabs).map(([i, tab]) => {
                  return (
                    <button
                      key={`tab${i}`}
                      className={`${ROOT_CLASS}-tab status-${(
                        tab?.status || MATCH_STATUS.UNKNOWN
                      ).toLowerCase()}`}
                      id={`tab_${tab.key}`}
                      role="tab"
                      aria-controls="matchmodal_content_panel"
                      aria-selected={activeTab === tab.key}
                      tabIndex={tab.key === activeTab ? 0 : -1}
                      ref={el => tabRefs.current.set(tab.key, el)}
                      onClick={() => setActiveTab(tab.key)}
                    >
                      <div className={`ed-progress-tab ${activeTab === tab.key ? 'active' : ''}`}>
                        <div className={`progress-tab-icon primary-color ${tab.icon}`}></div>
                        <label className="title">{translatr(...tab.title)}</label>
                        <label className="desc">
                          {translatr(
                            ...tab.description,
                            tab.key === TAB_KEYS.SKILLS
                              ? { matched: skillsMatchedCnt, all: skillsCnt }
                              : { opportunity: omp(`tm_${jobType}`) }
                          )}
                        </label>
                      </div>
                    </button>
                  );
                })}
              </div>
              <div
                className={`${ROOT_CLASS}-table-container table-${activeTab}`}
                role="tabpanel"
                aria-labelledby={`tab_${activeTab}`}
                id="matchmodal_content_panel"
              >
                {tabs.get(activeTab)?.status !== MATCH_STATUS.UNKNOWN ? (
                  <Table headers={tableHeaders} rows={matchDetails.get(activeTab)}></Table>
                ) : (
                  <>
                    {tabs.get(activeTab)?.hasPermissionToUpdate && (
                      <div className={`${ROOT_CLASS}-nodata-container`}>
                        <span>{translatr(...tabs.get(activeTab).missingDataText)}</span>
                        <Button onClick={updateDataHandler} color="primary">
                          {translatr(...tabs.get(activeTab).addButtonLabel)}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
              {tabs.get(activeTab)?.status !== MATCH_STATUS.UNKNOWN &&
                tabs.get(activeTab)?.updateButtonLabel &&
                tabs.get(activeTab)?.hasPermissionToUpdate && (
                  <div className={`${ROOT_CLASS}-update-button-wrapper`}>
                    <Button
                      onClick={updateDataHandler}
                      variant="borderless"
                      color="primary"
                      padding="xsmall"
                    >
                      {translatr(...tabs.get(activeTab).updateButtonLabel)}
                    </Button>
                  </div>
                )}
            </div>
          )}
        </ModalContent>
        {themeId === ThemeId.PICASSO && (
          <ModalFooter>
            <Button onClick={closeModal} color="secondary" variant="ghost">
              {translatr('web.talentmarketplace.main', 'Close')}
            </Button>
          </ModalFooter>
        )}
      </FocusLock>
    </Modal>
  );
};

MatchModal.propTypes = {
  jobId: PropTypes.string,
  jobType: PropTypes.string,
  scoreObject: PropTypes.object,
  currentUserLang: PropTypes.string,
  isCareerPathEnabled: PropTypes.bool,
  closeModal: PropTypes.func,
  preferences: PropTypes.object,
  locationFieldVisibility: PropTypes.object,
  organizations: PropTypes.object,
  savePreferences: PropTypes.func,
  opencompleteYourProfileModal: PropTypes.func,
  locationsEnabled: PropTypes.bool,
  openedFromHtmlElement: PropTypes.object
};

const mapStateToProps = ({
  modal,
  currentUser,
  team,
  careerPreferences,
  locationsConfiguration,
  organizations
}) => ({
  jobId: modal.get('jobId'),
  jobType: modal.get('jobType'),
  scoreObject: modal.get('scoreObject'),
  isCareerPathEnabled: modal.get('isCareerPathEnabled'),
  preferences: careerPreferences.get('enabledCareerPreferencesOptions'),
  locationFieldVisibility: locationsConfiguration.get('visibility'),
  locationsEnabled: locationsConfiguration.get('enable'),
  currentUserLang:
    currentUser.get('profile')?.get?.('language') ||
    currentUser.get('profile')?.language ||
    team?.get('config')?.DefaultOrgLanguage ||
    'en',
  organizations,
  openedFromHtmlElement: modal.get('openedFromHtmlElement')
});

const mapDispatchToProps = dispatch => ({
  closeModal: () => dispatch(close()),
  savePreferences: (locations, orgEnable) =>
    dispatch(saveEnabledCareerPreferenceOptions(locations, orgEnable)),
  opencompleteYourProfileModal: ({ active_tab, openedFromHtmlElement }) =>
    dispatch(openCompleteYourProfileModalv2({ active_tab, openedFromHtmlElement }))
});

export default connect(mapStateToProps, mapDispatchToProps)(MatchModal);
