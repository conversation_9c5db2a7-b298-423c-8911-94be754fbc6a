@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-dialog-modal .ed-dialog-modal-wrapper.small .content:has(.skills-modal) {
  max-width: 45rem;
}

.skills-modal {
  color: var(--ed-text-color-primary);

  .ed-dialog-modal-header {
    height: 63px;
  }

  .ed-select {
    padding-right: 3rem;
  }
  .ed-select-bg {
    background: transparent;
    height: auto;
    min-height: auto;
  }

  & .confirm-msg &-title {
    font-size: rem-calc(18);
    margin: rem-calc(22) 0 0;
  }

  & &-list {
    margin: rem-calc(19) 0 0 0;
    padding-right: rem-calc(3);
    max-height: 50vh;
    overflow-y: auto;
    &-item {
      display: flex;
      justify-content: space-between;
      padding: var(--ed-spacing-sm) 0;
      .skill-title {
        display: flex;
        align-items: center;
      }
      .skill-icon {
        font-size: rem-calc(22);
        padding-right: var(--ed-spacing-2xs);
        color: var(--ed-state-disabled-color);
      }
      .ed-link-secondary:hover {
        background-color: transparent;
      }
      &--declared {
        .skill-icon {
          color: $darkgreen;
        }
      }
    }
  }

  .ed-table-wrapper {
    max-height: rem-calc(390);
    thead tr th:first-child {
      position: sticky !important;
      left: auto !important;
      label div,
      label span {
        border-bottom: none;
      }
    }

    th,
    td {
      > div {
        display: flex;
        border-right: none;
        border-bottom: none;
        min-height: rem-calc(64);
        max-height: unset;
        height: auto;
      }
      .checkbox {
        align-self: center;
        margin-bottom: 0;
      }
      .ed-checkbox-label {
        margin-left: var(--ed-spacing-xs);
        word-break: break-word;
      }
    }

    th,
    td {
      position: static;
      border-bottom: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    }

    th {
      position: sticky;
      left: auto !important;
      top: 0;
      z-index: 4;
      p {
        font-weight: var(--ed-font-weight-semibold);
      }
    }

    td:first-child > div,
    th:first-child > div {
      border-right: none;
    }

    tbody td:first-child,
    tbody th:first-child {
      position: relative;
    }
  }

  [role='button'] {
    position: relative;
  }

  .ed-tooltip.right {
    position: static;
    .tooltip-msg {
      right: 0;
      left: auto;
      width: 54%;
    }
  }

  .ed-table-wrapper td:first-child > div,
  .ed-table-wrapper th:first-child > div {
    min-width: auto;
  }

  .ed-table-wrapper th:nth-child(3) {
    min-width: rem-calc(194);
  }

  .tooltip-msg {
    min-width: rem-calc(200);
  }

  .spinner {
    text-align: center;
    margin: 5rem;
  }

  &__preselected-skill {
    pointer-events: none;
  }

  .icon-info-circle {
    font-size: var(--ed-font-size-base) !important;
  }
  .warning-icon {
    padding-right: var(--ed-spacing-xs);
  }
  .preselected-skill-icon {
    margin-left: var(--ed-spacing-3xs);
  }
}
