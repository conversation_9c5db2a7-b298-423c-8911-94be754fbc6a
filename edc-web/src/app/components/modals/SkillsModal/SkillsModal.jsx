import React, { useMemo, useState, useEffect, useCallback, useLayoutEffect } from 'react';
import { func, array, string, number, bool, object } from 'prop-types';
import { connect } from 'react-redux';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import FocusLock from 'react-focus-lock';
import { open_v2 as openSnackBar } from '../../../../app/actions/snackBarActions';
import { Mo<PERSON>Header, ModalFooter } from 'centralized-design-system/src/Modals';
import { close } from '../../../../app/actions/modalActions';
import { updateSkillPassport, postSkillPassport } from 'edc-web-sdk/requests/users';
import './SkillsModal.scss';
import Table from 'centralized-design-system/src/Table';
import Loader from '@components/Loader/Loader';
import {
  updateSkills as updateSkillsHandler,
  getUserSkills,
  updateUserInterest,
  changeReceiveSkillsLoadedStatus
} from '../../../../app/actions/usersActions';
import {
  preselectGoalsLevels,
  preselectExpertsLevels,
  findUserSkill,
  findUserGoal,
  saveUserGoals,
  saveSkillsToPassport,
  sortSkillsByUserSkills,
  sortSkillsByPreselected,
  translateInterestsLabel,
  mapSkillsWithDefaultLevel,
  resizeListener
} from './helpers';
import { getEcsOpportunitiesInfo } from '../../../../app/actions/teamActions';
import { SKILLS_MODAL_TYPE } from './constant';
import TableHeaders from './components/TableHeaders';
import TableRows from './components/TableRows';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { noLevelPlaceholderOption } from 'centralized-design-system/src/Utils/proficiencyLevels';
import GoalsModalContentForAspirational from './components/GoalsModalContentForAspirational/GoalsModalContentForAspirational';
import { Button } from 'centralized-design-system/src/Buttons';

const onCloseModal = () => {
  return dispatch => {
    dispatch(changeReceiveSkillsLoadedStatus(false));
    dispatch(close());
  };
};

const SkillsModal = ({
  currentUserId,
  profileId,
  type = SKILLS_MODAL_TYPE.EXPERTS,
  skills,
  isUserSkillsLoaded = false,
  userSkills,
  userGoals,
  labels,
  currentUserLang,
  closeModal,
  toast,
  getCurrentUserSkills,
  updateUserGoals,
  onSkillAddedToPassport,
  openedFrom,
  skillsFrom,
  updateSkills,
  ecsVacancyConfig,
  getEcsOpportunitiesConfig,
  goalsLimit,
  additionalProps,
  openedFromHtmlElement
}) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [skillsToShow, setSkillsToShow] = useState(skills);
  const [showPreselectedSkillTooltip, setShowPreselectedSkillTooltip] = useState(false);

  const sortSkills =
    type === SKILLS_MODAL_TYPE.EXPERTS ? sortSkillsByUserSkills : sortSkillsByPreselected;

  const escFunction = useCallback(
    e => {
      if (e.key === 'Escape') {
        const openTooltip = document.getElementById('preselected-skill-info');
        if (openTooltip) {
          e.stopPropagation();
          e.preventDefault();
          setShowPreselectedSkillTooltip(false);
        } else {
          closeModal();
        }
      }
    },
    [closeModal]
  );

  useEffect(() => {
    document.addEventListener('keydown', escFunction, true);
    return () => {
      document.removeEventListener('keydown', escFunction, true);
    };
  }, [escFunction]);

  useEffect(() => {
    document.getElementsByClassName('ed-skills-dialog-modal-overlay')[0]?.remove();
    if (openedFrom === 'matchingSkills') {
      return;
    }
    if (!ecsVacancyConfig) {
      getEcsOpportunitiesConfig();
    }
    getCurrentUserSkills(currentUserId);
  }, []);

  useEffect(() => {
    if (!ecsVacancyConfig) return;
    const { detected_skills_level: detectedSkillsLevel } = ecsVacancyConfig;

    const skillsWithDefaultLevel = mapSkillsWithDefaultLevel({
      skills,
      skillsFrom,
      defaultLevel: detectedSkillsLevel,
      currentUserLang
    });

    if (type === SKILLS_MODAL_TYPE.EXPERTS) {
      preselectExpertsLevels({
        skills: skillsWithDefaultLevel,
        userSkills,
        skillsFrom,
        currentUserLang,
        setSkillsToShow,
        setLoading
      });
    } else {
      preselectGoalsLevels({
        skills: skillsWithDefaultLevel,
        userSkills,
        userGoals,
        skillsFrom,
        openedFrom,
        currentUserLang,
        setSkillsToShow,
        setLoading
      });
    }

    if (isUserSkillsLoaded && !!ecsVacancyConfig) {
      setLoading(false);
    }
  }, [userSkills, ecsVacancyConfig]);

  const hasUserAnyOfSkills = useMemo(
    () =>
      userSkills?.length &&
      skillsToShow
        .filter(({ isChecked }) => isChecked)
        .some(skill => findUserSkill(userSkills, skill.id)),
    [skillsToShow, userSkills]
  );

  const hasUserAnyOfGoals = useMemo(
    () =>
      userGoals?.length &&
      skillsToShow
        .filter(({ isChecked }) => isChecked)
        .some(skill => findUserGoal(userGoals, skill.id)),
    [skillsToShow, userGoals]
  );

  const isEachSkillChecked = useMemo(
    () => skillsToShow.filter(({ disabled }) => !disabled).every(({ isChecked }) => isChecked),
    [skillsToShow]
  );

  const isAnySkillChecked = useMemo(
    () => skillsToShow.filter(({ disabled }) => !disabled).some(({ isChecked }) => isChecked),
    [skillsToShow]
  );

  const isAnySkillCheckedAndNotDefined = useMemo(
    () =>
      skillsToShow.some(
        ({ targetLevel, isChecked }) =>
          isChecked && (!targetLevel || targetLevel === noLevelPlaceholderOption().value)
      ),
    [skillsToShow]
  );

  const toggleAll = useCallback(() => {
    const updatedSkills = skillsToShow.map(skill => ({
      ...skill,
      isChecked: skill.isPreselected || !isEachSkillChecked
    }));
    setSkillsToShow(updatedSkills);
  }, [skillsToShow, isEachSkillChecked]);

  const toggleSingle = useCallback(
    (id, isChecked) => {
      const index = skillsToShow.findIndex(({ id: IdToCheck }) => IdToCheck === id);
      const result = [...skillsToShow];
      result[index] = { ...skillsToShow[index], isChecked: !isChecked };
      setSkillsToShow(result);
    },
    [skillsToShow]
  );

  const setLevel = (id, targetLevel) => {
    const result = skillsToShow.map(skill =>
      parseInt(id) === parseInt(skill.id) ? { ...skill, targetLevel } : skill
    );
    setSkillsToShow(result);
  };

  useEffect(() => {
    window.addEventListener('resize', resizeListener);
    return () => {
      window.removeEventListener('resize', resizeListener);
    };
  }, []);

  useLayoutEffect(() => {
    window.dispatchEvent(new Event('resize'));
  });

  const existingUserSkills = userSkills?.filter(({ taxonomyDetails }) => !!taxonomyDetails);

  const predicate = type === SKILLS_MODAL_TYPE.EXPERTS ? hasUserAnyOfSkills : hasUserAnyOfGoals;
  const actionButtonLabel = predicate
    ? translatr('web.common.main', 'Save')
    : translatr('web.common.main', 'Add');

  const interestsLabel = translateInterestsLabel(labels, currentUserLang);

  const countAllGoalsToSave = () => {
    if (type !== SKILLS_MODAL_TYPE.GOALS) return -1;

    const goalsMap = {};
    skillsToShow
      .filter(({ isChecked, disabled }) => isChecked && !disabled)
      .forEach(({ id }) => (goalsMap[id] = true));
    userGoals.forEach(({ topic_id }) => (goalsMap[topic_id] = true));

    return Object.keys(goalsMap).length;
  };

  const allGoalsToSaveNumber = countAllGoalsToSave();

  const getModalTitle = () => {
    if (openedFrom === 'aspirational-role-switch') {
      return translatr('web.common.main', 'OpportunityMarkedAsAspirationalConfigurable', {
        opportunity: omp(`tm_job_role`),
        tm_aspirational_role: omp('tm_tm_aspirational_role')
      });
    }

    return type === SKILLS_MODAL_TYPE.EXPERTS
      ? translatr('web.common.main', 'AddToSkillsPassport')
      : translatr('web.common.main', 'SetYourInterests', {
          interests: interestsLabel
        });
  };

  const isSaveButtonDisabled =
    loading ||
    saving ||
    isAnySkillCheckedAndNotDefined ||
    !isAnySkillChecked ||
    allGoalsToSaveNumber > goalsLimit;

  return (
    <div className={`skills-modal ${type}`}>
      <FocusLock
        onDeactivation={() => {
          openedFromHtmlElement?.focus();
        }}
      >
        <ModalHeader id="skills-modal" title={getModalTitle()} onClose={closeModal} />
        <div className="vertical-spacing-large">
          <div className="confirm-msg">
            {openedFrom === 'aspirational-role-switch' && (
              <GoalsModalContentForAspirational jobRoleTitle={additionalProps.jobRole?.title} />
            )}
            {allGoalsToSaveNumber > goalsLimit && (
              <div className="goals-modal-content-for-aspirational__warning" role="alert">
                <span className="warning-icon icon-exclamation-circle"></span>
                {translatr('web.common.main', 'YouCanSelectAMaximumOfOnlyLimitTopics', {
                  limit: goalsLimit
                })}
              </div>
            )}
            {loading || saving || !skillsToShow.length ? (
              <div className="spinner" role="alert" aria-live="polite">
                <Loader center />
              </div>
            ) : (
              <Table
                headers={TableHeaders({ type, skillsFrom, isEachSkillChecked, toggleAll })}
                rows={TableRows({
                  type,
                  skillsToShow,
                  existingUserSkills,
                  currentUserLang,
                  openedFrom,
                  sortSkills,
                  toggleSingle,
                  setLevel,
                  showPreselectedSkillTooltip,
                  setShowPreselectedSkillTooltip
                })}
              />
            )}
          </div>
        </div>
        <ModalFooter>
          <Button
            color="secondary"
            variant="ghost"
            size="large"
            className="ed-btn ed-btn-neutral"
            onClick={closeModal}
            aria-label={translatr('web.common.main', 'Close')}
          >
            {translatr('web.common.main', 'Close')}
          </Button>
          <Button
            color="primary"
            size="large"
            onClick={
              type === SKILLS_MODAL_TYPE.EXPERTS
                ? () =>
                    saveSkillsToPassport({
                      setSaving,
                      skillsToShow,
                      userSkills,
                      closeModal,
                      toast,
                      updateSkillPassport,
                      postSkillPassport,
                      onSkillAddedToPassport,
                      updateSkills
                    })
                : () =>
                    saveUserGoals({
                      setSaving,
                      skillsToShow,
                      userGoals,
                      currentUserId,
                      profileId,
                      toast,
                      updateUserGoals,
                      closeModal
                    })
            }
            aria-label={actionButtonLabel}
            disabled={isSaveButtonDisabled}
          >
            {actionButtonLabel}
          </Button>
        </ModalFooter>
      </FocusLock>
    </div>
  );
};

SkillsModal.propTypes = {
  type: string,
  isUserSkillsLoaded: bool,
  currentUserId: string,
  profileId: number,
  skills: array,
  userSkills: array,
  userGoals: array,
  labels: object,
  currentUserLang: string,
  closeModal: func,
  onSkillAddedToPassport: func,
  updateUserGoals: func,
  toast: func,
  getCurrentUserSkills: func,
  openedFrom: string,
  skillsFrom: string,
  updateSkills: func,
  ecsVacancyConfig: object,
  getEcsOpportunitiesConfig: func,
  goalsLimit: number,
  additionalProps: object,
  openedFromHtmlElement: object
};

const mapStateToProps = ({ modal, currentUser, team }) => ({
  type: modal.get('modalType'),
  isUserSkillsLoaded: currentUser.get('isUserSkillsLoaded'),
  userSkills: currentUser.get('userSkills')?.skills || [],
  currentUserId: currentUser?.get('id'),
  profileId: currentUser.get('profile')?.get?.('id'),
  userGoals: currentUser
    .get('profile')
    .get('learningTopics')
    ?.toJS?.(),
  skills: modal.get('skills'),
  onSkillAddedToPassport: modal.get('onSkillAddedToPassport'),
  openedFrom: modal.get('openedFrom'),
  skillsFrom: modal.get('skillsFrom'),
  labels: team.get('OrgConfig')?.labels,
  currentUserLang:
    currentUser.get('profile')?.get?.('language') ||
    currentUser.get('profile')?.language ||
    team?.get('config')?.DefaultOrgLanguage ||
    'en',
  ecsVacancyConfig: team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY],
  goalsLimit: team.get('config')?.limit_options?.interests_limit,
  additionalProps: modal.get('additionalProps') || {},
  openedFromHtmlElement: modal.get('openedFromHtmlElement')
});

const mapDispatchToProps = dispatch => ({
  closeModal: () => dispatch(onCloseModal()),
  toast: (message, type = 'success') => dispatch(openSnackBar(message, type)),
  getCurrentUserSkills: currentUserId => dispatch(getUserSkills(currentUserId)),
  updateUserGoals: (skillArray, currentUserId, profileId) =>
    dispatch(updateUserInterest(skillArray, currentUserId, profileId)),
  updateSkills: skills => dispatch(updateSkillsHandler(skills)),
  getEcsOpportunitiesConfig: () => dispatch(getEcsOpportunitiesInfo())
});

export default connect(mapStateToProps, mapDispatchToProps)(SkillsModal);
