import { func, string, array } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import cn from 'classnames';

import Tooltip from 'centralized-design-system/src/Tooltip';
import Checkbox from 'centralized-design-system/src/Checkbox';
import { Select } from 'centralized-design-system/src/Inputs';

import { SKILLS_MODAL_TYPE } from '../constant';
import { noLevelPlaceholderOption } from 'centralized-design-system/src/Utils/proficiencyLevels';

import { isMfeEnabled, openSkillFlyout } from '@components/MfeSkillsFlyout';
import { Button } from 'centralized-design-system/src/Buttons';

const TableRows = ({
  type,
  skillsToShow,
  existingUserSkills,
  openedFrom,
  sortSkills,
  toggleSingle,
  setLevel,
  showPreselectedSkillTooltip,
  setShowPreselectedSkillTooltip
}) => {
  return sortSkills(skillsToShow, existingUserSkills).map(
    ({
      label,
      name,
      id,
      isChecked,
      proficiencyLevelObj,
      targetLevel,
      levels,
      disabled,
      isPreselected,
      externalData
    }) => {
      const noLevel = levels[0];
      const noLevelLabel = noLevel?.label || noLevel?.value;
      const noLevelName = noLevel?.value;
      const levelLabel = proficiencyLevelObj?.label;
      const isOpenedFromAspirational = openedFrom === 'aspirational-role-switch';
      const levelsToShown = levels?.slice(targetLevel === noLevelPlaceholderOption().value ? 0 : 1);

      if (disabled) {
        return [
          {
            colSpan: 3,
            children: (
              <Tooltip
                pos="right"
                hide={isOpenedFromAspirational}
                message={translatr('web.common.main', 'CantAddSkillBecauseUArchivedAdvanced')}
              >
                <span className="disabledColOne">
                  <Checkbox label={label || name || id} disabled />
                </span>
                <p className="disabledColTwo">{levelLabel || noLevelLabel}</p>
                <span className="disabledColThree">
                  <Select
                    items={levelsToShown}
                    defaultValue={targetLevel || noLevelName}
                    disabled
                  />
                </span>
              </Tooltip>
            )
          }
        ];
      }

      return [
        {
          children:
            !isPreselected || openedFrom === 'aspirational-role-switch' ? (
              <Checkbox
                checked={isChecked}
                onChange={() => toggleSingle(id, isChecked)}
                ariaLabel={label || name || id}
              />
            ) : (
              <>
                <Checkbox
                  className={cn('skills-modal__preselected-skill')}
                  checked={isChecked}
                  onChange={() => toggleSingle(id, isChecked)}
                  ariaLabel={label || name || id}
                  disabled
                />
              </>
            )
        },
        {
          children: (
            <>
              {isMfeEnabled() ? (
                <Button
                  color="primary"
                  variant="borderless"
                  padding="small"
                  onClick={() => {
                    openSkillFlyout(externalData);
                  }}
                >
                  {label || name || id}
                </Button>
              ) : (
                label || name || id
              )}

              {!(!isPreselected || openedFrom === 'aspirational-role-switch') && (
                <Tooltip
                  message={translatr('web.common.main', 'UCanRemoveSkillInSkillsPassport')}
                  pos={'bottom'}
                  show={showPreselectedSkillTooltip === id}
                  tooltipParentRole="tooltip"
                  id="preselected-skill-info"
                  ariaLabel="tooltip"
                >
                  <button
                    aria-label={`${label || name || id} ${translatr(
                      'web.talentmarketplace.main',
                      'PreselectedSkillRemovalInformation'
                    )}`}
                    className="icon-info-circle font-size-l preselected-skill-icon"
                    onFocus={() => setShowPreselectedSkillTooltip(id)}
                    aria-describedby="preselected-skill-info"
                  />
                </Tooltip>
              )}
            </>
          )
        },
        {
          children: (
            <p>
              {levelLabel || (
                <Tooltip pos="top" message={translatr('web.common.main', 'SelectCorrectLevel')}>
                  {noLevelLabel}
                </Tooltip>
              )}
            </p>
          )
        },
        {
          children: (
            <Tooltip
              pos="top"
              message={translatr('web.common.main', 'SelectCorrectLevel')}
              hide={targetLevel && targetLevel !== noLevelPlaceholderOption().value}
            >
              <Select
                items={levelsToShown}
                defaultValue={targetLevel || noLevelName}
                value={targetLevel || noLevelName}
                ariaLabel={
                  type === SKILLS_MODAL_TYPE.EXPERTS
                    ? `${translatr('web.common.main', 'YourLevel')} for ${label || name || id}`
                    : translatr('web.common.main', 'ValueTargetLevel', {
                        value: 'Learning'
                      })
                }
                id={id}
                isTranslated={true}
                disabled={!isChecked}
                onChange={value => setLevel(id, value.value)}
              />
            </Tooltip>
          )
        }
      ];
    }
  );
};

TableRows.propTypes = {
  type: string,
  skillsToShow: array,
  existingUserSkills: array,
  openedFrom: string,
  sortSkills: func,
  toggleSingle: func,
  setLevel: func
};

export default TableRows;
