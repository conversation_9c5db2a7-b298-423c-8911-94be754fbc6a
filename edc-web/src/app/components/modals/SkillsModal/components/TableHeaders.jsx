import React from 'react';
import { func, string, bool } from 'prop-types';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';

import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import Checkbox from 'centralized-design-system/src/Checkbox';

import { SKILLS_MODAL_TYPE } from '../constant';

const TableHeaders = ({ type, skillsFrom, isEachSkillChecked, toggleAll }) => {
  return [
    {
      children: (
        <Checkbox
          ariaLabel={translatr('web.common.main', 'SelectAllSkills')}
          checked={isEachSkillChecked}
          onChange={toggleAll}
        />
      )
    },
    {
      children: <p>{tr('Skill')}</p>
    },
    {
      children: (
        <p>
          {translatr('web.common.main', 'ValueTargetLevel', {
            value: skillsFrom === JOB_TYPE.VACANCY ? omp('tm_job_vacancy') : omp('tm_job_role')
          })}
        </p>
      )
    },
    {
      children: (
        <p>
          {type === SKILLS_MODAL_TYPE.EXPERTS
            ? translatr('web.common.main', 'YourLevel')
            : translatr('web.talentmarketplace.main', 'TargetLevel')}
        </p>
      )
    }
  ];
};

TableHeaders.propTypes = {
  type: string,
  skillsFrom: string,
  isEachSkillChecked: bool,
  toggleAll: func
};

export default TableHeaders;
