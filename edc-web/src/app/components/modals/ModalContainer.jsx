import React, { Component, lazy } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as modalTypes from '../../constants/modalTypes';
import { close } from '../../actions/modalActions';

import Dialog from '../Modal/Modal';
import ModalSuspenseV2 from './ModalV2Suspense';
import capture from '@utils/datalayer';
import LazyloadComponent from '@components/LazyloadComponent.js';
import withRouter from '@hocs/withRouter.js';
import { Translatr } from 'centralized-design-system/src/Translatr';

// Classic UI Modals(Pop-up) start
const GtcConfirmationModal = LazyloadComponent(() => import('./GtcConfirmationModal'))();
const UserNameModal = LazyloadComponent(() => import('./UserNameModal'))();
const PaypalSuccessModal = LazyloadComponent(() => import('./PaypalSuccessModal'))();
const InviteV2UserModal = LazyloadComponent(() => import('./InviteV2UserModal'))();
const SAInviteModal = LazyloadComponent(() => import('./SAInviteModal'))();
// Classic UI Modals(Pop-up) end

class ModalContainer extends Component {
  constructor(props, context) {
    super(props, context);
    this.styles = {
      dialogRoot: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1502,
        paddingTop: 0
      },
      dialogContent: {
        position: 'relative',
        width: '90vw',
        transform: 'none'
      },
      dialogBody: {
        paddingBottom: 0,
        overflowX: 'hidden'
      }
    };

    this.inlineCreationPathnames = ['/', '/required', '/featured', '/curate', '/teamLearning'];
  }

  componentDidMount() {
    window.addEventListener('keydown', this.closeModalOnESC);
  }

  closeModalOnESC = e => {
    let fileStackModalOpen = document.getElementsByClassName('fsp-picker').length;
    let cardTypePopupobj = document.getElementsByClassName('cardTypePopupIsActive').length;
    let type = this.props.type || this.props.typeBefore;
    const avoidCloseOnEscModalTypes = ['org_payment', 'user_name_modal', 'become_a_mentor_modal'];
    if (
      !avoidCloseOnEscModalTypes.includes(this.props.type) &&
      e.keyCode == 27 &&
      !fileStackModalOpen &&
      !cardTypePopupobj
    ) {
      if (this.props.type === 'skills_directory_modal' && !this.props.role) {
        this.props.dispatch(close());
        this.props.navigate(-1);
        return;
      }
      this.props.dispatch(close());
      if (
        type == modalTypes.SMARTBITE_CREATION ||
        type == modalTypes.PATHWAY_CREATION ||
        type == modalTypes.JOURNEY_CREATION
      ) {
        document.getElementsByClassName('createButton')[0].focus();
      }
    }
  };

  render() {
    let ComponentVar;
    let open = this.props.open || this.props.openCard || false;
    let customContentClass;
    let type = this.props.type || this.props.typeBefore;
    let externalCloseHandler = false;
    let DialogV2;
    let size = '';
    // eslint-disable-next-line sonarjs/max-switch-cases
    switch (type) {
      case modalTypes.ASSIGN:
        const AssignModal = lazy(() => import('@components/modals/AssignModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = (
          <ModalSuspenseV2>
            <AssignModal
              open={this.props.open}
              card={this.props.card}
              selfAssign={this.props.selfAssign}
              assignedStateChange={this.props.assignedStateChange}
              selfAssignedStateChange={this.props.selfAssignedStateChange}
              anchorRef={this.props.anchorRef}
            />
          </ModalSuspenseV2>
        );
        break;
      case modalTypes.CONFIRM:
        const ConfirmationModal = lazy(() => import('./ConfirmationModalV2.jsx'));
        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = (
          <ModalSuspenseV2>
            <ConfirmationModal
              title={this.props.title}
              message={this.props.message}
              hideConfirmBtn={!!this.props.hideConfirmBtn}
              callback={this.props.callback}
              hideCancelBtn={!!this.props.hideCancelBtn}
              confirmBtnTitle={this.props.confirmBtnTitle}
              isNegativeValue={this.props.isNegativeValue}
              isTranslated
              anchorRef={this.props.anchorRef}
            />
          </ModalSuspenseV2>
        );
        break;
      case modalTypes.CONFIRM_PRIVATE_CARD:
        const ConfirmationModalPrivate = lazy(() => import('./ConfirmationModalV2.jsx'));
        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = (
          <ModalSuspenseV2>
            <ConfirmationModalPrivate
              title={this.props.title}
              message={this.props.message}
              isPrivate={this.props.isPrivate}
              callback={this.props.callback}
              confirmBtnTitle={this.props.confirmBtnTitle}
              isTranslated
              isNegativeValue={this.props.isNegativeValue}
            />
          </ModalSuspenseV2>
        );
        break;
      case modalTypes.ADD_TO_PATHWAY:
        const AddToPathwayModal = lazy(() => import('@components/modals/AddToPathwayModal'));
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = (
          <ModalSuspenseV2>
            <AddToPathwayModal
              card={this.props.card}
              cardId={this.props.cardId}
              cardType={this.props.cardType}
              isAddingToJourney={this.props.isAddingToJourney}
            />
          </ModalSuspenseV2>
        );
        break;
      case modalTypes.ADD_TO_JOURNEY:
        const AddToJourneyModal = lazy(() => import('@components/modals/AddToJourneyModal'));
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = (
          <ModalSuspenseV2>
            <AddToJourneyModal cardId={this.props.cardId} />;
          </ModalSuspenseV2>
        );
        break;
      case modalTypes.INLINE_CREATION:
        ComponentVar = <InlineCreation expand={true} />;
        open = true;
        break;
      case modalTypes.INVITE_V2_USER_TO_GROUP:
        ComponentVar = <InviteV2UserModal />;
        customContentClass = 'group-invite-dialog';
        break;
      case modalTypes.SA_INVITE_MODAL:
        ComponentVar = <SAInviteModal />;
        open = true;
        break;
      case modalTypes.CONGRATULATION_MODAL:
        // Launches badge modal
        const CongratulationModal = lazy(() => import('@components/modals/BadgeModal'));
        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        capture('Pathway Badge Earned', this.props.userBadge);
        ComponentVar = (
          <ModalSuspenseV2>
            <CongratulationModal userBadge={this.props.userBadge} />
          </ModalSuspenseV2>
        );
        open = true;
        break;
      case modalTypes.POST_TO_CHANNEL_MODAL:
        const PostToChannelModal = lazy(() =>
          import('@components/modals/PostToChannel/PostToChannelModal')
        );
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = (
          <Translatr apps={['web.content.main']}>
            <ModalSuspenseV2>
              <PostToChannelModal card={this.props.card} />
            </ModalSuspenseV2>
          </Translatr>
        );
        break;
      case modalTypes.PAYPAL_SUCCESS_MODAL:
        ComponentVar = <PaypalSuccessModal paymentData={this.props.paymentData} />;
        break;
      case modalTypes.METRANET_PAYMENT_STATUS_MODAL:
        ComponentVar = <MetranetPaymentStatusModal paymentData={this.props.paymentData} />;
        externalCloseHandler = true;
        break;
      case modalTypes.SKILLS_DIRECTORY_MODAL:
        const SkillsDirectoryModal = lazy(() => import('@components/modals/SkillsDirectoryModal'));
        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = (
          <ModalSuspenseV2>
            <SkillsDirectoryModal
              changeRole={this.props.changeRole}
              role={this.props.role}
              isSettingsPage={this.props.isSettingsPage}
              isCareerPathPage={this.props.isCareerPathPage}
            />
          </ModalSuspenseV2>
        );
        open = true;
        externalCloseHandler = true;
        break;
      case modalTypes.USER_NAME_MODAL:
        ComponentVar = <UserNameModal />;
        externalCloseHandler = true;
        break;
      case modalTypes.UPLOAD_MODAL:
        const UploadModal = lazy(() => import('@components/modals/Upload'));
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <UploadModal uploadMeta={this.props.uploadMeta} />;
        externalCloseHandler = true;
        break;
      case modalTypes.GTC_CONFIRMATION_MODAL:
        ComponentVar = <GtcConfirmationModal />;
        externalCloseHandler = true;
        break;
      case modalTypes.BECOME_A_MENTOR_MODAL:
        const BecomeAMentorModal = lazy(() => import('@components/modals/BecomeAMentorModal'));

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <BecomeAMentorModal />;
        break;
      case modalTypes.BECOME_A_MENTOR_CONFIRMATION_MODAL:
        const BecomeAMentorConfirmationModal = lazy(() =>
          import('@components/modals/BecomeAMentorConfirmationModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <BecomeAMentorConfirmationModal />;
        break;
      case modalTypes.VIEW_COMMENT_MODAL:
        const ViewCommentModal = lazy(() => import('@components/modals/ViewCommentModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <ViewCommentModal />;
        break;

      case modalTypes.REJECTION_COMMENT_MODAL:
        const RejectionCommentModal = lazy(() =>
          import('@components/modals/RejectionCommentModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <RejectionCommentModal />;
        break;

      case modalTypes.ACCEPT_MENTORSHIP_WITH_DURATION_MODAL:
        const AcceptMentorshipWithDurationModal = lazy(() =>
          import('@components/modals/AcceptMentorshipWithDurationModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <AcceptMentorshipWithDurationModal />;
        break;

      case modalTypes.WITHDRAW_COMMENT_MODAL:
        const WithdrawCommentModal = lazy(() => import('@components/modals/WithdrawCommentModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <WithdrawCommentModal />;
        break;
      case modalTypes.REQUEST_MENTORSHIP_MODAL:
        const RequestMentorshipModal = lazy(() => import('./RequestMentorshipModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <RequestMentorshipModal />;
        break;
      case modalTypes.SKILLS_MODAL:
        const SkillsModal = lazy(() => import('@components/modals/SkillsModal/SkillsModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = <SkillsModal />;
        break;
      case modalTypes.USER_SKILLS_ASSESSMENT_MODAL:
        const UserSkillsAssessmentModal = lazy(() =>
          import('@components/modals/UserSkillsAssessmentModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = <UserSkillsAssessmentModal />;
        break;

      case modalTypes.REVIEW_ASSESSMENT_MODAL:
        const SkillRatingReviewModal = lazy(() =>
          import('@components/modals/SkillRatingReviewModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = <SkillRatingReviewModal />;
        break;
      case modalTypes.OMP_USER_NOTIFICATION_MODAL:
        const OmpUserNotificationModal = lazy(() =>
          import('@components/modals/OmpUserNotificationModal/OmpUserNotificationModal.jsx')
        );

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));
        ComponentVar = <OmpUserNotificationModal />;
        break;
      case modalTypes.OMP_SHARE_MODAL:
        const ShareModal = lazy(() => import('@pages/TalentMarketplace/shared/ShareModal.jsx'));

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <ShareModal />;
        break;
      case modalTypes.OMP_SIMPLIFIED_SHARE_MODAL:
        const SimplifiedShareModal = lazy(() =>
          import('@components/modals/ShareModal/SimplifiedShareModal.jsx')
        );

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <SimplifiedShareModal />;
        break;
      case modalTypes.VIEW_MESSAGE_MODAL:
        const ViewMessageModal = lazy(() => import('@components/modals/ViewMessageModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <ViewMessageModal />;
        break;

      case modalTypes.SUBSCRIBED_USER_MODAL:
        const SubscribedUserModal = lazy(() => import('@components/modals/SubscribedUserModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <SubscribedUserModal />;
        break;

      case modalTypes.MATCHING_SKILLS_MODAL:
        const MatchingSkillsModal = lazy(() => import('@components/modals/MatchingSkillsModal'));

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <MatchingSkillsModal />;
        break;

      case modalTypes.CAREER_PATH_FILTER:
        const CareerPathingFilters = lazy(() =>
          import('@pages/TalentMarketplace/CareerPathing/CareerPathingFilters')
        );

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <CareerPathingFilters />;
        break;

      case modalTypes.MATCH_MODAL:
        const MatchModal = lazy(() => import('@components/modals/MatchModal/MatchModal'));

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <MatchModal />;
        break;
      case modalTypes.RECOMMENDATION_FEEDBACK_MODAL:
        const RecommendationFeedbackModal = lazy(() =>
          import('@components/modals/RecommendationModal/RecommendationModal')
        );

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <RecommendationFeedbackModal />;
        break;
      case modalTypes.CREATE_OPPORTUNITY_ALERT_MODAL:
        const CreateOpportunityAlertModal = lazy(() =>
          import('@components/modals/CreateOpportunityAlertModal/CreateOpportunityAlertModal')
        );

        size = 'medium';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <CreateOpportunityAlertModal />;
        break;

      case modalTypes.MANAGER_SKILL_ASSESSMENT_MODAL:
        const ManagerSkillAssessmentModal = lazy(() =>
          import('@components/modals/ManagerSkillAssessmentModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <ManagerSkillAssessmentModal />;
        break;

      case modalTypes.MANAGER_RECOMMEND_UPSKILL_MODAL:
        const ManagerRecommendUpSkillModal = lazy(() =>
          import('@components/modals/ManagerRecommendUpSkillModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <ManagerRecommendUpSkillModal />;
        break;

      case modalTypes.COMPLETE_YOUR_PROFILE_MODAL:
        const CompleteYourProfileModal = lazy(() =>
          import('@components/modals/CompleteYourProfileModal')
        );

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = (
          <Translatr apps={['web.talentmarketplace.lov-labels-default']}>
            <CompleteYourProfileModal />
          </Translatr>
        );
        break;
      case modalTypes.APPROVERS_DETAILS_MODAL:
        const ApproverDetailsModal = lazy(() => import('@components/modals/ApproverDetailsModal'));

        size = 'small';
        DialogV2 = lazy(() => import('@components/modals/Modal'));

        ComponentVar = <ApproverDetailsModal />;
        break;
      default:
        ComponentVar = null;
        break;
    }

    return (
      <div>
        {DialogV2 ? (
          <ModalSuspenseV2>
            <DialogV2 open={open} size={size}>
              {ComponentVar}
            </DialogV2>
          </ModalSuspenseV2>
        ) : (
          <Dialog
            open={open}
            className={customContentClass}
            externalCloseHandler={externalCloseHandler}
          >
            {ComponentVar}
          </Dialog>
        )}
      </div>
    );
  }
}

ModalContainer.propTypes = {
  defaultTopic: PropTypes.object,
  isPrivate: PropTypes.bool,
  comment: PropTypes.object,
  metric: PropTypes.any,
  modalState: PropTypes.any,
  authorName: PropTypes.string,
  toggleChannelModal: PropTypes.func,
  dismissible: PropTypes.bool,
  ratingCount: PropTypes.any,
  handleCardClicked: PropTypes.func,
  standaloneLinkClickHandler: PropTypes.func,
  rateCard: PropTypes.any,
  providerLogos: PropTypes.object,
  groupId: PropTypes.any,
  type: PropTypes.string,
  typeBefore: PropTypes.string,
  cardTypes: PropTypes.string,
  pathname: PropTypes.string,
  channelId: PropTypes.any,
  assignedStateChange: PropTypes.any,
  selfAssignedStateChange: PropTypes.func,
  dueAt: PropTypes.string,
  startDate: PropTypes.string,
  currentUserId: PropTypes.string,
  userType: PropTypes.string,
  cardType: PropTypes.string,
  title: PropTypes.string,
  message: PropTypes.string,
  label: PropTypes.string,
  description: PropTypes.string,
  typeOfModal: PropTypes.string,
  statusBlock: PropTypes.string,
  statusMessage: PropTypes.string,
  defaultImage: PropTypes.string,
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  cardId: PropTypes.string,
  checkedCardId: PropTypes.number,
  openCard: PropTypes.bool,
  isReject: PropTypes.bool,
  selfAssign: PropTypes.bool,
  isViewsModal: PropTypes.bool,
  isAddingToJourney: PropTypes.bool,
  isStandaloneModal: PropTypes.bool,
  skill: PropTypes.object,
  card: PropTypes.object,
  curators: PropTypes.any,
  channel: PropTypes.object,
  group: PropTypes.object,
  logoObj: PropTypes.object,
  integration: PropTypes.object,
  channelSetting: PropTypes.bool,
  groupSetting: PropTypes.bool,
  userBadge: PropTypes.object,
  rejectCallback: PropTypes.func,
  deleteSharedCard: PropTypes.any,
  updateGroupsList: PropTypes.func,
  cardUpdated: PropTypes.func,
  callback: PropTypes.func,
  openChannelModal: PropTypes.func,
  handleCloseModal: PropTypes.func,
  importClickHandler: PropTypes.func,
  open: PropTypes.bool,
  courseStatus: PropTypes.string,
  courseType: PropTypes.string,
  currentAction: PropTypes.string,
  isInitilized: PropTypes.bool,
  paymentData: PropTypes.object,
  removable: PropTypes.bool,
  hideCancelBtn: PropTypes.bool,
  hideConfirmBtn: PropTypes.bool,
  removeCardFromCardContainer: PropTypes.func,
  removeCardFromList: PropTypes.func,
  cardSectionName: PropTypes.any,
  bookmarked: PropTypes.object,
  cardModalDetails: PropTypes.any,
  cards: PropTypes.any,
  confirmHandler: PropTypes.func,
  changeRole: PropTypes.func,
  unRegister: PropTypes.func,
  role: PropTypes.string,
  isSettingsPage: PropTypes.bool,
  sourceMode: PropTypes.string,
  isAddSkillPassportFromOnboarding: PropTypes.bool,
  credential: PropTypes.object,
  isSmallImage: PropTypes.bool,
  isAddSkillFromTaxonomy: PropTypes.bool,
  aggregations: PropTypes.object,
  getSearchResults: PropTypes.func,
  selectedAggregations: PropTypes.object,
  activeTab: PropTypes.string,
  isCareerPathPage: PropTypes.bool,
  userSkills: PropTypes.array,
  confirmBtnTitle: PropTypes.string,
  isNegativeValue: PropTypes.bool,
  passport: PropTypes.object,
  navigate: PropTypes.func,
  uploadMeta: PropTypes.object,
  anchorRef: PropTypes.object
};

function mapStoreStateToProps(state) {
  return {
    defaultTopic: state.modal.get('defaultTopic'),
    isPrivate: state.modal.get('isPrivate'),
    comment: state.modal.get('comment'),
    metric: state.modal.get('metric'),
    modalState: state.modal.get('modalState'),
    authorName: state.modal.get('authorName'),
    toggleChannelModal: state.modal.get('toggleChannelModal'),
    dismissible: state.modal.get('dismissible'),
    ratingCount: state.modal.get('ratingCount'),
    handleCardClicked: state.modal.get('handleCardClicked'),
    standaloneLinkClickHandler: state.modal.get('standaloneLinkClickHandler'),
    rateCard: state.modal.get('rateCard'),
    providerLogos: state.modal.get('providerLogos'),
    groupId: state.modal.get('groupId'),
    type: state.modal.get('type'),
    typeBefore: state.modal.get('typeBefore'),
    cardTypes: state.modal.get('cardTypes'),
    channelId: state.modal.get('channelId'),
    assignedStateChange: state.modal.get('assignedStateChange'),
    selfAssignedStateChange: state.modal.get('selfAssignedStateChange'),
    dueAt: state.modal.get('dueAt'),
    startDate: state.modal.get('startDate'),
    currentUserId: state.modal.get('currentUserId'),
    userType: state.modal.get('userType'),
    cardType: state.modal.get('cardType'),
    title: state.modal.get('title'),
    message: state.modal.get('message'),
    label: state.modal.get('label'),
    description: state.modal.get('description'),
    typeOfModal: state.modal.get('typeOfModal'),
    statusBlock: state.modal.get('statusBlock'),
    statusMessage: state.modal.get('statusMessage'),
    defaultImage: state.modal.get('defaultImage'),
    id: state.modal.get('id'),
    cardId: state.modal.get('cardId'),
    checkedCardId: state.modal.get('checkedCardId'),
    openCard: state.modal.get('openCard'),
    isReject: state.modal.get('isReject'),
    selfAssign: state.modal.get('selfAssign'),
    isViewsModal: state.modal.get('isViewsModal'),
    isAddingToJourney: state.modal.get('isAddingToJourney'),
    isStandaloneModal: state.modal.get('isStandaloneModal'),
    skill: state.modal.get('skill'),
    passport: state.modal.get('passport'),
    card: state.modal.get('card'),
    curators: state.modal.get('curators'),
    channel: state.modal.get('channel'),
    group: state.modal.get('group'),
    logoObj: state.modal.get('logoObj'),
    integration: state.modal.get('integration'),
    channelSetting: state.modal.get('channelSetting'),
    groupSetting: state.modal.get('groupSetting'),
    userBadge: state.modal.get('userBadge'),
    rejectCallback: state.modal.get('rejectCallback'),
    deleteSharedCard: state.modal.get('deleteSharedCard'),
    updateGroupsList: state.modal.get('updateGroupsList'),
    cardUpdated: state.modal.get('cardUpdated'),
    callback: state.modal.get('callback'),
    openChannelModal: state.modal.get('openChannelModal'),
    handleCloseModal: state.modal.get('handleCloseModal'),
    importClickHandler: state.modal.get('importClickHandler'),
    open: state.modal.get('open'),
    courseStatus: state.modal.get('courseStatus'),
    courseType: state.modal.get('courseType'),
    currentAction: state.modal.get('currentAction'),
    isInitilized: state.modal.get('isInitilized'),
    paymentData: state.modal.get('paymentData'),
    removable: state.modal.get('removable'),
    hideCancelBtn: state.modal.get('hideCancelBtn'),
    hideConfirmBtn: state.modal.get('hideConfirmBtn'),
    removeCardFromCardContainer: state.modal.get('removeCardFromCardContainer'),
    removeCardFromList: state.modal.get('removeCardFromList'),
    cardSectionName: state.modal.get('cardSectionName'),
    bookmarked: state.modal.get('bookmarked'),
    cardModalDetails: state.modal.get('cardModalDetails'),
    cards: state.modal.get('cards'),
    confirmHandler: state.modal.get('confirmHandler'),
    changeRole: state.modal.get('changeRole'),
    unRegister: state.modal.get('unRegister'),
    role: state.modal.get('role'),
    isSettingsPage: state.modal.get('isSettingsPage'),
    sourceMode: state.modal.get('sourceMode'),
    isAddSkillPassportFromOnboarding: state.modal.get('isAddSkillPassportFromOnboarding'),
    credential: state.modal.get('credential'),
    isSmallImage: state.modal.get('isSmallImage'),
    isAddSkillFromTaxonomy: state.modal.get('isAddSkillFromTaxonomy'),
    aggregations: state.modal.get('aggregations'),
    getSearchResults: state.modal.get('getSearchResults'),
    selectedAggregations: state.modal.get('selectedAggregations'),
    activeTab: state.modal.get('activeTab'),
    isCareerPathPage: state.modal.get('isCareerPathPage'),
    userSkills: state.modal.get('userSkills'),
    confirmBtnTitle: state.modal.get('confirmBtnTitle'),
    isNegativeValue: state.modal.get('isNegativeValue'),
    uploadMeta: state.modal.get('uploadMeta'),
    anchorRef: state.modal.get('anchorRef')
  };
}

export default withRouter(connect(mapStoreStateToProps)(ModalContainer));
