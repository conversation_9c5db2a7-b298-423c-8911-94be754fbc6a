import React, { useEffect, useState, useRef } from 'react';
import cx from 'classnames';
import FocusLock from 'react-focus-lock';
import { func, array, string, object, bool } from 'prop-types';
import Select from 'react-select';
import Table from 'centralized-design-system/src/Table';
import Checkbox from 'centralized-design-system/src/Checkbox';
import { Select as CustomSelect, AreaInput } from 'centralized-design-system/src/Inputs';

import { translatr } from 'centralized-design-system/src/Translatr';
import Modal, {
  ModalContent,
  ModalFooter,
  ModalHeader
} from 'centralized-design-system/src/Modals';
import { connect } from 'react-redux';
import './styles.scss';
import {
  getTeamMemberSkill,
  submitUserSkillsAssessment
} from 'edc-web-sdk/requests/managerDashboard.v2';
import { close } from 'actions/modalActions';
import { open_v2 as openSnackBar } from 'actions/snackBarActions';
import {
  capitalizeFirstLetter,
  getDefaultValue,
  getLastRatedDate,
  getStatusLabel,
  getSortedSkills,
  renderWarningMessage,
  hiddenLevels
} from './helpers';
import {
  getAllAvailableProficiencyLevels,
  getLevelByDecimal
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import { getSkillLevels } from '@pages/Projects/ProjectForm/helpers';
import { truncateText } from '@utils/utils';

const ManagerSkillAssessmentModal = ({
  teamMembersList,
  closeModal,
  currentUserLanguage,
  toast,
  assessmentType
}) => {
  const [selectedUser, setSelectedUser] = useState();
  const [isSelectingUser, setIsSelectingUser] = useState(true);
  const [isRatingSkill, setIsRatingSkill] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [title, setTitle] = useState();
  const [rowData, setRowData] = useState();
  const [userSelectedLevels, setUserSelectedLevels] = useState();
  const [selectedSkills, setSelectedSkills] = useState({});
  const [selectedSkillLevels, setSelectedSkillLevels] = useState({});
  const [selectAllSkills, setSelectAllSkills] = useState(false);
  const [showWaringMessage, setShowWaringMessage] = useState(false);
  const [finalSelectedSkills, setFinalSelectedSkills] = useState();
  const [teamMemberDetail, setTeamMemberDetail] = useState({});
  const [isClickable, setIsClickable] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const selectedSkillComments = useRef({});

  useEffect(() => {
    let newTitle = '';

    if (isSelectingUser) {
      newTitle =
        translatr('web.manager-dashboard-v2.main', 'ReviewAndRateYourTeam') ||
        'Review and rate your team';
    } else if (isRatingSkill) {
      newTitle =
        translatr('web.manager-dashboard-v2.main', 'RateSkillsForUser', {
          fullName: selectedUser.label
        }) || `Rate skills for ${selectedUser.label}`;
    } else if (isSubmitted) {
      newTitle =
        translatr(
          'web.manager-dashboard-v2.main',
          'NiceYouHaveJustAddedSkillsRatingsForTeamMember',
          { teamMemberName: selectedUser.label }
        ) || `Nice! You’ve just added skills ratings for ${selectedUser.label}.`;
    }

    setTitle(newTitle);
  }, [isSelectingUser, isRatingSkill, isSubmitted, selectedUser]);

  useEffect(() => {
    setSelectedSkills({});
    setSelectedSkillLevels({});
    selectedSkillComments.current = {};
    setFinalSelectedSkills({});
    setSelectAllSkills(false);
    setShowWaringMessage(false);
    setUserSelectedLevels([]);
  }, [selectedUser]);

  const selectedLevel = {
    label: translatr('web.manager-dashboard-v2.main', 'SelectLevel') || 'Select level',
    value: '-1'
  };

  useEffect(() => {
    if (teamMemberDetail?.skills?.length > 0) {
      const userLevels = [];
      if (teamMemberDetail?.skills) {
        teamMemberDetail.skills.forEach(skill => {
          if (skill.proficiency_level !== undefined && skill.proficiency_level !== null) {
            const level = getLevelByDecimal(skill.proficiency_level.toString());
            if (level) {
              userLevels.push({
                value: level.name,
                level: parseFloat(level.level),
                hidden: level.hidden
              });
            }
          }
        });
      }
      setUserSelectedLevels(userLevels);
      const shouldShowWarning = userLevels.filter(level => level.hidden).length > 0;
      setShowWaringMessage(shouldShowWarning);
      const proficiencyLevels = getSkillLevels([
        ...getAllAvailableProficiencyLevels(
          window.__edOrgData.proficiencyLevels,
          currentUserLanguage,
          userLevels
        )
      ]);
      const skillLevels = [selectedLevel, ...proficiencyLevels];
      setRowData(mapTableData(skillLevels));
    }
  }, [teamMemberDetail, selectedSkills, selectedSkillLevels, selectAllSkills]);

  useEffect(() => {
    setFinalSelectedSkills(getFinalselectedSkills());
  }, [selectedSkills, selectedSkillLevels, selectAllSkills]);

  const onContinue = () => {
    setIsLoading(true);
    let payload = { reportee_id: selectedUser.id, type: assessmentType };
    getTeamMemberSkill(selectedUser.id, payload)
      .then(resp => {
        setTeamMemberDetail({
          name: resp?.user?.fullName || resp?.name,
          id: resp?.user?.id,
          skills: getSortedSkills(resp?.skills),
          error: resp?.error
        });
        setIsRatingSkill(true);
        setIsSelectingUser(false);
        setIsLoading(false);
      })
      .catch(err => {
        console.error(err);
        toast(
          translatr(
            'web.talentmarketplace.main',
            ' ThereWasAnErrorRetrievingSearchResultsPleaseTryAgain'
          ),
          'error'
        );
        setIsLoading(false);
        closeModal();
      });
  };

  const handleSelectAllSkills = event => {
    const updatedSelectedSkills = {};

    const isChecked = event.target.checked;
    setSelectAllSkills(isChecked);

    teamMemberDetail?.skills?.forEach(skill => {
      updatedSelectedSkills[skill.skill_node_id] = isChecked;
    });
    setSelectedSkills(updatedSelectedSkills);
  };

  const Headers = [
    {
      children: (
        <Checkbox
          label={translatr('web.common.main', 'Skill')}
          aria-label={translatr('web.common.main', 'Skill')}
          checked={selectAllSkills}
          onChange={handleSelectAllSkills}
          disabled={teamMemberDetail?.skills?.length > 0 ? false : true}
        />
      )
    },
    { label: translatr('web.common.main', 'Level') }
  ];

  const skillSection = (skill, id) => {
    return {
      children: (
        <div className="skill-checkbox">
          <Checkbox
            key={id}
            id={skill?.skill_node_id}
            label={capitalizeFirstLetter(skill?.skill_label)}
            checked={selectedSkills[skill?.skill_node_id]}
            onChange={() => handleSkillCheck(skill?.skill_node_id)}
          />
        </div>
      )
    };
  };

  const levelSection = (skillLevels, skill, id) => {
    const defaultValue = getDefaultValue(skill?.proficiency_level, skillLevels);
    const isMetaDataPresent = skill?.updated_at && skill?.status;

    return {
      children: (
        <div className="level-section">
          <CustomSelect
            defaultValue={defaultValue}
            key={id}
            name={skill?.skill_node_id}
            items={skillLevels?.map(item => {
              const { label, value, hidden } = item;
              return {
                label: label,
                value: `${value}`,
                disabled: hidden
              };
            })}
            disabled={!selectedSkills[skill?.skill_node_id]}
            onChange={value => handleSkillLevelChange(skill?.skill_node_id, value.value)}
          />
          {isMetaDataPresent && (
            <span className="extra-info">
              <span>{getLastRatedDate(skill?.updated_at, currentUserLanguage)}</span>

              <span> | </span>
              <span>
                {translatr('web.manager-dashboard-v2.main', 'Status', {
                  status: getStatusLabel(skill?.status)
                }) || `Status: ${skill?.status}`}
              </span>
            </span>
          )}
          <div className="comment">
            <AddComment
              skillID={skill && skill.skill_node_id}
              disabled={!selectedSkills[skill?.skill_node_id]}
              prevComment={skill && skill.comment}
              handleSkillCommentChange={handleSkillCommentChange}
            />
          </div>
        </div>
      )
    };
  };

  const handleSkillLevelChange = (id, val) => {
    setSelectedSkillLevels(prevState => ({
      ...prevState,
      [id]: val
    }));
  };

  const handleSkillCommentChange = (id, val) => {
    let skillComments = selectedSkillComments.current;
    skillComments = { ...skillComments, [id]: val };
    selectedSkillComments.current = skillComments;
  };

  const handleSkillCheck = id => {
    setSelectedSkills(prevState => ({
      ...prevState,
      [id]: !selectedSkills[id]
    }));
  };

  const mapTableData = skillLevels => {
    const tempRow = [];
    teamMemberDetail?.skills?.map((skill, id) => {
      const row = [skillSection(skill, id), levelSection(skillLevels, skill, id)];
      tempRow.push(row);
    });
    return tempRow;
  };

  const getFinalselectedSkills = () => {
    const data = [];
    const skillWithoutLevel = [];

    Object.keys(selectedSkills).forEach(key => {
      if (!selectedSkills[key]) {
        return;
      }
      const tempSkill = teamMemberDetail?.skills?.find(skill => skill.skill_node_id === key);

      if (
        (!(key in selectedSkillLevels) && !tempSkill.proficiency_level) ||
        selectedSkillLevels[key] === '-1'
      ) {
        skillWithoutLevel.push(tempSkill.skill_node_id);
      } else if (!selectedSkillLevels[key] && tempSkill.proficiency_level) {
        data.push({ ...tempSkill, level: parseFloat(tempSkill.proficiency_level) });
      } else {
        data.push({ ...tempSkill, level: parseFloat(selectedSkillLevels[key]) });
      }
    });
    const isSubmitClickable =
      Object.keys(skillWithoutLevel).length > 0 ||
      Object.keys(data).length === 0 ||
      Object.values(object).every(value => value === false)
        ? false
        : true;
    setIsClickable(isSubmitClickable && !hasSkillWitHiddenLevel(data));
    return data;
  };

  const hasSkillWitHiddenLevel = data => {
    if (!data?.length || !userSelectedLevels?.length) {
      return false;
    }
    const userSelectedDecimalLevels = userSelectedLevels
      .filter(userLevel => {
        return userLevel?.hidden;
      })
      .map(userLevel => userLevel.level);
    return data
      .filter(skill => skill.level !== undefined && skill.level !== null)
      .some(skill => userSelectedDecimalLevels.includes(skill.level));
  };

  const onSubmit = () => {
    const payload = {
      reportee_id: teamMemberDetail?.id,
      type: assessmentType,
      assessments: finalSelectedSkills.map(skill => {
        let comment = '';
        if (Object.keys(selectedSkillComments.current).includes(skill.skill_node_id)) {
          comment = selectedSkillComments.current[skill.skill_node_id];
        }
        return {
          skill_node_id: skill.skill_node_id,
          skill_name: skill.skill_name,
          proficiency_level: skill.level,
          comment: comment
        };
      })
    };
    setIsLoading(true);
    submitUserSkillsAssessment(payload)
      .then(response => {
        if (response.errors && response.errors?.length === 0) {
          setIsSubmitted(true);
          setIsRatingSkill(false);
          setIsLoading(false);
        } else {
          toast(translatr('web.common.main', 'SomethingWentWrongPleaseTryLater'), 'success');
          closeModal();
        }
      })
      .catch(err => {
        console.error(err);
        toast(
          translatr(
            'web.talentmarketplace.main',
            ' ThereWasAnErrorSubmittingYourRequestPleaseTryAgain'
          ),
          'error'
        );
        closeModal();
      });
  };

  const onBack = () => {
    setIsSelectingUser(true);
    setIsRatingSkill(false);
  };

  const renderContent = () => {
    if (isSelectingUser) {
      return renderSelectTeamMember();
    } else if (isRatingSkill) {
      return renderRatingSkill();
    }
  };

  const renderSelectTeamMember = () => {
    return (
      <div className="select-team-member-content">
        <label for="employee">
          {translatr('web.manager-dashboard-v2.main', 'SelectAnEmployeeAndAssignASkillRating') ||
            'Select an employee and assign a skill rating'}
        </label>
        <div className="ed-input-container">
          <Select
            id="employee"
            role="application"
            isSearchable
            isClearable
            components={{
              DropdownIndicator: null
            }}
            className="ed-multi-select"
            classNamePrefix="ed-multi-select"
            onChange={user => setSelectedUser(user)}
            placeholder={
              translatr('web.manager-dashboard-v2.main', 'SelectEmployee') || 'Select employee...'
            }
            value={selectedUser}
            options={teamMembersList?.map(member => ({
              id: member.id,
              value: member.id,
              label: member.name
            }))}
          />
        </div>
      </div>
    );
  };

  const renderRatingSkill = () => {
    return (
      <div className="rate-skill-content">
        {showWaringMessage &&
          renderWarningMessage(
            translatr('web.skills-assessments-v2.main', 'SelectedLevelIsDisabled', {
              skillLevelName: hiddenLevels(userSelectedLevels)
            })
          )}
        {teamMemberDetail?.skills &&
        teamMemberDetail?.skills?.length > 0 &&
        !teamMemberDetail.error ? (
          <div className="rate-skill-container">
            {rowData && <Table headers={Headers} rows={rowData} tableClass="rate-skill-table" />}
          </div>
        ) : (
          <div className="rate-skill-zero-state">
            <div className="with-border-bottom">
              <p className="with-margin-bottom">
                {translatr(
                  'web.manager-dashboard-v2.main',
                  'SkillRatingIsNotAvailableForThisEmployee'
                ) || 'Skill rating is not available for this employee'}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderFooter = () => {
    if (isSelectingUser) {
      return (
        <button
          className="ed-btn ed-btn-primary"
          onClick={onContinue}
          disabled={!selectedUser || (selectedUser && isLoading)}
        >
          {translatr('web.common.main', 'Continue')}
        </button>
      );
    } else if (isRatingSkill) {
      return (
        <>
          <button className="ed-btn ed-btn-neutral" onClick={onBack}>
            {translatr('web.common.main', 'Back')}
          </button>
          <button
            className="ed-btn ed-btn-primary"
            onClick={onSubmit}
            disabled={!isClickable || (isClickable && isLoading)}
          >
            {translatr('web.common.main', 'Submit')}
          </button>
        </>
      );
    } else if (isSubmitted) {
      return (
        <button className="ed-btn ed-btn-primary" onClick={closeModal}>
          {translatr('web.common.main', 'Done')}
        </button>
      );
    }
  };

  return (
    <>
      <Modal className={cx('skill-assessment', { confirmation: isSubmitted })} size="small">
        <FocusLock>
          <ModalHeader title={title} onClose={closeModal} />
          <ModalContent>{renderContent()}</ModalContent>
          <ModalFooter>{renderFooter()}</ModalFooter>
        </FocusLock>
      </Modal>
    </>
  );
};

ManagerSkillAssessmentModal.propTypes = {
  teamMembersList: array,
  closeModal: func,
  showRateSkillModal: func,
  toast: func,
  currentUserLanguage: string,
  assessmentType: string
};

export default connect(
  ({ modal, currentUser }) => ({
    teamMembersList: modal.get('teamMembersList'),
    assessmentType: modal.get('assessmentType'),
    currentUserLanguage: currentUser.get('profile')?.get?.('language') || 'en'
  }),
  dispatch => ({
    closeModal: () => dispatch(close()),
    toast: (message, type) => dispatch(openSnackBar(message, type))
  })
)(ManagerSkillAssessmentModal);

const AddComment = ({ skillID, disabled, prevComment, handleSkillCommentChange }) => {
  const [comment, setComment] = useState(prevComment ? prevComment : '');
  const [addComment, setAddComment] = useState(false);
  const [edit, setEdit] = useState(false);

  const addCommentHandler = () => {
    setAddComment(!addComment);
  };

  const commentHandler = inputValue => {
    setComment(inputValue);
    handleSkillCommentChange(skillID, inputValue);
  };

  const handleEditComment = () => {
    setEdit(true);
  };

  return prevComment && !edit ? (
    <div className="text-comment">
      {truncateText(prevComment, 70, '...')}{' '}
      <span>
        <button onClick={handleEditComment} disabled={disabled}>
          {translatr('web.common.main', 'Edit')}
        </button>
      </span>{' '}
    </div>
  ) : addComment || edit ? (
    <>
      <button className="mb-8" onClick={addCommentHandler} disabled={disabled}>
        {translatr('web.common.main', 'Comment')}
      </button>
      <br />
      <AreaInput
        id="optional-msg-area-input-id"
        setValue={commentHandler}
        placeholder={translatr('web.common.main', 'WriteYourMessageHere')}
        maxLen={5000}
        shouldCheckForMaxChar={true}
        optional={true}
        defaultTextInput={comment}
      />
    </>
  ) : (
    <button className="mb-8" onClick={addCommentHandler} disabled={disabled}>
      + {translatr('web.common.main', 'Comment')}
    </button>
  );
};

AddComment.propTypes = {
  skillID: string,
  disabled: bool,
  prevComment: string,
  handleSkillCommentChange: func
};
