import React, { Component } from 'react';
import { bool, string, func, oneOfType, instanceOf, shape } from 'prop-types';
import { connect } from 'react-redux';
import startCase from 'lodash/startCase';
import HiddenTextField from '../common/HiddenTextField';
import { close } from '../../actions/modalActions';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import FocusLock from 'react-focus-lock';
import { <PERSON><PERSON><PERSON>eader, ModalFooter } from 'centralized-design-system/src/Modals';
import '@components/modals/CommonModalStyles.scss';
import classNames from 'classnames';

class ConfirmationModal extends Component {
  constructor(props, context) {
    super(props, context);
    this.cancelClickHandler = this.cancelClickHandler.bind(this);
    this.confirmClickHandler = this.confirmClickHandler.bind(this);
    this.handleClose = this.handleClose.bind(this);
  }

  cancelClickHandler() {
    !!this.props.cancelClick ? this.props.cancelClick() : this.props.dispatch(close());
  }

  handleClose() {
    !!this.props.closeModal ? this.props.closeModal() : this.props.dispatch(close());
  }

  confirmClickHandler() {
    !this.props.noNeedClose && this.props.dispatch(close());
    this.props.callback();
  }

  buttonTitle(btnText) {
    let propBtnTitle = this.props[`${btnText}BtnTitle`];

    if (propBtnTitle) {
      return this.props.isTranslated ? propBtnTitle : tr(propBtnTitle);
    } else {
      return translatr('web.common.main', startCase(btnText));
    }
  }

  componentDidMount() {
    document.getElementById('ed-dialog-modal-header-close-button')?.focus();
  }

  render() {
    const confirmBtnClasses = classNames('ed-btn', {
      'ed-btn-negative': this.props.isNegativeValue,
      'ed-btn-primary': !this.props.isNegativeValue
    });

    const { anchorRef } = this.props;

    return (
      <FocusLock
        onDeactivation={() => {
          // When FocusLock is deactivated (modal is closed), return focus to the element that invoked the modal
          anchorRef?.current?.focus();
        }}
      >
        <ModalHeader
          title={this.props.title || translatr('web.common.main', 'Confirm')}
          onClose={this.handleClose}
        />
        <div className="vertical-spacing-large">
          <HiddenTextField name="confirm" />
          <div className="confirm-msg">{this.props.message}</div>
        </div>
        <ModalFooter>
          {!this.props.hideCancelBtn && (
            <button
              className="ed-btn ed-btn-neutral"
              onClick={this.cancelClickHandler}
              aria-label={this.buttonTitle('cancel')}
            >
              {this.buttonTitle('cancel')}
            </button>
          )}

          {!this.props.hideConfirmBtn && (
            <button
              className={confirmBtnClasses}
              onClick={this.confirmClickHandler}
              aria-label={this.buttonTitle('confirm')}
            >
              {this.buttonTitle('confirm')}
            </button>
          )}
        </ModalFooter>
      </FocusLock>
    );
  }
}

ConfirmationModal.propTypes = {
  open: bool,
  noNeedClose: bool,
  isPrivate: bool,
  message: string,
  confirmBtnTitle: string,
  cancelBtnTitle: string,
  callback: func,
  cancelClick: func,
  closeModal: func,
  title: string,
  danger: bool,
  hideCancelBtn: bool,
  hideConfirmBtn: bool,
  isNegativeValue: bool,
  isTranslated: bool,
  anchorRef: oneOfType([func, shape({ current: instanceOf(Element) })])
};

export default connect()(ConfirmationModal);
