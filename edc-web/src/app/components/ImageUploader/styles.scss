@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-image-uploader {
  .trigger {
    .icon {
      display: inline-block;
      margin-right: var(--ed-spacing-2xs);
    }
  }

  .tabs {
    margin: 0;
  }

  .tab-bar.block {
    border-bottom: var(--ed-border-size-sm) solid #cecece;
    box-shadow: none;
    margin-bottom: var(--ed-spacing-xs);
  }

  .preview {
    width: rem-calc(180);
    height: rem-calc(76);
    border-radius: var(--ed-border-radius-lg);
    border: var(--ed-border-size-sm) solid var(--ed-border-color-light);
  }

  .ed-dialog-modal-header {
    margin-bottom: var(--ed-spacing-sm);

    h2 {
      margin: 0;
    }
  }

  .ed-dialog-modal-content {
    padding-top: 0 !important;
  }

  .fsp-drop-area,
  .fsp-button,
  .fst-sidebar__option {
    &:focus {
      outline: var(--ed-border-size-md) solid var(--ed-state-focus-color);
      outline-offset: var(--ed-border-size-md);
    }
  }
}
