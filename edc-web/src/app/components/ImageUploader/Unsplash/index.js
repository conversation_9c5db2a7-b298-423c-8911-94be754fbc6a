import * as React from 'react';
import PropTypes from 'prop-types';
import { OrderBy } from 'unsplash-js';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import Loading from 'centralized-design-system/src/Loading';
import { IMAGE_SOURCE } from '../types';
import UnsplashApi from './apis';
import './styles.scss';

let page = 1;

const ROOT_CLASS = 'unsplash-image-uploader';

export const UnsplashWidget = ({ apiKey, onImageSelect, onConfirm }) => {
  const api = React.useMemo(() => new UnsplashApi(apiKey), [apiKey]);
  const [selectedImage, setSelectedImage] = React.useState('');
  const [inputValue, setInputValue] = React.useState('');
  const [data, setData] = React.useState(null);
  const [initialData, setInitialData] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    (async () => {
      try {
        const result = await api.getPhotos(30, OrderBy.LATEST);
        setInitialData(result.response.results);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const onImageClick = image => {
    setSelectedImage(image);
    onImageSelect({
      alt: image.alt_description || image.description,
      blur: image.blur_hash,
      source: IMAGE_SOURCE.UNSPLASH,
      thumb: image.urls.thumb,
      url: image.urls.raw,
      downloadLocation: image.links.download_location
    });
  };

  const onImageEnterPress = (image, e) => {
    if (e.key === 'Enter') {
      if (selectedImage.id === image.id) {
        onConfirm(image);
      } else {
        onImageClick(image);
      }
    }
  };

  React.useEffect(() => {
    const getData = setTimeout(() => {
      if (inputValue) {
        api
          .searchPhoto(30, inputValue, page)
          .then(result => {
            setData(result.response.results);
          })
          .catch(() => {});
      } else {
        setData(initialData);
      }
    }, 500);
    return () => {
      clearTimeout(getData);
    };
  }, [initialData, inputValue]);

  const onLoadMore = async () => {
    try {
      page = page + 1;
      const result = await api.getPhotos(30, OrderBy.LATEST, page);
      setData([...data, ...result.response.results]);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className={ROOT_CLASS}>
      <span className={`${ROOT_CLASS}-image-provider-text`}>
        {translatr('web.common.main', 'PhotosByUnsplash')}
      </span>
      <div className="search-container">
        <TextField
          isTranslated
          ariaLabel={translatr('web.talentmarketplace.main', 'SearchTopic', {
            topic: translatr('web.common.main', 'PhotosByUnsplash')
          })}
          defaultValue={inputValue}
          placeholder={translatr('web.common.main', 'SearchPhotos')}
          setValue={val => setInputValue(val)}
          type="search"
        />
        {!inputValue && <span className="icon-search"></span>}
      </div>
      {loading && <Loading />}
      {!loading && ((data && data.length > 0) || (initialData && initialData.length > 0)) && (
        <>
          <ul>
            {(data || initialData).map(image => (
              <li>
                <div
                  onKeyDown={e => onImageEnterPress(image, e)}
                  onClick={() => onImageClick(image)}
                  tabIndex={0}
                  className={`image-item size-inherit ${
                    image.id == selectedImage.id ? 'selected-image' : ''
                  }`}
                  role="button"
                  aria-pressed={image.id == selectedImage.id}
                >
                  <img alt={image.alt_description || image.description} src={image.urls.small} />
                </div>
                <div className="user-details">
                  <a
                    className="user-link"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={`${image.user?.links?.html}?utm_source=opportunity_marketplace&utm_medium=referral&utm_campaign=api-credit`}
                  >
                    {translatr('web.common.main', 'ByUsername', { username: image.user.name })}
                  </a>
                </div>
              </li>
            ))}
          </ul>
          <div className={`${ROOT_CLASS}-load-more-container`}>
            <button id="load-more" className="ed-btn ed-btn-neutral" onClick={onLoadMore}>
              <span>{translatr('web.common.main', 'LoadMore')}</span>
            </button>
          </div>
        </>
      )}
      {!loading && !(data || initialData) && (
        <p>{translatr('web.common.main', 'ThereAreNoImagesToDisplay')}</p>
      )}
    </div>
  );
};

UnsplashWidget.propTypes = {
  apiKey: PropTypes.string.isRequired,
  onImageSelect: PropTypes.func,
  onConfirm: PropTypes.func
};

export default UnsplashWidget;
