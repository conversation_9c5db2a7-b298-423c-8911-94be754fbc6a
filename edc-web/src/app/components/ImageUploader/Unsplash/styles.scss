@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.unsplash-image-uploader {
  &-image-provider-text {
    font-weight: var(--ed-font-weight-semibold);
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-top: var(--ed-spacing-xs);

    .icon-search {
      position: absolute;
      right: var(--ed-spacing-sm);
      font-size: 1.1875rem;
      font-weight: var(--ed-font-weight-bold);
      color: var(--ed-alert-close-button-color);
    }

    .ed-input-container {
      flex-grow: 1;
    }
  }

  ul {
    display: flex;
    gap: rem-calc(13);
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0;
    margin: var(--ed-spacing-base) 0;
  }

  li {
    list-style: none;
    position: relative;
    margin: 0;
    flex: 1 1 30%;
    height: rem-calc(98);
    cursor: pointer;
    overflow: hidden;
    border-radius: var(--ed-border-radius-lg);
    &[aria-selected='true'] {
      outline: -webkit-focus-ring-color auto 1px;
      padding: rem-calc(7) var(--ed-spacing-2xs);
    }

    &:hover,
    &:focus-within {
      .user-details {
        visibility: visible;
      }
    }

    @media screen and (max-width: 425px) {
      flex-basis: 45%;
    }
  }

  li img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .user-details {
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 var(--ed-spacing-lg) rem-calc(1) rem-calc(15);
    background-color: var(--ed-text-color-primary);
    width: 100%;
    opacity: 0.7;
    text-align: left;
    visibility: hidden;
    a:link,
    a:visited {
      color: var(--ed-white);
      text-decoration: underline;
      font-size: var(--ed-font-size-sm);
      width: calc(100% - rem-calc(35));
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }
  }
  .ed-input-container {
    max-width: initial;
  }

  &-load-more-container {
    text-align: center;
  }

  #load-more {
    background-color: var(--ed-white);
    padding: var(--ed-spacing-xs) 25px;
    border: var(--ed-border-size-sm) solid grey;
  }
  .size-inherit {
    width: inherit;
    height: inherit;
  }
  .image-item {
    &:focus {
      outline: var(--ed-spacing-4xs) solid var(--ed-state-focus-color);
      outline-offset: calc(-1 * var(--ed-spacing-4xs));
    }
  }
  .user-link {
    &:focus {
      outline: var(--ed-spacing-4xs) solid var(--ed-state-focus-color);
    }
  }

  .selected-image {
    border: rem-calc(5) solid var(--ed-primary-darken-2);
    border-radius: var(--ed-border-radius-lg);
    background-clip: padding-box;

    img {
      padding: rem-calc(2);
      border-radius: var(--ed-border-radius-lg);
    }
  }
}
