import { useState, useEffect, useContext, useCallback } from 'react';
import { bool, string, number, array, func, shape, oneOfType, object } from 'prop-types';
import { connect, useDispatch } from 'react-redux';

import { translatr, omp } from 'centralized-design-system/src/Translatr';
import './VacancyDetails.scss';
import ConfirmationModal from '@components/modals/ConfirmationModal/ConfirmationModal';
import SocialActivityButtons from '@components/cardStandardization/opportunity/SocialActivityButtons';
import { JOB_TYPE, applyOpportunity } from 'edc-web-sdk/requests/careerOportunities.v2';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import OpportunityDetailProperties from 'opportunity-marketplace/shared/OpportunityDetailProperties/OpportunityDetailProperties';
import { track } from '@analytics/TrackWrapper';
import { TrackEvents, TrackEventProperties } from '@analytics/TrackEvents';
import { open_v3 } from 'actions/snackBarActions';
import AspirationalRoleSwitch from '@pages/TalentMarketplace/DetailPage/components/AspirationalRoleSwitch';
import { openMatchModal } from '@actions/modalActions';
import MatchComponent from '@components/MatchComponent';
import ProfileContainerProvider from '../../pages/MyProfile/Common/ProfileContainerContext';
import { ButtonAnchor } from 'centralized-design-system/src/Buttons';
import { useIsDevelopmentPlanEnabled } from '@pages/TalentMarketplace/DevelopmentPlan/hooks/useIsDevelopmentPlanEnabled';
import { clearMemoizedMatchingJobs } from '@actions/memoizedMatchingJobsActions';
import { generateDirectPath } from '@pages/TalentMarketplace/DevelopmentPlan/utils';

const VacancyDetails = ({
  actionPlan,
  id,
  locations = [],
  mode,
  department,
  type,
  jobType,
  salary,
  postingDate,
  primaryRecruiterId,
  company,
  referenceNumber,
  skillsGraphScore,
  overallScore,
  overallScoreStatus,
  area,
  level,
  roleType,
  title,
  applyURL,
  referralURL,
  jobDescriptionURL,
  capabilities,
  bookmarked,
  toggleBookmark,
  dismissed,
  toggleDismiss,
  applied,
  schedule,
  openSnackBar,
  jobFamily = {},
  jobFunction = {},
  currentUser,
  currentUserLang,
  careerTrack,
  organizationsByType,
  isCareerPathEnabled,
  currentUserJobFamily,
  hasNextRole,
  openMatchModalExtended,
  hideMatchingDetails = false,
  endDate = null,
  onVacancyUpdated = () => {}
}) => {
  const {
    isAspirationalRole: checkIfAspirationRole,
    isAspirationsLoading: aspirationsLoader,
    isAspirationsInitialized
  } = useContext(AspirationsContext);
  const [isMarkedAsAspirational, setIsMarkedAsAspirational] = useState(false);
  const [isModalOpen, toggleModal] = useState(false);
  const [isJobApplied, setIsJobApplied] = useState(applied);
  const { VACANCY_ID, VACANCY_TITLE, COMPONENT } = TrackEventProperties.OMP;
  const isCurrentRole = currentUser?.jobFamily?.roleId === id;

  const isSubwayViewEnabled = currentUserJobFamily && isCareerPathEnabled && hasNextRole;
  const { showDevelopmentPlan } = useIsDevelopmentPlanEnabled();
  const dispatch = useDispatch();

  useEffect(() => {
    if (isAspirationsInitialized) {
      setIsMarkedAsAspirational(checkIfAspirationRole(id));
    }
  }, [isAspirationsInitialized]);

  const openConfirmationModal = () => {
    toggleModal(true);
  };

  const onApply = () => {
    if (isJobApplied) {
      openConfirmationModal();
      return;
    }
    applyOpportunity(id, type)
      .then(() => {
        setIsJobApplied(true);
        openConfirmationModal();
        track(TrackEvents.OMP.APPLY_FOR_OPPORTUNITY, {
          [VACANCY_ID]: id,
          [VACANCY_TITLE]: title,
          [COMPONENT]: 'VacancyDetails'
        });
        onVacancyUpdated({ applied: true });
      })
      .catch(err => console.error('There was an error when applying to job', err));
  };

  const onDismissJobWithMessage = useCallback(
    (oId, dismiss, data) => {
      return new Promise((resolve, reject) => {
        toggleDismiss(oId, dismiss, data)
          .then(() => {
            const msg = translatr(
              'web.common.main',
              dismiss ? 'OpportunityDismissed' : 'OpportunityUndismissed',
              {
                opportunity: type === JOB_TYPE.VACANCY ? omp(`tm_job_vacancy`) : omp('tm_job_role')
              }
            );
            openSnackBar(msg);
            resolve();
          })
          .catch(() => {
            reject();
          });
      });
    },
    [toggleDismiss]
  );

  const onBookmarkJobWithMessage = useCallback(
    (jobId, bookmark, data, closeHandler) => {
      return new Promise((resolve, reject) => {
        toggleBookmark(jobId, bookmark, data)
          .then(() => {
            const msg = translatr(
              'web.common.main',
              bookmark ? 'OpportunityBookmarked' : 'OpportunityUnbookmarked',
              {
                opportunity: type === JOB_TYPE.VACANCY ? omp(`tm_job_vacancy`) : omp('tm_job_role')
              }
            );
            openSnackBar({ message: msg, closeHandler });
            onVacancyUpdated({ bookmarked: bookmark });
            resolve();
          })
          .catch(() => {
            reject();
          });
      });
    },
    [toggleBookmark]
  );

  const buttonsConfig = {
    dismissed,
    dismissable: dismissed,
    bookmarked,
    onBookmark: onBookmarkJobWithMessage,
    isMarkedAsAspirational,
    onDismiss: onDismissJobWithMessage,
    loading: aspirationsLoader
  };

  const scoreStatusClass = `score-status-${overallScoreStatus}`.toLowerCase();

  return (
    <ProfileContainerProvider>
      <div className={`vacancy-details block ${scoreStatusClass}`}>
        {!hideMatchingDetails && !aspirationsLoader && (
          <MatchComponent
            wrapperClass="vacancy-details__matching"
            score={overallScore}
            scoreStatus={overallScoreStatus}
            openModalFn={e =>
              openMatchModalExtended(
                id,
                type,
                { skillsGraphScore, overallScore },
                isCareerPathEnabled,
                e.target
              )
            }
          />
        )}
        <OpportunityDetailProperties
          area={area}
          careerTrack={careerTrack}
          company={company}
          currentUserLang={currentUserLang}
          department={department}
          jobFamily={jobFamily}
          jobFunction={jobFunction}
          jobType={jobType}
          level={level}
          locations={locations}
          mode={mode}
          organizationsByType={organizationsByType}
          postingDate={postingDate}
          primaryRecruiterId={primaryRecruiterId}
          referenceNumber={referenceNumber}
          roleType={roleType}
          salary={salary}
          schedule={schedule}
          type={type}
          endDate={endDate}
        />
        <div className="vacancy-details__buttons">
          {type === JOB_TYPE.VACANCY ? (
            <>
              {!!referralURL && (
                <ButtonAnchor
                  color="secondary"
                  size="large"
                  variant="ghost"
                  href={referralURL}
                  target="_blank"
                  rel="noreferrer noopener"
                  aria-describedby={`vacancy-details__buttons--refer-${id}`}
                >
                  {translatr('web.common.main', 'Refer')}
                  <i className="icon icon-external-link" aria-hidden="true" />
                  <span
                    id={`vacancy-details__buttons--refer-${id}`}
                    className="sr-only"
                    aria-hidden="true"
                  >
                    {translatr('web.common.main', 'OpenInNewTab')}
                  </span>
                </ButtonAnchor>
              )}
              {(!!applyURL || !!jobDescriptionURL) && (
                <>
                  <ButtonAnchor
                    color="primary"
                    size="large"
                    href={applyURL || jobDescriptionURL}
                    handleClick={onApply}
                    target="_blank"
                    rel="noreferrer noopener"
                    aria-describedby={`vacancy-details__buttons--apply-${id}`}
                  >
                    {!!applyURL
                      ? translatr('web.talentmarketplace.main', 'ApplyForVacancy')
                      : translatr('web.common.main', 'ViewOnCareerSite')}
                    <i className="icon icon-external-link" aria-hidden="true" />
                    <span
                      id={`vacancy-details__buttons--apply-${id}`}
                      className="sr-only"
                      aria-hidden="true"
                    >
                      {translatr('web.common.main', 'OpenInNewTab')}
                    </span>
                  </ButtonAnchor>
                  {isJobApplied && (
                    <span className="job-applied-info">
                      <i className="icon-clock no-icon-color" />
                      {translatr('web.talentmarketplace.main', 'ClickedApply')}
                    </span>
                  )}
                </>
              )}
            </>
          ) : (
            <>
              {!isCurrentRole && !isSubwayViewEnabled && (
                <AspirationalRoleSwitch
                  id={id}
                  title={title}
                  dismissed={dismissed}
                  type={type}
                  capabilities={capabilities}
                  showDevelopmentPlan={showDevelopmentPlan}
                  hasDevelopmentPlan={actionPlan?.hasTransitionPlan}
                  onPathSave={path => {
                    if (showDevelopmentPlan && !path) {
                      //we need to refresh job role details after aspirational role switch when action plan was removed
                      dispatch(clearMemoizedMatchingJobs());
                    }
                  }}
                  selectedPathData={
                    showDevelopmentPlan
                      ? generateDirectPath(currentUser?.jobFamily, {
                          id,
                          name: title,
                          isAspirational: isMarkedAsAspirational
                        })
                      : null
                  }
                />
              )}
              {!aspirationsLoader && isCurrentRole && (
                <div className="row">
                  <span className="job-applied-info">
                    <i className="icon-inverse-exclamation-circle no-icon-color" />
                    {translatr('web.common.main', 'YourCurrentRole', { role: omp(`tm_job_role`) })}
                  </span>
                </div>
              )}
            </>
          )}
          <SocialActivityButtons
            type={type}
            id={id}
            loading={aspirationsLoader}
            buttonsConfig={buttonsConfig}
            title={title}
            matchingScore={overallScore}
            isCurrentRole={isCurrentRole}
          />
        </div>
        {isModalOpen && (
          <ConfirmationModal
            closeModal={toggleModal}
            title={title}
            type={type}
            capabilities={capabilities}
          />
        )}
      </div>
    </ProfileContainerProvider>
  );
};

VacancyDetails.propTypes = {
  actionPlan: object,
  id: string,
  locations: array,
  mode: string,
  department: string,
  jobType: string,
  type: string,
  schedule: string,
  salary: shape({
    per: string,
    currency: string,
    rangeTo: number,
    rangeFrom: number
  }),
  postingDate: string,
  primaryRecruiterId: string,
  company: string,
  overallScore: number,
  overallScoreStatus: string,
  skillsGraphScore: number,
  referenceNumber: oneOfType([number, string]),
  area: string,
  roleType: string,
  level: shape({
    id: string
  }),
  title: string,
  applyURL: string,
  referralURL: string,
  capabilities: array,
  bookmarked: bool,
  toggleBookmark: func,
  dismissed: bool,
  applied: bool,
  toggleDismiss: func,
  jobDescriptionURL: string,
  openSnackBar: func,
  jobFamily: object,
  jobFunction: object,
  currentUser: object,
  currentUserLang: string,
  careerTrack: string,
  organizationsByType: object,
  isCareerPathEnabled: bool,
  openMatchModalExtended: func,
  currentUserJobFamily: object,
  hasNextRole: bool,
  hideMatchingDetails: bool,
  onVacancyUpdated: func,
  endDate: string
};

function mapStoreStateToProps({ currentUser }) {
  return {
    currentUser: currentUser.toJS()
  };
}

function mapDispatchToProps(dispatch) {
  return {
    openSnackBar: ({ message, status = 'success', closeHandler }) =>
      dispatch(open_v3({ message, status, closeHandler })),
    openMatchModalExtended: (jobId, jobType, scores, isCareerPathEnabled, openedFromHtmlElement) =>
      dispatch(openMatchModal(jobId, jobType, scores, isCareerPathEnabled, openedFromHtmlElement))
  };
}

export default connect(mapStoreStateToProps, mapDispatchToProps)(VacancyDetails);
