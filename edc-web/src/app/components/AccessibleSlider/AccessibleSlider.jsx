import React, { useRef, useCallback, useEffect, forwardRef } from 'react';
import PropTypes from 'prop-types';
import Slider from 'react-slick';

/**
 * AccessibleSlider is a wrapper around react-slick's Slider component that fixes
 * the accessibility issue with focusable elements inside aria-hidden slides.
 *
 * This component uses the CSS visibility approach to hide non-active slides completely,
 * which prevents keyboard focus and screen reader access to hidden content.
 */
const AccessibleSlider = forwardRef((props, ref) => {
  const sliderRef = useRef(null);
  const containerRef = useRef(null);
  const { className = '', ...restProps } = props;

  // Forward the Slider methods to the parent component
  React.useImperativeHandle(ref, () => ({
    slickGoTo: (...args) => sliderRef.current?.slickGoTo(...args),
    slickNext: () => sliderRef.current?.slickNext(),
    slickPrev: () => sliderRef.current?.slickPrev(),
    slickPlay: () => sliderRef.current?.slickPlay(),
    slickPause: () => sliderRef.current?.slickPause()
  }));

  // Function to toggle slide visibility based on active state
  const toggleSlideVisibility = useCallback(() => {
    try {
      if (!containerRef.current) return;

      // Find all slides within this specific container
      const slides = containerRef.current.querySelectorAll('.slick-slide');

      // Set visibility based on active state
      Array.from(slides).forEach(slide => {
        slide.style.visibility = slide.classList.contains('slick-active') ? 'visible' : 'hidden';
      });
    } catch (error) {
      console.error('Error in toggleSlideVisibility:', error);
    }
  }, []);

  // Function to make all slides visible during transition
  const handleBeforeChange = useCallback(() => {
    try {
      if (!containerRef.current) return;

      // Find all slides within this specific container
      const slides = containerRef.current.querySelectorAll('.slick-slide');

      // Make all slides visible during transition
      Array.from(slides).forEach(slide => {
        slide.style.visibility = 'visible';
      });
    } catch (error) {
      console.error('Error in handleBeforeChange:', error);
    }
  }, []);

  // Apply visibility toggling when component mounts
  useEffect(() => {
    // Wait for the slider to initialize
    const timer = setTimeout(() => {
      toggleSlideVisibility();
    }, 100);

    return () => clearTimeout(timer);
  }, [toggleSlideVisibility]);

  // Create a modified version of props that includes our change handlers
  const modifiedProps = {
    ...restProps,
    beforeChange: (currentSlide, nextSlide) => {
      if (props.beforeChange) {
        props.beforeChange(currentSlide, nextSlide);
      }
      handleBeforeChange();
    },
    afterChange: currentSlide => {
      if (props.afterChange) {
        props.afterChange(currentSlide);
      }

      setTimeout(() => {
        toggleSlideVisibility();
      }, 0);
    },
    onInit: () => {
      if (props.onInit) {
        props.onInit();
      }

      setTimeout(() => {
        toggleSlideVisibility();
      }, 100);
    }
  };

  return (
    <div ref={containerRef} className={className}>
      <Slider ref={sliderRef} {...modifiedProps} />
    </div>
  );
});

AccessibleSlider.displayName = 'AccessibleSlider';

AccessibleSlider.propTypes = {
  afterChange: PropTypes.func,
  beforeChange: PropTypes.func,
  onInit: PropTypes.func,
  arrows: PropTypes.bool,
  autoplay: PropTypes.bool,
  autoplaySpeed: PropTypes.number,
  className: PropTypes.string,
  dots: PropTypes.bool,
  infinite: PropTypes.bool,
  slidesToShow: PropTypes.number,
  slidesToScroll: PropTypes.number,
  speed: PropTypes.number
};

export default AccessibleSlider;
