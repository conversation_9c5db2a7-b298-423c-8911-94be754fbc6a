import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getRecommendedRoles as fetchRoles } from 'edc-web-sdk/requests/extOpportunities';
import { getUserPassport } from 'edc-web-sdk/requests/users';
import { getWorkHistories } from 'edc-web-sdk/requests/talentMarket';
import { getCareerPreferences } from '../../actions/careerPreferencesActions';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { mapJobRoles, RECOMMENDED_LIMIT } from '@pages/TalentMarketplace/Api';
import PropTypes from 'prop-types';
import RolesCarousel from '../RolesCarousel';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { checkIfUserHasUpdatedProfile } from './helpers';

const SuggestedRolesCarousel = ({
  placeholderButtonAction,
  hideIfNoData,
  sortBy,
  dispatch,
  careerPreferences,
  currentUserLang,
  itemsOnCarousel = 4,
  showDevelopmentPlan = false,
  ...props
}) => {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [userProfileFilled, setUserProfileFilled] = useState(false);
  const UserId = window.__ED__.id;
  const navigate = useNavigate();

  useEffect(() => {
    setLoading(true);
    fetchRoles(currentUserLang, RECOMMENDED_LIMIT, sortBy)
      .then(rolesList => {
        const mappedRoles = mapJobRoles(rolesList);
        setRoles(mappedRoles);
        setLoading(false);
        if (!mappedRoles.length) {
          checkDataUnderlyingSuggestions();
        }
      })
      .catch(() => {
        setLoading(false);
        checkDataUnderlyingSuggestions();
      });
  }, [fetchRoles, sortBy]);

  const checkDataUnderlyingSuggestions = React.useCallback(() => {
    setLoading(true);
    Promise.all([
      getWorkHistories({ limit: 0 }),
      getUserPassport(UserId),
      !careerPreferences ? dispatch(getCareerPreferences()) : Promise.resolve(careerPreferences)
    ])
      .then(resps => {
        setUserProfileFilled(!!checkIfUserHasUpdatedProfile(resps));
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }, [checkIfUserHasUpdatedProfile, getWorkHistories, getUserPassport, careerPreferences, UserId]);

  return (
    <RolesCarousel
      loading={loading}
      roles={roles}
      setRoles={setRoles}
      customPlaceholder={
        <EmptyState
          title={translatr(
            'web.talentmarketplace.main',
            userProfileFilled ? 'SuggestionNotFound' : 'SuggestionNotFoundExtended'
          )}
          icon="icon-file"
          buttonLabel={
            userProfileFilled
              ? translatr('web.talentmarketplace.main', 'ExploreCareerGrowthSection', {
                  career_growth_section: omp('tm_tm_job_roles')
                })
              : translatr('web.common.main', 'UpdateCareerProfile')
          }
          onButtonClick={
            userProfileFilled
              ? () => navigate('/career/job-roles')
              : () => placeholderButtonAction()
          }
        />
      }
      noDataLink=""
      hideShowAll
      description={translatr(
        'web.talentmarketplace.main',
        'SuggestionAlgorithmExplanationConfigurable',
        {
          roles: omp('tm_tm_job_roles'),
          tm_aspirational_roles: omp('tm_tm_aspirational_roles')
        }
      )}
      {...props}
      hideIfNoData={hideIfNoData && userProfileFilled}
      filterFunction={itm => !itm.dismissed}
      dismissable
      displayedItems={itemsOnCarousel}
      showDevelopmentPlan={showDevelopmentPlan}
      showFeedbackCard
    />
  );
};

SuggestedRolesCarousel.propTypes = {
  fullCard: PropTypes.bool,
  hideIfNoData: PropTypes.bool,
  sortBy: PropTypes.object,
  careerPreferences: PropTypes.object,
  dispatch: PropTypes.func,
  placeholderButtonAction: PropTypes.func,
  parentComponent: PropTypes.string,
  currentUserLang: PropTypes.string,
  itemsOnCarousel: PropTypes.number,
  showDevelopmentPlan: PropTypes.bool
};

const mapStoreStateToProps = state => {
  const userProfile = state.currentUser.get('profile');
  return {
    careerPreferences: state.careerPreferences.get('careerPreferences'),
    currentUserLang:
      userProfile?.get?.('language') ||
      userProfile?.language ||
      state.team?.get('config')?.DefaultOrgLanguage ||
      'en'
  };
};

export default connect(mapStoreStateToProps)(SuggestedRolesCarousel);
