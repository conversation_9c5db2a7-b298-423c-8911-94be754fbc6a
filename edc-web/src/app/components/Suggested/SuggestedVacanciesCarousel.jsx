import React, { useState } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getRecommendedJobs } from 'edc-web-sdk/requests/extOpportunities';
import { getUserPassport } from 'edc-web-sdk/requests/users';
import { getWorkHistories } from 'edc-web-sdk/requests/talentMarket';
import { getCareerPreferences } from '../../actions/careerPreferencesActions';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import JobCardWidget from '@pages/TalentMarketplace/shared/JobCardWidget';
import { RECOMMENDED_LIMIT, mapJobVacancies } from '@pages/TalentMarketplace/Api';
import PropTypes from 'prop-types';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { checkIfUserHasUpdatedProfile } from './helpers';

const SuggestedVacanciesCarousel = ({
  placeholderButtonAction,
  hideIfNoData,
  sortBy,
  dispatch,
  careerPreferences,
  currentUserLang,
  itemsOnCarousel = 4,
  ...props
}) => {
  const [loading, setLoading] = useState(false);
  const [userProfileFilled, setUserProfileFilled] = useState(false);
  const UserId = window.__ED__.id;
  const navigate = useNavigate();

  const recommendedJobWidgetApi = React.useCallback(
    () =>
      new Promise((resolve, reject) => {
        return getRecommendedJobs(currentUserLang, RECOMMENDED_LIMIT, sortBy)
          .then(data => {
            const mappedVacancies = mapJobVacancies(data || []);
            resolve(mappedVacancies);
            if (!mappedVacancies.length) {
              checkDataUnderlyingSuggestions();
            }
          })
          .catch(e => {
            checkDataUnderlyingSuggestions();
            reject(e);
          });
      }),
    [getRecommendedJobs, sortBy]
  );

  const checkDataUnderlyingSuggestions = React.useCallback(() => {
    setLoading(true);
    Promise.all([
      getWorkHistories({ limit: 0 }),
      getUserPassport(UserId),
      !careerPreferences ? dispatch(getCareerPreferences()) : Promise.resolve(careerPreferences)
    ])
      .then(resps => {
        setUserProfileFilled(checkIfUserHasUpdatedProfile(resps));
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }, [checkIfUserHasUpdatedProfile, getWorkHistories, getUserPassport, careerPreferences, UserId]);

  return (
    <JobCardWidget
      title={translatr('web.talentmarketplace.main', 'RecommendedOpportunities', {
        opportunities: omp('tm_tm_job_vacancies')
      })}
      hideShowAll
      noDataLink=""
      fetchApi={recommendedJobWidgetApi}
      customPlaceholder={
        <EmptyState
          icon="icon-file"
          title={translatr(
            'web.talentmarketplace.main',
            userProfileFilled ? 'SuggestionNotFound' : 'SuggestionNotFoundExtended'
          )}
          buttonLabel={
            userProfileFilled
              ? translatr('web.talentmarketplace.main', 'ExploreCareerGrowthSection', {
                  career_growth_section: omp('tm_tm_job_vacancies')
                })
              : translatr('web.common.main', 'UpdateCareerProfile')
          }
          onButtonClick={
            userProfileFilled
              ? () => navigate('/career/job-vacancies')
              : () => placeholderButtonAction()
          }
        />
      }
      customPlaceholderLoading={loading}
      description={translatr(
        'web.talentmarketplace.main',
        'SuggestionAlgorithmExplanationConfigurable',
        {
          roles: omp('tm_tm_job_roles'),
          tm_aspirational_roles: omp('tm_tm_aspirational_roles')
        }
      )}
      {...props}
      hideIfNoData={hideIfNoData && userProfileFilled}
      filterFunction={itm => !itm.dismissed}
      dismissable
      itemsOnCarousel={itemsOnCarousel}
      showFeedbackCard
    />
  );
};

SuggestedVacanciesCarousel.propTypes = {
  hideIfNoData: PropTypes.bool,
  sortBy: PropTypes.object,
  careerPreferences: PropTypes.object,
  dispatch: PropTypes.func,
  placeholderButtonAction: PropTypes.func,
  parentComponent: PropTypes.string,
  currentUserLang: PropTypes.string,
  itemsOnCarousel: PropTypes.number
};

const mapStoreStateToProps = state => {
  const userProfile = state.currentUser.get('profile');
  return {
    careerPreferences: state.careerPreferences.get('careerPreferences'),
    currentUserLang:
      userProfile?.get?.('language') ||
      userProfile?.language ||
      state.team?.get('config')?.DefaultOrgLanguage ||
      'en'
  };
};

export default connect(mapStoreStateToProps)(SuggestedVacanciesCarousel);
