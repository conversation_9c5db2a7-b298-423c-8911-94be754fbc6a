import React from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';

import SuggestedSkills from './SkillsSuggestions/SuggestedSkills';
import UserSkillsByProficiencyLevel from './UserSkillsByProficiencyLevel';
import UserSkillsNoLevels from './UserSkillsNoLevels';
import { Skill, SkillsFormData } from './types';
import { skillsProficiencyLevelsEnabled } from './utils';

interface UserSkillsFromProps {
  skillsData: SkillsFormData;
  onSkillsChange: (skills: Array<Skill>) => void;
  onSkillDelete: (topicId: string, proficiencyLevel: string) => void;
  selectedSkillsLimit?: number;
  handleFocus?: () => void;
  handleMenuClose?: () => void;
  setIsSaveButtonDisabled?: (show: boolean) => void;
}

const UserSkillsFrom: React.FC<UserSkillsFromProps> = ({
  skillsData,
  onSkillsChange,
  onSkillDelete,
  selectedSkillsLimit = undefined,
  handleFocus = null,
  handleMenuClose,
  setIsSaveButtonDisabled
}) => {
  const { selected } = skillsData;

  const skillsTotalLength = Object.values(selected).reduce((acc, arr) => acc + arr.length, 0);
  const respectMaxSelectedSkillLimit = selectedSkillsLimit !== undefined;
  const disableAddingNewSkills = respectMaxSelectedSkillLimit && skillsTotalLength >= selectedSkillsLimit;

  return (
    <div className="skills-container pt-30">
      <div className="add-skills-you-have-label">
        {translatr('web.common.main', 'AddSkillsYouHave')}
      </div>
      {!disableAddingNewSkills && (
        <SuggestedSkills
          skillsData={skillsData}
          onSkillsChange={onSkillsChange}
          maxSkillsLimit={selectedSkillsLimit}
        />
      )}
      {skillsProficiencyLevelsEnabled() ? (
        <UserSkillsByProficiencyLevel
          skills={selected}
          onSkillChange={skill => onSkillsChange([skill])}
          onSkillDelete={onSkillDelete}
          disableAddingNewSkills={disableAddingNewSkills}
          handleFocus={handleFocus}
          handleMenuClose={handleMenuClose}
          setIsSaveButtonDisabled={setIsSaveButtonDisabled}
        />
      ) : (
        <UserSkillsNoLevels
          skills={selected}
          onSkillChange={skill => onSkillsChange([skill])}
          onSkillDelete={onSkillDelete}
          disableAddingNewSkills={disableAddingNewSkills}
          handleFocus={handleFocus}
          handleMenuClose={handleMenuClose}
        />
      )}

      {respectMaxSelectedSkillLimit && (
        <div className="max-limit-skills-label">
          <p>
            {translatr('web.common.main', 'AddedDeclaredSkillsCounter', {
              selectedSkills: skillsTotalLength,
              skillsLimit: selectedSkillsLimit
            })}
          </p>
        </div>
      )}
    </div>
  );
};

export default UserSkillsFrom;
