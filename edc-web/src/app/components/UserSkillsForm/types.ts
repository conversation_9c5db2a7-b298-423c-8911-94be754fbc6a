export interface SkillsFormData {
  selected: SkillsGroupedByLevel,
  created?: SkillsGroupedByLevel,
  deleted?: SkillsGroupedByLevel,
  updated?: SkillsGroupedByLevel,
}

export interface SkillsGroupedByLevel {
  [key: string]: Array<Skill>
}

export interface SuggestedSkill {
  id: string,
  label: string,
  name: string,
  level?: string
}

export interface Skill {
  id?: number,
  label: string,
  name: string,
  topicId: string,
  value?: string
  proficiencyLevel?: string,
  verified?:boolean
}

export interface ProficiencyLevel {
  name: string,
  label: string,
  value: string,
  translatedLevelDescription?: string,
  level?: string,
  range?: string,
  hidden: boolean
}
