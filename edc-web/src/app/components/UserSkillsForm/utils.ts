import { getCurrentUserLanguage } from '@pages/Projects/ProjectForm/helpers';
import {
  getAllProficiencyLevels,
  getLevelByDecimal, noLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import {
  ProficiencyLevel,
  Skill,
  SkillsFormData, SkillsGroupedByLevel, SuggestedSkill
} from './types';

export const NO_PROFICIENCY_LEVEL = 'NO_PROFICIENCY_LEVEL';
export const DEFAULT_PROFILE_SKILL_DETECTION_LEVEL = '0.6667';

export const EMPTY_SKILLS_DATA: SkillsFormData = {
  selected: {},
  created: {},
  updated: {},
  deleted: {}
};

export const createNoSkillProficiencyLevel = (hidden: boolean = false): ProficiencyLevel => ({
  name: NO_PROFICIENCY_LEVEL,
  value: NO_PROFICIENCY_LEVEL,
  label: noLevelPlaceholderOption()?.label,
  hidden
});

export const getSkillProficiencyLevelsBasedOnFilter = (allLevels:Array<ProficiencyLevel>, filter?: (level: ProficiencyLevel) => boolean): Array<ProficiencyLevel> => {
  if (filter) {
    return allLevels.filter(filter);
  }
  return allLevels;
};

export const getSkillProficiencyLevels = (): Array<ProficiencyLevel> => {
  const options = window?.__edOrgData?.proficiencyLevels;
  const currentUserLang = getCurrentUserLanguage();
  if (options) {
    const allProficiencyLevels = getAllProficiencyLevels(options, currentUserLang, false);
    return allProficiencyLevels.map(
      ({ name, label, value, translatedLevelDescription, level, range, hidden }: ProficiencyLevel) => ({
        name,
        value,
        label,
        translatedLevelDescription,
        level,
        range,
        hidden
      })
    );
  } else {
    return [];
  }
};

export const getSkillProficiencyLevelWithNoLevel = (hiddenNoSkillProficiencyLevel: boolean = false): Array<ProficiencyLevel> => {
  return [ createNoSkillProficiencyLevel(hiddenNoSkillProficiencyLevel), ...getSkillProficiencyLevels()]
}

export const filterOutSkillsFromUserPassport = (suggestedSkills: Array<SuggestedSkill>, userSkills: Array<Skill>): Array<SuggestedSkill> => {
  if (!suggestedSkills) {
    return [];
  } else if (!userSkills?.length) {
    return suggestedSkills;
  }
  const userSkillIds: Array<string> = userSkills.map((skill: Skill) => skill.topicId);
  return suggestedSkills.filter((skill: SuggestedSkill) => !userSkillIds.includes(skill.id));
};

export const mapSkillsByLevels = (skills: SkillsGroupedByLevel): Array<Skill> => {
  if (!skills || !Object.keys(skills).length) {
    return [];
  } else {
    let mappedSkills: Array<Skill> = [];
    Object.keys(skills).forEach(proficiencyLevel => {
      skills[proficiencyLevel].forEach(skill => {
        let mappedSkill = {
          topicId: skill.topicId,
          label: skill.label,
          name: skill.name
        };
        mappedSkills.push(mappedSkill);
      });
    });
    return mappedSkills;
  }
};

export const getDefaultProfileSkillDetectionLevel = () => {
  const defaultLevel = getLevelByDecimal(DEFAULT_PROFILE_SKILL_DETECTION_LEVEL);
  return defaultLevel?.name;
};


export const mapSuggestedSkill = (topicId: string, name: string, label: string, proficiencyLevel: string) => {
  return {
    topicId,
    name,
    label,
    proficiencyLevel
  };
};

export const mapToTable = (skills: SkillsGroupedByLevel) => {
  const mappedSkills: Array<Skill> = [];
  Object.keys(skills).forEach(proficiencyLevel => {
    skills[proficiencyLevel].forEach((skill: Skill) => {
      mappedSkills.push({ ...skill, proficiencyLevel });
    });
  });
  return mappedSkills;
};


// stay for rollback case - to easy get rid of this new functionality
export const isNoLevelSkillsVisible = () => {
  return true;
}

export const skillsProficiencyLevelsEnabled = () => {
  return getConfig("enabled_bia", false);
}
