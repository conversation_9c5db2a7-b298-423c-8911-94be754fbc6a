import React, { useRef } from 'react';
import { useSelector } from 'react-redux';
import { translatr } from 'centralized-design-system/src/Translatr';
import { AsyncSearchInput } from 'centralized-design-system/src/Inputs';
import Tooltip from 'centralized-design-system/src/Tooltip';

import { getCurrentUserLanguage } from '@pages/Projects/ProjectForm/helpers';
import LD from '../../containers/LDStore';
import { Skill } from './types';

interface SkillInputProps {
  levelName: string;
  skills: Array<Skill>;
  label: string;
  labelTooltip?: string;
  excludedSkillsIds: Array<string>;
  handleSkillChange: (skillsAfterChange: Array<any>) => void;
  disableAddingNewSkills?: boolean;
  handleFocus?: (e: any) => void;
  handleMenuClose?: () => void;
  hint?: string;
}

const SkillInput: React.FC<SkillInputProps> = ({
  levelName,
  skills,
  label,
  labelTooltip,
  excludedSkillsIds,
  handleSkillChange,
  disableAddingNewSkills = false,
  handleFocus,
  handleMenuClose,
  hint = ''
}) => {
  const getTopicsFromV3Domain = ['v3', 'FS-onboarding'].includes(LD.onboardingVersion());
  const taxonomyDomain = useSelector((state: any) => state.team?.get('config')?.taxonomy_domain);

  const skillParentRef = useRef(null);

  return (
    <div
      className="skill-with-proficiency-level"
      ref={skillParentRef}
      onFocus={(e: any) => {
        if (e.target !== skillParentRef.current) {
          e.target = skillParentRef.current;
        }
        handleFocus(e);
      }}
      onBlur={handleMenuClose}
    >
      <label htmlFor={`skill-search-${levelName}`} className="skill-with-proficiency-level__title">
        <Tooltip pos="top" message={labelTooltip} hide={!labelTooltip}>
          {label}
        </Tooltip>
      </label>
      <div>
         <AsyncSearchInput
          id={`skill-search-${levelName}`}
          multiselect
          placeholder={translatr('web.projects.main', 'SearchSkills2')}
          skills={getTopicsFromV3Domain}
          users={false}
          channels={false}
          groups={false}
          onChange={handleSkillChange}
          items={skills}
          topics={!getTopicsFromV3Domain}
          extraPayload={{
            fields: 'id,label,name'
          }}
          extraData={{
            taxonomyDomain,
            currentUserLang: getCurrentUserLanguage()
          }}
          excludeSkillsIds={excludedSkillsIds}
          defaultAriaLabel={label}
          disableAddingNew={disableAddingNewSkills}
        />
      </div>
      {hint && <div className="skill-with-proficiency-level__hint">{hint}</div>}
    </div>
  );
};


export default SkillInput;
