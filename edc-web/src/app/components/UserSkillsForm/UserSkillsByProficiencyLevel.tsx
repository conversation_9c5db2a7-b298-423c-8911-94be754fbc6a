import React, { useState, useEffect, useRef } from 'react';

import {
  createNoSkillProficiencyLevel,
  getSkillProficiencyLevelWithNoLevel,
  isNoLevelSkillsVisible,
  getSkillProficiencyLevels,
  NO_PROFICIENCY_LEVEL,
  getSkillProficiencyLevelsBasedOnFilter
} from './utils';
import { translatr } from 'centralized-design-system/src/Translatr';
import SkillInput from './SkillInput';
import { ProficiencyLevel, Skill, SkillsGroupedByLevel } from './types';


interface UserSkillsByProficiencyLevelProps {
  skills: SkillsGroupedByLevel;
  onSkillChange: (skill: Skill) => void;
  onSkillDelete: (topicId: string, proficiencyLevel: string) => void;
  disableAddingNewSkills?: boolean;
  handleFocus?: () => void;
  handleMenuClose?: () => void;
  setIsSaveButtonDisabled?: (show: boolean) => void;
}

const UserSkillsByProficiencyLevel: React.FC<UserSkillsByProficiencyLevelProps> = ({
  skills,
  onSkillChange,
  onSkillDelete,
  disableAddingNewSkills = false,
  handleFocus,
  handleMenuClose,
  setIsSaveButtonDisabled
}) => {
  const [excludedSkillsIds, setExcludedSkillsIds] = useState([]);

  const extractLevelsWithSkills = (skills: SkillsGroupedByLevel): Array<string> => {
    return Object.entries(skills)
      .filter(([_, skillsArray]) => skillsArray.length > 0)
      .map(([level, _]) => level);
  };

  const levelNamesRef = useRef(extractLevelsWithSkills(skills));

  const displayProficiencyLevelFilter = ((proficiencyLevel: ProficiencyLevel): boolean => {
    return !proficiencyLevel.hidden || proficiencyLevel.name != '' && levelNamesRef.current.includes(proficiencyLevel.name);
  });

  const proficiencyLevels = (() => {
    if(isNoLevelSkillsVisible() && skills?.[NO_PROFICIENCY_LEVEL]?.length > 0) {
      return getSkillProficiencyLevelWithNoLevel(!window?.__edOrgData?.displayNoLevelInProficiencyLevels)
    }
    return getSkillProficiencyLevels();
  })();

  const hasHiddenLevels = ((): boolean => {
    const selectedLevelNames = extractLevelsWithSkills(skills);
    const currentSelectedProficiencyLevelFilter = ((proficiencyLevel: ProficiencyLevel): boolean => {
      return proficiencyLevel.name != '' && selectedLevelNames.includes(proficiencyLevel.name);
    });
    const status = getSkillProficiencyLevelsBasedOnFilter(proficiencyLevels, currentSelectedProficiencyLevelFilter)
      .some(proficiencyLevel => proficiencyLevel.hidden)
    return status;
  });

  useEffect(() => {
    const excludedIds = Object.values(skills)
      .flatMap(arr => arr)
      .map(skill => skill.topicId);
    setExcludedSkillsIds(excludedIds);
    if (setIsSaveButtonDisabled){
      setIsSaveButtonDisabled(hasHiddenLevels());
    }
  }, [skills]);



  return (
    <div>
      {
       getSkillProficiencyLevelsBasedOnFilter(proficiencyLevels, displayProficiencyLevelFilter)
        .map(({ name: levelName, label, translatedLevelDescription, hidden }: ProficiencyLevel) => {
        const handleSkillChange = (skillsAfterChange: Array<any>) => {
          const idsOfRemainingSkills = skillsAfterChange.map((skill: any) => skill.value);
          const topicId = skills[levelName]?.find(
            skill => !idsOfRemainingSkills.includes(skill.topicId)
          )?.topicId;

          if (topicId) {
            onSkillDelete(topicId, levelName);
            return;
          }

          const { value, name, label } = skillsAfterChange[skillsAfterChange.length - 1];
          onSkillChange({ topicId: value, name, label, proficiencyLevel: levelName });
        };

        return <SkillInput
          key={levelName}
          levelName={levelName}
          label={label}
          labelTooltip={translatedLevelDescription}
          skills={skills[levelName]}
          handleSkillChange={handleSkillChange}
          excludedSkillsIds={excludedSkillsIds}
          disableAddingNewSkills={disableAddingNewSkills || levelName === createNoSkillProficiencyLevel().name || hidden}
          handleFocus={handleFocus}
          handleMenuClose={handleMenuClose}
          hint={hidden ? translatr('web.common.main', 'LevelWasDisabled'): ''}
        />
      })}
    </div>
  );
};

export default UserSkillsByProficiencyLevel;
