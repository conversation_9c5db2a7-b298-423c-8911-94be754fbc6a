import React, { useState, useRef } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';

import { SuggestedSkillsModal } from '@components/modals/SuggestedSkillsModal';
import {
  filterOutSkillsFromUserPassport,
  getSkillProficiencyLevels,
  getDefaultProfileSkillDetectionLevel,
  mapSkillsByLevels,
  mapSuggestedSkill,
  skillsProficiencyLevelsEnabled,
  NO_PROFICIENCY_LEVEL
} from '../utils';
import {
  ProficiencyLevel,
  Skill,
  SkillsFormData,
  SuggestedSkill
} from '../types';
import useSuggestedSkills from './useSuggestedSkills';
import SuggestedSkillTag from './SuggestedSkillTag';
import './SuggestedSkills.scss';

const NUMBER_OF_SKILL_TAGS = 3;

interface SuggestedSkillsProps {
  skillsData: SkillsFormData,
  onSkillsChange: (updatedSkills: Array<Skill>) => void;
  maxSkillsLimit?: number;
}

const SuggestedSkills: React.FC<SuggestedSkillsProps> = ({
  skillsData,
  onSkillsChange,
  maxSkillsLimit
}) => {
  const currentSkills: Array<Skill> = mapSkillsByLevels(skillsData?.selected);
  const { suggestedSkills, isSuggestedSkillsLoading, suggestedSkillsError, isDefaultOnboardingTopicsSuggested } = useSuggestedSkills(skillsData?.selected, skillsData?.deleted);
  const filterOutSkillsSuggestions: Array<SuggestedSkill> = filterOutSkillsFromUserPassport(suggestedSkills, currentSkills);

  const suggestedSkillsDescriptionVisible = !isDefaultOnboardingTopicsSuggested;
  const skillProficiencyLevels: Array<ProficiencyLevel> = getSkillProficiencyLevels().filter(proficiencyLevel => !proficiencyLevel.hidden);

  const [skillsModalOpen, setSkillsModalOpen] = useState<boolean>(false);
  const moreSuggestedSkillsButtonRef = useRef(null);

  const handleSubmitSuggestedSkills = (addedSkills: Array<SuggestedSkill>) => {
    const mappedSuggestedSkills: Array<Skill> = addedSkills.map((skill: SuggestedSkill) =>
      mapSuggestedSkill(skill.id, skill.name, skill.label, skill.level)
    );
    onSkillsChange(mappedSuggestedSkills);
    setSkillsModalOpen(false);
  };

  if(isSuggestedSkillsLoading) {
    return <Skeleton height={80} width="100%" />
  }

  return (
    <>
      {(!!filterOutSkillsSuggestions?.length || suggestedSkillsError) && (
        <div className="skills-suggested-for-user">
          {skillsModalOpen && (
            <SuggestedSkillsModal
              selectedSkillsLength={currentSkills.length}
              subtitle={translatr('web.talentmarketplace.main', 'SelectSuggestedSkills')}
              supportsSkillsLevel={skillsProficiencyLevelsEnabled()}
              skillLevels={skillProficiencyLevels}
              skills={filterOutSkillsSuggestions?.map(({ id, label, name }: SuggestedSkill) => ({
                id,
                label,
                name
              }))}
              closeModal={() => setSkillsModalOpen(false)}
              onSubmit={(newSuggestedSkills: Array<SuggestedSkill>) => handleSubmitSuggestedSkills(newSuggestedSkills)}
              selectButtonTitle={translatr('web.common.main', 'Add')}
              defaultProficiencySkillLevel={skillsProficiencyLevelsEnabled() ? getDefaultProfileSkillDetectionLevel() : NO_PROFICIENCY_LEVEL}
              openedFromHtmlElement={moreSuggestedSkillsButtonRef?.current}
              isNumberOfSkillsLimited={maxSkillsLimit && maxSkillsLimit > 0}
              maxSkillsLimit={maxSkillsLimit}
            />
          )}
          <div className="skills-suggested-for-user__content">
            <div className="skills-suggested-for-user__content__title">
              <h3 id="skills-suggested-for-user-title">{translatr('web.projects.main', 'SuggestedSkills')}</h3>
            </div>
            <div className="skills-suggested-for-user__content__subtitle">
              {suggestedSkillsDescriptionVisible &&
                translatr(
                  'web.talentmarketplace.main',
                  'SuggestedSkillsBasedOnYourCurrentRoleAndWorkHistory'
                )}
            </div>
            {!suggestedSkillsError && (
              <ul className="skills-suggested-for-user__content__tags">
                {filterOutSkillsSuggestions.slice(0, NUMBER_OF_SKILL_TAGS).map((skill, id) =>
                  <SuggestedSkillTag
                    key={skill.id}
                    skill={skill}
                    skillProficiencyLevels={skillProficiencyLevels}
                    onSkillsChange={onSkillsChange}
                  />
                )}
                {filterOutSkillsSuggestions.length - NUMBER_OF_SKILL_TAGS > 0 && (
                  <li className="user-skill-tag">
                    <button
                      id="user-skill-tags-more-button"
                      ref={moreSuggestedSkillsButtonRef}
                      className="user-skill-tag__button"
                      onClick={() => setSkillsModalOpen(true)}
                      aria-labelledby='user-skill-tags-more-button skills-suggested-for-user-title'
                    >
                      <span aria-hidden="true">+ </span>
                      {translatr('web.projects.main', 'MoreSuggestedSkills', {
                        remainingSkillsCount: filterOutSkillsSuggestions?.length - 3
                      }).replace('+', '')}
                    </button>
                  </li>
                )}
              </ul>
            )}
            {suggestedSkillsError && (
              <div className="skills-suggested-for-user__content__error">
                {translatr('web.talentmarketplace.main', 'WeCouldNotFetchSkillsData')}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default SuggestedSkills;
