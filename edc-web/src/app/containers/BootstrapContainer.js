import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Routing from '../RoutingWrapper.jsx';
import AppBody from './AppBody';
import { getNavigationFeature } from 'edc-web-sdk/requests/superAdmin.js';
// import { getMfeAccessToken } from 'edc-web-sdk/requests/mfes.js';

import LD from '../containers/LDStore';
import LazyloadComponent from '@components/LazyloadComponent';
import { isConfigServiceEnabled } from '../../FeatureManagementClient';
import { IP_ADDRESS_NOT_ALLOWED } from '../utils/constants';
import { saveGroupThemeName } from '@actions/themeActions.js';

import {
  ENABLE_LX_MEDIA_HUB,
  LXMediaServicePayload,
  LX_MEDIA_HUB_DEFAULT_IMAGES
} from 'centralized-design-system/src/Utils/constants';
import { getConfigService } from 'edc-web-sdk/helpers/getConfigService';
import { getLXMediaHubDefaultImages } from 'edc-web-sdk/requests/lxMediaHub';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';

const Header = LazyloadComponent(() => import('centralized-design-system/src/Header'))(false);
const UnifiedNav = LazyloadComponent(() => import('../components/Header'))(false);

const ChatBot = LazyloadComponent(() => import('../components/chatbot'))(false);
const EdBot = LazyloadComponent(() => import('../components/EdBot'))(false);

const AppFooter = LazyloadComponent(() => import('centralized-design-system/src/Footer/footer'))();

/**
 * This is our "main container". It start the rendering of the nav and body.
 * Since some of our components rely on exact placements, we need to make sure the CSS is loaded first
 * and then render the application itself.
 */
const BootstrapContainer = props => {
  const {
    history,
    orgDetailsFetched,
    onboardingCompleted,
    teamsLoaded,
    isLoggedIn,
    dispatch,
    permissions,
    userInfoUpdated,
    isMobilePublicRoute
  } = props;
  const inSideApp = isLoggedIn && onboardingCompleted;
  const [ready, setReady] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [flagsLoaded, setFlagsLoaded] = useState(false);
  const navV2FlagEnabled = useRef(false);

  /**
   * The default z-index: 99999 is setup in the page.html file.
   * We add this to make the z-index: 100000 in the styles.scss file so that we can verify it is loaded
   * and parsed before removing the loader screen.
   */
  let count = 1;
  function verifyCSSLoaded() {
    let testCss = window.getComputedStyle(appLoader).zIndex;
    // IE11 is a Number, other browsers are a String
    if (testCss == '100000') {
      if (!loaded) {
        setLoaded(true);
      }
      appLoader.classList.add('loaded');
    } else {
      setTimeout(() => {
        // Continous backoff check for slow networks.
        count++;
        verifyCSSLoaded();
      }, 100 * count);
    }
  }

  const appLoader = document.getElementById('splashscreen');
  if ((inSideApp || orgDetailsFetched) && appLoader) {
    appLoader.classList.add('preloaded');
    verifyCSSLoaded();
  }

  let APP_LAYOUT = null;

  // Wait for teams to be loaded only if there are groups
  let hasMenuItemsRestrictedToGroups = false;
  let teamsCheckCompleted = true;
  try {
    const numberOfGroups = Object.values(
      window.__edOrgData.configs.find(c => c.name === 'OrgCustomizationConfig').value.web.topMenu
    ).reduce((acc, cur) => {
      return acc + (cur?.groups?.length || 0);
    }, 0);
    hasMenuItemsRestrictedToGroups = numberOfGroups > 0 ? true : false;

    if (hasMenuItemsRestrictedToGroups && teamsLoaded === null) {
      teamsCheckCompleted = false;
    }
  } catch (e) {
    teamsCheckCompleted = true;
    console.error('Error determining Restricted Groups in BootstrapContainer.jsx', e);
  }

  const isAccessDenied = window?.__edOrgData?.message === IP_ADDRESS_NOT_ALLOWED;

  const etisalat_chatbot = window.__edOrgData?.configs.find(x => x.name == 'etisalat_chatbot');

  const isUnifiedNavEnabled = window.__edOrgData?.configs.find(
    a => a.name === 'enable_navigation_v2'
  )?.value;

  /**
   * Get MFE data and permissions
   */
  async function getMfeData() {
    // This function may get expanded for permissions later on
    // const MfeEnabled = window.__edOrgData.configs.find(config => config.name === 'mfe_tms_config')?.value;

    /**
     * Multiple MFE's being loaded in and out of state can cause the _services event bus to be "reset" to an empty object
     * This will cause any global MFEs (e.g. SkillsFlyout) to be unregistered.
     * This creates a getter/setter for the
     */
    window._csod = {};
    Object.defineProperty(window, 'csod', {
      get: function() {
        return window._csod;
      },
      set: function(newValue) {
        const oldValue = window._csod;
        if (oldValue !== newValue) {
          let _new_services = newValue?._services || {};
          let _services = window._csod?.services || {};
          if (Object.keys(_new_services).length !== 0) {
            _services = { ..._services, ..._new_services };
          }
          window._csod = { ...newValue, _services };
        }
      },
      configurable: true, // Allows redefining/deleting later
      enumerable: true // Shows up in for...in loops
    });

    return Promise.resolve();
  }

  /**
   * Validate nav v2 and get configs if required
   */
  async function getNavV2Flag() {
    if (isUnifiedNavEnabled) {
      return await getNavigationFeature();
    }
    return Promise.resolve(false);
  }

  /**
   * Initialize Legacy Flags
   */
  function legacyFlags() {
    return new Promise(resolve => {
      // we wait for config service to return permissions then set routes
      if (isConfigServiceEnabled && isLoggedIn) {
        window.ldclient.secureWaitUntilReady
          .then(() => {
            setFlagsLoaded(true);
            resolve(true);
          })
          .catch(() => {
            console.error('Failed to get load secure configs.');
            resolve(true);
          });
      } else {
        // config service is not enabled so using ld to get permissions which is ready by now
        resolve(true);
      }
    });
  }

  async function init() {
    const flags = await Promise.allSettled([legacyFlags(), getNavV2Flag(), getMfeData()]);
    try {
      navV2FlagEnabled.current = flags[1]?.value?.navigation_v2 ?? false;
    } catch (err) {
      console.error('Unable to state of Nav V2 configuration', err);
    }
    setReady(true);
  }

  /**
   * Once we verify everything is actually loaded, then we can start rendering.
   */
  useEffect(() => {
    // userInfoUpdated is made after a info.json call
    // this will be set to true regardless if 200 or 401 response
    if (userInfoUpdated) {
      init();
    }
  }, [userInfoUpdated, inSideApp]);

  useEffect(() => {
    handleLXMediaHub();
  }, [isLoggedIn]);

  const handleLXMediaHub = async () => {
    try {
      if (isLoggedIn) {
        // Await the configuration service call to ensure platform rendering and upload service
        await getConfigService(LXMediaServicePayload);
        const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);
        if (isLXMediaHubEnabled && !localStorage.getItem(LX_MEDIA_HUB_DEFAULT_IMAGES)) {
          const defaultImages = await getLXMediaHubDefaultImages();
          let object = { value: defaultImages.stock_images, timestamp: new Date().getTime() };
          localStorage.setItem(LX_MEDIA_HUB_DEFAULT_IMAGES, JSON.stringify(object));
        }
      }
    } catch (err) {
      console.error(`Error in fetching LXMediaHUb: ${err}`);
    }
  };

  if (loaded) {
    const showUnifiedNav =
      isUnifiedNavEnabled &&
      navV2FlagEnabled.current &&
      [...permissions.values()].includes('ENABLE_UNIFIED_NAVIGATION');
    window.isUserSeeingNavV2 = showUnifiedNav;

    if (isAccessDenied || window.location.pathname.includes('/saml/auth')) {
      return (APP_LAYOUT = <Routing history={history} />);
    }

    if (inSideApp) {
      dispatch(saveGroupThemeName(window?.__ED__?.brandingTheme?.name));
    }

    APP_LAYOUT = (
      <>
        {onboardingCompleted &&
          teamsCheckCompleted &&
          ready &&
          flagsLoaded &&
          !isMobilePublicRoute &&
          (showUnifiedNav ? <UnifiedNav /> : <Header />)}
        {ready && (
          <>
            {orgDetailsFetched ? <Routing history={history} /> : inSideApp && <AppBody />}
            {inSideApp && !isMobilePublicRoute && <AppFooter />}
          </>
        )}
        {etisalat_chatbot?.value?.enabled && <ChatBot />} {LD.isEdBot() && <EdBot />}{' '}
      </>
    );
  }

  return APP_LAYOUT;
};

BootstrapContainer.defaultProps = {
  orgDetailsFetched: false,
  isLoggedIn: false,
  onboardingCompleted: false,
  teamsLoaded: null,
  permissions: []
};

BootstrapContainer.propTypes = {
  orgDetailsFetched: PropTypes.bool,
  isLoggedIn: PropTypes.bool,
  onboardingCompleted: PropTypes.bool,
  teamsLoaded: PropTypes.any,
  navigate: PropTypes.func,
  permissions: PropTypes.any,
  isMobilePublicRoute: PropTypes.bool
};

BootstrapContainer.displayName = 'BootstrapContainer';

function mapStateToProps(state) {
  return {
    isLoggedIn: state.currentUser.get('isLoggedIn'),
    userInfoUpdated: state.currentUser.get('userInfoUpdated'),
    onboardingCompleted: state.currentUser.get('onboardingCompleted'),
    teamsLoaded: state.currentUser.get('followedTeams'),
    orgDetailsFetched: state.team.get('orgDetailsFetched'),
    permissions: state.currentUser.get('permissions') || []
  };
}

/**
 * Everything has to come from a global state!
 */
const WrappedBootstrapContainer = connect(mapStateToProps)(BootstrapContainer);

/**
 * Memo'izig is required since we 'dispatch' many events that force redux to re-render the application.
 * Since this is the root of the application, there are numerous requests for this.
 */
export default React.memo(WrappedBootstrapContainer, (props, prevProps) => {
  return (
    props.isLoggedIn !== prevProps.isLoggedIn ||
    props.onboardingCompleted !== prevProps.onboardingCompleted ||
    props.orgDetailsFetched !== prevProps.orgDetailsFetched
  );
});
