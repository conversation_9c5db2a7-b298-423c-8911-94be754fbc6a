import ComponentSuspenseHOC from './ComponentSuspenseHOC';
import LazyloadComponent from '../components/LazyloadComponent';
import LoginSignupContainer from './LoginSignup';

export const LoginSignupPage = ComponentSuspenseHOC(() => LoginSignupContainer);

export const CurateChannelCards = LazyloadComponent(() =>
  import('../pages/channel/curation/CurationContainer')
)();
export const ViewCardDetailsPage = LazyloadComponent(() =>
  import('@components/cardStandardization/viewCardDetails/ViewCardDetailsContainer')
)();

export const PathwayConsumptionContainer = LazyloadComponent(() =>
  import('@components/cardStandardization/pathway/PathwayConsumptionContainer')
)();

export const JourneyConsumptionContainer = LazyloadComponent(() =>
  import('@components/cardStandardization/journey/JourneyConsumptionContainer')
)();

export const GroupPageContainerv2 = LazyloadComponent(() =>
  import('../pages/group/consumption/GroupConsumption')
)();
export const LeaderBoardContainerv2 = LazyloadComponent(() =>
  import('../pages/team/leaderboard/LeaderboardContainer')
)();

export const MyChannelsContainerv2 = LazyloadComponent(() =>
  import('../pages/channel/all_channel/AllChannelPage')
)();

export const SkillsAssessmentV2 = LazyloadComponent(() => import('../pages/SkillsAssessmentV2'))();

export const AdminHomePage = LazyloadComponent(() => import('../pages/adminconsole'))();

// forgot password routes component
export const ForgotPasswordContainer = LazyloadComponent(() =>
  import('../pages/Login/forgot_password/ForgotPasswordLayout')
)();
export const SendLinkContainer = LazyloadComponent(() =>
  import('../pages/Login/forgot_password/ForgotPasswordForm')
)();
export const SentLinkContainer = LazyloadComponent(() =>
  import('../pages/Login/forgot_password/EmailSentMessage')
)();
export const ChangedPasswordContainer = LazyloadComponent(() =>
  import('../pages/Login/forgot_password/PasswordResetSuccessful')
)();
export const ConfirmPasswordContainer = LazyloadComponent(() =>
  import('../pages/Login/forgot_password/PasswordResetForm')
)();
export const InvalidPasswordContainer = LazyloadComponent(() =>
  import('../pages/Login/forgot_password/EmailExpired')
)();

export const SamlAuthHandlerContainer = LazyloadComponent(() =>
  import('../pages/Login/SamlAuthHandler')
)();

export const PathwayStandaloneWrapper = LazyloadComponent(() =>
  import('@components/cardStandardization/PathwayWrapper')
)();
export const JourneyStandaloneWrapper = LazyloadComponent(() =>
  import('@components/cardStandardization/JourneyWrapper')
)();

//create group route
export const CreateGroupContainer = LazyloadComponent(() =>
  import('../pages/group/creation/CreateUpdateGroupContainer')
)();

export const logOutContainer = LazyloadComponent(() =>
  import('../components/LogOut/LogOutContainer')
)();

export const MyOpportunities = LazyloadComponent(() =>
  import('../pages/TalentMarketplace/MyOpportunities')
)();

// Classic components starts
export const Registration = LazyloadComponent(() =>
  import('../pages/FspRegistration/FspRegistrationContainer')
)();

// this has been used in ocg wrapper
export const SkillsAssessment = LazyloadComponent(() =>
  import('../components/skillsAssessment/SkillsAssessment')
)();

export const SkillUpdateContainer = LazyloadComponent(() =>
  import('../components/SkillUpdateContainer/SkillUpdateContainer')
)();
export const UnsubscribeEmails = LazyloadComponent(() =>
  import('../components/unsubscribe/UnsubscribeEmails.jsx')
)();

export const SkillsAssessmentWrapper = LazyloadComponent(() => import('../pages/OCG/Wrapper'))();
export const OCGPersonal = LazyloadComponent(() => import('../pages/OCG/Personal'))();
export const OCGPeers = LazyloadComponent(() => import('../pages/OCG/Peers'))();

export const TeamAnalyticsBase = LazyloadComponent(() =>
  import('../components/team/SingleTeam/analytics/TeamAnalyticsBase.jsx')
)();
export const TeamAssignmentAnalyticsContainer = LazyloadComponent(() =>
  import('../components/team/SingleTeam/analytics/TeamAssignmentAnalyticsContainer')
)();
export const TeamInsightAnalyticsContainer = LazyloadComponent(() =>
  import('../components/team/SingleTeam/analytics/TeamInsightAnalyticsContainer')
)();

export const SkillsGraph = LazyloadComponent(() =>
  import('../components/skillsGraph/SkillsGraph')
)();
export const SkillsDirectory = LazyloadComponent(() =>
  import('../components/skillsDirectory/SkillsDirectory')
)();

export const InsightContainer = LazyloadComponent(() =>
  import('../components/insight/InsightContainer.jsx')
)();

export const ScormContainer = LazyloadComponent(() =>
  import('../components/cardStandardization/content/scorm/ScormPlayerContainer.jsx')
)();

export const SingUpAccessContainer = LazyloadComponent(() =>
  import('../containers/SingUpAccessContainer')
)();

export const MobileBanner = LazyloadComponent(() =>
  import('../components/mobile/MobileBanner.jsx')
)();

export const AccountDetailsContainer = LazyloadComponent(() =>
  import('../components/settings/MyAccountDetails/AccountDetailsContainer')
)();
export const CareerPreferencesContainer = LazyloadComponent(() =>
  import('../components/settings/CareerPreferences/CareerPreferencesContainer')
)();
export const SettingsContainer = LazyloadComponent(() =>
  import('../components/settings/SettingsContainer')
)();
export const UserTriggersContainer = LazyloadComponent(() =>
  import('../components/settings/MyTriggers/TriggersContainer')
)();
export const IntegrationsContainer = LazyloadComponent(() =>
  import('../components/settings/MyIntegrations/index')
)();

export const OtherUsersDetails = LazyloadComponent(() =>
  import('../components/settings/OtherDetails/OtherUsersDetails')
)();

export const pageNotFound = LazyloadComponent(() => import('../components/PageNotFound'))();

// Classic components ends

export const ManagerRatingReview = LazyloadComponent(() =>
  import('../components/ManagerRatingReviewModalWrapper')
)();

export const AccessDenied = LazyloadComponent(() => import('../pages/AccessDenied/index'))();
