import React, { useEffect, useState } from 'react';
import { bool, node, string } from 'prop-types';
import { Routes, Route, Navigate } from 'react-router-dom';
import { connect, useDispatch } from 'react-redux';

import * as Pages from '../app/pages';
import * as AppPages from '../app/containers/AppPages';
import { Permissions } from '../app/utils/checkPermissions';
import { Translatr, translatr } from 'centralized-design-system/src/Translatr';
import LD from '../app/containers/LDStore';
import EmptyContainer from '../app/components/EmptyContainer';
import {
  TALENT_MARKETPLACE_SOURCING_ENABLED,
  shouldAllowTMProjectCreate,
  shouldShowTMProject,
  shouldShowTMMentorship,
  shouldShowTMJobVacancy,
  shouldShowTMJobRole,
  shouldShowAtLeastOneTMContent,
  shouldAllowJobEdit,
  shouldShowHomev2,
  shouldShowDevelopmentPlan,
  OMP_URL_BY_TYPE
} from 'opportunity-marketplace/util';
import { JOB_TYPE, OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { isAdminFunc } from '@utils/utils';
import AuthorizedContainer from '../app/containers/AuthorizedContainer';
import LoggedInAppBodyController from '../app/containers/LoggedInAppBodyController';
import RedirectPathToCareer from './containers/RedirectMeToCareer';
import rubyHandledPath from '../app/constants/rubyHandledPath';
import { MfeTest } from '@components/MfeTest';
import { isConfigServiceEnabled } from '../FeatureManagementClient';
import { LXP_ENV_TYPE } from 'edc-web-sdk/config/envConstants';
import { isNewContentEnabled } from '@pages/MyContentV2/utils';
import { isNewProfileEnabled } from '@pages/MyProfileV2/utils';
import { isNewMyLearningEnabled } from '@pages/MyLearningV2/utils';
import { isLowerEnvironment } from '@utils/isLowerEnvironment';
import { isSkillsAssessmentEnabled } from '@pages/SkillsAssessmentV2/utils';
import AccessDeniedView from '@components/common/AccessDeniedView';
import Loading from 'centralized-design-system/src/Loading';
import LazyloadComponent from '@components/LazyloadComponent';

const ImpersonateMessage = LazyloadComponent(() =>
  import('./components/ImpersonateMessage/ImpersonateMessage.js')
)(false);

const showSecondaryManager = global?.__edOrgData?.configs?.find(f => f.name === 'md_configuration')
  ?.value?.show_secondary_manager;

// Basic Auth style route handling. We still need to load the routes since they're passed from a parent Context/Consumer

function AuthRoute({ children: Children, isSecondaryManager, ...rest }) {
  let isAuth = false;
  let path = rest.path;
  let CustomUnavailableMsgComp = null;

  // Check Manager Dashboard access
  if (
    path.indexOf('manager-dashboard') > -1 &&
    ((window.__ED__.isManager && window.__ED__.hasReporters) ||
      (showSecondaryManager && isSecondaryManager))
  ) {
    isAuth = true;
  }

  // Check Skill Directory access
  if (
    path.indexOf('skills-directory') > -1 &&
    window?.__edOrgData?.configs.find(item => item.name === 'skills_directory_enablement')?.value &&
    Permissions.has('SHOW_SKILLS_DIRECTORY')
  ) {
    isAuth = true;
  }

  // Check for Analytics/Report access
  if (path.indexOf('analytics') > -1) {
    let aPerm = Permissions.has('MANAGE_GROUP_ANALYTICS') || Permissions.has('MANAGE_ANALYTICS');
    let aAuth = window.__ED__.isAdmin || window.__ED__.isOrgAdmin || window.__ED__.isSuperAdmin;
    if (aPerm && aAuth) {
      isAuth = true;
    }
  }

  // Check for OMP access
  if (path.indexOf('career') > -1 && shouldShowAtLeastOneTMContent()) {
    isAuth = true;
  }

  if (
    (path.includes('career-preferences') ||
      path.includes(OMP_URL_BY_TYPE[OPPORTUNITY_TYPE.JOB_ROLE]) ||
      path.includes(OMP_URL_BY_TYPE[OPPORTUNITY_TYPE.JOB_VACANCY])) &&
    LD.showCareerGrowthTab()
  ) {
    isAuth = true;
  }

  if (
    path.indexOf('sourcing') > -1 &&
    LD.showCareerGrowthTab() &&
    TALENT_MARKETPLACE_SOURCING_ENABLED &&
    Permissions.has('MANAGE_CAREER_GROWTH')
  ) {
    isAuth = true;
  }

  if (path === '/me' || path === '/settings') {
    let aAuth = !['user_dashboard', 'manager_dashboard'].includes(window?.__ED__?.proxyType);
    if (aAuth) {
      isAuth = true;
    }
  }

  if (path === '/manage-block-users') {
    const isAdmin = isAdminFunc(window.__ED__.isAdmin);
    isAuth = !isAdmin;
  }

  if (path === '/manager-dashboard/learning/content') {
    isAuth = true;
  }

  if (path === `/career/${OPPORTUNITY_TYPE.PROJECT}/new` && shouldAllowTMProjectCreate()) {
    isAuth = true;
  }

  if (path.includes(`career/detail/${OPPORTUNITY_TYPE.PROJECT}`) && shouldShowTMProject()) {
    isAuth = true;
  }

  if (path.includes(`career/detail/${OPPORTUNITY_TYPE.MENTORSHIP}`) && shouldShowTMMentorship()) {
    isAuth = true;
  }

  if (path.includes(`configure/homepage`) && Permissions.has('EDIT_LANDING_PAGE')) {
    isAuth = true;
  }

  if (
    path.includes(`/mfe-loader-testing/`) &&
    LXP_ENV_TYPE &&
    ['staging', 'qa'].includes(LXP_ENV_TYPE)
  ) {
    isAuth = true;
  }

  // Check if request is a SAML request  and bypass the auth check
  if (path.includes(`/saml`) && LXP_ENV_TYPE && ['staging', 'qa'].includes(LXP_ENV_TYPE)) {
    isAuth = true;
  }

  if (path.includes('/smartsearch')) {
    if (window.__ED__.isGlobalSearchEnabled) {
      isAuth = true;
    } else {
      CustomUnavailableMsgComp = AccessDeniedView;
    }
  }

  return isAuth ? (
    Children
  ) : CustomUnavailableMsgComp ? (
    <CustomUnavailableMsgComp />
  ) : (
    <div className="text-center" style={{ marginTop: '100px' }}>
      <h4>{translatr('web.common.main', 'SorryThisPageIsNotAvailable')}</h4>
    </div>
  );
}

AuthRoute = connect(state => {
  const currentUser = state.currentUser;
  const isSecondaryManager = currentUser.get('isSecondaryManager');
  return {
    isSecondaryManager
  };
})(AuthRoute);

function ProtectedRoutes({
  fsPrimeConfig,
  isLeaderboardConfigEnabled,
  isSkillsAssessmentAvailable,
  impersonatee,
  impersonator
}) {
  const [ready, setReady] = useState(false);
  const dispatch = useDispatch();

  const isHomeV2Enabled = shouldShowHomev2();
  const MD_TEAM_SKILLS_ASSESSMENT = global?.__edOrgData?.configs?.find(
    f => f.name === 'md_configuration'
  )?.value.show_skill_assessment;

  const MD_BLOCKS_ENABLED = window?.__edOrgData?.configs.find(a => a.name === 'md_configuration')
    ?.value?.galaxy_manager_dashboard;
  const showMDContentDrilldown =
    LD.isManagerDashboardEnabled() && LD.isManagerDashboardV2Enabled() && MD_BLOCKS_ENABLED;
  const newProfileEnabled = isNewProfileEnabled();
  const newContentEnabled = isNewContentEnabled();
  const newMyLearningEnabled = isNewMyLearningEnabled();

  useEffect(() => {
    // we wait for config service to return permissions then set routes
    if (isConfigServiceEnabled) {
      window.ldclient.secureWaitUntilReady
        .then(() => setReady(true))
        .catch(() => {
          console.error('Failed to get load secure configs. Some routes may not work.');
          setReady(true);
        });
    } else {
      // config service is not enabled so using ld to get permissions which is ready by now
      setReady(true);
    }
  }, []);

  return (
    <AuthorizedContainer>
      {ready && (
        <LoggedInAppBodyController>
          <Translatr apps={['web.common.main']}>
            <ImpersonateMessage
              impersonatee={impersonatee}
              impersonator={impersonator}
              dispatch={dispatch}
            />
          </Translatr>
          <Routes>
            {/*
             * Redirection from base is handled on routing container or the unified nav.
             * Have a base path here to prevent fallback route from loading to prevent flash of content.
             */}
            <Route path="/" element={<Loading />} />
            <Route path="/access-denied" element={<AppPages.AccessDenied />} />
            {isHomeV2Enabled && (
              <Route
                key="/home"
                path="/home"
                element={
                  <Translatr
                    apps={[
                      'web.home.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.skills-assessments-v2.main',
                      'web.talentmarketplace.main',
                      'web.channel.main',
                      'web.landingpage.main',
                      'web.topics.main'
                    ]}
                  >
                    <Pages.HomeV2 />
                  </Translatr>
                }
              />
            )}
            {/* End: Home page route */}

            {/** Start: checkin routes */}
            <Route
              path="/checkin/:conversationId"
              element={
                <Translatr apps={['web.common.main', 'web.pagenotfound.main', 'cds.common.main']}>
                  <Pages.CheckinMFE />
                </Translatr>
              }
            />
            {/** End: checkin routes */}

            {/** Start: admin homepage routes */}
            <Route
              path="/configure/homepage/:layoutId?"
              element={
                <AuthRoute path="/configure/homepage">
                  <Translatr apps={['web.common.main', 'web.landingpage.main', 'cds.common.main']}>
                    <AppPages.AdminHomePage />
                  </Translatr>
                </AuthRoute>
              }
            />
            {/** End: admin homepage routes */}

            {/** Start: library routes */}
            {isLowerEnvironment() && (
              <Route
                path="/library"
                element={
                  <Translatr apps={['web.common.main', 'cds.common.main']}>
                    <Pages.ComponentLibrary />
                  </Translatr>
                }
              />
            )}
            {/** End: library routes */}

            {/** Start: manager dashboard routes */}
            {showMDContentDrilldown && (
              <Route
                path="/manager-dashboard/learning/content"
                element={
                  <AuthRoute path="/manager-dashboard/learning/content">
                    <Translatr
                      apps={['web.manager-dashboard-v2.main', 'web.common.main', 'cds.common.main']}
                    >
                      <Pages.ContentDrilldownTable />
                    </Translatr>
                  </AuthRoute>
                }
              />
            )}
            {LD.isManagerDashboardEnabled() && (
              <Route
                path="/manager-dashboard/*?"
                element={
                  <AuthRoute path="/manager-dashboard">
                    {LD.isManagerDashboardV2Enabled() ? (
                      <Translatr
                        apps={[
                          'web.manager-dashboard-v2.main',
                          'web.common.main',
                          'cds.common.main'
                        ]}
                      >
                        {MD_BLOCKS_ENABLED ? (
                          <Pages.ManagerDashboardBlocks />
                        ) : (
                          <Pages.ManagerDashboardV2 />
                        )}
                      </Translatr>
                    ) : (
                      <Translatr
                        apps={['web.manager-dashboard.main', 'web.common.main', 'cds.common.main']}
                      >
                        <Pages.ManagerDashboard />
                      </Translatr>
                    )}
                  </AuthRoute>
                }
              />
            )}
            {/** End: manager dashboard routes */}

            {/** Start: Analytics routes */}
            {LD.performanceAnalytics() && (
              <Route
                path="/analytics"
                element={<Navigate to="/analytics/overview-performance" />}
              />
            )}
            {LD.performanceAnalytics() && (
              <Route
                path="/analytics/*"
                element={
                  <AuthRoute path="/analytics">
                    <Translatr
                      apps={['web.analytics-reports.main', 'web.common.main', 'cds.common.main']}
                    >
                      <Pages.AnalyticsReports />
                    </Translatr>
                  </AuthRoute>
                }
              />
            )}
            {/** End: Analytics routes */}

            {/** Start: settings routes */}
            <Route
              path="/settings/*"
              element={
                <AuthRoute path="/settings">
                  <AppPages.SettingsContainer>
                    <Routes>
                      <Route
                        index
                        element={
                          <Translatr
                            apps={[
                              'web.common.main',
                              'cds.common.main',
                              'web.account-details.main'
                            ]}
                          >
                            <AppPages.AccountDetailsContainer />
                          </Translatr>
                        }
                      />
                      <Route
                        path="details"
                        element={
                          <Translatr
                            apps={[
                              'web.common.main',
                              'cds.common.main',
                              'web.account-details.main'
                            ]}
                          >
                            <AppPages.AccountDetailsContainer />
                          </Translatr>
                        }
                      />
                      <Route
                        path="triggers"
                        key="/triggers"
                        element={
                          <Translatr
                            key={'/triggers'}
                            apps={['web.common.main', 'cds.common.main', 'web.notification.main']}
                          >
                            <AppPages.UserTriggersContainer />
                          </Translatr>
                        }
                      />
                      <Route
                        path="integrations"
                        element={
                          <Translatr apps={['web.common.main', 'cds.common.main']}>
                            <AppPages.IntegrationsContainer />
                          </Translatr>
                        }
                      />
                      <Route
                        path="other-details"
                        element={
                          <Translatr
                            key="/other-details"
                            apps={[
                              'web.common.main',
                              'cds.common.main',
                              'web.other-users-details.main'
                            ]}
                          >
                            <AppPages.OtherUsersDetails />
                          </Translatr>
                        }
                      />
                      <Route
                        path="/career-preferences"
                        element={
                          <AuthRoute path="/career-preferences">
                            <Translatr
                              apps={[
                                'web.common.main',
                                'cds.common.main',
                                'web.talentmarketplace.lov-labels-default'
                              ]}
                            >
                              <AppPages.CareerPreferencesContainer />
                            </Translatr>
                          </AuthRoute>
                        }
                      />
                    </Routes>
                  </AppPages.SettingsContainer>
                </AuthRoute>
              }
            ></Route>
            {/** End: settings routes */}

            {/** Start: Skill assessment routes */
            isSkillsAssessmentAvailable && (
              <>
                <Route
                  path="/skills-assessment"
                  element={
                    <Translatr
                      apps={[
                        'web.ocg.main',
                        'web.common.main',
                        'cds.common.main',
                        'web.skills-assessments-v2.main'
                      ]}
                    >
                      <AppPages.SkillsAssessmentWrapper />
                    </Translatr>
                  }
                />
                <Route
                  path="/skills-assessment/personal"
                  element={
                    <Translatr
                      apps={[
                        'web.ocg.main',
                        'web.common.main',
                        'cds.common.main',
                        'web.skills-assessments-v2.main'
                      ]}
                    >
                      <AppPages.OCGPersonal />
                    </Translatr>
                  }
                />
                <Route
                  path="/skills-assessment/peers"
                  element={
                    <Translatr
                      apps={[
                        'web.ocg.main',
                        'web.common.main',
                        'cds.common.main',
                        'web.skills-assessments-v2.main'
                      ]}
                    >
                      <AppPages.OCGPeers />
                    </Translatr>
                  }
                />
                {MD_TEAM_SKILLS_ASSESSMENT && (
                  <Route
                    path="/skills-assessment/manager-rating-review"
                    element={
                      <Translatr
                        apps={[
                          'web.ocg.main',
                          'web.common.main',
                          'cds.common.main',
                          'web.skills-assessments-v2.main'
                        ]}
                      >
                        <AppPages.ManagerRatingReview />
                      </Translatr>
                    }
                  />
                )}
                <Route
                  path="/skills-assessment/requests/received"
                  element={
                    <Translatr
                      apps={[
                        'web.skills-assessments-v2.main',
                        'web.common.main',
                        'cds.common.main'
                      ]}
                    >
                      <AppPages.SkillsAssessmentV2 ActivePage={'Requests'} isSentRequest={false} />
                    </Translatr>
                  }
                />
                <Route
                  path="/skills-assessment/requests/sent"
                  element={
                    <Translatr
                      apps={[
                        'web.skills-assessments-v2.main',
                        'web.common.main',
                        'cds.common.main'
                      ]}
                    >
                      <AppPages.SkillsAssessmentV2 ActivePage={'Requests'} isSentRequest={true} />
                    </Translatr>
                  }
                />
                <Route
                  path="/skills-assessment/ratings/received"
                  element={
                    <Translatr
                      apps={[
                        'web.skills-assessments-v2.main',
                        'web.common.main',
                        'cds.common.main'
                      ]}
                    >
                      <AppPages.SkillsAssessmentV2 ActivePage={'Ratings'} isSentRequest={false} />
                    </Translatr>
                  }
                />
                <Route
                  path="/skills-assessment/ratings/sent"
                  element={
                    <Translatr
                      apps={[
                        'web.skills-assessments-v2.main',
                        'web.common.main',
                        'cds.common.main'
                      ]}
                    >
                      <AppPages.SkillsAssessmentV2 ActivePage={'Ratings'} isSentRequest={true} />
                    </Translatr>
                  }
                />
              </>
            )
            /** End: Skill assessment routes */
            }

            {/** Start: Skills routes */}
            <Route
              path="/skills-graph"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.SkillsGraph />
                </Translatr>
              }
            />
            <Route
              path="/skills-directory"
              element={
                <AuthRoute path="/skills-directory">
                  <Translatr apps={['web.common.main', 'cds.common.main']}>
                    <AppPages.SkillsDirectory />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path="/update-skills"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.SkillUpdateContainer />
                </Translatr>
              }
            />
            {/** End: Skills routes */}

            {/** Start: Leaderboard routes */}
            {isLeaderboardConfigEnabled && (
              <Route
                path="/leaderboard"
                element={
                  <Translatr
                    apps={['web.common.main', 'cds.common.main', 'web.team.main', 'web.group.main']}
                  >
                    <AppPages.LeaderBoardContainerv2 />
                  </Translatr>
                }
              />
            )}
            {/** End: Leaderboard routes */}

            {/** Start: Register routes */}
            {fsPrimeConfig && (
              <Route
                path="/register"
                element={
                  <Translatr
                    key="/register"
                    apps={['web.fspregistration.main', 'web.common.main', 'cds.common.main']}
                  >
                    <AppPages.Registration />
                  </Translatr>
                }
              />
            )}
            {/** End: Register routes */}

            {/** Start: Logout routes */}
            <Route
              path="/log_out"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.logOutContainer />
                </Translatr>
              }
            />
            {/** End: Logout routes */}

            {/** Start: manage block users routes */}
            {LD.isBlockUser() && (
              <Route
                path="/manage-block-users"
                element={
                  <AuthRoute path="/manage-block-users">
                    <Translatr apps={['web.user.manage-block-user']}>
                      <Pages.ManageBlockedUsers />
                    </Translatr>
                  </AuthRoute>
                }
              />
            )}
            {/** End: manage block users routes */}

            {/** Start: Me routes */}

            {/** Added redirection rules from /me/project and /me/mentorhip to /career because /me/project and /me/mentorship has been moved to /career route */}
            <Route
              path={`/me/${OPPORTUNITY_TYPE.PROJECT}/*`}
              element={<RedirectPathToCareer opportunityType={OPPORTUNITY_TYPE.PROJECT} />}
            />

            <Route
              path={`/me/${OPPORTUNITY_TYPE.MENTORSHIP}/*`}
              element={<RedirectPathToCareer opportunityType={OPPORTUNITY_TYPE.MENTORSHIP} />}
            />

            <Route
              path={`/me/${OMP_URL_BY_TYPE[OPPORTUNITY_TYPE.JOB_VACANCY]}/*`}
              element={
                <RedirectPathToCareer
                  opportunityType={OMP_URL_BY_TYPE[OPPORTUNITY_TYPE.JOB_VACANCY]}
                />
              }
            />

            <Route
              path={`/me/${OMP_URL_BY_TYPE[OPPORTUNITY_TYPE.JOB_ROLE]}/*`}
              element={
                <RedirectPathToCareer
                  opportunityType={OMP_URL_BY_TYPE[OPPORTUNITY_TYPE.JOB_ROLE]}
                />
              }
            />

            <Route
              path="/me/skill-coins"
              element={
                <AuthRoute path="/me">
                  <Translatr
                    apps={[
                      'web.myprofile.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.talentmarketplace.main'
                    ]}
                  >
                    <Pages.SkillCoinsPage />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path="/me/*"
              element={
                <AuthRoute path="/me">
                  <Translatr
                    apps={[
                      'web.myprofile.main',
                      'web.languages.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default'
                    ]}
                  >
                    <Pages.ProfileContainer>
                      {(() => {
                        // prevent warning about needing key for route here
                        let i = 0;
                        return (
                          <Routes>
                            <Route
                              key={`r${i++}`}
                              index
                              element={
                                <Translatr
                                  apps={[
                                    'web.myprofile.main',
                                    'web.common.main',
                                    'cds.common.main',
                                    'web.talentmarketplace.main',
                                    'web.talentmarketplace.lov-labels-default'
                                  ]}
                                >
                                  <Pages.ProfilePage />
                                </Translatr>
                              }
                            />
                            {newProfileEnabled && (
                              <>
                                <Route
                                  key={`r${i++}`}
                                  path="overview"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.content.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/experiences"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="experiences" />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/certifications"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="certifications" />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/badges"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="badges" />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/groups"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="groups" />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/channels"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="channels" />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/activity"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="activity" />
                                    </Translatr>
                                  }
                                />
                                <Route
                                  key={`r${i++}`}
                                  path="overview/content"
                                  element={
                                    <Translatr
                                      apps={[
                                        'web.myprofile.main',
                                        'web.content.main',
                                        'web.common.main',
                                        'cds.common.main'
                                      ]}
                                    >
                                      <Pages.OverviewContainer page="content" />
                                    </Translatr>
                                  }
                                />
                              </>
                            )}
                            ,
                            {newProfileEnabled && (
                              <Route
                                key={`r${i++}`}
                                path="skills"
                                element={
                                  <Translatr
                                    apps={[
                                      'web.myprofile.main',
                                      'web.common.main',
                                      'cds.common.main'
                                    ]}
                                  >
                                    <Pages.SkillsContainer />
                                  </Translatr>
                                }
                              />
                            )}
                            {!newProfileEnabled && (
                              <Route
                                key={`r${i++}`}
                                path="activity"
                                element={
                                  <Translatr
                                    apps={[
                                      'web.myprofile.main',
                                      'web.common.main',
                                      'cds.common.main',
                                      'web.talentmarketplace.main'
                                    ]}
                                  >
                                    <Pages.AllActivity />
                                  </Translatr>
                                }
                              />
                            )}
                            <Route
                              key={`r${i++}`}
                              path="in-progress"
                              element={
                                <Translatr
                                  apps={[
                                    'web.myprofile.main',
                                    'web.common.main',
                                    'cds.common.main',
                                    'web.talentmarketplace.main'
                                  ]}
                                >
                                  <Pages.DashboardInProgressViewAll />
                                </Translatr>
                              }
                            />
                            {!newProfileEnabled && (
                              <Route
                                key={`r${i++}`}
                                path="skills-passport"
                                element={
                                  <Translatr
                                    key="/me/skills-passport"
                                    apps={[
                                      'web.skillspassport.main',
                                      'web.myprofile.main',
                                      'web.common.main',
                                      'cds.common.main',
                                      'cds.inputs.main'
                                    ]}
                                  >
                                    <Pages.SkillsPassportPage />
                                  </Translatr>
                                }
                              />
                            )}
                            {!newContentEnabled && (
                              <Route
                                key={`r${i++}`}
                                path="content/:filter?"
                                element={
                                  <Translatr
                                    apps={[
                                      'web.mycontent.main',
                                      'web.common.main',
                                      'cds.common.main'
                                    ]}
                                  >
                                    <Pages.MyContentPage />
                                  </Translatr>
                                }
                              />
                            )}
                            <Route
                              key={`r${i++}`}
                              path="learning/:filter?"
                              element={
                                <Translatr
                                  apps={[
                                    'web.mylearningplan.main',
                                    'web.mycontent.main',
                                    'web.common.main',
                                    'cds.common.main'
                                  ]}
                                >
                                  <Pages.LearningPlanPage />
                                </Translatr>
                              }
                            />
                            {LD.isSubscriptionFeatureEnabled() && (
                              <Route
                                key={`r${i++}`}
                                path="my-subscription"
                                element={
                                  <Translatr
                                    apps={[
                                      'web.subscriptionpage.main',
                                      'web.common.main',
                                      'cds.common.main'
                                    ]}
                                  >
                                    <Pages.SubscriptionPage />
                                  </Translatr>
                                }
                              />
                            )}
                            {fsPrimeConfig && (
                              <Route
                                key={`r${i++}`}
                                path="ledger"
                                element={
                                  <Translatr
                                    apps={[
                                      'web.ledgerv2.main',
                                      'web.common.main',
                                      'cds.common.main'
                                    ]}
                                  >
                                    <Pages.LedgerContainer />
                                  </Translatr>
                                }
                              />
                            )}
                          </Routes>
                        );
                      })()}
                    </Pages.ProfileContainer>
                  </Translatr>
                </AuthRoute>
              }
            />
            {/** End: Me routes */}

            {/** Start: subscription plans routes */}
            {LD.isSubscriptionFeatureEnabled() && (
              <Route
                path="/subscription-plans/:subscriptionSku?"
                element={
                  <Translatr
                    apps={['web.subscriptionpage.main', 'web.common.main', 'cds.common.main']}
                  >
                    <Pages.SubscriptionPlans />
                  </Translatr>
                }
              />
            )}
            {/** End: subscription plans routes */}

            {/** Start: Org groups routes */}
            <Route
              path="/org-groups/*"
              element={
                <Routes>
                  <Route
                    index
                    element={
                      <Translatr
                        apps={[
                          'web.group.main',
                          'web.common.main',
                          'cds.common.main',
                          'web.multilang.multilang-modal'
                        ]}
                      >
                        <Pages.AllGroupStandAlonePage />
                      </Translatr>
                    }
                  />
                  <Route
                    path="create"
                    element={
                      <Translatr
                        apps={[
                          'web.group.main',
                          'web.common.main',
                          'cds.common.main',
                          'web.multilang.multilang-modal'
                        ]}
                      >
                        <AppPages.CreateGroupContainer />
                      </Translatr>
                    }
                  />
                  <Route
                    path="*"
                    element={
                      <Translatr
                        apps={[
                          'web.group.main',
                          'web.common.main',
                          'cds.common.main',
                          'web.multilang.multilang-modal'
                        ]}
                      >
                        <Pages.AllGroupStandAlonePage />
                      </Translatr>
                    }
                  />
                </Routes>
              }
            />
            {/** End: Org groups routes */}

            {/** Start: Team routes */}
            <Route
              path="/teams"
              element={
                <Translatr apps={['web.analytics-reports.main']}>
                  <Navigate to="/org-groups" />
                </Translatr>
              }
              replace
            />
            <Route
              path="/teams/:slug/edit/*?"
              element={
                <Translatr
                  apps={['web.team.main', 'web.common.main', 'cds.common.main', 'web.group.main']}
                >
                  <Pages.CreateUpdateGroupContainer />
                </Translatr>
              }
            />
            <Route
              path="/teams/:slug/manage-post"
              element={
                <Translatr
                  apps={['web.team.main', 'web.common.main', 'cds.common.main', 'web.group.main']}
                >
                  <Pages.GroupManagePosts />
                </Translatr>
              }
            />
            {LD.teamAnalyticsEnabled() && (
              <Route
                path="/teams/:slug/analytics"
                element={
                  <Translatr
                    apps={[
                      'web.team.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.group.main',
                      'web.analytics-reports.main'
                    ]}
                  >
                    <AppPages.TeamAnalyticsBase />
                  </Translatr>
                }
              />
            )}
            {LD.teamAnalyticsEnabled() && (
              <Route
                path="/teams/:slug/analytics/assignment"
                element={
                  <Translatr
                    apps={[
                      'web.team.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.group.main',
                      'web.analytics-reports.main'
                    ]}
                  >
                    <AppPages.TeamAssignmentAnalyticsContainer />
                  </Translatr>
                }
              />
            )}
            {LD.teamAnalyticsEnabled() && (
              <Route
                path="/teams/:slug/analytics/insights"
                element={
                  <Translatr
                    apps={[
                      'web.team.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.group.main',
                      'web.analytics-reports.main'
                    ]}
                  >
                    <AppPages.TeamInsightAnalyticsContainer />
                  </Translatr>
                }
              />
            )}
            <Route
              path="/teams/:slug/:tab?/:subTab?"
              element={
                <Translatr
                  apps={[
                    'web.team.main',
                    'web.common.main',
                    'cds.common.main',
                    'web.group.main',
                    'web.analytics-reports.main'
                  ]}
                >
                  <AppPages.GroupPageContainerv2 />
                </Translatr>
              }
            />
            <Route path="/teams/*?" element={<EmptyContainer />} />
            <Route
              path="/team/*?"
              element={
                <Translatr
                  apps={[
                    'web.team.main',
                    'web.common.main',
                    'cds.common.main',
                    'web.group.main',
                    'web.people-search.main'
                  ]}
                >
                  <Pages.SearchPeopleV2 />
                </Translatr>
              }
            />
            {/** End: Team routes */}

            {/** Start: discover routes */}
            <Route
              path="/discover"
              element={
                <Translatr apps={['web.discover.main', 'web.common.main', 'cds.common.main']}>
                  <Pages.Discover />
                </Translatr>
              }
            />
            <Route
              path="/discover/:provider"
              element={
                <Translatr apps={['web.discover.main', 'web.common.main', 'cds.common.main']}>
                  <Pages.FeaturedProviderPage />
                </Translatr>
              }
            />
            {/** End: discover routes */}

            {/** Start: Feed routes */}
            {LD.homepageContainer() === 'final' &&
              [
                '/feed?/team-learning',
                '/feed?/my-assignments',
                '/feed?/featured',
                '/feed?/todays-learning-search',
                '/feed?/new-todays-learning-search'
              ].map(path => (
                <Route
                  key={path}
                  path={path}
                  element={
                    <Translatr
                      apps={[
                        'web.common.main',
                        'cds.common.main',
                        'web.pagenotfound.main',
                        'cds.header.main',
                        'web.home.main'
                      ]}
                    >
                      <Pages.Home />
                    </Translatr>
                  }
                />
              ))}
            <Route
              path="/feed/*?"
              element={
                <Translatr
                  apps={[
                    'web.home.main',
                    'web.talentmarketplace.main',
                    'web.common.main',
                    'cds.common.main'
                  ]}
                >
                  <Pages.HomeWrapper />
                </Translatr>
              }
            />
            {/** End: Feed routes */}

            {/* Start: Routes to handle tab route on homepage */}
            {LD.homepageContainer() === 'simple' && (
              <Route path="my-assignments" element={<Navigate to="/me/learning" replace />} />
            )}
            {/* End: Routes to handle tab route on homepage */}

            {/** Start: Notifications routes */}
            <Route
              path="/notifications"
              element={
                <Translatr apps={['web.notification.main']}>
                  <Pages.NotificationsContainer />
                </Translatr>
              }
            />
            {/** End: Notifications routes */}

            {/** Start: Channel routes */}
            <Route
              path="/channel/:slug"
              element={
                <Translatr
                  apps={[
                    'web.channel.main',
                    'web.common.main',
                    'cds.common.main',
                    'web.subscriptionpage.main',
                    'web.search.main'
                  ]}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/edit"
              key="/channel/:slug/edit"
              element={
                <Translatr
                  key="/channel/:slug/edit"
                  apps={['web.channel.main', 'web.common.main', 'cds.common.main']}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/edit/tuning"
              key="/channel/:slug/edit"
              element={
                <Translatr
                  key="/channel/:slug/edit"
                  apps={['web.channel.main', 'web.common.main', 'cds.common.main']}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/edit/widgets"
              key="/channel/:slug/edit"
              element={
                <Translatr
                  key="/channel/:slug/edit"
                  apps={['web.channel.main', 'web.common.main', 'cds.common.main']}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/edit/content-layout"
              element={
                <Translatr
                  key="/channel/:slug/edit"
                  apps={['web.channel.main', 'web.common.main', 'cds.common.main']}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/curate"
              element={
                <Translatr apps={['web.channel.main', 'web.common.main', 'cds.common.main']}>
                  <AppPages.CurateChannelCards />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/:tabName"
              element={
                <Translatr
                  apps={[
                    'web.channel.main',
                    'web.common.main',
                    'cds.common.main',
                    'web.subscriptionpage.main',
                    'web.search.main'
                  ]}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channel/:slug/:tabName/:subSectionId"
              element={
                <Translatr
                  apps={[
                    'web.channel.main',
                    'web.common.main',
                    'cds.common.main',
                    'web.subscriptionpage.main',
                    'web.search.main'
                  ]}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channels/new"
              element={
                <Translatr
                  key="/channels/new"
                  apps={[
                    'web.channel.main',
                    'web.common.main',
                    'cds.common.main',
                    'web.multilang.multilang-modal'
                  ]}
                >
                  <Pages.ChannelStandalone />
                </Translatr>
              }
            />
            <Route
              path="/channels/:filter/:id?"
              element={
                <Translatr
                  apps={['web.channel.main', 'web.common.main', 'cds.common.main']}
                  key="/channels/all"
                >
                  <AppPages.MyChannelsContainerv2 />
                </Translatr>
              }
            />
            {/** End: Channel routes */}

            {/** Start: smart search routes */}
            <Route
              path="/smartsearch/*?"
              element={
                <AuthRoute path="/smartsearch">
                  <Translatr
                    apps={[
                      'web.search.main',
                      'web.common.main',
                      'cds.common.main',
                      'web.people-search.main',
                      'web.projects.main',
                      'web.talentmarketplace.main'
                    ]}
                  >
                    <Pages.Search />
                  </Translatr>
                </AuthRoute>
              }
            />
            {/** End: smart search routes */}

            {/** Start: Pathways routes */}
            {['/pathways/new', '/pathways/:slug/edit/:tab'].map(path => (
              <Route
                key={path}
                path={path}
                element={
                  <Translatr
                    key={path}
                    apps={[
                      'web.multilang.multilang-modal',
                      'web.pathwayandjourney.main',
                      'web.common.main',
                      'cds.common.main',
                      'cds.inputs.gen-ai-assisted-description'
                    ]}
                  >
                    <Pages.PathwayBaseLayout />
                  </Translatr>
                }
              />
            ))}
            <Route
              path="/pathways/:slug/cards/:cardId"
              element={
                <Translatr
                  apps={['web.pathwayandjourney.main', 'web.common.main', 'cds.common.main']}
                >
                  <AppPages.PathwayConsumptionContainer />
                </Translatr>
              }
            />
            <Route
              path="/pathways/:slug/:mode?"
              element={
                <Translatr
                  apps={['web.pathwayandjourney.main', 'web.common.main', 'cds.common.main']}
                >
                  <AppPages.PathwayStandaloneWrapper />
                </Translatr>
              }
            />
            {/** End: Pathways routes */}

            {/** Start: Journey routes */}
            {['/journeys/new', '/journeys/:slug/edit/:tab'].map(path => (
              <Route
                key={path}
                path={path}
                element={
                  <Translatr
                    key={path}
                    apps={[
                      'web.multilang.multilang-modal',
                      'web.pathwayandjourney.main',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.JourneyBaseLayout />
                  </Translatr>
                }
              />
            ))}
            <Route
              path="/journey/:slug/cards/:cardId"
              element={
                <Translatr
                  apps={['web.pathwayandjourney.main', 'web.common.main', 'cds.common.main']}
                >
                  <AppPages.JourneyConsumptionContainer />
                </Translatr>
              }
            />
            <Route
              path="/journey/:slug/:mode?"
              element={
                <Translatr
                  apps={['web.pathwayandjourney.main', 'web.common.main', 'cds.common.main']}
                >
                  <AppPages.JourneyStandaloneWrapper />
                </Translatr>
              }
            />
            {/** End: Journey routes */}

            {/** Start: Insight routes */}
            <Route
              path="/insights/:id"
              element={
                <Translatr
                  apps={['web.common.main', 'cds.common.main', 'web.subscriptionpage.main']}
                >
                  <AppPages.InsightContainer />
                </Translatr>
              }
            />
            <Route
              path="/insights/:id/vilt/registeredusers"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.ViewCardDetailsPage />
                </Translatr>
              }
            />
            <Route
              path="/insights/:id/project/viewSubmissions"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.ViewCardDetailsPage />
                </Translatr>
              }
            />
            <Route
              path="/insights/:id/vilt/view"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.InsightContainer />
                </Translatr>
              }
            />
            <Route
              path="/mobile-insights/:id"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <AppPages.ScormContainer />
                </Translatr>
              }
            />
            <Route
              path="/curriculum-player/:loId"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <Pages.CurriculumPlayerMFE />
                </Translatr>
              }
            />

            <Route
              path="/one-player/:loId"
              element={
                <Translatr apps={['web.common.main', 'cds.common.main']}>
                  <Pages.OneplayerMFE />
                </Translatr>
              }
            />
            {/** End: Insight routes */}
            {newMyLearningEnabled && (
              <Route
                path="/learning/:tab?"
                element={
                  <Translatr apps={['web.common.main', 'cds.common.main']}>
                    <Pages.MyLearningV2 />
                  </Translatr>
                }
              />
            )}
            {/** Start: career routes */}
            <Route
              path={`/career/${OPPORTUNITY_TYPE.PROJECT}/new`}
              element={
                <AuthRoute path={`/career/${OPPORTUNITY_TYPE.PROJECT}/new`}>
                  <Translatr
                    apps={[
                      'web.projects.main',
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.CreateOrDuplicateProjectPage />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path={`/career/detail/${OPPORTUNITY_TYPE.PROJECT}/:slug`}
              element={
                shouldShowTMProject() ? (
                  <Translatr
                    apps={[
                      'web.projects.main',
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.ViewProjectPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.PROJECT}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path={`/career/detail/${OPPORTUNITY_TYPE.PROJECT}/:slug/edit`}
              element={
                <AuthRoute path={`/career/detail/${OPPORTUNITY_TYPE.PROJECT}/:slug/edit`}>
                  <Translatr
                    apps={[
                      'web.projects.main',
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.EditProjectPage />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path={`/career/detail/${OPPORTUNITY_TYPE.PROJECT}/:slug/manage`}
              element={
                <AuthRoute path={`/career/detail/${OPPORTUNITY_TYPE.PROJECT}/:slug/manage`}>
                  <Translatr
                    apps={[
                      'web.projects.main',
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.ManageProjectPage />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path={`/career/detail/${OPPORTUNITY_TYPE.MENTORSHIP}/:slug`}
              element={
                <AuthRoute path={`/career/detail/${OPPORTUNITY_TYPE.MENTORSHIP}/:slug`}>
                  {shouldShowTMMentorship() ? (
                    <Translatr
                      apps={[
                        'web.common.main',
                        'web.talentmarketplace.main',
                        'web.talentmarketplace.lov-labels-default',
                        'cds.common.main'
                      ]}
                    >
                      <Pages.MentorshipProfilePage />
                    </Translatr>
                  ) : (
                    <Pages.RestrictedAccesssOMPPage
                      opportunityType={OPPORTUNITY_TYPE.MENTORSHIP}
                      style={{ marginTop: 110 }}
                    />
                  )}
                </AuthRoute>
              }
            />
            <Route
              path={`/career/detail/${JOB_TYPE.VACANCY}/:slug/skills`}
              element={
                shouldShowTMJobVacancy() ? (
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMPJobVacancySkillsPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.JOB_VACANCY}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path={`/career/detail/${JOB_TYPE.ROLE}/:slug/skills`}
              element={
                shouldShowTMJobRole() ? (
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMPJobVacancySkillsPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.JOB_ROLE}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path={`/career/detail/${JOB_TYPE.VACANCY}/:slug/edit`}
              element={
                shouldAllowJobEdit() ? (
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMPJobVacancyEditPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.JOB_VACANCY}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path={`/career/detail/${JOB_TYPE.VACANCY}/:slug`}
              element={
                shouldShowTMJobVacancy() ? (
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMPJobVacancyDetailPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.JOB_VACANCY}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path={`/career/detail/${JOB_TYPE.ROLE}/:slug/:baseRoleId?`}
              element={
                shouldShowTMJobRole() ? (
                  <Translatr
                    key={`/career/detail/${JOB_TYPE.ROLE}`}
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.career-path',
                      'web.talentmarketplace.lov-labels-default',
                      'web.talentmarketplace.development-plan',
                      'web.recommendations.transision-recommendations',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMPJobVacancyDetailPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.JOB_ROLE}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path="/career/detail/*"
              element={
                <AuthRoute path="/career/detail/*">
                  <Translatr apps={['web.pagenotfound.main', 'web.common.main', 'cds.common.main']}>
                    <Pages.PageNotFound />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path="/career/plan/:roleId/:planId?"
              element={
                shouldShowDevelopmentPlan() ? (
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.talentmarketplace.development-plan',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMDevelopmentPlanPage />
                  </Translatr>
                ) : (
                  <Pages.RestrictedAccesssOMPPage
                    opportunityType={OPPORTUNITY_TYPE.JOB_ROLE}
                    style={{ marginTop: 110 }}
                  />
                )
              }
            />
            <Route
              path="/career/:tab?/*"
              element={
                <AuthRoute path="/career/*">
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'web.common.main',
                      'cds.common.main'
                    ]}
                  >
                    <Pages.TMLandingPageV2 />
                  </Translatr>
                </AuthRoute>
              }
            />
            {/** End: career routes */}

            {newContentEnabled && (
              <Route
                path="/content/:filter?"
                element={
                  <Translatr
                    key="/content"
                    apps={['web.pagenotfound.main', 'web.common.main', 'web.content.main']}
                  >
                    <Pages.MyContentPageV2 />
                  </Translatr>
                }
              />
            )}

            <Route
              path="/topic"
              element={
                <Translatr
                  key="/content"
                  apps={['web.pagenotfound.main', 'web.common.main', 'web.topics.main']}
                >
                  <Pages.ExploreByTopics />
                </Translatr>
              }
            />

            {/**
             * Start: ruby routes
             * Path blow this will be masked by rubyHandledPath
             */}
            {rubyHandledPath.map((path, index) => (
              <React.Fragment key={index}>
                <Route path={path} element={<EmptyContainer />} />
                <Route path={`${path}/*`} element={<EmptyContainer />} />
              </React.Fragment>
            ))}
            {/** End: ruby routes */}

            {/* Start: my content routes */}
            <Route path="/@my_content" element={<Navigate to="" />} />
            {/* End: my content routes */}

            {/** Start: sourcing routes */}
            <Route
              path={`/sourcing/manage/${JOB_TYPE.VACANCY}/:slug/:section/edit`}
              element={
                <AuthRoute path={`/sourcing/manage/${JOB_TYPE.VACANCY}/:slug/:section/edit`}>
                  <Translatr
                    apps={[
                      'web.talentmarketplace.main',
                      'web.talentmarketplace.lov-labels-default',
                      'cds.common.main',
                      'web.common.main',
                      'web.sourcing.main',
                      'web.sourcing.candidate-profile'
                    ]}
                  >
                    <Pages.TMPJobVacancyEditPage />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path={`/sourcing/manage/${JOB_TYPE.VACANCY}/:slug`}
              element={
                <AuthRoute path={`/sourcing/manage/${JOB_TYPE.VACANCY}/:slug`}>
                  <Translatr
                    apps={[
                      'web.common.main',
                      'web.sourcing.main',
                      'web.sourcing.candidate-profile'
                    ]}
                  >
                    <Pages.ManageJobVacancySourcing />
                  </Translatr>
                </AuthRoute>
              }
            />
            <Route
              path="/sourcing"
              element={
                <AuthRoute path="/sourcing">
                  <Translatr apps={['web.sourcing.main', 'web.sourcing.candidate-profile']}>
                    <Pages.Sourcing />
                  </Translatr>
                </AuthRoute>
              }
            />
            {/** End: sourcing routes */}

            {/* Start: Route to handle signout action */}
            <Route path="/login/signout" element={<></>} />
            {/* End: Route to handle signout action */}
            {/* Start: Route to handle saml auth action */}
            <Route
              path="/saml/auth/:uuid"
              key="/saml/auth/:uuid"
              element={
                <Translatr apps={['web.common.main']}>
                  <AppPages.SamlAuthHandlerContainer />
                </Translatr>
              }
            />
            {/* End: Route to handle saml auth action */}
            {/* Start: onboarding routes */}
            <Route
              path="/onboarding"
              element={
                <Translatr
                  key="/onboarding"
                  apps={[
                    'onboarding.common.common',
                    'onboarding.learning-goal.step-two',
                    'onboarding.sorted-tags.sorted-tags',
                    'cds.common.main'
                  ]}
                >
                  <Pages.Onboarding />
                </Translatr>
              }
            />
            {/* End: onboarding routes */}

            {/* Start: Mfe testing route */}
            <Route
              path="/mfe-loader-testing/*?"
              element={
                <AuthRoute path="/mfe-loader-testing/*?">
                  <MfeTest />
                </AuthRoute>
              }
            />
            {/* End: Mfe testing route */}

            <Route
              path="/okta-verify"
              element={
                <Translatr apps={['web.login.main', 'web.common.main', 'cds.common.main']}>
                  <Pages.OktaVerify />
                </Translatr>
              }
            />

            <Route path="/mkp-redirect" element={<Pages.MkpRedirect />} />

            {/**
             * Start: Profile routes
             * This should be at the end because it will match /{wild}/{wild}
             */}
            <Route
              path="/:handle/*"
              element={
                <Translatr
                  apps={[
                    'web.myprofile.main',
                    'web.mycontent.main',
                    'web.common.main',
                    'cds.common.main'
                  ]}
                >
                  <Pages.ProfileContainer>
                    <Routes>
                      {newProfileEnabled && (
                        <>
                          <Route
                            path="overview"
                            element={
                              <Translatr
                                apps={[
                                  'web.myprofile.main',
                                  'web.content.main',
                                  'web.common.main',
                                  'cds.common.main'
                                ]}
                              >
                                <Pages.OverviewContainer />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/experiences"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.OverviewContainer page="experiences" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/certifications"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.OverviewContainer page="certifications" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/badges"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.OverviewContainer page="badges" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/groups"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.OverviewContainer page="groups" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/channels"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.OverviewContainer page="channels" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/activity"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.OverviewContainer page="activity" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="overview/content"
                            element={
                              <Translatr
                                apps={[
                                  'web.myprofile.main',
                                  'web.content.main',
                                  'web.common.main',
                                  'cds.common.main'
                                ]}
                              >
                                <Pages.OverviewContainer page="content" />
                              </Translatr>
                            }
                          />
                          <Route
                            path="skills"
                            element={
                              <Translatr
                                apps={['web.myprofile.main', 'web.common.main', 'cds.common.main']}
                              >
                                <Pages.SkillsContainer />
                              </Translatr>
                            }
                          />
                        </>
                      )}
                      {!newProfileEnabled && (
                        <Route
                          index
                          element={
                            <Translatr apps={['web.common.main', 'cds.common.main']}>
                              <Pages.ProfilePage />
                            </Translatr>
                          }
                        />
                      )}
                      {!newProfileEnabled && (
                        <Route
                          path="skills-passport"
                          element={
                            <Translatr
                              key="/:handle/skills-passport"
                              apps={[
                                'web.skillspassport.main',
                                'web.common.main',
                                'cds.common.main'
                              ]}
                            >
                              <Pages.SkillsPassportPage />
                            </Translatr>
                          }
                        />
                      )}
                      <Route
                        path="my-subscription"
                        element={
                          <Translatr
                            apps={[
                              'web.common.main',
                              'cds.common.main',
                              'web.subscriptionpage.main'
                            ]}
                          >
                            <Pages.SubscriptionPage />
                          </Translatr>
                        }
                      />
                      {!newContentEnabled && (
                        <Route
                          path="content/:filter?"
                          element={
                            <Translatr
                              apps={['web.mycontent.main', 'web.common.main', 'cds.common.main']}
                            >
                              <Pages.MyContentPage />
                            </Translatr>
                          }
                        />
                      )}
                    </Routes>
                  </Pages.ProfileContainer>
                </Translatr>
              }
            />
            {/** End: Profile routes */}

            {/* Page not found route */}
            <Route
              path="*"
              element={
                <Translatr apps={['web.pagenotfound.main', 'web.common.main', 'cds.common.main']}>
                  <Pages.PageNotFound />
                </Translatr>
              }
            />
          </Routes>
        </LoggedInAppBodyController>
      )}
    </AuthorizedContainer>
  );
}

AuthRoute.propTypes = {
  children: node,
  isSecondaryManager: bool
};

ProtectedRoutes.propTypes = {
  fsPrimeConfig: bool,
  isLeaderboardConfigEnabled: bool,
  isSkillsAssessmentAvailable: bool,
  permissionsLoaded: bool,
  impersonatee: string,
  impersonator: string
};

export default connect(state => {
  const team = state.team;
  const currentUser = state.currentUser;
  const { fs_prime } = team.get('config');
  const isLeaderboardConfigEnabled = team.get('config') && team.get('config').leaderboard;
  const isSkillsAssessmentAvailable = isSkillsAssessmentEnabled(currentUser, team);
  const impersonatee = currentUser.get('impersonatee') || '';
  const impersonator = currentUser.get('impersonator') || '';

  return {
    fsPrimeConfig: fs_prime,
    isLeaderboardConfigEnabled,
    isSkillsAssessmentAvailable,
    // trigger rerender of routes when permissions are ready
    permissionsLoaded: currentUser.get('isLoaded'),
    impersonatee,
    impersonator
  };
})(ProtectedRoutes);
