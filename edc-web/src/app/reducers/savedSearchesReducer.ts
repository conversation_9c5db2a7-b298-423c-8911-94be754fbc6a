import * as actionTypes from '../constants/actionTypes';
import Immutable from 'immutable';
import { OpportunitySavedSearch } from '@pages/TalentMarketplace/shared/OpportunitiesSavedFilters/types';
import { FilterBucketName, SavedSearchesActions } from '@actions/savedSearchesActions';
import { TM_VACANCY_FILTER_BUCKET_NAME } from '@pages/TalentMarketplace/shared/filters/Filters.constants';

type State = Immutable.Map<FilterBucketName, OpportunitySavedSearch>;

const INITIAL_STATE = Immutable.Map({
  [TM_VACANCY_FILTER_BUCKET_NAME]: null
});

export default function savedSearchesReducer(
  state: State = Immutable.Map<FilterBucketName, OpportunitySavedSearch>(INITIAL_STATE),
  action: SavedSearchesActions
): State {
  switch (action.type) {
    case actionTypes.SAVE_SAVED_SEARCHES: {
      const { bucketType, data } = action;
      const currentData = state.get(bucketType) || {};
      return state.set(bucketType, {
        ...currentData,
        ...data
      });
    }

    default:
      return state;
  }
}
