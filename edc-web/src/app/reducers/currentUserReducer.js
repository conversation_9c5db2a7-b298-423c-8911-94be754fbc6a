/**
 * Created by ypling on 7/5/16.
 */

import * as actionTypes from '../constants/actionTypes';
import Immutable from 'immutable';
import { setLocales } from '../utils/locale';
import { IS_LOGGED_IN, SELECTED_LANGUAGE } from '../constants/localStorageConstants';
import { unescape, omit } from 'lodash';

export default function currentUserReducer(
  state = Immutable.fromJS({
    teams: [],
    invalidTeamName: false,
    walletTransactions: [],
    followingUsers: [],
    isLoadingPublicProfile: true,
    userPassport: {},
    publicUserPassport: {},
    samlRequestPayload: {},
    userInfoUpdated: false
  }),
  action
) {
  let newState;
  // eslint-disable-next-line sonarjs/max-switch-cases
  switch (action.type) {
    // case actionTypes.RECEIVE_USER_AUTHENTICATED:
    case actionTypes.RECEIVE_INIT_USER_INFO:
      localStorage.setItem(IS_LOGGED_IN, true);
      if (
        window.__GATrackingOrganizationObject__ &&
        action.user &&
        action.user.organization &&
        !action.user.organization.configs
      ) {
        action.user.organization = window.__GATrackingOrganizationObject__;
      }
      window.__ED__ = { ...(window.__ED__ || {}), ...action.user };

      let selectedLanguage = window.localStorage.getItem(SELECTED_LANGUAGE);
      const updateUserLang =
        selectedLanguage !== window.__ED__.profile.language && selectedLanguage !== 'id-key';

      if (!selectedLanguage || updateUserLang) {
        try {
          selectedLanguage = window.__ED__.profile.language;
          window.localStorage.setItem(SELECTED_LANGUAGE, selectedLanguage);
          document.getElementsByTagName('html')[0].setAttribute('lang', selectedLanguage);
          setLocales();
        } catch (e) {}
      }
      return state.merge({
        isLoggedIn: true,
        invalidLogin: false,
        isOcgEnabled: action.user.isOcgEnabled,
        isAdmin: action.user.isAdmin === true,
        isManager: action.user.isManager === true,
        isSecondaryManager: action.user.isSecondaryManager === true,
        hasReporters: action.user.hasReporters === true,
        id: action.user.id + '',
        picture: action.user.picture,
        avatar: action.user.picture,
        name: action.user.fullName,
        first_name: action.user.firstName,
        last_name: action.user.lastName,
        showGtcConfirmationModal: action.user.showGtcConfirmationModal,
        handle: action.user.handle && action.user.handle.substr(1),
        email: action.user.email,
        isGroupLeader: action.user.isGroupLeader,
        profile: omit(
          {
            ...action.user.profile,
            additionalLanguages:
              action.user.profile.additional_languages ||
              action.user.profile.additionalLanguages ||
              []
          },
          ['additional_languages']
        ),
        orgAnnualSubscriptionPaid: action.user.orgAnnualSubscriptionPaid,
        jwtToken: action.user.jwtToken,
        isSuperAdmin: action.user.isSuperAdmin,
        isLoaded: true,
        teams: action.user.activeOrgs,
        countryCode: action.user.countryCode,
        isOrgAdmin: action.user.isOrgAdmin,
        onboardingCompleted: action.user.onboardingCompleted,
        orgName: action.user.organization.name,
        filestackApiKey: action.user.filestackApiKey,
        impersonatee: action.user.impersonatee,
        impersonator: action.user.impersonator,
        isMkpAdmin: action.user.isMkpAdmin,
        relatedOrgs: action.user.relatedOrgs,
        isFactorEnrolled: action.user.isFactorEnrolled,
        passwordChangeable: action.user.passwordChangeable,
        skillAssessmentAccessible: action.user.skillAssessmentAccessible,
        isUserLoginViaMicrosite: action.user.isUserLoginViaMicrosite,
        permissions: action.user.permissions,
        isGlobalSearchEnabled: action.user.isGlobalSearchEnabled,
        ...(action.user.isGlobalSearchEnabled && {
          globalSearchSettings: action.user.globalSearchSettings
        }),
        isSearchSuggestionEnabled: action.user.isSearchSuggestionEnabled,
        ...(action.user.isSearchSuggestionEnabled && {
          searchSuggestionSettings: action.user.searchSuggestionSettings
        })
      });
    case actionTypes.USER_INFO_UPDATED:
      return state.set('userInfoUpdated', !state.get('userInfoUpdated'));
    case actionTypes.UPDATED_USER_DETAILS_FOR_SETTINGS_PAGE:
      const userDetails = action.onboardingState.user;
      return state.merge({
        picture: userDetails.picture,
        avatar: userDetails.avatar || userDetails.picture,
        bio: unescape(userDetails.bio),
        handle: userDetails.handle?.substr(1)
      });
    case actionTypes.RECEIVE_GROUPLEADER_USER_INFO:
      return state.set('isGroupLeader', action.state);
    case actionTypes.SET_CURRENT_USER_INFO:
      window.__ED__ = { ...(window.__ED__ || {}), ...action.userData };
      return state.merge(action.userData);
    case actionTypes.ERROR_INIT_USER:
      localStorage.setItem(IS_LOGGED_IN, false);
      return state.set('isLoggedIn', false);
    case actionTypes.ERROR_USER_LOGIN:
      const mergeObj = {
        invalidEmailInput: true,
        invalidPasswordInput: true,
        locked: false,
        isLoggedIn: false
      };
      if (action.errorMsg == 'Your account is locked.') {
        mergeObj.locked = true;
      }
      return state.merge(mergeObj);
    case actionTypes.ERROR_UPDATED_USER_INFO:
      return state.set('updateError', true);
    case actionTypes.UPDATE_USER_DETAILS:
      return state.merge({
        picture: action.user.picture,
        avatar: action.user.picture,
        first_name: action.user.firstName,
        last_name: action.user.lastName,
        name: action.user.fullName,
        email: action.user.email,
        bio: unescape(action.user.bio),
        coverImage: action.user.coverimages.banner_url || action.user.coverimages.banner,
        company: action.user.company,
        handle: action.user.handle?.substr(1),
        profile: action.user.profile
      });
    case actionTypes.UPDATE_USER_JOB_ROLE_FAMILY:
      return state.merge({
        jobFamily: action.user.jobRole || {}
      });
    case actionTypes.UPDATE_FOLLOWING_USERS_COUNT:
      newState = state.withMutations(mutableState => {
        if (action.state) {
          mutableState.update('followingCount', c => c + 1);
        } else {
          mutableState.update('followingCount', c => c - 1);
        }
        mutableState.set('needToUpdateFollowing', true);
      });
      return newState;
    case actionTypes.CHANGE_RECEIVE_SKILLS_LOADED_STATUS:
      return state.set('isUserSkillsLoaded', action.isUserSkillsLoaded);
    case actionTypes.RECEIVE_SKILLS:
      return state
        .set('userSkills', action.data)
        .set('isUserSkillsLoaded', true)
        .set('globalAssetSyncPending', action.data.globalAssetSyncPending);
    case actionTypes.UPDATE_SKILLS:
      return state.setIn(['profile', 'expertTopics'], action.skills);
    case actionTypes.RECEIVE_USER_PASSPORT:
      return state
        .set('userPassport', action.data)
        .set('globalAssetSyncPending', action.data.globalAssetSyncPending)
        .set('isUserPassportLoaded', true);
    case actionTypes.RECEIVE_PUBLIC_USER_PASSPORT:
      return state.set('publicUserPassport', action.data).set('isUserPassportLoaded', true);
    case actionTypes.RECEIVE_PUBLIC_PROFILE:
      return state.set('publicProfile', action.data).set('isLoadingPublicProfile', false);
    case actionTypes.REMOVE_PUBLIC_PROFILE:
      return state.set('publicProfile', false).set('publicProfileBasicInfo', false);
    case actionTypes.UPDATE_USER_EXPERTISE_AND_INTEREST:
      return state.merge({ profile: action.profile });
    case actionTypes.RECEIVE_WALLET_BALANCE:
      return state.set('walletBalance', action.walletbalance?.balance || 0);
    case actionTypes.RECEIVE_WALLET_TRANSACTIONS:
      let walletTransactions = !action.reload
        ? state.get('walletTransactions').concat(Immutable.fromJS(action.walletTransactions || []))
        : action.walletTransactions;
      return state.merge({
        walletTransactions: walletTransactions,
        walletTransactionsCount: action.walletTransactionsCount || 0
      });
    case actionTypes.SHOW_WALLET_LOADING:
      return state.set('showWalletLoading', true);
    case actionTypes.HIDE_WALLET_LOADING:
      return state.set('showWalletLoading', false);
    case actionTypes.FETCH_NOTIFICATION_CONFIG:
      return state.merge({
        notificationConfig: action.notificationConfig,
        showNotificationTab: action.showNotificationTab
      });
    case actionTypes.TRANSLATION_UPDATED:
      return state.set('currentAppLanguage', action.currentLanguage);
    case actionTypes.UPDATE_SUB_ADMIN:
      return state.set('subAdmin', action.sub_admin);
    case actionTypes.UPDATE_GTC_CONFIRMATION_MODAL:
      return state.set('showGtcConfirmationModal', false);
    case actionTypes.UPDATE_IS_FACTOR_ENROLLED:
      return state.set('isFactorEnrolled', action.isFactorEnrolled);
    case actionTypes.UPDATE_USER_PROFILE:
      return state.mergeIn(['profile'], action.profile);
    case actionTypes.COMPLETE_ONBOARDING_FOR_CURRENT_USER:
      return state.set('onboardingCompleted', action.onboardingCompleted);
    case actionTypes.UPDATE_USER_DETAILS_IN_ONBOARDING:
      const { name, profile } = action.user;
      return state.merge({ name, profile });
    case actionTypes.RECEIVE_PUBLIC_PROFILE_BASIC_INFO:
      return state
        .set('publicProfileBasicInfo', action.userParam)
        .set('isLoadingPublicProfile', false);
    case actionTypes.SET_SAML_REQUEST_DATA:
      return state.merge({ samlRequestPayload: action.payload });
    default:
      return state;
  }
}
