import users from './usersReducer';
import discovery from './discoveryReducer';
import cards from './cardsReducer';
import pathways from './pathwaysReducer';
import pathwaysv2Reducer from './pathwayV2Reducer';
import ecl from './eclReducer';
import results from './resultsReducer';
import feed from './feedReducer';
import todaysInsights from './todaysInsightsReducer';
import modal from './modalReducer';
import modalStandAlone from './modalStandAloneReducer';
import snackbar from './snackBarReducer';
import snackbarV2 from './snackBarV2Reducer';
import onboarding from './onboardingReducer';
import onboardingV5Reducer from './onboardingV5Reducer';
import channels from './channelsReducer';
import courses from './coursesReducer';
import groupsV2 from './groupsReducerV2';
import groupAnalytic from './groupAnalyticReducer';
import assignments from './assignmentsReducer';
import origin from './originReducer';
import config from './configReducer';
import learningQueue from './learningQueueReducer';
import teamActivity from './teamActivityReducer';
import relevancyRating from './relevancyRatingReducer';
import sociative from './sociativeReducer';
import talentmarketplaceReducer from './talentmarketplaceReducer';
import myLearningPlan from './myLearningPlansReducer';
import journey from './journeyReducer';
import journeysv2Reducer from './journeysV2Reducer';
import mlpv5 from './mylearningplanV5Reducer';
import routerLocations from './routerLocationsReduser';
import carousel from './carouselsReducer';
import shareContent from './shareContentReducer';
import contentMultiaction from './multiactionReducer';
import profile from './profileReducer';
import channelReducerV3 from './channelReducerV3';
import recommendationsFilters from './recommendationsFiltersReducer';
import search from './searchReducer';
import notifications from './notificationReducer';
import careerPreferences from './careerPreferencesReducer';
import availableLocations from './availableLocationsReducer';
import workExperiences from './workExperiencesReducer';
import organizations from './organizationsReducer';
import locationsConfiguration from './locationsConfigurationReducer';
import availableFiltersCounters from './availableFiltersCountersReducer';
import memoizedMatchingJobs from './memoizedMatchingJobsReducer';
import jobFamilies from './jobFamiliesReducer';
import timeZones from './timeZonesReducer';
import mentorProfile from './mentorProfileReducer';
import developmentPlan from './developmentPlanReducer';
import rejectionReasons from './rejectionReasonsReducer';
import configService from './configServiceReducer';
import sourcingState from './sourcingReducer';
import recommendationFeedback from './recommendationFeedbackReducer';
import savedSearches from './savedSearchesReducer';

export default {
  discovery,
  users,
  feed,
  cards,
  pathways,
  pathwaysv2Reducer,
  modal,
  modalStandAlone,
  snackbar,
  snackbarV2,
  onboarding,
  onboardingV5Reducer,
  groupsV2,
  groupAnalytic,
  channels,
  courses,
  ecl,
  results,
  assignments,
  origin,
  routerLocations,
  todaysInsights,
  config,
  learningQueue,
  teamActivity,
  relevancyRating,
  sociative,
  talentmarketplaceReducer,
  myLearningPlan,
  journey,
  journeysv2Reducer,
  mlpv5,
  carousel,
  shareContent,
  contentMultiaction,
  profile,
  channelReducerV3,
  recommendationsFilters,
  search,
  notifications,
  careerPreferences,
  availableLocations,
  workExperiences,
  organizations,
  locationsConfiguration,
  availableFiltersCounters,
  memoizedMatchingJobs,
  timeZones,
  jobFamilies,
  mentorProfile,
  developmentPlan,
  configService,
  rejectionReasons,
  sourcingState,
  recommendationFeedback,
  savedSearches
};
