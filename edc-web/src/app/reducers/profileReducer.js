import * as actionTypes from '../constants/actionTypes';
import Immutable from 'immutable';
import { uniqBy, differenceBy } from 'lodash';

const INIT = {
  userActivities: null,
  integrationList: [],
  externalCourses: [],
  inProgressArray: [],
  inProgressCount: 0,
  dashboardInfo: getInitalDashboardInfo()
};

export default function profileReducer(state = Immutable.fromJS(INIT), action) {
  let newState;
  switch (action.type) {
    case actionTypes.RECEIVE_PROFILE_SKILLS:
      return state;
    case actionTypes.RECEIVE_INTEGRATION:
      newState = state;
      newState = newState.set('integrationList', action.list);
      return newState;

    case actionTypes.RECEIVE_PUBLIC_PROFILE:
      newState = state;
      newState = newState
        .set('externalCourses', action.data.externalCourses)
        .set('profile', action.data.profile)
        .set('skills', action.data.skills)
        .set('userCardBadges', action.data.userCardBadges)
        .set('userRoles', action.data.profile.userRoles)
        .set('userClcBadges', action.data.userClcBadges);
      return newState;

    case actionTypes.GET_IN_PROGRESS:
      newState = state;
      newState = newState
        .set('inProgressArray', action.inProgressData.cards)
        .set('inProgressCount', action.inProgressData.total);
      return newState;

    case actionTypes.UPDATE_PUBLIC_PROFILE_INFO:
      newState = state;
      let diff = differenceBy(getInitalDashboardInfo(), action.dashboardInfo, 'name');
      newState = newState.set(
        'dashboardInfo',
        Immutable.List(
          action.dashboardInfo.length
            ? uniqBy([...action.dashboardInfo, ...diff], 'name')
            : getInitalDashboardInfo()
        )
      );
      return newState;

    case actionTypes.SET_ACTIVE_TOPICS:
      newState = state;
      newState = newState.set('activeTopics', action.activeTopics);
      return newState;
    default:
      return state;
  }
}

function getInitalDashboardInfo() {
  const v1DashboardInfo = [
    {
      name: 'Learning Hours',
      visible: false
    },
    {
      name: 'My Badges',
      visible: false
    },
    {
      name: 'In Progress',
      visible: false
    },
    // {
    //   "name": "Active Topics",
    //   "visible": false
    // },
    {
      name: 'Top Content',
      visible: false
    },
    {
      name: 'Activity Stream',
      visible: false
    },
    { name: 'Where you left off...', visible: false },
    { name: 'Manager View', visible: false }
  ];

  return [
    ...v1DashboardInfo,
    { name: 'My Skill Assessment', visible: false },
    { name: 'My Groups', visible: false },
    { name: 'My Channels', visible: false },
    { name: 'Skill Overview', visible: false },
    { name: 'My Skills', visible: false },
    { name: 'My Assessments', visible: false },
    { name: 'My Certificates', visible: false },
    { name: 'Learning Hours Predictive', visible: false },
    { name: 'My Work Histories', visible: false },
    { name: 'Points in user profile', visible: true },
    { name: 'Details in user profile', visible: false },
    { name: 'Contact info in user profile', visible: false },
    { name: 'My Patents', visible: false },
    { name: 'My Content', visible: false }
  ];
}
