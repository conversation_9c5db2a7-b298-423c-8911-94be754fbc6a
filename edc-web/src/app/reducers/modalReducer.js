import * as actionTypes from '../constants/actionTypes';
import * as modalTypes from '../constants/modalTypes';
import Immutable from 'immutable';
export default function modalReducer(
  state = Immutable.Map({ open: false, openCard: false, typeBefore: null, dataBefore: {} }),
  action
) {
  // eslint-disable-next-line sonarjs/max-switch-cases
  switch (action.type) {
    case actionTypes.CLOSE_MODAL:
      return Immutable.Map({ open: false, typeBefore: null, dataBefore: {} });
    case actionTypes.OPEN_ADD_TO_PATHWAY_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.ADD_TO_PATHWAY,
        cardId: action.cardId,
        card: action.card,
        cardType: action.cardType,
        typeBefore: null,
        dataBefore: {},
        isAddingToJourney: action.isAddingToJourney,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_ADD_TO_JOURNEY_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.ADD_TO_JOURNEY,
        cardId: action.cardId
      });
    case actionTypes.OPEN_CONFIRMATION_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.CONFIRM,
        title: action.title ? action.title : 'Confirm',
        message: action.message,
        hideConfirmBtn: action.hideConfirmBtn,
        callback: action.callback,
        hideCancelBtn: action.hideCancelBtn,
        confirmBtnTitle: action.confirmBtnTitle,
        isNegativeValue: action.isNegativeValue,
        cancelBtnTitle: action.cancelBtnTitle,
        anchorRef: action.anchorRef
      });
    case actionTypes.OPEN_PRIVATE_CARD_CONFIRMATION_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.CONFIRM_PRIVATE_CARD,
        title: action.title ? action.title : 'Confirm',
        message: action.message,
        isPrivate: action.isPrivate,
        callback: action.callback,
        confirmBtnTitle: action.confirmBtnTitle,
        isNegativeValue: action.isNegativeValue
      });
    case actionTypes.OPEN_ASSIGN_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.ASSIGN,
        card: action.card,
        selfAssign: action.selfAssign,
        assignedStateChange: action.assignedStateChange,
        selfAssignedStateChange: action.selfAssignedStateChange,
        anchorRef: action.anchorRef
      });
    case actionTypes.OPEN_INVITE_V2_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.INVITE_V2_USER_TO_GROUP,
        isAdpUser: action.isAdpUser
      });
    case actionTypes.OPEN_CHANNEL_CARDS_MODAL:
      return Immutable.Map({
        open: true,
        typeBefore: modalTypes.CHANNEL_CARDS,
        dataBefore: action,
        type: modalTypes.CHANNEL_CARDS,
        modalTitle: action.modalTitle,
        count: action.count,
        cards: action.cards,
        channel: action.channel,
        editMode: action.editMode,
        cardModalDetails: action.cardModalDetails
      });
    case actionTypes.OPEN_ADD_CURATORS_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.CHANNEL_ADD_CURATORS,
        channelId: action.channelId,
        curators: action.curators,
        currentUserId: action.currentUserId
      });

    case actionTypes.OPEN_PATHWAY_CONGRATULATION_MODAL:
      return Immutable.Map({
        open: true,
        userBadge: action.userBadge,
        type: modalTypes.CONGRATULATION_MODAL
      });
    case actionTypes.OPEN_POST_TO_CHANNEL_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.POST_TO_CHANNEL_MODAL,
        card: action.card,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_PAYPAL_SUCCESS_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.PAYPAL_SUCCESS_MODAL,
        paymentData: action.paymentData
      });
    case actionTypes.OPEN_METRANET_PAYMENT_STATUS_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.METRANET_PAYMENT_STATUS_MODAL,
        paymentData: action.paymentData
      });
    case actionTypes.OPEN_SKILLS_DIRECTORY_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.SKILLS_DIRECTORY_MODAL,
        changeRole: action.changeRole,
        role: action.role,
        isSettingsPage: action.isSettingsPage,
        isCareerPathPage: action.isCareerPathPage
      });
    case actionTypes.OPEN_SA_INVITE_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.SA_INVITE_MODAL
      });
    case actionTypes.OPEN_USER_NAME_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.USER_NAME_MODAL
      });
    case actionTypes.OPEN_UPLOAD:
      return Immutable.Map({
        open: true,
        type: modalTypes.UPLOAD_MODAL,
        uploadMeta: action.uploadMeta
      });
    case actionTypes.OPEN_GTC_CONFIRMATION_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.GTC_CONFIRMATION_MODAL
      });
    case actionTypes.OPEN_BECOME_A_MENTOR_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.BECOME_A_MENTOR_MODAL,
        editing: action.editing,
        meta: action.meta,
        onSuccessSaveProfile: action.onSuccessSaveProfile
      });
    case actionTypes.OPEN_BECOME_A_MENTOR_CONFIRMATION_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.BECOME_A_MENTOR_CONFIRMATION_MODAL,
        mentorshipId: action.mentorshipId
      });
    case actionTypes.OPEN_VIEW_COMMENT_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.VIEW_COMMENT_MODAL,
        modalMetaData: action.modalMetaData,
        onSuccess: action.onSuccess,
        durationModalConfig: action.durationModalConfig
      });
    case actionTypes.OPEN_REJECTION_COMMENT_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.REJECTION_COMMENT_MODAL,
        onReject: action.onReject,
        rejectionModalConfig: action.rejectionModalConfig,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_ACCEPT_MENTORSHIP_WITH_DURATION_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.ACCEPT_MENTORSHIP_WITH_DURATION_MODAL,
        durationModalConfig: action.durationModalConfig
      });
    case actionTypes.OPEN_WITHDRAW_COMMENT_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.WITHDRAW_COMMENT_MODAL,
        withdrawModalConfig: action.withdrawModalConfig
      });
    case actionTypes.OPEN_REQUEST_MENTORSHIP_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.REQUEST_MENTORSHIP_MODAL,
        id: action.id,
        name: action.name,
        onSuccess: action.onSuccess,
        mentorData: action.mentorData,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_SKILLS_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.SKILLS_MODAL,
        skills: action.skills,
        modalType: action.modalType,
        onSkillAddedToPassport: action.onSkillAddedToPassport,
        openedFrom: action.openedFrom,
        skillsFrom: action.skillsFrom,
        additionalProps: action.additionalProps,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_USER_SKILLS_ASSESSMENT_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.USER_SKILLS_ASSESSMENT_MODAL
      });

    case actionTypes.OPEN_OMP_USER_NOTIFICATION_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.OMP_USER_NOTIFICATION_MODAL,
        modalData: action.modalData
      });
    case actionTypes.OPEN_OMP_SHARE_MODAL: {
      // eslint-disable-next-line no-unused-vars
      const { type, ...rest } = action;
      return Immutable.Map({
        open: true,
        type: modalTypes.OMP_SHARE_MODAL,
        ...rest
      });
    }
    case actionTypes.OPEN_SIMPLIFIED_SHARE_MODAL: {
      // eslint-disable-next-line no-unused-vars
      const { type, ...rest } = action;
      return Immutable.Map({
        open: true,
        type: modalTypes.OMP_SIMPLIFIED_SHARE_MODAL,
        ...rest
      });
    }

    case actionTypes.OPEN_VIEW_MESSAGE_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.VIEW_MESSAGE_MODAL,
        sharersWithMessages: action.sharersWithMessages
      });

    case actionTypes.OPEN_SUBSCRIBED_USER_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.SUBSCRIBED_USER_MODAL,
        projectId: action.projectId,
        subscribedUsers: action.subscribedUsers,
        push: action.push
      });
    }
    case actionTypes.OPEN_MATCHING_SKILLS_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.MATCHING_SKILLS_MODAL,
        skills: action.skills,
        skillsFrom: action.skillsFrom,
        onOpenSkillsModal: action.onOpenSkillsModal,
        userId: action.userId,
        hideSkillsAdditionLink: action.hideSkillsAdditionLink,
        showCandidateLevelLabel: action.showCandidateLevelLabel,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    }
    case actionTypes.OMP_CAREERPATH_OPEN_FILTERS_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.CAREER_PATH_FILTER,
        filtersState: action.filtersState,
        recommended: action.recommended,
        associationId: action.associationId
      });
    }
    case actionTypes.OPEN_MATCH_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.MATCH_MODAL,
        jobId: action.jobId,
        jobType: action.jobType,
        scoreObject: action.scoreObject,
        isCareerPathEnabled: action.isCareerPathEnabled,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    }
    case actionTypes.OPEN_RECOMMENDATION_FEEDBACK_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.RECOMMENDATION_FEEDBACK_MODAL,
        thumbSelection: action.thumbSelection,
        onSubmit: action.onSubmit,
        opportunities: action.opportunities,
        opportunityType: action.opportunityType,
        focusReturnElement: action.focusReturnElement
      });
    }
    case actionTypes.OPEN_MANAGER_SKILL_ASSESSMENT_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.MANAGER_SKILL_ASSESSMENT_MODAL,
        teamMembersList: action.teamMembersList,
        assessmentType: action.assessmentType
      });
    }
    case actionTypes.OPEN_MANAGER_RECOMMEND_UPSKILL_MODAL: {
      return Immutable.Map({
        open: true,
        type: modalTypes.MANAGER_RECOMMEND_UPSKILL_MODAL,
        data: action.data
      });
    }
    case actionTypes.OPEN_REVIEW_ASSESSMENT_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.REVIEW_ASSESSMENT_MODAL,
        reportee_id: action.reportee_id
      });
    case actionTypes.OPEN_COMPLETE_YOUR_PROFILE_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.COMPLETE_YOUR_PROFILE_MODAL,
        active_tab: action.active_tab,
        cb: action.cb,
        activeExperienceId: action.activeExperienceId,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_APPROVERS_DETAILS_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.APPROVERS_DETAILS_MODAL,
        data: action.data,
        openedFromHtmlElement: action.openedFromHtmlElement
      });
    case actionTypes.OPEN_CREATE_OPPORTUNITY_ALERT_MODAL:
      return Immutable.Map({
        open: true,
        type: modalTypes.CREATE_OPPORTUNITY_ALERT_MODAL,
        data: action.data
      });
    default:
      return state;
  }
}
