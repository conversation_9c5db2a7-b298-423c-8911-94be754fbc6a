import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { getDefaultImageS3 } from 'edc-web-sdk/requests/cards.v2';
import { JWT } from 'edc-web-sdk/requests/csrfToken';
import listMenu from './utils/orderMenu';
import { readCookie, deleteCookie } from 'edc-web-sdk/helpers/cookies';
import { setFirstFeedTab } from '@actions/feedActions.js';
import { getFilestackDefaultImages } from 'edc-web-sdk/requests/filestack';
import { addNewStringForTranslation } from 'edc-web-sdk/requests/translation';
import checkingUserInactivity from './utils/checkingUserInactivity';
import { UNAUTHORIZED_ULR } from 'edc-web-sdk/requests/validationConstants';
import { getFilestackConfigKeysBasedOnOrgType } from 'edc-web-sdk/helpers/getFilestackConfigKeys';
import withRouter from '@hocs/withRouter.js';
import { MobileBanner } from './containers/AppPages';
import { shouldShowHomev2 } from '@pages/TalentMarketplace/util';
/*********************************************************************
 * All routing components are getting imported through AppPages file *
 *********************************************************************/

import V2 from './Router';
import LD from './containers/LDStore.js';
import { checkMfaFlow } from './utils/index.js';

import {
  AFTER_LOGIN_CONTENT_URL,
  AFTER_ONBOARDING_URL,
  CARD_IMAGES,
  DEFAULT_IMAGES_S3,
  FILESTACK_DEFAULT_IMAGES,
  PREV_IMAGE,
  REDIRECT_TO_APP
} from './constants/localStorageConstants.js';
import { isLocalStorageSupported, isLocationContentURL } from './actions/currentUserActions';

class Routing extends Component {
  constructor(props, context) {
    super(props, context);
    const isHomeV2Enabled = shouldShowHomev2();
    this.isHomeV2Enabled = isHomeV2Enabled;
    this.state = {
      showBanner: true
    };
    this.mobileAppBanner = LD.showMobileAppBanner();
    this.customDefaultImages = LD.customDefaultImages();
    this.unauthRoutes = [
      '/create_account',
      '/log_in',
      '/forgot_password',
      '/sent',
      '/confirm',
      '/changed',
      '/invalid',
      '/pre_register',
      '/access_denied',
      '/user/login',
      '/user/signup',
      '/user/pre_register',
      '/' // "/" is no longer a valid route, this will force it to redirect
    ];
    this.onboardingRoutes = ['/onboarding', '/onboard/v2'];

    // Get first index option
    this.configData = this.props.team.get('config') || {};
    this.recommendationsVisibilityConfig = !!this.configData.OrgCustomizationConfig?.feed?.[
      'feed/recommendations'
    ]?.visible;

    this.OrgConfigData = this.props.team.get('OrgConfig') || {};
    this.TopMenu = [];

    this.feedData = this.props.team.get('Feed') || {};

    let feedConfig = new Array(Object.keys(this.feedData).length);
    Object.keys(this.feedData).map(key => {
      let feed = this.feedData[key] || {};
      if (feed.visible) {
        feedConfig.splice(feed.index, 0, key);
      }
    });

    const firstRouteKey = feedConfig[Object.keys(feedConfig)[0]];
    this.props.dispatch(setFirstFeedTab(firstRouteKey));
    this.ROUTES = <V2 />;
  }

  componentDidMount() {
    const {
      inactivityTimeout,
      singleLogoutUrl,
      defaultOrgLanguage,
      oktaDomain,
      oktaEnabled,
      currentUserId,
      auth8Details
    } = this.props;
    window.addEventListener('storage', this.checkLogUserState);
    this.setFilestackDefaultImages();
    if (
      inactivityTimeout &&
      checkingUserInactivity &&
      !UNAUTHORIZED_ULR.test(window.location.pathname)
    ) {
      const oktaPayload = {
        oktaUrl: oktaDomain,
        oktaEnabled: oktaEnabled
      };
      checkingUserInactivity({
        timeout: inactivityTimeout,
        signOutLink: '',
        oktaPayload,
        singleLogoutUrl,
        defaultOrgLanguage,
        currentUserId,
        pollerFrequency: auth8Details.poller_frequency
      });
    }
  }

  componentDidUpdate(prevProps) {
    const { isLoggedIn } = this.props;
    const { isLoggedIn: _isLoggedIn } = prevProps;

    if (_isLoggedIn !== isLoggedIn) {
      if (
        this.customDefaultImages &&
        this.configData &&
        this.configData.custom_images_bucket_name &&
        this.configData.custom_images_region
      ) {
        const payload = {
          bucket_info: {
            bucket_name: this.configData.custom_images_bucket_name,
            bucket_region: this.configData.custom_images_region
          }
        };
        getDefaultImageS3(payload)
          .then(data => {
            if (data?.image_links?.length) {
              if (localStorage.getItem(PREV_IMAGE) === 'S3') {
                localStorage.setItem(DEFAULT_IMAGES_S3, JSON.stringify(data.image_links));
              } else {
                localStorage.removeItem(CARD_IMAGES);
                localStorage.setItem(DEFAULT_IMAGES_S3, JSON.stringify(data.image_links));
                localStorage.setItem(PREV_IMAGE, 'S3');
              }
            } else {
              this.checkS3Image();
            }
          })
          .catch(err => {
            console.error(`Error in Routing.componentDidUpdate.getDefaultImageS3.func : ${err}`);
            this.checkS3Image();
          });
      } else {
        this.checkS3Image();
      }
    }
  }

  shouldComponentUpdate(nextProps, nextState) {
    const { onboardingCompleted, isLoggedIn, oktaEnabled, passwordChangeable } = nextProps;
    const pathname = window.location.pathname;
    const isResetPasswordDisabled =
      window.__edOrgData.configs.find(item => item.name === 'reset_password_disabled')?.value ||
      false;
    const isUnauthRoutes = this.unauthRoutes.includes(pathname);
    const isOnboardingRoutes = this.onboardingRoutes.includes(pathname);
    const isOktaVerifyRoute = pathname.includes('okta-verify'); // This route is for MFA
    const getLoginContentUrl = localStorage.getItem(AFTER_LOGIN_CONTENT_URL);

    const sendToHomepage =
      onboardingCompleted &&
      isLoggedIn &&
      (isUnauthRoutes || isOnboardingRoutes || isOktaVerifyRoute) &&
      (!getLoginContentUrl || getLoginContentUrl === '/');

    const isUserMfaNeeded =
      isLoggedIn && checkMfaFlow(this.props.navigate, oktaEnabled, passwordChangeable);

    if (sendToHomepage && !isUserMfaNeeded) {
      if (!window.isUserSeeingNavV2) {
        // If not Nav V2, we need to utilize the old TopMenu items to render the routes and redirect
        // But if it is Nav V2, we defer the homepage navigation of / to UnifiedNav CDS component
        if (this.TopMenu.length === 0) {
          this.TopMenu = listMenu(this.OrgConfigData.topMenu);
        }
        const { url, defaultUrl } = this.TopMenu?.[0] || {};
        this.props.navigate(url || defaultUrl || '/');
      }
    } else if (isLoggedIn && !onboardingCompleted && !isOnboardingRoutes && !isUserMfaNeeded) {
      this.props.navigate('/onboarding');
    }
    if (
      pathname.includes('log_in') ||
      (pathname.includes('forgot_password') && isResetPasswordDisabled)
    ) {
      this.props.navigate('/user/login');
    }
    if (
      this.props.isLoggedIn &&
      this.props.pathname !== nextProps.pathname &&
      this.configData?.capture_translations
    ) {
      addNewStringForTranslation();
    }
    // We need to clear the afterLoginContentURL & afterOnboardingURL
    // after user as completed the redirection to desired page
    if (
      isLoggedIn &&
      isLocalStorageSupported() &&
      isLocationContentURL() &&
      getLoginContentUrl &&
      getLoginContentUrl.includes(window.location.pathname) &&
      getLoginContentUrl !== '/'
    ) {
      localStorage.setItem(AFTER_LOGIN_CONTENT_URL, '/');
      localStorage.setItem(AFTER_ONBOARDING_URL, '/');
    }

    if (this.props.isLoggedIn !== nextProps.isLoggedIn) {
      return true;
    } else if (
      this.props.team.get('Feed') !== nextProps.team.get('Feed') ||
      this.props.team.get('OrgConfig') !== nextProps.team.get('OrgConfig') ||
      this.props.team.get('config') !== nextProps.team.get('config')
    ) {
      return true;
    } else if (nextState.showBanner !== this.state.showBanner) {
      return true;
      // Set shouldComponentUpdate to true for saml auth( When a SP is calling the url for authentication via SAML)
    } else if (pathname.includes('/saml/auth')) {
      return true;
    } else return false;
  }

  setFilestackDefaultImages = () => {
    let stored = JSON.parse(localStorage.getItem(FILESTACK_DEFAULT_IMAGES));
    let now = new Date().getTime().toString();
    // refresh localStorage after every 24 hours
    if (stored && (now - stored.timestamp) / 1000 / 60 / 60 < 24) {
      return true;
    } else {
      let defaultImageUrl = getFilestackConfigKeysBasedOnOrgType().filestackDefaultImagesUrl;
      //this is declared in all the env
      if (defaultImageUrl) {
        getFilestackDefaultImages(defaultImageUrl)
          .then(resp => {
            let object = { value: resp, timestamp: new Date().getTime() };
            localStorage.setItem(FILESTACK_DEFAULT_IMAGES, JSON.stringify(object));
          })
          .catch(err => {
            console.error(`Error in Routing.getFilestackDefaultImages.func : ${err}`);
          });
      }
    }
  };

  checkS3Image = () => {
    localStorage.setItem(DEFAULT_IMAGES_S3, false);
    if (localStorage.getItem(PREV_IMAGE) === 'S3') {
      localStorage.removeItem(CARD_IMAGES);
    }
    localStorage.setItem(PREV_IMAGE, 'filestack');
  };

  checkLogUserState = event => {
    if (
      event.key === 'isLoggedIn' &&
      event.newValue !== event.oldValue &&
      (event.newValue === 'true' ||
        (event.newValue === 'false' && window.location.pathname !== '/log_in')) &&
      !document.hasFocus() // this fixes IE redundant page reload on login
    ) {
      window.location.reload();
    }
  };

  closeBanner = () => {
    this.setState({ showBanner: false });
  };

  render() {
    let isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );

    if (
      this.mobileAppBanner &&
      this.state.showBanner &&
      window.location.pathname.indexOf('/forgot_password') == -1 &&
      window.location.pathname.indexOf('/visit') == -1 &&
      window.location.pathname.indexOf('/mobile-insights') == -1 &&
      isMobileDevice
    ) {
      return <MobileBanner closeBanner={this.closeBanner} />;
    }

    if (this.props.isLoggedIn === true) {
      if (this.unauthRoutes.includes(window.location.pathname)) {
        let link = localStorage.getItem(REDIRECT_TO_APP);
        if (link && JWT.token) {
          localStorage.removeItem(REDIRECT_TO_APP);
          let fullName = this.props.name
            ? this.props.name.split(' ')
            : [this.props.firstName, this.props.lastName];
          link = `${link}?jwtToken=${JWT.token}&firstname=${fullName[0]}&lastname=${fullName[1]}`;
          window.location.href = link;
        } else if (!window.isUserSeeingNavV2) {
          // Same as above for unauthedRoute
          // If Nav V2, let CDS handle redirection otherwise use older TopMenu
          this.TopMenu = listMenu(this.OrgConfigData.topMenu);
          this.props.navigate(this.TopMenu[0].defaultUrl || this.TopMenu[0].url);
        }
      }
      if (readCookie('redirectRoute')) {
        window.location = readCookie('redirectRoute');
        deleteCookie('redirectRoute');
      } else {
        return this.ROUTES;
      }
    } else {
      if (window.location.pathname.includes('log_in')) {
        this.props.navigate('/user/login');
      }
      return this.ROUTES;
    }
  }
}

Routing.propTypes = {
  team: PropTypes.object,
  isLoggedIn: PropTypes.bool,
  firstName: PropTypes.string,
  lastName: PropTypes.string,
  name: PropTypes.string,
  navigate: PropTypes.func,
  profile: PropTypes.object,
  inactivityTimeout: PropTypes.number,
  onboardingCompleted: PropTypes.bool,
  filestackUrlExpireAfterSeconds: PropTypes.number,
  currentUserId: PropTypes.string,
  oktaDomain: PropTypes.string,
  oktaEnabled: PropTypes.bool,
  singleLogoutUrl: PropTypes.string,
  defaultOrgLanguage: PropTypes.string,
  currentUserIsAdmin: PropTypes.bool,
  passwordChangeable: PropTypes.bool,
  pathname: PropTypes.string,
  auth8Details: PropTypes.object
};

function mapStateToProps(state) {
  const {
    inactive_web_session_timeout,
    OktaDefaultApplication: { okta_domain: oktaDomain = '' } = {},
    'app.config.okta.enabled': oktaEnabled = false,
    DefaultOrgLanguage: defaultOrgLanguage,
    single_logout_url: singleLogoutUrl,
    auth8_details: auth8Details = {}
  } = state.team.get('config');
  return {
    currentUserIsAdmin: state.currentUser.get('isAdmin'),
    isLoggedIn: state.currentUser.get('isLoggedIn'),
    firstName: state.currentUser.get('first_name'),
    lastName: state.currentUser.get('last_name'),
    name: state.currentUser.get('name'),
    currentUserId: state.currentUser.get('id'),
    onboardingCompleted: state.currentUser.get('onboardingCompleted') || false,
    team: state.team,
    inactivityTimeout: inactive_web_session_timeout * 60 * 1000,
    oktaDomain,
    oktaEnabled,
    defaultOrgLanguage,
    singleLogoutUrl,
    passwordChangeable: state.currentUser.get('passwordChangeable'),
    auth8Details
  };
}
export default withRouter(connect(mapStateToProps)(Routing));
