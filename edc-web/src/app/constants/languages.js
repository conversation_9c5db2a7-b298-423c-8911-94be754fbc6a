export const langs = {
  'Chinese (Traditional)': 'zh-Hant',
  'Chinese (Simplified)': 'zh',
  'Dutch (Belgium)': 'nl-BE',
  'French (Canada)': 'fr-CA',
  'Indonesian (Indonesia)': 'id-ID',
  'Malay (Malaysia)': 'ms-Latn-MY',
  'Marathi (India)': 'mr-IN',
  'Portuguese (Brazil)': 'pt-BR',
  romanian: 'ro',
  'Serbian (Serbia)': 'sr-Latn-RS',
  'Slovak (Slovakia)': 'sk-SK',
  'Tamil (India)': 'ta-IN',
  'Spanish LATAM': 'es-LM',
  Arabic: 'ar',
  Chinese: 'zh-CN',
  Czech: 'cs',
  Danish: 'da',
  Dutch: 'nl',
  English: 'en',
  Finnish: 'fi',
  French: 'fr',
  German: 'de',
  Greek: 'el',
  Gujarati: 'gu',
  Hebrew: 'he',
  Hindi: 'hi',
  Hungarian: 'hu',
  Italian: 'it',
  Japanese: 'ja-JP',
  Korean: 'ko',
  Lithuanian: 'lt-LT',
  Polish: 'pl',
  Portuguese: 'pt',
  Russian: 'ru',
  Spanish: 'es',
  Swedish: 'sv',
  Telugu: 'te',
  Thai: 'th',
  Turkish: 'tr-TR',
  Ukrainian: 'uk',
  Urdu: 'ur',
  Vietnamese: 'vi'
};

export let languageKeyVal = [];
for (const key in langs) {
  if (langs.hasOwnProperty(key)) {
    const temp = {
      name: key,
      id: langs[key]
    };
    languageKeyVal.push(temp);
  }
}

export function languageKeyValConverter(languages) {
  const lang = [];
  for (const key in languages) {
    if (languages.hasOwnProperty(key)) {
      const temp = {
        name: key,
        id: languages[key]
      };
      lang.push(temp);
    }
  }
  return lang;
}
