export const ACTIVITYSMARTCARD = /[\[\]\\]*\(.*/g;

//removes comments/xml/html
export const XML_HTML_COMMENTS_REGEX = /(<!--.*?-->)|(<!--[\w\W\n\s]+?-->)|<\/?[^>]+(>|$)/g;

//eg. 2:38 PM || 2:38 pm || 9:08 am || 10:00 AM
export const TIME_FORMAT = /^(1[0-2]|0?[1-9]):[0-5][0-9] (AM|am|Am|aM|PM|pm|Pm|pM)$/;

//when user enter following url it converts to HTTPS
export const URL_ON_CHANGE_PROTOCOL_FOR_CKEDITOR = /^(http|https|ftp|news):\/\/(?=.)/i;

//Matches anchor tags without attributes
export const ANCHOR_TAG_REGEX = /<a /g;

// User experience in months 0 - 11
export const regExe0T011 = /^(0?[0-9]|[0-1][0-1])$/;
// User experience in years 0 - 99 (max two digit number)
export const regExe0T099 = /^(0?[0-9]|[0-9][0-9])$/;

// Removes the protocol from the url. For eg. "https://", "http://"
export const REMOVE_PROTOCOL_FROM_URL = /(^\w+:|^)\/\//;

// Skill passport Credential ID regex to allow special character
export const CREDENTIAL_ID_REGEX = /[^a-zA-Z0-9-/.\|, ]/;

// URL scheme checker
export const URL_SCHEME_REGEX = /^(http(s?)):\/\//i;

export const SPECIAL_CHAR_REGEX = /[^a-zA-Z0-9]/g;

export const LINE_BREAK_TAG_REGEX = /↵/gi;

export const TARGET_BLANK = /target="_blank"/g;

export const EMPTY_P_TAG_REGEX = /<p>\s*<\/p>/g;

export const CONSECUTIVE_NEW_LINE_REGEX = /\n+/g;

// This regex matches the URL pattern present for pathway consumption pages
export const PATHWAY_CONSUMPTION_URL_MATCH_REGEX = /^\/pathways\/[\w-]+\/cards\/[\w-]+$/;

// This regex matches the URL pattern present for Journey consumption pages
export const JOURNEY_CONSUMPTION_URL_MATCH_REGEX = /^\/journey\/[\w-]+\/cards\/[\w-]+$/;

// Matches the target="_blank" attribute with whitespace in the begining and ending
export const TARGET_BLANK_REGEX = /\s*target="_blank"\s*/;

// Matches anchor tags with attributes
export const ANCHOR_TAG_WITH_ATTRIBUTES_REGEX = /<a\s+(.*?)>/gi;

//This regex matches the start letter of each word, excluding words that have 's with a space after it (like "Bob's ")
export const FIRST_LETTER_OF_WORD_REGEX = /(?:^|\s|["([{]|'(?!s\s))+\S/g;
