//Application Bootstap
export const RECEIVE_ORG_INFO = 'receive_org_info';
export const UPDATE_ECS_ORG_INFO = 'update_ecs_org_info';
export const ERROR_INIT_ORG = 'error_init_org';
export const ORG_INFO_CACHE_REFRESH = 'org_info_cache_refresh';
export const GET_ROOT_PAGE_DATA = 'get_root_page_data';
export const SET_FEED_TAB_INFO = 'set_feed_tab_info';

//current user
export const RECEIVE_INIT_USER_INFO = 'receive_init_user_info';
export const USER_INFO_UPDATED = 'user_info_updated';
export const RECEIVE_GROUPLEADER_USER_INFO = 'receive_groupleader_user_info';
export const ERROR_INIT_USER = 'error_init_user';
export const ERROR_USER_LOGIN = 'error_user_login';
export const RECEIVE_FEED_TYPE_VIEW = 'receive_feed_type_view';
export const ERROR_UPDATED_USER_INFO = 'error_updated_user_info';
export const UPDATE_USER_DETAILS = 'update_user_details';
export const UPDATE_USER_JOB_ROLE_FAMILY = 'update_user_job_role_family';
export const RECEIVE_WALLET_BALANCE = 'recieve_wallet_balance';
export const RECEIVE_WALLET_RECHARGES = 'recieve_wallet_recharges';
export const RECEIVE_WALLET_TRANSACTIONS = 'recive_wallet_transcations';
export const SHOW_WALLET_LOADING = 'show_wallet_loading';
export const HIDE_WALLET_LOADING = 'hide_wallet_loading';
export const FETCH_NOTIFICATION_CONFIG = 'fetch_notification_config';
export const UPDATED_USER_DETAILS_FOR_SETTINGS_PAGE = 'updated_ser_details_for_settings_page';
export const TRANSLATION_UPDATED = 'translation_updated';
export const OPEN_USER_NAME_MODAL = 'open_user_name_modal';
export const OPEN_GTC_CONFIRMATION_MODAL = 'open_gtc_confirmation_modal';
export const RECEIVE_ORG_CONFIGS_AFTER_AUTHORIZATION = 'receive_org_configs_after_authorization';
export const UPDATE_IS_FACTOR_ENROLLED = 'update_is_factor_enrolled';
export const UPDATE_USER_PROFILE = 'update_user_profile';

//users
export const RECEIVE_MENTION_USERS = 'receive_mention_users';
export const REQUEST_TOGGLE_USER_FOLLOW = 'request_toggle_user_follow';
export const RECEIVE_TOGGLE_USER_FOLLOW = 'receive_toggle_user_follow';
export const RECEIVE_INTEGRATION = 'receive_integrations';
export const UPDATE_FOLLOWING_USERS_COUNT = 'update_following_users_count';
export const CHANGE_RECEIVE_SKILLS_LOADED_STATUS = 'change_receive_skills_loaded_status';
export const RECEIVE_SKILLS = 'receive_skills';
export const UPDATE_SKILLS = 'update_skills';
export const RECEIVE_USER_PASSPORT = 'receive_user_passport';
export const RECEIVE_PUBLIC_USER_PASSPORT = 'receive_public_user_passport';
export const RECEIVE_PUBLIC_PROFILE = 'receive_public_profile';
export const REMOVE_PUBLIC_PROFILE = 'remove_public_profile';
export const UPDATE_USER_EXPERTISE_AND_INTEREST = 'update_user_expertise_and_interest';
export const SET_CURRENT_USER_INFO = 'set_current_user_info';
export const UPDATE_REPORTED_CARDS = 'update_reported_cards';
export const UPDATE_REPORTED_COMMENTS = 'update_reported_comments';
export const UPDATE_SUB_ADMIN = 'update_sub_admin';
export const UPDATE_GTC_CONFIRMATION_MODAL = 'update_gtc_confirmation_modal';
export const RECEIVE_PUBLIC_PROFILE_BASIC_INFO = 'receive_public_profile_basic_info';

//profile
export const RECEIVE_PROFILE_SKILLS = 'receive_profile_skills';
export const GET_IN_PROGRESS = 'get_in_progress';
export const UPDATE_PUBLIC_PROFILE_INFO = 'update_public_profile_info';
export const SET_ACTIVE_TOPICS = 'set_active_topics';

//channels
export const REQUEST_TOGGLE_CHANNEL_FOLLOW = 'request_toggle_channel_follow';
export const RECEIVE_TOGGLE_CHANNEL_FOLLOW = 'receive_toggle_channel_follow';
export const RECEIVE_CHANNELS = 'receive_channels';

// channels V2
export const CREATE_CHANNEL = 'create_channel';

//courses
export const RECEIVE_COURSES = 'receive_courses';

//carousels
export const RECEIVE_CAROUSELS = 'receive_carousels';
export const RECEIVE_CAROUSEL_ITEMS = 'receive_carousel_items';

//groups
export const RECEIVE_GROUPS = 'receive_groups';
export const OPEN_INVITE_V2_MODAL = 'open_invite_v2_modal';
//groups v2
export const UPDATE_GROUP_DETAILS = 'update_group_details';
export const UPDATE_MEMBERS = 'update_members';
export const REMOVE_SHARED_CARD = 'remove_shared_card';
export const ADD_PENDING_MEMBER = 'add_pending_member';
export const LEAVE_GROUP = 'leave_group';
export const JOIN_GROUP = 'join_group';
export const ACCEPT_INVITE = 'accept_invite';
export const DECLINE_INVITE = 'decline_invite';
export const UPDATE_GROUP_INFORMATION = 'update_group_information';
//groups v3
export const REMOVE_GROUP_MEMBER = 'remove_group_member';

//group analytics
export const GET_GRAPH_DATA = 'get_graph_data';
export const SET_GROUP_DETAILS = 'set_group_details';
export const GET_TOP_CONTENT = 'get_top_content';
export const GET_TOP_CONTRIBUTORS = 'get_top_contributors';

//discovery
export const RECEIVE_DISCOVERY_COURSES = 'receive_discovery_courses';
export const RECEIVE_DISCOVERY_VIDEOS = 'receive_discovery_videos';
export const RECEIVE_DISCOVERY_PATHWAYS = 'receive_discovery_pathways';
export const RECEIVE_DISCOVERY_JOURNEYS = 'receive_discovery_journeys';
export const RECEIVE_FEATURED_PROVIDERS = 'receive_featured_providers';
export const GET_ALL_DISCOVER_CAROUSEL = 'get_all_discover_carousel';
export const UPDATE_DISCOVER_DATA = 'update_discover_data';
export const SAVE_DISCOVER_DATA = 'save_discover_data';
export const RECEIVE_USER_GROUPS = 'receive_user_groups';
export const UPDATE_CAROUSEL_DATA_BASED_ON_ENTITY_ID = 'update_carousel_data_based_on_entity_id';

// PICASSO ONLY
export const PICASSO_SAVE_DISCOVER_DATA = 'picasso_save_discover_data';

// onboarding
export const RECEIVE_INIT_ONBOARDING_STATE = 'receive_init_onboarding_state';
export const UPDATE_ONBOARDING_USER = 'update_onboarding_user';
export const RECEIVE_USER_PROFILE = 'receive_user_profile';
export const CHANGE_PASSWORD_REQUIRED_IN_ONBOARDING = 'change_password_required_in_onboarding';
export const UPDATE_ONBOARDING_STEP = 'update_onboarding_step';
export const RECEIVE_UPDATED_USER_INFO = 'receive_updated_user_info';
export const RECEIVE_TOPIC_SKILLS = 'receive_topic_skills';
export const UPDATE_USER_DETAILS_IN_ONBOARDING = 'update_user_details_in_onboarding';
export const COMPLETE_ONBOARDING_FOR_CURRENT_USER = 'complete_onboarding_for_current_user';
export const ERROR_FETCHING_SKILLS = 'error_fetching_skills';

//feed
export const RECEIVE_PROMOTED_FEED = 'receive_promoted_feed';
export const FIRST_FEED_TAB = 'first_feed_tab';

//assignments
export const REQUEST_USER_ASSIGNMENTS = 'request_user_assignments';
export const RECEIVE_USER_ASSIGNMENTS = 'receive_user_assignments';
export const UPDATE_ASSIGNMENT_COUNT = 'update_assignment_count';

// activity team
export const GET_ACTIVITY_TEAMS = 'get_activity_teams';

//cards
export const RECEIVE_CARDS = 'receive_cards';
export const RECEIVE_CARD = 'receive_card';
export const REQUEST_LIKE_COMMENT = 'request_like_comment';
export const RECEIVE_LIKE_COMMENT = 'receive_like_comment';

export const RECEIVE_DISMISS_CARD = 'receive_dismiss_card';
export const RECEIVE_DELETE_CARD = 'receive_delete_card';
export const RECEIVE_ARCHIVE_CARD = 'receive_archive_card';

export const SET_CURRENT_MKP_COURSE_CARD_ENROLLMENT_STATUS =
  'set_current_mkp_course_card_enrollment_status';

//config
export const TOGGLE_TOPNAV = 'toggle_topnav';
export const SAVE_UNIFIED_NAV_ITEMS = 'save_unified_nav_items';

//modal
export const OPEN_CONFIRMATION_MODAL = 'open_confirmation_modal';
export const OPEN_PRIVATE_CARD_CONFIRMATION_MODAL = 'open_private_card_confirmation_modal';
export const CLOSE_MODAL = 'close_modal';
export const OPEN_ASSIGN_MODAL = 'open_assign_modal';
export const OPEN_STANDALONE_MODAL = 'open_standalone_modal';
export const CLOSE_STANDALONE_CARD_MODAL = 'close_standalone_card_modal';
export const OPEN_ADD_TO_PATHWAY_MODAL = 'open_add_to_pathway_modal';
export const OPEN_ADD_TO_JOURNEY_MODAL = 'open_add_to_journey_modal';
export const OPEN_UPDATE_EXPERTISE_MODAL = 'open_update_expertise_modal';
export const OPEN_CHANNEL_CARDS_MODAL = 'open_channel_cards_modal';
export const OPEN_ADD_CURATORS_MODAL = 'open_add_curators_modal';
export const OPEN_POST_TO_CHANNEL_MODAL = 'open_post_to_channel_modal';
export const OPEN_SHOW_SKILL_MODAL = 'open_show_skill_modal';
export const OPEN_SKILLS_DIRECTORY_MODAL = 'open_skills_directory_modal';
export const OPEN_SA_INVITE_MODAL = 'open_sa_invite_modal';
export const OPEN_SKILLS_MODAL = 'open_skills_modal';
export const OPEN_USER_SKILLS_ASSESSMENT_MODAL = 'open_user_skills_assessment_modal';
export const OPEN_REVIEW_ASSESSMENT_MODAL = 'open_review_assessment_modal';
export const OPEN_COMPLETE_YOUR_PROFILE_MODAL = 'open_complete_your_profile_modal';

export const OPEN_OMP_USER_NOTIFICATION_MODAL = 'open_omp_user_notification_modal';
export const OPEN_OMP_SHARE_MODAL = 'open_omp_share_modal';
export const OPEN_SIMPLIFIED_SHARE_MODAL = 'open_simplified_share_modal';
export const OPEN_VIEW_MESSAGE_MODAL = 'open_view_message_modal';
export const OPEN_SUBSCRIBED_USER_MODAL = 'open_subscribed_user_modal';
export const OPEN_MATCHING_SKILLS_MODAL = 'open_matching_skills_modal';
export const OPEN_MATCH_MODAL = 'open_match_modal';
export const OPEN_MANAGER_SKILL_ASSESSMENT_MODAL = 'open_manager_skill_assessment_modal';
export const OPEN_MANAGER_RECOMMEND_UPSKILL_MODAL = 'open_manager_recommend_upskill_modal';
export const OPEN_APPROVERS_DETAILS_MODAL = 'open_approvers_details_modal';
//snackBar
export const OPEN_SNACKBAR = 'open_snackbar';
export const CLOSE_SNACKBAR = 'close_snackbar';

export const OPEN_SNACKBAR_V2 = 'open_snackbar_v2';
export const CLOSE_SNACKBAR_V2 = 'close_snackbar_v2';

//ecl
export const ECL_RESULTS_FOUND = 'ecl_results_found';
export const CARD_TYPE_CLICKED = 'card_type_clicked';
export const ORIGIN_TYPE_CLICKED = 'origin_type_clicked';

//pathways
export const RECEIVE_CARD_INTO_PATHWAYS = 'receive_card_into_pathways';
export const PATHWAYS_FOUND = 'pathways_found';
export const ADDED_CARD_TO_PATHWAY = 'added_card_to_pathway';
export const ERROR_WHILE_ADDING_TO_PATHWAY = 'error_while_adding_to_pathway';
export const OPEN_PATHWAY_CONGRATULATION_MODAL = 'open_pathway_congratulation_modal';
export const SAVE_CONSUMPTION_PATHWAY = 'save_consumption_pathway';

export const CONSUMPTION_PATHWAY_HISTORY = 'consumption_pathway_history';
export const REMOVE_CONSUMPTION_PATHWAY = 'remove_consumption_pathway';
export const REMOVE_CONSUMPTION_PATHWAY_HISTORY = 'remove_consumption_pathway_history';
export const REMOVE_REORDER_CARD_IDS_PATHWAY = 'delete_reorder_card_ids_pathway';

//Pathways v2 PICASSO
export const SAVE_PATHWAY_STEP_ONE = 'save_pathway_step_one';
export const SAVE_PATHWAY_STEP_TWO = 'save_pathway_step_two';
export const SAVE_CARDS_CONFIGURATION = 'save_cards_configuration';
export const RESET_CARDS_CONFIGURATION = 'reset_cards_configuration';
export const SET_CARD_OPTIONAL_STATUS = 'set_card_optional_status';
export const REMOVE_CARD_FROM_CONFIGURATION = 'remove_card_from_configuration';
export const REMOVE_SECTION_FROM_CONFIGURATION = 'remove_section_from_configuration';

//Journeys v2 PICASSO
export const SAVE_JOURNEY_STEP_ONE = 'save_journey_step_one';
export const SAVE_JOURNEY_STEP_TWO = 'save_journey_step_two';

//journey
export const REMOVE_REORDER_CARD_IDS_JOURNEY = 'delete_reorder_card_ids_journey';
export const SAVE_CONSUMPTION_JOURNEY = 'save_consumption_journey';

export const SAVE_CONSUMPTION_JOURNEY_OPEN_BLOCK = 'save_consumption_journey_open_block';
export const SAVE_CONSUMPTION_JOURNEY_PATHWAY_PAYLOAD = 'save_consumption_journey_pathway_payload';
export const CONSUMPTION_JOURNEY_HISTORY = 'consumption_journey_history';
export const REMOVE_CONSUMPTION_JOURNEY = 'remove_consumption_journey';
export const REMOVE_CONSUMPTION_JOURNEY_HISTORY = 'remove_consumption_journey_history';
export const SAVE_CONSUMPTION_JOURNEY_PATHWAY_INDEX = 'save_consumption_journey_pathway_index';
export const REMOVE_CONSUMPTION_JOURNEY_PATHWAY_INDEX = 'remove_consumption_journey_pathway_index';

// providers
export const RECEIVE_ECL_ITEMS = 'receive_ecl_items';

//today learning
export const REQUEST_USER_INTEREST = 'request_user_interest';
export const RECEIVE_USER_INTEREST = 'receive_user_interest';

// recommendations
export const RECOMMENDATIONS_ADD_FILTER_OBJECT = 'recommendations_set_filter_object';
export const RECOMMENDATIONS_UPDATE_FILTER_OBJECT = 'recommendations_update_filter_object';
export const RECOMMENDATIONS_DELETE_FILTER_OBJECT = 'recommendations_delete_filter_object';
export const RECOMMENDATIONS_LOAD_FILTER_OBJECTS = 'recommendations_load_filter_objects';

// learning queue
export const RECEIVE_LEARNING_QUEUE_ITEMS = 'receive_learning_queue_items';
export const REMOVE_BOOKMARK_FROM_LEARNING_QUEUE = 'remove_bookmark_from_learning_queue';

// relevancy rating
export const UPDATE_RATED_QUEUE_AFTER_RATING = 'update_rated_queue_after_rating';

//sociative
export const RECEIVE_SOCIATIVE_REGISTRATION_STATUS = 'receive_sociative_registration_status';

// my learning plan
export const QUARTERLY_ASSIGNMENTS = 'quarterly_assignments';
export const COMPETENCY_ASSIGNMENTS = 'competency_assignments';
export const RECEIVE_BOOKMARK_LEARNING_QUEUE = 'receive_bookmark_learning_queue';

// MLP v5

export const STORE_QUARTERLY_ASSIGNMENTS = 'store_quarterly_assignments';
export const STORE_COMPETENCY_ASSIGNMENTS = 'store_competency_assignments';
export const LOAD_MORE_ASSIGNMENTS = 'load_more_assignments';

// Monetization
export const OPEN_PAYPAL_SUCCESS_MODAL = 'open_paypal_success_modal';
export const OPEN_METRANET_PAYMENT_STATUS_MODAL = 'open_metranet_payment_status_modal';

// Share Content
export const ADD_SEARCHED_USER_FOR_SHARE = 'add_searched_user_for_share';
export const FETCH_CONTENT_TO_SHARE = 'fetch_content_to_share';
export const FETCH_MY_TEAM = 'fetch_my_team';
export const FETCH_USERS_FOR_SHARE = 'fetch_users_for_share';
export const ADD_USER_FOR_SHARE = 'add_user_for_share';
export const ADD_GROUP_FOR_SHARE = 'add_group_for_share';
export const REMOVE_USER_FROM_SHARE = 'remove_user_from_share';
export const REMOVE_GROUP_FROM_SHARE = 'remove_group_from_share';
export const CLEAR_SHARE_CONTENT_STATE = 'clear_share_content_state';
export const ADD_SEARCHED_GROUP_FOR_SHARE = 'add_searched_group_for_share';
export const REMOVE_SEARCHED_USER_FROM_SHARE = 'remove_searched_user_from_share';
export const REMOVE_SEARCHED_GROUP_FROM_SHARE = 'remove_searched_group_from_share';

// MultiAction Modal
export const FETCH_CONTENT_TO_ACTION = 'fetch_content_to_action';
export const FETCH_MY_TEAM_FOR_ACTIONS = 'fetch_my_team_for_actions';
export const FETCH_ALL_ASSIGNED = 'fetch_all_assigned';
export const FETCH_ASSIGNED_USERS_AND_GROUPS = 'fetch_assigned_users_and_groups';
export const FETCH_SHARED_USERS_AND_GROUPS = 'fetch_shared_users_and_groups';
export const FETCH_SEARCHED_ASSIGNED = 'fetch_searched_assigned';
export const FETCH_SEARCHED_SHARED = 'fetch_searched_shared';
export const ADD_USER_FOR_ACTION = 'add_user_for_action';
export const ADD_USER_FOR_COMPLETED_ASSIGNMENT = 'add_user_for_completed_assignment';
export const ADD_GROUP_FOR_ACTION = 'add_group_for_action';
export const REMOVE_USER_FROM_ACTION = 'remove_user_from_action';
export const REMOVE_GROUP_FROM_ACTION = 'remove_group_from_action';
export const CLEAR_ACTION_CONTENT_STATE = 'clear_action_content_state';
export const ADD_SEARCHED_GROUP_FOR_ACTION = 'add_searched_group_for_action';
export const CLEAR_REMOVE_USERS_LIST = 'clear_remove_users_list';
export const CLEAR_REMOVE_GROUPS_LIST = 'clear_remove_groups_list';
export const FILL_REMOVE_USERS_LIST = 'fill_remove_users_list';
export const FILL_REMOVE_GROUPS_LIST = 'fill_remove_groups_list';
export const FILL_ADD_USERS_LIST = 'fill_add_users_list';
export const FILL_ADD_GROUPS_LIST = 'fill_add_groups_list';
export const FILL_DIFF_GROUPS_LIST = 'fill_diff_groups_list';
export const CLEAR_ASSIGNED_STATE = 'clear_assigned_state';
export const CLEAR_NEWLY_SELECTED_USERS = 'clear_newly_selected_users';
export const SET_ASSIGNED_STATE = 'set_assigned_state';
export const ADD_SEARCHED_USERS_COUNT_FOR_ASSIGN = 'add_searched_users_count_for_assign';

// close popovers
//export const CLOSE_POPOVER = 'close_popover';

// New Channel Reducer actions

// remove assessment card from my learning plan in user panel

// clear channle reducer data

// channel v3 actions
export const SAVE_CHANNEL_DETAILS = 'save_channel_details';
export const SAVE_CHANNEL_FEATURED_CARDS = 'save_channel_fatured_cards';
export const REMOVE_CHANNEL_FEATURED_CARDS = 'remove_channel_fatured_cards';
export const ADD_FEATURED_CARD_IN_CHANNEL = 'add_featured_card_in_channel';
export const REMOVE_FEATURED_CARD_IN_CHANNEL = 'remove_featured_card_in_channel';
export const REMOVE_CARD_FROM_CHANNEL_CAROUSEL = 'remove_card_from_channel_carousel';
export const REMOVE_CHANNEL_CARD_FROM_CAROUSELS = 'remove_cards_from_carousels';

// group v4 actions
export const SAVE_MAPPED_ORGANIZATIONS = 'save_mapped_organizations';

// save sociative topics
export const SAVE_SOCIATIVE_TOPICS = 'save_sociative_topics';

// search state
export const SAVE_SEARCH_TAB_STATE = 'save_search_tab_state';
export const SAVE_STICKY_SESSION_STATE = 'save_sticky_session_state';
export const SAVE_GLOBAL_SEARCH_SOURCES = 'save_global_search_sources';
export const SAVE_CHANNEL_SEARCH_SOURCES = 'save_channel_search_sources';
export const SAVE_GLOBAL_SEARCH_STANDARD_TYPE_FILTER_KEY_LABELS =
  'save_global_search_standard_type_filter_key_labels';

// TalentMarketplace filters config state
export const TM_SAVE_FILTERS_CONFIG = 'tm_save_filters_config';
export const TM_ADD_CUSTOM_FILTER_OPTION = 'tm_add_custom_filter_option';
// TalentMarketplace filters state
export const TM_SAVE_SEARCH_FILTERS = 'tm_save_search_filters';
export const TM_CLEAR_KEYWORD_FILTER = 'tm_clear_keyword_filter';
export const TM_CLEAR_FILTER_FROM_GROUP = 'tm_clear_filter_from_group';
export const TM_CLEAR_ALL_FILTERS = 'tm_clear_all_filters';
export const TM_CLEAR_UNUSED_FILTERS_FROM_CONFIG = 'tm_clear_unused_filters_from_config';

// TalentMarketplace Match Recommendations config
export const TM_SAVE_MATCH_RECOMMENDATIONS_CONFIG = 'tm_request_match_recommendations_config';
export const OPEN_RECOMMENDATION_FEEDBACK_MODAL = 'open_recommendation_feedback_modal';
// OpportunityMarketplace CareerPath
export const OMP_CAREERPATH_SET_CONFIG = 'omp_careerpath_set_config';
export const OMP_CAREERPATH_OPEN_FILTERS_MODAL = 'omp_careerpath_open_filters_modal';

// onboarding v5 actions
export const SAVE_ONBOARDING_SELECTED_LEARNING_GOALS = 'save_onboarding_selected_learning_goals';

// organizations actions
export const SAVE_ORGANIZATIONS_TYPE_LOVS = 'save_organizations_type_lovs';
export const SAVE_ORGANIZATIONS_FOR_TYPE = 'save_organizations_for_type';
export const SAVE_ORGANIZATIONS_CONFIG = 'save_organizations_config';
export const SAVE_ORGANIZATIONS_LEVEL_FOR_TYPE_AND_VISIBILITY =
  'save_organizations_level_for_type_and_visibility';
export const SAVE_ORGANIZATIONS_FOR_ASSOCIATION = 'save_organizations_for_association';
export const SAVE_ORGANIZATIONS_FOR_VISIBILITY = 'save_organizations_for_visibility';
export const SAVE_ORGANIZATIONS_TREE_VIEW_VISIBILITY = 'save_organizations_tree_view_visibility';

// available filter counters actions
export const SAVE_AVAILABLE_FILTERS_COUNTERS_LOADING_STATE =
  'save_available_filters_counters_loading_state';
export const SAVE_AVAILABLE_FILTERS_COUNTERS = 'save_available_filters_counters';

// notification actions
export const REQUEST_NOTIFICATIONS = 'request_notifications';
export const RECEIVE_INIT_NOTIFICATIONS = 'receive_init_notifications';
export const RECEIVE_MORE_NOTIFICATIONS = 'receive_more_notifications';
export const RECEIVE_SEEN_NOTIFICATIONS_CONFIRMATION = 'receive_seen_notifications_confirmation';
export const SEEN_NOTIFICATIONS = 'seen_notifications';

// career preferences actions
export const REQUEST_CAREER_PREFERENCES = 'request_career_preferences';
export const UPDATE_CAREER_PREFERENCES = 'save_career_preferences';
export const SAVE_CAREER_PREFERENCE_OPTIONS = 'save_career_preference_options';

// available locations actions
export const REQUEST_AVAILABLE_LOCATIONS = 'request_available_locations';
export const REQUEST_AVAILABLE_LOCATIONS_ERROR = 'request_available_locations_error';
export const SAVE_AVAILABLE_COUNTRIES = 'save_available_countries';
export const SAVE_COUNTRIES_LOADING_STATE = 'save_countries_loading_state';

// job families actions
export const SAVE_JOB_FAMILIES_LOADING_STATE = 'save_job_families_loading_state';
export const SAVE_JOB_FAMILIES = 'save_job_families';

// mentorship
export const OPEN_BECOME_A_MENTOR_MODAL = 'open_become_a_mentor_modal';
export const OPEN_BECOME_A_MENTOR_CONFIRMATION_MODAL = 'open_become_a_mentor_confirmation_modal';
export const OPEN_VIEW_COMMENT_MODAL = 'open_view_comment_modal';
export const OPEN_REJECTION_COMMENT_MODAL = 'open_rejection_comment_modal';
export const OPEN_ACCEPT_MENTORSHIP_WITH_DURATION_MODAL =
  'open_accept_mentorship_with_duration_modal';
export const OPEN_WITHDRAW_COMMENT_MODAL = 'open_withdraw_comment_modal';
export const OPEN_REQUEST_MENTORSHIP_MODAL = 'open_request_mentorship_modal';

// work experienced actions
export const UPDATE_WORK_EXPERIENCES = 'save_work_experiences';

export const SAVE_LOCATIONS_CONFIGURATION = 'save_locations_configuration';

// memoized matching jobs actions
export const MEMOIZE_MATCHING_JOB = 'memoize_matching_job';
export const REMOVE_MEMOIZED_MATCHING_JOB_BY_ID = 'remove_memoized_matching_job_by_id';
export const CLEAR_MEMOIZED_MATCHING_JOBS = 'clear_memoized_matching_jobs';

// timezones actions
export const SAVE_TIMEZONES = 'save_timezones';

// mentorProfileActions
export const GET_PROGRAMS = 'get_programs';
export const GET_TOPICS = 'get_topics';
export const GET_DURATIONS = 'get_durations';
export const GET_TYPES = 'get_types';

export const UPDATE_THEME = 'update_theme';
export const UPDATE_GROUP_THEME_NAME = 'update_group_theme_name';
export const TOGGLE_ACCESSIBILITY_MODE = 'toggle-accessibility-mode';

// uppy uploads
export const OPEN_UPLOAD = 'open_upload';

// development plan
export const SAVE_DEVELOPMENT_PLAN = 'development_plan';
export const REPLACE_OR_ADD_DEVELOPMENT_PLAN_PATH_DETAILS =
  'replace_or_add_development_plan_path_details';
export const REPLACE_OR_ADD_DEVELOPMENT_PLAN_STEP = 'replace_or_add_development_plan_step';
export const ADD_DEV_PLAN_MENTOR = 'add_dev_plan_mentor';
export const ADD_DEV_PLAN_PROJECT = 'add_dev_plan_project';
export const REMOVE_DEV_PLAN_PROJECT = 'remove_development_plan_project';
export const REMOVE_DEV_PLAN_MENTOR = 'remove_development_plan_mentor';
export const UPDATE_DEVELOPMENT_PLAN_CURRENT_STEP_ID = 'update_development_plan_current_step_id';
export const SET_DEVELOPMENT_PLAN_LOADING = 'set_development_plan_loading';
export const CLEAR_DEVELOPMENT_PLAN = 'clear_development_plan';
export const TOGGLE_ADD_SKILL_MODAL = 'toggle_add_skill_modal';
export const TOGGLE_REMOVE_SKILL_MODAL = 'toggle_remove_skill_modal';
export const TOGGLE_ADD_CONTENT_MODAL = 'toggle_add_content_modal';
export const TOGGLE_ADD_MENTOR_MODAL = 'toggle_add_mentor_modal';
export const TOGGLE_ADD_PROJECT_MODAL = 'toggle_add_project_modal';
export const TOGGLE_PHASE_UNLOCKED_MODAL = 'toggle_phase_unlocked_modal';
export const TOGGLE_STEP_UNLOCKED_MODAL = 'toggle_step_unlocked_modal';
export const ADD_DEVELOPMENT_PLAN_SKILL = 'add_development_plan_skill';
export const REMOVE_DEVELOPMENT_PLAN_SKILL = 'remove_development_plan_skill';
export const REMOVE_DEVELOPMENT_PLAN_CONTENT_FROM_SKILL =
  'remove_development_plan_content_from_skill';
export const UPDATE_DEVELOPMENT_PLAN_PHASE = 'update_development_plan_phase';

//rejection reasons
export const GET_PROJECT_REJECTION_REASONS = 'get_project_rejection_reasons';
export const GET_MENTOR_REJECTION_REASONS = 'get_mentor_rejection_reasons';

//config service
export const GET_OMP_CONFIG_SERVICE = 'get_omp_config_service';

// Talent Sourcing
export const SAVE_SOURCING_FILTER_DATA = 'SAVE_SOURCING_FILTER_DATA';

export const SAVE_RECOMMENDATIONS_FEEDBACK = 'save_recommendations_feedback';

// opportunity alerts
export const OPEN_CREATE_OPPORTUNITY_ALERT_MODAL = 'OPEN_CREATE_OPPORTUNITY_ALERT_MODAL';
export const SAVE_SAVED_SEARCHES = 'save_saved_searched';

// SAML
export const SET_SAML_REQUEST_DATA = 'set_saml_request_data';
