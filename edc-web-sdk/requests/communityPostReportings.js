import config from './config';
import { _agent } from '../helpers/superagent-use';

export const createCommunityPostReporting = (payload, communityId) => {
  return new Promise((resolve, reject) => {
    _agent
      .post(`${config.ApiHost || ''}/api/v2/community_post_reportings`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};

export function dismissCommunityPost(payload) {
  return new Promise((resolve, reject) => {
    return _agent
      .put(`${config.ApiHost || ''}/api/v2/community_post_reportings/dismiss`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(response.body);
        }
      });
  });
}

export function trashCommunityPost(payload) {
  return new Promise((resolve, reject) => {
    return _agent
      .put(`${config.ApiHost || ''}/api/v2/community_post_reportings/trash`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(response.body);
        }
      });
  });
}
