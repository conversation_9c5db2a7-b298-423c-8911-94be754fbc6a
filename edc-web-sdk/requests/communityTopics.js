import config from './config';
import { _agent } from '../helpers/superagent-use';

export const createTopic = (payload, communityId) => {
  return new Promise((resolve, reject) => {
    _agent
      .post(`${config.ApiHost || ''}/api/v2/communities/${communityId}//community_topics`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};

export const updateTopic = (id, payload, communityId) => {
  return new Promise((resolve, reject) => {
    _agent
      .put(`${config.ApiHost || ''}/api/v2/communities/${communityId}/community_topics/${id}`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};

export const getTopicsList = communityId => {
  return new Promise((resolve, reject) => {
    _agent
      .get(`${config.ApiHost || ''}/api/v2/communities/${communityId}/community_topics/list`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};

export const getActiveTopics = (payload, communityId) => {
  return new Promise((resolve, reject) => {
    _agent
      .get(`${config.ApiHost || ''}/api/v2/communities/${communityId}/community_topics`)
      .query(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};

export const getArchivedTopics = (payload, communityId) => {
  return new Promise((resolve, reject) => {
    _agent
      .get(`${config.ApiHost || ''}/api/v2/communities/${communityId}/community_topics/archived`)
      .query(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};

export const deleteTopic = (id, communityId) => {
  return new Promise((resolve, reject) => {
    _agent
      .delete(`${config.ApiHost || ''}/api/v2/communities/${communityId}/community_topics/${id}`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err, response);
        }
      });
  });
};
