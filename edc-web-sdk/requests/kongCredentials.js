import { _agent } from '../helpers/superagent-use';
import config from './config';

/**
 * Creates Kong API credentials
 * @param {Object} payload - The credential payload to send
 * @returns {Promise<Object>} - The created credentials
 */
export function createCredentials(payload) {
  return new Promise((resolve, reject) => {
    return _agent
      .post(`${config.ApiHost || ''}/api/v2/kong/onboard`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}

/**
 * Retrieves Kong API keys
 * @returns {Promise<Object>} - The API keys
 */
export function getKeys() {
  return new Promise((resolve, reject) => {
    return _agent
      .get(`${config.ApiHost || ''}/api/v2/kong/keys`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}

/**
 * Gets details for a specific application
 * @param {string} id - The application ID
 * @returns {Promise<Object>} - The application details
 */
export function applicationDetails(id) {
  return new Promise((resolve, reject) => {
    _agent
      .get(`${config.ApiHost || ''}/api/v2/kong/application/${id}`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}

/**
 * Gets throttling information
 * @returns {Promise<Object>} - The throttling details
 */
export function throttling() {
  return new Promise((resolve, reject) => {
    _agent
      .get(`${config.ApiHost || ''}/api/v2/kong/throttling`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}

/**
 * Deletes credentials by ID
 * @param {string} credentialId - The credential ID to delete
 * @returns {Promise<Object>} - The deletion response
 */
export function deleteCredentials(credentialId) {
  return new Promise((resolve, reject) => {
    _agent
      .delete(`${config.ApiHost || ''}/api/v2/kong/application/${credentialId}`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}

/**
 * Updates an application by ID
 * @param {string} id - The application ID to update
 * @param {Object} payload - The update payload
 * @returns {Promise<Object>} - The updated application
 */
export function updateApplication(id, payload) {
  return new Promise((resolve, reject) => {
    _agent
      .put(`${config.ApiHost || ''}/api/v2/kong/application/${id}`)
      .send(payload)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}

/**
 * Gets all subscriptions
 * @returns {Promise<Object>} - The subscriptions data
 */
export function getSubscriptions() {
  return new Promise((resolve, reject) => {
    _agent
      .get(`${config.ApiHost || ''}/api/v2/kong/subscriptions`)
      .end((err, response) => {
        if (!err && response.ok) {
          resolve(response.body);
        } else {
          reject(err || response.body);
        }
      });
  });
}
